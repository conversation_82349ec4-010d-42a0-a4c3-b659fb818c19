{"compileOnSave": false, "compilerOptions": {"baseUrl": ".", "target": "esnext", "module": "esnext", "moduleResolution": "<PERSON><PERSON><PERSON>", "lib": ["dom", "esnext", "esnext.asynciterable", "DOM"], "types": ["node"], "typeRoots": ["./node_modules/@types"], "jsx": "react-jsx", "outDir": "./build", "declaration": false, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "experimentalDecorators": false, "allowJs": true, "resolveJsonModule": true, "sourceMap": true, "noEmit": true, "removeComments": false, "noImplicitAny": true, "noImplicitThis": true, "alwaysStrict": true, "strict": true, "strictBindCallApply": true, "strictNullChecks": true, "strictFunctionTypes": true, "strictPropertyInitialization": true, "noUnusedLocals": true, "noUnusedParameters": false, "noImplicitReturns": true, "skipLibCheck": true, "noFallthroughCasesInSwitch": true, "forceConsistentCasingInFileNames": true, "isolatedModules": true}, "include": ["src", "stories", "vite.config.ts"]}