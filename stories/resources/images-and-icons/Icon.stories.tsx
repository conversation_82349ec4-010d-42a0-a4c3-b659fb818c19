import { StoryFn, Meta } from '@storybook/react'

import { Icon, IconProps } from '../../../src/resources/images-and-icons/Icon'
import { brandColor50 } from '../../../src/resources/theme/Colors'

const Template: StoryFn<IconProps> = (props: IconProps) => <Icon {...props} />

export default {
  title: 'Resources/Images and icons/Icon',
  component: Icon,
} as Meta<typeof Icon>

export const Default = Template.bind({})
Default.storyName = 'Default'
Default.args = { name: 'search' }

export const SmallSize = Template.bind({})
SmallSize.storyName = 'With small size'
SmallSize.args = { name: 'search', size: 12 }

export const BigSize = Template.bind({})
BigSize.storyName = 'With big size'
BigSize.args = { name: 'search', size: 48 }

export const CustomFill = Template.bind({})
CustomFill.storyName = 'With custom fill'
CustomFill.args = { name: 'search', fill: brandColor50 }
