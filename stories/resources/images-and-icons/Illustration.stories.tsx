import { StoryFn, Meta } from '@storybook/react'

import {
  Illustration,
  Create,
  NotFound,
  NotAsked,
  ShopMissing,
  WarningTriangle,
  IllustrationProps,
} from '../../../src/resources/images-and-icons/Illustration'

const Template: StoryFn<IllustrationProps> = (props: IllustrationProps) => (
  <Illustration {...props} />
)

export default {
  title: 'Resources/Images and icons/Illustration',
  component: Illustration,
} as Meta<typeof Illustration>

export const WithCreate = Template.bind({})
WithCreate.storyName = 'With Create element'
WithCreate.args = {
  element: Create,
}

export const WithNotFound = Template.bind({})
WithNotFound.storyName = 'With NotFound element'
WithNotFound.args = {
  element: NotFound,
}

export const WithNotAsked = Template.bind({})
WithNotAsked.storyName = 'With NotAsked element'
WithNotAsked.args = {
  element: NotAsked,
}

export const WithShopMissing = Template.bind({})
WithShopMissing.storyName = 'With ShopMissing element'
WithShopMissing.args = {
  element: ShopMissing,
}

export const WithWarningTriangle = Template.bind({})
WithWarningTriangle.storyName = 'With WarningTriangle element'
WithWarningTriangle.args = {
  element: WarningTriangle,
}
