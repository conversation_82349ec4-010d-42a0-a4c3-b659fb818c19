import { StoryFn, Meta } from '@storybook/react'

import { Title, TitleProps } from '../../../src/resources/typography/Title'

const Template: StoryFn<TitleProps> = (props: Partial<TitleProps>) => (
  <Title children="Title" {...props} />
)

export default {
  title: 'Resources/Typography/Title',
  component: Title,
} as Meta<typeof Title>

export const Default = Template.bind({})
Default.storyName = 'Default'

export const WithLevel = Template.bind({})
WithLevel.storyName = 'With different level'
WithLevel.args = { level: 1 }

export const WithWeight = Template.bind({})
WithWeight.storyName = 'With different weight'
WithWeight.args = { weight: 'regular' }
