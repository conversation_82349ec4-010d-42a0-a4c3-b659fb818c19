import { StoryFn, Meta } from '@storybook/react'

import {
  TextStyle,
  TextStyleProps,
} from '../../../src/resources/typography/TextStyle'

const Template: StoryFn<TextStyleProps> = (props: Partial<TextStyleProps>) => (
  <TextStyle children="Text style" {...props} />
)

export default {
  title: 'Resources/Typography/TextStyle',
  component: TextStyle,
} as Meta<typeof TextStyle>

export const Default = Template.bind({})
Default.storyName = 'Default'

export const WithVariant = Template.bind({})
WithVariant.storyName = 'With variant'
WithVariant.args = { variation: 'success' }

export const WithUnderline = Template.bind({})
WithUnderline.storyName = 'With underlined'
WithUnderline.args = { underlined: true }

export const WithWeight = Template.bind({})
WithWeight.storyName = 'With different weight'
WithWeight.args = { weight: 'strong' }

export const WithSize = Template.bind({})
WithSize.storyName = 'With different size'
WithSize.args = { size: 'small' }

export const WithOpacity = Template.bind({})
WithOpacity.storyName = 'With different opacity'
WithOpacity.args = { opacity: 0.5 }

export const WithLongText = Template.bind({})
WithLongText.storyName = 'With long text'
WithLongText.args = {
  children: `Lorem ipsum dolor sit amet, consectetur adipiscing elit. Cras dictum auctor augue, sed vulputate ipsum fermentum vel. Donec tincidunt nec ante vitae congue. In id ullamcorper orci. Vivamus aliquam scelerisque mauris, id fringilla lorem condimentum vel. Pellentesque massa felis, venenatis quis dapibus eu, iaculis vitae ipsum.`,
}
