import { StoryFn, Meta } from '@storybook/react'

import {
  KbdClipboard,
  KbdClipboardProps,
} from '../../../src/resources/typography/KbdClipboard'

const Template: StoryFn<KbdClipboardProps> = (
  props: Partial<KbdClipboardProps>,
) => <KbdClipboard value="My text to copy" {...props} />

export default {
  title: 'Resources/Typography/KbdClipboard',
  component: KbdClipboard,
} as Meta<typeof KbdClipboard>

export const Default = Template.bind({})
Default.storyName = 'Default'
Default.args = { value: 'my value' }

export const Disabled = Template.bind({})
Disabled.storyName = 'Disabled'
Disabled.args = { disabled: true, value: 'my value' }

export const WithLabel = Template.bind({})
WithLabel.storyName = 'With a label'
WithLabel.args = {
  label: 'Copy this',
  value: 'value',
}

export const WithCropValue = Template.bind({})
WithCropValue.storyName = 'With cropped value at a specific index'
WithCropValue.args = {
  label: 'My value is "clipboard"',
  cropValueAt: 17,
  value: 'clipboard',
}
