import { StoryFn, Meta } from '@storybook/react'

import { Kbd, KbdProps } from '../../../src/resources/typography/Kbd'

const Template: StoryFn<KbdProps> = (props: Partial<KbdProps>) => (
  <Kbd children {...props} />
)

export default {
  title: 'Resources/Typography/Kbd',
  component: Kbd,
  parameters: { layout: 'centered' },
} as Meta<typeof Kbd>

export const WithText = Template.bind({})
WithText.storyName = 'With text'
WithText.args = { children: 'Hilighted text' }

export const WithNumbers = Template.bind({})
WithNumbers.storyName = 'With numbers'
WithNumbers.args = { children: 123456789 }
