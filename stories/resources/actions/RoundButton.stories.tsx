import { StoryFn, Meta } from '@storybook/react'

import {
  RoundButton,
  RoundButtonProps,
} from '../../../src/resources/actions/RoundButton'

const Template: StoryFn<RoundButtonProps> = (props: RoundButtonProps) => (
  <RoundButton {...props} onPress={alert} />
)

export default {
  title: 'Resources/Actions/RoundButton',
  component: RoundButton,
} as Meta<typeof RoundButton>

export const Default = Template.bind({})
Default.storyName = 'Default'

export const Disabled = Template.bind({})
Disabled.storyName = 'When disabled is sets to true'
Disabled.args = { disabled: true }
