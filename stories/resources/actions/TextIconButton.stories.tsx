import { StoryFn, Meta } from '@storybook/react'

import {
  TextIconButton,
  TextIconButtonProps,
} from '../../../src/resources/actions/TextIconButton'
import { IconName } from '../../../src/resources/images-and-icons/Icon'

const Template: StoryFn<TextIconButtonProps> = (
  props: Partial<Pick<TextIconButtonProps, 'onPress'>> &
    Omit<TextIconButtonProps, 'onPress'>,
) => <TextIconButton onPress={alert} {...props} />

export default {
  title: 'Resources/Actions/TextIconButton',
  component: TextIconButton,
} as Meta<typeof TextIconButton>

const icon: IconName = 'search'
const children = 'My button'
const defaultProps = { icon, children }

export const Default = Template.bind({})
Default.storyName = 'Default'
Default.args = defaultProps

export const Small = Template.bind({})
Small.storyName = 'With small font size'
Small.args = { ...defaultProps, size: 'small' }

export const WithTooltip = Template.bind({})
WithTooltip.storyName = 'With tooltip'
WithTooltip.args = {
  ...defaultProps,
  textTooltip: 'This is some tooltip text content.',
}
