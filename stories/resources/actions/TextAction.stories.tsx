import { StoryFn, Meta } from '@storybook/react'

import {
  TextAction,
  TextActionProps,
} from '../../../src/resources/actions/TextAction'

const Template: StoryFn<TextActionProps> = (
  props: Partial<TextActionProps>,
) => <TextAction text="My text link" onPress={alert} {...props} />

export default {
  title: 'Resources/Actions/TextAction',
  component: TextAction,
} as Meta<typeof TextAction>

export const Default = Template.bind({})
Default.storyName = 'Default'
Default.args = {}

export const Highlighted = Template.bind({})
Highlighted.storyName = 'With highlighted by default text'
Highlighted.args = { highlighted: true }
