import { StoryFn, Meta } from '@storybook/react'

import {
  IconButton,
  IconButtonProps,
  IconName,
} from '../../../src/resources/actions/IconButton'
import { brandColor40, brandColor60 } from '../../../src/resources/theme/Colors'

const Template: StoryFn<IconButtonProps> = (
  props: Partial<Pick<IconButtonProps, 'onPress'>> &
    Omit<IconButtonProps, 'onPress'>,
) => <IconButton onPress={alert} {...props} />

export default {
  title: 'Resources/Actions/IconButton',
  component: IconButton,
} as Meta<typeof IconButton>

const name: IconName = 'search'
const defaultProps = { name }

export const Default = Template.bind({})
Default.storyName = 'Default'
Default.args = defaultProps

export const WithCustomSize = Template.bind({})
WithCustomSize.storyName = 'Without custom size'
WithCustomSize.args = { ...defaultProps, size: 14 }

export const WithSmallMargin = Template.bind({})
WithSmallMargin.storyName = 'With small margin'
WithSmallMargin.args = { ...defaultProps, marginSize: 'xxsmall' }

export const WithBigMargin = Template.bind({})
WithBigMargin.storyName = 'With big margin'
WithBigMargin.args = { ...defaultProps, marginSize: 'normal' }

export const WithCustomFill = Template.bind({})
WithCustomFill.storyName = 'With custom fill'
WithCustomFill.args = {
  ...defaultProps,
  color: brandColor40,
  hoveredColor: brandColor60,
}

export const WithBold = Template.bind({})
WithBold.storyName = 'With bold'
WithBold.args = {
  ...defaultProps,
  bold: true,
  color: brandColor40,
  hoveredColor: brandColor60,
}

export const WithDisabled = Template.bind({})
WithDisabled.storyName = 'With disabled set to true'
WithDisabled.args = { ...defaultProps, marginSize: 'normal', disabled: true }
