import { StoryFn, Meta } from '@storybook/react'

import { Button, ButtonProps } from '../../../src/resources/actions/Button'

const Template: StoryFn<ButtonProps> = (props: ButtonProps) => (
  <Button {...props}>My button</Button>
)

export default {
  title: 'Resources/Actions/Button',
  component: Button,
} as Meta<typeof Button>

export const Default = Template.bind({})
Default.storyName = 'Default'

export const Pressable = Template.bind({})
Pressable.storyName = 'Pressable'
Pressable.args = { onPress: alert }

export const WhenDisabled = Template.bind({})
WhenDisabled.storyName = 'When disabled'
WhenDisabled.args = { disabled: true }

export const WhenFocused = Template.bind({})
WhenFocused.storyName = 'When focused'
WhenFocused.args = { focused: true }

export const WithLoading = Template.bind({})
WithLoading.storyName = 'With loading'
WithLoading.args = { loading: true }

export const WithVariation = Template.bind({})
WithVariation.storyName = 'With variation'
WithVariation.args = { variation: 'neutral' }

export const WithBetaBadge = Template.bind({})
WithBetaBadge.storyName = 'With beta badge'
WithBetaBadge.args = { betaBadge: true, variation: 'neutral' }

export const WithIcon = Template.bind({})
WithIcon.storyName = 'With icon'
WithIcon.args = { icon: 'search' }

export const WithCustomSize = Template.bind({})
WithCustomSize.storyName = 'With custom size'
WithCustomSize.args = { size: 'tiny' }
