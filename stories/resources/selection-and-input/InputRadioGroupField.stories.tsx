import { useState } from 'react'

import { StoryFn, Meta } from '@storybook/react'

import {
  InputRadioGroupField,
  InputRadioGroupFieldProps,
} from '../../../src/resources/selection-and-input/InputRadioGroupField'

const Template: StoryFn<Partial<InputRadioGroupFieldProps>> = (
  props: Partial<InputRadioGroupFieldProps>,
) => {
  const [value, setValue] = useState('option 1')

  return (
    <InputRadioGroupField
      label="My label"
      value={value}
      required={false}
      onChange={(value) => setValue((_) => value)}
      options={['option 1', 'option 2', 'option 3']}
      optionToText={(value) => value}
      {...props}
    />
  )
}

export default {
  title: 'Resources/Selection and input/InputRadioGroupField',
  component: InputRadioGroupField,
  parameters: { layout: 'centered' },
} as Meta<typeof InputRadioGroupField>

export const Default = Template.bind({})
Default.storyName = 'Default'

export const Required = Template.bind({})
Required.storyName = 'When required is sets to true'
Required.args = { required: true }

export const ErrorMessage = Template.bind({})
Required.storyName = 'With an error messsage'
Required.args = { errorMessage: 'There is an error' }
