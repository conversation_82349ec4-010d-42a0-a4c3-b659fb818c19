import { useState } from 'react'

import { StoryFn, Meta } from '@storybook/react'

import {
  InputToggleSwitchField,
  InputToggleSwitchFieldProps,
} from '../../../src/resources/selection-and-input/InputToggleSwitchField'

const Template: StoryFn<Partial<InputToggleSwitchFieldProps>> = (
  props: Partial<InputToggleSwitchFieldProps>,
) => {
  const [value, setValue] = useState(false)

  return (
    <InputToggleSwitchField
      label="My label"
      value={value}
      required={false}
      onChange={() => setValue(!value)}
      {...props}
    />
  )
}

export default {
  title: 'Resources/Selection and input/InputToggleSwitchField',
  component: InputToggleSwitchField,
  decorators: [
    (Story) => (
      <div style={{ width: 300 }}>
        <Story />
      </div>
    ),
  ],
} as Meta<typeof InputToggleSwitchField>

export const Default = Template.bind({})
Default.storyName = 'Default'

export const Disabled = Template.bind({})
Disabled.storyName = 'When disabled is sets to true'
Disabled.args = { disabled: true }

export const Required = Template.bind({})
Required.storyName = 'When required is sets to true'
Required.args = { required: true }

export const ErrorMessage = Template.bind({})
Required.storyName = 'With an error messsage'
Required.args = { errorMessage: 'There is an error' }
