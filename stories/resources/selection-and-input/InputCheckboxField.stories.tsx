import { useState } from 'react'

import { StoryFn, Meta } from '@storybook/react'

import {
  InputCheckboxField,
  InputCheckboxFieldProps,
} from '../../../src/resources/selection-and-input/InputCheckboxField'

const Template: StoryFn<Partial<InputCheckboxFieldProps>> = (
  props: Partial<InputCheckboxFieldProps>,
) => {
  const [value, setValue] = useState(false)

  return (
    <InputCheckboxField
      label="My label"
      text="My text"
      value={value}
      required={false}
      onChange={() => setValue(!value)}
      {...props}
    />
  )
}

export default {
  title: 'Resources/Selection and input/InputCheckboxField',
  component: InputCheckboxField,
  parameters: { layout: 'centered' },
} as Meta<typeof InputCheckboxField>

export const Default = Template.bind({})
Default.storyName = 'Default'

export const Disabled = Template.bind({})
Disabled.storyName = 'Disabled'
Disabled.args = { disabled: true }

export const Required = Template.bind({})
Required.storyName = 'Required'
Required.args = { required: true }

export const WithAnErrorMessage = Template.bind({})
WithAnErrorMessage.storyName = 'With an error message'
WithAnErrorMessage.args = { errorMessage: 'There is an error' }
