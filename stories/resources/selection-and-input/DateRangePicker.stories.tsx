import { StoryFn, Meta } from '@storybook/react'

import {
  DateRangePicker,
  DateRangePickerProps,
} from '../../../src/resources/selection-and-input/DateRangePicker'

const initialDateRange: [Date, Date] = [new Date(), new Date()]

const Template: StoryFn<DateRangePickerProps> = (
  props: Partial<DateRangePickerProps>,
) => (
  <DateRangePicker
    onChangePreset={(preset: 'Today' | 'Yesterday') => alert(preset)}
    onRequestReset={alert}
    onFocusChange={alert}
    onNextMonthClick={alert}
    onDatesChange={alert}
    onPrevMonthClick={alert}
    activePreset="Today"
    presets={['Today', 'Yesterday']}
    formatPreset={(preset: 'Today' | 'Yesterday') => preset}
    range={initialDateRange}
    visibleMonth={initialDateRange[0]}
    isEqualPreset={(a: 'Today' | 'Yesterday', b: 'Today' | 'Yesterday') =>
      a == b
    }
    {...props}
  />
)

export default {
  title: 'Resources/Selection and input/DateRangePicker',
  component: DateRangePicker,
} as Meta<typeof DateRangePicker>

export const Default = Template.bind({})
Default.storyName = 'Default'

export const WithDisabledResetButton = Template.bind({})
WithDisabledResetButton.storyName = 'With disabledResetButton set to true'
WithDisabledResetButton.args = { disabledResetButton: true }
