import { StoryFn, Meta } from '@storybook/react'

import {
  DayPickerRange,
  DayPickerRangeProps,
  defaultFocusedInput,
} from '../../../src/resources/selection-and-input/DayPickerRange'

const Template: StoryFn<DayPickerRangeProps> = (props: DayPickerRangeProps) => (
  <DayPickerRange {...props} />
)

export default {
  title: 'Resources/Selection and input/DayPickerRange',
  component: DayPickerRange,
} as Meta<typeof DayPickerRange>

export const Default = Template.bind({})
Default.storyName = 'Default'
Default.args = {
  startDate: new Date('1970-01-01T00:00:00.000Z'),
  endDate: new Date('1970-01-03T00:00:00.000Z'),
  focusedInput: defaultFocusedInput,
  onChange: alert,
  onFocusChange: alert,
}
