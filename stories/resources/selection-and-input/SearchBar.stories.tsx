import { StoryFn, Meta } from '@storybook/react'
import { action } from '@storybook/addon-actions'

import {
  SearchBar,
  SearchBarProps,
} from '../../../src/resources/selection-and-input/SearchBar'

const Template: StoryFn<SearchBarProps> = (props: SearchBarProps) => (
  <div style={{ flex: 1 }}>
    <SearchBar {...props} onChange={action('onChange')} />
  </div>
)

export default {
  title: 'Resources/Selection and input/SearchBar',
  component: SearchBar,
} as Meta<typeof SearchBar>

export const Default = Template.bind({})
Default.storyName = 'Default'

export const WithPlaceholder = Template.bind({})
WithPlaceholder.storyName = 'With placeholder'
WithPlaceholder.args = {
  placeholder: 'My placeholder',
}

export const WithIinitialValue = Template.bind({})
WithIinitialValue.storyName = 'With initial value'
WithIinitialValue.args = {
  value: 'My initial value',
}
