import { StoryFn, Meta } from '@storybook/react'

import {
  Checkbox,
  CheckboxProps,
} from '../../../src/resources/selection-and-input/Checkbox'

const Template: StoryFn<CheckboxProps> = (props: Partial<CheckboxProps>) => (
  <Checkbox value={false} onChange={alert} {...props} />
)

export default {
  title: 'Resources/Selection and input/Checkbox',
  component: Checkbox,
} as Meta<typeof Checkbox>

export const Unchecked = Template.bind({})
Unchecked.storyName = 'Unchecked'
Unchecked.args = { value: false }

export const UncheckedDisabled = Template.bind({})
UncheckedDisabled.storyName = 'Unchecked - disabled'
UncheckedDisabled.args = { value: false, disabled: true }

export const Checked = Template.bind({})
Checked.storyName = 'Checked'
Checked.args = { value: true }

export const CheckedDisabled = Template.bind({})
CheckedDisabled.storyName = 'Checked - disabled'
CheckedDisabled.args = { value: true, disabled: true }

export const Indeterminate = Template.bind({})
Indeterminate.storyName = 'Indeterminate'
Indeterminate.args = { indeterminate: true }

export const IndeterminateDisabled = Template.bind({})
IndeterminateDisabled.storyName = 'Indeterminate - disabled'
IndeterminateDisabled.args = { indeterminate: true, disabled: true }

export const BigSize = Template.bind({})
BigSize.storyName = 'With big size'
BigSize.args = { size: 32 }

export const SmallSize = Template.bind({})
SmallSize.storyName = 'With small size'
SmallSize.args = { size: 10 }
