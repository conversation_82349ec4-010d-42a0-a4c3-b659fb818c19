import { StoryFn, Meta } from '@storybook/react'

import {
  DayPickerSingle,
  DayPickerSingleProps,
} from '../../../src/resources/selection-and-input/DayPickerSingle'

const Template: StoryFn<DayPickerSingleProps> = (
  props: Omit<DayPickerSingleProps, 'onPress'>,
) => <DayPickerSingle {...props} />

export default {
  title: 'Resources/Selection and input/DayPickerSingle',
  component: DayPickerSingle,
} as Meta<typeof DayPickerSingle>

export const Default = Template.bind({})
Default.storyName = 'Default'
Default.args = {
  value: new Date(0),
  onChange: alert,
}
