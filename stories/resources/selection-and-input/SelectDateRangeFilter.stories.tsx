import 'react-dates/initialize'
import 'react-dates/lib/css/_datepicker.css'

import { StoryFn, Meta } from '@storybook/react'

import {
  SelectDateRangeFilter,
  SelectDateRangeFilterProps,
} from '../../../src/resources/selection-and-input/SelectDateRangeFilter'

const Template: StoryFn<SelectDateRangeFilterProps> = (
  props: Partial<SelectDateRangeFilterProps>,
) => (
  <div style={{ minHeight: 500 }}>
    <SelectDateRangeFilter
      value={undefined}
      onChange={alert}
      {...props}
      triggerLabelDisplay="showPreset"
    />
  </div>
)

export default {
  title: 'Resources/Selection and input/SelectDateRangeFilter',
  component: SelectDateRangeFilter,
} as Meta<typeof SelectDateRangeFilter>

export const Default = Template.bind({})
Default.storyName = 'Default'

export const WithTodayPreset = Template.bind({})
WithTodayPreset.storyName = 'With today preset'
WithTodayPreset.args = {
  value: [new Date(), new Date()],
}

export const WithCustomValue = Template.bind({})
WithCustomValue.storyName = 'With custom value'
WithCustomValue.args = {
  value: [new Date(0), new Date(60 * 60 * 24 * 2 * 1000)],
}

export const WithCustomPlaceholder = Template.bind({})
WithCustomPlaceholder.storyName = 'With custom placeholder'
WithCustomPlaceholder.args = {
  placeholder: 'My custom text',
  value: undefined,
}

export const WithLabel = Template.bind({})
WithLabel.storyName = 'With label'
WithLabel.args = {
  label: 'My label',
  value: undefined,
}

export const WithLabelAndPlaceholder = Template.bind({})
WithLabelAndPlaceholder.storyName = 'With label and placeholder'
WithLabelAndPlaceholder.args = {
  label: 'My label',
  placeholder: 'My placeholder',
  value: undefined,
}
