import { useState } from 'react'
import { StoryFn, Meta } from '@storybook/react'

import {
  InputNumberField,
  InputNumberFieldProps,
  inputNumberFieldAppenderCurrency,
  inputNumberFieldAppenderPercent,
  inputNumberFieldAppenderCustom,
} from '../../../src/resources/selection-and-input/InputNumberField'
import { intlCurrencyEur } from '../../../src/primitives/Intl'

const Template: StoryFn<InputNumberFieldProps> = (
  props: Omit<InputNumberFieldProps, 'value' | 'onChange'>,
) => {
  let [value, setValue] = useState(0)

  return (
    <InputNumberField
      value={value}
      onChange={(value) => setValue(value)}
      {...props}
    />
  )
}

export default {
  title: 'Resources/Selection and input/InputNumberField',
  component: InputNumberField,
} as Meta<typeof InputNumberField>

export const Default = Template.bind({})
Default.storyName = 'Default'

export const BasicNoDecimal = Template.bind({})
BasicNoDecimal.storyName = 'Basic without decimal'
BasicNoDecimal.args = {
  precision: 0,
  label: 'Basic without decimal',
}

export const Disabled = Template.bind({})
Disabled.storyName = 'Disabled'
Disabled.args = {
  label: 'Disabled',
  disabled: true,
}

export const Errored = Template.bind({})
Errored.storyName = 'Errored'
Errored.args = {
  label: 'Errored',
  errorMessage: 'Something wrong happened',
}

export const WithCurrencySign = Template.bind({})
WithCurrencySign.storyName = 'With a currency sign'
WithCurrencySign.args = {
  label: 'With a currency sign',
  appender: inputNumberFieldAppenderCurrency(intlCurrencyEur),
}

export const MinMaxDecimalPrecision = Template.bind({})
MinMaxDecimalPrecision.storyName =
  'With a minimum decimal precision to display & 2 fraction digits'
MinMaxDecimalPrecision.args = {
  label: 'With a minimum and maximal decimal precision',
  minPrecision: 0,
  precision: 2,
}

export const CustomPlaceholder = Template.bind({})
CustomPlaceholder.storyName = 'With custom placeholder & 3 fraction digits'
CustomPlaceholder.args = {
  precision: 3,
  label: 'With custom placeholder & 3 fraction digits',
  placeholder: "Prix d'achat",
  appender: inputNumberFieldAppenderCurrency(intlCurrencyEur),
}

export const WithCustomUnitSign = Template.bind({})
WithCustomUnitSign.storyName = 'With custom unit sign & 1 fraction digit'
WithCustomUnitSign.args = {
  precision: 1,
  label: 'With custom unit sign & 1 fraction digit',
  appender: inputNumberFieldAppenderCustom('N/m'),
}

export const LimitedValueRange = Template.bind({})
LimitedValueRange.storyName = 'With a limited value range & a step value of 0.1'
LimitedValueRange.args = {
  minValue: 0,
  maxValue: 100,
  step: 0.1,
  label: 'With a limited value range & a step value of 0.1',
  appender: inputNumberFieldAppenderPercent,
}

export const MinimalValue = Template.bind({})
MinimalValue.storyName = 'With a minimal value & a step value of 6'
MinimalValue.args = {
  minValue: 0,
  step: 6,
  label: 'With a minimal value & a step value of 6',
  placeholder: 'Quantity by 6',
}

export const InputAdaptiveWidth = Template.bind({})
InputAdaptiveWidth.storyName = 'With input adaptative width'
InputAdaptiveWidth.args = {
  label: 'With input adaptative width',
  shrinkInput: true,
}

export const InputAdaptiveWidthWithUnit = Template.bind({})
InputAdaptiveWidthWithUnit.storyName =
  'With input adaptative width & an unit sign'
InputAdaptiveWidthWithUnit.args = {
  label: 'With input adaptative width & an unit sign',
  appender: { tag: 'Custom', value: 'L' },
  shrinkInput: true,
}

export const NoNumberGrouping = Template.bind({})
NoNumberGrouping.storyName = 'Without number grouping'
NoNumberGrouping.args = {
  label: 'Without number grouping',
  useGrouping: false,
}
