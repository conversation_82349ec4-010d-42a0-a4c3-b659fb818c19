import { StoryFn, Meta } from '@storybook/react'
import { action } from '@storybook/addon-actions'

import {
  InputTimeField,
  InputTimeFieldProps,
} from '../../../src/resources/selection-and-input/InputTimeField'

const Template: StoryFn<InputTimeFieldProps> = (props: InputTimeFieldProps) => (
  <InputTimeField
    {...props}
    onChange={action('onChange')}
    onFocus={action('onFocus')}
    onBlur={action('onBlur')}
  />
)

export default {
  title: 'Resources/Selection and input/InputTimeField',
  component: InputTimeField,
} as Meta<typeof InputTimeField>

export const Default = Template.bind({})
Default.storyName = 'Default'

export const DefinedValue = Template.bind({})
DefinedValue.storyName = 'With a predefined value'
DefinedValue.args = { value: new Date() }

export const VariousParameters = Template.bind({})
VariousParameters.storyName = 'With different label'
VariousParameters.args = {
  label: 'Clock',
  required: true,
  errorMessage: "It's not time!",
}

export const LargeContainer = Template.bind({})
LargeContainer.storyName = 'With a large input container'
LargeContainer.decorators = [
  (Story, context) => (
    <div style={{ width: '200px' }}>
      <Story {...context} />
    </div>
  ),
]
