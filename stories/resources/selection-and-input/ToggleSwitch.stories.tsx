import { StoryFn, Meta } from '@storybook/react'

import {
  ToggleSwitch,
  ToggleSwitchProps,
} from '../../../src/resources/selection-and-input/ToggleSwitch'

const Template: StoryFn<ToggleSwitchProps> = (
  props: Partial<ToggleSwitchProps>,
) => <ToggleSwitch onChange={alert} value={false} disabled={false} {...props} />

export default {
  title: 'Resources/Selection and input/ToggleSwitch',
  component: ToggleSwitch,
  parameters: { layout: 'centered' },
} as Meta<typeof ToggleSwitch>

export const SwitchOff = Template.bind({})
SwitchOff.storyName = 'Switch off'
SwitchOff.args = { value: false }

export const SwitchOffDisabled = Template.bind({})
SwitchOffDisabled.storyName = 'Switch off - disabled'
SwitchOffDisabled.args = { value: false, disabled: true }

export const SwitchOn = Template.bind({})
SwitchOn.storyName = 'Switch on'
SwitchOn.args = { value: true }

export const SwitchOnDisabled = Template.bind({})
SwitchOnDisabled.storyName = 'Switch on - disabled'
SwitchOnDisabled.args = { value: true, disabled: true }
