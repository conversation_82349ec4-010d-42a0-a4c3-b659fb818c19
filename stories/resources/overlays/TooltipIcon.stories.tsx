import { useRef } from 'react'

import { StoryFn, Meta } from '@storybook/react'

import {
  TooltipIcon,
  TooltipIconProps,
} from '../../../src/resources/overlays/TooltipIcon'

const Template: StoryFn<TooltipIconProps> = (props) => {
  const ref = useRef(null)

  return (
    <div ref={ref}>
      {props.altTriggerRef && <span>you can hover me too</span>}
      <TooltipIcon {...props} altTriggerRef={ref}>
        Some tooltip content
      </TooltipIcon>
    </div>
  )
}

export default {
  title: 'Resources/Overlays/TooltipIcon',
  component: TooltipIcon,
} as Meta<typeof TooltipIcon>

export const Default = Template.bind({})
Default.storyName = 'Default'

export const WithVariation = Template.bind({})
WithVariation.storyName = 'With variation'
WithVariation.args = { variation: 'alert' }

export const WithArrow = Template.bind({})
WithArrow.storyName = 'With an arrow on overlay'
WithArrow.args = { arrowed: true }

export const WithAlternativeTrigger = Template.bind({})
WithAlternativeTrigger.storyName = 'With an alternative trigger'
WithAlternativeTrigger.args = { altTriggerRef: { current: null } }
