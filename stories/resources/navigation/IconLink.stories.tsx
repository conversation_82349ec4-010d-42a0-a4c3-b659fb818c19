import { StoryFn, Meta } from '@storybook/react'

import {
  IconLink,
  IconLinkProps,
  IconName,
  iconLinkToRoute,
} from '../../../src/resources/navigation/IconLink'
import { brandColor40, brandColor60 } from '../../../src/resources/theme/Colors'

const Template: StoryFn<IconLinkProps> = (props: IconLinkProps) => (
  <IconLink {...props} />
)

export default {
  title: 'Resources/Navigation/IconLink',
  component: IconLink,
} as Meta<typeof IconLink>

const defaultProps = {
  name: 'queue_arrow_right_light' as IconName,
  to: iconLinkToRoute('/'),
}

export const Default = Template.bind({})
Default.storyName = 'Default'
Default.args = defaultProps

export const NewTab = Template.bind({})
NewTab.storyName = 'With new tab opening'
NewTab.args = { ...defaultProps, openNewTab: true }

export const SmallMargin = Template.bind({})
SmallMargin.storyName = 'With small margin'
SmallMargin.args = { ...defaultProps, marginSize: 'xxsmall' }

export const BigMargin = Template.bind({})
BigMargin.storyName = 'With big margin'
BigMargin.args = { ...defaultProps, marginSize: 'normal' }

export const NoMargin = Template.bind({})
NoMargin.storyName = 'Without margin'
NoMargin.args = { ...defaultProps }

export const CustomFill = Template.bind({})
CustomFill.storyName = 'With custom fill'
CustomFill.args = {
  ...defaultProps,
  marginSize: 'normal',
  color: brandColor40,
  hoveredColor: brandColor60,
}

export const Disabled = Template.bind({})
Disabled.storyName = 'With disabled set to true'
Disabled.args = { ...defaultProps, marginSize: 'normal', disabled: true }
