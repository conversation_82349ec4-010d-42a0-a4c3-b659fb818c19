import { StoryFn, Meta } from '@storybook/react'

import {
  Pagination,
  PaginationProps,
} from '../../../src/resources/navigation/LegacyPagination'

const Template: StoryFn<PaginationProps> = (
  props: Omit<PaginationProps, 'onRequestPaginate'>,
) => <Pagination onRequestPaginate={alert} {...props} />

export default {
  title: 'Resources/Navigation/Pagination',
  component: Pagination,
} as Meta<typeof Pagination>

export const SinglePage = Template.bind({})
SinglePage.storyName = 'With single page'
SinglePage.args = { currentPage: 1, totalPages: 1 }

export const PagesAtStart = Template.bind({})
PagesAtStart.storyName = 'With pages at start'
PagesAtStart.args = { currentPage: 1, totalPages: 2 }

export const PagesAtEnd = Template.bind({})
PagesAtEnd.storyName = 'With pages at end'
PagesAtEnd.args = { currentPage: 2, totalPages: 2 }

export const ManyPagesAtMiddle = Template.bind({})
ManyPagesAtMiddle.storyName = 'With many pages at middle'
ManyPagesAtMiddle.args = { currentPage: 1, totalPages: 12 }

export const ManyPagesNearStart = Template.bind({})
ManyPagesNearStart.storyName = 'With many pages near Start'
ManyPagesNearStart.args = { currentPage: 2, totalPages: 12 }

export const ManyPagesNearEnd = Template.bind({})
ManyPagesNearEnd.storyName = 'With many pages near end'
ManyPagesNearEnd.args = { currentPage: 11, totalPages: 12 }

export const ManyPagesAtEnd = Template.bind({})
ManyPagesAtEnd.storyName = 'With many pages at end'
ManyPagesAtEnd.args = { currentPage: 12, totalPages: 12 }
