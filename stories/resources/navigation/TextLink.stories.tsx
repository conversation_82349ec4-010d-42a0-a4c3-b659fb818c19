import React from 'react'
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'react-router-dom'

import { StoryFn, Meta } from '@storybook/react'

import {
  TextLink,
  TextLinkProps,
} from '../../../src/resources/navigation/TextLink'
import {
  navigationToUrl,
  navigationToRoute,
} from '../../../src/primitives/Navigation'

// BrowserRouter can't be used as JSX in 'react-router-dom@5.3.0'
const RouterWrapper = ({ children }: { children: React.ReactNode }) => {
  return React.createElement(BrowserRouter as any, {}, children)
}

const Template: StoryFn<TextLinkProps> = (props: Partial<TextLinkProps>) => (
  <RouterWrapper>
    <TextLink text="My text link" to={navigationToRoute('/')} {...props} />
  </RouterWrapper>
)

export default {
  title: 'Resources/Navigation/TextLink',
  component: TextLink,
} as Meta<typeof TextLink>

export const Default = Template.bind({})
Default.storyName = 'Default'
Default.args = {}

export const Highlighted = Template.bind({})
Highlighted.storyName = 'With highlighted by default text'
Highlighted.args = { highlighted: true }

export const NewTab = Template.bind({})
NewTab.storyName = 'With new tab opening'
NewTab.args = { openNewTab: true }

export const ExternalLink = Template.bind({})
ExternalLink.storyName = 'With an external link'
ExternalLink.args = {
  to: navigationToUrl(new URL('https://wino.fr')),
}

export const WithIcon = Template.bind({})
WithIcon.storyName = 'With an icon'
WithIcon.args = {
  to: navigationToUrl(new URL('https://wino.fr')),
  icon: 'external_link',
}
