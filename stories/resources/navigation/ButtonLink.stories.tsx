import { StoryFn, Meta } from '@storybook/react'

import {
  ButtonLink,
  ButtonLinkProps,
} from '../../../src/resources/actions/ButtonLink'

import {
  navigationToRoute,
  navigationToUrl,
} from '../../../src/primitives/Navigation'

const Template: StoryFn<ButtonLinkProps> = (props: ButtonLinkProps) => (
  <ButtonLink {...props}> my button link </ButtonLink>
)

export default {
  title: 'Resources/Navigation/ButtonLink',
  component: ButtonLink,
} as Meta<typeof ButtonLink>

const defaultProps = { to: navigationToRoute('/about') }

export const Default = Template.bind({})
Default.storyName = 'Default'
Default.args = defaultProps

export const WithIcon = Template.bind({})
WithIcon.storyName = 'With icon'
WithIcon.args = { ...defaultProps, icon: 'search' }

export const WhenDisabled = Template.bind({})
WhenDisabled.storyName = 'When disabled'
WhenDisabled.args = { ...defaultProps, disabled: true }

export const WithExternalLink = Template.bind({})
WithExternalLink.storyName = 'With an external link'
WithExternalLink.args = {
  to: navigationToUrl(new URL('https://www.wino.fr')),
}
