import { StoryFn, Meta } from '@storybook/react'

import {
  TableDateCell,
  TableDateCellProps,
} from '../../../src/resources/tables/TableDateCell'

const Template: StoryFn<TableDateCellProps> = (props: TableDateCellProps) => (
  <TableDateCell {...props} />
)

export default {
  title: 'Resources/Tables/TableDateCell',
  component: TableDateCell,
  parameters: { layout: 'centered' },
} as Meta<typeof TableDateCell>

const date = new Date('January 1, 1970 00:00:00')

export const Default = Template.bind({})
Default.storyName = 'Default'
Default.args = { value: date }

export const WithLabel = Template.bind({})
WithLabel.storyName = 'With a label'
WithLabel.args = { value: date, label: 'current date' }
