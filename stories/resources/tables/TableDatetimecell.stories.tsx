import { StoryFn, Meta } from '@storybook/react'

import {
  TableDatetimeCell,
  TableDatetimeCellProps,
} from '../../../src/resources/tables/TableDatetimeCell'

const date = new Date('1248-04-26')

const Template: StoryFn<TableDatetimeCellProps> = (
  props: Partial<TableDatetimeCellProps>,
) => <TableDatetimeCell value={date} {...props} />

export default {
  title: 'Resources/Tables/TableDatetimeCell',
  component: TableDatetimeCell,
  parameters: { layout: 'centered' },
} as Meta<typeof TableDatetimeCell>

export const Default = Template.bind({})
Default.storyName = 'Default'
Default.args = { value: date }
