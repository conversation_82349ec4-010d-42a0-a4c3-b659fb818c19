import { useState, useEffect, useCallback } from 'react'

import { StoryFn, Meta } from '@storybook/react'
import { action } from '@storybook/addon-actions'

import {
  loading,
  reloadingOk,
  doneOk,
} from '../../../src/primitives/AsyncResult'
import {
  TableView,
  TableViewProps,
} from '../../../src/resources/tables/TableView'
import {
  TableColumn,
  TableLayout,
  px,
  pct,
  fr,
} from '../../../src/resources/tables/Table'
import { TextStyle } from '../../../src/resources/typography/TextStyle'

type Item = {
  id: string
  name: string
  purchasePrice?: number
}

const Basic: StoryFn<TableViewProps> = (props: Partial<TableViewProps>) => {
  const columns: Array<TableColumn<Item>> = [
    {
      key: 'name',
      name: 'Name',
      render: ({ data, errorMessage }) => (
        <div>
          <TextStyle> {data.name} </TextStyle>
          <br />
          {errorMessage && (
            <TextStyle variation="negative"> ({errorMessage}) </TextStyle>
          )}
        </div>
      ),
    },
    {
      key: 'purchase-price',
      name: 'Purchase price',
      render: ({ data }) => {
        const price = new Intl.NumberFormat('fr-FR', {
          style: 'currency',
          currency: 'EUR',
        })
        return <TextStyle> {price.format(data.purchasePrice ?? 0)} </TextStyle>
      },
    },
  ]

  const data = doneOk<void, Item[]>([
    {
      id: '0',
      name: 'Malbec Des Rochelles Rg - 2019 - 75CL *bio',
      purchasePrice: 4.83,
    },
    {
      id: '1',
      name: 'Crozes Hermitage Les Charmeuses - 2018 - 75CL',
      purchasePrice: 9.4,
    },
    {
      id: '2',
      name: 'Sancerre Les Monts Damnes Dom Girard Bl - 2018 - 75CL',
      purchasePrice: 8.85,
    },
  ])

  return (
    <TableView
      columns={columns}
      data={data}
      keyExtractor={(row) => row.id}
      onSelectChange={action('onSelectChange')}
      {...props}
    />
  )
}

const Empty: StoryFn<TableViewProps> = (props: Partial<TableViewProps>) => {
  const columns: Array<TableColumn<Item>> = [
    {
      key: 'name',
      name: 'Name',
      render: ({ data }) => <TextStyle> {data.name} </TextStyle>,
    },
    {
      key: 'purchase-price',
      name: 'Purchase price',
      render: ({ data }) => {
        const price = new Intl.NumberFormat('fr-FR', {
          style: 'currency',
          currency: 'EUR',
        })
        return <TextStyle> {price.format(data.purchasePrice ?? 0)} </TextStyle>
      },
    },
  ]
  const data = doneOk<void, Item[]>([])

  return (
    <TableView
      columns={columns}
      data={data}
      keyExtractor={(row) => row.id}
      {...props}
    />
  )
}

const Loading: StoryFn<TableViewProps> = (props: Partial<TableViewProps>) => {
  const [dataStateCursor, setDataStateCursor] = useState('loading')
  const [data, setData] = useState(loading<void, Item[]>())

  const columns: Array<TableColumn<Item>> = [
    {
      key: 'name',
      name: 'Name',
      render: ({ data }) => (
        <TextStyle size="huge" lineHeight="xhuge">
          {data.name}
        </TextStyle>
      ),
    },
  ]

  useEffect(() => {
    if (dataStateCursor === 'loading') {
      setTimeout(() => {
        setDataStateCursor('done')
        setData(
          doneOk<void, Item[]>([
            {
              id: '0',
              name: 'Malbec Des Rochelles Rg - 2019 - 75CL *bio',
            },
            {
              id: '1',
              name: 'Crozes Hermitage Les Charmeuses - 2018 - 75CL',
            },
          ]),
        )
      }, 2000)
    }
    if (dataStateCursor === 'reloading') {
      setTimeout(() => {
        setDataStateCursor('done')
        setData(
          doneOk<void, Item[]>([
            {
              id: '0',
              name: 'Malbec Des Rochelles Rg - 2019 - 75CL *bio',
            },
            {
              id: '1',
              name: 'Crozes Hermitage Les Charmeuses - 2018 - 75CL',
            },
            {
              id: '2',
              name: 'Paxton Australie The Guesser Rg - 2017 75CL',
            },
            {
              id: '3',
              name: 'Luberon Grand Marrenon Aop Blanc - 2019   75CL',
            },
          ]),
        )
      }, 2000)
    }
  }, [dataStateCursor])

  const onLoadMore = useCallback(() => {
    if (dataStateCursor === 'done') {
      setDataStateCursor('reloading')
      setData(
        reloadingOk<void, Item[]>([
          {
            id: '0',
            name: 'Malbec Des Rochelles Rg - 2019 - 75CL *bio',
          },
          {
            id: '1',
            name: 'Crozes Hermitage Les Charmeuses - 2018 - 75CL',
          },
        ]),
      )
    }
  }, [dataStateCursor])

  return (
    <TableView
      columns={columns}
      data={data}
      keyExtractor={(row) => row.id}
      hideReloadingPlaceholder
      maxHeight={150}
      onLoadMore={onLoadMore}
      {...props}
    />
  )
}

type LayoutItem = {
  firstContent: string
  secondContent: string
}

type LayoutsProp = {
  layouts: TableLayout[]
}

const Layout: StoryFn<TableViewProps & LayoutsProp> = (
  props: Partial<TableViewProps & LayoutsProp>,
) => {
  const columns: Array<TableColumn<LayoutItem>> = [
    {
      key: 'first-column',
      name: 'First',
      layout: props.layouts?.[0],
      render: ({ data }) => <TextStyle> {data.firstContent} </TextStyle>,
    },
    {
      key: 'second-column',
      name: 'Second',
      layout: props.layouts?.[1],
      render: ({ data }) => <TextStyle> {data.secondContent} </TextStyle>,
    },
  ]
  const keyExtractor = (row: LayoutItem) => row.firstContent

  return (
    <TableView
      columns={columns}
      // @ts-ignore
      data={props.data}
      keyExtractor={keyExtractor}
      {...props}
    />
  )
}

export default {
  title: 'Resources/Tables/TableView',
  component: TableView,
  parameters: { actions: { argTypesRegex: '^on.*' } },
} as Meta<typeof TableView>

export const Default = Basic.bind({})
Default.storyName = 'Default'
Default.args = {}

export const SelectionMulti = Basic.bind({})
SelectionMulti.storyName = 'Selection Multi'
SelectionMulti.args = { selectionEnabled: true }

export const DisabledRows = Basic.bind({})
DisabledRows.storyName = 'Disabled Rows & disabled all selection'
DisabledRows.args = {
  disabledRowsKeys: ['1'],
  selectAllEnabled: false,
  selectionEnabled: true,
}

export const InitialAllSelected = Basic.bind({})
InitialAllSelected.storyName = 'All selected by default'
InitialAllSelected.args = {
  initialAllSelected: true,
  selectionEnabled: true,
}

export const LimitedDimensions = Basic.bind({})
LimitedDimensions.storyName = 'Limited dimensions'
LimitedDimensions.args = {
  maxWidth: 500,
  minHeight: 50,
  maxHeight: 100,
}

export const EmptyData = Empty.bind({})
EmptyData.storyName = 'Empty data'
EmptyData.args = {}

export const EmptyDataPlaceholder = Empty.bind({})
EmptyDataPlaceholder.storyName = 'Empty data with custom placeholder'
EmptyDataPlaceholder.args = {
  placeholderEmptyState: <h3> No result to display </h3>,
}

export const ErroredRows = Basic.bind({})
ErroredRows.storyName = 'Errored rows'
ErroredRows.args = {
  erroredRowsMap: [
    { key: '1', message: 'second item error' },
    { key: '2', message: 'third item error' },
  ],
}

export const FilePicker = Basic.bind({})
FilePicker.storyName = 'Picking files dropped (csv, excel)'
FilePicker.args = {
  typesDrop: ['csv', 'excel'],
  onSuccessDrop: action('onSuccessDrop'),
  onErrorDrop: action('onErrorDrop'),
}

export const WithSearchBar = Basic.bind({})
WithSearchBar.storyName = 'Searching bar included'
WithSearchBar.args = {
  searchPlaceholder: 'Searching placeholder',
  onSearchQueryChange: action('onSearchQueryChange'),
}

export const LoadingData = Loading.bind({})
LoadingData.storyName = 'Loading & Scroll-to-row'
LoadingData.args = {}

export const LayoutVariationOne = Layout.bind({})
LayoutVariationOne.storyName = 'First column hidden'
LayoutVariationOne.args = {
  maxWidth: 500,
  data: doneOk<void, LayoutItem[]>([
    {
      firstContent: 'hidden',
      secondContent: 'value',
    },
  ]),
  layouts: [{ hidden: true }, {}],
}

export const LayoutVariationTwo = Layout.bind({})
LayoutVariationTwo.storyName = 'Different cells width'
LayoutVariationTwo.args = {
  maxWidth: 500,
  data: doneOk<void, LayoutItem[]>([
    {
      firstContent: 'Small cell',
      secondContent: 'Wide cell',
    },
  ]),
  layouts: [{ width: px(100) }, { width: px(200) }],
}

export const LayoutVariationThree = Layout.bind({})
LayoutVariationThree.storyName = 'Row scrollable and sticky column'
LayoutVariationThree.args = {
  maxWidth: 500,
  data: doneOk<void, LayoutItem[]>([
    {
      firstContent: 'Min. 70% cell',
      secondContent: 'Min. wide cell',
    },
  ]),
  layouts: [{ minWidth: pct(70), sticky: true }, { minWidth: px(300) }],
}

export const LayoutVariationFour = Layout.bind({})
LayoutVariationFour.storyName = 'Different cells content alignments'
LayoutVariationFour.args = {
  maxWidth: 600,
  data: doneOk<void, LayoutItem[]>([
    {
      firstContent: 'Dynamic wide cell Y aligned on top with large right space',
      secondContent: 'X aligned at center',
    },
  ]),
  layouts: [
    { width: fr(4), alignX: 'flexEnd', alignY: 'flexStart', margin: 'large' },
    { alignX: 'center' },
  ],
}

export const LayoutVariationFive = Layout.bind({})
LayoutVariationFive.storyName = 'Hide first column based on viewport width'
LayoutVariationFive.args = {
  data: doneOk<void, LayoutItem[]>([
    {
      firstContent: 'Hidden below wide desktop dimensions',
      secondContent: '<< resize table',
    },
  ]),
  layouts: [{ defaultWidth: px(200), breakpoint: 'large' }, {}],
}
