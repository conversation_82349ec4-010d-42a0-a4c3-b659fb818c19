import { StoryFn, Meta } from '@storybook/react'

import {
  Stack,
  StackProps,
} from '../../../src/resources/layout-and-structure/Stack'

const Template: StoryFn<StackProps> = (props: StackProps) => (
  <div style={{ width: '100%', border: '1px dotted lightgrey' }}>
    <Stack {...props}>
      <span style={{ background: 'azure' }}>element A</span>
      <span style={{ background: 'aliceblue' }}>element B</span>
    </Stack>
  </div>
)

export default {
  title: 'Resources/Layout and structure/Stack',
  component: Stack,
} as Meta<typeof Stack>

export const Default = Template.bind({})
Default.storyName = 'Default'
Default.args = {}

export const Space = Template.bind({})
Space.storyName = 'With huge spacing between elements'
Space.args = { space: 'huge' }

export const Align = Template.bind({})
Align.storyName = 'With centered alignment'
Align.args = { align: 'center' }
