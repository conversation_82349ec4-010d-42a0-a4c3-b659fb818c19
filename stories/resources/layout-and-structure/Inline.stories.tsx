import { StoryFn, Meta } from '@storybook/react'

import {
  Inline,
  InlineProps,
} from '../../../src/resources/layout-and-structure/Inline'

const Template: StoryFn<InlineProps> = (props: InlineProps) => (
  <div
    style={{
      ...(props.grow ? { width: '100%' } : {}),
      border: '1px dotted lightgrey',
    }}
  >
    <Inline {...props}>
      {props.children || [
        <span style={{ background: 'azure' }}>element A</span>,
        <span style={{ background: 'aliceblue' }}>element B</span>,
      ]}
    </Inline>
  </div>
)

export default {
  title: 'Resources/Layout and structure/Inline',
  component: Inline,
} as Meta<typeof Inline>

export const Default = Template.bind({})
Default.storyName = 'Default'
Default.args = {}

export const Space = Template.bind({})
Space.storyName = 'With huge space between elements'
Space.args = { space: 'huge' }

export const AlignCenter = Template.bind({})
AlignCenter.storyName =
  'Taking all container width and centering elements with small space'
AlignCenter.args = { align: 'center', grow: true, space: 'small' }

export const AlignEnd = Template.bind({})
AlignEnd.storyName =
  'Taking all container width and placing elements at end with medium space'
AlignEnd.args = { align: 'end', grow: true, space: 'medium' }

export const AlignYCenter = Template.bind({})
AlignYCenter.storyName = 'Placing elements vertically at the bottom  '
AlignYCenter.args = {
  alignY: 'bottom',
  children: [
    <span style={{ background: 'azure', paddingTop: 20 }}>element A</span>,
    <span style={{ background: 'aliceblue' }}>element B</span>,
  ],
}

export const GapWrap = Template.bind({})
GapWrap.storyName = 'Wrapping content to next line with huge space'
GapWrap.args = { wrap: true, space: 'huge' }
GapWrap.decorators = [
  (Story, context) => (
    <div style={{ width: '150px' }}>
      <Story {...context} />
    </div>
  ),
]
