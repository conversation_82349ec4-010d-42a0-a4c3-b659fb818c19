// TODO - stories
// TODO - try out tests within stories?
import { StoryFn, Meta } from '@storybook/react'

import {
  SliderTrack,
  SliderTrackProps,
} from '../../../src/resources/layout-and-structure/SliderTrack'

const Template: StoryFn<SliderTrackProps> = (props: SliderTrackProps) => (
  <SliderTrack {...props}>WIP TODO</SliderTrack>
)

export default {
  title: 'Resources/Actions/SliderTrack',
  component: SliderTrack,
} as Meta<typeof SliderTrack>

export const Default = Template.bind({})
Default.storyName = 'Default'
