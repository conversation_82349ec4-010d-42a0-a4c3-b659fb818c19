import { StoryFn, Meta } from '@storybook/react'

import {
  Separator,
  SeparatorProps,
} from '../../../src/resources/layout-and-structure/Separator'

const Template: StoryFn<SeparatorProps> = (props: SeparatorProps) => (
  <Separator {...props} />
)

export default {
  title: 'Resources/Layout and structure/Separator',
  component: Separator,
} as Meta<typeof Separator>

export const Default = Template.bind({})
Default.storyName = 'Default'
Default.args = {}

export const Space = Template.bind({})
Space.storyName = 'With a large horizontal margin'
Space.args = { space: 'large' }
