import { StoryFn, Meta } from '@storybook/react'

import { Box, BoxProps } from '../../../src/resources/layout-and-structure/Box'

const Template: StoryFn<BoxProps> = (props: BoxProps) => (
  <div
    style={{ display: 'flex', width: '100%', border: '1px dotted lightgrey' }}
  >
    <Box {...props}>
      <span style={{ background: 'azure' }}>element A</span>
      <span style={{ background: 'aliceblue' }}>element B</span>
    </Box>
  </div>
)

export default {
  title: 'Resources/Layout and structure/Box',
  component: Box,
} as Meta<typeof Box>

export const Default = Template.bind({})
Default.storyName = 'Default'
Default.args = {}

export const SpaceX = Template.bind({})
SpaceX.storyName = 'With big horizontal padding'
SpaceX.args = { spaceX: 'huge' }

export const SpaceY = Template.bind({})
SpaceY.storyName = 'With vertical padding'
SpaceY.args = { spaceY: 'medium' }

export const SpaceTopBottom = Template.bind({})
SpaceTopBottom.storyName = 'With different padding at top and bottom'
SpaceTopBottom.args = { spaceTop: 'medium', spaceBottom: 'small' }

export const SpaceLeftRight = Template.bind({})
SpaceLeftRight.storyName = 'With different padding at left and right'
SpaceLeftRight.args = { spaceLeft: 'medium', spaceRight: 'small' }
SpaceLeftRight.decorators = [
  (Story, context) => (
    <div style={{ width: '100px' }}>
      <Story {...context} />
    </div>
  ),
]

export const Grow = Template.bind({})
Grow.storyName = 'Growing to take all container space'
Grow.args = { grow: true }
