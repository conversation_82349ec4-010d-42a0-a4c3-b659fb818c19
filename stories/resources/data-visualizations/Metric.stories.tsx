import { StoryFn, Meta } from '@storybook/react'

import {
  Metric,
  MetricProps,
} from '../../../src/resources/data-visualizations/Metric'
import {
  ValueIndicator,
  valueIndicatorCurrencyValue,
} from '../../../src/resources/data-visualizations/ValueIndicator'
import { intlCurrencyEur } from '../../../src/primitives/Intl'

type TemplateProps = Partial<
  Omit<MetricProps, 'children'> & {
    childrenValue: number
  }
>

const Template: StoryFn<TemplateProps> = (props: TemplateProps) => {
  const { childrenValue, ...rest } = props
  return (
    <Metric {...rest} title={rest.title ?? 'Title'}>
      <ValueIndicator
        value={valueIndicatorCurrencyValue(props.childrenValue ?? 20, {
          currency: intlCurrencyEur,
        })}
        variant="standard"
      />
    </Metric>
  )
}

export default {
  title: 'Resources/Data visualizations/Metric',
  component: Metric,
  parameters: { layout: 'centered' },
} as Meta<typeof Metric>

export const Default = Template.bind({})
Default.storyName = 'Default'
Default.args = {}

export const WithLoading = Template.bind({})
WithLoading.storyName = 'With loading'
WithLoading.args = { loading: true }

export const WithLongTitle = Template.bind({})
WithLongTitle.storyName = 'With long title'
WithLongTitle.args = { title: 'I have many things to tell you' }

export const WithLargeChildren = Template.bind({})
WithLargeChildren.storyName = 'With large children'
WithLargeChildren.args = { childrenValue: 1239212.093 }

export const WithProgressionIncrease = Template.bind({})
WithProgressionIncrease.storyName = 'With progression increase'
WithProgressionIncrease.args = { childrenValue: 1239212.093, progression: 33 }

export const WithProgressionDecrease = Template.bind({})
WithProgressionDecrease.storyName = 'With progression decrease'
WithProgressionDecrease.args = { childrenValue: 1239212.093, progression: -3 }

export const WithProgressionConstant = Template.bind({})
WithProgressionConstant.storyName = 'With progression constant'
WithProgressionConstant.args = { childrenValue: 1239212.093, progression: 0 }

export const WithProgressionAndTooltip = Template.bind({})
WithProgressionAndTooltip.storyName = 'With progression constant and tooltip'
WithProgressionAndTooltip.args = {
  childrenValue: 1239212.093,
  progression: 0.4,
  tooltip: (
    <div>
      <div>1 juil. 2021 - 2 jul. 2021</div>
      <div>29 juin 2021 - 30 juin 2021</div>
    </div>
  ),
}
