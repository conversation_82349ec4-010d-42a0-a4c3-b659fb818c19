import { StoryFn, Meta } from '@storybook/react'

import {
  Badge,
  BadgeVariation,
} from '../../../src/resources/feedback-indicators/Badge'

type Props = { variation: BadgeVariation }

const Template: StoryFn<Props> = (props: Props) => (
  <div style={{ display: 'flex', flexDirection: 'column', gap: '5px' }}>
    <Badge children="My badge" {...props} />
    <Badge children="My compact badge" {...props} />
    <Badge children="My hightlighted badge" highlighted {...props} />
    <Badge children="My highlighted compact badge" highlighted {...props} />
  </div>
)

export default {
  title: 'Resources/Feedback indicators/Badge',
  component: Badge,
} as Meta<typeof Badge>

export const Default = Template.bind({})
Default.storyName = 'Default'

export const Primary = Template.bind({})
Primary.storyName = 'With variation primary'
Primary.args = { variation: 'primary' }

export const Neutral = Template.bind({})
Neutral.storyName = 'With variation neutral'
Neutral.args = { variation: 'neutral' }

export const Success = Template.bind({})
Success.storyName = 'With variation success'
Success.args = { variation: 'success' }

export const Danger = Template.bind({})
Danger.storyName = 'With variation danger'
Danger.args = { variation: 'danger' }

export const Warning = Template.bind({})
Warning.storyName = 'With variation warning'
Warning.args = { variation: 'warning' }

export const Important = Template.bind({})
Important.storyName = 'With variation important'
Important.args = { variation: 'important' }

export const Information = Template.bind({})
Information.storyName = 'With variation information'
Information.args = { variation: 'information' }
