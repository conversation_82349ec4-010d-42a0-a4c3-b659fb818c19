import { StoryFn, Meta } from '@storybook/react'

import {
  Banner,
  BannerProps,
} from '../../../src/resources/feedback-indicators/Banner'

const Template: StoryFn<BannerProps> = (props: BannerProps) => (
  <Banner {...props} />
)

export default {
  title: 'Resources/Feedback indicators/Banner',
  component: Banner,
} as Meta<typeof Banner>

export const WithVariation = Template.bind({})
WithVariation.storyName = 'With variation info'
WithVariation.args = {
  textStatus: {
    tag: 'Info',
    value: 'This is some content',
  },
}

export const WithoutCompact = Template.bind({})
WithoutCompact.storyName = 'Without compact '
WithoutCompact.args = {
  textStatus: { tag: 'Info', value: 'This is some content' },
  compact: false,
}

export const WithDetails = Template.bind({})
WithDetails.storyName = 'With details'
WithDetails.args = {
  details: ['one', 'two'],
  textStatus: { tag: 'Info', value: 'This is some content' },
}

export const WithManyDetails = Template.bind({})
WithManyDetails.storyName = 'With many details'
WithManyDetails.args = {
  details: [
    'one',
    'two',
    'three',
    'four',
    'five',
    'six',
    'seven,',
    'height',
    'nine',
    'ten',
    'eleven',
    'twelve',
    'thriteen',
    'fourteen',
  ],
  textStatus: { tag: 'Info', value: 'This is some content' },
}

export const WithMarkdownUrl = Template.bind({})
WithMarkdownUrl.storyName = 'With markdown url'
WithMarkdownUrl.args = {
  textStatus: {
    tag: 'Info',
    value:
      'This is some markdown with an url. Go to[wino.fr](https://www.wino.fr)',
  },
}
