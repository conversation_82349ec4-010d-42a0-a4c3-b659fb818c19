import { StoryFn, Meta } from '@storybook/react'

import {
  CountBadge,
  CountBadgeProps,
} from '../../../src/resources/feedback-indicators/CountBadge'

const Template: StoryFn<CountBadgeProps> = (
  props: Partial<CountBadgeProps>,
) => <CountBadge children="001" {...props} />

export default {
  title: 'Resources/Feedback indicators/CountBadge',
  component: CountBadge,
} as Meta<typeof CountBadge>

export const Default = Template.bind({})
Default.storyName = 'Default'

export const WithSize = Template.bind({})
WithSize.storyName = 'With different size'
WithSize.args = { size: 'medium' }

export const WithVariation = Template.bind({})
WithVariation.storyName = 'With different variation'
WithVariation.args = { variation: 'solid' }

export const WithTone = Template.bind({})
WithTone.storyName = 'With different tone'
WithTone.args = { tone: 'brand' }

export const WithAccent = Template.bind({})
WithAccent.storyName = 'With accent'
WithAccent.args = { accent: true, variation: 'ghost' }
