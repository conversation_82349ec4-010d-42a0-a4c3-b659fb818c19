import { StoryFn, Meta } from '@storybook/react'

import {
  Spinner,
  SpinnerProps,
} from '../../../src/resources/feedback-indicators/Spinner'

const Template: StoryFn<SpinnerProps> = (props: SpinnerProps) => (
  <Spinner {...props} />
)

export default {
  title: 'Resources/Feedback indicators/Spinner',
  component: Spinner,
} as Meta<typeof Spinner>

export const Default = Template.bind({})
Default.storyName = 'Default'

export const SmallSize = Template.bind({})
SmallSize.storyName = 'With small size'
SmallSize.args = { size: 14 }

export const BigSize = Template.bind({})
BigSize.storyName = 'With big size'
BigSize.args = { size: 58 }
