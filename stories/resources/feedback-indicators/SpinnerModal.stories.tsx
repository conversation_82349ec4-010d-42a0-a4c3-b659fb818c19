import { useState, useEffect } from 'react'

import { StoryFn, Meta } from '@storybook/react'

import {
  SpinnerModal,
  SpinnerModalProps,
} from '../../../src/resources/feedback-indicators/SpinnerModal'

const Template: StoryFn<SpinnerModalProps & { testDelay: number }> = (
  props,
) => {
  const [opened, setOpened] = useState(props.opened)

  useEffect(() => {
    setOpened(props.opened)
  }, [props.opened])

  useEffect(() => {
    if (!opened) return

    let timerId = setTimeout(() => {
      setOpened(false)
    }, props.testDelay)

    return () => clearTimeout(timerId)
  }, [opened, props.testDelay])

  return (
    <SpinnerModal
      title={props.title}
      opened={opened}
      loopMessages={props.loopMessages}
    />
  )
}

export default {
  title: 'Resources/Feedback indicators/SpinnerModal',
  component: SpinnerModal,
  argTypes: {
    opened: {
      control: 'boolean',
      description: 'Switch me to open the modal',
    },
    testDelay: {
      control: 'number',
      description: 'TESTING - Delay before auto-close (ms)',
      defaultValue: 2000,
    },
  },
} as Meta<typeof SpinnerModal>

export const Default = Template.bind({})
Default.args = {
  title: 'My progress modal',
  opened: false,
  testDelay: 2000,
  loopMessages: [
    'Customizing the shop',
    'Painting the door',
    'Digging an escape tunnel',
  ],
}
