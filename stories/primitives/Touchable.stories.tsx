import { StoryFn, Meta } from '@storybook/react'

import { Touchable, TouchableProps } from '../../src/primitives/Touchable'

const Template: StoryFn<TouchableProps> = (
  props: Omit<TouchableProps, 'onPress'>,
) => <Touchable onPress={alert} {...props} />

export default {
  title: 'Primitives/Touchable',
  component: Touchable,
} as Meta<typeof Touchable>

export const Default = Template.bind({})
Default.storyName = 'Default'
Default.args = { children: 'My trigger' }

export const Disabled = Template.bind({})
Disabled.storyName = 'Disabled'
Disabled.args = { children: 'My trigger', disabled: true }
