import { StoryFn, Meta } from '@storybook/react'

import { TextInput, TextInputProps } from '../../src/primitives/TextInput'

const Template: StoryFn<TextInputProps> = (props: TextInputProps) => (
  <TextInput {...props} />
)

export default {
  title: 'Primitives/TextInput',
  component: TextInput,
} as Meta<typeof TextInput>

export const Default = Template.bind({})
Default.storyName = 'Default'
Default.args = {
  style: {
    borderWidth: '1px',
    borderColor: 'black',
    borderStyle: 'solid',
    fontWeight: '_600',
    fontSize: '18px',
    fontStyle: 'italic',
    letterSpacing: '2px',
  },
}

export const WithPlaceholder = Template.bind({})
WithPlaceholder.storyName = 'With placeholder'
WithPlaceholder.args = { placeholder: 'My placeholder' }

export const ReadOnly = Template.bind({})
ReadOnly.storyName = 'With readonly input'
ReadOnly.args = { readOnly: true }
