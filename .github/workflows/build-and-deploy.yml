# For more information see: https://help.github.com/actions/language-and-framework-guides/using-nodejs-with-github-actions

name: build-and-deploy

on:
  pull_request:
    types: [ready_for_review, labeled]
  push:
    branches:
      - main
      - dev

jobs:
  build:
    runs-on: ubuntu-latest
    if: >-
      (github.event_name == 'pull_request'
        && (github.event.action == 'ready_for_review'
            || contains(github.event.pull_request.labels.*.name, 'run-ci')
            || contains(github.event.pull_request.labels.*.name, 'run-ci-prod')
      )) || github.event_name == 'push'
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Set general env
        run: |
          echo "NPM_TOKEN=${{ secrets.NPM_TOKEN }}" >> $GITHUB_ENV
          echo "VITE_SENTRY_DSN=${{ secrets.REACT_APP_SENTRY_DSN }}" >> $GITHUB_ENV

      - name: Set preview and staging env
        if: github.ref != 'refs/heads/main' && !contains(github.event.pull_request.labels.*.name, 'run-ci-prod')
        run: |
          echo "VITE_CONTEXT=staging" >> $GITHUB_ENV
          echo "VITE_GATEWAY_URL=${{ secrets.STAGING_REACT_APP_GATEWAY_URL }}" >> $GITHUB_ENV
          echo "VITE_CAVAVIN_URL=${{ secrets.STAGING_REACT_APP_CAVAVIN_URL }}" >> $GITHUB_ENV
          echo "VITE_CAVAVIN_USERS=${{ secrets.STAGING_REACT_APP_CAVAVIN_USERS }}" >> $GITHUB_ENV
          echo "VITE_ANALYTICS_URL=${{ secrets.STAGING_REACT_APP_ANALYTICS_URL }}" >> $GITHUB_ENV
          echo "VITE_SHEET_URL=${{ secrets.STAGING_REACT_APP_SHEET_URL }}" >> $GITHUB_ENV
          echo "VITE_PDF_URL=${{ secrets.STAGING_REACT_APP_PDF_URL }}" >> $GITHUB_ENV

      - name: Set preview and production env
        if: github.ref != 'refs/heads/main' && contains(github.event.pull_request.labels.*.name, 'run-ci-prod')
        run: |
          echo "VITE_CONTEXT=staging" >> $GITHUB_ENV
          echo "VITE_GATEWAY_URL=${{ secrets.PRODUCTION_REACT_APP_GATEWAY_URL }}" >> $GITHUB_ENV
          echo "VITE_CAVAVIN_URL=${{ secrets.PRODUCTION_REACT_APP_CAVAVIN_URL }}" >> $GITHUB_ENV
          echo "VITE_CAVAVIN_USERS=${{ secrets.PRODUCTION_REACT_APP_CAVAVIN_USERS }}" >> $GITHUB_ENV
          echo "VITE_ANALYTICS_URL=${{ secrets.PRODUCTION_REACT_APP_ANALYTICS_URL }}" >> $GITHUB_ENV
          echo "VITE_SHEET_URL=${{ secrets.PRODUCTION_REACT_APP_SHEET_URL }}" >> $GITHUB_ENV
          echo "VITE_PDF_URL=${{ secrets.PRODUCTION_REACT_APP_PDF_URL }}" >> $GITHUB_ENV

      - name: Set staging env
        if: github.ref == 'refs/heads/dev'
        run: echo "NETLIFY_ALIAS=staging" >> $GITHUB_ENV

      - name: Set production env
        if: github.ref == 'refs/heads/main'
        run: |
          echo "VITE_CONTEXT=production" >> $GITHUB_ENV
          echo "VITE_GATEWAY_URL=${{ secrets.PRODUCTION_REACT_APP_GATEWAY_URL }}" >> $GITHUB_ENV
          echo "VITE_CAVAVIN_URL=${{ secrets.PRODUCTION_REACT_APP_CAVAVIN_URL }}" >> $GITHUB_ENV
          echo "VITE_CAVAVIN_USERS=${{ secrets.PRODUCTION_REACT_APP_CAVAVIN_USERS }}" >> $GITHUB_ENV
          echo "VITE_ANALYTICS_URL=${{ secrets.PRODUCTION_REACT_APP_ANALYTICS_URL }}" >> $GITHUB_ENV
          echo "VITE_SHEET_URL=${{ secrets.PRODUCTION_REACT_APP_SHEET_URL }}" >> $GITHUB_ENV
          echo "VITE_PDF_URL=${{ secrets.PRODUCTION_REACT_APP_PDF_URL }}" >> $GITHUB_ENV

      - uses: actions/setup-node@v4
        with:
          node-version-file: '.nvmrc'

      - name: Build
        run: |
          export NODE_OPTIONS=--max_old_space_size=16384
          yarn build

      - name: Storybook
        if: github.ref == 'refs/heads/main' || github.ref == 'refs/heads/dev'
        run: yarn storybook

      - name: Test (with coverage)
        if: github.ref == 'refs/heads/main' || github.ref == 'refs/heads/dev'
        run: yarn test --coverage

      - name: Test (no coverage)
        if: github.ref != 'refs/heads/main' && github.ref != 'refs/heads/dev'
        run: yarn test

      - name: Upload coverage artifacts
        if: github.ref == 'refs/heads/main' || github.ref == 'refs/heads/dev'
        uses: actions/upload-artifact@v4
        with:
          name: code-coverage-report
          path: coverage

      - name: Upload build artifacts
        if: github.ref == 'refs/heads/main' || github.ref == 'refs/heads/dev'
        uses: actions/upload-artifact@v4
        with:
          name: build-archive
          path: build

      - name: Deploy Storybook to Netlify
        if: github.ref == 'refs/heads/main' || github.ref == 'refs/heads/dev'
        uses: nwtgck/actions-netlify@v3
        with:
          publish-dir: './storybook-static'
          production-branch: main
          github-token: ${{ secrets.GITHUB_TOKEN }}
          enable-pull-request-comment: true
          enable-commit-comment: false
          overwrites-pull-request-comment: true
          alias: ${{ env.NETLIFY_ALIAS }}
        env:
          NETLIFY_AUTH_TOKEN: ${{ secrets.NETLIFY_AUTH_TOKEN }}
          NETLIFY_SITE_ID: ${{ secrets.NETLIFY_STORYBOOK_SITE_ID }}

      - name: Deploy App to Netlify
        uses: nwtgck/actions-netlify@v3
        with:
          publish-dir: './build'
          production-branch: main
          github-token: ${{ secrets.GITHUB_TOKEN }}
          enable-pull-request-comment: true
          enable-commit-comment: false
          overwrites-pull-request-comment: true
          alias: ${{ env.NETLIFY_ALIAS }}
        env:
          NETLIFY_AUTH_TOKEN: ${{ secrets.NETLIFY_AUTH_TOKEN }}
          NETLIFY_SITE_ID: ${{ secrets.NETLIFY_APP_SITE_ID }}
