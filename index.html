<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <link rel="shortcut icon" href="/favicon.ico" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1, shrink-to-fit=no"
    />
    <meta name="theme-color" content="#a01e72" />
    <link rel="manifest" href="/manifest.json" />
    <link rel="stylesheet" href="/reset.css" />
    <link rel="stylesheet" href="/animations.css" />
    <link rel="stylesheet" href="/fonts.css" />
    <link rel="stylesheet" href="/style.css" />
    <link rel="stylesheet" href="/react-dates.css" />
    <title>Wino</title>
  </head>
  <body style="display: flex; min-height: 100vh; flex-direction: column">
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <script>
      // NOTE - Vite doesn't include shims for NodeJS:
      // cannot be defined in the config unless overriding in tests.
      var global = global || window
    </script>
    <script type="module" src="/src/index.tsx"></script>
    <div id="root" style="display: flex; flex: 1"></div>
  </body>

</html>
