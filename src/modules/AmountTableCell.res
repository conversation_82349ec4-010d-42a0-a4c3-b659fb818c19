let style = (~hasSecondaryValue) => {
  let offset = hasSecondaryValue ? 25. : 0.
  // NOTE - putting table column layout align to flexEnd would be circonstancial
  ReactDOM.Style.make(~width=`calc(75px + ${offset->Float.toString}px)`, ~textAlign="right", ())
}

@react.component
let make = (~value, ~secondaryValue=?, ~decimalPrecision=2) => {
  let formate = value =>
    value->Intl.currencyFormat(
      ~currency=#EUR,
      ~minimumFractionDigits=decimalPrecision,
      ~maximumFractionDigits=decimalPrecision,
    )
  let hasSecondaryValue = secondaryValue->Option.isSome

  <div style={style(~hasSecondaryValue)}>
    {switch secondaryValue {
    | Some(secondaryValue) =>
      <Stack space=#xxsmall>
        <TextStyle maxLines=1> {formate(value)->React.string} </TextStyle>
        <TextStyle maxLines=1 size=#xxsmall variation=#normal>
          {(formate(secondaryValue) ++ " " ++ Intl.t("VAT incl."))->React.string}
        </TextStyle>
      </Stack>
    | None => <TextStyle maxLines=1 weight=#semibold> {formate(value)->React.string} </TextStyle>
    }}
  </div>
}

let make = React.memo(make)
