open Intl

type t =
  | January
  | February
  | March
  | April
  | May
  | June
  | July
  | August
  | September
  | October
  | November
  | December

let values = [
  January,
  February,
  March,
  April,
  May,
  June,
  July,
  August,
  September,
  October,
  November,
  December,
]

let isEqual = (a, b) =>
  switch (a, b) {
  | (January, January)
  | (February, February)
  | (March, March)
  | (April, April)
  | (May, May)
  | (June, June)
  | (July, July)
  | (August, August)
  | (September, September)
  | (October, October)
  | (November, November)
  | (December, December) => true
  | _ => false
  }

// NOTE — With ReScript v11, it should be possible
// to use tagged variants and coercion here.
let toString = value =>
  t(
    switch value {
    | January => "January"
    | February => "February"
    | March => "March"
    | April => "April"
    | May => "May"
    | June => "June"
    | July => "July"
    | August => "August"
    | September => "September"
    | October => "October"
    | November => "November"
    | December => "December"
    },
  )

let toDateMonthJsIndex = value => values->Array.getIndexBy(item => value === item)->Option.getUnsafe

let toSelectItem = value => {
  Select.key: value->toString,
  label: value->toString,
  value,
}

let decodeFromJson = json =>
  json
  ->Json.decodeNumber
  ->Option.flatMap(monthJsIndex => values->Array.get(monthJsIndex->Float.toInt))

let encodeToJson = value => value->toDateMonthJsIndex->Int.toFloat->Json.encodeNumber

let request = (~shopId) => {
  let endpoint = (~shopId) => Env.gatewayUrl() ++ "/accounting-export-configurations/" ++ shopId

  let decodeRequestError = _ => ()

  let decodeResponse = response =>
    response
    ->Json.decodeDict
    ->Option.map(dict =>
      dict->Js.Dict.get("fiscalYearOpeningMonth")->Option.flatMap(decodeFromJson)
    )

  Request.make(endpoint(~shopId))->Future.mapOk(decodeResponse)->Future.mapError(decodeRequestError)
}
