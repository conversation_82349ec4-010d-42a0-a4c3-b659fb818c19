open Intl
open Accounting.Types

let kindToHeaderText = kind =>
  switch kind {
  | Transport => t("Transport U. fees")
  | Taxes => t("Taxes U. fees")
  | Other => t("Other U. fees")
  }

let getFeesRowData = (productFees: array<fee>, ~cartFees) =>
  switch cartFees {
  | Some(cartFees) =>
    cartFees->Array.map(cartFee => {
      let match_ = productFees->Array.getBy(fee => fee.kind === cartFee.kind)
      switch match_ {
      | Some(fee) =>
        fee.amount->Intl.currencyFormat(
          ~currency=#EUR,
          ~minimumFractionDigits=3,
          ~maximumFractionDigits=3,
        )
      | _ => ""
      }
    })
  | _ => [""]
  }

let formattedTaxRates = (productTaxes: array<tax>) =>
  productTaxes->Array.reduce("", (acc, tax) =>
    acc ++
    (acc !== "" ? ", " : "") ++
    (tax.rate /. 100.)->Intl.percentFormat(~maximumFractionDigits=2)
  )
