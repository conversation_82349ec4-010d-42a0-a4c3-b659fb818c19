open Intl

module Query = %graphql(`
  query VariantsQuery($search: String, $after: String, $first: Int, $filterBy: InputVariantsQueryFilter) {
    variants(search: $search, after: $after, first: $first, filterBy: $filterBy) {
      pageInfo {
        endCursor
        hasNextPage
      }
      edges {
        node {
          id
          createdAt
          formattedName
          name
          product {
            id
            name
            kind @ppxOmitFutureValue
            color @ppxOmitFutureValue
            wineType @ppxOmitFutureValue
            whiteWineType @ppxOmitFutureValue
            beerType
            producer
            designation
            family
            region
            country
            tax { value }
          }
          stockKeepingUnit
          priceLookUpCode
          internalCode
          supplier { companyName }
          formattedCategory
          alcoholVolume
          purchasedPrice
          formattedPurchasedPrice
          packaging
          bulk
          capacityPrecision
          capacityUnit
          stock {
            rawQuantity
            formattedQuantity
            state @ppxOmitFutureValue
          }
        }
      }
    }
  }
`)

type filters = {
  shopId: option<string>,
  category?: CatalogCategorySelectFilter.parentCategory,
  supplier?: SupplierSelect.supplier,
  producer?: string,
  stock?: CatalogStockRangeSelect.stockRange,
}

type row = {
  id: string,
  formattedName: string,
  createdAt: Js.Date.t,
  productKind: CatalogProduct.Kind.t,
  purchasedPrice: float,
  formattedPurchasedPrice: option<string>,
  packaging: option<int>,
  bulkPrecision: option<int>,
  capacityUnit: option<string>,
  tax: float,
  stockQuantity: option<int>,
  stockFormattedQuantity: option<string>,
  stockState: option<Product.Stock.t>,
  information: CatalogProduct.Information.t,
}

let rowsFromEdgesData = data =>
  data->Array.map(edge => {
    id: edge.Query.node.id,
    createdAt: edge.node.createdAt,
    formattedName: edge.node.formattedName,
    productKind: edge.node.product.kind,
    packaging: edge.node.packaging,
    bulkPrecision: switch edge.node {
    | {bulk: Some(true), capacityPrecision: Some(precision)} => Some(precision)
    | _ => None
    },
    capacityUnit: edge.node.capacityUnit,
    purchasedPrice: edge.node.purchasedPrice->Option.getWithDefault(0.),
    formattedPurchasedPrice: edge.node.formattedPurchasedPrice,
    stockQuantity: edge.node.stock.rawQuantity,
    stockFormattedQuantity: edge.node.stock.formattedQuantity,
    stockState: edge.node.stock.state,
    tax: edge.node.product.tax.value,
    information: {
      productName: edge.node.product.name,
      variantName: edge.node.name,
      sku: ?edge.node.stockKeepingUnit,
      plu: ?edge.node.priceLookUpCode->Option.map(Int.toString),
      internalCode: ?edge.node.internalCode,
      color: ?edge.node.product.color,
      producerName: ?edge.node.product.producer,
      designation: ?edge.node.product.designation,
      productFamily: ?edge.node.product.family,
      wineType: ?edge.node.product.wineType,
      whiteWineType: ?edge.node.product.whiteWineType,
      beerType: ?edge.node.product.beerType,
      region: ?edge.node.product.region,
      country: edge.node.product.country->Option.getWithDefault("—"),
      categoryName: edge.node.formattedCategory->Option.getWithDefault("—"),
      supplierName: ?edge.node.supplier->Option.map(supplier => supplier.companyName),
      alcoholVolume: ?edge.node.alcoholVolume->Option.map(alcoholVolume =>
        alcoholVolume->Intl.decimalFormat(~maximumFractionDigits=1) ++ "°"
      ),
    },
  })

let columns = [
  {
    Table.key: "reference",
    name: t("Product"),
    layout: {width: 65.->#pct, margin: #xxlarge},
    render: ({data: product, disabled}) => {
      let badgeNew = {
        let hoursThreshold = 24
        DateHelpers.diffInHours(Js.Date.make(), product.createdAt) <= hoursThreshold
          ? Some({
              ProductReferenceTableCell.variation: #information,
              text: t("New"),
            })
          : None
      }
      <ProductReferenceTableCell
        disabled badge=?badgeNew productKind=product.productKind information=product.information
      />
    },
  },
  {
    key: "stock",
    name: t("Stock"),
    render: ({data: product, disabled}) =>
      if !disabled {
        <ProductStockTableCell
          value=product.stockFormattedQuantity state=?product.stockState size=#xsmall
        />
      } else {
        <TextStyle variation=#subdued size=#small> {t("Already added")->React.string} </TextStyle>
      },
  },
  {
    key: "purchasedPrice",
    name: t("Purchase price"),
    layout: {width: 140.->#px},
    render: ({data: product, disabled}) =>
      if !disabled {
        <ProductPriceTableCell size=#xsmall value=product.formattedPurchasedPrice />
      } else {
        React.null
      },
  },
]

let setDefaultOrderedQuantityValue: Accounting.Types.productInput => Accounting.Types.productInput = productInput =>
  switch productInput {
  | Unit({product}) =>
    Unit({
      product: {
        ...product,
        quantity: 1,
      },
    })
  | Bulk({product, precision}) =>
    Bulk({
      product: {
        ...product,
        quantity: 1.,
      },
      precision,
    })
  }

module PickerModal = {
  @react.component
  let make = React.memo((~children, ~opened, ~selectedProducts, ~onCommit, ~onRequestClose) => {
    let renderStartText = () => {
      let length = selectedProducts->Array.length
      let shouldBePrimaryView = length > 0
      let formattedText =
        `${length->Int.toString} ` ++ t(isPlural(length) ? "selected products" : "selected product")

      <Inline>
        <TextStyle
          weight={shouldBePrimaryView ? #strong : #regular}
          variation={shouldBePrimaryView ? #primary : #neutral}>
          {formattedText->React.string}
        </TextStyle>
      </Inline>
    }

    let onCommit = () =>
      onCommit(
        selectedProducts->Array.map(row =>
          CartProduct.makeProductInput(
            ~id=row.id,
            ~name=row.formattedName,
            ~description=CatalogProduct.Information.formatDescription(
              ~productKind=row.productKind,
              ~information=row.information,
              (),
            ),
            ~stockRawQuantity=?row.stockQuantity,
            ~packagingRawQuantity=?row.packaging,
            ~taxValue=row.tax,
            ~unitPrice=row.purchasedPrice,
            ~bulkPrecision=?row.bulkPrecision,
            ~capacityUnit=?row.capacityUnit,
            (),
          )->setDefaultOrderedQuantityValue
        ),
      )

    <Modal
      title={t("Product append")}
      variation=#secondary
      compact=false
      allowCommit={selectedProducts->Array.length > 0}
      backgroundColor=Colors.neutralColor00
      renderStartText
      abortButtonText={t("Cancel")}
      commitButtonText={t("Add products")}
      commitButtonVariation=#primary
      commitButtonCallback=onCommit
      opened
      onRequestClose>
      children
    </Modal>
  })
}

let makeVariables = (~search, ~filters, ~after=?, ()) =>
  Query.makeVariables(
    ~first=20,
    ~after?,
    ~search,
    ~filterBy=Query.makeInputObjectInputVariantsQueryFilter(
      ~shopIds=?switch filters.shopId {
      | Some(shopId) => Some({_in: [shopId]})
      | None => None
      },
      ~supplierId=?switch filters.supplier {
      | Some(supplier) =>
        Some(
          Query.makeInputObjectNullableStringEqualsFilter(
            ~_equals=switch supplier.id->Js.Nullable.toOption {
            | Some(supplierId) => supplierId
            | None => %raw(`null`)
            },
            (),
          ),
        )
      | None => None
      },
      ~categoryId=?switch filters.category {
      | Some(category) =>
        Some(
          Query.makeInputObjectNullableStringEqualsFilter(
            ~_equals=switch category.id->Js.Nullable.toOption {
            | Some(categoryId) => categoryId
            | None => %raw(`null`)
            },
            (),
          ),
        )
      | None => None
      },
      ~producer=?switch filters.producer {
      | Some(producer) => Some(Query.makeInputObjectStringEqualsFilter(~_equals=producer, ()))
      | None => None
      },
      ~stock=?switch filters.stock {
      | Some({?min, ?max}) =>
        Some(Query.makeInputObjectNumberRangeFilter(~_min=?min, ~_max=?max, ()))
      | None => None
      },
      (),
    ),
    (),
  )

type queryResult = Js.Promise.t<
  Result.t<
    ApolloClient__React_Hooks_UseApolloClient.ApolloClient.ApolloQueryResult.t__ok<Query.t>,
    ApolloClient__React_Hooks_UseApolloClient.ApolloClient.ApolloError.t,
  >,
>

let rec runScanEdges = (~fetcher: (~after: _) => queryResult, ~cursor=?, ~data=[], ()) =>
  fetcher(~after=cursor)
  ->FuturePromise.fromPromise
  ->Future.mapError(_ => ())
  ->Future.flatMapOk(response =>
    switch response {
    | Ok({
        data: {variants: {pageInfo: {endCursor, hasNextPage: Some(true)}, edges: variants}},
        error: None,
      }) =>
      runScanEdges(~fetcher, ~data=data->Array.concat(variants), ~cursor=?endCursor, ())
    | Ok({data: {variants: {edges: variants}}, error: None}) =>
      Future.value(Ok(data->Array.concat(variants)))
    | _ => Future.value(Error())
    }
  )

module Reducer = {
  type state = {
    searchQuery: string,
    filters: filters,
    selectedProducts: array<row>,
  }

  type action =
    | Searched(string)
    | FiltersUpdated(filters => filters)
    | ProductSelectionUpdated(Table.selection, array<row>)

  let initialState = (~filters) => {
    selectedProducts: [],
    searchQuery: "",
    filters,
  }

  let make = (state, action) =>
    switch action {
    | Searched(searchQuery) => {...state, searchQuery}
    | FiltersUpdated(updateStateFilters) => {...state, filters: updateStateFilters(state.filters)}
    | ProductSelectionUpdated(tableSelection, allProducts) =>
      let prev = state.selectedProducts
      let selectedProducts = switch tableSelection {
      | Selected(keys) =>
        keys->Array.keepMap(selectedRowId =>
          allProducts->Array.concat(prev)->Array.getBy(row => row.id === selectedRowId)
        )
      | All => prev // NOTE - not supported yet from the picker
      }
      {...state, selectedProducts}
    }
}

@react.component
let make = (
  ~opened,
  ~shopId,
  ~supplierId=?,
  ~supplierName="?",
  ~disabledIds=[],
  ~onCommit,
  ~onRequestClose,
) => {
  let (state, dispatch) = React.useReducer(
    Reducer.make,
    Reducer.initialState(~filters={shopId: shopId}),
  )
  let query = Query.use(
    makeVariables(~search=state.searchQuery, ~filters=state.filters, ()),
    ~skip=shopId->Option.isNone,
    ~notifyOnNetworkStatusChange=true,
    ~fetchPolicy=NetworkOnly,
    ~nextFetchPolicy=CacheFirst,
  )

  let asyncResult =
    query->ApolloHelpers.useQueryResultToAsyncResultWithVariablesReloading2(
      state.searchQuery,
      state.filters,
    )
  let data = asyncResult->AsyncResult.mapOk(data => rowsFromEdgesData(data.variants.edges))

  React.useEffect1(() => {
    if !opened && state.searchQuery !== "" {
      dispatch(Searched(""))
    }
    None
  }, [opened])

  ReactUpdateEffect.use1(() => {
    dispatch(FiltersUpdated(prev => {...prev, shopId}))
    None
  }, [shopId])

  React.useEffect1(() => {
    let supplier =
      supplierId->Option.flatMap(id =>
        id !== "" ? Some({SupplierSelect.id: id->Js.Nullable.return, name: supplierName}) : None
      )
    dispatch(FiltersUpdated(prev => {...prev, ?supplier}))
    None
  }, [supplierId])

  let onLoadMore = React.useCallback1(() =>
    switch asyncResult {
    | Done(Ok(data)) if data.variants.pageInfo.hasNextPage === Some(true) =>
      query.fetchMore(
        ~variables=makeVariables(
          ~search=state.searchQuery,
          ~filters=state.filters,
          ~after=?data.variants.pageInfo.endCursor,
          (),
        ),
        ~updateQuery=(prevResult, {fetchMoreResult}) =>
          switch fetchMoreResult {
          | Some({variants: newVariants}) => {
              variants: {
                ...newVariants,
                edges: prevResult.variants.edges->Array.concat(newVariants.edges),
              },
            }
          | None => prevResult
          },
        (),
      )->ignore
    | _ => ()
    }
  , [asyncResult])

  let onSelectChange = React.useCallback2(selection => {
    let rowsData = switch data {
    | Done(Ok(data)) | Reloading(Ok(data)) => data
    | _ => []
    }
    dispatch(ProductSelectionUpdated(selection, rowsData))
  }, (data, disabledIds))

  let filters = [
    <SupplierSelect
      label={t("Supplier")}
      preset=#filter
      size=#compact
      showDefaultItem=true
      hideOverlayFooter=true
      shopId
      value={state.filters.supplier}
      onChange={supplier => dispatch(FiltersUpdated(prev => {...prev, ?supplier}))}
    />,
    <CatalogCategorySelectFilter
      shopId
      value={state.filters.category}
      onChange={category => dispatch(FiltersUpdated(prev => {...prev, ?category}))}
    />,
    <CatalogProducerSelect
      shopId
      value={state.filters.producer}
      onChange={producer => dispatch(FiltersUpdated(prev => {...prev, ?producer}))}
    />,
    <CatalogStockRangeSelect
      disabled={shopId->Option.isNone}
      value={state.filters.stock}
      onChange={stock => dispatch(FiltersUpdated(prev => {...prev, ?stock}))}
    />,
  ]

  let placeholderEmptyState =
    <Placeholder
      status={shopId->Option.isNone
        ? Pending({
            illustration: None,
            title: t("Beware !"),
            message: t("Select the shop in charge of the order to start adding products."),
          })
        : NoDataAvailable}
      childComponent={() =>
        <OrderEditForm.InputSelect
          field=ShopId
          label={t("Shop")}
          placeholder={t("Select a shop")}
          sections=[
            {
              Select.items: Auth.useShops()->Array.map(shop => {
                Select.key: shop.id,
                label: shop.name,
                value: shop.id,
              }),
            },
          ]
        />}
    />

  <PickerModal opened selectedProducts=state.selectedProducts onCommit onRequestClose>
    <AnimatedRender displayed=opened animation=#fadePopinTranslation duration=300>
      <div style={ReactDOMStyle.make(~display="flex", ~minHeight="70vh", ~maxHeight="70vh", ())}>
        <TableView
          searchPlaceholder={t("Search product")}
          columns
          data
          keyExtractor={row => row.id}
          disabledRowsKeys=disabledIds
          selectionEnabled=true
          selectAllEnabled=false
          hideReloadingPlaceholder=true
          compactRows=true
          filters
          placeholderEmptyState
          onSearchQueryChange={value => dispatch(Searched(value))}
          onSelectChange
          onLoadMore
        />
      </div>
    </AnimatedRender>
  </PickerModal>
}

let make = React.memoCustomCompareProps(make, (oldProps, newProps) =>
  oldProps.opened === newProps.opened &&
  oldProps.shopId === newProps.shopId &&
  oldProps.supplierId === newProps.supplierId
)
