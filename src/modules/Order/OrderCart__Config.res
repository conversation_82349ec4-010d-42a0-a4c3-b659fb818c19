type loadingState = {message: option<string>}

type action =
  | ProductPickerModalCalled(bool)
  | GlobalFeeModalCalled(bool)
  | FileImported(Sheet.csvFile, string)
  | CartLoadingStateRequested(option<loadingState>)

type csvFile = {
  src: Sheet.csvFile,
  shopId: string,
}

type state = {
  productPickerModalOpened: bool,
  globalFeeModalOpened: bool,
  file: option<csvFile>,
  loadingState: option<loadingState>,
}

let initialState = {
  productPickerModalOpened: false,
  globalFeeModalOpened: false,
  file: None,
  loadingState: None,
}

let reducer = (state, action) =>
  switch action {
  | ProductPickerModalCalled(newState) => {...state, productPickerModalOpened: newState}
  | GlobalFeeModalCalled(newState) => {...state, globalFeeModalOpened: newState}
  | CartLoadingStateRequested(loadingState) => {...state, loadingState}
  | FileImported(file, shopId) => {
      ...state,
      file: Some({src: file, shopId}),
    }
  }
