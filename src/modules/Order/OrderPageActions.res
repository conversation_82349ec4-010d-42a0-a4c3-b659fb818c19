open Intl

@react.component
let make = (~id, ~statuses, ~edition, ~onRequestCancelEdition) => {
  let (canGoBack, onGoBack) = Navigation.useGoBack()

  let action = statuses->OrderStatus.toAction
  let createOrder = OrderMutations.useCreate()
  let updateOrder = OrderMutations.useUpdate(~id?)
  let moveOrder = OrderMutations.useMove(~id, ~action)
  let unarchive = OrderMutations.useUnarchive(~id?)

  switch statuses->Array.getExn(0) {
  | _ if edition =>
    <Inline space=#small>
      <OrderEditForm.ResetButton
        text={t("Discard")}
        variation=#neutral
        size=#medium
        onPress={_ =>
          switch id {
          | None if canGoBack => onGoBack()
          | _ => onRequestCancelEdition()
          }}
      />
      <OrderEditForm.SubmitButton
        text={t(id->Option.isNone ? "Create draft" : "Save")}
        variation=#success
        size=#medium
        onSubmit={id->Option.isNone ? createOrder : updateOrder}
      />
    </Inline>
  | #DRAFT =>
    <OrderEditForm.SubmitButton
      text={t("Finalize")} variation=#primary size=#medium onSubmit=moveOrder
    />
  | #FINALIZED =>
    <OrderEditForm.SubmitButton
      text={t("Define as accepted")} size=#medium variation=#primary onSubmit=moveOrder
    />
  | #ACCEPTED =>
    <OrderEditForm.SubmitButton
      text={t("Start reception")} size=#medium variation=#primary onSubmit=moveOrder
    />
  | #RECEIVING =>
    <OrderEditForm.SubmitButton
      text={t("Finish reception")} size=#medium variation=#primary onSubmit=moveOrder
    />
  | #RECEIVED if !(statuses->OrderStatus.has(#PAID)) =>
    <OrderEditForm.SubmitButton
      text={t("Define as paid")} size=#medium variation=#primary onSubmit=moveOrder
    />
  | #ARCHIVED =>
    <Button size=#medium variation=#primary onPress={_ => unarchive()->ignore}>
      {t("Unarchive")->React.string}
    </Button>
  | _ => React.null
  }
}

let make = React.memo(make)
