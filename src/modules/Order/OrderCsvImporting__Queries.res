module Query = %graphql(`
  query variants($filterBy: InputVariantsQueryFilter) {
    variants(first: 1, filterBy: $filterBy) {
      edges {
        node {
          id
          formattedName
          formattedDescription
          stockKeepingUnit
          purchasedPrice
          packaging
          stock {
            rawQuantity
          }
          bulk
          capacityUnit
          capacityPrecision
          shop {
            id
          }
          product {
            kind @ppxOmitFutureValue
            tax {
              value
            }
          }
        }
      }
    }
  }
`)

module Fetch = {
  // TODO - add MSW tests
  // TODO - support fetching by variantId
  // NOTE - the order of the rows doesn't guarantee the order of the fetch responses
  let fetchVariantFromSKU = (client: ApolloClient.t, ~shopId, ~stockKeepingUnit) =>
    client.query(
      ~query=module(Query),
      Query.makeVariables(
        ~filterBy=Query.makeInputObjectInputVariantsQueryFilter(
          ~archived=#EXCLUDED,
          ~shopIds=Query.makeInputObjectInFilter(~_in=[shopId], ()),
          ~stockKeepingUnit=Query.makeInputObjectStringEqualsFilter(~_equals=stockKeepingUnit, ()),
          (),
        ),
        (),
      ),
    )
    ->FuturePromise.fromPromise
    ->Future.map(response =>
      switch response {
      | Ok(Ok({data: {variants: {edges}}, error: None})) =>
        Ok(edges[0]->Option.map(edge => edge.node))
      | _ => Error(Request.serverErrorMessage)
      }
    )
}
