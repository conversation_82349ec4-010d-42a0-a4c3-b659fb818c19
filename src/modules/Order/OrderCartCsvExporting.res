open Intl
open Accounting.Types

module Utils = OrderCartCsvExporting__Utils

let purchaseOrder = statuses => {
  switch statuses->Array.getExn(0) {
  | #RECEIVING
  | #RECEIVED => false
  | _ => true
  }
}

let makeFilename = (~statuses, ~name) =>
  t(statuses->purchaseOrder ? "purchaseorder" : "receiptorder") ++
  ("_" ++
  (name->Js.String2.replace("N°", "") ++ ".csv"))

let bigToFloat = Accounting.Formatter.Utils.bigToFloat
let currencyFormat = value =>
  value->Intl.currencyFormat(~currency=#EUR, ~minimumFractionDigits=3, ~maximumFractionDigits=3)

let makePlainText = (
  cart,
  ~name,
  ~statuses,
  ~supplierName,
  ~supplierAddress,
  ~supplierPostalCode,
  ~supplierCity,
  ~supplierCountry,
) => {
  let titleRow = [
    [
      template(t("Order{{entilted}}{{name}}"), ~values={"entilted": " ", "name": name}, ()) ++
      (" - " ++
      t(statuses->purchaseOrder ? "Purchase order" : "Order receipt")),
    ],
  ]

  let supplierHeader = [t("Supplier's informations") ++ " :"]

  let supplierRow = switch supplierAddress {
  | "" => `${supplierPostalCode} ${supplierCity} ${supplierCountry}`
  | _ => `${supplierAddress}, ${supplierPostalCode} ${supplierCity} ${supplierCountry}`
  }

  let feeHeaders = switch cart.fees {
  | Some(fees) => fees->Array.map(fee => fee.kind->Utils.kindToHeaderText)
  | _ => [t("U. fees")]
  }

  let headers = Array.concatMany([
    [
      "SKU",
      t("Product's name"),
      t("Description"),
      t(statuses->purchaseOrder ? "Ordered quantity" : "Received quantity"),
      t("Packaging"),
      t("U. price excl. VAT"),
      t("Discount"),
    ],
    feeHeaders,
    Array.concat(
      switch cart.taxesFree {
      | false => [t("VAT")]
      | _ => []
      },
      [t("Total excl. VAT")],
    ),
  ])

  let productRows = cart.products->Array.map(product => {
    let (
      stockKeepingUnit,
      name,
      description,
      formattedQuantity,
      packaging,
      formattedUnitPrice,
      formattedTotalDiscounts,
      productFees,
      productTaxes,
      formattedTotalAmountExcludingTaxes,
    ) = switch product {
    | Unit(product) => (
        product.stockKeepingUnit,
        product.name,
        product.description,
        product.formattedQuantity,
        product.packaging->Option.map(Int.toFloat),
        Some(product.unitPrice->currencyFormat),
        product.totalDiscounts->Option.map(Big.toFloat)->Option.map(currencyFormat),
        product.fees,
        product.taxes,
        product.totalAmountExcludingTaxes->Option.map(Big.toFloat)->Option.map(currencyFormat),
      )
    | Bulk(product, _) => (
        product.stockKeepingUnit,
        product.name,
        product.description,
        product.formattedQuantity,
        product.packaging->Option.map(Big.toFloat),
        Some(product.unitPrice->currencyFormat),
        product.totalDiscounts->Option.map(Big.toFloat)->Option.map(currencyFormat),
        product.fees,
        product.taxes,
        product.totalAmountExcludingTaxes->Option.map(Big.toFloat)->Option.map(currencyFormat),
      )
    }

    Array.concatMany([
      [
        stockKeepingUnit->Option.getWithDefault(""),
        name,
        description,
        formattedQuantity->Option.getWithDefault(""),
        packaging->Option.mapWithDefault("", Float.toString),
        formattedUnitPrice->Option.getWithDefault(""),
        formattedTotalDiscounts->Option.getWithDefault(""),
      ],
      productFees->Utils.getFeesRowData(~cartFees=cart.fees),
      Array.concat(
        switch (productTaxes, cart.taxesFree) {
        | (Some(taxes), false) => [taxes->Utils.formattedTaxRates]
        | _ => []
        },
        [formattedTotalAmountExcludingTaxes->Option.getWithDefault("")],
      ),
    ])
  })

  let formattedTotalAmountExcludingTaxes =
    bigToFloat(cart.totalAmountExcludingTaxes->Option.getExn)->currencyFormat
  let formattedTotalAmountIncludingTaxes =
    bigToFloat(cart.totalAmountIncludingTaxes->Option.getExn)->currencyFormat
  let formattedTotalTaxes = bigToFloat(cart.totalTaxes->Option.getExn)->currencyFormat

  let totalsRows = Array.concatMany(
    switch cart.taxesFree {
    | false => [
        [[t("Excl. VAT total"), formattedTotalAmountExcludingTaxes]],
        cart.taxes
        ->Option.getExn
        ->Array.keep(tax =>
          switch tax.amount {
          | Some(amount) => bigToFloat(amount) > 0.
          | None => false
          }
        )
        ->Array.map(tax => {
          let formattedTaxAmount = bigToFloat(tax.amount->Option.getExn)->currencyFormat
          let formattedTaxRate = (tax.rate /. 100.)->Intl.percentFormat(~maximumFractionDigits=2)

          [t("VAT") ++ " " ++ formattedTaxRate, formattedTaxAmount]
        }),
        [[t("VAT total"), formattedTotalTaxes]],
        [[t("Total amount incl. VAT"), formattedTotalAmountIncludingTaxes]],
      ]
    | true => [[[t("Total amount excl. VAT"), formattedTotalAmountIncludingTaxes]]]
    },
  )

  // Combines differents data rows with removed blank columns
  Array.concatMany([
    titleRow,
    [[]],
    [supplierHeader],
    [[supplierName]],
    [[Js.String.trim(supplierRow)]],
    [[]],
    [
      headers->Array.keepWithIndex((_, idx) =>
        productRows->Array.some(row => row->Array.getExn(idx) !== "")
      ),
    ],
    productRows->Array.map(row =>
      row->Array.keepWithIndex((_, idx) =>
        productRows->Array.some(row => row->Array.getExn(idx) !== "")
      )
    ),
    [[]],
    totalsRows,
  ])->Sheet.makeCsvPlainText
}

let makeBlob = (
  cart,
  ~name,
  ~statuses,
  ~supplierName,
  ~supplierAddress,
  ~supplierPostalCode,
  ~supplierCity,
  ~supplierCountry,
) =>
  cart
  ->makePlainText(
    ~name,
    ~statuses,
    ~supplierName,
    ~supplierAddress,
    ~supplierPostalCode,
    ~supplierCity,
    ~supplierCountry,
  )
  ->Result.map(Sheet.makeCsvBlobFromPlainText)
