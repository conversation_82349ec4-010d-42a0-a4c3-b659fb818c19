module Formatter = OrderSupplierInformationCard__Config__Formatters

type data = OrderSupplierInformationCard__Queries.SupplierQuery.t_supplier

type supplierContact = {
  formattedName: string,
  email: option<string>,
  phoneNumber: option<string>,
  mobileNumber: option<string>,
}

type supplierLocation = {
  recipient: option<string>,
  address: option<string>,
  formattedCity: option<string>,
  country: string,
}

let supplierContactFromData: data => option<supplierContact> = data =>
  data.contacts.edges
  ->Array.getBy(({node}) => node.isDefault)
  ->Option.map(({node: contact}) => {
    formattedName: Formatter.makeContactName(
      ~civility=contact.civility->Option.map(civility => civility->SupplierCivility.toShortLabel),
      ~lastName=contact.lastName,
      ~firstName=contact.firstName,
    ),
    email: contact.email,
    phoneNumber: contact.phoneNumber,
    mobileNumber: contact.mobileNumber,
  })

let supplierLocationFromData: data => option<supplierLocation> = data =>
  data.locations.edges
  ->Array.get(0)
  ->Option.map(({node: location}) => {
    recipient: location.recipient,
    address: location.address,
    formattedCity: Formatter.makeCity(~postalCode=location.postalCode, ~cityName=location.city),
    country: location.country->Option.getWithDefault("France"),
  })
