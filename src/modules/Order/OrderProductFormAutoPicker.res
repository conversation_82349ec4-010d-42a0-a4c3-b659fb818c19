exception TaxValueNotFound

type status =
  | Pristine
  | Loading
  | Success(int)
  | Error

module MissingStockVariantsQuery = %graphql(`
  query MissingStockVariantsQuery($after: String, $supplierId: String!) {
    missingStockVariants(first: 50, after: $after, filterBy: { supplierId: { _equals: $supplierId } }) {
      pageInfo {
        endCursor
        hasNextPage
      }
      edges {
        node {
          id
          formattedName
          formattedDescription
          stockKeepingUnit
          purchasedPrice
          packaging
          bulk
          capacityPrecision
          maxStockThreshold
          stock {
            rawQuantity
          }
          product {
            kind @ppxOmitFutureValue
            tax {
              value
            }
          }
        }
      }
    }
  }
`)

type data = MissingStockVariantsQuery.t_missingStockVariants_edges

let rec runScanMissingStockVariants = (
  ~apolloClient: ApolloClient.Bindings.Client.React.Types.ApolloClient.t,
  ~supplierId: string,
  ~cursor: option<string>=?,
  ~data: array<data>=[],
  ~onDone: (status, array<data>) => unit,
  (),
) =>
  apolloClient.query(
    ~query=module(MissingStockVariantsQuery),
    ~fetchPolicy=NetworkOnly,
    MissingStockVariantsQuery.makeVariables(~after=?cursor, ~supplierId, ()),
  )
  ->FuturePromise.fromPromise
  ->Future.get(response =>
    switch response {
    | Ok(Ok({
        data: {
          missingStockVariants: {pageInfo: {endCursor, hasNextPage: Some(true)}, edges: variants},
        },
        error: None,
      })) =>
      runScanMissingStockVariants(
        ~supplierId,
        ~apolloClient,
        ~data=data->Array.concat(variants),
        ~cursor=?endCursor,
        ~onDone,
        (),
      )
    | Ok(Ok({data: {missingStockVariants: {edges: variants}}, error: None})) =>
      // NOTE - tmp: removes variants of bulk type
      let allVariants =
        data
        ->Array.concat(variants)
        ->Array.keep(({node: variant}) => variant.bulk->Option.getWithDefault(false) === false)

      onDone(Success(allVariants->Array.length), allVariants)
    | _ => onDone(Error, [])
    }
  )

@react.component
let make = (
  ~status: status,
  ~orderId: option<string>,
  ~supplierId: string,
  ~supplierName: option<string>,
  ~onQueryStatusChange: status => unit,
  ~onPickProducts,
) => {
  let apolloClient = ApolloClient.React.useApolloClient()
  let (opened, setOpened) = React.useState(() => false)

  // Opens process validation modal on supplier selection
  React.useEffect2(() => {
    switch (opened, orderId, supplierName, supplierId) {
    | (false, None, Some(_), supplierId) if supplierId !== "" => setOpened(_ => true)
    | _ => ()
    }
    None
  }, (supplierId, supplierName))

  // Gets all low stock variants
  React.useEffect1(() => {
    if status === Loading {
      runScanMissingStockVariants(
        ~supplierId,
        ~apolloClient,
        ~onDone=(status, data) => {
          onQueryStatusChange(status)
          data
          ->Array.map(({node: variant}) => {
            let rawQuantity = CartProduct.makeQuantity(
              ~maxStockThreshold=?variant.maxStockThreshold,
              ~stockRawQuantity=?variant.stock.rawQuantity,
              ~packaging=?variant.packaging,
              ~bulkPrecision=?variant.capacityPrecision,
              (),
            )

            CartProduct.makeProductInput(
              ~id=variant.id,
              ~name=variant.formattedName,
              ~description=switch variant.formattedDescription {
              | "" => Intl.t("Description not filled in")
              | description => description
              },
              ~sku=?variant.stockKeepingUnit,
              ~stockRawQuantity=?variant.stock.rawQuantity,
              ~packagingRawQuantity=?variant.packaging,
              ~rawQuantity,
              ~taxValue=variant.product.tax.value,
              ~unitPrice=?variant.purchasedPrice,
              (),
            )
          })
          ->onPickProducts
        },
        (),
      )
    }
    None
  }, [status])

  React.null
}

let make = React.memoCustomCompareProps(make, (oldProps, newProps) =>
  oldProps.status === newProps.status &&
  oldProps.supplierId === newProps.supplierId &&
  oldProps.supplierName === newProps.supplierName
)
