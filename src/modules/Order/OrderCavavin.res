open Intl

let kvisteEndpoint = Env.cavavinUrl() ++ "/cart/update"

let checkProductDataKvisteAPI = {
  let encodeProduct = product => {
    let (quantity, stockKeepingUnit) = switch product {
    | Accounting.Types.Unit({quantity, stockKeepingUnit}) => (
        quantity->Int.toFloat,
        stockKeepingUnit,
      )
    | Bulk({quantity, stockKeepingUnit}, _) => (quantity->Big.toFloat, stockKeepingUnit)
    }

    switch stockKeepingUnit {
    | Some(reference) =>
      Ok(
        Js.Dict.fromArray([
          ("quantity", quantity->Json.encodeNumber),
          ("reference", reference->Json.encodeString),
        ])->Json.encodeDict,
      )
    | _ => Error()
    }
  }

  exception ResponseError(string)

  let sendToKviste = (~cartProducts, ~shopId: string) => {
    let payload = Js.Dict.empty()

    payload->Js.Dict.set(
      "cart",
      cartProducts
      ->Array.keepMap(product =>
        switch product->encodeProduct {
        | Ok(product) => Some(product)
        | Error() => None
        }
      )
      ->Json.encodeArray,
    )

    Fetch.make(
      kvisteEndpoint,
      {
        method: #POST,
        body: payload->Json.encodeDict->Json.stringify->Fetch.Body.string,
        headers: Fetch.Headers.fromObject({
          "Content-Type": "application/json",
          "x-shop-id": shopId,
        }),
      },
    )
    ->Promise.then(Fetch.Response.json)
    ->Promise.then(json =>
      switch json->Json.decodeDict {
      | Some(dict) =>
        let success = switch (
          dict->Js.Dict.get("success")->Option.flatMap(Json.decodeBoolean),
          dict->Js.Dict.get("sucess")->Option.flatMap(Json.decodeBoolean),
        ) {
        | (Some(success), _) | (_, Some(success)) => success
        | _ => failwith("Cannot decode success property")
        }
        let redirect = dict->Js.Dict.get("redirect")->Option.flatMap(Json.decodeString)
        let decodeError = response => {
          switch response->Json.decodeDict {
          | Some(dict) =>
            let reference = dict->Js.Dict.get("reference")->Option.flatMap(Json.decodeString)
            let message = dict->Js.Dict.get("message")->Option.flatMap(Json.decodeString)
            switch (reference, message) {
            | (Some(reference), Some(message)) => Some({"message": message, "reference": reference})
            | _ => None
            }
          | _ => None
          }
        }
        let errors =
          dict
          ->Js.Dict.get("errors")
          ->Option.flatMap(Json.decodeArray)
          ->Option.map(errors => errors->Array.keepMap(decodeError))

        Promise.resolve({
          "success": success,
          "redirect": redirect,
          "errors": errors,
        })
      | _ => Promise.reject(ResponseError("Cannot decode json response"))
      }
    )
    ->FuturePromise.fromPromise
    ->Future.mapError(_ => t("Kviste service is temporarily unavailable."))
  }

  (~cartProducts, ~shopId, ~notifier, ~onRequestCartRowsError, ~onRequestCartLoading) => {
    onRequestCartLoading(Some({OrderCart__Config.message: None}))

    sendToKviste(~cartProducts, ~shopId)
    ->Future.tap(_ => onRequestCartLoading(None))
    ->Future.mapOk(result =>
      switch (result["errors"], result["redirect"]) {
      | (None, Some(link)) if result["success"] =>
        notifier.Notifier.reset(
          Success(
            template(
              t("The order has successfully been sent to Kviste. [Get to cart]({{link}})"),
              ~values={"link": link},
              (),
            ),
          ),
          (),
        )
      | (Some(errors), _) =>
        let cartErrors = errors->Array.keepMap(error =>
          switch cartProducts->Array.keepMap(
            product => {
              let (productId, sku) = switch product {
              | Unit({id, stockKeepingUnit}) | Bulk({id, stockKeepingUnit}, _) => (
                  id,
                  stockKeepingUnit,
                )
              }

              switch sku {
              | Some(sku) if sku === error["reference"] => Some(productId)
              | _ => None
              }
            },
          ) {
          | [productId] => Some({Table.key: productId, message: t(error["message"])})
          | _ => None
          }
        )

        onRequestCartRowsError(cartErrors)
      | _ => ()
      }
    )
    ->Future.tapError(message => notifier.add(Error(message), ()))
    ->ignore
  }
}
