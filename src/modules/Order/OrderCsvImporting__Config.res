type orderRow = {
  sku: string,
  quantity: float,
  unitPrice: option<float>,
}

type orderCell =
  | Reference(string)
  | Quantity(string)
  | Price(option<string>)
  | Unknown

let cellFromIndex = (index, value) =>
  switch index {
  | 0 => Reference(value)
  | 1 => Quantity(value)
  | 2 => Price(Some(value))
  | _ => Unknown
  }

// NOTE - not strict: it only seek for a decimal value inside the string
let floatFromString = value =>
  switch value->Js.String2.replace(",", ".")->Float.fromString {
  | Some(value) => Ok(value)
  | None => Error()
  }

let referenceRegex = %re("/^[\w-]+$/")
let quantityAndPriceRegex = %re("/^(?:(?:\d+(?:,\d{1,3})?(?:\.\d{1,3})?)|(?:.\d+))$/")

let validateCellFromLabel = cell =>
  switch cell {
  | Reference(value) =>
    let test = referenceRegex->Js.Re.test_(value)
    test ? Ok() : Error("invalid reference (SKU)")
  | Quantity(value) =>
    let test = quantityAndPriceRegex->Js.Re.test_(value)
    let test =
      test && value->floatFromString->Result.mapWithDefault(false, quantity => quantity > 0.)
    test ? Ok() : Error("invalid quantity")
  | Price(valueOptional) =>
    let test = switch valueOptional {
    | Some(value) => quantityAndPriceRegex->Js.Re.test_(value)
    | _ => true
    }
    test ? Ok() : Error("invalid purchasing price")
  | Unknown => Error("invalid column")
  }

let entryValueFromOption = entry =>
  switch entry {
  | Some(entry) => entry
  | None => failwith("Unexpected error when parsing an Order CSV entry.")
  }

exception StringToQuantityFailure
exception StringToUnitPriceFailure

let rowFromEntryExn = entry => {
  let (reference, quantity, price) = (
    entry->Array.get(0)->entryValueFromOption,
    entry->Array.get(1)->entryValueFromOption,
    entry->Array.get(2),
  )

  {
    sku: reference,
    quantity: switch quantity->floatFromString {
    | Ok(value) => value
    | Error() => raise(StringToQuantityFailure)
    },
    unitPrice: price->Option.map(price =>
      switch price->floatFromString {
      | Ok(value) => value
      | Error() => raise(StringToUnitPriceFailure)
      }
    ),
  }
}

module ParserConfig = {
  type cell = orderCell
  type row = orderRow

  let cellFromEntryIndex: (int, string) => cell = cellFromIndex
  let validateCellFromLabel: cell => Result.t<unit, string> = validateCellFromLabel
  let rowFromEntry = rowFromEntryExn
}
