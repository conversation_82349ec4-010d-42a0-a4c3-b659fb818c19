open Intl

@react.component
let make = (~statuses: array<OrderStatus.t>) =>
  <Inline wrap=true space=#small>
    {statuses
    ->Array.map(x =>
      switch x {
      | #ARCHIVED => <Badge variation=#neutral> {t("Archived")->React.string} </Badge>
      | #DRAFT => <Badge variation=#neutral> {t("Draft")->React.string} </Badge>
      | #FINALIZED => <Badge variation=#success> {t("Finalized")->React.string} </Badge>
      | #ACCEPTED => <Badge variation=#success> {t("Accepted")->React.string} </Badge>
      | #REFUSED => <Badge variation=#danger> {t("Declined")->React.string} </Badge>
      | #NOT_RECEIVED => <Badge variation=#normal> {t("Not received")->React.string} </Badge>
      | #RECEIVING =>
        <Badge variation=#information> {t("Reception in progress")->React.string} </Badge>
      | #RECEIVED => <Badge variation=#success> {t("Reception finished")->React.string} </Badge>
      | _ if statuses->OrderStatus.has(#TO_PAY) =>
        <Badge variation=#warning> {t("To pay")->React.string} </Badge>
      | _ if statuses->OrderStatus.has(#PAID) =>
        <Badge variation=#success> {t("Paid")->React.string} </Badge>
      | _ => React.null
      }
    )
    ->React.array}
  </Inline>

let make = React.memo(make)
