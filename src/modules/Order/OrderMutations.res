open Intl

module OrderQuery = OrderEdit__Queries.OrderQuery

exception UpdateOrderMutation_IdNotFound

module CreateOrderMutation = %graphql(`
    mutation CreateOrderMutation($input: InputCreateOrder!) {
      createOrder(input: $input) {
        id
        formattedName
        shopName
        supplierCompanyName
        issueDate
        receptionFinishedAt
        estimatedReceptionDate
        formattedStatus @ppxOmitFutureValue
        formattedTotalAmountIncludingTaxes
        totalProductsQuantity
        totalProductsExpectedQuantity
        supplier { id }
      }
    }
  `)

module UpdateOrderMutation = %graphql(`
    mutation UpdateOrderMutation($id: ID!, $input: InputUpdateOrder!) {
      updateOrder(id: $id, input: $input) {
        id
        formattedName
        condition
        noteForSupplier
        supplierNote
        supplier { id }
        issueDate
        estimatedReceptionDate
        note
        cart
        updatedAt
      }
    }
  `)

module MoveOrderMutation = %graphql(`
    mutation MoveOrderMutation($id: ID!, $action: OrderAction) {
      moveOrder(id: $id, action: $action) {
        id
        formattedStatus
      }
    }
  `)

module ArchiveOrderMutation = %graphql(`
    mutation ArchiveOrderMutation($id: ID!) {
      archiveOrder(id: $id) {
        id
        formattedStatus
      }
    }
  `)

module UnarchiveOrderMutation = %graphql(`
    mutation UnarchiveOrderMutation($id: ID!) {
      unarchiveOrder(id: $id) {
        id
        formattedStatus
      }
    }
  `)

let useCreate = () => {
  let (mutate, result) = CreateOrderMutation.use()

  let filterGlobalDiscount = cart => {
    open Accounting.Types
    {
      ...cart,
      discounts: cart.discounts->Array.keep(discount =>
        switch discount.amount {
        | Some(amount) => amount->Big.toFloat > 0.
        | None => false
        }
      ),
    }
  }

  React.useCallback((_, state: OrderEditForm.Lenses.state) => {
    let input = CreateOrderMutation.makeInputObjectInputCreateOrder(
      ~condition=state.condition,
      ~noteForSupplier=state.noteForSupplier,
      ~supplierId=state.supplierId,
      ~issueDate=state.issueDate->Scalar.Datetime.serialize,
      ~estimatedReceptionDate=state.estimatedReceptionDate->Scalar.Datetime.serialize,
      ~note=state.note,
      ~cart=state.cart
      ->filterGlobalDiscount
      ->Accounting.Serializer.serialize
      ->Scalar.Text.serialize,
      ~deviceId=state.deviceId,
      ~shopId=state.shopId,
      (),
    )

    // Additional checks with error message
    switch state {
    | {deviceId: ""} =>
      Future.make(resolve => {
        BugTracker.reportErrorMessage(
          "Empty deviceId error with saved order state: " ++
          Json.stringifyAny(input)->Option.getWithDefault(""),
        )

        resolve(
          Error(
            t(
              "Something wrong happened, please try again or contact the support through the bottom left side corner chat button.",
            ),
          ),
        )
        None
      })
    | _ =>
      mutate(CreateOrderMutation.makeVariables(~input, ()))
      ->ApolloHelpers.mutationPromiseToFutureResult
      ->Future.mapOk(({CreateOrderMutation.createOrder: {id}}) => Some(id))
      // NOTE - force refetching with the new order when going back to list page
      ->Future.tapOk(_ =>
        switch result.client {
        | Some({resetStore}) => resetStore()->ignore
        | _ => ()
        }
      )
    }
  })
}

let useUpdate = (~id=?) => {
  let (mutate, _) = UpdateOrderMutation.use()
  let filterGlobalDiscount = cart => {
    open Accounting.Types
    {
      ...cart,
      discounts: cart.discounts->Array.keep(discount =>
        switch discount.amount {
        | Some(amount) => amount->Big.toFloat > 0.
        | None => false
        }
      ),
    }
  }

  React.useCallback0((_, state: OrderEditForm.Lenses.state) => {
    let input = UpdateOrderMutation.makeInputObjectInputUpdateOrder(
      ~condition=state.condition,
      ~noteForSupplier=state.noteForSupplier,
      ~issueDate=state.issueDate->Scalar.Datetime.serialize,
      ~estimatedReceptionDate=state.estimatedReceptionDate->Scalar.Datetime.serialize,
      ~note=state.note,
      ~supplierId=state.supplierId,
      ~cart=state.cart
      ->filterGlobalDiscount
      ->Accounting.Serializer.serialize
      ->Scalar.Text.serialize,
      (),
    )

    mutate(
      UpdateOrderMutation.makeVariables(
        ~id=switch id {
        | Some(id) => id
        | _ => raise(UpdateOrderMutation_IdNotFound)
        },
        ~input,
        (),
      ),
    )
    ->ApolloHelpers.mutationPromiseToFutureResult
    ->Future.mapOk(({UpdateOrderMutation.updateOrder: {id}}) => Some(id))
  })
}

let useMove = (~id, ~action) => {
  let (mutate, _) = MoveOrderMutation.use()

  React.useCallback1((_, _) => {
    let id = switch id {
    | Some(id) => id
    | _ => failwith("The order should be saved before continuing.")
    }
    mutate(
      MoveOrderMutation.makeVariables(~id, ~action, ()),
      ~refetchQueries=[OrderQuery.refetchQueryDescription(OrderQuery.makeVariables(~id, ()))],
    )
    ->ApolloHelpers.mutationPromiseToFutureResult
    ->Future.mapOk(({MoveOrderMutation.moveOrder: {id}}) => Some(id))
  }, [action])
}

let useArchive = (~id=?) => {
  let (mutate, _) = ArchiveOrderMutation.use()

  React.useCallback(() => {
    let id = switch id {
    | Some(id) => id
    | _ => failwith("The order should be saved before archiving.")
    }
    mutate(
      ArchiveOrderMutation.makeVariables(~id, ()),
      ~refetchQueries=[OrderQuery.refetchQueryDescription(OrderQuery.makeVariables(~id, ()))],
    )
    ->ApolloHelpers.mutationPromiseToFutureResult
    ->Future.mapOk(_ => Some(id))
  })
}

let useUnarchive = (~id=?) => {
  let (mutate, _) = UnarchiveOrderMutation.use()

  React.useCallback(() => {
    let id = switch id {
    | Some(id) => id
    | _ => failwith("The order should be saved before unarchiving.")
    }
    mutate(
      UnarchiveOrderMutation.makeVariables(~id, ()),
      ~refetchQueries=[OrderQuery.refetchQueryDescription(OrderQuery.makeVariables(~id, ()))],
    )
    ->ApolloHelpers.mutationPromiseToFutureResult
    ->Future.mapOk(_ => Some(id))
  })
}
