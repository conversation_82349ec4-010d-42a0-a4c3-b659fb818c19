open Intl
open Accounting.Types
open Accounting.Actions

module CartConfig = OrderCart__Config

module Config = OrderCsvImporting__Config.ParserConfig
module Fetch = OrderCsvImporting__Queries.Fetch

module OrderCsvParserAndDecoder = Sheet.CsvParserAndDecoder(Config)

exception OrderCsvImporting_TaxValueNotFound

type fetchContent = {
  data: option<OrderCsvImporting__Queries.Query.t_variants_edges_node>,
  file: Result.t<OrderCsvImporting__Config.orderRow, string>,
}

// TODO - revamp module:
// - make it more modular to implement unit tests (MSW tests included)
// - remove usage of ReScript ref (make code more functionnal compliant)
// - use explicit records instead of tuples
let make = (
  file: CartConfig.csvFile,
  ~client: ApolloClient.t,
  ~notifier,
  ~cartProducts: array<product>,
  ~cartDispatch,
  ~onRequestCartLoading: option<CartConfig.loadingState> => unit,
) =>
  OrderCsvParserAndDecoder.make(file.src)
  ->Future.mapOk(value => {
    let (data, errors) = value
    let fetchingVariants = ref([])
    let fetchedValidCount = ref(0)
    let fetchedInvalidCount = ref(0)
    let updatedValidCount = ref(0)

    notifier.Notifier.clear()
    onRequestCartLoading(Some({message: None}))

    // Removes in cart products not found in the imported CSV
    cartProducts->Array.forEach(product => {
      let (id, sku) = switch product {
      | Unit({id, stockKeepingUnit}) | Bulk({id, stockKeepingUnit}, _) => (id, stockKeepingUnit)
      }

      switch data->Array.some(current => Some(current.sku) == sku) {
      | false => ProductRemoved(id)->cartDispatch
      | _ => ()
      }
    })

    // Updates cart products quantities & prices
    // and fetch variants to prepare bulk cart update.
    data->Array.forEach(({sku, quantity, unitPrice}) =>
      switch cartProducts->Array.keepMap(
        product => {
          let (productId, currentSku) = switch product {
          | Unit({id, stockKeepingUnit}) | Bulk({id, stockKeepingUnit}, _) => (id, stockKeepingUnit)
          }

          switch currentSku {
          | Some(currentSku) if currentSku === sku => Some((productId, product->Accounting.isBulk))
          | _ => None
          }
        },
      ) {
      // Updates cart product if found
      | [(cartProductId, bulk)] =>
        updatedValidCount := updatedValidCount.contents + 1

        ProductExpectedQuantityUpdated(
          cartProductId,
          bulk ? BulkQuantity(quantity) : UnitQuantity(quantity->Float.toInt),
        )->cartDispatch

        switch unitPrice {
        | Some(price) => ProductUnitPriceUpdated(cartProductId, price)->cartDispatch
        | _ => ()
        }
      // Queue up fetch request if the product wasn't found inside the cart
      | _ =>
        fetchingVariants :=
          fetchingVariants.contents->Array.concat([
            client
            ->Fetch.fetchVariantFromSKU(~shopId=file.shopId, ~stockKeepingUnit=sku)
            ->Future.mapOk(
              response =>
                switch response {
                | None =>
                  fetchedInvalidCount := fetchedInvalidCount.contents + 1

                  {
                    data: None,
                    file: Error(sku),
                  }
                | data =>
                  fetchedValidCount := fetchedValidCount.contents + 1
                  onRequestCartLoading(
                    Some({
                      message: Some(
                        template(
                          t("{{count}} products loaded out of {{total}}"),
                          ~values={
                            "count": fetchedValidCount.contents->Int.toString,
                            "total": fetchingVariants.contents->Array.length->Int.toString,
                          },
                          (),
                        ),
                      ),
                    }),
                  )

                  {
                    data,
                    file: Ok({sku, quantity, unitPrice}),
                  }
                },
            ),
          ])
      }
    )

    // Gets formatted parsing errors
    let parsingErrors = errors->Array.map(error =>
      switch error {
      | InvalidCell({entryIndex: col, rowIndex: row, message: msg}) =>
        template(
          t("{{msg}} at column {{col}} row {{row}}"),
          ~values={"row": row, "col": col, "msg": t(msg)},
          (),
        )
      }
    )

    // Waits for all variants to be fetched before adding new products from CSV
    Future.all(fetchingVariants.contents)->Future.get(results => {
      let productInputs = results->Array.keepMap(
        x =>
          switch x {
          | Result.Ok({data: Some(variantData), file: Ok(fileData)}) =>
            let productInput = CartProduct.makeProductInput(
              ~id=variantData.id,
              ~name=variantData.formattedName,
              ~description=variantData.formattedDescription,
              ~sku=?variantData.stockKeepingUnit,
              ~stockRawQuantity=?variantData.stock.rawQuantity,
              ~packagingRawQuantity=?variantData.packaging,
              ~taxValue=variantData.product.tax.value,
              ~unitPrice=?variantData.purchasedPrice,
              ~capacityUnit=?variantData.capacityUnit,
              ~bulkPrecision=?switch variantData {
              | {bulk: Some(true), capacityPrecision: Some(precision)} => Some(precision)
              | _ => None
              },
              (),
            )
            let updatedUnitPrice = switch productInput {
            | Unit({product: {unitPrice}}) | Bulk({product: {unitPrice}}) =>
              switch fileData.unitPrice {
              | Some(unitPrice) => unitPrice
              | _ => unitPrice
              }
            }
            let updatedProductInput: productInput = switch productInput {
            | Unit({product}) =>
              Unit({
                product: {
                  ...product,
                  quantity: fileData.quantity->Int.fromFloat,
                  unitPrice: updatedUnitPrice,
                },
              })
            | Bulk({product, precision}) =>
              Bulk({
                product: {
                  ...product,
                  quantity: fileData.quantity,
                  unitPrice: updatedUnitPrice,
                },
                precision,
              })
            }

            Some(updatedProductInput)
          | _ => None
          },
      )

      let errorsMessages = results->Array.keepMap(
        x =>
          switch x {
          | Result.Ok({data: None, file: Error(sku)}) =>
            Some(
              template(
                t("{{sku}} could not be found in the current catalog (SKU)"),
                ~values={"sku": sku},
                (),
              ),
            )
          | _ => None
          },
      )

      BatchProductAdded(productInputs)->cartDispatch
      onRequestCartLoading(None)

      // Handles notifications
      if parsingErrors->Array.length + fetchedInvalidCount.contents > 0 {
        let count = parsingErrors->Array.length + fetchedInvalidCount.contents

        notifier.add(
          Error(
            template(
              t(
                count > 1
                  ? "{{count}} products from the imported CSV have not been added to the order"
                  : "{{count}} product from the imported CSV have not been added to the order",
              ) ++ ".",
              ~values={"count": count},
              (),
            ),
          ),
          ~details=parsingErrors->Array.concat(errorsMessages),
          (),
        )
      }
      if fetchedValidCount.contents > 0 || updatedValidCount.contents > 0 {
        notifier.add(
          Success(
            template(
              t(
                parsingErrors->Array.length + fetchedInvalidCount.contents > 0
                  ? "{{count}} products from the imported CSV have been successfully added to the cart!"
                  : "All products from the imported CSV have been successfully added to the cart!",
              ),
              ~values={
                "count": fetchedValidCount.contents + updatedValidCount.contents,
              },
              (),
            ),
          ),
          (),
        )
      }
    })
  })
  ->Future.get(result =>
    switch result {
    | Error(_) =>
      notifier.reset(
        Error(t("Something wrong happened when importing file, please try again.")),
        (),
      )
    | _ => ()
    }
  )
