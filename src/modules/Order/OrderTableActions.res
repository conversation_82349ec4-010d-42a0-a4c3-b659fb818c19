open Intl

@react.component
let make = (
  ~id: string,
  ~statuses: array<OrderStatus.t>,
  ~callbackUrlAfterArchiveAction: option<string>=?,
) => {
  let (archiveModalOpened, setArchiveModalOpened) = React.useState(_ => false)
  let unarchiveOrder = OrderMutations.useUnarchive(~id)

  <>
    <Inline align=#end>
      <Menu variation=#more_round>
        <MenuItem
          content=Text(t("See"))
          action=OpenLink(Route(Order->LegacyRouter.routeToPathname ++ "/" ++ id))
        />
        {switch statuses->Array.getExn(0) {
        | #ARCHIVED =>
          <MenuItem content=Text(t("Unarchive")) action=Callback(() => unarchiveOrder()->ignore) />
        | _ =>
          <MenuItem
            content=Text(t("Archive")) action=Callback(() => setArchiveModalOpened(_ => true))
          />
        }}
      </Menu>
    </Inline>
    <OrderArchiveModal
      orderId=Some(id)
      opened=archiveModalOpened
      commitCallbackUrl=?callbackUrlAfterArchiveAction
      onRequestClose={() => setArchiveModalOpened(_ => false)}
    />
  </>
}

let make = React.memo(make)
