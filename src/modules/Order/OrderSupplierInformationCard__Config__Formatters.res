// TODO - rework this file in a specific PR for formatters

module Utils = {
  let \"+++" = (s1, s2) =>
    switch (s1, s2) {
    | (Some(s1), Some(s2)) => s1 ++ (" " ++ s2)
    | (Some(s1), None) => s1
    | (None, Some(s2)) => s2
    | _ => ""
    }
}

let makeContactName = (~civility, ~lastName, ~firstName) =>
  switch firstName {
  | Some(_) =>
    open Utils
    \"+++"(firstName, Some(lastName))
  | _ =>
    open Utils
    \"+++"(civility, Some(lastName))
  }

let makeCity = (~postalCode, ~cityName) =>
  switch (postalCode, cityName) {
  | (None, None) => None
  | _ =>
    Some({
      open Utils
      \"+++"(postalCode, cityName)
    })
  }
