module CreateOrder = {
  type t = {
    supplierId: string,
    shopId: string,
  }

  module Codecs = {
    let encoder = ({supplierId, shopId}) => (supplierId, shopId)
    let decoder = ((supplierId, shopId)) => Ok({supplierId, shopId})

    let value = JsonCodec.object2(
      encoder,
      decoder,
      JsonCodec.field("supplierId", JsonCodec.string),
      JsonCodec.field("shopId", JsonCodec.string),
    )
  }

  let encode = state => state->JsonCodec.encodeWith(Codecs.value)->QueryString.stringify

  let decode = query =>
    switch query->QueryString.parse->JsonCodec.decodeWith(Codecs.value) {
    | Ok({supplierId, shopId}) => (Some(supplierId), Some(shopId))
    | _ => (None, None)
    }
}
