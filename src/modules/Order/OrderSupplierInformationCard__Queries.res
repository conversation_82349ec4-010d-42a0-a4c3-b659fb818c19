module SupplierQuery = %graphql(`
    query SupplierQuery($id: ID!) {
      supplier(id: $id) {
        id
        companyName
        email
        phoneNumber
        mobileNumber
        contacts {
          edges {
            node {
              isDefault
              civility @ppxOmitFutureValue
              lastName
              firstName
              email
              phoneNumber
              mobileNumber
            }
          }
        }
        locations {
          edges {
            node {
              recipient
              address
              postalCode
              city
              country
            }
          }
        }
      }
    }
  `)
