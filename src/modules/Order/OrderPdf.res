open Intl

type error = OpenWindowError | RequestError(Request.error)

let errorMessage = t("<PERSON><PERSON> could not generate the PDF, please reiterate your request.")

let endpoint = Env.pdfUrl() ++ "/order-document"

let makeRequestBodyJson = (~orderId) => {
  let body = Js.Dict.empty()

  body->Js.Dict.set("orderId", orderId->Json.encodeString)
  body->Json.encodeDict
}

let decodeAndMakeUrlExn = json =>
  json->Json.decodeDict->Json.flatDecodeDictFieldString("url")->Option.getExn->Url.make

let requestAndOpen = (~orderId, ~orderName) => {
  let windowName = template(t("Download {{orderName}} PDF"), ~values={"orderName": orderName}, ())
  let windowUrl = LoadingRouter.baseRoute(
    ~text=template(
      t("Generating PDF of order {{orderName}}"),
      ~values={"orderName": orderName},
      (),
    ),
    (),
  )

  switch WebAPI.window->WebAPI.Window.openUrlWithTarget(windowUrl, windowName) {
  | Some(loadingWindow) =>
    Request.make(endpoint, ~method=#POST, ~bodyJson=makeRequestBodyJson(~orderId))
    ->Future.tap(result =>
      switch result {
      | Ok(json) =>
        let generatedPdfUrl = decodeAndMakeUrlExn(json)

        loadingWindow->WebAPI.Window.setLocation(generatedPdfUrl->Url.href)
      | Error(_serverErrorMessage) => loadingWindow->WebAPI.Window.close
      }
    )
    ->Future.mapError(error => RequestError(error))
  | _ => Future.value(Error(OpenWindowError))
  }
}
