open Intl

exception UnknownStringValue

type t = [
  | #DRAFT
  | #FINALIZED
  | #ACCEPTED
  | #REFUSED
  | #NOT_RECEIVED
  | #RECEIVING
  | #RECEIVED
  | #TO_PAY
  | #PAID
  | #SENT
  | #ARCHIVED
]

let toString = (status: t) => (status :> string)

let toLabel = x =>
  switch x {
  | #DRAFT => t("Draft")
  | #FINALIZED => t("Finalized")
  | #ACCEPTED => t("Accepted - Not received")
  | #REFUSED => t("Refused")
  | #NOT_RECEIVED => t("")
  | #RECEIVING => t("Receiving")
  | #RECEIVED => t("Received")
  | #TO_PAY => t("Received - To pay")
  | #PAID => t("Received - Paid")
  | #SENT => t("Sent")
  | #ARCHIVED => t("Archived")
  }

let fromStringExn = value =>
  switch value {
  | "DRAFT" => #DRAFT
  | "FINALIZED" => #FINALIZED
  | "ACCEPTED" => #ACCEPTED
  | "REFUSED" => #REFUSED
  | "NOT_RECEIVED" => #NOT_RECEIVED
  | "RECEIVING" => #RECEIVING
  | "RECEIVED" => #RECEIVED
  | "TO_PAY" => #TO_PAY
  | "PAID" => #PAID
  | "SENT" => #SENT
  | "ARCHIVED" => #ARCHIVED
  | _ => raise(UnknownStringValue)
  }

type action = [
  | #FINALIZE
  | #ACCEPT
  | #REFUSE
  | #START_RECEPTION
  | #FINISH_RECEPTION
  | #PAY
  | #SEND
  | #CANCEL
]

let has = (orderStatuses, predicate: t) => orderStatuses->Array.some(status => status === predicate)

let toAction = x =>
  switch x {
  | statuses if statuses->has(#DRAFT) => #FINALIZE
  | statuses if statuses->has(#FINALIZED) => #ACCEPT
  | statuses if statuses->has(#ACCEPTED) => #START_RECEPTION
  | statuses if statuses->has(#RECEIVING) => #FINISH_RECEPTION
  | statuses if statuses->has(#RECEIVED) => #PAY
  | _ => #CANCEL
  }
