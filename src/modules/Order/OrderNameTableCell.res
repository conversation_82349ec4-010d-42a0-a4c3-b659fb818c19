open Intl

@react.component
let make = (~value, ~shopName=?, ~id=?) => {
  let orderBaseRoute = Order->LegacyRouter.routeToPathname
  let orderPageRoute = switch id {
  | Some(id) => orderBaseRoute ++ "/" ++ id
  | _ => orderBaseRoute
  }

  <Box spaceY=#xsmall>
    <Stack space={shopName->Option.isSome ? #xxsmall : #none}>
      <TextLink text={t(value)} to=Route(orderPageRoute) />
      {switch shopName {
      | Some(shopName) =>
        <TextStyle size=#xxsmall variation=#normal> {shopName->React.string} </TextStyle>
      | _ => React.null
      }}
    </Stack>
  </Box>
}

let make = React.memo(make)
