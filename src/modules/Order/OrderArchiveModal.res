open Intl

@react.component
let make = (~orderId, ~opened, ~onRequestClose, ~commitCallbackUrl=?) => {
  let navigate = Navigation.useNavigate()
  let archiveOrder = OrderMutations.useArchive(~id=?orderId)

  <Modal
    title={t("Confirm the archiving of this order")}
    opened
    commitButtonText={t("Confirm")}
    commitButtonCallback={_ =>
      archiveOrder()->Future.get(result =>
        switch result {
        | Ok(_) =>
          switch commitCallbackUrl {
          | Some(commitCallbackUrl) => navigate(commitCallbackUrl)
          | None => ()
          }
        | Error(_) => ()
        }
      )}
    abortButtonText={t("Cancel")}
    onRequestClose>
    <Box spaceY=#xxlarge spaceX=#xlarge>
      <TextStyle variation=#normal>
        {t(
          "Once the order is archived, it will only be visible in the order list by applying the \"Archive\" filter.",
        )->React.string}
      </TextStyle>
    </Box>
  </Modal>
}

let make = React.memoCustomCompareProps(make, (oldProps, newProps) =>
  oldProps.orderId === newProps.orderId && oldProps.opened === newProps.opened
)
