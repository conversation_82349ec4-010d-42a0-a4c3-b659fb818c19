open Intl

module Config = OrderSupplierInformationCard__Config
module SupplierQuery = OrderSupplierInformationCard__Queries.SupplierQuery

exception SupplierNotFound

module OrderSupplierFormInputSearch = {
  @react.component
  let make = React.memo(() => {
    let dispatch = OrderEditForm.useFormDispatch()
    let {validation, submission, values} = OrderEditForm.useFormState()
    let {supplierId, supplierName, shopId} = values

    let archivedSupplierNotification = supplierId === "" && supplierName !== ""
    // FIXME - unable to update value within onChange
    // let archivedSupplierNotification = React.useRef(false)
    // let (archivedSupplierNotification, setArchivedSupplierNotification) = React.useState(() =>
    //   false
    // )

    let errorMessage = switch (validation, submission) {
    | (Error(errors), Failed(_)) =>
      errors
      ->Array.keepMap(((field, error)) =>
        switch (field, error) {
        | (OrderEditForm.Schema.Field(OrderEditForm.Lenses.SupplierId), error) => Some(error)
        | _ => None
        }
      )
      ->Array.get(0)
    | _ => None
    }
    let value =
      supplierId !== ""
        ? Some({SupplierSelect.id: supplierId->Js.Nullable.return, name: supplierName})
        : None

    let onChange = React.useCallback0(value => {
      switch value {
      | Some(supplier) =>
        let supplierId = supplier.SupplierSelect.id->Js.Nullable.toOption->Option.getWithDefault("")
        FieldValueChanged(SupplierId, _ => supplierId)->dispatch
        FieldValueChanged(SupplierName, _ => supplier.name)->dispatch
      | None => FieldValueChanged(SupplierId, _ => "")->dispatch
      }
    })

    <Stack space=#normal>
      <SupplierSelect
        label={t("Supplier")}
        preset=#inputField({OverlayTriggerView.required: true, ?errorMessage})
        shopId=Some(shopId)
        value
        onChange
      />
      {if archivedSupplierNotification {
        <Banner
          textStatus=Warning(t("The previously selected supplier cannot be found (archived)."))
        />
      } else {
        React.null
      }}
    </Stack>
  })
}

@react.component
let make = (~editable) => {
  let {supplierId, shopId} = OrderEditForm.useFormState().values
  let asyncQueryResult =
    SupplierQuery.use(
      ~skip=supplierId === "",
      {id: supplierId},
    )->ApolloHelpers.queryResultToAsyncResult

  let selectedSupplierId = switch asyncQueryResult {
  | Done(Ok({supplier: Some({id})})) if id !== "" => id
  | _ => supplierId
  }

  <Card
    title={t("Supplier information")}
    action=?{switch selectedSupplierId {
    | "" => None
    | _ =>
      Some({
        icon: #queue_arrow_right_light,
        title: t("See supplier"),
        handler: OpenLinkNewTab(Route(SupplierRoutes.showRoute(~id=selectedSupplierId))),
      })
    }}>
    {switch asyncQueryResult {
    | _ if shopId === "" =>
      <Banner
        textStatus=Info(t("Please first select the shop below in order to pick a supplier."))
      />
    | NotAsked if editable => <OrderSupplierFormInputSearch />
    | NotAsked | Done(Error(_)) | Reloading(Error(_)) => <Placeholder status=Error />
    | Loading => <Placeholder status=Loading />
    | Done(Ok(data)) | Reloading(Ok(data)) =>
      switch data.supplier {
      | Some(supplier) =>
        <Stack
          space=?{switch editable {
          | true => Some(#xxsmall)
          | _ => None
          }}>
          {switch editable {
          | true => <OrderSupplierFormInputSearch />
          | _ => React.null
          }}
          {switch (supplier.email, supplier.phoneNumber, supplier.mobileNumber) {
          | (None, None, None) if editable => React.null
          | _ =>
            <Box spaceTop=?{editable ? Some(#medium) : None}>
              <Stack space=#xsmall>
                {if !editable {
                  <TextLink
                    text=supplier.companyName
                    to=Route(SupplierRoutes.showRoute(~id=supplier.id))
                    openNewTab=true
                  />
                } else {
                  React.null
                }}
                {switch supplier.email {
                | Some(email) => <TextStyle> {email->React.string} </TextStyle>
                | None => React.null
                }}
                {switch supplier.phoneNumber {
                | Some(phoneNumber) =>
                  <TextStyle> {phoneNumber->Intl.phoneNumberFormat->React.string} </TextStyle>
                | None => React.null
                }}
                {switch supplier.mobileNumber {
                | Some(mobileNumber) =>
                  <TextStyle> {mobileNumber->Intl.phoneNumberFormat->React.string} </TextStyle>
                | None => React.null
                }}
              </Stack>
            </Box>
          }}
        </Stack>
      | _ => raise(SupplierNotFound)
      }
    }}
    {switch asyncQueryResult {
    | Done(Ok({supplier: Some(supplier)})) =>
      <Stack space=#xlarge>
        <InfoBlock title={t("CONTACT")}>
          {switch supplier->Config.supplierContactFromData {
          | Some(contact) =>
            <Stack>
              <TextStyle> {contact.formattedName->React.string} </TextStyle>
              {switch contact.email {
              | Some(email) => <TextStyle> {email->React.string} </TextStyle>
              | None => React.null
              }}
              {switch contact.phoneNumber {
              | Some(phoneNumber) =>
                <TextStyle> {phoneNumber->Intl.phoneNumberFormat->React.string} </TextStyle>
              | None => React.null
              }}
              {switch contact.mobileNumber {
              | Some(mobileNumber) =>
                <TextStyle> {mobileNumber->Intl.phoneNumberFormat->React.string} </TextStyle>
              | None => React.null
              }}
            </Stack>
          | _ =>
            <TextStyle variation=#subdued>
              {t("Contact information not specified")->React.string}
            </TextStyle>
          }}
        </InfoBlock>
        <InfoBlock title={t("ADDRESS")}>
          {switch supplier->Config.supplierLocationFromData {
          | Some(location) =>
            <Stack>
              {switch location.recipient {
              | Some(recipient) => <TextStyle> {recipient->React.string} </TextStyle>
              | _ => React.null
              }}
              {switch location.address {
              | Some(address) => <TextStyle> {address->React.string} </TextStyle>
              | _ => React.null
              }}
              {switch location.formattedCity {
              | Some(formattedCity) => <TextStyle> {formattedCity->React.string} </TextStyle>
              | _ => React.null
              }}
              <TextStyle> {location.country->React.string} </TextStyle>
            </Stack>
          | _ =>
            <TextStyle variation=#subdued> {t("Address not specified")->React.string} </TextStyle>
          }}
        </InfoBlock>
      </Stack>
    | _ => React.null
    }}
  </Card>
}

let make = React.memo(make)
