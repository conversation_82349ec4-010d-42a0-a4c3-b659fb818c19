let initialCart: Accounting.Types.cartInput = {
  discounts: [],
  products: [],
  decimalPrecision: 5,
  currency: Accounting.Types.Eur,
  taxesFree: false,
  standardTaxRate: 20.,
}

module Lenses = %lenses(
  type state = {
    condition: string,
    noteForSupplier: string,
    supplierId: string,
    issueDate: Js.Date.t,
    estimatedReceptionDate: Js.Date.t,
    note: string,
    cart: Accounting.Types.cart,
    shopId: string,
    deviceId: string,
    supplierName: string,
    supplierAddress: string,
    supplierPostalCode: string,
    supplierCity: string,
    supplierCountry: string,
  }
)

include Form.Make(Lenses)
