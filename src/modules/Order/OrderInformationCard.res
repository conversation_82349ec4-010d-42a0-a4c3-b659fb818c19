open Intl

@react.component
let make = (~editable, ~receptionFinishedAt, ~updatedAt) => {
  let state = OrderEditForm.useFormState()
  let dispatch = OrderEditForm.useFormDispatch()

  let scope = Auth.useScope()
  let shops = Auth.useShops()

  let renderSelectedShop = () =>
    switch shops->Array.getBy(shop => shop.id === state.values.shopId) {
    | Some(shop) =>
      <InlineText>
        <TextStyle variation=#normal> {(t("By") ++ " ")->React.string} </TextStyle>
        <TextStyle weight=#strong> {shop.name->React.string} </TextStyle>
      </InlineText>
    | None => React.null
    }

  React.useEffect1(() => {
    if state.values.shopId !== state.initialValues.shopId {
      let maybeShop = shops->Array.getBy(shop => shop.id === state.values.shopId)

      if state.values.supplierId !== "" {
        dispatch(FieldValueChanged(SupplierId, _ => ""))
      }
      if state.values.cart.products->Array.length > 0 {
        dispatch(FieldValueChanged(Cart, _ => state.initialValues.cart))
      }

      switch maybeShop {
      | Some({activeWebDeviceId}) => dispatch(FieldValueChanged(DeviceId, _ => activeWebDeviceId))
      | _ => ()
      }
    }

    None
  }, [state.values.shopId])

  <Card title={t("Orders information")}>
    {<>
      {if editable {
        <Stack space=#large>
          <OrderEditForm.InputDate field=IssueDate label={t("Date of issue")} />
          {switch (scope, state.id) {
          | (Organisation(_), None) =>
            <Stack space=#small>
              <OrderEditForm.InputSelect
                field=ShopId
                label={t("Shop")}
                placeholder={t("Select a shop")}
                sections={
                  let items = shops->Array.map(shop => {
                    Select.key: shop.id,
                    label: shop.name,
                    value: shop.id,
                  })
                  [{Select.items: items}]
                }
              />
              <Box spaceTop=#small>
                <Banner
                  compact=false
                  textStatus=Warning(
                    t(
                      "Beware ! Once the draft is saved, you will no longer be able to edit the shop.",
                    ),
                  )
                />
              </Box>
            </Stack>
          | _ => renderSelectedShop()
          }}
        </Stack>
      } else {
        <Stack space=#xsmall>
          <InlineText>
            <TextStyle variation=#normal> {(t("Issued on") ++ " ")->React.string} </TextStyle>
            <TextStyle>
              {state.values.issueDate->Intl.dateTimeFormat(~dateStyle=#short)->React.string}
            </TextStyle>
          </InlineText>
          {renderSelectedShop()}
          <Inline space=#none>
            <TextStyle variation=#normal> {(t("Last modif. on") ++ " ")->React.string} </TextStyle>
            <TextStyle>
              {switch updatedAt {
              | Some(datetime) =>
                template(
                  t("{{date}} to {{time}}"),
                  ~values={
                    "date": datetime->Intl.dateTimeFormat(~dateStyle=#short),
                    "time": datetime->Intl.dateTimeFormat(~timeStyle=#short),
                  },
                  (),
                )
              | None => t("No registration")
              }->React.string}
            </TextStyle>
          </Inline>
        </Stack>
      }}
    </>}
    {switch shops->Array.getBy(shop => shop.id === state.values.shopId) {
    | Some(shop) =>
      <Stack space=#xlarge>
        <InfoBlock title={t("CONTACT")}>
          {switch shop.legalRepresentative {
          | Some(legalRepresentative) =>
            <TextStyle> {legalRepresentative->React.string} </TextStyle>
          | None => React.null
          }}
          <TextStyle> {shop.email->React.string} </TextStyle>
          <TextStyle> {shop.phoneNumber->React.string} </TextStyle>
        </InfoBlock>
        <InfoBlock title={t("ADDRESS")}>
          <TextStyle> {shop.name->React.string} </TextStyle>
          <TextStyle> {shop.address->React.string} </TextStyle>
          <TextStyle> {(shop.postalCode ++ shop.city)->React.string} </TextStyle>
          <TextStyle> {shop.country->React.string} </TextStyle>
        </InfoBlock>
      </Stack>
    | None => React.null
    }}
    <Divider />
    {switch editable {
    | true =>
      <OrderEditForm.InputDate
        field=EstimatedReceptionDate label={t("Estimated date of reception")}
      />
    | _ =>
      <Stack>
        <InlineText>
          <TextStyle variation=#normal>
            {(t("Reception scheduled on") ++ " ")->React.string}
          </TextStyle>
          <TextStyle>
            {state.values.estimatedReceptionDate
            ->Intl.dateTimeFormat(~dateStyle=#short)
            ->React.string}
          </TextStyle>
        </InlineText>
        {switch receptionFinishedAt {
        | Some(date) =>
          <InlineText>
            <TextStyle variation=#normal>
              {(t("Reception completed on") ++ " ")->React.string}
            </TextStyle>
            <TextStyle> {date->Intl.dateTimeFormat(~dateStyle=#short)->React.string} </TextStyle>
          </InlineText>
        | _ => React.null
        }}
      </Stack>
    }}
  </Card>
}

let make = React.memo(make)
