open Intl

module PaymentAccount = {
  type t = {
    paymentMethod: PaymentMethod.t,
    accountNumber: string,
    accountLabel: string,
    isaComptaJournalCode: option<string>,
  }

  let decodeFromJson = json =>
    switch json->Json.decodeDict {
    | Some(dict) =>
      let maybePaymentMethod =
        dict->Js.Dict.get("paymentMethod")->Option.flatMap(PaymentMethod.decodeFromJson)
      let maybeAccountNumber = dict->Json.decodeDictFieldString("accountNumber")
      let maybeAccountLabel = dict->Json.decodeDictFieldString("label")
      let isaComptaJournalCode = dict->Json.decodeDictFieldString("isaComptaJournalCode")
      switch (maybePaymentMethod, maybeAccountNumber, maybeAccountLabel) {
      | (Some(paymentMethod), Some(accountNumber), Some(accountLabel)) =>
        Some({paymentMethod, accountNumber, accountLabel, isaComptaJournalCode})
      | (None, _, _) | (_, None, _) | (_, _, None) => None
      }
    | None => None
    }

  let encodeToJson = value => {
    let dict = Js.Dict.fromArray([
      ("paymentMethod", value.paymentMethod->PaymentMethod.encodeToJson),
      ("accountNumber", value.accountNumber->Json.encodeString),
      ("label", value.accountLabel->Json.encodeString),
    ])
    dict->Js.Dict.set(
      "isaComptaJournalCode",
      switch value.isaComptaJournalCode {
      | Some(isaComptaJournalCode) => isaComptaJournalCode->Json.encodeString
      | _ => Json.encodedNull
      },
    )
    dict->Json.encodeDict
  }
}

let autocompleteBasicPaymentsAccounts = (~vendor) =>
  [
    (PaymentMethod.Cash, "**********", "R1"),
    (Cheque, "**********", "R2"),
    (DebitCard, "**********", "R3"),
    (BankTransfer, "**********", "R6"),
  ]->Array.map(((paymentMethod, accountNumber, isaComptaJournalCode)) => {
    PaymentAccount.paymentMethod,
    accountNumber,
    accountLabel: template(
      t("Payment {{paymentMethod}}"),
      ~values={"paymentMethod": paymentMethod->PaymentMethod.toLabel},
      (),
    ),
    isaComptaJournalCode: switch vendor {
    | AccountingVendor.Excel => None
    | IsaCompta => Some(isaComptaJournalCode)
    },
  })

module TaxAccount = {
  type t = {
    taxId: string,
    isaComptaCode: option<string>,
    productsSold: (string, string),
    deductibleTax: option<(string, string)>,
  }

  let fillWithStandard = (~taxRate, ~taxId) => {
    switch taxRate {
    | 20. => {
        taxId,
        isaComptaCode: Some("V5"),
        productsSold: ("********", "Ventes marchandises TVA 20%"),
        deductibleTax: Some(("**********", "TVA collectée 20%")),
      }
    | 10. => {
        taxId,
        isaComptaCode: Some("V6"),
        productsSold: ("********", "Ventes marchandises TVA 10%"),
        deductibleTax: Some(("**********", "TVA collectée 10%")),
      }
    | 5.5 => {
        taxId,
        isaComptaCode: Some("V2"),
        productsSold: ("********", "Ventes marchandises TVA 5.5%"),
        deductibleTax: Some(("**********", "TVA collectée 5.5%")),
      }
    | 2.1 => {
        taxId,
        isaComptaCode: Some("V3"),
        productsSold: ("********", "Ventes marchandises TVA 2.1%"),
        deductibleTax: Some(("4457180000", "TVA collectée 2.1%")),
      }
    | 0. => {
        taxId,
        isaComptaCode: Some("Y"),
        productsSold: ("********", "Ventes marchandises TVA 0%"),
        deductibleTax: None,
      }
    | _ => {
        taxId,
        isaComptaCode: None,
        productsSold: ("", ""),
        deductibleTax: Some(("", "")),
      }
    }
  }

  let decodeFromJson = json =>
    switch json->Json.decodeDict {
    | Some(dict) =>
      let taxId = dict->Json.decodeDictFieldString("taxId")
      let isaComptaCode = dict->Json.decodeDictFieldString("isaComptaCode")
      let productsSold =
        dict
        ->Js.Dict.get("productsSold")
        ->Option.flatMap(Json.decodeDict)
        ->Option.flatMap(value => {
          let accountNumber = value->Json.decodeDictFieldString("accountNumber")
          let label = value->Json.decodeDictFieldString("label")
          switch (accountNumber, label) {
          | (Some(accountNumber), Some(label)) => Some((accountNumber, label))
          | (_, _) => None
          }
        })
      let deductibleTax =
        dict
        ->Js.Dict.get("deductibleTax")
        ->Option.flatMap(Json.decodeDict)
        ->Option.flatMap(value => {
          let accountNumber = value->Json.decodeDictFieldString("accountNumber")
          let label = value->Json.decodeDictFieldString("label")
          switch (accountNumber, label) {
          | (Some(accountNumber), Some(label)) => Some((accountNumber, label))
          | (_, _) => None
          }
        })
      switch (taxId, productsSold) {
      | (Some(taxId), Some(productsSold)) =>
        Some({taxId, isaComptaCode, productsSold, deductibleTax})
      | (_, _) => None
      }
    | None => None
    }

  let encodeToJson = ({taxId, isaComptaCode, productsSold, deductibleTax}) =>
    Js.Dict.fromArray([
      ("taxId", taxId->Json.encodeString),
      (
        "isaComptaCode",
        switch isaComptaCode {
        | Some(isaComptaCode) => Json.encodeString(isaComptaCode)
        | None => Json.encodedNull
        },
      ),
      (
        "productsSold",
        switch productsSold {
        | (accountNumber, label) =>
          Js.Dict.fromArray([
            ("accountNumber", accountNumber->Json.encodeString),
            ("label", label->Json.encodeString),
          ])->Json.encodeDict
        },
      ),
      (
        "deductibleTax",
        switch deductibleTax {
        | Some((accountNumber, label)) =>
          Js.Dict.fromArray([
            ("accountNumber", accountNumber->Json.encodeString),
            ("label", label->Json.encodeString),
          ])->Json.encodeDict
        | None => Json.encodedNull
        },
      ),
    ])->Json.encodeDict
}

let endpoint = (~shopId) => Env.gatewayUrl() ++ "/accounting-export-configurations/" ++ shopId

type failure = NotFoundAccountingExportConfigurationFailure | UnknownServerFailure

let decodeInvalidRequestFailure = error =>
  switch error {
  | {Request.kind: "NotFoundAccountingExportConfiguration"} =>
    NotFoundAccountingExportConfigurationFailure
  | _ => UnknownServerFailure
  }

type t = {
  shopId: string,
  isaComptaAccountNumber: option<string>,
  fiscalYearOpeningMonth: FiscalYearOpeningMonth.t,
  taxesAccounts: array<TaxAccount.t>,
  maybePaymentsAccounts: option<array<PaymentAccount.t>>,
  breakdownOfConsumerSalesByCashRegisterDailyReport: bool,
  includeCashFromIndividualsInPaymentJournal: bool,
}

let decodeRequestError = requestError =>
  Future.value(
    switch requestError {
    | Request.UnexpectedServerError
    | ServerError(_)
    | ClientError(_)
    | MalformedResponse
    | JwtAuthenticationRedirection =>
      Error()
    | InvalidRequestFailures(invalidRequestFailures) =>
      let firstFailure =
        invalidRequestFailures->Array.get(0)->Option.map(decodeInvalidRequestFailure)
      switch firstFailure {
      | Some(NotFoundAccountingExportConfigurationFailure) => Ok(None)
      | Some(UnknownServerFailure) | None => Error()
      }
    },
  )

let decodeResponse = response =>
  response
  ->Json.decodeDict
  ->Option.flatMap(dict => {
    let isaComptaAccountNumber = dict->Json.decodeDictFieldString("isaComptaAccountNumber")

    let fiscalYearOpeningMonth =
      dict
      ->Js.Dict.get("fiscalYearOpeningMonth")
      ->Option.flatMap(FiscalYearOpeningMonth.decodeFromJson)
    let taxesAccounts =
      dict
      ->Json.decodeDictFieldArray("accountingExportTaxAccounts")
      ->Option.map(value => value->Array.keepMap(TaxAccount.decodeFromJson))
    let shopId = dict->Json.decodeDictFieldString("shopId")
    let breakdownOfConsumerSalesByCashRegisterDailyReport =
      dict->Json.decodeDictFieldBoolean("breakdownOfConsumerSalesByCashRegisterDailyReport")
    let includeCashFromIndividualsInPaymentJournal =
      dict->Json.decodeDictFieldBoolean("includeCashFromIndividualsInPaymentJournal")
    let maybePaymentsAccounts =
      dict
      ->Json.decodeDictFieldArray("accountingExportPaymentAccounts")
      ->Option.map(value => value->Array.keepMap(PaymentAccount.decodeFromJson))
    let maybePaymentsAccounts = switch maybePaymentsAccounts {
    | Some([]) | None => None
    | Some(paymentsAccounts) => Some(paymentsAccounts)
    }
    switch (
      shopId,
      fiscalYearOpeningMonth,
      taxesAccounts,
      breakdownOfConsumerSalesByCashRegisterDailyReport,
      includeCashFromIndividualsInPaymentJournal,
    ) {
    | (
        Some(shopId),
        Some(fiscalYearOpeningMonth),
        Some(taxesAccounts),
        Some(breakdownOfConsumerSalesByCashRegisterDailyReport),
        Some(includeCashFromIndividualsInPaymentJournal),
      ) if taxesAccounts->Array.size > 0 =>
      Some({
        shopId,
        isaComptaAccountNumber,
        fiscalYearOpeningMonth,
        taxesAccounts,
        breakdownOfConsumerSalesByCashRegisterDailyReport,
        includeCashFromIndividualsInPaymentJournal,
        maybePaymentsAccounts,
      })
    | _ => None
    }
  })

let request = (~shopId) =>
  Request.make(endpoint(~shopId))
  ->Future.mapOk(decodeResponse)
  ->Future.flatMapError(decodeRequestError)
