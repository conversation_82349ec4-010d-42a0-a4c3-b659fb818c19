open Intl

@react.component
let make = (~valueExcludingTax, ~valueIncludingTax, ~taxIncluded, ~capacityUnit) => {
  let formattedRetailPrice = {
    let value = taxIncluded ? valueIncludingTax : valueExcludingTax
    value->Intl.currencyFormat(~currency=#EUR) ++
      capacityUnit->Option.mapWithDefault("", unit => `/${unit}`)
  }

  <Stack space={taxIncluded ? #xxsmall : #none}>
    <TextStyle> {formattedRetailPrice->React.string} </TextStyle>
    {if taxIncluded {
      let formattedValueExcludingTax =
        valueExcludingTax->Intl.currencyFormat(~currency=#EUR) ++ " " ++ t("HT")
      <TextStyle size=#xxsmall variation=#normal>
        {formattedValueExcludingTax->React.string}
      </TextStyle>
    } else {
      React.null
    }}
  </Stack>
}

let make = React.memo(make)
