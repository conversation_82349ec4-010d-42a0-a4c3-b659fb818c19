@react.component
let make = (~rateType as kind, ~valueExcludingTax, ~purchasePrice, ~taxRate) => {
  let formattedRate = switch PriceCalculator.Retail.Rate.apply(
    valueExcludingTax,
    ~kind,
    ~purchasePrice,
    ~taxRate,
    ~charges=0.,
  ) {
  | Some(computedRate) =>
    computedRate->Js.Float.toFixedWithPrecision(~digits=2)->Js.String2.replace(".", ",") ++ " %"
  | _ => "—"
  }
  <TextStyle> {formattedRate->React.string} </TextStyle>
}

let make = React.memo(make)
