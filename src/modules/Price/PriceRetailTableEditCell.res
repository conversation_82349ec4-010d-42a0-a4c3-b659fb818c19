open Intl

@react.component
let make = (
  ~valueExcludingTax,
  ~valueIncludingTax,
  ~taxIncluded,
  ~taxRate,
  ~purchasePrice,
  ~bulkUnit,
  ~autoFocused=false,
  ~shrinkInput=false,
  ~onChange,
  ~onFocus=?,
  ~onBlur=?,
  ~onError=?,
) => {
  let (errored, setErrored) = React.useState(() => false)

  React.useEffect1(() => {
    setErrored(_ => valueExcludingTax < purchasePrice)
    None
  }, [valueExcludingTax])

  React.useEffect1(() => {
    switch (errored, onError) {
    | (true, Some(onError)) => onError()
    | _ => ()
    }
    None
  }, [errored])

  let value = taxIncluded ? valueIncludingTax : valueExcludingTax

  let onChange = React.useCallback1(value => {
    let valueExcludingTax = taxIncluded
      ? value->PriceCalculator.Retail.Tax.removeBack(~taxRate)
      : value
    onChange(valueExcludingTax)
  }, [taxRate])

  <Stack space={taxIncluded ? #xsmall : #none}>
    <InputNumberField
      appender={Custom(
        #EUR->Intl.toCurrencySymbol ++ bulkUnit->Option.mapWithDefault("", unit => ` / ${unit}`),
      )}
      errorMessage=?{errored ? Some("") : None}
      minValue=0.
      autoFocused
      shrinkInput
      value
      onChange
      ?onFocus
      ?onBlur
    />
    {if taxIncluded {
      let formattedValueExcludingTax =
        valueExcludingTax->Intl.currencyFormat(~currency=#EUR) ++ " " ++ t("HT")
      <TextStyle size=#xxsmall variation=#normal>
        {formattedValueExcludingTax->React.string}
      </TextStyle>
    } else {
      React.null
    }}
  </Stack>
}

let make = React.memo(make)
