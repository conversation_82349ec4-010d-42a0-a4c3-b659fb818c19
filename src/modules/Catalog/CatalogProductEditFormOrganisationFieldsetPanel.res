open Intl

exception OrganisationProduct_ShopIdNotFound

@react.component
let make = () => {
  let dispatch = CatalogProductEditForm.useFormDispatch()
  let (formattedValue, setFormattedValue) = React.useState(() => "")
  let activeShop = Auth.useActiveShop()

  let shopId = switch activeShop {
  | Some({id}) => id
  | _ => raise(OrganisationProduct_ShopIdNotFound)
  }

  let onChange = (category: CatalogCategoryPicker.categoryChange) =>
    switch category {
    | CommonCategory({id, formattedName}) =>
      let uuid = switch id {
      | "" => None
      | _ => id->Uuid.fromString
      }
      FieldValueChanged(CategoryId, _ => uuid)->dispatch
      setFormattedValue(_ => formattedName)
    | NotClassifiedCategory =>
      FieldValueChanged(CategoryId, _ => None)->dispatch
      setFormattedValue(_ => t("Not classified"))
    }

  <FieldsetLayoutPanel
    title={t("Organisation")}
    description={t("Enter in which categorie this product should belong to.")}>
    <Box spaceLeft=#normal>
      <CatalogCategoryPicker
        triggerType=#input placeholder={t("Add a category")} value=formattedValue shopId onChange
      />
    </Box>
  </FieldsetLayoutPanel>
}

let make = React.memo(make)
