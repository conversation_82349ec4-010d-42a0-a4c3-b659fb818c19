open Intl
open StyleX

@react.component
let make = (~supplierId, ~supplierName, ~variantId, ~shopId) => {
  let (focused, setFocused) = React.useState(() => false)
  let (ref, hovered) = Hover.use()

  // Closes the edition when value has mutated
  ReactUpdateEffect.use1(() => {
    setFocused(_ => false)
    None
  }, [supplierId])

  <Inline>
    <Box ref spaceRight=#xxlarge>
      <DivX style={StyleX.props([style(~marginLeft="-13px", ())])}>
        {switch focused {
        | true =>
          <CatalogSupplierTableEditCell
            supplierId supplierName variantId shopId onDismiss={_ => setFocused(_ => false)}
          />
        | _ =>
          <ButtonPhased ref hovered onPress={_ => setFocused(_ => true)}>
            {switch supplierName {
            | "" => <TextStyle variation=#subdued> {t("To be filled")->React.string} </TextStyle>
            | _ => <TextStyle> {supplierName->React.string} </TextStyle>
            }}
          </ButtonPhased>
        }}
      </DivX>
    </Box>
  </Inline>
}

let make = React.memo(make)
