open Intl
open CatalogProduct

@react.component
let make = (~cku, ~id, ~status: option<Status.t>=?) => {
  let scope = Auth.useScope()
  let {pathname} = Navigation.useUrl()
  let (archiveModalOpened, setArchiveModalOpened) = React.useState(_ => false)
  let unarchiveVariant = CatalogVariantMutations.useUnarchive(~id)

  let fromCatalogPage = pathname === Catalog->LegacyRouter.routeToPathname

  <>
    <Inline align=#end>
      <Menu variation=#more_square buttonBordered=false>
        <MenuItem
          content={Text(t("See"))}
          action=OpenLink(Route(Catalog->LegacyRouter.routeToPathname ++ `/${cku}`))
        />
        <MenuItem
          content={Text(t("Edit"))}
          action=OpenLink(Route(Catalog->LegacyRouter.routeToPathname ++ `/${cku}/edit`))
        />
        {if !(pathname->Js.String2.includes("/product")) {
          <MenuItem
            content={Text(t("See bound product"))}
            action=OpenLink(
              Route(Catalog->LegacyRouter.routeToPathname ++ `/product/redirect/${cku}`),
            )
          />
        } else {
          React.null
        }}
        {if pathname->Js.String2.endsWith("/catalog") {
          <MenuItem
            content={Text(t("Create variant"))}
            action=OpenLink(
              Route(Catalog->LegacyRouter.routeToPathname ++ `/create/redirect/${cku}`),
            )
          />
        } else {
          React.null
        }}
        {switch scope {
        | Organisation(_) =>
          <MenuItem
            content={Text(t("Duplicate"))}
            action=OpenLink(Route(Catalog->LegacyRouter.routeToPathname ++ `/duplication/${cku}`))
          />
        | _ => React.null
        }}
        {switch (fromCatalogPage, scope, status) {
        | (true, Organisation({activeShop: None}), _) => React.null
        | (_, _, Some(Archived)) =>
          <MenuItem
            content={Text(t("Unarchive"))} action=Callback(() => unarchiveVariant()->ignore)
          />
        | _ =>
          <MenuItem
            content={Text(t("Archive"))} action=Callback(() => setArchiveModalOpened(_ => true))
          />
        }}
      </Menu>
    </Inline>
    <CatalogVariantArchiveModal
      id opened=archiveModalOpened onRequestClose={() => setArchiveModalOpened(_ => false)}
    />
  </>
}

let make = React.memo(make)
