open Intl

type shopNotes = {
  variantId: string,
  shopId: string,
  internalNote: string,
  tastingNote: string,
}

module UpdateVariantTastingNoteMutation = %graphql(`
  mutation updateVariant_tastingNote($id: ID!, $input: InputUpdateVariant!) {
    updateVariant(id: $id, input:	$input) {
      id
      tastingNote
    }
  }
`)

module UpdateVariantInternalNoteMutation = %graphql(`
  mutation updateVariant_internalNote($id: ID!, $input: InputUpdateVariant!) {
    updateVariant(id: $id, input:	$input) {
      id
      internalNote
    }
  }
`)

type error =
  | TastingNoteUpdatingError(string)
  | InternalNoteUpdatingError(string)
  | NoError

@react.component
let make = (~shopsNotes: array<shopNotes>) => {
  let activeShop = Auth.useActiveShop()

  let (variantId, initialTastingNote, initialInternalNote) =
    shopsNotes
    ->Array.getBy(productNotes => Some(productNotes.shopId) == activeShop->Option.map(({id}) => id))
    ->Option.mapWithDefault((Some(""), "", ""), notes => (
      Some(notes.variantId),
      notes.tastingNote,
      notes.internalNote,
    ))
  let (tastingNote, setTastingNote) = React.useState(() => initialTastingNote)
  let debouncedTastingNote = ReactUpdateDebounced.use(
    tastingNote,
    ~delay=ApolloConfig.mutationDebounceInterval,
  )
  let (internalNote, setInternalNote) = React.useState(() => initialInternalNote)
  let debouncedInternalNote = ReactUpdateDebounced.use(
    internalNote,
    ~delay=ApolloConfig.mutationDebounceInterval,
  )

  let (mutateTastingNote, _) = UpdateVariantTastingNoteMutation.use()
  let (mutateInternalNote, _) = UpdateVariantInternalNoteMutation.use()
  let (error, setError) = React.useState(() => NoError)

  let onMutateTastingNoteRequested = React.useCallback1(tastingNote => {
    let input = UpdateVariantTastingNoteMutation.makeInputObjectInputUpdateVariant(
      ~tastingNote=tastingNote->Scalar.Text.serialize,
      (),
    )

    mutateTastingNote(
      UpdateVariantTastingNoteMutation.makeVariables(
        ~id=variantId->Option.getWithDefault(""),
        ~input,
        (),
      ),
    )
    ->FuturePromise.fromPromise
    ->Future.get(response =>
      switch response {
      | Ok(Ok({error: None})) => setError(_ => NoError)
      | _ =>
        setTastingNote(_ => initialTastingNote)
        setError(_ => TastingNoteUpdatingError(t("Couldn't update the tasting note.")))
      }
    )
  }, [variantId])

  let onMutateInternalNoteRequested = React.useCallback1(internalNote => {
    let input = UpdateVariantInternalNoteMutation.makeInputObjectInputUpdateVariant(
      ~internalNote=internalNote->Scalar.Text.serialize,
      (),
    )

    mutateInternalNote(
      UpdateVariantInternalNoteMutation.makeVariables(
        ~id=variantId->Option.getWithDefault(""),
        ~input,
        (),
      ),
    )
    ->FuturePromise.fromPromise
    ->Future.get(response =>
      switch response {
      | Ok(Ok({error: None})) => setError(_ => NoError)
      | _ =>
        setInternalNote(_ => initialInternalNote)
        setError(_ => InternalNoteUpdatingError(t("Couldn't update the internal note.")))
      }
    )
  }, [variantId])

  ReactUpdateEffect.use1(() => {
    onMutateTastingNoteRequested(debouncedTastingNote)
    None
  }, [debouncedTastingNote])

  ReactUpdateEffect.use1(() => {
    onMutateInternalNoteRequested(debouncedInternalNote)
    None
  }, [debouncedInternalNote])

  <Card title="Notes">
    {switch Auth.useScope() {
    | Organisation({activeShop: Some(_)})
    | Single(_) =>
      <Stack space=#xlarge>
        <InputTextAreaField
          label={t("Tasting note")}
          errorMessage=?{switch error {
          | TastingNoteUpdatingError(errorMessage) => Some(errorMessage)
          | _ => None
          }}
          value=tastingNote
          onChange={value => setTastingNote(_ => value)}
        />
        <InputTextAreaField
          label={t("Internal memo")}
          errorMessage=?{switch error {
          | InternalNoteUpdatingError(errorMessage) => Some(errorMessage)
          | _ => None
          }}
          value=internalNote
          onChange={value => setInternalNote(_ => value)}
        />
      </Stack>
    | _ =>
      <Box spaceTop=#medium>
        <Banner
          compact=false
          textStatus=Info(
            t(
              "Filter the product page on a single shop to visualize and update its tasting note or internal memo.",
            ),
          )
        />
      </Box>
    }}
  </Card>
}

let make = React.memo(make)
