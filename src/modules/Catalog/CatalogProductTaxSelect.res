open Intl

module Query = %graphql(`
  query taxes($filterBy: InputTaxQueryFilter) {
    taxes(first: 50, filterBy: $filterBy) {
      edges {
        node {
          id
          value
          shop { id }
        }
      }
    }
  }
`)

type data = Query.Query_inner.t_taxes_edges_node

type tax = {
  id: Uuid.t,
  value: float,
}

@react.component
let make = (~value, ~defaultTaxValue, ~onChange) => {
  let activeShop = Auth.useActiveShop()
  let queryResults = Query.use(
    Query.makeVariables(
      ~filterBy=Query.makeInputObjectInputTaxQueryFilter(
        ~shopIds=Query.makeInputObjectInFilter(
          ~_in=[activeShop->Option.mapWithDefault("", ({id}) => id)],
          (),
        ),
        (),
      ),
      (),
    ),
    ~fetchPolicy=NetworkOnly, // NOTE - required in case of history.back
  )

  let options = switch queryResults {
  | {data: Some({taxes})} =>
    taxes.edges
    ->Array.keepMap(({node: tax}) =>
      switch tax.id->Uuid.fromString {
      | Some(uuid) => Some({id: uuid, value: tax.value})
      | _ => None
      }
    )
    ->SortArray.stableSortBy((current, next) => current.value > next.value ? 1 : 0)
  | _ => []
  }

  React.useEffect1(() => {
    if value->Option.isNone {
      let defaultTaxValue =
        defaultTaxValue->Option.getWithDefault(
          options->Array.map(tax => tax.value)->Js.Math.maxMany_float,
        )
      let taxFromQueryOptions = options->Array.getBy(tax => tax.value === defaultTaxValue)

      switch (taxFromQueryOptions, options[0]) {
      | (Some(tax), _) | (_, Some(tax)) => onChange(tax)
      | _ => ()
      }
    }
    None
  }, [queryResults.data])

  let onChange = React.useCallback0(tax => tax->Option.forEach(onChange))

  let items = options->Array.map(tax => {
    Select.key: tax.id->Uuid.toString,
    label: tax.value
    ->Js.Float.toFixedWithPrecision(~digits=2)
    ->Js.String2.replace(".", ",") ++ " %",
    value: Some(tax),
  })
  let sections = [{Select.items: items}]

  <Select
    preset=#inputField({required: true})
    label={t("VAT")}
    disabled={value->Option.isNone}
    placeholder={t("Loading...")}
    sections
    value
    onChange
  />
}

let make = React.memo(make)
