open Intl

type parentCategory = {
  id: Js.Nullable.t<string>,
  name: string,
}

module Query = %graphql(`
  query ParentCategories($filterBy: InputParentCategoriesQueryFilter) {
    parentCategories(filterBy: $filterBy) {
      id
      formattedName
    }
  }
`)

let useCategoriesQueryFn = variables =>
  Query.use(variables)->ApolloHelpers.queryResultToAsyncResult->AsyncResult.mapError(_ => ())

@react.component
let make = (~shopId, ~value, ~useCategoriesQuery=useCategoriesQueryFn, ~onChange) => {
  let queryResult = useCategoriesQuery({
    filterBy: Some({
      shopIds: switch shopId {
      | Some(shopId) => Some(Query.makeInputObjectInFilter(~_in=[shopId], ()))
      | None => None
      },
      archived: Some(#EXCLUDED),
    }),
  })

  let sections = {
    let items = switch queryResult {
    | Done(Ok({parentCategories})) | Reloading(Ok({parentCategories})) =>
      parentCategories->Array.map(parentCategory => {
        Select.label: parentCategory.formattedName,
        key: parentCategory.Query.id,
        value: Some({
          id: parentCategory.id->Js.Nullable.return,
          name: parentCategory.formattedName,
        }),
      })
    | _ => []
    }
    let defaultItem = {
      Select.key: "default",
      label: template(t("All{{feminine}}"), ()),
      value: None,
      sticky: true,
    }
    let unclassifiedItem = {
      Select.key: "not-classified",
      label: template(t("Not classified"), ()),
      value: Some({
        id: Js.Nullable.null,
        name: t("Not classified"),
      }),
      sticky: true,
    }
    [{Select.items: [defaultItem, unclassifiedItem]}, {title: t("Categories"), items}]
  }
  let disabled = shopId->Option.isNone || queryResult->AsyncResult.isBusy

  <Tooltip
    content={<Tooltip.Span text={t("Please select a shop beforehand.")} />}
    delay=0
    placement=#top
    disabled={!disabled}>
    <Select label={t("Category")} preset=#filter size=#compact disabled sections value onChange />
  </Tooltip>
}
