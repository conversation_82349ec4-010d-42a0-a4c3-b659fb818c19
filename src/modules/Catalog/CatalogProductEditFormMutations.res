open Intl
open CatalogProductEditForm

exception EmptyProductName
exception ShopIdNotFound
exception TaxIdNotFound
exception FormIdNotFound

module CreateProductMutation = %graphql(`
  mutation createProduct($input: InputCreateProduct!) {
    createProduct(input: $input) {
      id
    }
  }
`)

module UpdateProductMutation = %graphql(`
  mutation updateProduct($id: ID!, $input: InputUpdateProduct!) {
    updateProduct(id: $id, input: $input) {
      id
      updatedAt
      name
      formattedDescription
      formattedOrigin
      category {
        id
        formattedName
      }
      tax {
        value
      }
    }
  }
`)

let optionFromString = str =>
  switch str {
  | "" => None
  | _ => Some(str)
  }

let makeCreateProductInput = (~state: Lenses.state, ~shop: option<Auth.shop>) =>
  CreateProductMutation.makeInputObjectInputCreateProduct(
    ~name=switch state.name {
    | "" => raise(EmptyProductName)
    | name => name
    },
    ~kind=state.kind,
    ~color=?state.color,
    ~producer=state.producer->optionFromString->Option.getWithDefault("—"),
    ~family=?state.family->optionFromString,
    ~designation=?state.designation->optionFromString,
    ~country=state.country,
    ~region=?state.region->optionFromString,
    ~beerType=?state.beerType->optionFromString,
    ~wineType=?state.wineType,
    ~whiteWineType=?state.whiteWineType,
    ~categoryId=?state.categoryId->Option.map(Uuid.toString),
    ~taxId=switch state.tax->Option.map(tax => tax.id->Uuid.toString) {
    | Some(id) => id
    | _ => raise(TaxIdNotFound)
    },
    ~shopId=switch shop {
    | Some({id}) => id
    | _ => raise(ShopIdNotFound)
    },
    (),
  )

let useCreateProduct = () => {
  let (mutate, _) = CreateProductMutation.use()
  let activeShop = Auth.useActiveShop()

  React.useCallback1((_, state: Lenses.state) => {
    let input = makeCreateProductInput(~state, ~shop=activeShop)

    mutate(CreateProductMutation.makeVariables(~input, ()))
    ->ApolloHelpers.mutationPromiseToFutureResult
    ->Future.mapOk(({CreateProductMutation.createProduct: {id}}) => id)
  }, [activeShop])
}

let makeUpdateProductInput = (~state: Lenses.state) =>
  UpdateProductMutation.makeInputObjectInputUpdateProduct(
    ~name=switch state.name {
    | "" => raise(EmptyProductName)
    | name => name
    },
    ~kind=state.kind,
    ~color=?state.color,
    ~producer=?state.producer->optionFromString,
    ~family=?state.family->optionFromString,
    ~designation=?state.designation->optionFromString,
    ~country=state.country,
    ~region=?state.region->optionFromString,
    ~beerType=?state.beerType->optionFromString,
    ~wineType=?state.wineType,
    ~whiteWineType=?state.whiteWineType,
    ~taxId=switch state.tax->Option.map(tax => tax.id->Uuid.toString) {
    | Some(id) => id
    | _ => raise(TaxIdNotFound)
    },
    (),
  )

let useUpdateProduct = () => {
  let (mutate, _) = UpdateProductMutation.use()

  React.useCallback0((productId, state: Lenses.state) => {
    let id = switch productId {
    | Some(id) if id->Uuid.fromString !== None => id
    | _ => raise(FormIdNotFound)
    }
    let input = makeUpdateProductInput(~state)

    mutate(UpdateProductMutation.makeVariables(~id, ~input, ()))
    ->ApolloHelpers.mutationPromiseToFutureResult
    ->Future.mapOk(_ => Some(t("The product information have been correctly updated.")))
  })
}
