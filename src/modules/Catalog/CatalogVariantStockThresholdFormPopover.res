@react.component
let make = (
  ~popover,
  ~popoverAriaProps,
  ~popoverTriggerRef,
  ~variantId,
  ~maxStockThreshold,
  ~minStockThreshold,
  ~stockOrderTriggerThreshold,
) => {
  <CatalogVariantStockThresholdFormContainer
    variantId minStockThreshold maxStockThreshold stockOrderTriggerThreshold>
    <Popover modal=false placement=#"bottom end" state=popover triggerRef=popoverTriggerRef>
      <Popover.Dialog ariaProps=popoverAriaProps.ReactAria.Overlay.Trigger.overlayProps>
        <Box spaceX=#large spaceY=#xlarge>
          <CatalogVariantStockThresholdFormInputs />
        </Box>
      </Popover.Dialog>
    </Popover>
  </CatalogVariantStockThresholdFormContainer>
}

let make = React.memo(make)
