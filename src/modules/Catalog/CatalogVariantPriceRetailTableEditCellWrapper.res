open StyleX
open CatalogVariantRetailPrice
open CatalogVariantRetailPriceReducer

@react.component
let make = (~disabled=false, ~variantPrice: variantPrice, ~onRequestDispatch as dispatch) => {
  let (focused, setFocused) = React.useState(_ => false)
  let onFocus = () => setFocused(_ => true)
  let onBlur = () => setFocused(_ => false)

  let (ref, hovered) = Hover.use()

  let onChange = React.useCallback2(price =>
    RetailPriceUpdated(
      {
        CatalogVariantRetailPriceReducer.id: variantPrice.id,
        variantId: variantPrice.variantId,
        priceId: variantPrice.priceId,
      },
      price,
    )->dispatch
  , (variantPrice.variantId, variantPrice.id))

  <Box ref>
    <DivX style={StyleX.props([style(~marginLeft="-13px", ())])}>
      {if focused {
        <PriceRetailTableEditCell
          valueExcludingTax=variantPrice.valueExcludingTax
          valueIncludingTax=variantPrice.valueIncludingTax
          taxIncluded=variantPrice.taxIncluded
          taxRate=variantPrice.taxRate
          purchasePrice=variantPrice.purchasePrice
          bulkUnit=variantPrice.capacityUnit
          autoFocused=true
          shrinkInput=true
          onChange
          onFocus
          onBlur
        />
      } else {
        <ButtonPhased hovered disabled onPress={_ => onFocus()}>
          <PriceRetailTableCell
            valueExcludingTax=variantPrice.valueExcludingTax
            valueIncludingTax=variantPrice.valueIncludingTax
            taxIncluded=variantPrice.taxIncluded
            capacityUnit=variantPrice.capacityUnit
          />
        </ButtonPhased>
      }}
    </DivX>
  </Box>
}

let make = React.memo(make)
