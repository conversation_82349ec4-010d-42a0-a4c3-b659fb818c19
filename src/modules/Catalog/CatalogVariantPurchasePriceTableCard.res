open Intl

module Config = CatalogVariant__Config
module Row = {
  type t = {
    variantId: string,
    shopId: string,
    shopName: string,
    supplierId: string,
    supplierName: string,
    purchasePrice: float,
    formattedPurchasePrice: option<string>,
    minRetailPrice: float,
    bulkUnit: option<string>,
  }
  let keyExtractor = row => row.variantId ++ "-purchase"
}

type variantPrices = {
  purchasePriceData: Config.variantPurchasePrice,
  retailPricesData: array<Config.variantRetailPrice>,
}

let tableColumns = (~allShopsFiltered) => [
  {
    Table.key: "purchasedprice-shopname",
    name: t("Shop/Warehouse"),
    layout: {
      minWidth: 200.->#px,
      width: 1.75->#fr,
      hidden: !allShopsFiltered,
    },
    render: ({data: {Row.shopName: shopName}}) => <TextStyle> {shopName->React.string} </TextStyle>,
  },
  {
    key: "supplier-id",
    name: t("Supplier"),
    layout: {minWidth: 230.->#px, width: 2.->#fr},
    render: ({data: {supplierId, supplierName, variantId, shopId}}) =>
      <Box spaceRight=#xxlarge>
        <CatalogSupplierTableEditCellWrapper supplierId supplierName variantId shopId />
      </Box>,
  },
  {
    key: "purchasedprice-price",
    name: t("Purchase price") ++ " " ++ t("HT"),
    layout: {minWidth: 100.->#px},
    render: ({
      data: {purchasePrice, formattedPurchasePrice, minRetailPrice, bulkUnit, variantId},
    }) =>
      <PricePurchaseTableEditCellWrapper
        value=purchasePrice formattedPurchasePrice minRetailPrice bulkUnit variantId
      />,
  },
]

@react.component
let make = (~shopsVariantPrices: array<variantPrices>) => {
  let allShopsFiltered = switch Auth.useScope() {
  | Organisation({activeShop: None}) => true
  | _ => false
  }
  let columns = React.useMemo1(() => tableColumns(~allShopsFiltered), [allShopsFiltered])

  let shopsVariantRetailPriceMinValue = React.useMemo1(() =>
    shopsVariantPrices->Array.map(({retailPricesData}) => {
      let priceValues = retailPricesData->Array.map(data => data.valueExcludingTax)
      let minPriceValue = Js.Math.minMany_float(priceValues)
      Js.Float.isFinite(minPriceValue) ? Some(minPriceValue) : None
    })
  , [shopsVariantPrices])

  let rows = shopsVariantPrices->Array.mapWithIndex((index, {purchasePriceData}) => {
    Row.variantId: purchasePriceData.id,
    shopId: purchasePriceData.shopId,
    shopName: purchasePriceData.shopName,
    supplierId: purchasePriceData.supplierId,
    supplierName: purchasePriceData.supplierName,
    purchasePrice: purchasePriceData.purchasePrice->Option.getWithDefault(0.),
    formattedPurchasePrice: purchasePriceData.formattedPurchasedPrice,
    minRetailPrice: switch shopsVariantRetailPriceMinValue->Array.get(index) {
    | Some(Some(minRetailPrice)) => minRetailPrice
    | _ => 0.
    },
    bulkUnit: purchasePriceData.capacityUnit,
  })

  let placeholderEmptyState =
    <Placeholder status=NoDataAvailable customText={t("No purchase price has been yet recorded")} />
  let data = AsyncData.Done(Ok(rows))

  <Card variation=#table title={t("Purchasing price")}>
    <TableView columns data keyExtractor=Row.keyExtractor maxHeight=325. placeholderEmptyState />
  </Card>
}

let make = React.memoCustomCompareProps(make, (oldProps, newProps) =>
  oldProps.shopsVariantPrices === newProps.shopsVariantPrices
)
