open Intl

let defaultBulkCapacityPrecision = 3

module Name = {
  let placeholderFromProductKind = kind =>
    switch kind {
    | #WINE => "0,75L - 2017"
    | #SPIRITUOUS => "0,7L - 10 " ++ t("years")
    | #BEER => "0,33L"
    | #SIMPLE => "0,5kg"
    }

  let make = (~capacityValue, ~capacityUnit, ~bulk, ~year) =>
    switch capacityUnit {
    | Some(unit) =>
      switch (bulk, capacityValue) {
      | (true, _) => template(t("Bulk by {{unit}}"), ~values={"unit": unit}, ())
      | (false, Some(capacityValue)) => capacityValue->Float.toString ++ unit
      | _ => ""
      } ++
      year->Option.mapWithDefault("", year => {
        let separator = Option.isSome(capacityValue) || bulk ? " - " : ""
        switch year->Int.toString {
        | absoluteYear if year > 999 => separator ++ absoluteYear
        | relativeYear if year > 1 => separator ++ (relativeYear ++ (" " ++ t("years")))
        | relativeYear => separator ++ (relativeYear ++ (" " ++ t("year")))
        }
      })
    | None if !bulk => t("Piece")
    | _ => ""
    }->Js.String2.replace(".", ",")
}

module CapacityUnit = {
  // NOTE - It is not possible to define a precise list
  // of units because in v1 it is possible to declare new ones
  // type t = [#CL | #KG | #ML | #L | #G]

  let kg = "kg"
  let g = "g"
  let l = "L"
  let cl = "cL"
  let ml = "mL"

  let getDefaultFromProductKind = productKind =>
    switch productKind {
    | #SIMPLE => Some(kg)
    | _ => Some(l)
    }

  let makeOptions = (~productKind) =>
    switch productKind {
    | #SIMPLE => [kg, g, l, cl, ml]
    | #WINE | #SPIRITUOUS => [l, cl]
    | #BEER => [l, cl, ml]
    }
}

module CapacityValue = {
  let placeholderFromProductKind = kind =>
    switch kind {
    | #WINE => "0,75"
    | #SPIRITUOUS => "0,70"
    | #BEER => "0,33"
    | #SIMPLE => "0,50"
    }
}

module Year = {
  let placeholderFromProductKind = kind =>
    switch kind {
    | #WINE => "2017"
    | #SPIRITUOUS => "10"
    | #BEER => ""
    | #SIMPLE => ""
    }
}

module Volume = {
  let placeholderFromProductKind = kind =>
    switch kind {
    | #WINE => "13,10"
    | #SPIRITUOUS => "42,00"
    | #BEER => "4,50"
    | #SIMPLE => ""
    }
}

module MultiShops = {
  // Remove all duplicated nodes from predicate compare
  // @types: (array('a), ('a, 'a) => bool) => array('a)
  // TODO - this should be a private function
  let arrayKeepUniqueBy = (collection, predicate) =>
    collection->Array.keepWithIndex((current, index) =>
      collection->Array.getIndexBy(element => predicate(element, current)) === Some(index)
    )

  let isReferenceDiffering = (shopsReference, ~scope: Auth.scope, ~predicate) =>
    switch (scope, shopsReference->arrayKeepUniqueBy(predicate)) {
    | (Organisation({activeShop: None}), merged) => merged->Array.length > 1
    | _ => false
    }
}

module StockQuantity = {
  let format = (rawValue: int, ~capacityPrecision: option<int>, ~capacityUnit: option<string>) =>
    switch capacityPrecision {
    | Some(precision) =>
      rawValue->Int.toFloat /. Js.Math.pow_float(~base=10., ~exp=precision->Float.fromInt)
    | _ => rawValue->Int.toFloat
    }
    ->Float.toString
    ->Js.String2.replace(".", ",") ++
      switch capacityUnit {
      | Some(capacityUnit) => capacityUnit
      | None => ""
      }
}
