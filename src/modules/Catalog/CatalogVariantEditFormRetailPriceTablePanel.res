open Intl

module Query = %graphql(`
  query prices($filterBy: InputPricesQueryFilter) {
    prices(first: 50, filterBy: $filterBy) {
      edges {
        node {
          id
          name
          taxIncluded
          shop {
            id
          }
        }
      }
    }
  }
`)

module Row = {
  type t = {
    id: string,
    name: string,
    valueExcludingTax: float,
    valueIncludingTax: float,
    taxIncluded: bool,
    taxRate: float,
  }
  let keyExtractor = row => row.id ++ "-retail"
}

let tableColumns = (~purchasePrice, ~bulkUnit, ~onRequestPriceUpdate) => [
  {
    Table.key: "retailprice-list",
    name: t("Price list"),
    layout: {minWidth: 220.->#px, width: 1.5->#fr},
    render: ({data: {Row.name: name, taxIncluded}}) => {
      let taxTypename = t(taxIncluded ? "VAT incl." : "VAT excl.")

      <Inline space=#small>
        <TextStyle weight=#semibold> {name->React.string} </TextStyle>
        <Badge variation=#neutral size=#small> {taxTypename->React.string} </Badge>
      </Inline>
    },
  },
  {
    key: "retailprice-prices",
    name: t("Retail prices"),
    layout: {minWidth: 160.->#px, margin: #large},
    render: ({data: {id, valueExcludingTax, valueIncludingTax, taxIncluded, taxRate}}) =>
      <PriceRetailTableEditCell
        valueExcludingTax
        valueIncludingTax
        taxIncluded
        taxRate
        purchasePrice
        bulkUnit
        onChange={valueExcludingTax => onRequestPriceUpdate(id, valueExcludingTax)}
      />,
  },
  {
    key: "retailprice-marginrate",
    name: t("Margin rt"),
    layout: {minWidth: 110.->#px},
    render: ({data: {valueExcludingTax, taxRate}}) =>
      <PriceRateTableCell
        rateType=PriceCalculator.Retail.Rate.MarginRate valueExcludingTax purchasePrice taxRate
      />,
  },
  {
    key: "retailprice-markuprate",
    name: t("Markup rt"),
    layout: {minWidth: 110.->#px},
    render: ({data: {valueExcludingTax, taxRate}}) =>
      <PriceRateTableCell
        rateType=PriceCalculator.Retail.Rate.MarkupRate valueExcludingTax purchasePrice taxRate
      />,
  },
]

@react.component
let make = (~taxRate: float) => {
  let {purchasePrice, capacityUnit, bulk} = CatalogVariantEditForm.useFormState().values
  let dispatch = CatalogVariantEditForm.useFormDispatch()
  let activeShop = Auth.useActiveShop()
  let queryResults = Query.use(
    Query.makeVariables(
      ~filterBy=Query.makeInputObjectInputPricesQueryFilter(
        ~shopIds=Query.makeInputObjectInFilter(
          ~_in=[activeShop->Option.mapWithDefault("", ({id}) => id)],
          (),
        ),
        (),
      ),
      (),
    ),
  )
  let (prices, setPrices) = React.useState(() => [])

  // Initializes price list from query when the purchase price is set
  React.useEffect2(() => {
    switch (prices, queryResults) {
    | ([], {data: Some({prices})}) =>
      setPrices(_ =>
        prices.edges->Array.map(
          ({node: price}) => {
            Row.id: price.id,
            name: price.name,
            valueExcludingTax: 0.,
            valueIncludingTax: 0.,
            taxIncluded: price.taxIncluded,
            taxRate,
          },
        )
      )
    | _ => ()
    }
    None
  }, (queryResults, purchasePrice))

  // REVIEW - maybe undefined prices values should be saved/created too
  // Sets up in form state variant prices to create
  ReactUpdateEffect.use1(() => {
    FieldValueChanged(
      VariantPrices,
      _ => Some(
        prices->Array.keepMap(({id, valueExcludingTax, valueIncludingTax}) =>
          valueExcludingTax > 0.
            ? Some({
                CatalogVariantEditForm.priceId: id,
                valueExcludingTax,
                valueIncludingTax,
              })
            : None
        ),
      ),
    )->dispatch
    None
  }, [prices])

  // Updates edited price from its ID and primary value
  let onRequestPriceUpdate = React.useCallback0((priceId, nextValueExcludingTax) =>
    setPrices(prices =>
      prices->Array.map(
        priceData =>
          if priceData.Row.id === priceId {
            {
              ...priceData,
              valueExcludingTax: nextValueExcludingTax,
              valueIncludingTax: nextValueExcludingTax->PriceCalculator.Retail.Tax.add(~taxRate),
            }
          } else {
            priceData
          },
      )
    )
  )

  let columns = React.useMemo3(() =>
    tableColumns(
      ~purchasePrice,
      ~bulkUnit=switch (capacityUnit, bulk) {
      | (Some(unit), true) => Some(unit)
      | _ => None
      },
      ~onRequestPriceUpdate,
    )
  , (purchasePrice, capacityUnit, bulk))

  <TableLayoutPanel
    title={t("Retail price")}
    description={t("Enter the retail prices for each of your price lists.")}>
    {switch queryResults {
    | {data: Some(_)} =>
      <TableView columns data=AsyncData.Done(Ok(prices)) keyExtractor=Row.keyExtractor />
    | {loading: true} => <Placeholder status=Loading />
    | _ => <Placeholder status=Error />
    }}
  </TableLayoutPanel>
}

let make = React.memo(make)
