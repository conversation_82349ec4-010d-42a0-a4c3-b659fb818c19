open Intl

module UpdateProductInternalNoteMutation = %graphql(`
  mutation updateProduct_internalNote($id: ID!, $input: InputUpdateProduct!) {
    updateProduct(id: $id, input:	$input) {
      id
      internalNote
    }
  }
`)

type error =
  | InternalNoteUpdatingError(string)
  | None

// TODO - add mutation & multishop mode
@react.component
let make = (~productId, ~internalNote as initialInternalNote) => {
  let (internalNote, setInternalNote) = React.useState(() => initialInternalNote)
  let debouncedInternalNote = ReactUpdateDebounced.use(
    internalNote,
    ~delay=ApolloConfig.mutationDebounceInterval,
  )

  let (mutateInternalNote, _) = UpdateProductInternalNoteMutation.use()
  let (error, setError) = React.useState(() => None)

  let onMutateInternalNoteRequested = React.useCallback1(internalNote => {
    let input = UpdateProductInternalNoteMutation.makeInputObjectInputUpdateProduct(
      ~internalNote=internalNote->Scalar.Text.serialize,
      ~country=t("France"),
      (),
    )
    mutateInternalNote(
      UpdateProductInternalNoteMutation.makeVariables(
        ~id=productId->Option.getWithDefault(""),
        ~input,
        (),
      ),
    )
    ->FuturePromise.fromPromise
    ->Future.get(result =>
      switch result {
      | Ok(Ok({error: None})) => setError(_ => None)
      | _ =>
        setInternalNote(_ => initialInternalNote)
        setError(_ => InternalNoteUpdatingError(t("Couldn't update the internal note.")))
      }
    )
  }, [productId])

  ReactUpdateEffect.use1(() => {
    onMutateInternalNoteRequested(debouncedInternalNote)
    None
  }, [debouncedInternalNote])

  <Card title="Note">
    {switch Auth.useScope() {
    | Organisation({activeShop: Some(_)})
    | Single(_) =>
      <Stack space=#xlarge>
        <InputTextAreaField
          label={t("Internal memo")}
          errorMessage=?{switch error {
          | InternalNoteUpdatingError(errorMessage) => Some(errorMessage)
          | _ => None
          }}
          value=internalNote
          onChange={value => setInternalNote(_ => value)}
        />
      </Stack>
    | _ =>
      <Box spaceTop=#medium>
        <Banner
          compact=false
          textStatus=Info(
            t("Please filter the product page on a single shop to edit its internal memo."),
          )
        />
      </Box>
    }}
  </Card>
}

let make = React.memo(make)
