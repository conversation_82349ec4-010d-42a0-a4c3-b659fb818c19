open Intl
open CatalogProduct

module Mutations = CatalogProductMutations

@react.component
let make = (~cku, ~id, ~status: option<Status.t>, ~allVariantsArchived: bool) => {
  let scope = Auth.useScope()
  let url = Navigation.useUrl()
  let (archiveModalOpened, setArchiveModalOpened) = React.useState(_ => false)
  let unarchiveProduct = Mutations.useUnarchive(~id)

  <>
    <Inline space=#small>
      <Menu>
        <MenuItem content=Text(t("Edit")) action=OpenLink(Route(url.pathname ++ "/edit")) />
        {switch (allVariantsArchived, status, scope) {
        | (false, Some(Archived), Single(_) | Organisation({activeShop: Some(_)})) =>
          <MenuItem
            content=Text(t("Unarchive")) action=Callback(() => unarchiveProduct()->ignore)
          />
        | (_, Some(Active | Inactive), Single(_) | Organisation({activeShop: Some(_)})) =>
          <MenuItem
            content=Text(t("Archive")) action=Callback(() => setArchiveModalOpened(_ => true))
          />
        | _ => React.null
        }}
        {switch scope {
        | Organisation(_) =>
          <MenuItem
            content=Text(t("Duplicate"))
            action=OpenLink(
              Route(Catalog->LegacyRouter.routeToPathname ++ `/product/duplication/${cku}`),
            )
          />
        | _ => React.null
        }}
      </Menu>
      <ButtonLink to=Route(Catalog->LegacyRouter.routeToPathname ++ `/${cku}/create`)>
        {t("Create new variant")->React.string}
      </ButtonLink>
    </Inline>
    <CatalogProductArchiveModal
      id opened=archiveModalOpened onRequestClose={() => setArchiveModalOpened(_ => false)}
    />
  </>
}

let make = React.memo(make)
