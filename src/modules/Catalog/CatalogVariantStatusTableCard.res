open Intl

module Config = CatalogVariant__Config
module Row = {
  type t = {
    id: string,
    shopName: string,
    status: option<CatalogProduct.Status.t>,
  }
  let keyExtractor = row => row.id ++ "-status"
}

let columns = [
  {
    Table.key: "shopname",
    name: t("Shop/Warehouse"),
    layout: {minWidth: 150.->#px},
    render: ({data: {Row.shopName: shopName}}) => <TextStyle> {shopName->React.string} </TextStyle>,
  },
  {
    key: "status",
    name: t("Status"),
    layout: {minWidth: 110.->#px, width: 0.->#fr},
    render: ({data: {status}}) => <CatalogProductStatusBadge value=status />,
  },
]

@react.component
let make = (~shopsVariant: array<Config.variantInformation>) => {
  let rows = shopsVariant->Array.map(product => {
    Row.id: product.id,
    shopName: product.shopName,
    status: product.status,
  })

  <Card variation=#table title={t("Statuses")}>
    <Box spaceTop=#small>
      <TableView
        columns data=AsyncData.Done(Ok(rows)) keyExtractor=Row.keyExtractor hideCard=true
      />
    </Box>
  </Card>
}

let make = React.memo(make)
