open Intl

module OrderSupplies = CatalogVariantOrderSupplyTableCard__Queries
module Query = OrderSupplies.OrderSuppliesQuery

module Row = {
  type t = {
    id: string,
    shopName: string,
    name: string,
    receptionDate: option<Js.Date.t>,
    quantity: string,
    totalAmountExcludingTaxes: float,
    totalAmountIncludingTaxes: float,
    supplierId: string,
    supplierCompanyName: string,
  }

  let keyExtractor = row => row.id ++ "-supply"
}

let tableColumns = (~allShopsFiltered) => [
  {
    Table.name: t("Number"),
    key: "supply-name",
    layout: {minWidth: 160.->#px, width: 30.->#pct, margin: #normal},
    render: ({data: {Row.id: id, name, shopName}}) => {
      let shopName = allShopsFiltered ? Some(shopName) : None
      <OrderNameTableCell value=name ?shopName id />
    },
  },
  {
    name: t("Supplier"),
    key: "supply-supplier",
    layout: {minWidth: 140.->#px, width: 20.->#pct, margin: #small},
    render: ({data: {supplierId, supplierCompanyName}}) =>
      <TextLink text=supplierCompanyName to=Route(SupplierRoutes.showRoute(~id=supplierId)) />,
  },
  {
    name: t("Reception date"),
    key: "supply-date",
    layout: {minWidth: 150.->#px, width: 2.->#fr, margin: #huge},
    render: ({data: {receptionDate}}) => <TableDateCell value=receptionDate />,
  },
  {
    name: t("Amount"),
    key: "supply-amount",
    layout: {minWidth: 110.->#px},
    render: ({data: {totalAmountExcludingTaxes, totalAmountIncludingTaxes}}) =>
      <AmountTableCell
        value=totalAmountExcludingTaxes secondaryValue=totalAmountIncludingTaxes decimalPrecision=3
      />,
  },
  {
    name: t("Quantity"),
    key: "supply-quantity",
    layout: {width: 2.->#fr, alignX: #center},
    render: ({data: {quantity}}) => <TextStyle> {quantity->React.string} </TextStyle>,
  },
]

@react.component
let make = (~cku) => {
  let (executeQuery, queryResults) = Query.useLazy()
  let activeShop = Auth.useActiveShop()
  let {pathname} = Navigation.useUrl()

  React.useEffect1(() => {
    executeQuery(
      Query.makeVariables(
        ~cku=cku->Scalar.CKU.serialize,
        ~first=OrderSupplies.edgesPerFetch,
        ~filterBy=Query.makeInputObjectInputOrderProductsByVariantCkuQueryFilter(
          ~archived=#INCLUDED,
          ~receptionFinishedAt={
            _before: Some(Js.Date.now()->Js.Date.fromFloat->Scalar.Datetime.serialize),
            _between: None,
            _after: None,
          },
          ~shopIds=?switch activeShop {
          | Some({id}) => Some(Query.makeInputObjectInFilter(~_in=[id], ()))
          | _ => None
          },
          (),
        ),
        (),
      ),
    )
    None
  }, [activeShop])

  let allShopsFiltered = switch Auth.useScope() {
  | Organisation({activeShop: None}) => true
  | _ => false
  }
  let columns = React.useMemo1(() => tableColumns(~allShopsFiltered), [allShopsFiltered])

  <Card variation=#table title={t("Orders")}>
    {switch queryResults {
    | Executed({data: Some({orderProductsByVariantCku: {edges, pageInfo: {hasNextPage}}})}) =>
      let rows = edges->Array.keepMap(({node: orderProduct}) =>
        switch (orderProduct.order, orderProduct.order->Option.flatMap(order => order.supplier)) {
        | (Some(order), Some(supplier)) =>
          Some({
            {
              Row.id: order.id,
              shopName: order.shopName,
              name: order.name->Option.getWithDefault("?"),
              receptionDate: order.receptionFinishedAt,
              quantity: orderProduct.quantity->Int.toString,
              totalAmountExcludingTaxes: orderProduct.totalAmountExcludingTaxes,
              totalAmountIncludingTaxes: orderProduct.totalAmountIncludingTaxes,
              supplierId: supplier.id,
              supplierCompanyName: order.supplierCompanyName,
            }
          })
        | _ => None
        }
      )

      let placeholderEmptyState =
        <Placeholder status=NoDataAvailable customText={t("No order has been yet recorded")} />
      let data = AsyncData.Done(Ok(rows))

      <>
        <TableView columns data keyExtractor=Row.keyExtractor placeholderEmptyState />
        {if hasNextPage === Some(true) {
          let pathname = pathname ++ OrderSupply->LegacyRouter.routeToPathname
          <ShowAllDataLink to=Route(pathname) text={t("Show all data")} />
        } else {
          React.null
        }}
      </>
    | Unexecuted(_)
    | Executed({loading: true}) =>
      <Placeholder status=Loading />
    | _ => <Placeholder status=Error />
    }}
  </Card>
}

let make = React.memo(make)
