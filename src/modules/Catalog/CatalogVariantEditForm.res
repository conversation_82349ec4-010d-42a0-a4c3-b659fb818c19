open Intl

type variantPrice = {
  priceId: string,
  valueIncludingTax: float,
  valueExcludingTax: float,
}

type priceLookUpCode = {
  invalidValues: array<int>,
  value: option<int>,
}

// NOTE - fields indicated as optional passed with an empty string
// will be considered as undefined / unchanged during the mutation.
module Lenses = %lenses(
  type state = {
    name: string,
    capacityUnit: option<string>,
    capacityValue: option<float>, // non-bulk
    capacityPrecision: option<int>, // bulk
    ean13: string, // optional
    stockKeepingUnit: string, // optional
    priceLookUpCode: priceLookUpCode, // optional
    internalCode: string, // optional
    alcoholVolume: option<float>, // WINE | BEER | SPIRITUOUS
    year: option<float>, // WINE | SPIRITUOUS
    bulk: bool, // creation
    purchasePrice: float, // optional - creation
    supplierId: string, // optional - creation
    variantPrices: option<array<variantPrice>>, // creation
    initialStockQuantity: float, // optional - creation
    initialStockComment: string, // optional - creation
    maxStockThreshold: float, // optional - creation
    minStockThreshold: float, // optional - creation
    stockOrderTriggerThreshold: float, // optional - creation
  }
)

include Form.Make(Lenses)

let validateMinStockThreshold = state =>
  switch state {
  | {Lenses.minStockThreshold: 0., maxStockThreshold: 0., stockOrderTriggerThreshold: 0.} => Ok()
  | _ =>
    switch state {
    | {minStockThreshold, stockOrderTriggerThreshold}
      if minStockThreshold > stockOrderTriggerThreshold =>
      Error(t("The minimum stock should be less or equal to the value triggering a new order."))
    | {minStockThreshold, maxStockThreshold} if minStockThreshold >= maxStockThreshold =>
      Error(t("The minimum stock should be less than the maximum stock."))
    | _ => Ok()
    }
  }

let validateStockOrderTriggerThreshold = state =>
  switch state {
  | {Lenses.minStockThreshold: 0., maxStockThreshold: 0., stockOrderTriggerThreshold: 0.} => Ok()
  | _ =>
    switch state {
    | {maxStockThreshold: 0., stockOrderTriggerThreshold} if stockOrderTriggerThreshold > 0. =>
      Error(t("You have to define the maximum stock beforehand."))
    | {maxStockThreshold, stockOrderTriggerThreshold}
      if stockOrderTriggerThreshold >= maxStockThreshold =>
      Error(t("This value should be less than maximum stock value."))
    | _ => Ok()
    }
  }

let validatePriceLookupCode = state => {
  let (_, pluMax) = CatalogPriceLookUpInput.pluRange

  switch state.Lenses.priceLookUpCode {
  | {value: Some(value)} if value > pluMax => Error(t("The PLU code must be between 1 and 9997."))
  | {invalidValues, value: Some(value)} if invalidValues->Array.some(plu => plu === value) =>
    switch CatalogPriceLookUpInput.makeValue(
      ~alreadyConsummedValues=state.priceLookUpCode.invalidValues,
      (),
    ) {
    | Ok(_) => Error(t("This PLU code is used by another product."))
    | Error() => Error(t("There is no more PLU code available."))
    }
  | _ => Ok()
  }
}

let schema = [
  Schema.StringNotEmpty(Name),
  Custom(MinStockThreshold, validateMinStockThreshold),
  Custom(StockOrderTriggerThreshold, validateStockOrderTriggerThreshold),
  Custom(PriceLookUpCode, validatePriceLookupCode),
]
