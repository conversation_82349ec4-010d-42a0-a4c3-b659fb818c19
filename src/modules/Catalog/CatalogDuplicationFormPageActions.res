open Intl
open CatalogDuplication

module Mutations = CatalogDuplicationFormMutations

@react.component
let make = (~duplicationMode: Mode.t) => {
  let duplicateProduct = Mutations.useDuplicateProduct()
  let duplicateVariant = Mutations.useDuplicateVariant()

  <Inline space=#small>
    <CatalogDuplicationForm.SubmitButton
      text={t("Duplicate")}
      variation=#success
      size=#medium
      onSubmit={switch duplicationMode {
      | Product => duplicateProduct
      | Variant => duplicateVariant
      }}
    />
  </Inline>
}

let make = React.memo(make)
