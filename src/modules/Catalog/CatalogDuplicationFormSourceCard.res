open Intl
open CatalogDuplication

type reference = {
  id: string,
  shopId: string,
}

@react.component
let make = (~shopsData, ~references, ~duplicationMode) => {
  let availableShops = shopsData->Array.keep(shop => shop.Shop.duplicability === ExistsAlready)

  let options = availableShops->Array.map(shop => {
    let reference = references->Array.getBy(({shopId}) => shopId === shop.dataId)

    switch (duplicationMode, reference) {
    | (Mode.Product, Some({id, shopId})) => Some(Source.Product({id, shopId: Some(shopId)}))
    | (Mode.Variant, Some({id, shopId})) => Some(Source.Variant({id, shopId: Some(shopId)}))
    | _ => None
    }
  })

  let optionToLabel = source =>
    source
    ->Option.flatMap(source =>
      availableShops->Array.getBy(shop =>
        switch (shop.dataId, source) {
        | (shopId, Source.Product({shopId: Some(id)}) | Variant({shopId: Some(id)})) =>
          shopId === id
        | _ => false
        }
      )
    )
    ->Option.mapWithDefault("", ({dataName}) => dataName)

  let items = options->Array.map(source => {
    Select.key: source->optionToLabel,
    label: source->optionToLabel,
    value: source,
  })
  let sections = [{Select.items: items}]

  <Card grow=true>
    <CatalogDuplicationForm.InputSelect
      field=Source label={t("Shop catalog")} placeholder={t("Select a shop")} sections
    />
  </Card>
}

let make = React.memo(make)
