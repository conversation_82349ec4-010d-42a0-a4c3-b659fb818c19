open Intl

module Config = CatalogVariant__Config

@react.component
let make = (
  ~cku,
  ~variant: Config.variantInformation,
  ~fromLabelEditSettingsRedirected,
  ~onRequestNotification,
) => {
  let {pathname} = Navigation.useUrl()
  let (archiveModalOpened, setArchiveModalOpened) = React.useState(_ => false)
  let (printerStatusResult, setPrinterStatusResult) = React.useState(_ => None)
  let unarchiveVariant = CatalogVariantMutations.useUnarchive(~id=variant.id)
  let activeShop = Auth.useActiveShop()
  let scope = Auth.useScope()

  React.useEffect1(() => {
    switch activeShop {
    | Some({id: shopId}) =>
      let future = CatalogLabel.Print.DefaultPrinterRequest.make(~shopId)
      future->Future.map(result => setPrinterStatusResult(_ => Some(result)))->ignore
      Some(() => future->Future.cancel)
    | None => None
    }
  }, [activeShop])

  <>
    <Inline space=#small>
      {switch (printerStatusResult, activeShop) {
      | (Some(Ok() as status | Error(NoPrinterFound) as status), Some({id: shopId})) =>
        <CatalogLabelQuickPrintButton
          fromEditRedirection=fromLabelEditSettingsRedirected
          featureLocked={Result.isError(status)}
          variantId=variant.id
          shopId
          requestBarcodeCompletion=CatalogLabel.BarcodeCompletionRequest.make
          requestLabelsPrinting=CatalogLabel.Print.LabelsRequest.make
          onRequestNotification
        />
      | (Some(Error(UnexpectedFailure)), _) => React.null
      | (_, shop) =>
        <Tooltip
          content={<Tooltip.Span text={t("Please select a shop beforehand.")} />}
          delay=0
          disabled={shop->Option.isSome}>
          <Button variation=#neutral disabled=true>
            <Box spaceY=#small>
              <Inline space=#xnormal>
                <Icon name=#printer size=18. fill=Colors.neutralColor35 />
                <SpanX> {t("Print label")->React.string} </SpanX>
              </Inline>
            </Box>
          </Button>
        </Tooltip>
      }}
      <ButtonLink
        variation=#neutral
        to=Route(Catalog->LegacyRouter.routeToPathname ++ "/product/redirect/" ++ cku)>
        {t("See bound product")->React.string}
      </ButtonLink>
      {switch (variant.status, scope) {
      | (Some(Archived), Organisation({activeShop: Some(_)}) | Single(_)) =>
        <Button variation=#primary onPress={_ => unarchiveVariant()->ignore}>
          {t("Unarchive")->React.string}
        </Button>
      | _ =>
        <Menu>
          <MenuItem content={Text(t("Edit"))} action=OpenLink(Route(pathname ++ "/edit")) />
          {switch scope {
          | Organisation({activeShop: Some(_)})
          | Single(_) =>
            <MenuItem
              content={Text(t("Archive"))} action=Callback(() => setArchiveModalOpened(_ => true))
            />
          | _ => React.null
          }}
          {switch scope {
          | Organisation(_) =>
            <MenuItem
              content={Text(t("Duplicate"))}
              action=OpenLink(
                Route(Catalog->LegacyRouter.routeToPathname ++ "/duplication/" ++ cku),
              )
            />
          | _ => React.null
          }}
        </Menu>
      }}
    </Inline>
    <CatalogVariantArchiveModal
      id=variant.id
      opened=archiveModalOpened
      onRequestClose={() => setArchiveModalOpened(_ => false)}
    />
  </>
}

let make = React.memo(make)
