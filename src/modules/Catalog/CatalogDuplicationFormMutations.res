open Intl
open CatalogDuplicationForm

exception DuplicateProduct_SourceIdNotFound
exception DuplicateProduct_DestinationIdsMissing

exception DuplicateVariant_SourceIdNotFound
exception DuplicateVariant_DestinationIdsMissing

module DuplicateProductMutation = %graphql(`
  mutation duplicateProduct($id: ID!, $input: InputDuplicateProduct!) {
    duplicateProduct(id: $id, input: $input) {
      id
      name
      shop {
        name
      }
    }
  }
`)

module DuplicateVariantMutation = %graphql(`
  mutation duplicateVariant($id: ID!, $input: InputDuplicateVariant!) {
    duplicateVariant(id: $id, input: $input) {
      id
      name
      shop {
        name
      }
    }
  }
`)

let useDuplicateProduct = () => {
  let (mutate, _) = DuplicateProductMutation.use()

  React.useCallback0((_, state: Lenses.state) => {
    let id = switch state.source {
    | Some(Product({id})) => id
    | _ => raise(DuplicateProduct_SourceIdNotFound)
    }
    let linkTo = state.destinations->Array.keepMap(destination =>
      switch destination {
      | Product({shopId: Some(shopId)}) => Some((shopId, None))
      | Category({id, shopId: Some(shopId)}) => Some((shopId, Some(id)))
      | _ => None
      }
    )
    let input = DuplicateProductMutation.makeInputObjectInputDuplicateProduct(
      ~linkTo=linkTo->Array.map(((shopId, categoryId)) =>
        DuplicateProductMutation.makeInputObjectInputDuplicateProductLinkTo(
          ~shopId,
          ~categoryId?,
          (),
        )
      ),
      (),
    )

    // Additional type safety checks
    if linkTo->Array.length !== state.destinations->Array.length {
      raise(DuplicateProduct_DestinationIdsMissing)
    }

    mutate(DuplicateProductMutation.makeVariables(~id, ~input, ()))
    ->ApolloHelpers.mutationPromiseToFutureResult
    ->Future.mapOk(({DuplicateProductMutation.duplicateProduct: {name, shop}}) => Some(
      template(
        if linkTo->Array.length > 1 {
          t(
            "The product \"{{referenceName}}\" from catalog {{shopName}} has been correctly added to the selected shops.",
          )
        } else {
          t(
            "The product \"{{referenceName}}\" from catalog {{shopName}} has been correctly added to your shop.",
          )
        },
        ~values={"referenceName": name, "shopName": shop.name},
        (),
      ),
    ))
  })
}

let useDuplicateVariant = () => {
  let (mutate, _) = DuplicateVariantMutation.use()

  React.useCallback0((_, state: Lenses.state) => {
    let id = switch state.source {
    | Some(Variant({id})) => id
    | _ => raise(DuplicateVariant_SourceIdNotFound)
    }
    let productIds = state.destinations->Array.keepMap(x =>
      switch x {
      | Product({id}) => Some(id)
      | _ => None
      }
    )
    let input = DuplicateVariantMutation.makeInputObjectInputDuplicateVariant(
      ~linkTo=productIds->Array.map(productId =>
        DuplicateVariantMutation.makeInputObjectInputDuplicateVariantLinkTo(~productId, ())
      ),
      (),
    )

    // Additional type safety checks
    if productIds->Array.length !== state.destinations->Array.length {
      raise(DuplicateVariant_DestinationIdsMissing)
    }

    mutate(DuplicateVariantMutation.makeVariables(~id, ~input, ()))
    ->ApolloHelpers.mutationPromiseToFutureResult
    ->Future.mapOk(({DuplicateVariantMutation.duplicateVariant: {name, shop}}) => Some(
      template(
        if productIds->Array.length > 1 {
          t(
            "The variant \"{{referenceName}}\" from catalog {{shopName}} has been correctly added to the selected shops.",
          )
        } else {
          t(
            "The variant \"{{referenceName}}\" from catalog {{shopName}} has been correctly added to your shop.",
          )
        },
        ~values={"referenceName": name, "shopName": shop.name},
        (),
      ),
    ))
  })
}
