open Intl
open StyleX

module CatalogLabelQuickPrintModal = {
  @react.component
  let make = React.memo((
    ~fromEditRedirection,
    ~variantId,
    ~shopId,
    ~requestBarcodeCompletion: Json.t => Future.t<result<Json.t, Request.error>>,
    ~requestLabelsPrinting,
    ~opened,
    ~onRequestClose,
    ~onRequestNotification,
    ~stateSettingsStorageUseRead=CatalogLabel.StateSettingsUserPreferences.useRead,
  ) => {
    let printSettings = stateSettingsStorageUseRead()
    let (printingQuantity, setPrintingQuantity) = React.useState(() => 1.)
    let navigate = Navigation.useNavigate()
    let {pathname} = Navigation.useUrl()

    let editRedirectionQueryString = CatalogRoutes.CatalogLabelQueryString.encode({
      variantIdfromLabelEditSettings: Some(variantId),
      fromPathname: Some(pathname),
    })

    let hasSettings = switch printSettings {
    | Some({labelFormat: LabelPrint(_)}) => true
    | Some({labelFormat: LabelSheet(_)}) | None => false
    }

    let onCommit = () =>
      if hasSettings {
        switch printSettings {
        | Some({labelFormat: LabelPrint(labelFormat), priceName} as values) =>
          CatalogLabel.Print.CombineRequests.make(
            ~requestBarcodeCompletion,
            ~requestLabelsPrinting,
            ~priceId=values.priceId,
            ~priceName,
            ~productBarcodeDisplayed=values.productBarcodeDisplayed,
            ~missingProductBarcodeGenerated=values.missingProductBarcodeGenerated,
            ~priceDisplayed=values.priceDisplayed,
            ~producerDisplayed=values.producerDisplayed,
            ~alcoholVolumeDisplayed=values.alcoholVolumeDisplayed,
            ~productCode=values.productCode,
            ~labelFormat,
            ~productsValues=Picked([
              {
                CatalogLabel.RequestProductType.variantId,
                repetitions: printingQuantity->Int.fromFloat,
              },
            ]),
            ~shopId,
          )->Future.get(result =>
            switch result {
            | Ok() =>
              onRequestNotification(
                Ok(
                  template(
                    printingQuantity > 1.
                      ? t("{{quantity}} prints of the label has been started.")
                      : t("{{quantity}} print of the label has been started."),
                    ~values={
                      "quantity": printingQuantity,
                    },
                    (),
                  ) ++
                  template(
                    t("[Monitor printing from your StarPrinter Online account]({{link}})"),
                    ~values={"link": "https://portal.starprinter.online/Dashboard"},
                    (),
                  ),
                ),
              )
            | Error(message) => onRequestNotification(Error(message))
            }
          )
        | _ => ()
        }
      } else {
        navigate(
          CatalogRoutes.labelsCreateSettingsRoute ++
          "?" ++
          editRedirectionQueryString->QueryString.toString,
        )
      }

    let modalContentElement = switch printSettings {
    | Some({labelFormat: LabelPrint(labelFormat)} as values) =>
      <Stack space=#large>
        {if fromEditRedirection {
          <Banner textStatus={Success(t("Print parameters set correctly!"))} />
        } else {
          React.null
        }}
        <InputNumberField
          label={t("Copies")}
          minValue=1.
          precision=0
          value=printingQuantity
          onChange={value => setPrintingQuantity(_ => value)}
        />
        <div
          style={ReactDOM.Style.make(
            ~border="1px solid " ++ Colors.neutralColor20,
            ~borderRadius="4px",
            (),
          )}>
          <Card
            title={t("Print settings")}
            action={{
              icon: #edit_light,
              title: t("Edit settings"),
              handler: OpenLink(
                RouteWithQueryString(
                  CatalogRoutes.labelsCreateSettingsRoute,
                  editRedirectionQueryString,
                ),
              ),
            }}>
            <Stack space=#normal>
              <Stack>
                <TextStyle> {values.priceName->React.string} </TextStyle>
                <TextStyle>
                  {template(
                    t("Format: {{format}}"),
                    ~values={
                      "format": CatalogLabel.Print.LabelFormat.toFormatLabel(labelFormat),
                    },
                    (),
                  )->React.string}
                </TextStyle>
              </Stack>
              <Stack>
                {values.productBarcodeDisplayed
                  ? <TextStyle>
                      {(t("Display barcode") ++ (
                        values.missingProductBarcodeGenerated
                          ? " " ++ t("(generate automatically if missing)")
                          : ""
                      ))->React.string}
                    </TextStyle>
                  : React.null}
                {values.priceDisplayed
                  ? <TextStyle> {t("Display retail price")->React.string} </TextStyle>
                  : React.null}
                {values.producerDisplayed
                  ? <TextStyle> {t("Display producer")->React.string} </TextStyle>
                  : React.null}
                {values.alcoholVolumeDisplayed
                  ? <TextStyle> {t("Display alcohol volume")->React.string} </TextStyle>
                  : React.null}
                <TextStyle>
                  {switch values.productCode {
                  | Hidden => React.null
                  | SKU => t("Display SKU")->React.string
                  | InternalCode => t("Display internal code")->React.string
                  }}
                </TextStyle>
              </Stack>
            </Stack>
          </Card>
        </div>
      </Stack>
    | Some({labelFormat: LabelSheet(_)}) | None =>
      <Placeholder
        status=Pending({
          illustration: Some(Illustration.warningTriangle),
          title: t("No print settings set"),
          message: t(
            "To print a label from a product page, you must first enter the print settings.",
          ),
        })
      />
    }

    <Modal
      opened
      title={t("Print label")}
      abortButtonText={t("Cancel")}
      commitButtonText={hasSettings ? t("Print label") : t("Set settings")}
      commitButtonVariation=#primary
      commitButtonCallback=onCommit
      onRequestClose>
      <Box spaceX=#large spaceTop=#xlarge spaceBottom=#large> modalContentElement </Box>
    </Modal>
  })
}

let styles = StyleX.create({
  "buttonBackbone": style(
    ~display=#flex,
    ~flexDirection=#row,
    ~justifyContent=#center,
    ~alignItems=#center,
    ~height="42px",
    ~paddingInline="24px",
    ~borderRadius="5px",
    (),
  ),
  "buttonTextLocked": style(~font=`normal 600 16px "Archivo"`, ~color=Colors.brandColor50, ()),
  "buttonStyleLocked": style(
    ~borderColor=Colors.brandColor40,
    ~backgroundColor=Colors.brandColor05,
    ~borderStyle=#solid,
    ~borderWidth="1px",
    (),
  ),
  "overlayBackbone": style(~width="400px", ~padding=Spaces.mediumPx, ()),
  "overlayTitleText": style(
    ~font=`normal 600 18px "Libre Franklin"`,
    ~color=Colors.brandColor50,
    (),
  ),
  "overlayTitleIcon": style(
    ~display=#flex,
    ~padding="6px",
    ~borderRadius="5px",
    ~backgroundColor=Colors.brandColor05,
    (),
  ),
  "overlayButtonStyle": style(
    ~borderColor=Colors.brandColor40,
    ~backgroundColor=Colors.brandColor05,
    (),
  ),
})

let buttonStyleFromParams = (~hovered, ~active) =>
  if active {
    style(~backgroundColor=Colors.brandColor10, ~borderColor=Colors.brandColor60, ())
  } else if hovered {
    style(~backgroundColor=Colors.brandColor10, ())
  } else {
    style()
  }

module CatalogLabelQuickPrintButtonPopover = {
  @react.component
  let make = React.memo((~triggerRef, ~state, ~overlayProps) => {
    let (buttonRef, buttonHovered) = Hover.use()
    let onToggleHelpCenter = () => HelpCenter.showArticle(HelpCenter.getLabelPrintFeature)

    <Popover triggerRef state modal=false placement=#"bottom start" borderColor=Colors.brandColor50>
      <Popover.Dialog ariaProps=overlayProps>
        <DivX style={StyleX.props([styles["overlayBackbone"]])}>
          <Stack space=#xmedium>
            <Inline space=#normal>
              <DivX style={StyleX.props([styles["overlayTitleIcon"]])}>
                <Icon name={#printer_locked} fill=Colors.brandColor50 size=20. />
              </DivX>
              <SpanX style={StyleX.props([styles["overlayTitleText"]])}>
                {t("Print label")->React.string}
              </SpanX>
            </Inline>
            <TextStyle>
              {t(
                t(
                  "This new feature lets you print a label directly from a product page with a Star printer.",
                ),
              )->React.string}
            </TextStyle>
            <Touchable ref=buttonRef onPress={_ => onToggleHelpCenter()}>
              <DivX
                style={StyleX.props([
                  styles["buttonBackbone"],
                  styles["buttonTextLocked"],
                  styles["overlayButtonStyle"],
                  buttonStyleFromParams(~hovered=buttonHovered, ~active=false),
                ])}>
                <SpanX style={StyleX.props([styles["buttonTextLocked"]])}>
                  {t("Get the feature")->React.string}
                </SpanX>
              </DivX>
            </Touchable>
          </Stack>
        </DivX>
      </Popover.Dialog>
    </Popover>
  })
}

type variation = [#standard | #rounded | #vertical]

@react.component
let make = React.memo((
  ~variation: variation=#standard,
  ~featureLocked,
  ~fromEditRedirection=false,
  ~variantId,
  ~shopId,
  ~requestBarcodeCompletion,
  ~requestLabelsPrinting,
  ~onRequestNotification,
  ~stateSettingsStorageUseRead=CatalogLabel.StateSettingsUserPreferences.useRead,
) => {
  let {ref: popoverTriggerRef, state: popover, ariaProps: popoverAriaProps} = Popover.useTrigger()
  let (_, triggerHovered) = Hover.use(~ref=popoverTriggerRef, ())
  let (printModalOpened, setPrintModalOpened) = React.useState(() => fromEditRedirection)

  let handleOnPress = _ =>
    if featureLocked {
      popover.onRequestToggle()
    } else {
      setPrintModalOpened(_ => true)
    }

  switch variation {
  | #standard =>
    <>
      {if featureLocked {
        <Touchable ref=popoverTriggerRef onPress=handleOnPress>
          <DivX
            style={StyleX.props([
              styles["buttonBackbone"],
              styles["buttonStyleLocked"],
              buttonStyleFromParams(~hovered=triggerHovered, ~active=popover.opened),
            ])}>
            <Inline space=#xnormal>
              <Icon name=#printer_locked fill=Colors.brandColor50 size=18. />
              <SpanX style={StyleX.props([styles["buttonTextLocked"]])}>
                {t("Print label")->React.string}
              </SpanX>
            </Inline>
          </DivX>
        </Touchable>
      } else {
        <>
          <Button variation=#neutral onPress=handleOnPress>
            <Box spaceY=#small>
              <Inline space=#xnormal>
                <Icon name=#printer size=18. />
                {t("Print label")->React.string}
              </Inline>
            </Box>
          </Button>
          <CatalogLabelQuickPrintModal
            fromEditRedirection
            variantId
            shopId
            requestBarcodeCompletion
            requestLabelsPrinting
            opened=printModalOpened
            onRequestClose={() => setPrintModalOpened(_ => false)}
            onRequestNotification
            stateSettingsStorageUseRead
          />
        </>
      }}
      {if popover.opened {
        <CatalogLabelQuickPrintButtonPopover
          triggerRef=popoverTriggerRef state=popover overlayProps=popoverAriaProps.overlayProps
        />
      } else {
        React.null
      }}
    </>
  | #rounded if !featureLocked =>
    <>
      <Tooltip
        placement=#"top end"
        arrowed=false
        closeDelay=0
        content={<Tooltip.Span text={t("Print label")} />}>
        <RoundButton
          ref=popoverTriggerRef
          ariaProps=popoverAriaProps.triggerProps
          icon=#printer
          focused=popover.opened
          onPress=handleOnPress
        />
      </Tooltip>
      <CatalogLabelQuickPrintModal
        fromEditRedirection
        variantId
        shopId
        requestBarcodeCompletion
        requestLabelsPrinting
        opened=printModalOpened
        onRequestClose={() => setPrintModalOpened(_ => false)}
        onRequestNotification
        stateSettingsStorageUseRead
      />
    </>
  | #vertical if !featureLocked =>
    <>
      <Tooltip
        placement=#"top end"
        arrowed=false
        closeDelay=0
        content={<Tooltip.Span text={t("Print label")} />}>
        <ShortIconButton
          name=#printer
          bordered=false
          focused=popover.opened
          ref=popoverTriggerRef
          ariaProps=popoverAriaProps.triggerProps
          action={Callback(handleOnPress)}
        />
      </Tooltip>
      <CatalogLabelQuickPrintModal
        fromEditRedirection
        variantId
        shopId
        requestBarcodeCompletion
        requestLabelsPrinting
        opened=printModalOpened
        onRequestClose={() => setPrintModalOpened(_ => false)}
        onRequestNotification
        stateSettingsStorageUseRead
      />
    </>
  | _ => React.null
  }
})
