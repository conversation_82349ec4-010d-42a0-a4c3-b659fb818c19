open CatalogProduct.Status

@react.component
let make = (~value, ~size=#medium) =>
  switch value {
  | Some(Active) => <Badge variation=#success size> {Active->toLabel->React.string} </Badge>
  | Some(Inactive) => <Badge variation=#danger size> {Inactive->toLabel->React.string} </Badge>
  | Some(Archived) => <Badge variation=#neutral size> {Archived->toLabel->React.string} </Badge>
  | _ => React.null
  }

let make = React.memo(make)
