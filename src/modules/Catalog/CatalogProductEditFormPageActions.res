open Intl

module Mutations = CatalogProductEditFormMutations

@react.component
let make = (~editionMode: bool) => {
  let updateProduct = Mutations.useUpdateProduct()
  let navigate = Navigation.useNavigate()

  let onPressNextStep = React.useCallback0((_, state) => {
    navigate(
      Catalog->LegacyRouter.routeToPathname ++ "/create",
      ~state=CatalogVariantEditUrlState.CreateVariantFromProduct.encode(state),
    )
    Future.value(Ok(None))
  })

  <Inline space=#small>
    {if editionMode {
      <CatalogProductEditForm.SubmitButton
        text={t("Save")} variation=#success size=#medium onSubmit=updateProduct
      />
    } else {
      <CatalogProductEditForm.SubmitButton
        text={t("Continue")} variation=#success size=#medium onSubmit=onPressNextStep
      />
    }}
  </Inline>
}

let make = React.memo(make)
