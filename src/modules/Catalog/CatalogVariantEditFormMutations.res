open CatalogVariantEditForm

exception CreateProductAndVariant_ProductValuesNotFound
exception CreateVariantInput_NameError
exception CreateVariantInput_CapacityValueError
exception CreateVariantInput_StockThresholdsInvalid
exception CreateVariant_FormIdNotFound
exception CreateVariant_ShopIdNotFound
exception CreateVariant_DeviceIdNotFound
exception UpdateVariantInput_NameError
exception UpdateVariant_FormIdNotFound

module CreateVariantMutation = %graphql(`
  mutation createVariant($input: InputCreateVariant!, $variantPricesInput: [InputCreateVariantPrices!]!) {
    createVariant(input: $input, variantPricesInput: $variantPricesInput) {
      id
      cku
      purchasedPrice
      name
      formattedName
      capacityValue
      capacityUnit
      capacityPrecision
      year
      alcoholVolume
      ean13
      stockKeepingUnit
      priceLookUpCode
      internalCode
      maxStockThreshold
      minStockThreshold
      stockOrderTriggerThreshold
    }
  }
`)

module CreateStockActivity = %graphql(`
  mutation createStockActivity($input: InputCreateStockActivity!) {
    createStockActivity(input: $input) {
      id
    }
  }
`)

module UpdateVariantMutation = %graphql(`
  mutation updateVariant($id: ID!, $input: InputUpdateVariant!) {
    updateVariant(id: $id, input: $input) {
      id
      updatedAt
      purchasedPrice
      name
      formattedName
      capacityValue
      capacityUnit
      capacityPrecision
      year
      alcoholVolume
      ean13
      stockKeepingUnit
      priceLookUpCode
      internalCode
    }
  }
`)

let makeCreateVariantInput = (~state: Lenses.state, ~productId, ~shopId) =>
  switch state {
  | {name: ""} => raise(CreateVariantInput_NameError)
  | {bulk: true, capacityValue: Some(capacityValue)} if capacityValue > 0. =>
    raise(CreateVariantInput_CapacityValueError)
  | {minStockThreshold, maxStockThreshold}
    if minStockThreshold >= maxStockThreshold && maxStockThreshold !== 0. =>
    raise(CreateVariantInput_StockThresholdsInvalid)
  | {minStockThreshold, stockOrderTriggerThreshold}
    if minStockThreshold > stockOrderTriggerThreshold && stockOrderTriggerThreshold !== 0. =>
    raise(CreateVariantInput_StockThresholdsInvalid)
  | {maxStockThreshold, stockOrderTriggerThreshold}
    if stockOrderTriggerThreshold >= maxStockThreshold && maxStockThreshold !== 0. =>
    raise(CreateVariantInput_StockThresholdsInvalid)
  | state =>
    CreateVariantMutation.makeInputObjectInputCreateVariant(
      ~name=state.name,
      ~capacityValue=?switch state {
      | {capacityUnit: Some(_), capacityValue} => capacityValue
      | {capacityUnit: None, bulk: false} | _ => None
      },
      ~capacityUnit=?switch state {
      | {capacityUnit: Some(unit)} => Some(unit)
      | {capacityValue: None, bulk: false} | _ => None
      },
      ~capacityPrecision=?switch state {
      | {bulk: true} => Some(CatalogVariant.defaultBulkCapacityPrecision)
      | _ => None
      },
      ~year=?state.year->Option.map(Int.fromFloat),
      ~alcoholVolume=?state.alcoholVolume,
      ~ean13=state.ean13,
      ~stockKeepingUnit=state.stockKeepingUnit,
      ~priceLookUpCode=?state.priceLookUpCode.value,
      ~internalCode=state.internalCode,
      ~bulk=state.bulk,
      ~maxStockThreshold=?{
        switch state {
        | {maxStockThreshold: 0.} => None
        | {maxStockThreshold} => Some(maxStockThreshold->Int.fromFloat)
        }
      },
      ~minStockThreshold=?{
        switch state {
        | {minStockThreshold: 0.} => None
        | {minStockThreshold} => Some(minStockThreshold->Int.fromFloat)
        }
      },
      ~stockOrderTriggerThreshold=?{
        switch state {
        | {stockOrderTriggerThreshold: 0.} => None
        | {stockOrderTriggerThreshold} => Some(stockOrderTriggerThreshold->Int.fromFloat)
        }
      },
      ~purchasedPrice=state.purchasePrice,
      ~supplierId=?switch state {
      | {supplierId: ""} => None
      | _ => Some(state.supplierId)
      },
      // TODO - ~packaging: option(int),
      ~productId,
      ~shopId,
      (),
    )
  }

let useCreateVariant = () => {
  let (mutate, _) = CreateVariantMutation.use()
  let (mutateStockActivity, _) = CreateStockActivity.use()
  let activeShop = Auth.useActiveShop()

  React.useCallback1((productId, state: Lenses.state) => {
    let productId = switch productId {
    | Some(id) => id
    | _ => raise(CreateVariant_FormIdNotFound)
    }
    let shopId = switch activeShop {
    | Some({id}) => id
    | _ => raise(CreateVariant_ShopIdNotFound)
    }
    let deviceId = switch activeShop {
    | Some({activeWebDeviceId}) => activeWebDeviceId
    | _ => raise(CreateVariant_DeviceIdNotFound)
    }
    let input = makeCreateVariantInput(~state, ~productId, ~shopId)
    let variantPricesInput = switch state.variantPrices {
    | Some(variantPrices) =>
      variantPrices->Array.map(({priceId, valueIncludingTax, valueExcludingTax}) =>
        CreateVariantMutation.makeInputObjectInputCreateVariantPrices(
          ~priceId,
          ~valueIncludingTax,
          ~valueExcludingTax,
          (),
        )
      )
    | _ => []
    }

    mutate(CreateVariantMutation.makeVariables(~input, ~variantPricesInput, ()))
    ->ApolloHelpers.mutationPromiseToFutureResult
    ->Future.flatMapOk(({
      CreateVariantMutation.createVariant: {id: variantId, cku, capacityPrecision},
    }) =>
      switch state {
      | {initialStockQuantity: quantity, initialStockComment: comment} if quantity > 0. =>
        let input = CreateStockActivity.makeInputObjectInputCreateStockActivity(
          ~kind=#DELIVERY,
          ~variantId,
          ~quantity=quantity->StockActivityQuantity.toRawValue(~capacityPrecision?),
          ~capacityUnit=?state.capacityUnit,
          ~capacityPrecision?,
          ~comment,
          ~deviceId,
          ~shopId,
          (),
        )
        let variables = CreateStockActivity.makeVariables(~input, ())
        mutateStockActivity(variables)
        ->ApolloHelpers.mutationPromiseToFutureResult
        ->Future.mapOk(_ => Some(cku))
      | _ => Future.value(Ok(Some(cku)))
      }
    )
  }, [activeShop])
}

// TODO - make nullable field typesafe with a specific function and majJsVariables
// see: https://github.com/winoteam/pos/pull/419#discussion_r792867588
let makeUpdateVariantInput = (~state: Lenses.state) =>
  UpdateVariantMutation.makeInputObjectInputUpdateVariant(
    ~name=switch state {
    | {name: ""} => raise(UpdateVariantInput_NameError)
    | _ => state.name
    },
    ~capacityValue=switch state {
    | {capacityUnit: None} | {capacityValue: None} => %raw(`null`)
    | {capacityValue: Some(capacityValue)} => capacityValue
    },
    ~capacityUnit=state.capacityUnit->Option.getWithDefault(%raw(`null`)),
    ~year=state.year->Option.mapWithDefault(%raw(`null`), Int.fromFloat),
    ~alcoholVolume=state.alcoholVolume->Option.getWithDefault(%raw(`null`)),
    ~ean13=state.ean13,
    ~stockKeepingUnit=state.stockKeepingUnit,
    ~priceLookUpCode=state.priceLookUpCode.value->Option.getWithDefault(%raw(`null`)),
    ~internalCode=state.internalCode,
    (),
  )

let useUpdateVariant = () => {
  let (mutate, _) = UpdateVariantMutation.use()

  React.useCallback0((variantId, state: Lenses.state) => {
    let id = switch variantId {
    | Some(id) => id
    | _ => raise(UpdateVariant_FormIdNotFound)
    }
    let input = makeUpdateVariantInput(~state)

    mutate(UpdateVariantMutation.makeVariables(~id, ~input, ()))
    ->ApolloHelpers.mutationPromiseToFutureResult
    ->Future.mapOk(_ => Some(id))
  })
}

let useCreateProductAndVariant = (~productValues) => {
  let createProduct = CatalogProductEditFormMutations.useCreateProduct()
  let createVariant = useCreateVariant()

  React.useCallback1((_, state: Lenses.state) => {
    let productState = switch productValues {
    | Some(productValues) => productValues
    | _ => raise(CreateProductAndVariant_ProductValuesNotFound)
    }

    createProduct(None, productState)
    ->Future.flatMapOk(productId => createVariant(Some(productId), state))
    ->Future.mapOk(result => result)
  }, [productValues])
}
