open Intl

module Query = %graphql(`
  query ProductProducers($filterBy: InputProductProducersQueryFilter) {
    productProducers(filterBy: $filterBy)
  }
`)

let useProductProducersQueryFn = variables =>
  Query.use(~fetchPolicy=CacheAndNetwork, variables)
  ->ApolloHelpers.queryResultToAsyncResult
  ->AsyncResult.mapError(_ => ())

@react.component
let make = (~shopId, ~value, ~useProductProducersQuery=useProductProducersQueryFn, ~onChange) => {
  let queryResult = useProductProducersQuery({
    filterBy: Some({
      shopIds: switch shopId {
      | Some(shopId) => Some(Query.makeInputObjectInFilter(~_in=[shopId], ()))
      | None => None
      },
    }),
  })

  let sections = {
    let items = switch queryResult {
    | Done(Ok({productProducers})) | Reloading(Ok({productProducers})) =>
      productProducers->Array.map(producerName => {
        Select.label: producerName,
        key: producerName,
        value: Some(producerName),
      })
    | _ => []
    }
    let defaultItem = {
      Select.key: "default",
      label: t("All"),
      value: None,
      sticky: true,
    }
    [{Select.items: [defaultItem]}, {title: t("Producers"), items}]
  }
  let disabled = shopId->Option.isNone || queryResult->AsyncResult.isBusy

  <Tooltip
    content={<Tooltip.Span text={t("Please select a shop beforehand.")} />}
    delay=0
    placement=#top
    disabled={!disabled}>
    <Select label={t("Producer")} preset=#filter size=#compact disabled sections value onChange />
  </Tooltip>
}
