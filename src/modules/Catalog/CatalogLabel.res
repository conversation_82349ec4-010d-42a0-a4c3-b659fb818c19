open Intl

module RequestProductType = {
  type product = {
    variantId: string,
    repetitions: int,
  }
  type t = Picked(array<product>) | All
}

module RequestSheetLabelsType = {
  type variant = {
    id: string,
    productName: string,
    name: string,
  }
  type t = {
    url: Url.t,
    invalidBarcodesVariants: array<variant>,
  }
}

module ProductCode = {
  type t = Hidden | SKU | InternalCode

  let values = [Hidden, SKU, InternalCode]
  let toString = value =>
    switch value {
    | Hidden => "Hidden"
    | SKU => "SKU"
    | InternalCode => "InternalCode"
    }
  let fromString = value =>
    switch value {
    | "Hidden" => Ok(Hidden)
    | "SKU" => Ok(SKU)
    | "InternalCode" => Ok(InternalCode)
    | _ => Error()
    }
  let toLabel = value =>
    switch value {
    | Hidden => t("Do not display additional product codes")
    | SKU => t("Display SKU")
    | InternalCode => t("Display internal code")
    }
}

module BarcodeCompletionRequest = {
  let endpoint = Env.gatewayUrl() ++ "/barcode-completions"

  let encodeBodyJson = (~productsValues, ~shopId) =>
    switch productsValues {
    | RequestProductType.Picked([]) =>
      Error(t("Products list empty, please select at least one product before continuing"))
    | products =>
      let dict = Js.Dict.empty()
      switch products {
      | All => dict->Js.Dict.set("shopId", shopId->Js.Json.string)
      | Picked(variantIds) =>
        dict->Js.Dict.set("shopId", shopId->Js.Json.string)
        dict->Js.Dict.set(
          "variantIds",
          variantIds->Array.map(({variantId}) => variantId->Js.Json.string)->Js.Json.array,
        )
      }
      Ok(dict->Js.Json.object_)
    }

  let make = bodyJson =>
    Request.make(endpoint, ~bodyJson, ~method=#POST, ~skipMalformedOkResult=true)
}

module Sheet = {
  module LabelFormat = {
    type t = Grid21 | Grid64

    let toString = value =>
      switch value {
      | Grid21 => "Grid21"
      | Grid64 => "Grid64"
      }
    let fromString = value =>
      switch value {
      | "Grid21" => Ok(Grid21)
      | "Grid64" => Ok(Grid64)
      | _ => Error()
      }
    let toMaxOffset = value =>
      switch value {
      | Grid21 => 21
      | Grid64 => 64
      }
    let toFormatLabel = value =>
      switch value {
      | Grid21 => "6,35 x 3,81 cm"
      | Grid64 => "4,57 x 1,68 cm"
      }
    let toLabel = value =>
      template(
        t("Sheet of {{number}} labels (format {{format}})"),
        ~values={
          "number": toMaxOffset(value),
          "format": toFormatLabel(value),
        },
        (),
      )
  }

  module LabelSort = {
    type t = Unsorted | AscProductName | AscProductCategory

    let values = [Unsorted, AscProductName, AscProductCategory]
    let toString = value =>
      switch value {
      | Unsorted => "Unsorted"
      | AscProductName => "AscProductName"
      | AscProductCategory => "AscProductCategory"
      }
    let fromString = value =>
      switch value {
      | "Unsorted" => Ok(Unsorted)
      | "AscProductName" => Ok(AscProductName)
      | "AscProductCategory" => Ok(AscProductCategory)
      | _ => Error()
      }
    let toLabel = value =>
      switch value {
      | Unsorted => t("Do not sort")
      | AscProductName => t("Sort alphabetically by product name")
      | AscProductCategory => t("Sort alphabetically by product category")
      }
  }

  module LabelsRequest = {
    let endpoint = Env.pdfUrl() ++ "/variant-labels"

    let encodeBodyJson = (
      ~productsValues,
      ~priceId,
      ~productBarcodeDisplayed,
      ~priceDisplayed,
      ~producerDisplayed,
      ~alcoholVolumeDisplayed,
      ~productCode,
      ~labelFormat,
      ~printOffset,
      ~borderEnabled,
      ~sort,
    ) => {
      switch productsValues {
      | RequestProductType.Picked([]) =>
        Error(t("Products list empty, please select at least one product before continuing"))
      | _ =>
        let dict = Js.Dict.empty()

        dict->Js.Dict.set("priceId", priceId->Uuid.toString->Json.encodeString)
        dict->Js.Dict.set("barcodeEnabled", productBarcodeDisplayed->Json.encodeBoolean)
        dict->Js.Dict.set("priceEnabled", priceDisplayed->Json.encodeBoolean)
        dict->Js.Dict.set("producerEnabled", producerDisplayed->Json.encodeBoolean)
        dict->Js.Dict.set("alcoholVolumeEnabled", alcoholVolumeDisplayed->Json.encodeBoolean)
        dict->Js.Dict.set("borderEnabled", borderEnabled->Json.encodeBoolean)
        dict->Js.Dict.set("offsetLabel", printOffset->Float.fromInt->Json.encodeNumber)

        open ProductCode
        switch productCode {
        | Hidden => ()
        | SKU => dict->Js.Dict.set("labelCodeKind", "SKU"->Json.encodeString)
        | InternalCode => dict->Js.Dict.set("labelCodeKind", "INTERNAL_CODE"->Json.encodeString)
        }

        open LabelFormat
        switch labelFormat {
        | Grid64 => dict->Js.Dict.set("labelsPerPage", "A4_64"->Json.encodeString)
        | Grid21 => dict->Js.Dict.set("labelsPerPage", "A4_21"->Json.encodeString)
        }

        open LabelSort
        switch sort {
        | AscProductName => dict->Js.Dict.set("labelSorting", "ASC_PRODUCT_NAME"->Json.encodeString)
        | AscProductCategory => dict->Js.Dict.set("labelSorting", "ASC_CATEGORY"->Json.encodeString)
        | Unsorted => ()
        }

        switch productsValues {
        | Picked(products) =>
          let items = products->Array.map(({variantId, repetitions}) => {
            Js.Dict.fromArray([
              ("variantId", Json.encodeString(variantId)),
              ("repetitions", Json.encodeNumber(repetitions->Int.toFloat)),
            ])->Js.Json.object_
          })
          dict->Js.Dict.set("items", items->Json.encodeArray)
        | All => ()
        }

        Ok(dict->Json.encodeDict)
      }
    }

    let decodeUrl = json => json->Json.flatDecodeDictFieldString("url")
    let decodeInvalidBarcodesVariants = json =>
      json
      ->Json.flatDecodeDictFieldArray("invalidBarcodes")
      ->Option.map(invalidBarcodes =>
        invalidBarcodes->Array.keepMap(item => {
          let variantId = item->Json.decodeDict->Json.flatDecodeDictFieldString("variantId")
          let productName = item->Json.decodeDict->Json.flatDecodeDictFieldString("productName")
          let variantName = item->Json.decodeDict->Json.flatDecodeDictFieldString("variantName")
          switch (variantId, productName, variantName) {
          | (Some(variantId), Some(productName), Some(variantName)) =>
            Some({RequestSheetLabelsType.id: variantId, productName, name: variantName})
          | _ => None
          }
        })
      )
    let decodeBodyJson = result =>
      switch result
      ->Json.decodeDict
      ->Option.map(data => (decodeUrl(Some(data)), decodeInvalidBarcodesVariants(Some(data))))
      ->Option.map(((url, variants)) => (url->Option.map(Url.make), variants)) {
      | exception _ => Error()
      | Some((Some(url), Some(variants))) if url->Url.protocol === "https:" =>
        Ok({RequestSheetLabelsType.url, invalidBarcodesVariants: variants})
      | Some(_) | None => Error()
      }

    let make = bodyJson =>
      Request.make(endpoint, ~bodyJson, ~method=#POST, ~skipMalformedOkResult=true)
  }

  module CombineRequests = {
    let make = (
      ~requestBarcodeCompletion,
      ~requestLabelsGenerating,
      ~productsValues,
      ~priceId,
      ~missingProductBarcodeGenerated,
      ~productBarcodeDisplayed,
      ~priceDisplayed,
      ~producerDisplayed,
      ~alcoholVolumeDisplayed,
      ~productCode,
      ~labelFormat,
      ~printOffset,
      ~borderEnabled,
      ~sort,
      ~shopId,
    ) =>
      switch LabelsRequest.encodeBodyJson(
        ~productsValues,
        ~priceId,
        ~productBarcodeDisplayed,
        ~priceDisplayed,
        ~producerDisplayed,
        ~alcoholVolumeDisplayed,
        ~productCode,
        ~labelFormat,
        ~printOffset,
        ~borderEnabled,
        ~sort,
      ) {
      | Ok(variantLabelBodyJson) =>
        let preRequestBarcodeCompletion = if missingProductBarcodeGenerated {
          let barcodeCompletionBodyJson = BarcodeCompletionRequest.encodeBodyJson(
            ~productsValues,
            ~shopId,
          )
          switch barcodeCompletionBodyJson {
          | Ok(bodyJson) =>
            requestBarcodeCompletion(bodyJson)->Future.mapOk(_ => ())->Future.mapError(_ => ())
          | Error(_) => Future.value(Error())
          }
        } else {
          Future.value(Ok())
        }

        preRequestBarcodeCompletion
        ->Future.flatMapOk(_ =>
          requestLabelsGenerating(variantLabelBodyJson)->Future.map(result =>
            switch result {
            | Ok(json) => LabelsRequest.decodeBodyJson(json)
            | _ => Error()
            }
          )
        )
        ->Future.mapOk(result => Some(result))
        ->Future.mapError(() =>
          t("An unexpected error occured. Please try again or contact the support.")
        )
      | Error(message) => Future.value(Error(message))
      }
  }
}

module Print = {
  module LabelFormat = {
    type t = Label31x22 | Label57x19

    let toString = value =>
      switch value {
      | Label31x22 => "Label31x22"
      | Label57x19 => "Label57x19"
      }
    let fromString = value =>
      switch value {
      | "Label31x22" => Ok(Label31x22)
      | "Label57x19" => Ok(Label57x19)
      | _ => Error()
      }
    let toFormatLabel = value =>
      switch value {
      | Label31x22 => "3,10 x 2,20 cm"
      | Label57x19 => "5,70 x 1,90 cm"
      }
    let toLabel = value =>
      template(
        t("Unit label (format {{format}})"),
        ~values={
          "format": toFormatLabel(value),
        },
        (),
      )
  }

  let printersHubName = "StarPrintersHub"

  module DefaultPrinterRequest = {
    type requestError =
      | NoPrinterFound
      | UnexpectedFailure

    let endpoint = (~shopId) =>
      Env.gatewayUrl() ++ "/printers-hub/" ++ printersHubName ++ "/" ++ shopId ++ "/default-printer"

    let decodeInvalidRequestFailure = error =>
      switch error {
      | {Request.kind: "NotFoundShopDefaultPrinterConfig"} => NoPrinterFound
      | _ => UnexpectedFailure
      }

    let futureMapResult = futureResult =>
      futureResult
      ->Future.mapOk(_ => ())
      ->Future.mapError(error =>
        switch error {
        | Request.InvalidRequestFailures(invalidRequestFailures) =>
          invalidRequestFailures[0]
          ->Option.map(decodeInvalidRequestFailure)
          ->Option.getWithDefault(UnexpectedFailure)
        | _ => UnexpectedFailure
        }
      )

    let make = (~shopId) => Request.make(endpoint(~shopId), ~method=#GET)->futureMapResult
  }

  module LabelsRequest = {
    type serverFailure =
      | PriceUnknown
      | Unknown

    let endpoint = (~shopId) =>
      Env.gatewayUrl() ++ `/printers-hub/${printersHubName}/${shopId}/print-variants-labels`

    let encodeBodyJson = (
      ~priceId,
      ~productCode,
      ~productBarcodeDisplayed,
      ~priceDisplayed,
      ~producerDisplayed,
      ~alcoholVolumeDisplayed,
      ~labelFormat,
      ~productsValues,
    ) =>
      switch productsValues {
      | RequestProductType.Picked([]) =>
        Error(t("Products list empty, please select at least one product before continuing"))
      | _ =>
        let dict = Js.Dict.empty()

        dict->Js.Dict.set("priceId", priceId->Uuid.toString->Json.encodeString)
        dict->Js.Dict.set("barcodeEnabled", productBarcodeDisplayed->Json.encodeBoolean)
        dict->Js.Dict.set("priceEnabled", priceDisplayed->Json.encodeBoolean)
        dict->Js.Dict.set("producerEnabled", producerDisplayed->Json.encodeBoolean)
        dict->Js.Dict.set("alcoholVolumeEnabled", alcoholVolumeDisplayed->Json.encodeBoolean)

        open ProductCode
        switch productCode {
        | Hidden => ()
        | SKU => dict->Js.Dict.set("labelCodeKind", "SKU"->Json.encodeString)
        | InternalCode => dict->Js.Dict.set("labelCodeKind", "INTERNAL_CODE"->Json.encodeString)
        }

        open LabelFormat
        switch labelFormat {
        | Label31x22 => dict->Js.Dict.set("labelFormat", "W31_X_H22"->Json.encodeString)
        | Label57x19 => () // NOTE - not supported yet
        }

        switch productsValues {
        | Picked(products) =>
          let items = products->Array.map(({variantId, repetitions}) => {
            Js.Dict.fromArray([
              ("variantId", Json.encodeString(variantId)),
              ("repetitions", Json.encodeNumber(repetitions->Int.toFloat)),
            ])->Js.Json.object_
          })
          dict->Js.Dict.set("items", items->Json.encodeArray)
        | All => ()
        }

        Ok(dict->Json.encodeDict)
      }

    let decodeInvalidRequestFailure = serverFailure =>
      switch serverFailure.Request.kind {
      | "NotFoundPriceForVariant"
      | "InvalidVariantLabelPrice" =>
        PriceUnknown
      | _ => Unknown
      }
    let parseAndDecodeError = string =>
      switch string->Json.parseExn {
      | exception _ => Some(Unknown)
      | json =>
        switch json->Request.decodeResult {
        | Error(Some(serverFailures)) => serverFailures[0]->Option.map(decodeInvalidRequestFailure)
        | _ => None
        }
      }

    // TODO - backend: response should be json only to avoid unsafe parsing and specific logic
    let make = (bodyJson, shopId) => {
      switch Auth.getJwt() {
      | Some(jwt) =>
        Fetch.make(
          endpoint(~shopId),
          {
            method: #POST,
            body: bodyJson->Json.stringify->Fetch.Body.string,
            headers: Fetch.Headers.fromObject({
              "Content-Type": "application/json",
              "Authorization": `Bearer ${jwt}`,
            }),
          },
        )
        ->Promise.then(Fetch.Response.text)
        ->FuturePromise.fromPromise
        ->Future.map(result =>
          switch result {
          | Ok("OK") => Ok()
          | Ok(errorTextResponse) =>
            Error(parseAndDecodeError(errorTextResponse)->Option.getWithDefault(Unknown))
          | Error(_) => Error(Unknown)
          }
        )
      | None => Future.value(Error(Unknown))
      }
    }
  }

  module CombineRequests = {
    let make = (
      ~requestBarcodeCompletion,
      ~requestLabelsPrinting,
      ~priceId,
      ~priceName,
      ~productCode,
      ~productBarcodeDisplayed,
      ~productsValues,
      ~labelFormat,
      ~missingProductBarcodeGenerated,
      ~priceDisplayed,
      ~producerDisplayed,
      ~alcoholVolumeDisplayed,
      ~shopId,
    ) =>
      switch LabelsRequest.encodeBodyJson(
        ~productsValues,
        ~priceId,
        ~productCode,
        ~priceDisplayed,
        ~productBarcodeDisplayed,
        ~producerDisplayed,
        ~alcoholVolumeDisplayed,
        ~labelFormat,
      ) {
      | Ok(bodyJson) =>
        let preRequestBarcodeCompletion = if missingProductBarcodeGenerated {
          let barcodeCompletionBodyJson = BarcodeCompletionRequest.encodeBodyJson(
            ~productsValues,
            ~shopId,
          )
          switch barcodeCompletionBodyJson {
          | Ok(bodyJson) =>
            requestBarcodeCompletion(bodyJson)->Future.mapOk(_ => ())->Future.mapError(_ => ())
          | Error(_) => Future.value(Error())
          }
        } else {
          Future.value(Ok())
        }

        preRequestBarcodeCompletion
        ->Future.mapError(() => LabelsRequest.Unknown)
        ->Future.flatMapOk(_ => requestLabelsPrinting(bodyJson, shopId))
        ->Future.mapError(serverFailure =>
          switch serverFailure {
          | PriceUnknown =>
            template(
              t(
                "The label printing has failed: please enter the retail price for the price list set up for printing ({{priceName}}).",
              ),
              ~values={"priceName": priceName},
              (),
            )
          | Unknown => t("An unexpected error occured. Please try again or contact the support.")
          }
        )
      | Error(message) => Future.value(Error(message))
      }
  }
}

module StateSettingsUserPreferences = {
  module GlobalLabelFormat = {
    type t<'a, 'b> = LabelSheet('a) | LabelPrint('b)

    let toString = polyValue =>
      switch polyValue {
      | LabelSheet(value) => Sheet.LabelFormat.toString(value)
      | LabelPrint(value) => Print.LabelFormat.toString(value)
      }
    let fromString = value =>
      switch (Sheet.LabelFormat.fromString(value), Print.LabelFormat.fromString(value)) {
      | (Ok(sheetFormat), _) => LabelSheet(Ok(sheetFormat))
      | (_, Ok(printFormat)) => LabelPrint(Ok(printFormat))
      | (Error(), _) => LabelPrint(Error())
      }
  }

  type t = {
    priceId: Uuid.t,
    priceName: string,
    productBarcodeDisplayed: bool,
    missingProductBarcodeGenerated: bool,
    priceDisplayed: bool,
    producerDisplayed: bool,
    alcoholVolumeDisplayed: bool,
    productCode: ProductCode.t,
    labelFormat: GlobalLabelFormat.t<Sheet.LabelFormat.t, Print.LabelFormat.t>,
    printOffset: int,
    borderEnabled: bool,
    sort: Sheet.LabelSort.t,
  }

  let jsonCodec = JsonCodec.object12(
    ({
      priceId,
      priceName,
      productBarcodeDisplayed,
      missingProductBarcodeGenerated,
      priceDisplayed,
      producerDisplayed,
      alcoholVolumeDisplayed,
      productCode,
      labelFormat,
      printOffset,
      borderEnabled,
      sort,
    }) => (
      priceId->Uuid.toString,
      priceName,
      productBarcodeDisplayed,
      missingProductBarcodeGenerated,
      priceDisplayed,
      producerDisplayed,
      alcoholVolumeDisplayed,
      productCode->ProductCode.toString,
      labelFormat->GlobalLabelFormat.toString,
      printOffset,
      borderEnabled,
      sort->Sheet.LabelSort.toString,
    ),
    ((
      priceId,
      priceName,
      productBarcodeDisplayed,
      missingProductBarcodeGenerated,
      priceDisplayed,
      producerDisplayed,
      alcoholVolumeDisplayed,
      productCode,
      labelFormat,
      printOffset,
      borderEnabled,
      sort,
    )) =>
      switch (
        priceId->Uuid.fromString,
        productCode->ProductCode.fromString,
        labelFormat->GlobalLabelFormat.fromString,
        sort->Sheet.LabelSort.fromString,
      ) {
      | (Some(priceId), Ok(productCode), LabelSheet(Ok(labelFormat)), Ok(sort)) =>
        Ok({
          priceId,
          priceName,
          productBarcodeDisplayed,
          priceDisplayed,
          missingProductBarcodeGenerated,
          producerDisplayed,
          alcoholVolumeDisplayed,
          productCode,
          labelFormat: LabelSheet(labelFormat),
          printOffset,
          borderEnabled,
          sort,
        })
      | (Some(priceId), Ok(productCode), LabelPrint(Ok(labelFormat)), Ok(sort)) =>
        Ok({
          priceId,
          priceName,
          productBarcodeDisplayed,
          missingProductBarcodeGenerated,
          priceDisplayed,
          producerDisplayed,
          alcoholVolumeDisplayed,
          productCode,
          labelFormat: LabelPrint(labelFormat),
          printOffset,
          borderEnabled,
          sort,
        })
      | _ => Error(#SyntaxError("Could not decode"))
      },
    JsonCodec.field("priceId", JsonCodec.string),
    JsonCodec.field("priceName", JsonCodec.string),
    JsonCodec.field("productBarcodeDisplayed", JsonCodec.bool),
    JsonCodec.field("missingProductBarcodeGenerated", JsonCodec.bool),
    JsonCodec.field("priceDisplayed", JsonCodec.bool),
    JsonCodec.field("producerDisplayed", JsonCodec.bool),
    JsonCodec.field("alcoholVolumeDisplayed", JsonCodec.bool),
    JsonCodec.field("productCode", JsonCodec.string),
    JsonCodec.field("labelFormat", JsonCodec.string),
    JsonCodec.field("printOffset", JsonCodec.int),
    JsonCodec.field("borderEnabled", JsonCodec.bool),
    JsonCodec.field("sort", JsonCodec.string),
  )

  let encoder = state => state->JsonCodec.encodeWith(jsonCodec)
  let decoder = json => json->JsonCodec.decodeWith(jsonCodec)

  let useRead = () => UserPreferences.useRead(~key=#"catalog-labels-options", ~decoder)
}
