open Intl

module Mutations = CatalogVariantEditFormMutations

@react.component
let make = (~editionMode: bool, ~productValues: option<CatalogProductEditForm.Lenses.state>) => {
  let updateVariant = Mutations.useUpdateVariant()
  let createVariant = Mutations.useCreateVariant()
  let createProductAndVariant = Mutations.useCreateProductAndVariant(~productValues)

  let onSubmit = switch (editionMode, productValues) {
  | (true, _) => updateVariant
  | (false, Some(_)) => createProductAndVariant
  | _ => createVariant
  }

  <Inline space=#small>
    <CatalogVariantEditForm.SubmitButton
      text={t("Save")} variation=#success size=#medium onSubmit
    />
  </Inline>
}

let make = React.memo(make)
