open Intl
open CatalogDuplication

type categoryData = {
  id: option<string>,
  name: string,
  shopId: string,
}

module Row = {
  type t = {
    id: option<Destination.t>,
    shopId: string,
    shopName: string,
    categoryName: option<string>,
    note: option<string>,
  }
  let keyExtractor = row => row.shopId
}

let tableColumns = (
  ~everyShopsDuplicable: bool,
  ~hideCategory: bool,
  ~onRequestCategoryUpdate: categoryData => unit,
) => [
  {
    Table.key: "shop-catalog",
    name: t("Shop catalog"),
    render: ({data: {Row.shopName: shopName}, disabled}) =>
      <TextStyle variation={disabled ? #subdued : #neutral}> {shopName->React.string} </TextStyle>,
  },
  {
    key: "product-category",
    name: t("Category"),
    layout: {hidden: hideCategory},
    render: ({data: {categoryName, shopId}, disabled}) => {
      let onChange = (category: CatalogCategoryPicker.categoryChange) =>
        switch category {
        | CommonCategory({id, formattedName}) =>
          onRequestCategoryUpdate({
            id: Some(id),
            name: formattedName,
            shopId,
          })
        | NotClassifiedCategory =>
          onRequestCategoryUpdate({
            id: None,
            name: t("Not classified"),
            shopId,
          })
        }

      if !disabled {
        <CatalogCategoryPicker
          variation={categoryName->Option.isSome ? #important : #neutral}
          value={categoryName->Option.getWithDefault(t("Add a category"))}
          shopId
          onChange
        />
      } else {
        <TextStyle variation=#subdued>
          {categoryName->Option.getWithDefault("—")->React.string}
        </TextStyle>
      }
    },
  },
  {
    key: "more-information",
    name: t("Additional information"),
    layout: {hidden: everyShopsDuplicable},
    render: ({data: {note}, disabled}) =>
      switch note {
      | Some(note) =>
        <TextStyle variation={disabled ? #subdued : #neutral}> {note->React.string} </TextStyle>
      | _ => React.null
      },
  },
]

let make = TableView.make
