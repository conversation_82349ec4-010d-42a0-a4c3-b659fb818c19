module CreateVariantFromProduct = {
  type t = CatalogProductEditForm.Lenses.state

  module Codecs = {
    open! CatalogProduct

    module Tax = {
      open CatalogProductEditForm.Tax
      let encoder = ({id, value}) => (id->Uuid.toString, value)
      let decoder = ((id, value)) =>
        switch id->Uuid.fromString {
        | Some(uuid) => Ok({id: uuid, value})
        | _ => Error(#SyntaxError("Invalid tax uuid"))
        }

      let value = JsonCodec.object2(
        encoder,
        decoder,
        JsonCodec.field("id", JsonCodec.string),
        JsonCodec.field("value", JsonCodec.float),
      )
    }

    let encoder = (state: t) => (
      state.name,
      state.tax,
      state.categoryId->Option.map(Uuid.toString),
      state.kind->Kind.toString,
      state.color->Option.map(Color.toString),
      state.producer,
      state.family,
      state.designation,
      state.country,
      state.region,
      state.beerType,
      state.wineType->Option.map(WineType.toString),
      state.whiteWineType->Option.map(WhiteWineType.toString),
    )
    let decoder = ((
      name,
      tax,
      categoryId,
      kind,
      color,
      producer,
      family,
      designation,
      country,
      region,
      beerType,
      wineType,
      whiteWineType,
    )) => {
      let getValueAndError = value =>
        switch value {
        | Some(Ok(value)) => (Some(value), None)
        | Some(Error(error)) => (None, Some(error))
        | None => (None, None)
        }

      let categoryId = categoryId->Option.map(id =>
        switch id->Uuid.fromString {
        | Some(uuid) => Ok(uuid)
        | _ => Error("Invalid category uuid")
        }
      )

      switch (
        kind->Kind.fromString,
        categoryId->getValueAndError,
        color->Option.map(Color.fromString)->getValueAndError,
        wineType->Option.map(WineType.fromString)->getValueAndError,
        whiteWineType->Option.map(WhiteWineType.fromString)->getValueAndError,
      ) {
      | (Error(error), _, _, _, _)
      | (_, (_, Some(error)), _, _, _)
      | (_, _, (_, Some(error)), _, _)
      | (_, _, _, (_, Some(error)), _)
      | (_, _, _, _, (_, Some(error))) =>
        Error(#SyntaxError(error))
      | (Ok(kind), (categoryId, None), (color, None), (wineType, None), (whiteWineType, None)) =>
        Ok({
          CatalogProductEditForm.Lenses.name,
          tax,
          categoryId,
          kind,
          color,
          producer,
          family,
          designation,
          country,
          region,
          beerType,
          wineType,
          whiteWineType,
        })
      }
    }

    let value = JsonCodec.object13(
      encoder,
      decoder,
      JsonCodec.field("name", JsonCodec.string),
      JsonCodec.field("tax", Tax.value)->JsonCodec.optional,
      JsonCodec.field("categoryId", JsonCodec.string)->JsonCodec.optional,
      JsonCodec.field("kind", JsonCodec.string),
      JsonCodec.field("color", JsonCodec.string)->JsonCodec.optional,
      JsonCodec.field("producer", JsonCodec.string),
      JsonCodec.field("family", JsonCodec.string),
      JsonCodec.field("designation", JsonCodec.string),
      JsonCodec.field("country", JsonCodec.string),
      JsonCodec.field("region", JsonCodec.string),
      JsonCodec.field("beerType", JsonCodec.string),
      JsonCodec.field("wineType", JsonCodec.string)->JsonCodec.optional,
      JsonCodec.field("whiteWineType", JsonCodec.string)->JsonCodec.optional,
    )
  }

  let encode = state => state->JsonCodec.encodeWith(Codecs.value)

  let decode = historyState =>
    historyState->Option.map(state => state->JsonCodec.decodeWith(Codecs.value))
}
