open Intl

module InputVariantCapacityValueSelect = {
  @react.component
  let make = (~productKind) => {
    let {capacityUnit} = CatalogVariantEditForm.useFormState().values

    let inputPlaceholder = productKind->CatalogVariant.CapacityValue.placeholderFromProductKind
    let inputPrecision = {
      open CatalogVariant.CapacityUnit
      let unit = capacityUnit->Option.getWithDefault("custom")
      if unit === kg || unit === l {
        2
      } else if unit === cl {
        1
      } else if unit === ml || unit === g {
        0
      } else {
        3
      }
    }

    <CatalogVariantEditForm.InputOptionalNumber
      field=CapacityValue
      label={t("Capacity")}
      placeholder=inputPlaceholder
      precision=inputPrecision
      minValue=0.
    />
  }
}

module InputVariantCapacityUnitSelect = {
  @react.component
  let make = (~productKind, ~disabled) => {
    let {bulk, capacityUnit} = CatalogVariantEditForm.useFormState().values
    let dispatch = CatalogVariantEditForm.useFormDispatch()

    let options = CatalogVariant.CapacityUnit.makeOptions(~productKind)

    React.useEffect1(() => {
      if bulk && capacityUnit->Option.isNone {
        FieldValueChanged(CapacityUnit, _ => options[0])->dispatch
      }
      None
    }, [bulk])

    let tooltip = switch (bulk, disabled) {
    | (true, true) => Some(<Tooltip.Span text={t("The capacity unit cannot be changed.")} />)
    | (true, false) =>
      Some(
        <Tooltip.Span
          text={t(
            "Once the product has been created in bulk, the capacity unit can't be modified.",
          )}
        />,
      )
    | _ => None
    }

    let defaultItem = {
      Select.key: "default",
      label: template(t("Piece"), ()),
      value: None,
    }
    let items = options->Array.map(unit => {
      Select.key: unit,
      label: unit,
      value: Some(unit),
    })
    let sections = [
      {
        Select.items: Array.concatMany(
          if !bulk {
            [items, [defaultItem]]
          } else {
            [items]
          },
        ),
      },
    ]

    <CatalogVariantEditForm.InputSelect
      field=CapacityUnit
      label={t("Capacity unit")}
      placeholder={t("Select an unit")}
      searchable=false
      disabled
      ?tooltip
      sections
    />
  }
}

module InputVariantYear = {
  @react.component
  let make = (~productKind: CatalogProduct.Kind.t) =>
    <CatalogVariantEditForm.InputOptionalNumber
      field=Year
      label={t("Vintage")}
      placeholder={productKind->CatalogVariant.Year.placeholderFromProductKind}
      minValue=0.
      maxValue={Js.Date.make()->Js.Date.getFullYear}
      precision=0
      useGrouping=false
      hideStepper=true
    />
}

module InputVariantAlcoholVolume = {
  @react.component
  let make = (~productKind: CatalogProduct.Kind.t) =>
    <CatalogVariantEditForm.InputOptionalNumber
      field=AlcoholVolume
      label={t("Alcohol volume")}
      appender=InputNumberField.Percent
      placeholder={productKind->CatalogVariant.Volume.placeholderFromProductKind}
      minValue=0.
      maxValue=100.
    />
}

module InputVariantPriceLookup = {
  @react.component
  let make = () => {
    let {validation, values} = CatalogVariantEditForm.useFormState()
    let dispatch = CatalogVariantEditForm.useFormDispatch()

    let priceLookUpCodeError = switch validation {
    | Ok() => None
    | Error(errors) =>
      open CatalogVariantEditForm
      errors
      ->Array.keepMap(((field, error)) =>
        switch (field, error) {
        | (Schema.Field(Lenses.PriceLookUpCode), error) => Some(error)
        | _ => None
        }
      )
      ->Array.get(0)
    }

    let onAlreadyConsummedValuesFetched = values =>
      FieldValueChanged(
        PriceLookUpCode,
        previous => {
          ...previous,
          invalidValues: values,
        },
      )->dispatch

    let onChange = value =>
      FieldValueChanged(
        PriceLookUpCode,
        previous => {
          ...previous,
          value,
        },
      )->dispatch

    <CatalogPriceLookUpInput
      value=values.priceLookUpCode.value
      errorMessage=priceLookUpCodeError
      onAlreadyConsummedValuesFetched
      onChange
    />
  }
}
