open Intl

@react.component
let make = () => {
  let {capacityUnit, bulk} = CatalogVariantEditForm.useFormState().values

  <FieldsetLayoutPanel
    title={t("Stock")}
    description={t(
      "Indicate the initial stock quantity of the reference or leave the stock at zero and create an order for the reception of the product.",
    )}>
    <CatalogVariantEditForm.InputNumber
      field=InitialStockQuantity
      label={t("Initial stock")}
      appender=?{switch (capacityUnit, bulk) {
      | (Some(unit), true) => Some(InputNumberField.Custom(unit))
      | _ => None
      }}
      minValue=0.
      precision={bulk ? CatalogVariant.defaultBulkCapacityPrecision : 0}
    />
    <CatalogVariantEditForm.InputTextArea field=InitialStockComment label={t("Comment")} />
    // TODO - Bulk mode is not handled yet in Gateway
    {if !bulk {
      <StackFields>
        <CatalogVariantEditForm.InputNumber
          field=MaxStockThreshold
          label={t("Maximum stock")}
          hideRequired=true
          minValue=0.
          precision=0
        />
        <CatalogVariantEditForm.InputNumber
          field=MinStockThreshold
          label={t("Minimum stock")}
          hideRequired=true
          minValue=0.
          precision=0
        />
        <CatalogVariantEditForm.InputNumber
          field=StockOrderTriggerThreshold
          label={t("Stock triggering the order")}
          hideRequired=true
          minValue=0.
          precision=0
        />
      </StackFields>
    } else {
      React.null
    }}
  </FieldsetLayoutPanel>
}

let make = React.memo(make)
