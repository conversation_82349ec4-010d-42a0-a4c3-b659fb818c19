open Intl

let edgesPerFetch = 5

module StockActivitiesQuery = %graphql(`
  query stockActivitiesByVariantCku($cku: CKU!, $first: Int, $filterBy: InputStockActivitiesQueryFilter) {
    stockActivitiesByVariantCku(cku: $cku, first: $first, filterBy: $filterBy) {
      edges {
        node {
          id
          kind @ppxOmitFutureValue
          reason @ppxOmitFutureValue
          comment
          shop { name }
          device { name slug }
          createdAt
          quantity
          variant {
            id
            cku
            bulk
            capacityPrecision
            capacityUnit
            product {
              id
              color @ppxOmitFutureValue
            }
          }
        }
      }
      pageInfo {
        hasNextPage
      }
    }
  }
`)

module Row = {
  type t = {
    id: string,
    formattedKind: string,
    kind: StockActivityKind.t,
    reason: option<StockActivityReason.t>,
    quantity: int,
    variantCapacityPrecision: option<int>,
    variantCapacityUnit: option<string>,
    description: option<string>,
    date: Js.Date.t,
    shopName: string,
    deviceName: string,
  }

  let keyExtractor = row => row.id ++ "-activity"
}

let columns = [
  {
    Table.key: "activity-type",
    name: t("Type"),
    layout: {minWidth: 130.->#px, width: 1.->#fr},
    render: ({data: {Row.formattedKind: formattedKind, reason}}) =>
      <StockActivityTypeTableCell value=formattedKind reason />,
  },
  {
    key: "activity-quantity",
    name: t("Quantity"),
    layout: {minWidth: 70.->#px, width: 1.->#fr, alignX: #center},
    render: ({data: {quantity, variantCapacityPrecision, variantCapacityUnit, kind}}) =>
      <StockActivityQuantityTableCell
        value=quantity
        capacityPrecision=?variantCapacityPrecision
        capacityUnit=?variantCapacityUnit
        kind
      />,
  },
  {
    key: "activity-description",
    name: t("Comment"),
    layout: {minWidth: 130.->#px, width: 1.5->#fr, margin: #normal},
    render: ({data: {description}}) =>
      <TextStyle> {description->Option.getWithDefault("—")->React.string} </TextStyle>,
  },
  {
    key: "activity-datetime",
    name: t("Datetime"),
    layout: {minWidth: 110.->#px},
    render: ({data: {date}}) => <TableDatetimeCell value=date />,
  },
  {
    key: "activity-source",
    name: t("Source"),
    layout: {minWidth: 150.->#px},
    render: ({data: {deviceName, shopName}}) =>
      <StockActivitySourceTableCell deviceName shopName />,
  },
]

@react.component
let make = (~cku) => {
  let (executeQuery, queryResults) = StockActivitiesQuery.useLazy(~fetchPolicy=CacheAndNetwork, ())
  let activeShop = Auth.useActiveShop()
  let activeShopId = activeShop->Option.map(shop => shop.id)
  let {pathname} = Navigation.useUrl()

  React.useEffect1(() => {
    executeQuery(
      StockActivitiesQuery.makeVariables(
        ~cku=cku->Scalar.CKU.serialize,
        ~first=edgesPerFetch,
        ~filterBy=StockActivitiesQuery.makeInputObjectInputStockActivitiesQueryFilter(
          ~shopIds=?switch activeShopId {
          | Some(shopId) => Some(StockActivitiesQuery.makeInputObjectInFilter(~_in=[shopId], ()))
          | _ => None
          },
          (),
        ),
        (),
      ),
    )
    None
  }, [activeShopId])

  <Card variation=#table title={t("Stock activities")}>
    {switch queryResults {
    | Executed({data: Some({stockActivitiesByVariantCku: {edges, pageInfo: {hasNextPage}}})}) =>
      let rows = edges->Array.keepMap(({node}) =>
        switch node.variant {
        | Some(variant) =>
          Some({
            Row.id: node.id,
            kind: node.kind,
            formattedKind: node.kind->StockActivityKind.toLabel,
            reason: node.reason,
            description: node.comment,
            shopName: node.shop.name,
            deviceName: DeviceName.decode(
              ~slug=node.device.slug,
              ~name=node.device.name,
            )->DeviceName.format,
            date: node.createdAt,
            quantity: node.quantity,
            variantCapacityUnit: switch (variant.bulk, variant.capacityUnit) {
            | (Some(true), Some(unit)) => Some(unit)
            | _ => None
            },
            variantCapacityPrecision: switch (variant.bulk, variant.capacityPrecision) {
            | (Some(true), Some(precision)) => Some(precision)
            | _ => None
            },
          })
        | _ => None
        }
      )

      let placeholderEmptyState =
        <Placeholder
          status={NoDataAvailable} customText={t("No stock activity has been yet recorded")}
        />

      let data = AsyncData.Done(Ok(rows))

      <>
        <TableView columns data keyExtractor=Row.keyExtractor placeholderEmptyState />
        {if hasNextPage === Some(true) {
          let pathname = pathname ++ "/stockactivities"
          <ShowAllDataLink to=Route(pathname) text={t("Show all data")} />
        } else {
          React.null
        }}
      </>
    | Unexecuted(_)
    | Executed({loading: true}) =>
      <Placeholder status=Loading />
    | _ => <Placeholder status=Error />
    }}
  </Card>
}

let make = React.memo(make)
