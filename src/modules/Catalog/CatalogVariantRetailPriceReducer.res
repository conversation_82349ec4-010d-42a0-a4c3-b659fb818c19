open CatalogVariantRetailPrice

exception CatalogVariantRetailPriceReducer_CouldNotUpdatePrice

type state = array<variantPrice>

type variant = {
  id: option<string>,
  variantId: string,
  priceId: string,
}

type priceExcludingTax = float
type action =
  | RetailPriceUpdated(variant, priceExcludingTax)
  | RetailPricesPurchaseValueUpdated(state)
  | Reset(state)

let make = (state, action) =>
  switch action {
  | Reset(newState) => newState
  | RetailPricesPurchaseValueUpdated(newState) =>
    state->Array.map((retailPrice: variantPrice) => {
      {
        ...retailPrice,
        purchasePrice: switch newState->Array.getBy(current =>
          current.id === retailPrice.id &&
          current.priceId === retailPrice.priceId &&
          current.variantId === retailPrice.variantId
        ) {
        | Some({purchasePrice}) => purchasePrice
        | _ => retailPrice.purchasePrice
        },
      }
    })
  | RetailPriceUpdated(variant, nextValueExcludingTax) =>
    state->Array.map(retailPrice =>
      if (
        retailPrice.id === variant.id &&
        retailPrice.priceId === variant.priceId &&
        retailPrice.variantId === variant.variantId
      ) {
        {
          ...retailPrice,
          valueExcludingTax: nextValueExcludingTax,
          valueIncludingTax: nextValueExcludingTax->PriceCalculator.Retail.Tax.add(
            ~taxRate=retailPrice.taxRate,
          ),
          edited: true,
        }
      } else {
        retailPrice
      }
    )
  }
