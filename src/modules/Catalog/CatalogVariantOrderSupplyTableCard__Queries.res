let edgesPerFetch = 5

module OrderSuppliesQuery = %graphql(`
    query OrderSuppliesQuery($cku: CKU!, $first: Int, $filterBy: InputOrderProductsByVariantCkuQueryFilter) {
      orderProductsByVariantCku(cku: $cku, first: $first, filterBy: $filterBy) {
        edges {
          node {
            quantity
            totalAmountIncludingTaxes
            totalAmountExcludingTaxes
            order {
              shopName
              id
              name
              receptionFinishedAt
              supplierCompanyName
              supplier {
                id
              }
            }
          }
        }
        pageInfo {
          hasNextPage
        }
      }
    }
  `)
