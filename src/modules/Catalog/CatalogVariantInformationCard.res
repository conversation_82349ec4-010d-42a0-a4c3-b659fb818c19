open Intl

module Config = CatalogVariant__Config

module InfoTextLine = {
  @react.component
  let make = React.memo((~children, ~label) =>
    switch children {
    | Some(children) =>
      <InlineText>
        <TextStyle variation=#normal> {label->React.string} </TextStyle>
        <TextStyle> {" "->React.string} </TextStyle>
        <TextStyle> {children->React.string} </TextStyle>
      </InlineText>
    | _ => React.null
    }
  )
}

module Content = {
  @react.component
  let make = React.memo((
    ~variant: option<Config.variantInformation>=?,
    ~displayShopName=true,
    ~displayVariantId=false,
  ) =>
    switch variant {
    | Some(variant) =>
      <Stack space=#medium>
        <Stack>
          {if displayShopName {
            <Box spaceBottom=#xsmall>
              <TextStyle variation=#normal size=#xxsmall>
                {(t("From catalog of") ++ " " ++ variant.shopName)->React.string}
              </TextStyle>
            </Box>
          } else {
            React.null
          }}
          <TextStyle weight=#strong> {variant.name->React.string} </TextStyle>
          <InfoTextLine label={t("Capacity of")}> variant.formattedCapacity </InfoTextLine>
          <InfoTextLine label={t("Vintage")}>
            {variant.year->Option.map(year =>
              switch year->Int.fromString {
              | Some(value) if value == 1 => year ++ " " ++ t("year")
              | Some(value) if value < 4 => year ++ " " ++ t(isPlural(value) ? "years" : "year")
              | _ => year
              }
            )}
          </InfoTextLine>
          <InfoTextLine label={t("Alcohol volume of")}>
            variant.formattedAlcoholVolume
          </InfoTextLine>
        </Stack>
        <Stack>
          {switch variant {
          | {ean13: Some(_)}
          | {stockKeepingUnit: Some(_)}
          | {priceLookUpCode: Some(_)}
          | {internalCode: Some(_)} =>
            <>
              <InfoTextLine label={t("Bar code")}> variant.ean13 </InfoTextLine>
              <InfoTextLine label="SKU"> variant.stockKeepingUnit </InfoTextLine>
              <InfoTextLine label="PLU">
                {variant.priceLookUpCode->Option.map(Int.toString)}
              </InfoTextLine>
              <InfoTextLine label={t("Internal code")}> variant.internalCode </InfoTextLine>
            </>
          | _ => React.null
          }}
          {switch (variant.id, displayVariantId) {
          | ("", _)
          | (_, false) => React.null
          | (id, true) => <KbdClipboard label={t("Copy ID")} value=id />
          }}
        </Stack>
      </Stack>
    | _ => React.null
    }
  )
}

module PreviewContent = {
  @react.component
  let make = React.memo((~variant: option<Config.variantInformation>=?) =>
    switch variant {
    | Some(variant) =>
      <Stack space=#normal>
        <TextStyle variation=#normal size=#xxsmall>
          {(t("From catalog of") ++ " " ++ variant.shopName)->React.string}
        </TextStyle>
        <TextStyle weight=#strong> {variant.name->React.string} </TextStyle>
      </Stack>
    | None => React.null
    }
  )
}

module ShowMoreWrapper = {
  @react.component
  let make = React.memo((~children, ~collapsed, ~onPress) => {
    let (ref, hovered) = Hover.use()

    <Stack space=#none>
      children
      <Touchable ref onPress>
        <Inline>
          <Icon name={collapsed ? #arrow_down_light : #arrow_up_light} />
          <Title level=#5 weight=#medium color=?{hovered ? Some(Colors.neutralColor50) : None}>
            {t(collapsed ? "Show more" : "Collapse")->React.string}
          </Title>
        </Inline>
      </Touchable>
    </Stack>
  })
}

@react.component
let make = (~shopsVariant: array<Config.variantInformation>) => {
  let (showMore, setShowMore) = React.useState(_ => false)
  let scope = Auth.useScope()
  let {pathname} = Navigation.useUrl()

  // Wether there are differing variants content between shops
  let variantDiffering = React.useMemo2(
    () =>
      shopsVariant->CatalogVariant.MultiShops.isReferenceDiffering(~scope, ~predicate=(
        a: Config.variantInformation,
        b: Config.variantInformation,
      ) =>
        a.name === b.name &&
        a.formattedCapacity === b.formattedCapacity &&
        a.year === b.year &&
        a.formattedAlcoholVolume === b.formattedAlcoholVolume &&
        a.ean13 === b.ean13 &&
        a.stockKeepingUnit === b.stockKeepingUnit &&
        a.priceLookUpCode === b.priceLookUpCode &&
        a.internalCode === b.internalCode
      ),
    (scope, shopsVariant),
  )

  <Card
    title={t("Variant's information")}
    action={{
      icon: #edit_light,
      title: t("Edit variant"),
      handler: OpenLink(Route(pathname ++ "/edit")),
    }}>
    <Stack space=#large>
      <Content
        variant=?{shopsVariant[0]}
        displayShopName=variantDiffering
        displayVariantId={shopsVariant->Array.length > 1 ? false : true}
      />
      {if variantDiffering {
        <>
          <ShowMoreWrapper collapsed={!showMore} onPress={_ => setShowMore(state => !state)}>
            {if showMore {
              <PreviewContent variant=?{shopsVariant[1]} />
            } else {
              <Stack space=#normal>
                {shopsVariant
                ->Array.slice(~offset=1, ~len=shopsVariant->Array.length)
                ->Array.map(variant =>
                  <Content
                    variant displayVariantId={shopsVariant->Array.length > 1 ? false : true}
                  />
                )
                ->React.array}
              </Stack>
            }}
          </ShowMoreWrapper>
          {if showMore {
            <Box spaceTop=#medium>
              <Banner
                compact=false
                textStatus=Warning(
                  t("Beware, some information differs among the catalogs of the shops."),
                )
              />
            </Box>
          } else {
            React.null
          }}
        </>
      } else {
        React.null
      }}
    </Stack>
  </Card>
}

let make = React.memo(make)
