open Intl

@react.component
let make = (~variantId, ~maxStockThreshold, ~minStockThreshold, ~stockOrderTriggerThreshold) => {
  <CatalogVariantStockThresholdFormContainer
    variantId minStockThreshold maxStockThreshold stockOrderTriggerThreshold>
    <Card title={t("Stock control")}>
      <Box spaceTop=#normal>
        <CatalogVariantStockThresholdFormInputs />
      </Box>
    </Card>
  </CatalogVariantStockThresholdFormContainer>
}

let make = React.memo(make)
