open Intl

module Mutations = CatalogVariantMutations

@react.component
let make = (~id, ~opened, ~onRequestClose) => {
  let url = Navigation.useUrl()
  let navigate = Navigation.useNavigate()
  let (canGoBack, onGoBack) = Navigation.useGoBack()
  let archiveVariant = Mutations.useArchive(~id)

  let fromVariantPage =
    url.pathname !== Catalog->LegacyRouter.routeToPathname &&
      !(url.pathname->Js.String2.includes("/product"))

  <Modal
    title={t("Confirm the archiving of this variant")}
    opened
    commitButtonText={t("Confirm")}
    commitButtonCallback={_ =>
      archiveVariant()->Future.get(result =>
        switch (result, canGoBack, fromVariantPage) {
        | (Ok(_), true, true) if url.pathname !== Catalog->LegacyRouter.routeToPathname =>
          onGoBack()
        | (Ok(_), _, true) if url.pathname === Catalog->LegacyRouter.routeToPathname =>
          navigate(Catalog->LegacyRouter.routeToPathname)
        | _ => ()
        }
      )}
    abortButtonText={t("Cancel")}
    onRequestClose>
    <Box spaceY=#xxlarge spaceX=#xlarge>
      <TextStyle variation=#normal>
        {t(
          "Once the variant is archived, it will only be visible in its product page and the catalog by applying the \"Archive\" filter.",
        )->React.string}
      </TextStyle>
    </Box>
  </Modal>
}

let make = React.memoCustomCompareProps(make, (oldProps, newProps) =>
  oldProps.id === newProps.id && oldProps.opened === newProps.opened
)
