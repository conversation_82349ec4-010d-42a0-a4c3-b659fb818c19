open Intl
open CatalogProduct

module Tax = {
  type t = {
    id: Uuid.t,
    value: float,
  }
}

// NOTE - fields indicated as optional passed with an empty string
// will be considered as undefined (unchanged) during the mutation.
module Lenses = %lenses(
  type state = {
    name: string,
    kind: Kind.t,
    color: option<Color.t>,
    tax: option<Tax.t>,
    categoryId: option<Uuid.t>,
    producer: string, // optional
    family: string, // optional
    designation: string, // optional
    country: string, // optional
    region: string, // optional
    beerType: string, // optional
    wineType: option<WineType.t>,
    whiteWineType: option<WhiteWineType.t>,
  }
)

include Form.Make(Lenses)

let schema = [
  Schema.StringNotEmpty(Name),
  StringNotEmpty(Country),
  Custom(
    Designation,
    ({designation, kind}) =>
      switch (designation, kind) {
      | ("", #WINE) => Error(t("Please fulfill this field."))
      | _ => Ok()
      },
  ),
  Custom(
    Region,
    ({region, kind}) =>
      switch (region, kind) {
      | ("", #WINE) => Error(t("Please fulfill this field."))
      | _ => Ok()
      },
  ),
  Custom(
    Color,
    ({color, kind}) =>
      switch (color, kind) {
      | (Some(#WHITE | #RED | #ROSE | #ORANGE), #WINE)
      | (Some(#WHITE | #RED | #AMBER | #BLOND | #DARK | #BLACK), #BEER)
      | (_, #SIMPLE | #SPIRITUOUS) =>
        Ok()
      | _ => Error(t("Please pick a color."))
      },
  ),
  Custom(
    WineType,
    ({wineType, kind}) =>
      switch (wineType, kind) {
      | (None, #WINE) => Error(t("Please pick a wine type."))
      | _ => Ok()
      },
  ),
  Custom(
    Family,
    ({family, kind}) =>
      switch (family, kind) {
      | ("", #SPIRITUOUS) => Error(t("Please fulfill this field."))
      | _ => Ok()
      },
  ),
  Custom(
    BeerType,
    ({beerType, kind}) =>
      switch (beerType, kind) {
      | ("", #BEER) => Error(t("Please fulfill this field."))
      | _ => Ok()
      },
  ),
  // NOTE - Custom field TaxId value is only virtually optional but it is not tested
  // in schemas because the consumer component is responsible for providing it.
]
