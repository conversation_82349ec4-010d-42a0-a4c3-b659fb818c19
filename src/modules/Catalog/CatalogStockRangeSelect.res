open Intl

type stockRange = {
  min?: float,
  max?: float,
}

@react.component
let make = (~disabled, ~value, ~onChange) => {
  let {ref: popoverTriggerRef, state: popover, ariaProps: popoverAriaProps} = Popover.useTrigger()
  let (_, triggerHovered) = Hover.use(~ref=popoverTriggerRef, ())

  let (focused, setFocused) = React.useState(() => false)
  let props = {
    ReactAria.Focus.Within.onFocusWithinChange: focused => setFocused(_ => focused),
  }
  let {focusWithinProps} = ReactAria.Focus.Within.use(~props)

  let (range, setRange) = React.useState(() => value)
  let debouncedValue = ReactUpdateDebounced.use(range, ~delay=1000)

  ReactUpdateEffect.use1(() => {
    onChange(debouncedValue)
    None
  }, [debouncedValue])

  React.useEffect1(() => {
    if value !== range {
      setRange(_ => value)
    }
    None
  }, [value])

  let handleMinChange = value => {
    setRange(range => {
      let next = switch range {
      | Some(range) => Some({...range, min: ?value})
      | None => Some({min: ?value})
      }
      switch next {
      | Some({min: ?None, max: ?None}) => None
      | _ => next
      }
    })
  }
  let handleMaxChange = value => {
    setRange(range => {
      let next = switch range {
      | Some(range) => Some({...range, max: ?value})
      | None => Some({max: ?value})
      }
      switch next {
      | Some({min: ?None, max: ?None}) => None
      | _ => next
      }
    })
  }
  let handleReset = _ => {
    setRange(_ => None)
    popover.onRequestClose()
  }

  let formattedRange = {
    let toStr = value =>
      value->Intl.decimalFormat(~minimumFractionDigits=0, ~maximumFractionDigits=3)
    switch range {
    | Some({min, max}) => `${min->toStr} — ${max->toStr}`
    | Some({min}) => `≥ ${min->toStr}`
    | Some({max}) => `≤ ${max->toStr}`
    | _ => t("All")
    }
  }
  let rangeMinValue = range->Option.flatMap(({?min}) => min)
  let rangeMaxValue = range->Option.flatMap(({?max}) => max)

  <ReactAria.Spread props=focusWithinProps>
    <div style={ReactDOMStyle.make(~marginRight="1px", ())}>
      <Tooltip
        content={<Tooltip.Span text={t("Please select a shop beforehand.")} />}
        delay=0
        placement=#top
        disabled={!disabled}>
        <Touchable ref=popoverTriggerRef ariaProps=popoverAriaProps.triggerProps disabled>
          <OverlayTriggerView
            label={t("Stock")}
            preset=#filter
            size=#compact
            disabled
            focused
            active=popover.opened
            highlighted={range->Option.isSome}
            hovered=triggerHovered>
            <span> {formattedRange->React.string} </span>
          </OverlayTriggerView>
        </Touchable>
      </Tooltip>
      {if popover.opened {
        <Popover
          triggerRef=popoverTriggerRef
          state=popover
          shouldUpdatePosition=false
          modal=false
          placement=#"bottom start">
          <Popover.Dialog ariaProps=popoverAriaProps.overlayProps>
            <Box spaceX=#large spaceY=#xlarge>
              <div
                style={ReactDOM.Style.make(
                  ~display="flex",
                  ~width="180px",
                  ~columnGap=Spaces.normalPx,
                  (),
                )}>
                <InputOptionalNumberField
                  label={t("Minimum")}
                  placeholder="-∞"
                  precision=3
                  maxValue=?rangeMaxValue
                  value=rangeMinValue
                  onChange=handleMinChange
                />
                <InputOptionalNumberField
                  label={t("Maximum")}
                  placeholder="∞"
                  precision=3
                  minValue=?rangeMinValue
                  value=rangeMaxValue
                  onChange=handleMaxChange
                />
              </div>
              <Box spaceBottom=#medium />
              <Inline>
                <TextIconButton size=#small icon=#close_light onPress=handleReset>
                  {t("Reset")->React.string}
                </TextIconButton>
              </Inline>
            </Box>
          </Popover.Dialog>
        </Popover>
      } else {
        React.null
      }}
    </div>
  </ReactAria.Spread>
}
