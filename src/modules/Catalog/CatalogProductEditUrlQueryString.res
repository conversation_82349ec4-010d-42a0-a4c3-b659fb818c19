open! CatalogProduct

module CreateProduct = {
  type t = {productKind: Kind.t}

  module Codecs = {
    let encoder = ({productKind}) => productKind->Kind.toString
    let decoder = value =>
      switch value->Kind.fromString {
      | Ok(productKind) => Ok({productKind: productKind})
      | Error(message) => Error(#SyntaxError(message))
      }

    let value = JsonCodec.object1(
      encoder,
      decoder,
      JsonCodec.field("productKind", JsonCodec.string),
    )
  }

  let encode = state => state->JsonCodec.encodeWith(Codecs.value)->QueryString.stringify

  let decode = query => query->QueryString.parse->JsonCodec.decodeWith(Codecs.value)
}
