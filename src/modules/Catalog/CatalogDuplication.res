module Mode = {
  type t =
    | Product
    | Variant
}

module Status = {
  type t =
    | Duplicable
    | ExistsAlready
    | MissingProduct
}

module Entity = {
  type t = {
    id: string,
    shopId: option<string>,
  }
}

module Source = {
  type t =
    | Product(Entity.t)
    | Variant(Entity.t)
}

module Destination = {
  type t =
    | Category(Entity.t)
    | Product(Entity.t)
}

module Shop = {
  type t = {
    dataId: string,
    dataName: string,
    duplicability: Status.t,
  }
}
