module ArchiveVariantMutation = %graphql(`
  mutation archiveVariant($id: ID!) {
    archiveVariant(id: $id) {
      id
      formattedStatus
    }
  }
`)

module UnarchiveVariantMutation = %graphql(`
  mutation unarchiveVariant($id: ID!) {
    unarchiveVariant(id: $id) {
      id
      formattedStatus
    }
  }
`)

module ActivateVariantMutation = %graphql(`
  mutation activateVariant($id: ID!) {
    activateVariant(id: $id) {
      id
      active
      formattedStatus
    }
  }
`)

module DeactivateVariantMutation = %graphql(`
  mutation deactivateVariant($id: ID!) {
    deactivateVariant(id: $id) {
      id
      active
      formattedStatus
    }
  }
`)

let useArchive = (~id) => {
  let (mutate, _) = ArchiveVariantMutation.use()

  React.useCallback1(
    () =>
      mutate(ArchiveVariantMutation.makeVariables(~id, ()))
      ->ApolloHelpers.mutationPromiseToFutureResult
      ->Future.mapOk(_ => Some(id)),
    [id],
  )
}

let useUnarchive = (~id) => {
  let (mutate, _) = UnarchiveVariantMutation.use()

  React.useCallback1(
    () =>
      mutate(UnarchiveVariantMutation.makeVariables(~id, ()))
      ->ApolloHelpers.mutationPromiseToFutureResult
      ->Future.mapOk(_ => Some(id)),
    [id],
  )
}

let useActivate = (~id) => {
  let (mutate, _) = ActivateVariantMutation.use()

  React.useCallback1(
    () =>
      mutate(ActivateVariantMutation.makeVariables(~id, ()))
      ->ApolloHelpers.mutationPromiseToFutureResult
      ->Future.mapOk(_ => Some(id)),
    [id],
  )
}

let useDeactivate = (~id) => {
  let (mutate, _) = DeactivateVariantMutation.use()

  React.useCallback1(
    () =>
      mutate(DeactivateVariantMutation.makeVariables(~id, ()))
      ->ApolloHelpers.mutationPromiseToFutureResult
      ->Future.mapOk(_ => Some(id)),
    [id],
  )
}
