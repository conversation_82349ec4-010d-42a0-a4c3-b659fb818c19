open Intl

module Config = CatalogProduct__Config
module Row = {
  type t = {
    productId: string,
    shopId: string,
    shopName: string,
    categoryId: option<string>,
    categoryName: string,
  }
  let keyExtractor = row => row.shopId ++ "-organisation"
}

let columns = [
  {
    Table.key: "shopname",
    name: t("Shop/Warehouse"),
    layout: {minWidth: 150.->#px, margin: #large},
    render: ({data: {Row.shopName: shopName}}) => <TextStyle> {shopName->React.string} </TextStyle>,
  },
  {
    key: "category",
    name: t("Category"),
    layout: {minWidth: 140.->#px},
    render: ({data: {productId, shopId, categoryName}}) =>
      <CatalogCategoryEditCell variation=#important value=categoryName productId shopId />,
  },
]

@react.component
let make = (~shopsProduct) => {
  let rows = shopsProduct->Array.map((product: Config.productInformation) => {
    Row.productId: product.id,
    shopId: product.shopId,
    shopName: product.shopName,
    categoryId: product.categoryId,
    categoryName: product.formattedCategoryName,
  })

  <Card variation=#table title="Organisation">
    <Box spaceTop=#small>
      <TableView
        columns data=AsyncData.Done(Ok(rows)) keyExtractor=Row.keyExtractor hideCard=true
      />
    </Box>
  </Card>
}

let make = React.memo(make)
