open Intl

module UpdateProductCategoryMutation = %graphql(`
    mutation UpdateProductCategory($id: ID!, $input: InputUpdateProductCategory!) {
      updateProductCategory(id: $id, input: $input) {
        id
        variants {
         edges {
           node {
             id
           }
         }
        }
        category {
          id
          formattedName
        }
      }
    }
  `)

@react.component
let make = (~variation=#normal, ~compact=false, ~value, ~productId, ~shopId=?) => {
  let mutate = UpdateProductCategoryMutation.use()->fst
  let (optimisticValue, setOptimisticValue) = React.useState(() => None)
  let (errored, setErrored) = React.useState(() => false)

  // Handles ProductCategory mutation from productId and categoryId
  let onMutateCategoryRequested = React.useCallback0((
    category: CatalogCategoryPicker.categoryChange,
  ) =>
    mutate(
      UpdateProductCategoryMutation.makeVariables(
        ~id=productId,
        ~input=UpdateProductCategoryMutation.makeInputObjectInputUpdateProductCategory(
          ~categoryId=switch category {
          | CommonCategory({id}) => id
          | NotClassifiedCategory => %raw(` null `)
          },
          (),
        ),
        (),
      ),
    )
    ->FuturePromise.fromPromise
    ->Future.get(response =>
      switch response {
      | Ok(Ok(_)) => setErrored(_ => false)
      | _ =>
        setErrored(_ => true)
        setOptimisticValue(_ => None)
      }
    )
  )

  let onChange = (category: CatalogCategoryPicker.categoryChange) => {
    setOptimisticValue(_ =>
      switch category {
      | CommonCategory({formattedName}) => Some(formattedName)
      | NotClassifiedCategory => Some(t("Not classified"))
      }
    )
    onMutateCategoryRequested(category)
  }

  let value = switch optimisticValue {
  | Some(optimisticValue) => optimisticValue
  | _ => value
  }
  let variation = value === t("Not classified") ? #subdued : variation

  <CatalogCategoryPicker value variation compact errored ?shopId onChange />
}

let make = React.memo(make)
