open Intl

module Config = CatalogProduct__Config

// TODO - it should look into catalogs information differences like in variant view
@react.component
let make = (~product: Config.productInformation) => {
  let {pathname} = Navigation.useUrl()

  <Card
    title={t("Product's information")}
    action={{
      icon: #edit_light,
      title: t("Edit product"),
      handler: OpenLink(Route(pathname ++ "/edit")),
    }}>
    <Stack space=#xxsmall>
      <TextStyle weight=#strong> {product.name->React.string} </TextStyle>
      {switch product.description {
      | Some(description) if description->String.length > 0 =>
        <TextStyle> {description->React.string} </TextStyle>
      | _ => React.null
      }}
      <InlineText>
        <TextStyle variation=#normal> {t("From ")->React.string} </TextStyle>
        <TextStyle> {product.formattedOrigin->React.string} </TextStyle>
      </InlineText>
      <InlineText>
        <TextStyle variation=#normal> {t("Default VAT of ")->React.string} </TextStyle>
        <TextStyle>
          {(product.taxValue->Float.toString->Js.String2.replace(".", ",") ++ " %")->React.string}
        </TextStyle>
      </InlineText>
    </Stack>
  </Card>
}

let make = React.memo(make)
