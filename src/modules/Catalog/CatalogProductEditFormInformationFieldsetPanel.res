open Intl
open CatalogProductEditFormInputs

@react.component
let make = () => {
  let state = CatalogProductEditForm.useFormState()
  let productKind = state.initialValues.kind

  <FieldsetLayoutPanel
    title={t("General information")}
    description={t(
      "Complete the product information.\nThe information of this reference will be later enriched by the information of the variant.",
    )}>
    <CatalogProductEditForm.InputText
      field=Name label={t("Product's name")} placeholder={t("Product's name")}
    />
    {switch productKind {
    | #SIMPLE => React.null
    | #BEER | #SPIRITUOUS | #WINE =>
      <Group>
        {switch productKind {
        | #WINE | #BEER => <ColorSelect />
        | #SPIRITUOUS => <ProductFamilyComboBox />
        | #SIMPLE => React.null
        }}
        {switch productKind {
        | #WINE => <WineTypeSelect />
        | #BEER => <ProductBeerTypeComboBox />
        | #SIMPLE | #SPIRITUOUS => React.null
        }}
      </Group>
    }}
    {switch (productKind, state.values.color) {
    | (#WINE, Some(#WHITE)) => <WhiteWineTypeSelect />
    | _ => React.null
    }}
    {switch productKind {
    | #WINE =>
      <Group>
        <ProductProducerComboBox />
        <ProductDesignationComboBox />
      </Group>
    | _ => React.null
    }}
    <Group>
      <ProductCountrySelect />
      {switch productKind {
      | #BEER | #SPIRITUOUS | #SIMPLE => <ProductProducerComboBox />
      | #WINE => <ProductRegionComboBox />
      }}
    </Group>
    <ProductTaxSelect productKind />
  </FieldsetLayoutPanel>
}

let make = React.memo(make)
