open Intl

module UpdateVariantStockThresholdsMutation = %graphql(`
  mutation updateVariant_stockThresholds($id: ID!, $input: InputUpdateVariant!) {
    updateVariant(id: $id, input: $input) {
      id
      maxStockThreshold
      minStockThreshold
      stockOrderTriggerThreshold
      stock {
        formattedQuantity
        state @ppxOmitFutureValue
        formattedShopsNames
      }
    }
  }
`)

let validateMinStockThreshold = ({
  CatalogVariantStockThresholdForm.Lenses.maxStockThreshold: maxStockThreshold,
  minStockThreshold,
  stockOrderTriggerThreshold,
}) =>
  switch (maxStockThreshold, minStockThreshold, stockOrderTriggerThreshold) {
  | (_, minStock, triggerStock) if minStock > triggerStock =>
    Error(t("The minimum stock should be less or equal to the value triggering a new order."))
  | (maxStock, minStock, _) if minStock >= maxStock =>
    Error(t("The minimum stock should be less than the maximum stock."))
  | _ => Ok()
  }

let validateStockOrderTriggerThreshold = ({
  CatalogVariantStockThresholdForm.Lenses.maxStockThreshold: maxStockThreshold,
  stockOrderTriggerThreshold,
}) =>
  switch (maxStockThreshold, stockOrderTriggerThreshold) {
  | (0., _) => Error(t("You have to define the maximum stock beforehand."))
  | (maxStock, triggerStock) if triggerStock >= maxStock =>
    Error(t("This value should be less than maximum stock value."))
  | _ => Ok()
  }

let schema = [
  CatalogVariantStockThresholdForm.Schema.Custom(MinStockThreshold, validateMinStockThreshold),
  Custom(StockOrderTriggerThreshold, validateStockOrderTriggerThreshold),
]

@react.component
let make = (
  ~children,
  ~variantId,
  ~maxStockThreshold,
  ~minStockThreshold,
  ~stockOrderTriggerThreshold,
) => {
  let formPropState = CatalogVariantStockThresholdForm.useFormPropState({
    id: {variantId},
    schema,
    initialValues: {
      maxStockThreshold,
      minStockThreshold,
      stockOrderTriggerThreshold,
    },
  })
  let (mutate, mutation) = UpdateVariantStockThresholdsMutation.use()

  let commit = React.useCallback1(
    ({
      CatalogVariantStockThresholdForm.Lenses.minStockThreshold: minStockThreshold,
      maxStockThreshold,
      stockOrderTriggerThreshold,
    }) => {
      let input = UpdateVariantStockThresholdsMutation.makeInputObjectInputUpdateVariant(
        ~minStockThreshold=minStockThreshold->Int.fromFloat,
        ~maxStockThreshold=maxStockThreshold->Int.fromFloat,
        ~stockOrderTriggerThreshold=stockOrderTriggerThreshold->Int.fromFloat,
        (),
      )
      let id = switch variantId {
      | "" => failwith("There is no variant id found here.")
      | _ => variantId
      }

      mutate(UpdateVariantStockThresholdsMutation.makeVariables(~id, ~input, ()))
      ->ApolloHelpers.mutationPromiseToFutureResult
      ->Future.mapOk(({UpdateVariantStockThresholdsMutation.updateVariant: {id}}) => Some(id))
    },
    [variantId],
  )

  <CatalogVariantStockThresholdForm.FormProvider propState=formPropState>
    <CatalogVariantStockThresholdForm.AutoSave
      onSubmit={(_, values) => commit(values)} disabled={mutation.loading}
    />
    children
  </CatalogVariantStockThresholdForm.FormProvider>
}

let make = React.memo(make)
