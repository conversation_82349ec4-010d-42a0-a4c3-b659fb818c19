open Intl

module Mutations = CatalogProductMutations

@react.component
let make = (~id, ~opened, ~onRequestClose) => {
  let scope = Auth.useScope()
  let navigate = Navigation.useNavigate()
  let archiveProduct = Mutations.useArchive(~id)

  <Modal
    title={t("Confirm the archiving of this product and its variants")}
    opened
    commitButtonText={t("Confirm")}
    commitButtonCallback={_ =>
      archiveProduct()->Future.get(result =>
        switch (result, scope) {
        | (Ok(_), Single(_)) => navigate(Catalog->LegacyRouter.routeToPathname)
        | _ => ()
        }
      )}
    abortButtonText={t("Cancel")}
    onRequestClose>
    <Box spaceY=#xxlarge spaceX=#xlarge>
      <TextStyle variation=#normal>
        {t(
          "Once the product is archived, all its variants will be archived too.\nIts variants will only be visible from the catalog by applying the \"Archive\" filter.",
        )->React.string}
      </TextStyle>
    </Box>
  </Modal>
}

let make = React.memoCustomCompareProps(make, (oldProps, newProps) =>
  oldProps.id === newProps.id && oldProps.opened === newProps.opened
)
