// Warning case 23 treated as error:
// cannot use ppx syntax because the record only has one field.
module Lenses = {
  type state = {internalNote: string}
  type rec field<_> = InternalNote: field<string>
  let get:
    type value. (state, field<value>) => value =
    (state, field) =>
      switch field {
      | InternalNote => state.internalNote
      }
  let set:
    type value. (state, field<value>, value) => state =
    (_, field, value) =>
      switch field {
      | InternalNote => {internalNote: value}
      }
}

include Form.Make(Lenses)
