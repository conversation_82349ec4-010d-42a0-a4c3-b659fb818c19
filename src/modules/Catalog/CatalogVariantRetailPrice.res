type variantPrice = {
  id: option<string>,
  priceId: string,
  variantId: string,
  shopName: string,
  name: string,
  valueExcludingTax: float,
  valueIncludingTax: float,
  taxRate: float,
  taxIncluded: bool,
  purchasePrice: float,
  toQuantity: option<float>,
  fromQuantity: option<float>,
  capacityUnit: option<string>,
  edited: bool,
}

type mutationPriceValues = {
  valueIncludingTax: float,
  valueExcludingTax: float,
}
