type inventoryItem = {
  variantId: string,
  stock: float,
}

let fileTypes: array<FilePicker.fileType> = [#csv, #spreadsheetml]

type decodeVariantIdCellError =
  | Empty
  | Unkown

let decodeRowVariantId = json => {
  if json === Json.encodedNull || json === Json.encodeString("") {
    Error(Empty)
  } else {
    let option = json->Json.decodeString->Option.flatMap(Uuid.fromString)->Option.map(Uuid.toString)
    switch option {
    | Some(variantId) => Ok(variantId)
    | None => Error(Unkown)
    }
  }
}

type decodeStockCellError =
  | Empty
  | Negative
  | NotNumber
  | NonFinite
  | Unknown

let decodeRowStock = json =>
  switch json
  ->Json.decodeString
  ->Option.map(string => Js.String.replace(",", ".", string))
  ->Option.map(Js.Float.fromString)
  ->Option.orElse(json->Json.decodeNumber) {
  | Some(number) =>
    if Js.Float.isNaN(number) {
      Error(NotNumber)
    } else if !Js.Float.isFinite(number) {
      Error(NonFinite)
    } else if number < 0. {
      Error(Negative)
    } else if number === 0. && json === Json.encodeString("") {
      Error(Empty)
    } else {
      Ok(number)
    }
  | None =>
    if json === Json.encodedNull {
      Error(Empty)
    } else {
      Error(Unknown)
    }
  }

let dictVariantIdKey = Intl.t("ID")
let dictStockKey = Intl.t("Inventory quantity")

type decodeRowError =
  | CannotDecodeHeaderRow({index: int, json: Json.t})
  | CannotDecodeRow({index: int, json: Json.t})
  | CannotDecodeCellVariantId({index: int, json: Json.t})
  | CannotDecodeCellStock({index: int, json: Json.t})
  | NegativeCellStock({index: int, json: Json.t})
  | EmptyCellStock({index: int})
  | EmptyCellVariantId({index: int})

let decodeRowValues = (jsonVariantId, jsonStock, index) => {
  let variantId = decodeRowVariantId(jsonVariantId)
  let stock = decodeRowStock(jsonStock)
  switch (variantId, stock) {
  | (Ok(variantId), Ok(stock)) => Ok({variantId, stock})
  | (Error(Empty), _) => Error(EmptyCellVariantId({index: index}))
  | (Error(_), _) => Error(CannotDecodeCellVariantId({index, json: jsonVariantId}))
  | (_, Error(Empty)) => Error(EmptyCellStock({index: index}))
  | (_, Error(Negative)) => Error(NegativeCellStock({index, json: jsonStock}))
  | (_, Error(_)) => Error(CannotDecodeCellStock({index, json: jsonStock}))
  }
}

let decodeDictRowWithIndex = (index, dictRow) =>
  switch (dictRow->Js.Dict.get(dictVariantIdKey), dictRow->Js.Dict.get(dictStockKey)) {
  | (Some(jsonVariantId), Some(jsonStock)) => decodeRowValues(jsonVariantId, jsonStock, index)
  | _ => Error(CannotDecodeHeaderRow({index, json: dictRow->Json.encodeDict}))
  }

let decodeTupleRowWithIndex = (index, tupleRow) =>
  switch (tupleRow[0], tupleRow[1]) {
  | (Some(jsonVariantId), Some(jsonStock)) => decodeRowValues(jsonVariantId, jsonStock, index)
  | _ => Error(CannotDecodeRow({index, json: tupleRow->Json.encodeArray}))
  }

type parseAndDecodeError =
  | InvalidHeaderRow
  | NoRows
  | EmptyColumnStock
  | Unknown

let decodeRows = (~headerIndexOffset=0, rows) => {
  let decodedRows = rows->Array.mapWithIndex((index, jsonRow) =>
    switch (Json.decodeArray(jsonRow), Json.decodeDict(jsonRow)) {
    | (Some(tupleRow), _) => decodeTupleRowWithIndex(index, tupleRow)
    | (_, Some(dictRow)) => decodeDictRowWithIndex(index + headerIndexOffset, dictRow)
    | (None, None) => Error(CannotDecodeRow({index: index + headerIndexOffset, json: jsonRow}))
    }
  )

  let okRowsCount =
    decodedRows
    ->Array.keepMap(row =>
      switch row {
      | Ok(row) => Some(row)
      | Error(_) => None
      }
    )
    ->Array.length
  let errorRowsCount =
    decodedRows
    ->Array.keepMap(row =>
      switch row {
      | Error(_) => Some()
      | Ok(_) => None
      }
    )
    ->Array.length

  if errorRowsCount === 0 && okRowsCount === 0 {
    Error(NoRows)
  } else if errorRowsCount > 0 && okRowsCount === 0 {
    let errorCannotDecodeHeaderRowCount =
      decodedRows
      ->Array.keepMap(row =>
        switch row {
        | Error(CannotDecodeHeaderRow(_)) => Some()
        | Error(_) | Ok(_) => None
        }
      )
      ->Array.length
    let errorEmptyStockCellCount =
      decodedRows
      ->Array.keepMap(row =>
        switch row {
        | Error(EmptyCellStock(_)) => Some()
        | Error(_) | Ok(_) => None
        }
      )
      ->Array.length

    if errorCannotDecodeHeaderRowCount === errorRowsCount {
      Error(InvalidHeaderRow)
    } else if errorEmptyStockCellCount === errorRowsCount {
      Error(EmptyColumnStock)
    } else {
      Ok(decodedRows)
    }
  } else {
    Ok(decodedRows)
  }
}

type sheetDecodeResult<'result> = {
  headerOffset: int,
  payload: 'result,
}

let parseAndDecodeCsvFile = file =>
  Sheet.parseCsvFile(file)
  ->Future.mapError(() => Unknown)
  ->Future.flatMapOk(rows => Future.value(decodeRows(rows)))
  ->Future.map(result => {
    headerOffset: 0,
    payload: result,
  })

let rec parseAndDecodeXlsxFile = (~headerIndexOffset as initialHeaderIndexOffset=?, file) => {
  let headerIndexOffset = initialHeaderIndexOffset->Option.getWithDefault(1)

  Sheet.parseExceljsXlsxFile(file, ~headerIndexOffset)
  ->Future.mapError(() => Unknown)
  ->Future.flatMapOk(rows => Future.value(decodeRows(~headerIndexOffset, rows)))
  ->Future.flatMapError(error =>
    switch error {
    | InvalidHeaderRow if initialHeaderIndexOffset->Option.isNone =>
      parseAndDecodeXlsxFile(file, ~headerIndexOffset=0)->Future.map(result => result.payload)
    | error => Future.value(Error(error))
    }
  )
  ->Future.map(result => {
    headerOffset: headerIndexOffset,
    payload: result,
  })
}
