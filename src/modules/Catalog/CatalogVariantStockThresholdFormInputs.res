open Intl

// TODO - handle bulk mode
@react.component
let make = () =>
  <Stack space=#medium>
    <CatalogVariantStockThresholdForm.InputNumber
      field=MaxStockThreshold label={t("Maximum stock")} minValue=0. precision=0 hideRequired=true
    />
    <CatalogVariantStockThresholdForm.InputNumber
      field=MinStockThreshold label={t("Minimum stock")} minValue=0. precision=0 hideRequired=true
    />
    <CatalogVariantStockThresholdForm.InputNumber
      field=StockOrderTriggerThreshold
      label={t("Stock triggering the order")}
      minValue=0.
      precision=0
      hideRequired=true
    />
  </Stack>

let make = React.memo(make)
