open Intl

@react.component
let make = (~cku, ~name, ~shopName=?, ~openNewTab=false) =>
  <Box spaceRight=#normal spaceY=#xsmall>
    <Stack space={shopName->Option.isSome ? #xxsmall : #none}>
      <TextLink
        text={t(name)} to=Route(Catalog->LegacyRouter.routeToPathname ++ "/" ++ cku) openNewTab
      />
      {switch shopName {
      | Some(shopName) =>
        <TextStyle size=#xxsmall variation=#normal> {shopName->React.string} </TextStyle>
      | _ => React.null
      }}
    </Stack>
  </Box>

let make = React.memo(make)
