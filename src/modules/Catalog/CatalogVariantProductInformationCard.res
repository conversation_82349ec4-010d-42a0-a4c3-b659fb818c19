open Intl

module Config = CatalogProduct__Config

module OrganisationBlockContent = {
  @react.component
  let make = React.memo((~product: option<Config.productInformation>=?, ~displayShopName=true) =>
    switch product {
    | Some(product) =>
      <Stack>
        {switch displayShopName {
        | true =>
          <TextStyle variation=#normal size=#xxsmall>
            {(t("From catalog of") ++ (" " ++ product.shopName))->React.string}
          </TextStyle>
        | _ => React.null
        }}
        <InlineText>
          <TextStyle variation=#normal> {t("Inside category ")->React.string} </TextStyle>
          <TextStyle> {product.formattedCategoryName->React.string} </TextStyle>
        </InlineText>
      </Stack>
    | _ => React.null
    }
  )
}

module ShowMoreWrapper = {
  @react.component
  let make = React.memo((~children, ~collapsed, ~onPress) => {
    let (ref, hovered) = Hover.use()

    <Stack space=#small>
      {switch collapsed {
      | false => <Box spaceTop=#medium> children </Box>
      | _ => React.null
      }}
      <Box spaceTop=#xsmall>
        <Touchable ref onPress>
          <Inline>
            <Icon name={collapsed ? #arrow_down_light : #arrow_up_light} />
            <Title level=#5 weight=#medium color=?{hovered ? Some(Colors.neutralColor50) : None}>
              {t(collapsed ? "Show more" : "Collapse")->React.string}
            </Title>
          </Inline>
        </Touchable>
      </Box>
    </Stack>
  })
}

@react.component
let make = (~cku, ~shopsProduct: array<Config.productInformation>) => {
  let (showMore, setShowMore) = React.useState(_ => false)
  let scope = Auth.useScope()
  let productPageRoute = "/product/" ++ cku
  let productPagePathname = Catalog->LegacyRouter.routeToPathname ++ productPageRoute

  // Wether there are differing variant categories between shops
  let productCategoryDiffering = React.useMemo2(
    () =>
      shopsProduct->CatalogVariant.MultiShops.isReferenceDiffering(~scope, ~predicate=(
        a: Config.productInformation,
        b: Config.productInformation,
      ) => a.formattedCategoryName === b.formattedCategoryName),
    (scope, shopsProduct),
  )

  <Card
    title={t("Bound product")}
    action={{
      icon: #queue_arrow_right_light,
      title: t("See bound product"),
      handler: OpenLink(Route(productPagePathname)),
    }}>
    {switch shopsProduct[0] {
    | Some(product) =>
      <Stack space=#xxsmall>
        {switch (product.status, scope) {
        | (Some(Archived), Single(_) | Organisation({activeShop: Some(_)})) =>
          <Badge variation=#neutral size=#small> {t("Archived")->React.string} </Badge>
        | _ => React.null
        }}
        <TextLink
          text=product.name to=Route(Catalog->LegacyRouter.routeToPathname ++ productPageRoute)
        />
        {switch product.description {
        | Some(description) if description->String.length > 0 =>
          <TextStyle> {description->React.string} </TextStyle>
        | _ => React.null
        }}
        <InlineText>
          <TextStyle variation=#normal> {t("From ")->React.string} </TextStyle>
          <TextStyle> {product.formattedOrigin->React.string} </TextStyle>
        </InlineText>
        <InlineText>
          <TextStyle variation=#normal> {t("Default VAT of ")->React.string} </TextStyle>
          <TextStyle>
            {(product.taxValue->Float.toString->Js.String2.replace(".", ",") ++ " %")->React.string}
          </TextStyle>
        </InlineText>
      </Stack>
    | _ => React.null
    }}
    <InfoBlock title="ORGANISATION">
      <OrganisationBlockContent
        product=?{shopsProduct[0]} displayShopName=productCategoryDiffering
      />
      {switch productCategoryDiffering {
      | true =>
        <ShowMoreWrapper collapsed={!showMore} onPress={_ => setShowMore(state => !state)}>
          <Stack space=#medium>
            {shopsProduct
            ->Array.slice(~offset=1, ~len=shopsProduct->Array.length)
            ->Array.map(product => <OrganisationBlockContent product />)
            ->React.array}
          </Stack>
        </ShowMoreWrapper>
      | _ => React.null
      }}
    </InfoBlock>
  </Card>
}

let make = React.memo(make)
