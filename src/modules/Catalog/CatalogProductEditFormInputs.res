open Intl

exception InputColorUnexpectedKind

module ColorSelect = {
  module ColorItem = {
    @react.component
    let make = React.memo((~children, ~color) => {
      let {foregroundColor, backgroundColor} = CatalogProduct.Color.toColorSet(
        color,
        ~variation=#badge,
      )
      let borderColor = foregroundColor ++ "1A"
      <div style={ReactDOM.Style.make(~alignSelf="flex-start", ())}>
        <Badge backgroundColor foregroundColor borderColor> children </Badge>
      </div>
    })
  }

  @react.component
  let make = () => {
    let {validation, submission} = CatalogProductEditForm.useFormState()

    let errorMessage = switch (validation, submission) {
    | (Error(errors), Failed(_)) =>
      errors
      ->Array.keepMap(((field, error)) =>
        switch (field, error) {
        | (CatalogProductEditForm.Schema.Field(CatalogProductEditForm.Lenses.Color), error) =>
          Some(error)
        | _ => None
        }
      )
      ->Array.get(0)
    | _ => None
    }

    let renderTriggerView = (
      ~children as _,
      ~item: option<Select.item<option<CatalogProduct.Color.t>>>,
      ~hovered,
      ~active,
      ~focused,
    ) => {
      let itemLabel = item->Option.mapWithDefault(t("Select a color"), item => item.label)
      let color = CatalogProduct.Color.fromString(itemLabel)

      <OverlayTriggerView
        preset=#inputField({required: true, ?errorMessage})
        label={t("Color")}
        hovered
        active
        focused>
        {switch color {
        | Ok(color) =>
          let colorLabel = CatalogProduct.Color.toLabel(color)
          <ColorItem color> {colorLabel->React.string} </ColorItem>
        | Error(_) => <TextStyle variation=#normal> {t("Select a color")->React.string} </TextStyle>
        }}
      </OverlayTriggerView>
    }
    let renderItemContent = React.useCallback0((
      color: Select.item<option<CatalogProduct.Color.t>>,
    ) =>
      switch color.Select.value {
      | Some(color) =>
        let colorLabel = CatalogProduct.Color.toLabel(color)
        <ColorItem color> {colorLabel->React.string} </ColorItem>
      | None => React.null
      }
    )

    let sections = {
      let options = switch CatalogProductEditForm.useFormState().initialValues.kind {
      | #WINE => [#WHITE, #RED, #ROSE, #ORANGE]
      | #BEER => [#WHITE, #RED, #AMBER, #BLOND, #DARK, #BLACK]
      | _ => raise(InputColorUnexpectedKind)
      }
      [
        {
          Select.items: options->Array.map(color => {
            Select.key: CatalogProduct.Color.toString(color),
            label: CatalogProduct.Color.toString(color),
            value: Some(color),
          }),
        },
      ]
    }

    <CatalogProductEditForm.InputSelect
      field=Color label={t("Color")} searchable=false renderTriggerView renderItemContent sections
    />
  }
}

module WineTypeSelect = {
  open CatalogProduct.WineType
  @react.component
  let make = () => {
    let options = [#STILL, #EFFERVESCENT]

    let items = options->Array.map(wineType => {
      Select.key: wineType->toString,
      label: wineType->toLabel,
      value: Some(wineType),
    })
    let sections = [{Select.items: items}]

    <CatalogProductEditForm.InputSelect
      field=WineType label={t("Wine type")} placeholder={t("Select a wine type")} sections
    />
  }
}

module WhiteWineTypeSelect = {
  open CatalogProduct.WhiteWineType
  @react.component
  let make = () => {
    let options = [#DRY, #SEMIDRY, #SOFT, #SWEET]

    let items = options->Array.map(whiteWineType => {
      Select.key: whiteWineType->toString,
      label: whiteWineType->toLabel,
      value: Some(whiteWineType),
    })
    let sections = [{Select.items: items}]

    <CatalogProductEditForm.InputSelect
      field=WhiteWineType
      label={t("White wine type")}
      placeholder={t("Select a white wine type")}
      sections
    />
  }
}

module ProductProducerComboBox = {
  module ProductProducersQuery = %graphql(`
    query productProducers {
      productProducers
    }
  `)

  @react.component
  let make = () => {
    let results = ProductProducersQuery.use()

    <CatalogProductEditForm.InputSuggestionComboBox
      field=Producer
      label={t("Producer")}
      placeholder={t("Add a producer")}
      hideRequired=true
      items={switch results {
      | {data: Some({productProducers})} =>
        productProducers
        ->Array.keep(suggestion => suggestion !== "")
        ->Array.map(productProducer => {
          InputSuggestionComboBoxField.value: productProducer,
        })
      | _ => []
      }}
    />
  }
}

module ProductDesignationComboBox = {
  module ProductWineDesignationsQuery = %graphql(`
    query productWineDesignations {
      productWineDesignations
    }
  `)

  @react.component
  let make = () => {
    let results = ProductWineDesignationsQuery.use()

    <CatalogProductEditForm.InputSuggestionComboBox
      field=Designation
      label={t("Designation")}
      placeholder={t("Add a designation")}
      items={switch results {
      | {data: Some({productWineDesignations})} =>
        productWineDesignations
        ->Array.keep(suggestion => suggestion !== "")
        ->Array.map(productWineDesignation => {
          InputSuggestionComboBoxField.value: productWineDesignation,
        })
      | _ => []
      }}
    />
  }
}

module ProductFamilyComboBox = {
  module ProductSpirituousFamiliesQuery = %graphql(`
    query productSpirituousFamilies {
      productSpirituousFamilies
    }
  `)

  @react.component
  let make = () => {
    let results = ProductSpirituousFamiliesQuery.use()

    <CatalogProductEditForm.InputSuggestionComboBox
      field=Family
      label={t("Family")}
      placeholder={t("Add a family")}
      items={switch results {
      | {data: Some({productSpirituousFamilies})} =>
        productSpirituousFamilies
        ->Array.keep(suggestion => suggestion !== "")
        ->Array.map(productSpirituousFamily => {
          InputSuggestionComboBoxField.value: productSpirituousFamily,
        })
      | _ => []
      }}
    />
  }
}

module ProductBeerTypeComboBox = {
  module ProductBeerTypesQuery = %graphql(`
    query productBeerTypes {
      productBeerTypes
    }
  `)

  @react.component
  let make = () => {
    let results = ProductBeerTypesQuery.use()

    <CatalogProductEditForm.InputSuggestionComboBox
      field=BeerType
      label={t("Beer type")}
      placeholder={t("Add a beer type")}
      items={switch results {
      | {data: Some({productBeerTypes})} =>
        productBeerTypes
        ->Array.keep(suggestion => suggestion !== "")
        ->Array.map(productBeerType => {
          InputSuggestionComboBoxField.value: productBeerType,
        })
      | _ => []
      }}
    />
  }
}

module ProductRegionComboBox = {
  module ProductRegionsQuery = %graphql(`
    query productRegions($country: String) {
      productRegions(country: $country)
    }
  `)

  @react.component
  let make = () => {
    let {country} = CatalogProductEditForm.useFormState().values

    // TODO - we have to manage the translation upstream in the json
    let queryVariableCountry = switch country {
    | "Afrique du Sud" => "South Africa"
    | "Allemagne" => "Germany"
    | "Argentine" => "Argentina"
    | "Australie" => "Australia"
    | "Autriche" => "Austria"
    | "Croatie" => "Croatia"
    | "Espagne" => "Spain"
    | "Etats-Unis" => "United States"
    | "Georgie" => "Georgia"
    | "Hongrie" => "Hungary"
    | "Italie" => "Italy"
    | "Liban" => "Lebanon"
    | "Slovaquie" => "Slovakia"
    | "Suisse" => "Switzerland"
    | "Grèce" => "Greece"
    | "Nouvelle Zélande" => "New Zealand"
    | "Israël" => "Israel"
    | _ => country
    }

    let results = ProductRegionsQuery.use(
      ProductRegionsQuery.makeVariables(~country=queryVariableCountry, ()),
    )

    <CatalogProductEditForm.InputSuggestionComboBox
      field=Region
      label={t("Region")}
      placeholder={t("Add a region")}
      items={switch results {
      | {data: Some({productRegions})} =>
        productRegions
        ->Array.keep(suggestion => suggestion !== "")
        ->Array.map(productRegion => {InputSuggestionComboBoxField.value: productRegion})
      | _ => []
      }}
    />
  }
}

module ProductCountrySelect = {
  @react.component
  let make = () =>
    <CatalogProductEditForm.InputCountrySelect field=Country subdivisionsIncluded=true />
}

module ProductTaxSelect = {
  @react.component
  let make = (~productKind: CatalogProduct.Kind.t) => {
    let {values} = CatalogProductEditForm.useFormState()
    let dispatch = CatalogProductEditForm.useFormDispatch()

    let defaultTaxValue = switch productKind {
    | #SIMPLE => Some(5.5)
    | _ => None
    }

    let value = values.tax->Option.map(tax => {
      CatalogProductTaxSelect.id: tax.id,
      value: tax.value,
    })

    let onChange = React.useCallback0(tax =>
      FieldValueChanged(
        Tax,
        _ => Some({
          CatalogProductEditForm.Tax.id: tax.CatalogProductTaxSelect.id,
          value: tax.value,
        }),
      )->dispatch
    )

    <CatalogProductTaxSelect defaultTaxValue value onChange />
  }
}
