open Accounting.Types

module Utils = OrderEdit__Utils

@react.component
let make = (~cart, ~statuses, ~onRequestDispatch as dispatch) =>
  <Inline>
    <Stack space=#large>
      <CartGlobalDiscountView
        discount=?{cart.discounts[0]}
        totalAmountExcludingGlobalDiscounts=cart.totalAmountExcludingGlobalDiscounts
        onRequestDispatch=dispatch
      />
      <InputToggleSwitchField
        label={Intl.t("order_cart.cart_options.tax_application.toggle_switch.label")}
        alignToggleButtonToStart=true
        required=false
        disabled={statuses->OrderStatus.has(#RECEIVED)}
        value=cart.taxesFree
        onChange={_ => dispatch(TaxesFreeToggleRequested)}
      />
    </Stack>
  </Inline>

let make = React.memoCustomCompareProps(make, (oldProps, newProps) =>
  oldProps.cart === newProps.cart && oldProps.statuses === newProps.statuses
)
