open Accounting.Types
open Accounting.Actions

module Utils = CartProductFeeFieldset__Utils

@react.component
let make = (~editable=false, ~product, ~onRequestDispatch as dispatch) => {
  let {ref: popoverTriggerRef, state: popover, ariaProps: popoverAriaProps} = Popover.useTrigger()

  let (productId, fees, formattedUnitFee) = switch product {
  | Unit({id, fees, unitFee}) | Bulk({id, fees, unitFee}, _) =>
    let formattedUnitFee =
      unitFee->Option.map(value =>
        Big.toFloat(value)->Intl.currencyFormat(
          ~currency=#EUR,
          ~minimumFractionDigits=3,
          ~maximumFractionDigits=3,
        )
      )
    (id, fees, formattedUnitFee)
  }

  React.useEffect1(() => {
    if popover.opened && fees->Array.size === 0 {
      ProductFeeAdded(productId)->dispatch
    }
    None
  }, [popover.opened])

  let renderFeesTooltip = React.useMemo1(() =>
    <TooltipIcon variation=#info>
      {fees
      ->Array.keepMap(fee => {
        switch fee {
        | {kind, amount} if amount > 0. =>
          let formattedAmount =
            amount->Intl.currencyFormat(
              ~currency=#EUR,
              ~minimumFractionDigits=3,
              ~maximumFractionDigits=3,
            )
          let formattedFee = `${kind->Utils.optionToText} : ${formattedAmount}`
          Some(<>
            <Tooltip.Span text=formattedFee />
            <Tooltip.Gap space=#xxsmall />
          </>)
        | _ => None
        }
      })
      ->React.array}
    </TooltipIcon>
  , [fees])

  let totalAmountElement =
    <Inline space=#small>
      <ProductPriceTableCell value=formattedUnitFee />
      {switch (editable, fees->Array.size > 0) {
      | (false, true) => renderFeesTooltip
      | _ => React.null
      }}
    </Inline>

  <>
    {if editable {
      <OpeningButton
        ref=popoverTriggerRef
        ariaProps=popoverAriaProps.triggerProps
        opened=popover.opened
        onPress={_ => popover.onRequestToggle()}>
        totalAmountElement
      </OpeningButton>
    } else {
      totalAmountElement
    }}
    {if popover.opened {
      <CartProductFeesPopover
        popover
        popoverTriggerRef
        popoverAriaProps
        product
        onRequestProductFeeAdd={() => ProductFeeAdded(productId)->dispatch}
        onRequestProductFeeUpdate={(feeId, updatedFee) =>
          ProductFeeUpdated(productId, feeId, updatedFee)->dispatch}
        onRequestProductFeeReplicate={fee => ProductFeeReplicated(fee)->dispatch}
        onRequestProductFeeRemove={feeId => ProductFeeRemoved(productId, feeId)->dispatch}
      />
    } else {
      React.null
    }}
  </>
}

let make = React.memoCustomCompareProps(make, (oldProps, newProps) =>
  oldProps.product === newProps.product && oldProps.editable === newProps.editable
)
