open Accounting.Types

let makeQuantity = (
  ~maxStockThreshold=?,
  ~stockRawQuantity=?,
  ~packaging=?,
  ~bulkPrecision=?,
  (),
) => {
  let defaultQuantity = {
    let exponent = bulkPrecision->Option.mapWithDefault(0., Float.fromInt)
    let defaultQuantity = 1. *. 10. ** exponent

    defaultQuantity->Int.fromFloat
  }
  let quantity = switch (maxStockThreshold, stockRawQuantity, packaging) {
  | (Some(maxStock), Some(stock), Some(packaging)) => {
      let quantity = maxStock - stock

      switch mod(quantity, packaging) {
      | 0 => quantity
      | diff => quantity - diff
      }
    }

  | (Some(maxStock), Some(stock), None) => maxStock - stock
  | (None, None, Some(packaging)) => packaging
  | _ => defaultQuantity
  }

  switch quantity <= 0 {
  | true => defaultQuantity
  | _ => quantity
  }
}

let makeProductInput = (
  ~id: string,
  ~name: string,
  ~description: string,
  ~sku=?,
  ~stockRawQuantity=0,
  ~packagingRawQuantity=?,
  ~taxValue: float,
  ~unitPrice=0.,
  ~rawQuantity=0,
  ~bulkPrecision=?,
  ~capacityUnit=?,
  (),
): Accounting.Types.productInput =>
  switch bulkPrecision {
  | Some(capacityPrecision) =>
    // REVIEW - maybe add this logic in an Accounting helper
    let (quantity, stock, packaging) = (
      rawQuantity->Accounting.fromRawProductQuantity(~capacityPrecision)->Big.toFloat,
      stockRawQuantity->Accounting.fromRawProductQuantity(~capacityPrecision)->Big.toFloat,
      packagingRawQuantity->Option.map(value =>
        value->Accounting.fromRawProductQuantity(~capacityPrecision)->Big.toFloat
      ),
    )

    Bulk({
      product: {
        id: None,
        identifier: Some(id),
        stockKeepingUnit: sku,
        name,
        description,
        capacityUnit,
        stock,
        packaging,
        quantity,
        expectedQuantity: None,
        unitPrice,
        fees: [],
        discounts: [],
        tax: taxValue,
      },
      precision: capacityPrecision,
    })
  | None =>
    Unit({
      product: {
        id: None,
        identifier: Some(id),
        stockKeepingUnit: sku,
        name,
        description,
        capacityUnit: None,
        stock: stockRawQuantity,
        packaging: packagingRawQuantity,
        quantity: rawQuantity,
        expectedQuantity: None,
        unitPrice,
        fees: [],
        discounts: [],
        tax: taxValue,
      },
    })
  }
