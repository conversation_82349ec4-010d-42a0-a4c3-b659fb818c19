open Intl
open Accounting.Types

@react.component
let make = (
  ~product,
  ~popover: Popover.t,
  ~popoverAriaProps: ReactAria.Overlay.Trigger.t,
  ~popoverTriggerRef,
  ~onRequestProductFeeAdd,
  ~onRequestProductFeeUpdate,
  ~onRequestProductFeeReplicate,
  ~onRequestProductFeeRemove,
) => {
  let (fees, availablesFeeKinds) = switch product {
  | Unit({fees, availablesFeeKinds}) | Bulk({fees, availablesFeeKinds}, _) => (
      fees,
      availablesFeeKinds,
    )
  }
  let feesRef = React.useRef(fees)

  React.useEffect1(() => {
    if fees->Array.size === 0 && fees->Array.length < feesRef.current->Array.length {
      popover.onRequestClose()
    }
    feesRef.current = fees
    None
  }, [fees])

  if fees->Array.size > 0 {
    <Popover triggerRef=popoverTriggerRef state=popover modal=false placement=#"bottom end">
      <Popover.Dialog ariaProps=popoverAriaProps.overlayProps>
        <Box spaceX=#xlarge spaceY=#xlarge>
          <Stack space=#normal>
            {fees
            ->Array.mapWithIndex((index, fee) =>
              <CartProductFeeFieldset
                key=fee.id
                displayLabel={index === 0}
                fee
                product
                onRequestProductFeeUpdate
                onRequestProductFeeReplicate
                onRequestProductFeeRemove
              />
            )
            ->React.array}
          </Stack>
          {switch availablesFeeKinds->Option.map(Array.length) {
          | Some(0) | None => React.null
          | _ =>
            <Inline>
              <Box spaceY=#medium spaceBottom=#xsmall>
                <TextIconButton icon=#plus_light onPress={_ => onRequestProductFeeAdd()}>
                  {t("Add costs")->React.string}
                </TextIconButton>
              </Box>
            </Inline>
          }}
        </Box>
      </Popover.Dialog>
    </Popover>
  } else {
    React.null
  }
}

let make = React.memo(make)
