open Intl
open Accounting.Types

let kindToText = kind =>
  switch (kind: discountKind) {
  | Currency => t("EURO")
  | Percent => t("%")
  | Free => t("Free")
  }

let discountWarningToText = warning =>
  switch (warning: discountWarning) {
  | AmountGreaterThanTotalPrice => t("Cannot be greater than {{max}}")
  }

let productExpectedQuantityWarningToText = warning =>
  switch (warning: productExpectedQuantityWarning) {
  | IsNotMultipleOfPackaging => t("Quantity by {{packaging}}")
  }
