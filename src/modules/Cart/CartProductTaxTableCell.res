open Intl

@react.component
let make = (~taxAmount, ~taxRate) => {
  let formattedTaxAmount =
    taxAmount->Intl.currencyFormat(
      ~currency=#EUR,
      ~minimumFractionDigits=3,
      ~maximumFractionDigits=3,
    )
  let formattedTaxRate = (taxRate /. 100.)->Intl.percentFormat(~maximumFractionDigits=1)

  <Stack space=#xsmall>
    <TextStyle> {formattedTaxAmount->React.string} </TextStyle>
    <TextStyle size=#xxsmall variation=#normal>
      {(t("order_cart.table.vat_total_column.product_tax_cell.subtext") ++ " " ++ formattedTaxRate)
        ->React.string}
    </TextStyle>
  </Stack>
}

let make = React.memo(make)
