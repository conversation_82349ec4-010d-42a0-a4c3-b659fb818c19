open Accounting.Types

@react.component
let make = (~editable=false, ~product, ~onRequestDispatch) => {
  let {ref: popoverTriggerRef, state: popover, ariaProps: popoverAriaProps} = Popover.useTrigger()

  let formattedTotalLocalDiscounts = switch product {
  | Unit({totalLocalDiscounts})
  | Bulk({totalLocalDiscounts}, _) =>
    totalLocalDiscounts->Option.map(value =>
      Big.toFloat(value)->Intl.currencyFormat(
        ~currency=#EUR,
        ~minimumFractionDigits=3,
        ~maximumFractionDigits=3,
      )
    )
  }
  let totalAmountElement = <ProductPriceTableCell value=formattedTotalLocalDiscounts />

  <>
    {if editable {
      <OpeningButton
        ref=popoverTriggerRef
        ariaProps=popoverAriaProps.triggerProps
        opened=popover.opened
        onPress={_ => popover.onRequestToggle()}>
        totalAmountElement
      </OpeningButton>
    } else {
      totalAmountElement
    }}
    {if popover.opened {
      <CartProductDiscountPopover
        popover popoverTriggerRef popoverAriaProps product onRequestDispatch
      />
    } else {
      React.null
    }}
  </>
}

let make = React.memoCustomCompareProps(make, (oldProps, newProps) =>
  oldProps.product === newProps.product && oldProps.editable === newProps.editable
)
