open Intl
open Accounting.Types
open Accounting.Actions

module Utils = CartDiscount__Utils

let defaultDiscount = {
  id: "",
  name: "",
  kind: Percent,
  value: 0.,
  quantity: 0.->Big.fromFloat,
  formattedValue: None,
  amount: None,
  formattedAmount: None,
  warnings: [],
}

@react.component
let make = (
  ~discount=defaultDiscount,
  ~totalAmountExcludingGlobalDiscounts,
  ~onRequestDispatch as dispatch,
) => {
  let (value, setValue) = React.useState(() => discount.value)

  ReactUpdateEffect.use2(() => {
    switch (discount.id, value) {
    | ("", _) =>
      GlobalDiscountAdded({
        id: None,
        name: "",
        kind: Percent,
        value,
        quantity: 0,
      })->dispatch
    | (_, 0.) => GlobalDiscountRemoved(discount.id)->dispatch
    | (_, _) =>
      GlobalDiscountUpdated(
        discount.id,
        switch discount.kind {
        | Currency => {...discount, value}
        | Percent if value <= 100. => {...discount, value}
        | _ => {...discount, value: 0.}
        },
      )->dispatch
    }
    None
  }, (value, discount.kind))

  let onGlobalDiscountKindChange = React.useCallback1(kind => {
    switch discount.id {
    | "" =>
      GlobalDiscountAdded({
        id: None,
        name: "",
        kind,
        value: 0.,
        quantity: 0,
      })->dispatch
    | _ => GlobalDiscountUpdated(discount.id, {...discount, kind, value: 0.})->dispatch
    }
  }, [discount])

  <Inline space=#medium>
    {switch discount.kind {
    | Percent
    | Currency =>
      <div style={ReactDOMStyle.make(~width="207px", ())}>
        <Stack space=#xsmall>
          <InputOptionalNumberField
            label={t("Global merchandise discount")}
            appender={discount.kind === Percent ? Percent : Currency(#EUR)}
            minValue=0.
            maxValue=?{discount.kind === Percent
              ? Some(100.)
              : Some(totalAmountExcludingGlobalDiscounts->Option.mapWithDefault(0., Big.toFloat))}
            precision=2
            value=Some(value)
            onChange={value => setValue(_ => value->Option.getWithDefault(0.))}
          />
          {switch discount.warnings {
          | [warning] =>
            <TextStyle size=#xsmall>
              {template(
                warning->Utils.discountWarningToText,
                ~values={
                  "max": totalAmountExcludingGlobalDiscounts->Option.map(value =>
                    value
                    ->Big.toFloat
                    ->Intl.currencyFormat(
                      ~currency=#EUR,
                      ~minimumFractionDigits=3,
                      ~maximumFractionDigits=3,
                    )
                  ),
                },
                (),
              )->React.string}
            </TextStyle>
          | _ => React.null
          }}
        </Stack>
      </div>

    | Free => React.null
    }}
    <InputSegmentedControlsField
      label={t("Type")}
      compact=true
      options=list{Percent, Currency}
      optionToText=Utils.kindToText
      value=discount.kind
      required=false
      onChange={kind => kind->onGlobalDiscountKindChange}
    />
  </Inline>
}

let make = React.memoCustomCompareProps(make, (oldProps, newProps) =>
  oldProps.discount === newProps.discount &&
    oldProps.totalAmountExcludingGlobalDiscounts === newProps.totalAmountExcludingGlobalDiscounts
)
