open Intl

type address = {
  label: string,
  name: string,
  postcode: string,
  city: string,
  country: string,
}

module AddressListRequest = {
  type result = array<address>

  let decodeItem = result => {
    let dict = result->Json.decodeDict->Json.flatDecodeDictFieldDict("properties")
    switch (
      dict->Json.flatDecodeDictFieldString("label"),
      dict->Json.flatDecodeDictFieldString("name"),
      dict->Json.flatDecodeDictFieldString("postcode")->Option.getWithDefault(""),
      dict->Json.flatDecodeDictFieldString("city")->Option.getWithDefault(""),
      dict->Json.flatDecodeDictFieldString("country")->Option.getWithDefault("France"),
    ) {
    | (Some(label), Some(name), postcode, city, country) =>
      Ok({label, name, postcode, city, country})
    | _ => Error()
    }
  }

  let decode = result =>
    switch result
    ->Json.decodeDict
    ->Json.flatDecodeDictFieldArray("features")
    ->Option.map(arrayResult =>
      arrayResult->Array.map(address => decodeItem(address)->Result.getExn)
    )
    ->Option.getExn {
    | exception _ => Error(Request.MalformedResponse)
    | decodedResult => Ok(decodedResult)
    }

  // TODO — It should use Fetch primitive rather than Request core module
  let make = (query, ()) => {
    let endpoint = "https://api-adresse.data.gouv.fr/search?q=" ++ query
    Request.make(
      endpoint,
      ~method=#GET,
      ~authTokenRequired=false,
      ~skipMalformedOkResult=true,
    )->Future.mapResult(result => decode(result))
  }
}

let queryValueThreshold = 10

@react.component
let make = (
  ~errorMessage=?,
  ~required=false,
  ~addressName as value,
  ~onInputChange,
  ~onRequestAutoComplete,
) => {
  let (searchResults, setSearchResults) = React.useState(_ => [])
  let (loading, setLoading) = React.useState(_ => false)

  let loading = ReactUpdateDebounced.use(loading, ~delay=500)
  let shouldFetch = value->String.length >= queryValueThreshold

  React.useEffect1(() => {
    if shouldFetch {
      setLoading(_ => true)
      let request = AddressListRequest.make(value, ())
      let future = request->Future.tapOk(searchResults => setSearchResults(_ => searchResults))
      Some(() => future->Future.cancel)
    } else {
      setSearchResults(_ => [])
      None
    }
  }, [value])

  React.useEffect1(() => {
    setLoading(_ => false)
    None
  }, [searchResults])

  React.useEffect1(() => {
    if value === "" {
      onRequestAutoComplete({
        label: "",
        name: "",
        postcode: "",
        city: "",
        country: "",
      })
    }
    None
  }, [value])

  let items = searchResults->Array.map(address => {
    InputSearchComboBoxField.key: address.label,
    label: `${address.name}, ${address.postcode} ${address.city}`,
    value: address,
  })

  let onInputChange = React.useCallback1(value =>
    switch items->Array.getBy(item => item.label === value) {
    | Some(itemFromLabel) => onInputChange(itemFromLabel.value.name)
    | None => onInputChange(value)
    }
  , [items])

  let onSelectItem: InputSearchComboBoxField.item<address> => unit = item =>
    onRequestAutoComplete(item.InputSearchComboBoxField.value)
  let onSelectItem = React.useCallback0(onSelectItem)

  let showResults = ReactUpdateDebounced.use(shouldFetch, ~delay=350)

  <InputSearchComboBoxField
    label={t("Address")}
    required
    placeholder={t("Search an address")}
    ?errorMessage
    noResultLabel={t("No address suggestion")}
    showResults
    loading
    items
    value
    onInputChange
    onSelectionChange=onSelectItem
  />
}
