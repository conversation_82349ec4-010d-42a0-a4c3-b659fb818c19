open Intl

let arrayKeepUniqueBy = (collection, predicate) =>
  collection->Array.keepWithIndex((current, index) =>
    collection->Array.getIndexBy(element => predicate(element, current)) === Some(index)
  )

module PricesQuery = %graphql(`
  query PricesQuery($after: String) {
    prices(first: 50, after: $after) {
      pageInfo {
        endCursor
        hasNextPage
      }
      edges {
        node {
          id
          name
          enableByDefault
        }
      }
    }
  }
`)

type queryResult = Js.Promise.t<
  Result.t<
    ApolloClient__React_Hooks_UseApolloClient.ApolloClient.ApolloQueryResult.t__ok<PricesQuery.t>,
    ApolloClient__React_Hooks_UseApolloClient.ApolloClient.ApolloError.t,
  >,
>
type data = PricesQuery.t_prices_edges

type status =
  | Loading
  | Success(array<data>)
  | Error

type priceNamesDataset = {
  priceNames: array<string>,
  defaultPriceName: option<string>,
}

let makePriceNamesDataset = (~prices: array<data>) => {
  let uniquePriceNames =
    prices->Array.map(({node: price}) => price.name)->arrayKeepUniqueBy((a, b) => a === b)
  let defaultPriceName =
    prices
    ->Array.reduce((None, 0), (acc, {node: price}) => {
      switch prices
      ->Array.keep(({node: current}) =>
        current.name === price.name && current.enableByDefault && price.enableByDefault
      )
      ->Array.length {
      | occurrences if occurrences > acc->snd => (Some(price.name), occurrences)
      | _ => acc
      }
    })
    ->fst

  {
    priceNames: uniquePriceNames,
    defaultPriceName,
  }
}

// TODO - make it as a functor for later on
let rec runScanPrices = (
  ~pricesFetch: (~after: option<string>) => queryResult,
  ~cursor=?,
  ~data=[],
  ~onDone,
  (),
) =>
  pricesFetch(~after=cursor)
  ->FuturePromise.fromPromise
  ->Future.mapOk(response =>
    switch response {
    | Ok({
        data: {prices: {pageInfo: {endCursor, hasNextPage: Some(true)}, edges: prices}},
        error: None,
      }) =>
      runScanPrices(~pricesFetch, ~data=data->Array.concat(prices), ~cursor=?endCursor, ~onDone, ())
    | Ok({data: {prices: {edges: prices}}, error: None}) =>
      onDone(Success(data->Array.concat(prices)))
    | _ => onDone(Error)
    }
  )
  ->ignore

@react.component
let make = (~required=false, ~errorMessage=?, ~value, ~onChange) => {
  let apolloClient = ApolloClient.React.useApolloClient()
  let (status, setStatus) = React.useState(() => Loading)

  // Fetch combined prices data from every shops
  React.useEffect0(() => {
    let pricesFetch = (~after) =>
      apolloClient.query(
        ~query=module(PricesQuery),
        ~fetchPolicy=NetworkOnly,
        PricesQuery.makeVariables(~after?, ()),
      )

    runScanPrices(~pricesFetch, ~onDone=result => setStatus(_ => result), ())
    None
  })

  // Merges priceNames (from multishop) and gets the defaultPriceName
  let (priceNames, defaultPriceName) = React.useMemo1(() =>
    switch status {
    | Success(prices) =>
      let result = makePriceNamesDataset(~prices)
      (result.priceNames, result.defaultPriceName)
    | _ => ([], None)
    }
  , [status])

  // Updates the form value once and if a defaultPriceName is found
  React.useEffect1(() => {
    switch (value, defaultPriceName) {
    | ("", Some(priceName)) => onChange(priceName)
    | _ => ()
    }
    None
  }, [defaultPriceName])

  // NOTE - custom trigger to handle errorMessage
  let renderTriggerView = (~children, ~item as _, ~hovered, ~active, ~focused) =>
    <OverlayTriggerView
      preset=#inputField({required, ?errorMessage})
      label={t("Price list")}
      disabled={priceNames->Array.size === 0}
      hovered
      active
      focused>
      children
    </OverlayTriggerView>

  <Select
    preset=#filter
    placeholder={priceNames->Array.size > 0 ? t("Select a price list") : t("Loading...")}
    disabled={priceNames->Array.size === 0}
    renderTriggerView
    sections={
      let items = priceNames->Array.map(priceName => {
        Select.label: priceName,
        key: priceName,
        value: priceName,
      })

      [{title: t("Options"), items}]
    }
    value
    onChange
  />
}

let make = React.memo(make)
