module Discount = {
  type kind = [#PERCENT | #CURRENCY]
  type t = {
    id: string,
    amount: float,
    kind: kind,
  }
}

module ProductPrice = {
  type t = {
    name: string,
    value: float,
  }

  let makeDiscounted = (~value: float, ~amount: float, ~kind: Discount.kind) =>
    switch kind {
    | #PERCENT => value -. value *. amount /. 100.
    | #CURRENCY => value -. amount
    }
}

type t = {
  id: string,
  cku: string,
  name: string,
  description: string,
  stockKeepingUnit: option<string>,
  purchasePrice: float,
  bulkUnit: option<string>,
  retailPrices: option<array<ProductPrice.t>>,
  discount: Discount.t,
}

let make = (
  ~id,
  ~cku,
  ~name,
  ~description,
  ~stockKeepingUnit,
  ~purchasePrice,
  ~bulkUnit,
  ~retailPricesData,
  ~selectedPriceName,
) => {
  id,
  cku,
  name,
  description,
  stockKeepingUnit,
  purchasePrice,
  bulkUnit,
  retailPrices: switch retailPricesData->Array.map(((priceName, valueIncludingTax)) => {
    ProductPrice.name: priceName,
    value: valueIncludingTax,
  }) {
  | prices if prices->Array.some(({name}) => Some(name) === selectedPriceName) => Some(prices)
  | _ => None
  },
  discount: {
    id: Uuid.make()->Uuid.toString,
    amount: 0.,
    kind: #CURRENCY,
  },
}

let isValid = (discountedProduct: t, ~selectedPriceName: option<string>) => {
  let {amount, kind} = discountedProduct.discount
  let retailPriceValue = switch (discountedProduct.retailPrices, selectedPriceName) {
  | (Some(retailPrices), Some(selectedPriceName)) =>
    switch retailPrices->Array.getBy(price => price.name === selectedPriceName) {
    | Some({value}) => Some(value)
    | _ => None
    }
  | _ => None
  }

  switch retailPriceValue {
  | Some(retailPriceValue) =>
    let discountedValue = ProductPrice.makeDiscounted(~value=retailPriceValue, ~amount, ~kind)

    discountedValue < retailPriceValue && discountedValue >= 0.
  | _ => false
  }
}
