open Intl

module RootCampaign = {
  type t = {
    id: string,
    creatorIdentifier: string,
    shopId: string,
    shopName: string,
    priceId: string,
    status: option<PromotionStatus.t>,
    selected: bool,
  }
}

// NOTE - This model supports multishop by regrouping all common data and by splitting
// individual data as an array of RootCampaign.t who have their own IDs, shop and status.
module Lenses = %lenses(
  type state = {
    discountedProducts: array<PromotionEditDiscountedProduct.t>,
    name: string,
    priceName: option<string>,
    period: option<(Js.Date.t, Js.Date.t)>,
    rootCampaigns: array<RootCampaign.t>,
  }
)

include Form.Make(Lenses)

let schema = [
  Schema.StringNotEmpty(Name),
  Custom(
    Period,
    ({period}) => period->Option.isSome ? Ok() : Error(t("Please select a date range.")),
  ),
  Custom(
    PriceName,
    ({priceName}) =>
      switch priceName {
      | None | Some("") => Error(t("Please select a price name."))
      | _ => Ok()
      },
  ),
  Custom(
    RootCampaigns,
    ({rootCampaigns}) =>
      rootCampaigns->Array.some(campaign => campaign.selected)
        ? Ok()
        : Error(t("Please select at least one shop.")),
  ),
  Custom(
    DiscountedProducts,
    ({discountedProducts, priceName: selectedPriceName}) => {
      open PromotionEditDiscountedProduct
      switch discountedProducts {
      | [] => Error(t("Please add at least one product."))
      | products if !(products->Array.every(isValid(~selectedPriceName))) =>
        Error("At least one product has an invalid discount.")
      | _ => Ok()
      }
    },
  ),
]
