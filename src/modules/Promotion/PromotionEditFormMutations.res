open PromotionEditForm

exception MakeCreateCampaignsInputs_CreatorIdentifierEmpty
exception MakeCreateCampaignsInputs_NameEmpty
exception MakeCreateCampaignsInputs_PeriodNotFound
exception MakeCreateCampaignsInputs_InvalidDiscountedProduct
exception MakeUpdateCampaignsInputs_NameEmpty
exception MakeUpdateCampaignsInputs_PeriodNotFound
exception MakeUpdateCampaignsInputs_InvalidDiscountedProduct
exception CreateGlobalCampaignMutation_InvalidInputs
exception CreateGlobalCampaignMutation_CreatorIdentifierNotFound
exception UpdateGlobalCampaignMutation_InvalidInputs
exception StartCampaignMutation_InvalidStatus
exception StopCampaignMutation_InvalidStatus

module CreateCampaignMutation = %graphql(`
  mutation createPromotionCampaign($id: ID!, $cku: CKU!, $input: InputCreatePromotionCampaign!, $discountsInput: [InputPromotionCampaignDiscounts!]!) {
    createPromotionCampaign(id: $id, cku: $cku, input: $input, promotionCampaignDiscountsInput: $discountsInput) {
      id
      cku
      formattedStatus @ppxOmitFutureValue
    }
  }
`)

module UpdateCampaignMutation = %graphql(`
  mutation updatePromotionCampaign($id: ID!, $input: InputUpdatePromotionCampaign!, $discountsInput: [InputPromotionCampaignDiscounts!]!) {
    updatePromotionCampaign(id: $id, input: $input, promotionCampaignDiscountsInput: $discountsInput) {
      id
      cku
      name
      startDate
      endDate
      formattedStatus @ppxOmitFutureValue
    }
  }
`)

module StartCampaignMutation = %graphql(`
  mutation startPromotionCampaign($id: ID!) {
    startPromotionCampaign(id: $id) {
      id
      cku
      formattedStatus @ppxOmitFutureValue
    }
  }
`)

module StopCampaignMutation = %graphql(`
  mutation stopPromotionCampaign($id: ID!) {
    stopPromotionCampaign(id: $id) {
      id
      cku
      formattedStatus @ppxOmitFutureValue
    }
  }
`)

// TODO - convert (discount == price) or (100%) into #FREE
let makeCreateCampaignsInputs = (~state: Lenses.state, ~creatorIdentifier: string) => {
  let (startDate, endDate) = switch state.period {
  | Some((startDate, endDate)) => (
      startDate->Scalar.Datetime.serialize,
      endDate->Scalar.Datetime.serialize,
    )
  | _ => raise(MakeCreateCampaignsInputs_PeriodNotFound)
  }
  let rootCampaignsInputs =
    state.rootCampaigns
    ->Array.reverse
    ->Array.keepMap(campaign =>
      switch (campaign, state.name, creatorIdentifier) {
      | ({selected: true}, _, "") => raise(MakeCreateCampaignsInputs_CreatorIdentifierEmpty)
      | ({selected: true}, "", _) => raise(MakeCreateCampaignsInputs_NameEmpty)
      | ({selected: true}, _, _) =>
        Some((
          campaign.id,
          CreateCampaignMutation.makeInputObjectInputCreatePromotionCampaign(
            ~creatorIdentifier,
            ~name=state.name,
            ~shopId=campaign.shopId,
            ~priceId=campaign.priceId,
            ~startDate,
            ~endDate,
            (),
          ),
        ))
      | _ => None
      }
    )
  let discountsInput =
    state.discountedProducts
    ->Array.reverse
    ->Array.map(product =>
      switch product->PromotionEditDiscountedProduct.isValid(~selectedPriceName=state.priceName) {
      | true =>
        CreateCampaignMutation.makeInputObjectInputPromotionCampaignDiscounts(
          ~variantCku=product.cku->Scalar.CKU.serialize,
          ~value=product.discount.amount,
          ~kind=product.discount.kind,
          (),
        )
      | _ => raise(MakeCreateCampaignsInputs_InvalidDiscountedProduct)
      }
    )

  (rootCampaignsInputs, discountsInput)
}

let useCreateGlobalCampaign = () => {
  let (mutate, result) = CreateCampaignMutation.use()
  let authState = Auth.useState()

  React.useCallback0((_, state: Lenses.state) => {
    let cku = Uuid.make()->Uuid.toString
    let creatorIdentifier = switch authState {
    | Logged({user}) => user.id
    | _ => raise(CreateGlobalCampaignMutation_CreatorIdentifierNotFound)
    }

    switch makeCreateCampaignsInputs(~state, ~creatorIdentifier) {
    | ([], _) | (_, []) => raise(CreateGlobalCampaignMutation_InvalidInputs)
    | (rootCampaignsInputs, discountsInput) =>
      rootCampaignsInputs
      ->Array.map(((id, input)) =>
        mutate(
          CreateCampaignMutation.makeVariables(
            ~cku=cku->Scalar.CKU.serialize,
            ~id,
            ~input,
            ~discountsInput,
            (),
          ),
        )
        ->ApolloHelpers.mutationPromiseToFutureResult
        ->Future.mapOk(({CreateCampaignMutation.createPromotionCampaign: {cku}}) => Some(cku))
        // NOTE - force refetching with the new promotion when going back to list page
        ->Future.tapOk(
          _ =>
            switch result.client {
            | Some({resetStore}) => resetStore()->ignore
            | _ => ()
            },
        )
      )
      ->ApolloHelpers.mergeFutureResults
    }
  })
}

let makeUpdateCampaignsInputs = (~state: Lenses.state) => {
  let (startDate, endDate) = switch state.period {
  | Some((startDate, endDate)) => (
      startDate->Scalar.Datetime.serialize,
      endDate->Scalar.Datetime.serialize,
    )
  | _ => raise(MakeUpdateCampaignsInputs_PeriodNotFound)
  }
  let rootCampaignsInputs =
    state.rootCampaigns
    ->Array.reverse
    ->Array.keepMap(campaign =>
      switch (campaign, state) {
      | ({selected: true}, {name: ""}) => raise(MakeUpdateCampaignsInputs_NameEmpty)
      | ({selected: true}, _) =>
        Some((
          campaign.id,
          UpdateCampaignMutation.makeInputObjectInputUpdatePromotionCampaign(
            ~name=state.name,
            ~startDate,
            ~endDate,
            (),
          ),
        ))
      | _ => None
      }
    )
  let discountsInput =
    state.discountedProducts
    ->Array.reverse
    ->Array.map(product =>
      switch product->PromotionEditDiscountedProduct.isValid(~selectedPriceName=state.priceName) {
      | true =>
        UpdateCampaignMutation.makeInputObjectInputPromotionCampaignDiscounts(
          ~variantCku=product.cku->Scalar.CKU.serialize,
          ~value=product.discount.amount,
          ~kind=product.discount.kind,
          (),
        )
      | _ => raise(MakeUpdateCampaignsInputs_InvalidDiscountedProduct)
      }
    )

  (rootCampaignsInputs, discountsInput)
}

let useUpdateGlobalCampaign = () => {
  let mutate = UpdateCampaignMutation.use()->fst

  React.useCallback0((_, state: Lenses.state) =>
    switch makeUpdateCampaignsInputs(~state) {
    | ([], _) | (_, []) => raise(UpdateGlobalCampaignMutation_InvalidInputs)
    | (rootCampaignsInputs, discountsInput) =>
      rootCampaignsInputs
      ->Array.map(((id, input)) =>
        mutate(UpdateCampaignMutation.makeVariables(~id, ~input, ~discountsInput, ()))
        ->ApolloHelpers.mutationPromiseToFutureResult
        ->Future.mapOk(({UpdateCampaignMutation.updatePromotionCampaign: {cku}}) => Some(cku))
      )
      ->ApolloHelpers.mergeFutureResults
    }
  )
}

let useStartGlobalCampaign = () => {
  let mutate = StartCampaignMutation.use()->fst

  React.useCallback0((_, state: Lenses.state) =>
    state.rootCampaigns
    ->Array.keepMap(campaign =>
      switch campaign {
      | {selected: true} =>
        Some(
          mutate(StartCampaignMutation.makeVariables(~id=campaign.id, ()))
          ->ApolloHelpers.mutationPromiseToFutureResult
          ->Future.mapOk(({StartCampaignMutation.startPromotionCampaign: {cku}}) => Some(cku)),
        )
      | _ => None
      }
    )
    ->ApolloHelpers.mergeFutureResults
  )
}

let useStartCampaign = (~id: string) => {
  let mutate = StartCampaignMutation.use()->fst

  React.useCallback0((status: PromotionStatus.t) =>
    switch status {
    | #NOT_PROGRAMMED =>
      mutate(StartCampaignMutation.makeVariables(~id, ()))
      ->ApolloHelpers.mutationPromiseToFutureResult
      ->Future.mapOk(({StartCampaignMutation.startPromotionCampaign: {cku}}) => Some(cku))
    | _ => raise(StartCampaignMutation_InvalidStatus)
    }
  )
}

let useStopCampaign = (~id: string) => {
  let mutate = StopCampaignMutation.use()->fst

  React.useCallback0((status: PromotionStatus.t) =>
    switch status {
    | #PROGRAMMED | #ONGOING =>
      mutate(StopCampaignMutation.makeVariables(~id, ()))
      ->ApolloHelpers.mutationPromiseToFutureResult
      ->Future.mapOk(({StopCampaignMutation.stopPromotionCampaign: {cku}}) => Some(cku))
    | _ => raise(StopCampaignMutation_InvalidStatus)
    }
  )
}
