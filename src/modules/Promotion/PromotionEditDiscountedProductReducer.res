open PromotionEditDiscountedProduct

exception PromotionEditDiscountedProductReducer_CouldNotRemoveProduct
exception PromotionEditDiscountedProductReducer_CouldNotUpdateDiscount
exception PromotionEditDiscountedProductReducer_CouldReplicateDiscount

type state = array<PromotionEditDiscountedProduct.t>

type action =
  | Reset(state)
  | ProductsAdded(array<PromotionEditDiscountedProduct.t>)
  | ProductRemoved(string)
  | ProductDiscountUpdated(string, float, Discount.kind)
  | ProductDiscountReplicated(string)

let make = (state, action) =>
  switch action {
  | Reset(state) => state
  | ProductsAdded(products) =>
    products->Array.reduce(state, (acc, product) =>
      switch state->Array.getBy(current => current.cku === product.cku) {
      | Some(oldProduct) =>
        let updatedProduct = {
          ...product,
          discount: oldProduct.discount,
        }

        Array.concat([updatedProduct], acc->Array.keep(current => current.cku !== product.cku))
      | _ => Array.concat([product], acc)
      }
    )

  | ProductRemoved(productCku) =>
    state->Array.some(current => current.cku === productCku)
      ? state->Array.keep(current => current.cku !== productCku)
      : raise(PromotionEditDiscountedProductReducer_CouldNotRemoveProduct)

  | ProductDiscountUpdated(productCku, discountAmount, discountKind) =>
    state->Array.some(current => current.cku === productCku)
      ? state->Array.map(current =>
          current.cku === productCku
            ? {
                ...current,
                discount: {
                  id: current.discount.id,
                  amount: discountAmount,
                  kind: discountKind,
                },
              }
            : current
        )
      : raise(PromotionEditDiscountedProductReducer_CouldNotUpdateDiscount)

  | ProductDiscountReplicated(productCku) =>
    switch state->Array.getBy(current => current.cku === productCku) {
    | Some({discount: {amount, kind}}) =>
      state->Array.map(current => {
        ...current,
        discount: {
          id: current.discount.id,
          amount,
          kind,
        },
      })

    | _ => raise(PromotionEditDiscountedProductReducer_CouldReplicateDiscount)
    }
  }
