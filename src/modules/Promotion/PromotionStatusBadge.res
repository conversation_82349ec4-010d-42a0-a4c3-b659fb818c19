open Intl

@react.component
let make = (~status: PromotionStatus.t) =>
  switch status {
  | #DRAFT => <Badge variation=#neutral> {t("Draft")->React.string} </Badge>
  | #PROGRAMMED => <Badge variation=#information> {t("Scheduled")->React.string} </Badge>
  | #NOT_PROGRAMMED => <Badge variation=#danger> {t("Unscheduled")->React.string} </Badge>
  | #ONGOING => <Badge variation=#success> {t("Ongoing")->React.string} </Badge>
  | #STOPPED => <Badge variation=#danger> {t("Stopped")->React.string} </Badge>
  | #EXPIRED => <Badge variation=#warning> {t("Expired")->React.string} </Badge>
  | #ARCHIVED => React.null
  }

let make = React.memo(make)
