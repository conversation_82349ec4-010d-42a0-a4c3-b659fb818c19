open Intl

module Mutations = PromotionEditFormMutations

type variation = [#menuItem | #actionBar]

@react.component
let make = (~variation: variation, ~id, ~status: PromotionStatus.t) => {
  let scope = Auth.useScope()
  let startCampaign = Mutations.useStartCampaign(~id)
  let stopCampaign = Mutations.useStopCampaign(~id)

  switch status {
  | #PROGRAMMED | #NOT_PROGRAMMED | #ONGOING =>
    switch variation {
    | #menuItem =>
      <Inline align=#end>
        <Menu variation=#more_round>
          {switch status {
          | #NOT_PROGRAMMED =>
            <MenuItem
              content=Text(t("Schedule")) action=Callback(() => startCampaign(status)->ignore)
            />
          | #PROGRAMMED | #ONGOING =>
            let text = MenuItem.Text(status === #PROGRAMMED ? t("Unschedule") : t("Stop"))

            <MenuItem content=text action=Callback(() => stopCampaign(status)->ignore) />
          | _ => React.null
          }}
        </Menu>
      </Inline>
    | #actionBar =>
      switch (status, scope) {
      | (#NOT_PROGRAMMED, Single(_)) =>
        <Button variation=#primary size=#medium onPress={_ => startCampaign(status)->ignore}>
          {t("Schedule")->React.string}
        </Button>
      | (#PROGRAMMED | #ONGOING, Single(_)) =>
        let label = status === #PROGRAMMED ? t("Unschedule") : t("Stop")

        <Button variation=#danger size=#medium onPress={_ => stopCampaign(status)->ignore}>
          {label->React.string}
        </Button>
      | _ => React.null
      }
    }
  | _ => React.null
  }
}

let make = React.memo(make)
