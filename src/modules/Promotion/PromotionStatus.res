open Intl

exception PromotionStatus__StatusFromRawValueNotFound

type t = [
  | #DRAFT
  | #PROGRAMMED
  | #NOT_PROGRAMMED
  | #ONGOING
  | #STOPPED
  | #EXPIRED
  | #ARCHIVED
]

let toRawValue = status =>
  switch status {
  | #DRAFT => "DRAFT"
  | #PROGRAMMED => "PROGRAMMED"
  | #NOT_PROGRAMMED => "NOT_PROGRAMMED"
  | #ONGOING => "ONGOING"
  | #STOPPED => "STOPPED"
  | #EXPIRED => "EXPIRED"
  | #ARCHIVED => "ARCHIVED"
  }

let fromRawValue = status =>
  switch status {
  | "DRAFT" => #DRAFT
  | "PROGRAMMED" => #PROGRAMMED
  | "NOT_PROGRAMMED" => #NOT_PROGRAMMED
  | "ONGOING" => #ONGOING
  | "STOPPED" => #STOPPED
  | "EXPIRED" => #EXPIRED
  | "ARCHIVED" => #ARCHIVED
  | _ => raise(PromotionStatus__StatusFromRawValueNotFound)
  }

let toString = status =>
  switch status {
  | #DRAFT => t("Draft")
  | #PROGRAMMED => t("Scheduled")
  | #NOT_PROGRAMMED => t("Unscheduled")
  | #ONGOING => t("Ongoing")
  | #STOPPED => t("Stopped")
  | #EXPIRED => t("Expired")
  | #ARCHIVED => t("Archived")
  }

let statusesWeight: array<(t, int)> = [
  (#DRAFT, 0),
  (#ONGOING, 1),
  (#PROGRAMMED, 2),
  (#STOPPED, 3),
  (#NOT_PROGRAMMED, 4),
  (#EXPIRED, 5),
  (#ARCHIVED, 6),
]

let globalFromStatuses = (statuses: array<t>) =>
  statuses
  ->SortArray.stableSortBy((current, next) => {
    switch (
      statusesWeight->Array.getBy(((status, _)) => status === current),
      statusesWeight->Array.getBy(((status, _)) => status === next),
    ) {
    | (Some((_, weightCurrent)), Some((_, weightNext))) => weightCurrent - weightNext
    | _ => 0
    }
  })
  ->Array.get(0)
