open Intl
open PromotionEditDiscountedProduct
open PromotionEditDiscountedProductReducer

let getRetailPriceValue = (~retailPrices: option<array<ProductPrice.t>>, ~selectedPriceName) =>
  retailPrices->Option.flatMap(prices =>
    prices
    ->Array.getBy(price => Some(price.name) === selectedPriceName)
    ->Option.map(price => price.value)
  )

module Row = {
  type t = PromotionEditDiscountedProduct.t
  let keyExtractor = row => row.cku
}

let tableColumns = (~editable, ~selectedPriceName, ~allowRowReplication, ~onRequestDispatch) => [
  {
    Table.key: "reference",
    name: t("Product and description"),
    layout: {minWidth: 250.->#px, width: 40.->#pct, margin: #large},
    render: ({data: {cku, name, description}, errorMessage}) =>
      <LegacyProductReferenceTableCell cku name description ?errorMessage openNewTab=true />,
  },
  {
    key: "purchase-price",
    name: t("Purchase price") ++ " " ++ t("VAT excl."),
    layout: {minWidth: 120.->#px},
    render: ({data: {purchasePrice, bulkUnit}}) =>
      <TextStyle>
        {(purchasePrice->Intl.currencyFormat(
          ~currency=#EUR,
          ~minimumFractionDigits=3,
          ~maximumFractionDigits=3,
        ) ++ bulkUnit->Option.mapWithDefault("", unit => `/${unit}`))->React.string}
      </TextStyle>,
  },
  {
    key: "retail-price",
    name: t("Retail price") ++ " " ++ t("VAT incl."),
    layout: {minWidth: 120.->#px},
    render: ({data: {cku, name, retailPrices, bulkUnit}}) =>
      switch selectedPriceName {
      | Some(selectedPriceName) =>
        switch getRetailPriceValue(~retailPrices, ~selectedPriceName=Some(selectedPriceName)) {
        | Some(retailPriceValue) =>
          <TextStyle key={cku ++ name}>
            {(retailPriceValue->Intl.currencyFormat(~currency=#EUR) ++
              bulkUnit->Option.mapWithDefault("", unit => `/${unit}`))->React.string}
          </TextStyle>
        | None =>
          <TooltipIcon key={cku ++ name} variation=#info>
            <Tooltip.Span
              text={t(
                "One of the promotional campaign linked shops does not have a retail price entered for that product on the selected price list.",
              )}
            />
          </TooltipIcon>
        }
      | _ =>
        <TooltipIcon key={cku ++ name} variation=#info>
          <Tooltip.Span text={t("Please select the price list first to see the retail price.")} />
        </TooltipIcon>
      },
  },
  {
    key: "discount",
    name: t("Discount"),
    layout: {minWidth: 120.->#px},
    render: ({data: {cku, discount, bulkUnit}}) => {
      let onRequestUpdate = current =>
        ProductDiscountUpdated(cku, current.Discount.amount, current.kind)->onRequestDispatch
      let onRequestReplicate = () => ProductDiscountReplicated(cku)->onRequestDispatch

      <PromotionEditDiscountedProductDiscountTableCell
        editable
        disabled={selectedPriceName->Option.isNone}
        discount
        bulkUnit
        onRequestUpdate
        onRequestReplicate=?{allowRowReplication ? Some(onRequestReplicate) : None}
      />
    },
  },
  {
    key: "discounted-price",
    name: t("Discounted price") ++ " " ++ t("VAT incl."),
    layout: {minWidth: 120.->#px},
    render: ({data: {retailPrices, discount: {amount, kind}, bulkUnit}}) => {
      let retailPriceValue = getRetailPriceValue(~retailPrices, ~selectedPriceName)
      let discountedPrice =
        retailPriceValue->Option.map(value => ProductPrice.makeDiscounted(~value, ~amount, ~kind))

      <TextStyle
        variation={switch retailPriceValue {
        | Some(_) if retailPriceValue === discountedPrice => #normal
        | Some(_) if discountedPrice < Some(0.) => #negative
        | Some(0.) | None => #normal
        | _ => #neutral
        }}>
        {(discountedPrice->Option.getWithDefault(0.)->Intl.currencyFormat(~currency=#EUR) ++
          bulkUnit->Option.mapWithDefault("", unit => `/${unit}`))->React.string}
      </TextStyle>
    },
  },
  {
    key: "actions",
    layout: {width: 0.->#fr, alignX: #flexEnd},
    render: ({data: {cku}}) =>
      if editable {
        <RoundButton icon=#delete_light onPress={_ => ProductRemoved(cku)->onRequestDispatch} />
      } else {
        React.null
      },
  },
]

let make = TableView.make
