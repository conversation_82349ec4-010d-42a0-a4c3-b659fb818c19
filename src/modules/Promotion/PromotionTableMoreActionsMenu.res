open Intl

let {makeCsvFilename, queryAndMakeCsvBlob, makeExcelFilename, queryAndMakeExcelBlob} = module(
  PromotionSheet
)

@react.component
let make = (~cku, ~id, ~name, ~startDate, ~notifier) => {
  let (csvDownloadRequested, setCsvDownloadRequested) = React.useState(() => false)
  let (excelDownloadRequested, setExcelDownloadRequested) = React.useState(() => false)

  let processBlobAndOpenFile = (future, ~filename) =>
    future
    ->Future.flatMap(result => {
      switch result {
      | Ok(blob) => blob->TriggerDownload.fromBlob(~filename)
      | Error(_) => Future.value(Error())
      }
    })
    ->Future.tap(result => {
      setCsvDownloadRequested(_ => false)
      switch result {
      | Ok() => ()
      | Error() => notifier.Notifier.add(Error("Something went wrong"), ())
      }
    })

  React.useEffect1(() =>
    if csvDownloadRequested {
      let filename = makeCsvFilename(name, ~date=startDate)
      let future = queryAndMakeCsvBlob(~promotionCampaignId=id)->processBlobAndOpenFile(~filename)

      // NOTE - We don't cancel graphql query here, apollo doesn't handle it yet
      // (maybe use abort method ?)
      Some(() => future->Future.cancel)
    } else {
      None
    }
  , [csvDownloadRequested])

  React.useEffect1(() =>
    if excelDownloadRequested {
      let filename = makeExcelFilename(name, ~date=startDate)
      let future = queryAndMakeExcelBlob(~promotionCampaignId=id)->processBlobAndOpenFile(~filename)

      // NOTE - We don't cancel graphql query here, apollo doesn't handle it yet
      // (maybe use abort method ?)
      Some(() => future->Future.cancel)
    } else {
      None
    }
  , [excelDownloadRequested])

  <Menu variation=#more_round>
    <MenuItem
      content=Text(t("See"))
      action=OpenLink(Route(Promotion->LegacyRouter.routeToPathname ++ "/" ++ cku))
    />
    <MenuItem
      content=Text(t("Download CSV")) action=Callback(() => setCsvDownloadRequested(_ => true))
    />
    <MenuItem
      content=Text(t("Download Excel")) action=Callback(() => setExcelDownloadRequested(_ => true))
    />
  </Menu>
}

let make = React.memo(make)
