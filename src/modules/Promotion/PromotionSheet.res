open Intl

type queryAndMakeError = QueryFailure | NoDataFailure | WriteBlobFailure

module Query = %graphql(`
  query PromotionSheetQuery($id: ID!, $first: Int, $after: String) {
    promotionCampaign(id: $id) {
      startDate
      discounts(first: $first, after: $after) {
        pageInfo {
          endCursor
          hasNextPage
        }
        edges {
          node {
            value
            kind @ppxOmitFutureValue
            variant {
              id
              stockKeepingUnit
            }
          }
        }
      }
    }
  }
`)

type kind = [#PERCENT | #CURRENCY]

type promotionCampaignDiscount = {
  value: float,
  kind: kind,
  variantId: string,
  variantStockKeepingUnit: option<string>,
}

let makeFilename = (name, ~date, ~extension) =>
  t("promotionsexport") ++
  "_" ++
  Array.joinWith(name->Js.String2.split(" "), "_", x => x) ++
  "_" ++
  Array.joinWith(date->dateTimeFormat->Js.String2.split("/"), "-", x => x) ++
  "." ++
  extension

let makeCsvFilename = makeFilename(~extension=Sheet.csvFileExtension)
let makeExcelFilename = makeFilename(~extension=Sheet.excelFileExtension)

let makeRows = promotionCampaign => {
  let discountHeaderRow = [t("Variant ID"), "SKU", t("Promotional value"), t("Promotional type")]

  let discountRows = promotionCampaign->Array.map(discount => [
    discount.variantId,
    discount.variantStockKeepingUnit->Option.getWithDefault(""),
    discount.value->Float.toString->Js.String2.replace(".", ","),
    switch discount.kind {
    | #PERCENT => "%"
    | #CURRENCY => "eur"
    },
  ])

  Array.concatMany([[discountHeaderRow], discountRows])
}

let query = (~after=?, ~promotionCampaignId, ~first) =>
  ApolloConfig.makeClient().query(
    ~query=module(Query),
    Query.makeVariables(~id=promotionCampaignId, ~first, ~after?, ()),
  )->FuturePromise.fromPromise

let rec queryPromotionDiscountProducts = (
  ~promotionCampaignId,
  ~previousResult=?,
  ~first=50,
  ~after=?,
  (),
) =>
  query(~promotionCampaignId, ~first, ~after?)->Future.flatMap(result =>
    switch result {
    | Ok(Ok({data: {promotionCampaign: Some(campaigns)}, error: None})) =>
      let promotionCampaign = switch previousResult {
      | Some(previousResult) =>
        previousResult->Array.concat(
          campaigns.discounts.edges->Array.map(({node: discount}) => {
            value: discount.value,
            kind: discount.kind,
            variantId: discount.variant.id,
            variantStockKeepingUnit: discount.variant.stockKeepingUnit,
          }),
        )
      | None =>
        campaigns.discounts.edges->Array.map(({node: discount}) => {
          value: discount.value,
          kind: discount.kind,
          variantId: discount.variant.id,
          variantStockKeepingUnit: discount.variant.stockKeepingUnit,
        })
      }

      switch campaigns {
      | {discounts: {pageInfo: {hasNextPage: Some(true), endCursor: Some(endCursor)}}} =>
        queryPromotionDiscountProducts(
          ~promotionCampaignId,
          ~previousResult=promotionCampaign,
          ~first,
          ~after=endCursor,
          (),
        )
      // NOTE - following GraphQL Relay specs, endCursor can't be None if hasNextPage is true
      | {discounts: {pageInfo: {hasNextPage: Some(true), endCursor: None}}} =>
        Future.value(Error(QueryFailure))
      | _ => Future.value(Ok(Some(promotionCampaign)))
      }
    | Ok(Ok({data: {promotionCampaign: None}, error: None})) => Future.value(Ok(None))
    | _ => Future.value(Error(QueryFailure))
    }
  )

let makeCsvPlainText = promotionCampaign => Sheet.makeCsvPlainText(promotionCampaign->makeRows)

let makeCsvBlob = promotionCampaign =>
  promotionCampaign->makeCsvPlainText->Result.map(Sheet.makeCsvBlobFromPlainText)

let queryAndMakeCsvBlob = (~promotionCampaignId) =>
  queryPromotionDiscountProducts(~promotionCampaignId, ())->Future.map(results =>
    switch results {
    | Ok(Some([])) | Ok(None) => Error(NoDataFailure)
    | Ok(Some(discountProducts)) =>
      switch discountProducts->makeCsvBlob {
      | Ok(csvBlob) => Ok(csvBlob)
      | Error(_) => Error(WriteBlobFailure)
      }
    | Error(error) => Error(error)
    }
  )

let makeExcelBlob = promotionCampaign =>
  promotionCampaign
  ->makeRows
  ->Sheet.makeExcelBlob(~worksheetName=t("Promotional campaigns"))
  ->Future.mapError(_ => WriteBlobFailure)

let queryAndMakeExcelBlob = (~promotionCampaignId) =>
  queryPromotionDiscountProducts(~promotionCampaignId, ())->Future.flatMap(result =>
    switch result {
    | Ok(Some([])) | Ok(None) => Future.value(Error(NoDataFailure))
    | Ok(Some(discountProducts)) => discountProducts->makeExcelBlob
    | Error(error) => Future.value(Error(error))
    }
  )
