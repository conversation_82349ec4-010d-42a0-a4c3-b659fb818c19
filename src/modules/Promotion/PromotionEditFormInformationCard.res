open Intl

module PromotionEditPriceNameFormSelect = {
  @react.component
  let make = React.memo(() => {
    let {validation, submission, values: {priceName}} = PromotionEditForm.useFormState()
    let dispatch = PromotionEditForm.useFormDispatch()

    let priceNameError = switch validation {
    | Ok() => None
    | Error(errors) =>
      errors
      ->Array.keepMap(((field, error)) =>
        switch (field, error) {
        | (PromotionEditForm.Schema.Field(PromotionEditForm.Lenses.PriceName), error) => Some(error)
        | _ => None
        }
      )
      ->Array.get(0)
    }

    let onChange = React.useCallback0(value =>
      FieldValueChanged(PriceName, _ => value != "" ? Some(value) : None)->dispatch
    )

    let errorMessage = switch (priceNameError, submission) {
    | (Some(message), Failed(_)) => Some(t(message))
    | _ => None
    }

    <Stack space=#xsmall>
      <PromotionEditPriceNameSelect
        required=true ?errorMessage value={priceName->Option.getWithDefault("")} onChange
      />
      <Box spaceTop=#small>
        <Banner
          compact=false
          textStatus=Warning(
            t(
              "Beware ! Once the draft is saved, you will no longer be able to edit the price list.",
            ),
          )
        />
      </Box>
    </Stack>
  })
}

@react.component
let make = (~editable, ~promotionCreated) => {
  let {name, priceName, period} = PromotionEditForm.useFormState().values

  <Card variation=#common title={t("Information")}>
    {switch (editable, promotionCreated, priceName) {
    | (true, false, _) =>
      <Stack space=#large>
        <PromotionEditForm.InputText
          field=Name label={t("Name of the campaign")} placeholder={t("Wine fair 2020")}
        />
        <PromotionEditPriceNameFormSelect />
      </Stack>
    | (true, true, Some(priceName)) =>
      <Stack space=#xxsmall>
        <PromotionEditForm.InputText
          field=Name label={t("Name of the campaign")} placeholder={t("Wine fair 2020")}
        />
        <Box spaceBottom=#normal />
        <InlineText>
          <TextStyle variation=#normal> {(t("Price list") ++ " ")->React.string} </TextStyle>
          <TextStyle> {priceName->React.string} </TextStyle>
        </InlineText>
      </Stack>
    | (_, _, Some(priceName)) =>
      <Stack space=#xxsmall>
        <InlineText>
          <TextStyle variation=#normal> {(t("Campaign") ++ " ")->React.string} </TextStyle>
          <TextStyle weight=#semibold> {name->React.string} </TextStyle>
        </InlineText>
        <InlineText>
          <TextStyle variation=#normal> {(t("Price list") ++ " ")->React.string} </TextStyle>
          <TextStyle> {priceName->React.string} </TextStyle>
        </InlineText>
      </Stack>
    | _ => React.null
    }}
    <Divider />
    {switch (editable, period) {
    | (true, _) =>
      <PromotionEditForm.InputDateRange
        field=Period label={t("Dates of the campaign")} placeholder={t("Select a period")}
      />
    | (false, Some((startDate, endDate))) =>
      <Stack space=#xxsmall>
        <InlineText>
          <TextStyle variation=#normal>
            {(t("Start of the campaign at the") ++ " ")->React.string}
          </TextStyle>
          <TextStyle> {startDate->Intl.dateTimeFormat(~dateStyle=#short)->React.string} </TextStyle>
        </InlineText>
        <InlineText>
          <TextStyle variation=#normal>
            {(t("End of the campaign at the") ++ " ")->React.string}
          </TextStyle>
          <TextStyle> {endDate->Intl.dateTimeFormat(~dateStyle=#short)->React.string} </TextStyle>
        </InlineText>
      </Stack>
    | _ => React.null
    }}
  </Card>
}

let make = React.memo(make)
