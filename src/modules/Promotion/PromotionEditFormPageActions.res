open Intl

module Mutations = PromotionEditFormMutations

@react.component
let make = (
  ~status: PromotionStatus.t,
  ~promotionCreated,
  ~editing,
  ~onRequestEditing,
  ~onRequestCsvDownload,
  ~onRequestExcelDownload,
) => {
  let (canGoBack, onGoBack) = Navigation.useGoBack()
  let state = PromotionEditForm.useFormState()
  let createGlobalCampaign = Mutations.useCreateGlobalCampaign()
  let updateGlobalCampaign = Mutations.useUpdateGlobalCampaign()
  let startGlobalCampaign = Mutations.useStartGlobalCampaign()

  // Handles page global edition mode
  React.useEffect2(() => {
    switch status {
    | #DRAFT | #PROGRAMMED | #NOT_PROGRAMMED =>
      switch (state.status, state.submission, editing) {
      | (_, Succeeded(_), true) => onRequestEditing(false)
      | (Pristine, _, _) | (_, Requested, _) | (_, _, true) => ()
      | _ => onRequestEditing(true)
      }
    | _ => ()
    }
    None
  }, (state.status, state.submission))

  <Inline
    space={switch Auth.useScope() {
    | Single(_) => #small
    | _ => #none
    }}>
    {switch (editing, status) {
    | (false, #PROGRAMMED) | (false, #NOT_PROGRAMMED) =>
      <Menu>
        {switch status {
        | #NOT_PROGRAMMED | #PROGRAMMED =>
          <MenuItem
            content=Text(t("Edit campaign")) action=Callback(() => onRequestEditing(true))
          />
        | _ => React.null
        }}
        <MenuItem
          content=Text(t("Download CSV")) action=Callback(() => onRequestCsvDownload(state.values))
        />
        <MenuItem
          content=Text(t("Download Excel"))
          action=Callback(() => onRequestExcelDownload(state.values))
        />
      </Menu>
    | _ => React.null
    }}
    {switch (status, editing, promotionCreated) {
    | (_, true, _) =>
      <Inline space=#small>
        <PromotionEditForm.ResetButton
          text={t("Discard")}
          variation=#neutral
          size=#medium
          onPress={_ =>
            switch promotionCreated {
            | false if canGoBack => onGoBack()
            | _ => onRequestEditing(false)
            }}
        />
        <PromotionEditForm.SubmitButton
          text={t(!promotionCreated ? "Create draft" : "Save")}
          variation=#success
          size=#medium
          onSubmit={!promotionCreated ? createGlobalCampaign : updateGlobalCampaign}
        />
      </Inline>
    | (#DRAFT, _, true) =>
      <PromotionEditForm.SubmitButton
        text={t("Schedule")} variation=#primary size=#medium onSubmit=startGlobalCampaign
      />
    | (_, _, true) =>
      switch state.values.rootCampaigns[0] {
      | Some({id, status: Some(status)}) => <PromotionEditActions variation=#actionBar id status />
      | _ => React.null
      }
    | _ => React.null
    }}
  </Inline>
}

let make = React.memo(make)
