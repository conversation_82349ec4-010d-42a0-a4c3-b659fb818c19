open Intl

module Reducer = PromotionEditDiscountedProductReducer
module PromotionTable = PromotionEditDiscountedProductTable

module AddProductModalButton = {
  @react.component
  let make = (~onRequestModalOpen) => {
    let (ref, hovered) = Hover.use()

    <Touchable ref onPress={_ => onRequestModalOpen()}>
      <OverlayTriggerView preset=#inputField({required: false}) icon=#search hovered>
        <TextStyle variation=#normal> {t("Add product")->React.string} </TextStyle>
      </OverlayTriggerView>
    </Touchable>
  }
}

@react.component
let make = (
  ~editable,
  ~products,
  ~selectedPriceName,
  ~renderPageActions,
  ~onRequestProductsUpdate,
) => {
  let ({Nav.Context.opened: navOpened}, _) = Nav.Context.use()

  let (rows, dispatch) = React.useReducer(Reducer.make, products)
  let (pickerOpened, setPickerOpened) = React.useState(() => false)

  ReactUpdateEffect.use2(() => {
    Reset(products)->dispatch
    None
  }, (products, selectedPriceName))

  ReactUpdateEffect.use1(() => {
    onRequestProductsUpdate(rows)
    None
  }, [rows])

  let onPickProducts = React.useCallback0(newProducts => {
    ProductsAdded(newProducts)->dispatch
  })
  let onRequestClose = React.useCallback0(() => setPickerOpened(_ => false))

  let renderActionsStart = () =>
    if editable {
      <AddProductModalButton onRequestModalOpen={() => setPickerOpened(_ => true)} />
    } else {
      React.null
    }

  let columns = React.useMemo3(
    () =>
      PromotionTable.tableColumns(
        ~editable,
        ~selectedPriceName,
        ~allowRowReplication=products->Array.length > 1,
        ~onRequestDispatch=dispatch,
      ),
    (editable, selectedPriceName, products->Array.length),
  )

  let disabledIds = rows->Array.map(({cku}) => cku)
  let rowsErrors = rows->Array.keepMap(row =>
    if row.retailPrices === None {
      Some({
        Table.key: row.cku,
        message: t("This product is not compatible with the selected price list."),
      })
    } else if row.discount.amount === 0. {
      Some({
        Table.key: row.cku,
        message: t("The discount of this product must have a positive amount."),
      })
    } else {
      None
    }
  )

  <>
    <Card variation=#unset>
      {if editable {
        <Box spaceX=#large>
          <AddProductModalButton onRequestModalOpen={() => setPickerOpened(_ => true)} />
        </Box>
      } else {
        React.null
      }}
      <PromotionTable
        columns
        data=AsyncData.Done(Ok(rows))
        erroredRowsMap=rowsErrors
        keyExtractor=PromotionTable.Row.keyExtractor
        hideCard=true
        maxHeight=500.
        placeholderEmptyState={<Box
          spaceX=#large
          spaceY=#xlarge
          style={StyleX.style(~backgroundColor=Colors.neutralColor05, ())}>
          <Inline align=#center>
            <TextStyle variation=#normal size=#small align=#center>
              {t("Please add products to create your promotional campaign.")->React.string}
            </TextStyle>
          </Inline>
        </Box>}
      />
      <Box />
    </Card>
    <PromotionEditDiscountedProductPickerModalTable
      opened=pickerOpened selectedPriceName disabledIds onCommit=onPickProducts onRequestClose
    />
    <PageBottomActionsBar
      displayThreshold=160. renderStart=renderActionsStart renderEnd=renderPageActions navOpened
    />
  </>
}

let make = React.memoCustomCompareProps(make, (oldProps, newProps) =>
  oldProps.products === newProps.products &&
  oldProps.selectedPriceName === newProps.selectedPriceName &&
  oldProps.editable === newProps.editable
)
