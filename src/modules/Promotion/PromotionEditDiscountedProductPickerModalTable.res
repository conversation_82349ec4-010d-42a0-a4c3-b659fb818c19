open Intl

module Query = %graphql(`
  query variantsDistinctOnCku($search: String, $after: String, $first: Int) {
    variantsDistinctOnCku(search: $search, after: $after, first: $first) {
      pageInfo {
        endCursor
        hasNextPage
      }
      edges {
        node {
          id
          cku
          createdAt
          formattedName
          name
          product {
            id
            name
            kind @ppxOmitFutureValue
            color @ppxOmitFutureValue
            wineType @ppxOmitFutureValue
            whiteWineType @ppxOmitFutureValue
            beerType
            producer
            designation
            family
            region
            country
            tax { value }
          }
          stockKeepingUnit
          internalCode
          priceLookUpCode
          supplier { companyName }
          formattedCategory
          alcoholVolume
          capacityUnit
          bulk
          purchasedPrice
          variantPrices(first: 50) {
            edges {
              node {
                valueIncludingTax
                price { name }
              }
            }
          }
        }
      }
    }
  }
`)

module Row = {
  type retailPrice = {
    priceName: string,
    valueIncludingTax: float,
  }
  type t = {
    id: string,
    cku: string,
    createdAt: Js.Date.t,
    formattedName: string,
    productKind: CatalogProduct.Kind.t,
    information: CatalogProduct.Information.t,
    bulkUnit: option<string>,
    purchasePrice: float,
    retailPrices: array<retailPrice>,
  }

  let keyExtractor = row => row.cku
}

let rowsFromEdgesData = data =>
  data->Array.map(edge => {
    Row.id: edge.Query.node.id,
    cku: edge.node.cku,
    createdAt: edge.node.createdAt,
    formattedName: edge.node.formattedName,
    productKind: edge.node.product.kind,
    information: {
      productName: edge.node.product.name,
      variantName: edge.node.name,
      sku: ?edge.node.stockKeepingUnit,
      plu: ?edge.node.priceLookUpCode->Option.map(Int.toString),
      internalCode: ?edge.node.internalCode,
      color: ?edge.node.product.color,
      producerName: ?edge.node.product.producer,
      designation: ?edge.node.product.designation,
      productFamily: ?edge.node.product.family,
      wineType: ?edge.node.product.wineType,
      whiteWineType: ?edge.node.product.whiteWineType,
      beerType: ?edge.node.product.beerType,
      region: ?edge.node.product.region,
      country: edge.node.product.country->Option.getWithDefault("—"),
      categoryName: edge.node.formattedCategory->Option.getWithDefault("—"),
      supplierName: ?edge.node.supplier->Option.map(supplier => supplier.companyName),
      alcoholVolume: ?edge.node.alcoholVolume->Option.map(alcoholVolume =>
        alcoholVolume->Intl.decimalFormat(~maximumFractionDigits=1) ++ "°"
      ),
    },
    bulkUnit: switch (edge.node.capacityUnit, edge.node.bulk->Option.getWithDefault(false)) {
    | (Some(unit), true) => Some(unit)
    | _ => None
    },
    purchasePrice: edge.node.purchasedPrice->Option.getWithDefault(0.),
    retailPrices: edge.node.variantPrices.edges->Array.keepMap(({node: variantPrice}) =>
      switch variantPrice.price {
      | Some(price) =>
        Some({
          Row.priceName: price.name,
          valueIncludingTax: variantPrice.valueIncludingTax,
        })
      | _ => None
      }
    ),
  })

let columns = [
  {
    Table.key: "reference",
    name: t("Product"),
    render: ({data: product, disabled, errorMessage}) => {
      let hoursThreshold = 24
      let badgeNew =
        DateHelpers.diffInHours(Js.Date.make(), product.Row.createdAt) <= hoursThreshold
          ? Some({
              ProductReferenceTableCell.variation: #information,
              text: t("New"),
            })
          : None
      <ProductReferenceTableCell
        disabled
        badge=?badgeNew
        productKind=product.productKind
        information=product.information
        ?errorMessage
      />
    },
  },
]

module PickerModal = {
  @react.component
  let make = React.memo((
    ~children,
    ~opened,
    ~selectedPriceName,
    ~selectedProducts,
    ~onCommit,
    ~onRequestClose,
  ) => {
    let renderStartText = () => {
      let length = selectedProducts->Array.length
      let shouldBePrimaryView = length > 0
      let formattedText =
        `${length->Int.toString} ` ++ t(isPlural(length) ? "selected products" : "selected product")

      <Inline>
        <TextStyle
          weight={shouldBePrimaryView ? #strong : #regular}
          variation={shouldBePrimaryView ? #primary : #neutral}>
          {formattedText->React.string}
        </TextStyle>
      </Inline>
    }

    let onCommit = () =>
      onCommit(
        selectedProducts->Array.map(row =>
          PromotionEditDiscountedProduct.make(
            ~id=row.Row.id,
            ~cku=row.cku,
            ~name=row.formattedName,
            ~description=CatalogProduct.Information.formatDescription(
              ~productKind=row.productKind,
              ~information=row.information,
              (),
            ),
            ~stockKeepingUnit=row.information.sku,
            ~purchasePrice=row.purchasePrice,
            ~bulkUnit=row.bulkUnit,
            ~retailPricesData=row.retailPrices->Array.map(
              ({priceName, valueIncludingTax}) => (priceName, valueIncludingTax),
            ),
            ~selectedPriceName,
          )
        ),
      )

    <Modal
      title={t("Product append")}
      variation=#secondary
      compact=false
      allowCommit={selectedProducts->Array.length > 0}
      backgroundColor=Colors.neutralColor00
      renderStartText
      abortButtonText={t("Cancel")}
      commitButtonText={t("Add products")}
      commitButtonVariation=#primary
      commitButtonCallback=onCommit
      opened
      onRequestClose>
      children
    </Modal>
  })
}

let makeVariables = (~search, ~after=?, ()) => Query.makeVariables(~first=20, ~after?, ~search, ())

@react.component
let make = (~opened, ~selectedPriceName, ~disabledIds=[], ~onCommit, ~onRequestClose) => {
  let (selectedProducts, setSelectedProducts) = React.useState(() => [])
  let (searchQuery, setSearchQuery) = React.useState(() => "")

  let query = Query.use(
    makeVariables(~search=searchQuery, ()),
    ~notifyOnNetworkStatusChange=true,
    ~fetchPolicy=NetworkOnly,
    ~nextFetchPolicy=CacheFirst,
  )

  let asyncResult =
    query->ApolloHelpers.useQueryResultToAsyncResultWithVariablesReloading1(searchQuery)
  let data =
    asyncResult->AsyncResult.mapOk(data => rowsFromEdgesData(data.variantsDistinctOnCku.edges))

  React.useEffect1(() => {
    if !opened && searchQuery !== "" {
      setSearchQuery(_ => "")
    }
    None
  }, [opened])

  let onLoadMore = React.useCallback1(() =>
    switch asyncResult {
    | Done(Ok(data)) if data.variantsDistinctOnCku.pageInfo.hasNextPage === Some(true) =>
      query.fetchMore(
        ~variables=makeVariables(
          ~search=searchQuery,
          ~after=?data.variantsDistinctOnCku.pageInfo.endCursor,
          (),
        ),
        ~updateQuery=(prevResult, {fetchMoreResult}) =>
          switch fetchMoreResult {
          | Some({variantsDistinctOnCku: newVariants}) => {
              variantsDistinctOnCku: {
                ...newVariants,
                edges: prevResult.variantsDistinctOnCku.edges->Array.concat(newVariants.edges),
              },
            }
          | None => prevResult
          },
        (),
      )->ignore
    | _ => ()
    }
  , [asyncResult])

  let onSelectChange = React.useCallback2(selection =>
    setSelectedProducts(prev => {
      let rowsData = switch data {
      | Done(Ok(data)) | Reloading(Ok(data)) => data
      | _ => []
      }
      switch selection {
      | Table.Selected(keys) =>
        keys->Array.keepMap(
          selectedRowCku =>
            rowsData->Array.concat(prev)->Array.getBy(row => row.cku === selectedRowCku),
        )
      | All => prev // NOTE - not supported
      }
    })
  , (data, disabledIds))

  <PickerModal opened selectedPriceName selectedProducts onCommit onRequestClose>
    <AnimatedRender displayed=opened animation=#fadePopinTranslation duration=300>
      <div style={ReactDOMStyle.make(~display="flex", ~minHeight="70vh", ~maxHeight="70vh", ())}>
        <TableView
          searchPlaceholder={t("Search product")}
          columns
          data
          keyExtractor=Row.keyExtractor
          disabledRowsKeys=disabledIds
          selectionEnabled=true
          selectAllEnabled=false
          hideReloadingPlaceholder=true
          compactRows=true
          onSearchQueryChange={value => setSearchQuery(_ => value)}
          onSelectChange
          onLoadMore
        />
      </div>
    </AnimatedRender>
  </PickerModal>
}

let make = React.memoCustomCompareProps(make, (oldProps, newProps) =>
  oldProps.opened === newProps.opened
)
