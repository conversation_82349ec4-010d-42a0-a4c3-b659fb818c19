open Intl

module ArchiveSupplierMutation = %graphql(`
  mutation SupplierArchiveModalMutation($id: ID!) {
    archiveSupplier(id: $id) {
      id
      archivedAt
    }
  }
`)

let text = "Once the supplier is archived, it will only be visible in the supplier list by applying the \"Archive\" filter."

@react.component
let make = (~id, ~opened, ~onRequestClose, ~supplierBaseRoute) => {
  let navigate = Navigation.useNavigate()
  let (archiveSupplier, _) = ArchiveSupplierMutation.use()

  let commit = _ =>
    archiveSupplier(ArchiveSupplierMutation.makeVariables(~id, ()))
    ->ApolloHelpers.mutationPromiseToFutureResult
    ->Future.tapOk(_ => navigate(supplierBaseRoute))
    ->ignore

  <Modal
    title={t("Confirm the archiving of this supplier")}
    opened
    commitButtonText={t("Confirm")}
    commitButtonCallback={commit}
    abortButtonText={t("Cancel")}
    onRequestClose>
    <Box spaceY=#xxlarge spaceX=#xlarge>
      <TextStyle variation=#normal> {t(text)->React.string} </TextStyle>
    </Box>
  </Modal>
}

let make = React.memo(make)
