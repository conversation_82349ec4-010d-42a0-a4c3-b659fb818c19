open Intl

exception UnknownStringValue

type t = [#ARCHIVED | #UNARCHIVED]

let toLabel: t => string = x =>
  switch x {
  | #UNARCHIVED => t("Not archived")
  | #ARCHIVED => t("Archived")
  }

let toString: t => string = status => (status :> string)

let fromString: string => t = value =>
  switch value {
  | "ARCHIVED" => #ARCHIVED
  | "UNARCHIVED" => #UNARCHIVED
  | _ => raise(UnknownStringValue)
  }

let values: array<t> = [#ARCHIVED, #UNARCHIVED]
