// TODO - backend: use query without pagination then add tests
open Intl

type supplier = {
  id: Js.Nullable.t<string>,
  name: string,
}

module Query = %graphql(`
  query SuppliersQuery($filterBy: InputSuppliersQueryFilter, $after: String, $first: Int) {
    suppliers(filterBy: $filterBy, after: $after, first: $first) {
      pageInfo {
        endCursor
        hasNextPage
      }
      edges {
        node {
          id
          companyName
        }
      }
    }
  }
`)
type queryResult = Js.Promise.t<
  Result.t<
    ApolloClient__React_Hooks_UseApolloClient.ApolloClient.ApolloQueryResult.t__ok<Query.t>,
    ApolloClient__React_Hooks_UseApolloClient.ApolloClient.ApolloError.t,
  >,
>
let rec paginateSuppliersQuery = (~query: (~after: _) => queryResult, ~cursor=?, ~data=[], ()) =>
  query(~after=cursor)
  ->FuturePromise.fromPromise
  ->Future.mapError(_ => ())
  ->Future.flatMapOk(response =>
    switch response {
    | Ok({
        data: {suppliers: {pageInfo: {endCursor, hasNextPage: Some(true)}, edges: suppliers}},
        error: None,
      }) =>
      paginateSuppliersQuery(~query, ~data=data->Array.concat(suppliers), ~cursor=?endCursor, ())
    | Ok({data: {suppliers: {edges: suppliers}}, error: None}) =>
      Future.value(Ok(data->Array.concat(suppliers)))
    | _ => Future.value(Error())
    }
  )

let useSuppliersQueryFn = (~shopId, ~skip) => {
  let apolloClient = ApolloClient.React.useApolloClient()
  let (suppliers, setSuppliers) = React.useState(() => AsyncData.NotAsked)

  let query = (~after) =>
    apolloClient.query(
      ~query=module(Query),
      ~fetchPolicy=NetworkOnly,
      Query.makeVariables(
        ~first=50,
        ~after?,
        ~filterBy=Query.makeInputObjectInputSuppliersQueryFilter(
          ~shopIds=?switch shopId {
          | Some(shopId) => Some({_in: [shopId]})
          | None => None
          },
          ~archived=#EXCLUDED,
          (),
        ),
        (),
      ),
    )

  React.useEffect0(() => {
    setSuppliers(_ => Loading)
    if shopId->Option.isSome {
      paginateSuppliersQuery(~query, ())
      ->Future.tapOk(suppliers => setSuppliers(_ => Done(suppliers)))
      ->ignore
    }
    None
  })

  React.useEffect2(() => {
    if !skip && shopId->Option.isSome {
      setSuppliers(value =>
        switch value {
        | Done(value) => Reloading(value)
        | _ => Loading
        }
      )
      paginateSuppliersQuery(~query, ())
      ->Future.tapOk(suppliers => setSuppliers(_ => Done(suppliers)))
      ->ignore
    }
    None
  }, (skip, shopId))

  suppliers
}

@react.component
let make = (
  ~size=?,
  ~label=?,
  ~preset,
  ~defaultOpen=?,
  ~showDefaultItem=false,
  ~hideOverlayFooter=false,
  ~shopId,
  ~useSuppliersQuery=useSuppliersQueryFn,
  ~value,
  ~onChange,
  ~onDismiss=?,
) => {
  let (opened, setOpened) = React.useState(() => false)
  let queryResult = useSuppliersQuery(~shopId, ~skip=!opened)

  let suppliersResult = queryResult->AsyncData.map(suppliers =>
    suppliers->Array.map(supplier => {
      id: supplier.node.id->Js.Nullable.return,
      name: supplier.node.companyName,
    })
  )

  let sections = {
    let items = switch suppliersResult {
    | Done(suppliers) | Reloading(suppliers) =>
      suppliers->Array.map(supplier => {
        Select.key: supplier.id->Js.Nullable.toOption->Option.getWithDefault(supplier.name),
        label: supplier.name,
        value: Some(supplier),
      })
    | _ =>
      value->Option.mapWithDefault([], supplier => [
        {
          Select.key: supplier.id->Js.Nullable.toOption->Option.getWithDefault(supplier.name),
          label: supplier.name,
          value: Some(supplier),
        },
      ])
    }
    let defaultItem = {
      Select.key: "default",
      label: t("All"),
      value: None,
      sticky: true,
    }

    let notSpecifiedItem = {
      Select.key: "not-specified",
      label: template(t("Not specified"), ()),
      value: Some({
        id: Js.Nullable.null,
        name: t("Not specified"),
      }),
      sticky: true,
    }
    showDefaultItem
      ? [{Select.items: [defaultItem, notSpecifiedItem]}, {title: t("Suppliers"), items}]
      : [{Select.title: t("Suppliers"), items}]
  }
  let loading = suppliersResult === Loading

  // NOTE - makes sure there is no archived selected supplier on a command editing
  React.useEffect1(() => {
    switch (value, suppliersResult) {
    | (Some(selectedSupplier), Done(suppliers))
      if suppliers->Array.every(current => current.id !== selectedSupplier.id) &&
        !(selectedSupplier.id->Js.isNullable) =>
      onChange(None)
    | _ => ()
    }
    None
  }, [suppliersResult])

  let onToggle = React.useCallback0(opened => {
    setOpened(_ => opened)
    switch (opened, onDismiss) {
    | (false, Some(onDismiss)) => onDismiss()
    | _ => ()
    }
  })

  let disabled = shopId->Option.isNone
  let overlayFooterLink = !hideOverlayFooter
    ? Some({
        Select.text: t("Create supplier"),
        to: Route(SupplierRoutes.newRoute(~shopId?, ())),
      })
    : None

  <Tooltip
    content={<Tooltip.Span text={t("Please select a shop beforehand.")} />}
    delay=0
    placement=#top
    disabled={!disabled}>
    <Select
      ?size
      preset
      disabled
      ?label
      placeholder={t("Select a supplier")}
      ?defaultOpen
      loading
      sections
      ?overlayFooterLink
      value
      onChange
      onToggle
    />
  </Tooltip>
}

let make = React.memo(make)
