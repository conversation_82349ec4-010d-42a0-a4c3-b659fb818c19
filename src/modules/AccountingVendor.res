type t = Excel | IsaCompta

let values = [Excel, <PERSON><PERSON>om<PERSON>]

let toLabel = value =>
  switch value {
  | Excel => "Excel"
  | IsaCompta => "IsaCompta"
  }

let toLowercaseString = value =>
  switch value {
  | Excel => "excel"
  | IsaCompta => "isacompta"
  }

let fromString = value =>
  switch value {
  | "Excel" | "excel" => Some(Excel)
  | "IsaCompta" | "isacompta" => Some(IsaCompta)
  | _ => None
  }

let toSelectItem = value => {
  Select.key: value->toLowercaseString,
  label: value->toLabel,
  value,
}

let isExcel = value =>
  switch value {
  | Excel => true
  | IsaCompta => false
  }

let isIsaCompta = value =>
  switch value {
  | Excel => false
  | IsaCompta => true
  }

let equal = (a, b) =>
  switch (a, b) {
  | (Excel, Excel) | (IsaCompta, IsaCompta) => true
  | (_, _) => false
  }
