// TODO — at some point, the specs of this file should be handled in a shared Wino package

type t =
  | DashboardWinoFr
  | AppWinoFr
  | ERP
  | Bot
  | WinoPay
  | CashRegister({slug: string, name: string})

let decode = (~slug, ~name) =>
  switch slug {
  | "d" => DashboardWinoFr
  | "W" => AppWinoFr
  | "ERP" => ERP
  | "BOT" => Bot
  | "winopay" => WinoPay
  | slug => CashRegister({slug, name})
  }

// NOTE - this function share the logic from Wino core-sdk repo:
// https://github.com/winoteam/wino/blob/c81feffc5b05c4bdbecbb90a7cf21aa052dde00a/packages/core-sdk/src/authentication/devices.ts#L221
let format = name =>
  switch name {
  | DashboardWinoFr => "dashboard.wino.fr"
  | AppWinoFr => "app.wino.fr"
  | ERP => "ERP"
  | Bot => "BOT"
  | WinoPay => "WinoPay"
  | CashRegister({slug, name}) => `${slug} - ${name}`
  }
