open Intl

let signFromKind = (kind: StockActivityKind.t) => {
  switch kind {
  | #REFUND | #DELIVERY | #INCOMING_TRANSFER | #CREDIT_MEMO | #RECEPTION => "+"
  | #LOSS | #SALE | #DELIVERY_RECEIPT | #OUTGOING_TRANSFER => "-"
  | #RESET => "="
  }
}

let toRawValue = (~capacityPrecision=?, value) =>
  switch capacityPrecision {
  | Some(precision) => value *. Js.Math.pow_float(~base=10., ~exp=precision->Float.fromInt)
  | _ => value
  }->Float.toInt

let format = (~kind: StockActivityKind.t, ~capacityPrecision=?, ~capacityUnit=?, value: int) => {
  let value = switch capacityPrecision {
  | Some(precision) =>
    value->Int.toFloat /. Js.Math.pow_float(~base=10., ~exp=precision->Float.fromInt)
  | _ => value->Int.toFloat
  }
  let unit = switch capacityUnit {
  | Some("g") | Some("G") => Some(#gram)
  | Some("kg") | Some("KG") => Some(#kilogram)
  | Some("l") | Some("L") => Some(#liter)
  // NOTE - not supported https://github.com/unicode-org/cldr/blob/main/common/validity/unit.xml
  // | Some("cl") | Some("cL") | Some("CL") => Some(#deciliter)
  | _ => None
  }

  kind->signFromKind ++
    switch (unit, capacityUnit) {
    | (Some(unit), _) => value->unitFormat(~unit)
    | (None, Some(unit)) => value->decimalFormat ++ " " ++ unit
    | (None, None) => value->decimalFormat
    }
}
