open Intl

exception UnknownStringValue

type t = [
  | #LOSS
  | #THEFT
  | #DAMAGE
  | #TASTING
  | #OUTDATED_DLUO
  | #TRADE_IN_SUPPLIER
  | #SALE_BY_GLASS
  | #CORKY
  | #OTHER
]

let values: array<t> = [
  #LOSS,
  #THEFT,
  #DAMAGE,
  #TASTING,
  #OUTDATED_DLUO,
  #TRADE_IN_SUPPLIER,
  #SALE_BY_GLASS,
  #CORKY,
  #OTHER,
]

let toString: t => string = reason =>
  switch reason {
  | #LOSS => "LOSS"
  | #THEFT => "THEFT"
  | #DAMAGE => "DAMAGE"
  | #TASTING => "TASTING"
  | #OUTDATED_DLUO => "OUTDATED_DLUO"
  | #TRADE_IN_SUPPLIER => "TRADE_IN_SUPPLIER"
  | #SALE_BY_GLASS => "SALE_BY_GLASS"
  | #CORKY => "CORKY"
  | #OTHER => "OTHER"
  }

let toLabel: t => string = reason =>
  switch reason {
  | #LOSS => t("Loss")
  | #THEFT => t("Theft")
  | #DAMAGE => t("Damage")
  | #TASTING => t("Tasting")
  | #OUTDATED_DLUO => t("Outdated DLUO")
  | #TRADE_IN_SUPPLIER => t("Supplier trade in")
  | #SALE_BY_GLASS => t("Sale by glass")
  | #CORKY => t("Corky")
  | #OTHER => t("Other")
  }

let fromStringExn: string => t = value =>
  switch value {
  | "LOSS" => #LOSS
  | "THEFT" => #THEFT
  | "DAMAGE" => #DAMAGE
  | "TASTING" => #TASTING
  | "OUTDATED_DLUO" => #OUTDATED_DLUO
  | "TRADE_IN_SUPPLIER" => #TRADE_IN_SUPPLIER
  | "SALE_BY_GLASS" => #SALE_BY_GLASS
  | "CORKY" => #CORKY
  | "OTHER" => #OTHER
  | _ => raise(UnknownStringValue)
  }
