let defaultPrice = "0,000"
let defaultCurrency = #EUR->Intl.toCurrencySymbol

@react.component
let make = (~value, ~size=#normal) =>
  switch value {
  | Some(price) =>
    <TextStyle size lineHeight=#xsmall>
      {price->Js.String2.replace(".", ",")->React.string}
    </TextStyle>
  | None =>
    <TextStyle size variation=#normal>
      {`${defaultPrice} ${defaultCurrency}`->React.string}
    </TextStyle>
  }

let make = React.memo(make)
