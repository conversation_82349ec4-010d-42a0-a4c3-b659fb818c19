open Intl
open StockTransferSheet

@react.component
let make = (~id, ~code, ~onError) => {
  let (csvDownloadRequested, setCsvDownloadRequested) = React.useState(() => false)
  let (excelDownloadRequested, setExcelDownloadRequested) = React.useState(() => false)

  let processBlobAndOpenFile = (future, ~filename) =>
    future
    ->Future.flatMap(result =>
      switch result {
      | Ok(blob) => blob->TriggerDownload.fromBlob(~filename)
      | Error(_) => Future.value(Error())
      }
    )
    ->Future.tap(result => {
      setCsvDownloadRequested(_ => false)
      switch result {
      | Ok() => ()
      | Error() => onError("Something went wrong")
      }
    })

  React.useEffect1(() =>
    if csvDownloadRequested {
      let filename = makeCsvFilename(~code)
      let future = queryAndMakeCsvBlob(~stockTransferId=id)->processBlobAndOpenFile(~filename)

      // NOTE - We don't cancel graphql query here, apollo doesn't handle it yet
      Some(() => future->Future.cancel)
    } else {
      None
    }
  , [csvDownloadRequested])

  React.useEffect1(() =>
    if excelDownloadRequested {
      let filename = makeExcelFilename(~code)
      let future = queryAndMakeExcelBlob(~stockTransferId=id)->processBlobAndOpenFile(~filename)

      // NOTE - We don't cancel query here, apollo doesn't handle it yet
      Some(() => future->Future.cancel)
    } else {
      None
    }
  , [excelDownloadRequested])

  <Inline align=#end>
    <Menu variation=#more_round>
      <MenuItem
        content=Text(t("Download CSV")) action=Callback(_ => setCsvDownloadRequested(_ => true))
      />
      <MenuItem
        content=Text(t("Download Excel")) action=Callback(_ => setExcelDownloadRequested(_ => true))
      />
    </Menu>
  </Inline>
}

let make = React.memo(make)
