type product = {
  cku: string,
  name: string,
  description: string,
  purchasePrice: option<string>,
  stockKeepingUnit: option<string>,
  senderVariantId: string,
  senderStockQuantity: int,
  senderStockState: option<Product.Stock.t>,
  recipientVariantId: string,
  recipientStockQuantity: option<int>,
  recipientStockState: option<Product.Stock.t>,
  transferredQuantity: int,
}

module VariantsByCkuQuery = %graphql(`
    query VariantsByCkuQuery($cku: CKU!, $filterBy: InputVariantsByCkuQueryFilter) {
      variantsByCku(cku: $cku, filterBy: $filterBy) {
        edges {
          node {
            id
            stock {
              rawQuantity
              state @ppxOmitFutureValue
            }
          }
        }
      }
    }
  `)

let getVariantByCkuAndShopId = (client: ApolloClient.t, ~cku, ~shopId) =>
  client.query(
    ~query=module(VariantsByCkuQuery),
    ~fetchPolicy=NetworkOnly,
    VariantsByCkuQuery.makeVariables(
      ~cku=cku->Scalar.CKU.serialize,
      ~filterBy=VariantsByCkuQuery.makeInputObjectInputVariantsByCkuQueryFilter(
        ~shopIds=?Some(VariantsByCkuQuery.makeInputObjectInFilter(~_in=[shopId], ())),
        (),
      ),
      (),
    ),
  )
  ->FuturePromise.fromPromise
  ->Future.map(results =>
    switch results {
    | Ok(Ok({data: {variantsByCku}, error: None})) => Ok(variantsByCku.edges)
    | _ => Error()
    }
  )

type action =
  | ResetRequested
  | ProductBulkAddRequested(array<product>)
  | ProductRemoveRequested(string)
  | ProductUpdateRequested(string, product => product)
  | ProductTransferredQuantityUpdateRequested(string, int)
  | ProductRecipientStockAndIdBulkFetchRequested(string)
  | ProductRecipientStockAndIdBulkFetchSucceeded

type state = {
  processing: bool,
  products: array<product>,
}

let initialState = {processing: false, products: []}

let reducer = (client, recipientShopId, state, action) =>
  switch action {
  | ResetRequested => ReactUpdateReducer.Update(initialState)
  | ProductBulkAddRequested(products) =>
    UpdateWithSideEffects(
      {...state, products: products->Array.concat(state.products)},
      self => {
        self.dispatch(ProductRecipientStockAndIdBulkFetchRequested(recipientShopId))
        None
      },
    )
  | ProductRemoveRequested(cku) =>
    Update({
      ...state,
      products: state.products->Array.keep(product => product.cku !== cku),
    })
  | ProductUpdateRequested(cku, update) =>
    Update({
      ...state,
      products: state.products->Array.map(product =>
        product.cku === cku ? product->update : product
      ),
    })
  | ProductTransferredQuantityUpdateRequested(cku, transferredQuantity) =>
    Update({
      ...state,
      products: state.products->Array.map(product =>
        product.cku === cku ? {...product, transferredQuantity} : product
      ),
    })
  | ProductRecipientStockAndIdBulkFetchRequested(shopId) =>
    UpdateWithSideEffects(
      {
        processing: true,
        products: state.products->Array.map(product => {...product, recipientStockQuantity: None}),
      },
      self => {
        let updatingRecipientStocksAndIds = self.state.products->Array.map(product =>
          client
          ->getVariantByCkuAndShopId(~cku=product.cku, ~shopId)
          ->Future.mapOk(results =>
            switch results {
            | [result] =>
              self.dispatch(
                ProductUpdateRequested(
                  product.cku,
                  productToUpdate => {
                    ...productToUpdate,
                    recipientVariantId: result.node.id,
                    recipientStockQuantity: result.node.stock.rawQuantity,
                    recipientStockState: result.node.stock.state,
                  },
                ),
              )
            | _ => ()
            }
          )
        )

        Future.all(updatingRecipientStocksAndIds)
        ->Future.get(_ => self.dispatch(ProductRecipientStockAndIdBulkFetchSucceeded))
        ->ignore

        None
      },
    )
  | ProductRecipientStockAndIdBulkFetchSucceeded => Update({...state, processing: false})
  }

let use = () => {
  let client = ApolloClient.React.useApolloClient()
  let {values, status} = StockTransferForm.useFormState()
  let formDispatch = StockTransferForm.useFormDispatch()
  let recipientShopId = values.recipientShopId

  let (productsState, productsDispatch) = ReactUpdateReducer.use(
    reducer(client, recipientShopId),
    initialState,
  )

  // Update form products on productState.products update
  React.useEffect1(() => {
    if !productsState.processing && status !== Pristine {
      FieldValueChanged(
        Products,
        _ =>
          productsState.products->Array.map(product => {
            let formProduct: StockTransferForm.ProductLenses.state = {
              quantity: product.transferredQuantity,
              senderVariantId: product.senderVariantId,
              recipientVariantId: product.recipientVariantId,
            }

            formProduct
          }),
      )->formDispatch
    }

    None
  }, [productsState])

  // Reset products state on form products reseted
  React.useEffect1(() => {
    if status === Pristine {
      ResetRequested->productsDispatch
    }
    None
  }, [status])

  (productsState, productsDispatch)
}
