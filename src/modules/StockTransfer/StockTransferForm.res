open Intl

module ProductLenses = %lenses(
  type state = {
    quantity: int,
    senderVariantId: string,
    recipientVariantId: string,
  }
)

module Lenses = %lenses(
  type state = {
    senderShopId: string,
    recipientShopId: string,
    products: array<ProductLenses.state>,
  }
)

let initialValues = {
  Lenses.senderShopId: "",
  recipientShopId: "",
  products: [],
}

include Form.Make(Lenses)

let productsError = products =>
  products->Array.keepMap(product =>
    product.ProductLenses.quantity <= 0
      ? Some({
          Table.key: product.senderVariantId,
          message: t("Transferred quantity must be greater than 0."),
        })
      : None
  )

let schema = [
  Schema.StringNotEmpty(SenderShopId),
  StringNotEmpty(RecipientShopId),
  Custom(
    Products,
    ({products}) =>
      products->Array.length === 0
        ? Error(t("You should have at least one product in cart."))
        : Ok(),
  ),
  Custom(
    Products,
    ({products}) => {
      products->productsError->Array.length > 0
        ? Error(t("The cart contains products with an incorrect transferred quantity."))
        : Ok()
    },
  ),
]
