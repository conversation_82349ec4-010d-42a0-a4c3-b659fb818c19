open Intl

type queryAndMakeError = QueryFailure | NoDataFailure | WriteBlobFailure

module Query = %graphql(`
  query StockTransferSheetQuery($stockTransferId: ID!, $productsFirst: Int, $productsAfter: String) {
    stockTransfer(id: $stockTransferId) {
      id
      code
      senderShop {
        name
        address
        city
        postalCode
        country
        phoneNumber
      }
      recipientShop {
        name
        address
        city
        postalCode
        country
        phoneNumber
      }
      createdAt
      products(first: $productsFirst, after: $productsAfter) {
        totalCount
        pageInfo {
          endCursor
          hasNextPage
        }
        edges {
          node {
            recipientVariant {
              formattedName
              formattedDescription
              stockKeepingUnit
            }
            quantity
          }
        }
      }
    }
  }
`)

let makeFilename = (~code, ~extension) => t("transferorder") ++ "_" ++ code ++ "." ++ extension
let makeCsvFilename = makeFilename(~extension=Sheet.csvFileExtension)
let makeExcelFilename = makeFilename(~extension=Sheet.excelFileExtension)

let makeRows = stockTransfer => {
  open Query
  let titleRow = [
    t("Transfer of") ++
    " " ++
    stockTransfer.createdAt->Intl.dateTimeFormat(~dateStyle=#short) ++
    " " ++
    t("at") ++
    " " ++
    stockTransfer.createdAt->Intl.dateTimeFormat(~timeStyle=#short),
    template(
      t("Reference{{entilted}}{{code}}"),
      ~values={"entilted": " : ", "code": stockTransfer.code},
      (),
    ),
  ]
  let headers = [t("Sender") ++ " :", t("Recipient") ++ " :"]
  let shopNameRows = [stockTransfer.senderShop.name, stockTransfer.recipientShop.name]
  let shopAdressRows = [
    stockTransfer.senderShop.address ++
    " " ++
    stockTransfer.senderShop.postalCode ++
    " " ++
    stockTransfer.senderShop.city ++
    ", " ++
    stockTransfer.senderShop.country,
    stockTransfer.recipientShop.address ++
    " " ++
    stockTransfer.recipientShop.postalCode ++
    " " ++
    stockTransfer.recipientShop.city ++
    ", " ++
    stockTransfer.recipientShop.country,
  ]
  let shopPhoneNumberRows = [
    stockTransfer.senderShop.phoneNumber->Option.getWithDefault(""),
    stockTransfer.recipientShop.phoneNumber->Option.getWithDefault(""),
  ]
  let productHeaderRows = ["SKU", t("Product's name"), "Description", t("Transferred quantity")]
  let productsRows =
    stockTransfer.products.edges->Array.map(product => [
      product.node.recipientVariant.stockKeepingUnit->Option.getWithDefault(""),
      product.node.recipientVariant.formattedName,
      product.node.recipientVariant.formattedDescription,
      product.node.quantity->Js.Int.toString,
    ])
  Array.concatMany([
    [titleRow],
    [[]],
    [headers],
    [shopNameRows],
    [shopAdressRows],
    [shopPhoneNumberRows],
    [[]],
    [productHeaderRows],
    productsRows->Array.map(row => row),
  ])
}

let makeCsvPlainText = stockTransfer => Sheet.makeCsvPlainText(stockTransfer->makeRows)

let makeCsvBlob = stockTransfer =>
  stockTransfer->makeCsvPlainText->Result.map(Sheet.makeCsvBlobFromPlainText)

let query = (~stockTransferId, ~productsFirst, ~productsAfter) =>
  ApolloConfig.makeClient().query(
    ~query=module(Query),
    Query.makeVariables(~stockTransferId, ~productsFirst, ~productsAfter?, ()),
  )->FuturePromise.fromPromise

let rec queryWithAllProducts = (
  ~stockTransferId,
  ~previousResult: option<Query.t_stockTransfer>=?,
  ~productEdgesEndCursor=?,
  ~productEdgesPerFetch=ApolloConfig.edgesPerFetchLimit,
  (),
) =>
  query(
    ~stockTransferId,
    ~productsFirst=productEdgesPerFetch,
    ~productsAfter=productEdgesEndCursor,
  )->Future.flatMap(result =>
    switch result {
    | Ok(Ok({data: {stockTransfer: Some(stockTransfer)}, error: None})) =>
      let stockTransfer = switch previousResult {
      | Some(previousResult) => {
          ...stockTransfer,
          products: {
            ...stockTransfer.products,
            edges: previousResult.products.edges->Array.concat(stockTransfer.products.edges),
          },
        }
      | None => stockTransfer
      }

      switch stockTransfer {
      | {products: {pageInfo: {hasNextPage: Some(true), endCursor}}} =>
        queryWithAllProducts(
          ~stockTransferId,
          ~previousResult=stockTransfer,
          ~productEdgesEndCursor=?endCursor,
          ~productEdgesPerFetch,
          (),
        )
      | _ => Future.value(Ok(Some(stockTransfer)))
      }
    | Ok(Ok({data: {stockTransfer: None}, error: None})) => Future.value(Ok(None))
    | _ => Future.value(Error(QueryFailure))
    }
  )

let queryAndMakeCsvBlob = (~stockTransferId) =>
  queryWithAllProducts(~stockTransferId, ())->Future.map(results =>
    switch results {
    | Ok(Some(stockTransfer)) =>
      switch stockTransfer->makeCsvBlob {
      | Ok(csvBlob) => Ok(csvBlob)
      | Error() => Error(WriteBlobFailure)
      }
    | Ok(None) => Error(NoDataFailure)
    | Error(error) => Error(error)
    }
  )

let makeExcelBlob = stockTransfer =>
  stockTransfer
  ->makeRows
  ->Sheet.makeExcelBlob(~worksheetName=t("Stock transfer"))
  ->Future.mapError(_ => WriteBlobFailure)

let queryAndMakeExcelBlob = (~stockTransferId) =>
  queryWithAllProducts(~stockTransferId, ())->Future.flatMap(results =>
    switch results {
    | Ok(Some(stockTransfer)) => stockTransfer->makeExcelBlob
    | Ok(None) => Future.value(Error(NoDataFailure))
    | Error(error) => Future.value(Error(error))
    }
  )
