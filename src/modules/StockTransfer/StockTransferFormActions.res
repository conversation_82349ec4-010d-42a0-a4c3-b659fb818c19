open Intl

module CreateStockTransferMutation = %graphql(`
  mutation createStockTransfer($input: InputCreateStockTransfer!, $productsInput: [InputCreateStockTransferProduct!]!) {
    createStockTransfer(input: $input, productsInput: $productsInput) {
      id
      senderShop {
        id
      }
      recipientShop {
        id
      }
      products {
        edges {
          node {
            quantity
            senderStock
            recipientStock
            senderVariant {
              id
            }
            recipientVariant {
              id
            }
          }
        }
      }
    }
  }
`)

let makeMutationVariables = ({
  StockTransferForm.Lenses.products: products,
  senderShopId,
  recipientShopId,
}) =>
  CreateStockTransferMutation.makeVariables(
    ~input=CreateStockTransferMutation.makeInputObjectInputCreateStockTransfer(
      ~senderShopId,
      ~recipientShopId,
      (),
    ),
    ~productsInput=products->Array.map(product =>
      CreateStockTransferMutation.makeInputObjectInputCreateStockTransferProduct(
        ~quantity=product.quantity,
        ~senderVariantId=product.senderVariantId,
        ~recipientVariantId=product.recipientVariantId,
        (),
      )
    ),
    (),
  )

@react.component
let make = () => {
  let (mutate, _) = CreateStockTransferMutation.use()
  let handler = values =>
    mutate(makeMutationVariables(values))
    ->ApolloHelpers.mutationPromiseToFutureResult
    ->Future.mapOk(({CreateStockTransferMutation.createStockTransfer: {id}}) => Some(id))

  <StockTransferForm.SubmitButton
    text={t("Finalize")} variation=#primary size=#medium onSubmit={(_, values) => handler(values)}
  />
}

let make = React.memo(make)
