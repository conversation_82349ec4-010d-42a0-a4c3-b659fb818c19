open Intl

@react.component
let make = () => {
  let shops = Auth.useShops()
  let formState = StockTransferForm.useFormState()

  let items = shops->Array.map(shop => {
    Select.key: shop.id,
    label: shop.name,
    value: shop.id,
    disabled: shop.id === formState.values.recipientShopId,
  })
  let sections = [{Select.title: t("Shops"), items}]

  <Card grow=true title={t("Sender information")}>
    <Stack space=#large>
      <StockTransferForm.InputSelect
        field=SenderShopId
        label={t("Sender")}
        placeholder={t("Select the sending shop")}
        tooltip={<Tooltip.Span text={t("Changing the sender will reset the transfer basket.")} />}
        sections
      />
    </Stack>
  </Card>
}

let make = React.memo(make)
