import {
  make,
  type Column,
  type ColumnRenderProps,
  type Layout,
  type ColumnSize_static,
  type ColumnSize_dynamic,
} from './Table.gen'

type TableProps = React.ComponentProps<typeof make>

const Table = make as React.FunctionComponent<TableProps>

export function px(value: number): ColumnSize_static {
  return { NAME: 'px', VAL: value }
}
export function pct(value: number): ColumnSize_static {
  return { NAME: 'pct', VAL: value }
}
export function fr(value: number): ColumnSize_dynamic {
  return { NAME: 'fr', VAL: value }
}

export {
  Table,
  type TableProps,
  type Column as TableColumn,
  type ColumnRenderProps as TableColumnRenderProps,
  type Layout as TableLayout,
}
