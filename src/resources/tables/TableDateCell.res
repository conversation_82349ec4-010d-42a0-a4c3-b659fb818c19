@react.component
let make = (~value, ~label=?) => {
  <Stack space=#none>
    <TextStyle>
      {switch value {
      | Some(value) => value->Intl.dateTimeFormat(~dateStyle=#short)
      | None => "—"
      }->React.string}
    </TextStyle>
    {switch label {
    | Some(label) =>
      <TextStyle size=#xxsmall variation=#normal> {("(" ++ label ++ ")")->React.string} </TextStyle>
    | None => React.null
    }}
  </Stack>
}

let make = React.memo(make)
