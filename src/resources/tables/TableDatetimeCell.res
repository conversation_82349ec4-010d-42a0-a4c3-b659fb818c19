open Intl

@react.component
let make = (~value) => {
  let formattedDate = value->Intl.dateTimeFormat(~dateStyle=#short)
  let formattedTime = switch (Intl.locale, value->Intl.dateTimeFormat(~timeStyle=#short)) {
  | (#"fr-FR", time) => time->Js.String2.replace(":", "h")
  | (#"en-EN", time) => time
  }
  <Stack>
    <TextStyle> {formattedDate->React.string} </TextStyle>
    <TextStyle variation=#normal size=#xxsmall>
      {(t("at") ++ (" " ++ formattedTime))->React.string}
    </TextStyle>
  </Stack>
}

let make = React.memo(make)
