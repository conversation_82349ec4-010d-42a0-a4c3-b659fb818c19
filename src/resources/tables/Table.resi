module TableLayout: {
  type columnLayout = {
    key: ReactStately.key,
    index: int,
    relativeRect: option<Dom.domRect>,
    sticky: bool,
  }

  type scrollBar = {
    position: float,
    scrollable: bool,
    size?: int,
  }

  type t = {
    state: array<columnLayout>,
    ariaState: ReactStately.Table.Column.Resize.t,
    headerRef: React.ref<Js.Nullable.t<Dom.element>>,
    bodyRef: React.ref<Js.Nullable.t<Dom.element>>,
    tableWidth: option<float>,
    scrollBarX: scrollBar,
    scrollBarY: scrollBar,
  }

  let computeColumnStickyPositionState: (
    ~index: int,
    ~layoutState: array<columnLayout>,
    ~scrolled: bool,
  ) => (float, bool)

  let getRelativeBoundingRect: (~src: Dom.domRect, ~dest: Dom.domRect) => Dom.domRect
}

module ColumnSize: {
  type static = [#px(float) | #pct(float)]
  type dynamic = [#fr(float)]
  type t = [static | dynamic]
}

@genType.as("Selection")
type selection = All | Selected(array<ReactStately.key>)

@genType.as("ErroredRow")
type erroredRow = {
  key: ReactStately.key,
  message: string,
}

@genType.as("Layout")
type layout = {
  hidden?: bool,
  breakpoint?: Breakpoints.t,
  headerActionComponent?: React.element,
  minWidth?: ColumnSize.static,
  width?: ColumnSize.t,
  defaultWidth?: ColumnSize.t,
  margin?: Spaces.t,
  alignX?: [#flexStart | #center | #flexEnd],
  alignY?: [#flexStart | #center | #flexEnd],
  sticky?: bool,
}

@genType.as("ColumnRenderProps")
type columnRenderProps<'row> = {
  data: 'row,
  index: int,
  disabled: bool,
  errorMessage: option<string>,
}

@genType.as("Column")
type column<'row> = {
  key: string,
  name?: string,
  layout?: layout,
  allowsSorting?: bool,
  render: columnRenderProps<'row> => React.element,
}

@genType @react.component
let make: (
  ~columns: array<column<'row>>,
  ~rows: array<'row>,
  ~keyExtractor: 'row => ReactStately.key,
  ~disabledRowsKeys: array<ReactStately.key>=?,
  ~erroredRowsMap: array<erroredRow>=?,
  ~selectionEnabled: bool=?,
  ~selectAllEnabled: bool=?,
  ~initialAllSelected: bool=?,
  ~sortDescriptor: ReactStately.Table.sortDescriptor=?,
  ~maxWidth: float=?,
  ~minHeight: float=?,
  ~maxHeight: float=?,
  ~compactRows: bool=?,
  ~footerComponent: React.element=?,
  ~onLoadMore: unit => unit=?,
  ~onSelectChange: selection => unit=?,
  ~onSortChange: ReactStately.Table.sortDescriptor => unit=?,
) => React.element
