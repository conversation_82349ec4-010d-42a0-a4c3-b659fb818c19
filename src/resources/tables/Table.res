open WebAPI

// NOTE - usage: for performance concerns the sticky columns are disabled when rows > rowsLimitStickyColumns
let rowsLimitStickyColumns = 50
// NOTE - usage: when setting a `breakpoint` to a column, `defaultWidth` should be
// used instead of `width` (it will anyway be considered as a `defaultWidth`).
// NOTE - patch package: allows columns to rerender whenever its inherent properties change

module ColumnSize = {
  type static = [#px(float) | #pct(float)]
  type dynamic = [#fr(float)]
  type t = [static | dynamic]

  let fromStaticToReactStatelyTableColumnSize: static => ReactStately.Table.Column.Size.t = value =>
    switch value {
    | #px(rawValue) => Static(#px(rawValue))
    | #pct(rawValue) => Static(#pct(rawValue))
    }->ReactStately.Table.Column.Size.make

  let toReactStatelyTableColumnSize: t => ReactStately.Table.Column.Size.t = value =>
    switch value {
    | #px(rawValue) => Static(#px(rawValue))
    | #pct(rawValue) => Static(#pct(rawValue))
    | #fr(rawValue) => Dynamic(#fr(rawValue))
    }->ReactStately.Table.Column.Size.make
}

type selection = All | Selected(array<ReactStately.key>)
type jsSelection = All | Selected(ReactStately.jsSet<ReactStately.key>)

type extraColumnProps = {
  breakpoint: Breakpoints.t,
  headerActionComponent?: React.element,
  margin?: Spaces.t,
  alignX?: [#flexStart | #center | #flexEnd],
  alignY?: [#flexStart | #center | #flexEnd],
  sticky?: bool,
  mutable ref?: React.ref<Js.nullable<Dom.element>>,
}

type erroredRow = {
  key: ReactStately.key,
  message: string,
}

type layout = {
  hidden?: bool,
  breakpoint?: Breakpoints.t,
  headerActionComponent?: React.element,
  minWidth?: ColumnSize.static,
  width?: ColumnSize.t,
  defaultWidth?: ColumnSize.t,
  margin?: Spaces.t,
  alignX?: [#flexStart | #center | #flexEnd],
  alignY?: [#flexStart | #center | #flexEnd],
  sticky?: bool,
}

type columnRenderProps<'row> = {
  data: 'row,
  index: int,
  disabled: bool,
  errorMessage: option<string>,
}

type column<'row> = {
  key: string,
  name?: string,
  layout?: layout,
  allowsSorting?: bool,
  render: columnRenderProps<'row> => React.element,
}

module TableLayout = {
  let rawSelectCellWidth = 17.
  let rawDefaultCellWidth = 90.
  let selectCellWidth = Static(rawSelectCellWidth->#px)->ReactStately.Table.Column.Size.make
  let defaultCellWidth = Static(rawDefaultCellWidth->#px)->ReactStately.Table.Column.Size.make

  type columnLayout = {
    key: ReactStately.key,
    index: int,
    relativeRect: option<Dom.domRect>,
    sticky: bool,
  }
  type scrollBar = {
    position: float,
    scrollable: bool,
    size?: int,
  }
  type t = {
    state: array<columnLayout>,
    ariaState: ReactStately.Table.Column.Resize.t,
    headerRef: React.ref<Js.Nullable.t<Dom.element>>,
    bodyRef: React.ref<Js.Nullable.t<Dom.element>>,
    tableWidth: option<float>,
    scrollBarX: scrollBar,
    scrollBarY: scrollBar,
  }

  let getBoundingRectFromNodeRef = ref =>
    ref->ReactDomElement.fromRef->Option.map(DomElement.getBoundingClientRect)

  let getScrollableXYFromNodeRef = ref =>
    ref
    ->ReactDomElement.fromRef
    ->Option.map(domElement => (
      domElement->DomElement.scrollLeft->Float.toInt + domElement->DomElement.clientWidth <
        domElement->DomElement.scrollWidth - 17,
      domElement->DomElement.scrollTop->Float.toInt + domElement->DomElement.clientHeight <
        domElement->DomElement.scrollHeight - 17,
    ))

  let getScrollBarWidthFromNodeRef = ref =>
    ref
    ->ReactDomElement.fromRef
    ->Option.map(domElement =>
      domElement->DomElement.offsetWidth->Float.toInt - domElement->DomElement.clientWidth
    )

  let computeColumnStickyPositionState = (~index as columnIndex, ~layoutState, ~scrolled) => {
    let columnSticky =
      layoutState
      ->Array.getBy(col => col.index === columnIndex)
      ->Option.mapWithDefault(false, col => col.sticky)
    let stickyColumns = columnSticky
      ? layoutState->Array.keepWithIndex((col, idx) => col.sticky && idx <= columnIndex)
      : []

    let (_, left, sticking) = stickyColumns->Array.reduce((0., 0., false), (acc, curr) => {
      let (cumulatedStickyWidth, _, _) = acc
      let left = curr.relativeRect->Option.mapWithDefault(0., DomRect.left)
      let width = curr.relativeRect->Option.mapWithDefault(0., DomRect.width)
      let nextCumulatedStickyWidth = cumulatedStickyWidth +. width
      let sticking = left -. cumulatedStickyWidth <= 0. && scrolled

      (nextCumulatedStickyWidth, cumulatedStickyWidth, sticking)
    })

    (left, sticking)
  }

  let getRelativeBoundingRect = (~src, ~dest) =>
    DomRect.make(
      ~x=dest->DomRect.left -. src->DomRect.left,
      ~y=dest->DomRect.top -. src->DomRect.top,
      ~width=dest->DomRect.width,
      ~height=dest->DomRect.height,
    )

  let layoutState = (~tableRect, ~columns) =>
    columns->Array.map(column =>
      !column.ReactStately.Table.props.selectionCell
        ? {
            key: column.key,
            index: column.index,
            relativeRect: switch (
              tableRect,
              column.props.extraColumnProps.ref->Option.flatMap(getBoundingRectFromNodeRef),
            ) {
            | (Some(src), Some(dest)) => Some(getRelativeBoundingRect(~src, ~dest))
            | _ => None
            },
            sticky: column.props.extraColumnProps.sticky->Option.getWithDefault(false),
          }
        : {
            key: column.key,
            index: column.index,
            relativeRect: None,
            sticky: false,
          }
    )

  let use = (~ref as tableRef, ~headerRef, ~bodyRef, ~state as tableState, ~onLoadMore) => {
    let (tableRect, setTableRect) = React.useState(() => None)
    let (scrollPositions, setScrollPositions) = React.useState(() => (0., 0.))
    let (scrollBarWidth, setScrollBarWidth) = React.useState(() => 0)

    React.useLayoutEffect0(() => {
      let headerDomElement = headerRef->ReactDomElement.fromRef
      let bodyDomElement = bodyRef->ReactDomElement.fromRef
      let handler = _ =>
        switch (
          scrollPositions,
          headerDomElement,
          bodyDomElement->Option.map(bodyDomElement => bodyDomElement->DomElement.scrollLeft),
          bodyDomElement->Option.map(bodyDomElement => bodyDomElement->DomElement.scrollTop),
        ) {
        | ((x, _y), Some(headerDomElement), Some(left), Some(top)) if x !== left || x === 0. =>
          headerDomElement->DomElement.setScrollLeft(left)
          setScrollPositions(_ => (left, top))
        | _ => ()
        }

      setTableRect(_ => tableRef->getBoundingRectFromNodeRef)

      bodyDomElement->Option.forEach(domNode =>
        domNode->DomElement.addEventListener("scroll", handler)
      )

      Some(
        () =>
          bodyDomElement->Option.forEach(domEmement =>
            domEmement->DomElement.removeEventListener("scroll", handler)
          ),
      )
    })

    React.useLayoutEffect1(() => {
      let domElement = bodyRef->ReactDomElement.fromRef
      let handler = _ =>
        switch (domElement, onLoadMore) {
        | (Some(domElement), Some(onLoadMore)) =>
          let scrolledY = domElement->DomElement.scrollTop +. domElement->DomElement.offsetHeight
          let scrolledY = scrolledY->Float.toInt

          if domElement->DomElement.scrollHeight - scrolledY < 100 {
            onLoadMore()
          }
        | _ => ()
        }

      domElement->Option.forEach(domElement =>
        domElement->DomElement.removeEventListener("scroll", handler)
      )
      domElement->Option.forEach(domElement =>
        domElement->DomElement.addEventListener("scroll", handler)
      )

      Some(
        () =>
          domElement->Option.forEach(domElement =>
            domElement->DomElement.removeEventListener("scroll", handler)
          ),
      )
    }, [onLoadMore])

    // NOTE - setTimeout: sets state at the end of the stack to avoid resize event loops
    ReactAria.Resize.useObserver({
      ref: tableRef->ReactDOM.Ref.domRef,
      onResize: () =>
        switch (tableRect, tableRef->getBoundingRectFromNodeRef) {
        | (Some(prev), Some(next)) if prev->DomRect.width !== next->DomRect.width =>
          Js.Global.setTimeout(() => {
            setTableRect(_ => tableRef->getBoundingRectFromNodeRef)
          }, 0)->ignore
        | _ => ()
        },
    })
    ReactAria.Resize.useObserver({
      ref: bodyRef->ReactDOM.Ref.domRef,
      onResize: () =>
        switch bodyRef->getScrollBarWidthFromNodeRef {
        | Some(width) if width !== scrollBarWidth => Js.Global.setTimeout(() => {
            setScrollBarWidth(_ => width)
          }, 0)->ignore

        | _ => ()
        },
    })

    let getDefaultWidth = React.useCallback0(node =>
      node.ReactStately.Table.props.selectionCell ? Some(selectCellWidth) : None
    )
    let getDefaultMinWidth = React.useCallback0(node =>
      node.ReactStately.Table.props.selectionCell ? Some(selectCellWidth) : Some(defaultCellWidth)
    )
    let tableWidth = switch (
      tableRect,
      tableState.ReactStately.Table.selectionManager.selectionMode,
    ) {
    | (Some(rect), #multiple) => Some(rect->DomRect.width -. rawSelectCellWidth)
    | _ => tableRect->Option.map(DomRect.width)->Option.map(x => x -. scrollBarWidth->Int.toFloat)
    }
    let scrollBarX = {
      position: scrollPositions->fst,
      scrollable: bodyRef->getScrollableXYFromNodeRef->Option.mapWithDefault(false, fst),
    }
    let scrollBarY = {
      position: scrollPositions->snd,
      scrollable: bodyRef->getScrollableXYFromNodeRef->Option.mapWithDefault(false, snd),
      size: scrollBarWidth,
    }

    let columnResizeState = ReactStately.Table.useColumnResizeState(
      ~props={
        tableWidth,
        getDefaultWidth,
        getDefaultMinWidth,
      },
      ~state=tableState,
    )

    {
      state: layoutState(~tableRect, ~columns=tableState.collection.columns),
      ariaState: columnResizeState,
      headerRef,
      bodyRef,
      tableWidth,
      scrollBarX,
      scrollBarY,
    }
  }
}

module HeaderRow = {
  let style = ReactDOM.Style.make(~display="flex", ())

  let makeScrollBarOffsetStyle = (~scrollBarWidth) =>
    ReactDOM.Style.make(~minWidth=`${scrollBarWidth->Int.toString}px`, ())

  @react.component
  let make = React.memo((~children, ~item, ~state, ~layout) => {
    let ref = React.useRef(Js.Nullable.null)->ReactDOM.Ref.domRef
    let {rowProps} = ReactAria.Table.HeaderRow.use(~props={node: item}, ~state, ~ref)

    let scrollBarWidth = layout.TableLayout.scrollBarY.size->Option.getWithDefault(0)

    <ReactAria.Spread props=rowProps>
      <tr ref style>
        children
        <div style={makeScrollBarOffsetStyle(~scrollBarWidth)} />
      </tr>
    </ReactAria.Spread>
  })
}

module ColumnHeaderSelectAll = {
  let style = (~width) =>
    ReactDOM.Style.make(
      ~display="flex",
      ~alignSelf="flex-end",
      ~width=`${width->Float.toString}px`,
      ~backgroundColor=Colors.neutralColor00,
      ~paddingLeft=Spaces.largePx,
      ~paddingRight="5px",
      ~paddingBottom=Spaces.xnormalPx,
      (),
    )

  @react.component
  let make = React.memo((~column, ~state, ~layout, ~disabled) => {
    let ref = React.useRef(Js.Nullable.null)->ReactDOM.Ref.domRef
    let {columnHeaderProps} = ReactAria.Table.ColumnHeader.use(~props={node: column}, ~state, ~ref)
    let {checkboxProps} = ReactAria.Table.Selection.useCheckAll(~state)

    let width = layout.TableLayout.ariaState.getColumnWidth(column.key)

    <ReactAria.Spread props=columnHeaderProps>
      <th ref style={style(~width)}>
        {switch state.selectionManager.selectionMode {
        | #multiple if !disabled =>
          <Checkbox
            accessibilityLabel="select all"
            ariaProps=checkboxProps
            value=checkboxProps.selected
            indeterminate=checkboxProps.indeterminate
          />
        | #none | #single | #multiple =>
          <ReactAria.VisuallyHidden>
            {checkboxProps.\"aria-label"->React.string}
          </ReactAria.VisuallyHidden>
        }}
      </th>
    </ReactAria.Spread>
  })
}

module ColumnHeader = {
  let style = (
    ~sortable,
    ~fromFirstColumn,
    ~fromLastColumn,
    ~minWidth,
    ~width,
    ~left,
    ~sticky,
    ~sticking,
    ~margin=#xsmall,
    ~alignX=#flexStart,
    (),
  ) =>
    ReactDOM.Style.make(
      ~display="flex",
      ~alignSelf="flex-end",
      ~position=sticky ? "sticky" : "initial",
      ~left=`${left->Float.toString}px`,
      ~zIndex=sticking ? "2" : "0",
      ~backgroundColor=Colors.neutralColor00,
      ~minWidth=`${minWidth->Float.toString}px`,
      ~width=`${width->Float.toString}px`,
      ~lineHeight="14px",
      ~textAlign="start",
      ~boxSizing="border-box",
      ~boxShadow=`${sticking ? Colors.neutralColor00 : Colors.transparent} 4px 0px 16px 4px`,
      ~paddingLeft=fromFirstColumn ? Spaces.largePx : Spaces.xnormalPx,
      ~paddingRight=fromLastColumn ? "15px" : `${margin->Spaces.toFloat->Float.toString}px`,
      ~paddingBottom=Spaces.xnormalPx,
      ~cursor=sortable ? "pointer" : "default",
      ~color=Colors.neutralColor30,
      ~justifyContent=switch alignX {
      | #flexStart => "start"
      | #center => "center"
      | #flexEnd => "end"
      },
      (),
    )

  @react.component
  let make = React.memo((~column, ~state, ~layout) => {
    let (ref, hovered) = Hover.use()
    let domRef = ref->ReactDOM.Ref.domRef
    let {columnHeaderProps} = ReactAria.Table.ColumnHeader.use(
      ~props={node: column},
      ~state,
      ~ref=domRef,
    )
    let {focusProps} = ReactAria.Focus.useRing()

    // NOTE - binds the header to compute layout
    column.props.extraColumnProps.ref = Some(ref)

    let breakpoint = column.props.extraColumnProps.breakpoint
    let breakpointMatched = Media.use(breakpoint->Breakpoints.toMediaQuery)

    let minWidth = layout.TableLayout.ariaState.getColumnMinWidth(column.key)
    let width = layout.TableLayout.ariaState.getColumnWidth(column.key)
    let fromFirstColumn = column.index === 0
    let fromLastColumn = column.index === state.collection.columnCount - 1
    let {?margin, ?alignX} = column.props.extraColumnProps
    let sticky = column.props.extraColumnProps.sticky->Option.getWithDefault(false)

    let (left, sticking) = React.useMemo1(
      () =>
        TableLayout.computeColumnStickyPositionState(
          ~index=column.index,
          ~layoutState=layout.state,
          ~scrolled=layout.scrollBarX.position > 0.,
        ),
      [layout.scrollBarX.position],
    )

    let sortDirection =
      state.sortDescriptor
      ->Js.Nullable.toOption
      ->Option.flatMap(descriptor =>
        descriptor.column === column.key ? Some(descriptor.direction) : None
      )
    let sortable = column.props.allowsSorting
    let sortableOrSorted = sortDirection->Option.isSome || (hovered && sortable)

    if breakpointMatched {
      <ReactAria.Spread props={ReactAria.mergeProps2(columnHeaderProps, focusProps)}>
        <th
          ref=domRef
          colSpan=column.colspan
          style={style(
            ~sortable,
            ~fromFirstColumn,
            ~fromLastColumn,
            ~minWidth,
            ~width,
            ~margin?,
            ~alignX?,
            ~sticky,
            ~sticking,
            ~left,
            (),
          )}>
          <TextStyle
            variation={sortableOrSorted ? #neutral : #normal}
            weight={sortDirection->Option.isSome ? #semibold : #medium}
            size=#tiny
            maxLines=1>
            column.rendered
          </TextStyle>
          {switch sortDirection {
          | Some(direction) if sortable =>
            <Svg width="14" height="13" viewBox="0 0 20 20">
              <Svg.Path
                d={direction === #ascending
                  ? "M 4.707 9.707 C 4.317 10.098 3.683 10.098 3.293 9.707 C 2.902 9.317 2.902 8.683 3.293 8.293 L 10 1.586 L 16.707 8.293 C 17.098 8.683 17.098 9.317 16.707 9.707 C 16.317 10.098 15.683 10.098 15.293 9.707 L 11 5.414 L 11 19 C 11 19.552 10.552 20 10 20 C 9.448 20 9 19.552 9 19 L 9 5.414 L 4.707 9.707 Z"
                  : "M4.70711 11.2929C4.31658 10.9024 3.68342 10.9024 3.29289 11.2929C2.90237 11.6834 2.90237 12.3166 3.29289 12.7071L10 19.4142L16.7071 12.7071C17.0976 12.3166 17.0976 11.6834 16.7071 11.2929C16.3166 10.9024 15.6834 10.9024 15.2929 11.2929L11 15.5858V2C11 1.44772 10.5523 1 10 1C9.44772 1 9 1.44772 9 2V15.5858L4.70711 11.2929Z"}
                fill=Colors.neutralColor90
                transform="translate(2 1)"
              />
            </Svg>
          | None if sortable =>
            <Svg width="14" height="13" viewBox="0 0 20 20">
              <Svg.Path
                d="M16.2929 5.70714C16.6834 6.09766 17.3166 6.09766 17.7071 5.70714C18.0976 5.31661 18.0976 4.68345 17.7071 4.29292L14 0.585815L10.2929 4.29292C9.90237 4.68345 9.90237 5.31661 10.2929 5.70714C10.6834 6.09766 11.3166 6.09766 11.7071 5.70714L13 4.41424V14C13 14.5523 13.4477 15 14 15C14.5523 15 15 14.5523 15 14V4.41424L16.2929 5.70714ZM3.70711 14.2929C3.31658 13.9024 2.68342 13.9024 2.29289 14.2929C1.90237 14.6834 1.90237 15.3166 2.29289 15.7071L6 19.4142L9.70711 15.7071C10.0976 15.3166 10.0976 14.6834 9.70711 14.2929C9.31658 13.9024 8.68342 13.9024 8.29289 14.2929L7 15.5858L7 6.00006C7 5.44778 6.55229 5.00006 6 5.00006C5.44771 5.00006 5 5.44778 5 6.00006L5 15.5858L3.70711 14.2929Z"
                fill={hovered ? Colors.neutralColor90 : Colors.neutralColor35}
                transform="translate(2 1)"
              />
            </Svg>
          | _ => React.null
          }}
          {switch column.props.extraColumnProps.headerActionComponent {
          | Some(headerAction) =>
            <div style={ReactDOMStyle.make(~marginLeft="3px", ~marginTop="1px", ())}>
              {headerAction}
            </div>
          | None => React.null
          }}
        </th>
      </ReactAria.Spread>
    } else {
      React.null
    }
  })
}

module RowGroup = {
  let theadStyle = ReactDOM.Style.make(
    ~display="block",
    ~minHeight="26px",
    ~overflow="auto",
    ~borderBottom="1px solid",
    ~borderBottomColor=Colors.neutralColor15, // REVIEW - frame Colors.neutralColor20 ?
    (),
  )

  let tbodyStyle = (~minHeight, ~maxHeight, ~hasEvenRows, ~hasRows) => {
    let minHeight =
      minHeight->Option.mapWithDefault("auto", height => height->Float.toString ++ "px")
    let maxHeight =
      maxHeight->Option.mapWithDefault("auto", height => height->Float.toString ++ "px")
    ReactDOM.Style.make(
      ~display="flex",
      ~flexDirection="column",
      // NOTE - "0" allows closing a popover clicking the blank area of the table
      // while "1" is still required to center the placeholders when there's no data.
      ~flexGrow=hasRows ? "0" : "1",
      ~overflow="auto",
      ~minHeight,
      ~maxHeight,
      ~backgroundColor=hasEvenRows ? Colors.neutralColor00 : Colors.neutralColor05,
      (),
    )
  }

  @react.component
  let make = React.memo((~children, ~type_, ~layout, ~minHeight=?, ~maxHeight=?) => {
    let {rowGroupProps} = ReactAria.Table.RowGroup.use()
    let headerRef = layout.TableLayout.headerRef->ReactDOM.Ref.domRef
    let bodyRef = layout.TableLayout.bodyRef->ReactDOM.Ref.domRef

    let hasEvenRows = mod(children->React.Children.count - 1, 2) === 0
    let hasRows = children->React.Children.count - 1 > 0

    <ReactAria.Spread props=rowGroupProps>
      {switch type_ {
      | #thead => <thead ref=headerRef style=theadStyle> children </thead>
      | #tbody =>
        <tbody ref=bodyRef style={tbodyStyle(~minHeight, ~maxHeight, ~hasEvenRows, ~hasRows)}>
          children
        </tbody>
      }}
    </ReactAria.Spread>
  })
}

module Row = {
  let theadRowStyle = ReactDOM.Style.make(~display="flex", ())

  let tbodyRowErrorMarkerStyle = ReactDOM.Style.make(
    ~position="absolute",
    ~zIndex="2",
    ~width="2px",
    ~height="100%",
    ~backgroundColor=Colors.dangerColor50,
    (),
  )

  let tbodyRowStyle = (~even, ~selectable, ~selected, ~hovered, ~errored, ~minWidth, ~compact) => {
    let rowColor = if errored {
      Colors.dangerColor05
    } else if selected {
      Colors.brandColor00
    } else if hovered {
      Colors.neutralColor10
    } else if even {
      Colors.neutralColor05
    } else {
      Colors.neutralColor00
    }

    ReactDOM.Style.make(
      ~display="flex",
      ~position="relative",
      ~minWidth=`${minWidth->Float.toString}px`,
      ~minHeight=compact ? "46px" : "unset",
      ~maxHeight=compact ? "46px" : "unset",
      ~boxShadow=`${rowColor} -1px 0 0 0`,
      ~backgroundColor=rowColor,
      ~cursor=selectable ? "default" : "auto",
      (),
    )
  }

  @react.component
  let make = React.memo((~children, ~item, ~state, ~layout, ~errored, ~compactRows as compact) => {
    let (ref, hovered) = Hover.use()
    let ref = ref->ReactDOM.Ref.domRef
    let {rowProps} = ReactAria.Table.Row.use(~props={node: item}, ~state, ~ref)
    let {focusProps, focusVisible} = ReactAria.Focus.useRing()

    let selectable = state.selectionManager.selectionMode !== #none
    let selected = state.selectionManager.isSelected(. ~key=item.key)
    let disabled = state.selectionManager.isDisabled(. ~key=item.key)

    <ReactAria.Spread props={ReactAria.mergeProps2(rowProps, focusProps)}>
      {switch item.type_ {
      | #thead => <tr ref style=theadRowStyle> children </tr>
      | #tbody =>
        let even = mod(item.index, 2) === 0
        let hovered = hovered && !disabled
        let minWidth = layout.TableLayout.tableWidth->Option.getWithDefault(0.)

        <tr
          ref
          className=?{focusVisible ? Some("focus-visible-ring") : None}
          style={tbodyRowStyle(
            ~even,
            ~selectable,
            ~selected,
            ~hovered,
            ~errored,
            ~minWidth,
            ~compact,
          )}>
          {if errored {
            <div style=tbodyRowErrorMarkerStyle />
          } else {
            React.null
          }}
          children
        </tr>
      }}
    </ReactAria.Spread>
  })
}

module CellSelect = {
  let style = (~width, ~fromLastRow) =>
    ReactDOM.Style.make(
      ~display="flex",
      ~backgroundColor="inherit",
      ~width=`${width->Float.toString}px`,
      ~paddingLeft=Spaces.largePx,
      ~paddingRight="5px",
      ~paddingTop="1px",
      ~borderBottom=!fromLastRow ? `1px solid ${Colors.neutralColor15}` : "unset",
      (),
    )

  @react.component
  let make = React.memo((~fromLastRow, ~cell, ~state, ~layout) => {
    let ref = React.useRef(Js.Nullable.null)->ReactDOM.Ref.domRef
    let {gridCellProps} = ReactAria.Table.Cell.use(~props={node: cell}, ~state, ~ref)
    let {checkboxProps} = ReactAria.Table.Selection.useCheck(~props={key: cell.parentKey}, ~state)

    let width = layout.TableLayout.ariaState.getColumnWidth(cell.column.key)

    <ReactAria.Spread props=gridCellProps>
      <td ref style={style(~width, ~fromLastRow)}>
        <Checkbox
          accessibilityLabel="select"
          ariaProps=checkboxProps
          value=checkboxProps.selected
          disabled=checkboxProps.disabled
        />
      </td>
    </ReactAria.Spread>
  })
}

module Cell = {
  let style = (
    ~fromFirstColumn,
    ~fromLastColumn,
    ~fromLastRow,
    ~minWidth,
    ~width,
    ~left,
    ~sticky=false,
    ~sticking=false,
    ~margin=#xsmall,
    ~alignX=#flexStart,
    ~alignY=#center,
    ~compact,
    (),
  ) =>
    ReactDOM.Style.make(
      ~display="flex",
      ~flexDirection="column",
      ~position=sticky ? "sticky" : "initial",
      ~left=`${left->Float.toString}px`,
      ~zIndex=sticking ? "2" : "0",
      ~backgroundColor="inherit",
      ~minWidth=`${minWidth->Float.toString}px`,
      ~width=`${width->Float.toString}px`,
      ~boxShadow=sticking ? "inherit" : "unset",
      ~borderRight=`1px solid ${sticking ? Colors.neutralColor20 : Colors.transparent}`,
      ~boxSizing="border-box",
      ~paddingLeft=fromFirstColumn ? Spaces.largePx : Spaces.xnormalPx,
      ~paddingRight=fromLastColumn ? "15px" : `${margin->Spaces.toFloat->Float.toString}px`,
      ~paddingTop=compact ? Spaces.smallPx : Spaces.xnormalPx,
      ~paddingBottom=compact ? Spaces.smallPx : Spaces.xnormalPx,
      ~borderBottom=!fromLastRow ? `1px solid ${Colors.neutralColor15}` : "unset",
      ~alignItems=switch alignX {
      | #flexStart => "flex-start"
      | #center => "center"
      | #flexEnd => "flex-end"
      | _ => "normal"
      },
      ~justifyContent=switch alignY {
      | #flexStart => "flex-start"
      | #center => "center"
      | #flexEnd => "flex-end"
      | _ => "normal"
      },
      (),
    )

  @react.component
  let make = React.memo((~fromLastRow, ~compactRows as compact, ~cell, ~state, ~layout) => {
    let ref = React.useRef(Js.Nullable.null)->ReactDOM.Ref.domRef
    let {gridCellProps} = ReactAria.Table.Cell.use(~props={node: cell}, ~state, ~ref)
    let {focusProps, focusVisible} = ReactAria.Focus.useRing()

    let {breakpoint}: extraColumnProps = cell.column.props.extraColumnProps
    let breakpointMatched = Media.use(breakpoint->Breakpoints.toMediaQuery)

    let width = layout.TableLayout.ariaState.getColumnWidth(cell.column.key)
    let minWidth = layout.TableLayout.ariaState.getColumnMinWidth(cell.column.key)
    let initialWidthRef = React.useRef(
      width > 0. ? width : cell.column.props.defaultWidth->Option.getWithDefault(minWidth),
    )

    // TODO - optional: `fit-content` hook to update column based on highest cell width

    React.useLayoutEffect1(() => {
      if !breakpointMatched {
        layout.ariaState.updateResizedColumns(. ~key=cell.column.key, ~width=0.)->ignore
      } else if breakpoint !== #xsmall {
        layout.ariaState.updateResizedColumns(.
          ~key=cell.column.key,
          ~width=initialWidthRef.current,
        )->ignore
      }
      None
    }, [breakpointMatched])

    let fromFirstColumn = cell.column.index === 0
    let fromLastColumn = cell.column.index === state.collection.columnCount - 1
    let {?margin, ?alignX, ?alignY} = cell.column.props.extraColumnProps
    let sticky = cell.column.props.extraColumnProps.sticky->Option.getWithDefault(false)

    let (left, sticking) = React.useMemo1(
      () =>
        TableLayout.computeColumnStickyPositionState(
          ~index=cell.column.index,
          ~layoutState=layout.state,
          ~scrolled=layout.scrollBarX.position > 0.,
        ),
      [layout.scrollBarX.position],
    )

    if breakpointMatched {
      <ReactAria.Spread props={ReactAria.mergeProps2(gridCellProps, focusProps)}>
        <td
          className=?{focusVisible ? Some("focus-visible-ring") : None}
          ref
          style={style(
            ~fromFirstColumn,
            ~fromLastColumn,
            ~fromLastRow,
            ~minWidth,
            ~width,
            ~margin?,
            ~alignX?,
            ~alignY?,
            ~sticky,
            ~sticking,
            ~left,
            ~compact,
            (),
          )}>
          cell.rendered
        </td>
      </ReactAria.Spread>
    } else {
      React.null
    }
  })
}

let verticalGradientOverflowStyle = (~right=0, ~bottom=0, ()) =>
  ReactDOM.Style.make(
    ~position="absolute",
    ~right=`${right->Int.toString}px`,
    ~bottom=`${bottom->Int.toString}px`,
    ~zIndex="2",
    ~width=Spaces.xnormalPx,
    ~height="calc(100% - 25px)", // NOTE - substractes thead's height
    ~pointerEvents="none",
    ~backgroundImage="radial-gradient(at 100% 50%, rgba(0, 0, 0, 0.12) 0%, transparent 70%)",
    (),
  )

let horizontalGradientOverflowStyle = ReactDOM.Style.make(
  ~position="absolute",
  ~left="0",
  ~right="0",
  ~bottom="0",
  ~zIndex="2",
  ~height="13px",
  ~pointerEvents="none",
  ~backgroundImage="radial-gradient(ellipse at center, rgba(0, 0, 0, 0.08) 0%, transparent 70%)",
  ~backgroundSize="100% 200%",
  (),
)

let style = (~maxWidth, ~bottomBorder) => {
  let maxWidth = maxWidth->Option.mapWithDefault("auto", width => width->Float.toString ++ "px")
  ReactDOM.Style.make(
    ~display="flex",
    ~position="relative",
    ~flexDirection="column",
    ~overflow="hidden",
    ~height="100%",
    ~width="100%",
    ~maxWidth,
    ~backgroundColor=Colors.neutralColor00,
    ~borderBottom="1px solid",
    ~borderBottomColor=bottomBorder ? Colors.neutralColor15 : Colors.transparent,
    ~borderRadius="6px",
    (),
  )
}

@new external makeKeySet: array<ReactStately.key> => ReactStately.jsSet<ReactStately.key> = "Set"

let defaultDisabledRowsKeys = []
let defaultErroredRowsMap = []

@react.component
let make = (
  ~columns,
  ~rows,
  ~keyExtractor,
  ~disabledRowsKeys=defaultDisabledRowsKeys,
  ~erroredRowsMap=defaultErroredRowsMap,
  ~selectionEnabled=false,
  ~selectAllEnabled=true,
  ~initialAllSelected=false,
  ~sortDescriptor=?,
  ~maxWidth=?,
  ~minHeight=?,
  ~maxHeight=?,
  ~compactRows=false,
  ~footerComponent=?,
  ~onLoadMore=?,
  ~onSelectChange=?,
  ~onSortChange=?,
) => {
  let ref = React.useRef(Js.Nullable.null)
  let headerRef = React.useRef(Js.Nullable.null)
  let bodyRef = React.useRef(Js.Nullable.null)

  let (selectedKeys, setSelectedKeys) = React.useState(() =>
    initialAllSelected && selectAllEnabled ? All : Selected(makeKeySet([]))
  )

  React.useEffect2(() => {
    onSelectChange->Option.forEach(fn => {
      let selectedKeys: selection = switch selectedKeys {
      | All => All
      | Selected(keys) => Selected(keys->Js.Array.from)
      }
      if selectionEnabled {
        fn(selectedKeys)
      }
    })
    None
  }, (selectedKeys, rows->Array.length > 0))

  let columns = React.useMemo1(() => {
    columns->Array.keep(column => {
      let hidden =
        column.layout->Option.flatMap(display => display.hidden)->Option.getWithDefault(false)
      hidden === false
    })
  }, [columns])

  let tableChildren = React.useMemo5(() => (
    <ReactStately.Table.Header columns>
      {(~column) => {
        let breakpoint = column.layout->Option.flatMap(display => display.breakpoint)
        let headerActionComponent =
          column.layout->Option.flatMap(layout => layout.headerActionComponent)
        let minWidth =
          column.layout
          ->Option.flatMap(display => display.minWidth)
          ->Option.map(ColumnSize.fromStaticToReactStatelyTableColumnSize)
        let width =
          column.layout
          ->Option.flatMap(display => display.width)
          ->Option.map(ColumnSize.toReactStatelyTableColumnSize)
        let defaultWidth =
          column.layout
          ->Option.flatMap(display => display.defaultWidth)
          ->Option.map(ColumnSize.toReactStatelyTableColumnSize)

        let extraColumnProps = {
          breakpoint: breakpoint->Option.getWithDefault(#xsmall),
          ?headerActionComponent,
          margin: ?column.layout->Option.flatMap(display => display.margin),
          alignX: ?column.layout->Option.flatMap(display => display.alignX),
          alignY: ?column.layout->Option.flatMap(display => display.alignY),
          sticky: ?column.layout->Option.flatMap(display =>
            display.sticky->Option.map(
              sticky => sticky && rows->Array.length <= rowsLimitStickyColumns,
            )
          ),
        }

        // NOTE - `width` is incompatible with `breakpoint`
        let (width, defaultWidth) = switch (width, breakpoint) {
        | (Some(_), Some(_)) => (None, width)
        | (Some(_), None) | (None, _) => (width, defaultWidth)
        }
        // NOTE - a column that can break must have a `minWidth` of 0
        let minWidth =
          breakpoint->Option.mapWithDefault(minWidth, _ => Some(
            0.->#px->ColumnSize.fromStaticToReactStatelyTableColumnSize,
          ))

        let allowsSorting = column.allowsSorting->Option.getWithDefault(false)

        <ReactStately.Table.Column
          key=column.key allowsSorting ?minWidth ?width ?defaultWidth extraColumnProps>
          {column.name->Option.getWithDefault("")->React.string}
        </ReactStately.Table.Column>
      }}
    </ReactStately.Table.Header>,
    <ReactStately.Table.Body items=rows>
      {(~item as row) => {
        let rowKey = keyExtractor(row)
        let disabled = disabledRowsKeys->Array.some(current => current === rowKey)
        let index =
          rows
          ->Array.getIndexBy(current => keyExtractor(current) === keyExtractor(row))
          ->Option.getWithDefault(-1)

        <ReactStately.Table.Row key=rowKey>
          {(~columnKey) => {
            let errorMessage =
              erroredRowsMap
              ->Array.getBy((error: erroredRow) => error.key === rowKey)
              ->Option.map(error => error.message)
            let column = columns->Array.getBy(column => column.key === columnKey)->Option.getUnsafe
            <ReactStately.Table.Cell key={rowKey ++ column.key}>
              {column.render({data: row, index, disabled, errorMessage})}
            </ReactStately.Table.Cell>
          }}
        </ReactStately.Table.Row>
      }}
    </ReactStately.Table.Body>,
  ), (columns, disabledRowsKeys, keyExtractor, rows, erroredRowsMap))

  let props = {
    ReactStately.Table.children: tableChildren,
    selectionMode: selectionEnabled ? #multiple : #none,
    showSelectionCheckboxes: selectionEnabled,
    allowDuplicateSelectionEvents: false,
    disabledBehavior: #all,
    disabledKeys: disabledRowsKeys,
    selectedKeys: switch selectedKeys {
    | All => Obj.magic("all")
    | Selected(keys) => keys
    },
    ?sortDescriptor,
    ?onSortChange,
    // NOTE - get around for polymorphic `selection` type
    // TODO - use `Untagged variants` ReScript v11 feature
    onSelectionChange: keys => {
      let value = switch Js.typeof(keys) {
      | "string" => All
      | _ => Selected(keys->Obj.magic)
      }
      setSelectedKeys(prev =>
        switch (value, selectAllEnabled) {
        | (All, false) => prev
        | (All, true) => value
        | (Selected(_), _) => value
        }
      )
    },
  }
  let state = ReactStately.Table.useState(~props)

  let layout = TableLayout.use(~ref, ~headerRef, ~bodyRef, ~state, ~onLoadMore)

  let props = {
    ReactAria.Table.\"aria-label": "table",
    scrollRef: bodyRef->ReactDOM.Ref.domRef,
  }
  let {gridProps} = ReactAria.Table.use(~props, ~state, ~ref=ref->ReactDOM.Ref.domRef)

  React.useMemo7(() =>
    <ReactAria.Spread props=gridProps>
      <table
        ref={ref->ReactDOM.Ref.domRef}
        style={style(~maxWidth, ~bottomBorder=layout.scrollBarY.scrollable)}>
        <RowGroup type_=#thead layout>
          {state.collection.headerRows
          ->Array.map(headerRow =>
            <HeaderRow key=headerRow.key item=headerRow state layout>
              {headerRow.childNodes
              ->ReactStately.Table.collectionToArray
              ->Array.map(
                column =>
                  column.props.selectionCell
                    ? <ColumnHeaderSelectAll
                        key=column.key column state layout disabled={!selectAllEnabled}
                      />
                    : <ColumnHeader key=column.key column state layout />,
              )
              ->React.array}
            </HeaderRow>
          )
          ->React.array}
        </RowGroup>
        <RowGroup type_=#tbody layout ?minHeight ?maxHeight>
          {state.collection.body.childNodes
          ->ReactStately.Table.collectionToArray
          ->Array.mapWithIndex((rowIdx, row) => {
            let errored = erroredRowsMap->Array.some(error => error.key === row.key)
            <Row key=row.key item=row state layout compactRows errored>
              {row.childNodes
              ->ReactStately.Table.collectionToArray
              ->Array.map(
                cell => {
                  let fromLastRow = rowIdx === state.collection.size - 1
                  cell.props.selectionCell
                    ? <CellSelect key=cell.key fromLastRow cell state layout />
                    : <Cell key=cell.key fromLastRow compactRows cell state layout />
                },
              )
              ->React.array}
            </Row>
          })
          ->React.array}
          {footerComponent->Option.getWithDefault(React.null)}
        </RowGroup>
        <AnimatedRender displayed=layout.scrollBarX.scrollable animation=#fade duration=450>
          <div style={verticalGradientOverflowStyle(~right=?layout.scrollBarY.size, ())} />
        </AnimatedRender>
        <AnimatedRender displayed=layout.scrollBarY.scrollable animation=#fade duration=450>
          <div style=horizontalGradientOverflowStyle />
        </AnimatedRender>
      </table>
    </ReactAria.Spread>
  , (
    selectAllEnabled && selectionEnabled,
    selectedKeys,
    tableChildren,
    columns->Array.some(c =>
      c.layout->Option.flatMap(l => l.sticky)->Option.getWithDefault(false)
    ) && rows->Array.length <= rowsLimitStickyColumns
      ? layout.scrollBarX.position
      : 0.,
    layout.ariaState.updateResizedColumns, // NOTE - do not stringify functions
    Json.stringifyAny((layout.tableWidth, layout.scrollBarY.scrollable, layout.scrollBarY.size)),
    Json.stringifyAny((state.selectionManager.focusedKey, state.sortDescriptor)),
  ))
}
