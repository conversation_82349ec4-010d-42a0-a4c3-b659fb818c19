@genType @react.component
let make: (
  ~data: AsyncResult.t<array<'data>, 'error>,
  ~columns: array<Table.column<'data>>,
  ~keyExtractor: 'data => ReactStately.key,
  ~disabledRowsKeys: array<ReactStately.key>=?,
  ~erroredRowsMap: array<Table.erroredRow>=?,
  ~selectionEnabled: bool=?,
  ~selectAllEnabled: bool=?,
  ~initialAllSelected: bool=?,
  ~hideCard: bool=?, // TODO - to be reworked and renamed
  ~hideReloadingPlaceholder: bool=?, // TODO - temporary props
  ~maxWidth: float=?,
  ~minHeight: float=?,
  ~maxHeight: float=?,
  ~compactRows: bool=?,
  ~placeholderEmptyState: React.element=?,
  ~searchBar: React.element=?,
  ~searchPlaceholder: string=?,
  ~filters: array<React.element>=?,
  ~typesDrop: array<FilePicker.fileType>=?,
  ~sortDescriptor: ReactStately.Table.sortDescriptor=?,
  ~onSortChange: ReactStately.Table.sortDescriptor => unit=?,
  ~onSearchQueryChange: string => unit=?,
  ~onLoadMore: unit => unit=?,
  ~onSelectChange: Table.selection => unit=?,
  ~onSuccessDrop: File.t => unit=?,
  ~onErrorDrop: string => unit=?,
) => React.element
