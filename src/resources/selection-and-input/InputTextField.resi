type suggestionVariationProps = {
  opened: bool,
  toggleButtonDisabled: bool,
  toggleButtonProps: ReactAria.Button.props,
  onRequestClear: unit => unit,
}
@genType
type variation = [
  | #normal
  | #suggestion(suggestionVariationProps)
]

let textInputStyle: (~disabled: bool) => ReactDOM.Style.t

@genType @react.component
let make: (
  ~variation: variation=?,
  ~label: string,
  ~required: bool=?,
  ~tooltip: React.element=?,
  ~fieldAction: Field.Action.t=?,
  ~disabled: bool=?,
  ~bordered: bool=?,
  ~focused: bool=?,
  ~placeholder: string=?,
  ~secureTextEntry: bool=?,
  ~autoTrim: bool=?,
  ~autoFocus: bool=?,
  ~errorMessage: string=?,
  ~containerRef: ReactDOM.Ref.currentDomRef=?,
  ~inputRef: ReactDOM.Ref.currentDomRef=?,
  ~ariaProps: JsxDOM.domProps=?,
  ~value: string=?,
  ~onChange: string => unit=?,
  ~onFocus: unit => unit=?,
  ~onBlur: unit => unit=?,
) => React.element
