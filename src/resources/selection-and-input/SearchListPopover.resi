@react.component
let make: (
  ~children: React.element,
  ~expendableWidth: bool,
  ~items: option<array<'item>>,
  ~itemToValue: 'item => string,
  ~itemToOption: 'item => string,
  ~opened: bool=?,
  ~searchQueryDelay: int=?,
  ~onSearchQueryChange: string => unit=?,
  ~onChange: 'item => unit,
  ~onToggle: bool => unit,
  ~onEndReached: int => unit,
  ~renderEndItem: unit => React.element=?,
  ~renderEndActions: unit => array<React.element>=?,
) => React.element
