@react.component
let make = React.memo((~placeholder, ~value="", ~autoFocus=true, ~onChange) => {
  let mounted = React.useRef(false)
  let (focused, setFocused) = React.useState(() => false)
  let (searchQuery, setSearchQuery) = React.useState(() => value)
  let debouncedSearchQuery = ReactUpdateDebounced.use(searchQuery, ~delay=500)

  React.useEffect1(() => {
    if mounted.current {
      onChange(debouncedSearchQuery)
    }
    None
  }, [debouncedSearchQuery])

  React.useEffect1(() => {
    if mounted.current {
      setSearchQuery(_ => value)
    }
    None
  }, [value])

  React.useEffect0(() => {
    mounted.current = true
    Some(() => mounted.current = false)
  })

  <InputSearch
    loading=false
    onRequestClear={() => setSearchQuery(_ => "")}
    focused
    placeholder
    autoFocus
    value=searchQuery
    onChange={value => setSearchQuery(_ => value)}
    onFocus={_ => setFocused(_ => true)}
    onBlur={_ => setFocused(_ => false)}
  />
})
