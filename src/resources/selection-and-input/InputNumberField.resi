@genType
type appender =
  | Currency(Intl.currency)
  | Percent
  | Custom(string)

@genType let appenderCurrency: Intl.currency => appender
@genType let appenderPercent: appender
@genType let appenderCustom: string => appender

module OptionalValue: {
  @deprecated("Use <InputOptionalNumberField> alias instead") @react.component
  let make: (
    ~value: option<float>,
    ~minValue: float=?,
    ~maxValue: float=?,
    ~step: float=?,
    ~minPrecision: int=?,
    ~precision: int=?,
    ~label: string=?,
    ~tooltip: React.element=?,
    ~errorMessage: string=?,
    ~placeholder: string=?,
    ~appender: appender=?,
    ~shrinkInput: bool=?,
    ~autoFocused: bool=?,
    ~focused: bool=?,
    ~required: bool=?,
    ~disabled: bool=?,
    ~strongStyle: bool=?,
    ~hideStepper: bool=?,
    ~useGrouping: bool=?,
    ~allowsSideEffectChange: bool=?,
    ~onChange: option<float> => unit,
    ~onFocus: unit => unit=?,
    ~onBlur: unit => unit=?,
    ~testLocalization: Intl.locale=?,
  ) => React.element
}

@genType @react.component
let make: (
  ~value: float,
  ~minValue: float=?,
  ~maxValue: float=?,
  ~step: float=?,
  ~minPrecision: int=?,
  ~precision: int=?,
  ~label: string=?,
  ~tooltip: React.element=?,
  ~errorMessage: string=?,
  ~placeholder: string=?,
  ~appender: appender=?,
  ~shrinkInput: bool=?,
  ~autoFocused: bool=?,
  ~focused: bool=?,
  ~required: bool=?,
  ~disabled: bool=?,
  ~strongStyle: bool=?,
  ~hideStepper: bool=?,
  ~useGrouping: bool=?,
  ~allowsSideEffectChange: bool=?,
  ~onChange: float => unit,
  ~onFocus: unit => unit=?,
  ~onBlur: unit => unit=?,
  ~testLocalization: Intl.locale=?,
) => React.element
