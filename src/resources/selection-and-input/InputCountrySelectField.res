@module("src/assets/countries-fr-FR.json") external countriesFrFR: array<string> = "default"
@module("src/assets/subdivisions-fr-FR.json") external subdivisionsFrFR: array<string> = "default"

let countries = (~locale) =>
  switch locale {
  | #"fr-FR" => countriesFrFR
  | #"en-EN" => []
  }
let subdivisions = (~locale) =>
  switch locale {
  | #"fr-FR" => subdivisionsFrFR
  | #"en-EN" => []
  }

let countriesAndSubdivisions = (~locale) =>
  Array.concat(countries(~locale), subdivisions(~locale))->SortArray.stableSortBy((current, next) =>
    Js.String.localeCompare(next, current)->Float.toInt
  )

@react.component
let make = (
  ~value,
  ~onChange,
  ~subdivisionsIncluded=false,
  ~required=false,
  ~errorMessage=?,
  ~testLocalization=?,
) => {
  let locale = testLocalization->Option.getWithDefault(Intl.locale)
  let countriesAndSubdivisions = if subdivisionsIncluded {
    countriesAndSubdivisions(~locale)
  } else {
    countries(~locale)
  }
  let items = countriesAndSubdivisions->Array.map(country => {
    Select.key: country,
    label: country,
    value: country,
  })
  let sections = [{Select.items: items}]

  <Select
    preset={#inputField({required, ?errorMessage})}
    label={Intl.t("Country")}
    placeholder={Intl.t("Select a country")}
    sections
    value
    onChange
  />
}
