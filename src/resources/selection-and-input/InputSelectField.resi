@react.component
let make: (
  ~label: string,
  ~errorMessage: string=?,
  ~placeholder: string=?,
  ~required: bool=?,
  ~disabled: bool=?,
  ~tooltip: React.element=?,
  ~onChange: 'value => unit,
  ~searchable: bool=?,
  ~renderItemContent: Select.item<'value> => React.element=?,
  ~renderTriggerView: (
    ~children: React.element,
    ~item: option<Select.item<'value>>,
    ~hovered: bool,
    ~active: bool,
    ~focused: bool,
  ) => React.element=?,
  ~value: 'value,
  ~sections: array<Select.section<'value>>,
) => React.element
