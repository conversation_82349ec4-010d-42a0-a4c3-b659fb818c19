open Intl
open StyleX

let getMaxDate = () => Js.Date.make()->DateHelpers.endOfDay
let isOutsideRange = date => date->Js.Date.getTime > getMaxDate()->Js.Date.getTime

let styles = StyleX.create({
  "presets": style(
    ~minHeight="342px",
    ~padding="20px",
    ~borderRight="1px solid " ++ Colors.neutralColor15,
    (),
  ),
})

let {defaultFocusedInput} = module(DayPickerRange)

@react.component
let make = (
  ~focusedInput=defaultFocusedInput,
  ~visibleMonth,
  ~activePreset,
  ~presets,
  ~formatPreset,
  ~isEqualPreset,
  ~range,
  ~disabledResetButton=false,
  ~onChangePreset,
  ~onRequestReset,
  ~onDatesChange,
  ~onFocusChange,
  ~onPrevMonthClick,
  ~onNextMonthClick,
) => {
  let onPrevMonthClick = date => onPrevMonthClick(date)
  let onNextMonthClick = date => onNextMonthClick(date)
  let initialVisibleMonth = () => visibleMonth
  let (startDate, endDate) = range

  <Inline>
    <DivX style={StyleX.props([styles["presets"]])}>
      <Box spaceBottom=#small>
        <TextStyle variation=#normal size=#xxsmall weight=#semibold>
          {t("Period")->React.string}
        </TextStyle>
      </Box>
      <InputRadioGroupField
        required=false
        options=presets
        optionToText=formatPreset
        value=activePreset
        isEqualValue=isEqualPreset
        onChange=onChangePreset
      />
      {if !disabledResetButton {
        <Box spaceY=#xnormal>
          <TextIconButton
            size=#small
            icon=#delete_light
            disabled={snd(range)->Js.Nullable.fromOption->Js.Nullable.isNullable}
            onPress=onRequestReset>
            {t("Reset period")->React.string}
          </TextIconButton>
        </Box>
      } else {
        React.null
      }}
    </DivX>
    <DayPickerRange
      key={visibleMonth->Js.Date.toDateString}
      initialVisibleMonth
      startDate
      endDate
      focusedInput
      onChange=onDatesChange
      onFocusChange
      onPrevMonthClick
      onNextMonthClick
      maxDate={getMaxDate()}
      isOutsideRange
    />
  </Inline>
}
