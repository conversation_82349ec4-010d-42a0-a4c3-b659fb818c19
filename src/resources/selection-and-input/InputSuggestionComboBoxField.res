type item = {value: string}

@react.component
let make = (
  ~label,
  ~required=?,
  ~disabled=false,
  ~focused=false,
  ~placeholder=?,
  ~errorMessage=?,
  ~items: array<item>,
  ~value,
  ~onChange,
  ~onFocus=?,
  ~onBlur=?,
) => {
  let derivedFocused = focused

  let (focused, setFocused) = React.useState(() => derivedFocused)
  let {contains} = ReactAria.Filter.use({sensitivity: #base})
  let (itemsLimit, setItemsLimit) = React.useState(_ => ListBox.defaultItemsPerScroll)

  let triggerRef = React.useRef(Js.Nullable.null)
  let inputRef = React.useRef(Js.Nullable.null)
  let popoverRef = React.useRef(Js.Nullable.null)
  let listBoxRef = React.useRef(Js.Nullable.null)

  ReactUpdateEffect.use1(() => {
    setFocused(_ => derivedFocused)
    None
  }, [derivedFocused])

  let children = React.useMemo1(() => [
    <ReactStately.Collection.Section title={Intl.t("Suggestions")->React.string}>
      {items
      ->Array.map(item =>
        <ReactStately.Collection.Item key=item.value textValue=item.value>
          <span> {item.value->React.string} </span>
        </ReactStately.Collection.Item>
      )
      ->React.array}
    </ReactStately.Collection.Section>,
  ], [items])

  let onFocusChange = React.useCallback0(focused => setFocused(_ => focused))
  let props = {
    ReactStately.ComboBox.children,
    \"aria-label": label,
    disabled,
    ?placeholder,
    menuTrigger: #input,
    defaultFilter: contains,
    allowsCustomValue: true,
    allowsEmptyCollection: false,
    defaultSelectedKey: value,
    inputValue: value,
    onInputChange: onChange,
    onFocusChange,
    ?onFocus,
    ?onBlur,
  }

  let state = ReactStately.ComboBox.useState(~props)
  let {inputProps, listBoxProps, buttonProps} = ReactAria.ComboBox.use(
    ~props={
      ...props,
      inputRef: inputRef->ReactDOM.Ref.domRef,
      popoverRef: popoverRef->ReactDOM.Ref.domRef,
      listBoxRef: listBoxRef->ReactDOM.Ref.domRef,
    },
    ~state,
  )

  let onScroll = React.useCallback0(event => {
    let element =
      event
      ->ReactEvent.UI.currentTarget
      ->ReactDomEventTarget.toUnsafeDomEventTarget
      ->WebAPI.EventTarget.unsafeAsDomElement
    let scrollHeight = element->WebAPI.DomElement.scrollHeight
    let clientHeight = element->WebAPI.DomElement.clientHeight
    let scrollOffset = scrollHeight - clientHeight * 2
    if element->WebAPI.DomElement.scrollTop > scrollOffset->Int.toFloat {
      setItemsLimit(itemsLimit => itemsLimit + ListBox.defaultItemsPerScroll)
    }
  })

  let openedList = state.opened
  let errored = errorMessage->Option.isSome
  let hasNoItem = items->Array.length === 0
  let focused = focused || openedList

  <>
    <InputTextField
      variation={#suggestion({
        opened: openedList,
        toggleButtonDisabled: hasNoItem,
        toggleButtonProps: buttonProps,
        onRequestClear: () => onChange(""),
      })}
      label
      disabled
      focused
      ?required
      ?placeholder
      ?errorMessage
      containerRef=triggerRef
      inputRef
      ariaProps=inputProps
    />
    {if openedList {
      <Popover
        modal=false
        layout=#triggerStrictWidth
        offset=?{errored ? Some(-16.) : None}
        popoverRef
        triggerRef
        state={
          opened: state.opened,
          onRequestClose: state.onRequestClose,
          onRequestToggle: state.onRequestToggle,
        }>
        <ListBox
          itemsLimit
          props=listBoxProps
          domRef={listBoxRef->ReactDOM.Ref.domRef}
          state={state->ReactStately.ListBox.fromComboBoxState}
          onScroll
        />
      </Popover>
    } else {
      React.null
    }}
  </>
}
