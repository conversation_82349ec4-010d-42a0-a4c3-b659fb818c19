let style = (~focused, ~hovered, ~errored, ~disabled) =>
  ReactDOM.Style.make(
    ~display="flex",
    ~backgroundColor=TextFieldStyle.backgroundColor(~disabled),
    ~borderRadius=TextFieldStyle.borderRadiusPx,
    ~outline="1px solid",
    ~outlineOffset="-1px",
    ~outlineColor=TextFieldStyle.borderColor(~focused, ~hovered, ~errored, ~disabled),
    (),
  )

let textAreaStyle = (~disabled) =>
  ReactDOM.Style.make(
    ~flex="1",
    ~boxSizing="border-box",
    ~padding=`${Spaces.xnormal->Float.toString}px ${Spaces.normal->Float.toString}px`,
    ~backgroundColor=TextFieldStyle.backgroundColor(~disabled),
    ~color=TextFieldStyle.color(~disabled),
    ~fontSize=TextFieldStyle.fontSizePx,
    (),
  )

let useFocus = (~focused=?, ~onFocus=?, ~onBlur=?, ()) =>
  switch (focused, onFocus, onBlur) {
  | (Some(focused), Some(onFocus), Some(onBlur)) => (focused, onFocus, onBlur)
  | (_, _, _) =>
    let (focused, setFocused) = React.useState(_ => false)
    let onFocus = () => setFocused(_ => true)
    let onBlur = () => setFocused(_ => false)
    (focused, onFocus, onBlur)
  }

@react.component
let make = (
  ~label=?,
  ~focused=?,
  ~required=?,
  ~tooltip=?,
  ~disabled=false,
  ~placeholder=?,
  ~errorMessage=?,
  ~ariaProps=?,
  ~value,
  ~onChange=?,
  ~onFocus=?,
  ~onBlur=?,
) => {
  let (ref, hovered) = Hover.use()
  let (focused, onFocus, onBlur) = useFocus(~focused?, ~onFocus?, ~onBlur?, ())

  let props = {
    ReactAria.Label.label: label->Option.getWithDefault(Intl.t("Textarea input field")),
    \"aria-label": label->Option.getWithDefault(Intl.t("Textarea input field")),
  }
  let {labelProps, fieldProps} = ReactAria.Label.use(~props)

  let ariaProps = ReactAria.mergeProps2(ariaProps, fieldProps)
  let errored = errorMessage->Option.isSome
  let style = style(~focused, ~hovered, ~errored, ~disabled)
  let textAreaStyle = textAreaStyle(~disabled)

  <Field ?label ?required ?tooltip labelAriaProps=labelProps ?errorMessage ref>
    <div style>
      <TextArea
        readOnly=disabled ?placeholder ariaProps style=textAreaStyle value ?onChange onFocus onBlur
      />
    </div>
  </Field>
}

let make = React.memo(make)
