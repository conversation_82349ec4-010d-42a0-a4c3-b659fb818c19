// NOTE - Legacy component, ComboBox is better

open StyleX

let styles = StyleX.create({
  "item": style(~paddingInline=Spaces.xnormalPx, ~overflow=#hidden, ()),
  "itemHovered": style(~backgroundColor=Colors.neutralColor10, ()),
  "itemCentered": style(~alignSelf=#center, ()),
  "itemText": style(
    ~font=`normal 400 15px "Libre Franklin"`,
    ~color=Colors.neutralColor90,
    ~lineHeight="36px",
    ~letterSpacing="0.125px",
    (),
  ),
})

let itemStyleFromParams = (~hovered, ~centered) =>
  StyleX.arrayStyle([
    hovered ? styles["itemHovered"] : style(),
    centered ? styles["itemCentered"] : style(),
  ])

@react.component
let make = (~children=React.null, ~name: option<string>=?, ~onPress=?) => {
  let (ref, hovered) = Hover.use()

  <DivX
    style={StyleX.props([
      styles["item"],
      itemStyleFromParams(
        ~hovered=hovered && onPress->Option.isSome,
        ~centered=name->Option.isNone,
      ),
    ])}>
    {switch (name, onPress) {
    | (Some(name), Some(onPress)) =>
      <Touchable ref onPress={_ => onPress()}>
        <SpanX style={StyleX.props([styles["itemText"]])}> {name->React.string} </SpanX>
      </Touchable>
    | _ => children
    }}
  </DivX>
}

let make = React.memo(make)
