type rule =
  | MinStr
  | MinLower
  | MinUpper
  | MinDigit

@genType let applyRule: (string, rule) => bool
@genType let minStr: rule
@genType let minLower: rule
@genType let minUpper: rule
@genType let minDigit: rule

@genType @react.component
let make: (
  ~label: string,
  ~value: string,
  ~focused: bool,
  ~required: bool=?,
  ~errorMessage: string=?,
  ~onChange: string => unit=?,
  ~onFocus: unit => unit,
  ~onBlur: unit => unit,
  ~placeholder: string=?,
  ~showTypingValidation: bool=?,
) => React.element
