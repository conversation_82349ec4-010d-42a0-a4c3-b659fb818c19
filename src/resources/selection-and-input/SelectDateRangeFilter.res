open Intl

let dateRangetoPartialDateRange = value =>
  switch value {
  | Some((startDate, endDate)) => (Some(startDate), Some(endDate))
  | None => (None, None)
  }

type triggerLabelDisplay = [#showPreset | #showDateRange]

let formatDateRange = value => {
  let (startDate, endDate) = value
  let divider = ` → `
  startDate->Intl.dateTimeFormat(~dateStyle=#medium) ++
  divider ++
  endDate->Intl.dateTimeFormat(~dateStyle=#medium)
}

module type Preset = {
  type t
  let isCustomOrNotDefined: t => bool
  let toString: (~notDefinedPlaceholder: string, t) => string
  let toDateRange: t => option<(Js.Date.t, Js.Date.t)>
  let fromPartialDateRange: ((option<Js.Date.t>, option<Js.Date.t>), array<t>) => t
  let isEqual: (t, t) => bool
}

module StandardPreset = {
  type t =
    | Today
    | Yesterday
    | CurrentWeek
    | LastWeek
    | CurrentMonth
    | LastMonth
    | CurrentYear
    | LastYear
    | Custom((option<Js.Date.t>, option<Js.Date.t>))
    | NotDefined

  let isCustomOrNotDefined = value =>
    switch value {
    | Custom(_) | NotDefined => true
    | _ => false
    }

  let toString = (~notDefinedPlaceholder, value) => {
    let divider = ` → `
    switch value {
    | Custom((Some(startDate), None)) =>
      startDate->Intl.dateTimeFormat(~dateStyle=#medium) ++ divider ++ ` …`
    | Custom((Some(startDate), Some(endDate))) if DateHelpers.isSameDay(startDate, endDate) =>
      startDate->Intl.dateTimeFormat(~dateStyle=#medium)
    | Custom((Some(startDate), Some(endDate))) => formatDateRange((startDate, endDate))
    | NotDefined => notDefinedPlaceholder
    | Today => t("Today")
    | Yesterday => t("Yesterday")
    | CurrentWeek => t("Current week")
    | LastWeek => t("Last week")
    | CurrentMonth => t("Current month")
    | LastMonth => t("Last month")
    | CurrentYear => t("Current year")
    | LastYear => t("Last year")
    | Custom(_) => t("Custom")
    }
  }

  let toDateRange = preset => {
    let {
      startOfDay,
      endOfDay,
      subDays,
      startOfWeek,
      subWeeks,
      endOfWeek,
      startOfMonth,
      subMonths,
      endOfMonth,
      startOfYear,
      subYears,
      endOfYear,
    } = module(DateHelpers)
    let now = Js.Date.fromFloat(Js.Date.now())
    switch preset {
    | Today => Some((now->startOfDay, now->endOfDay))
    | Yesterday => Some((now->startOfDay->subDays(1.), now->subDays(1.)->endOfDay))
    | CurrentWeek => Some((now->startOfWeek, now->endOfDay))
    | LastWeek => Some((now->subWeeks(1.)->startOfWeek, now->subWeeks(1.)->endOfWeek))
    | CurrentMonth => Some((now->startOfMonth, now->endOfDay))
    | LastMonth => Some((now->subMonths(1.)->startOfMonth, now->subMonths(1.)->endOfMonth))
    | CurrentYear => Some((now->startOfYear, now->endOfDay))
    | LastYear => Some((now->subYears(1.)->startOfYear, now->subYears(1.)->endOfYear))
    | Custom((Some(startDate), Some(endDate))) => Some((startDate, endDate))
    | Custom(_) | NotDefined => None
    }
  }

  let fromPartialDateRange = (range, presets) => {
    switch range {
    | (Some(startDateA), Some(endDateA)) =>
      let preset = presets->Array.getBy(preset =>
        switch preset->toDateRange {
        | Some(startDateB, endDateB)
          if DateHelpers.isSameDay(startDateA, startDateB) &&
          DateHelpers.isSameDay(endDateA, endDateB) => true
        | _ => false
        }
      )
      preset->Option.getWithDefault(Custom(range))
    | (Some(_), None) => Custom(range)
    | (None, Some(_)) | (None, None) => NotDefined
    }
  }

  let isEqual = (a, b) =>
    switch (a, b) {
    | (Today, Today)
    | (Yesterday, Yesterday)
    | (CurrentWeek, CurrentWeek)
    | (LastWeek, LastWeek)
    | (CurrentMonth, CurrentMonth)
    | (LastMonth, LastMonth)
    | (CurrentYear, CurrentYear)
    | (LastYear, LastYear)
    | (NotDefined, NotDefined)
    | (Custom(_, _), Custom((_, _))) => true
    | _ => false
    }
}

let {defaultFocusedInput} = module(DayPickerRange)

let standardPresetsArray = [
  StandardPreset.Today,
  Yesterday,
  CurrentWeek,
  LastWeek,
  CurrentMonth,
  LastMonth,
  CurrentYear,
  LastYear,
]

module Component = {
  @react.component
  let make = (
    ~value=?,
    ~label=?,
    ~placeholder="Pick a date range",
    ~disabledResetButton=?,
    ~preset: module(Preset),
    ~presets,
    ~onChange,
    ~triggerLabelDisplay,
  ) => {
    let module(Preset) = preset
    let presets: array<Preset.t> = Obj.magic(presets) // FIXME
    let mounted = React.useRef(false)

    let {ref: popoverTriggerRef, state: popover, ariaProps: popoverAriaProps} = Popover.useTrigger()
    let (_, triggerHovered) = Hover.use(~ref=popoverTriggerRef, ())
    let (focusedInput, setFocusedInput) = React.useState(_ => defaultFocusedInput)

    let (partialDateRange, setPartialDateRange) = React.useState(_ =>
      value->dateRangetoPartialDateRange
    )
    let (preset, setPreset) = React.useState(_ =>
      Preset.fromPartialDateRange(partialDateRange, presets)
    )
    let (visibleMonth, setVisibleMonth) = React.useState(() =>
      switch partialDateRange {
      | (_, Some(endDate)) => endDate
      | _ => Js.Date.make()
      }
    )

    React.useEffect1(() => {
      if mounted.current {
        switch (value, partialDateRange) {
        | (Some((valueStartDate, valueEndDate)), (Some(rangeStartDate), Some(rangeEndDate)))
          if valueStartDate->Js.Date.getTime === rangeStartDate->Js.Date.getTime &&
            valueEndDate->Js.Date.getTime === rangeEndDate->Js.Date.getTime => ()
        | (None, (None, None)) => ()
        | (value, _) => setPartialDateRange(_ => value->dateRangetoPartialDateRange)
        }
      }
      None
    }, [value])

    React.useEffect1(() => {
      if mounted.current {
        switch (value, partialDateRange) {
        | (Some((startDate, endDate)), (Some(startPartialDateRange), Some(endPartialDateRange)))
          if startDate->Js.Date.getTime !== startPartialDateRange->Js.Date.getTime ||
            endDate->Js.Date.getTime !== endPartialDateRange->Js.Date.getTime =>
          onChange(Some(startPartialDateRange, endPartialDateRange))
        | (None, (Some(startPartialDateRange), Some(endPartialDateRange))) =>
          onChange(Some(startPartialDateRange, endPartialDateRange))
        | (Some(_), (None, None)) => onChange(None)
        | (Some((_, _)), (Some(_), Some(_)))
        | (None, (None, None))
        | (_, (Some(_), None))
        | (_, (None, Some(_))) => ()
        }
        switch (preset->Preset.toDateRange, partialDateRange) {
        | (Some((startA, endA)), (Some(startB), Some(endB)))
          if startA->Js.Date.valueOf === startB->Js.Date.valueOf &&
            endA->Js.Date.valueOf === endB->Js.Date.valueOf => ()
        | _ => setPreset(_ => Preset.fromPartialDateRange(partialDateRange, presets))
        }
      }
      None
    }, [partialDateRange])

    React.useEffect1(() => {
      if mounted.current {
        switch (partialDateRange, preset, preset->Preset.toDateRange) {
        | (_, _, None) => ()
        | (
            (Some(startPartialDateRange), Some(endPartialDateRange)),
            _,
            Some((startPresetDateRange, endPresetDateRange)),
          )
          if startPartialDateRange->Js.Date.getTime === startPresetDateRange->Js.Date.getTime &&
            endPartialDateRange->Js.Date.getTime === endPresetDateRange->Js.Date.getTime => ()

        | (_, _, dateRange) => setPartialDateRange(_ => dateRange->dateRangetoPartialDateRange)
        }
        if !Preset.isCustomOrNotDefined(preset) {
          popover.onRequestClose()
        }
      }
      None
    }, [preset])

    React.useEffect1(() => {
      mounted.current = true
      Some(() => mounted.current = false)
    }, [])

    let onDatesChange = ((startDate, endDate)) =>
      setPartialDateRange(_ => (
        startDate->Option.map(DateHelpers.startOfDay),
        endDate->Option.map(DateHelpers.endOfDay),
      ))

    let onFocusChange = focusedInput =>
      setFocusedInput(_ =>
        switch focusedInput->Js.Nullable.toOption {
        | Some(focusedInput) => focusedInput
        | _ => defaultFocusedInput
        }
      )

    let onRequestReset = _ => setPartialDateRange(_ => (None, None))

    let onPrevMonthClick = date => setVisibleMonth(_ => date)
    let onNextMonthClick = date => setVisibleMonth(_ => date)

    let onChangePreset = preset => {
      setPreset(_ => preset)
      // TODO - Try to use onMonthSelect from onMonthSelect prop
      // to animate the month change. Maybe it breaks more things than it fixed ?
      // > https://github.com/airbnb/react-dates/blob/6c2cb61c5e876fbdac72e6efed49c0bd64f06d6b/stories/DayPickerRangeController.js#L418
      switch (partialDateRange, preset->Preset.toDateRange) {
      | ((_, Some(endDate)), Some(_, nextEndDate))
        if !DateHelpers.isSameMonth(endDate, nextEndDate) =>
        setVisibleMonth(_ => nextEndDate)
      | _ => ()
      }
    }

    <>
      <Touchable
        ref=popoverTriggerRef
        ariaProps=popoverAriaProps.triggerProps
        onPress={_ => popover.onRequestToggle()}>
        <OverlayTriggerView
          ?label
          preset=#filter
          icon=#calendar
          hovered=triggerHovered
          active=popover.opened
          focused=popover.opened>
          {{
            switch triggerLabelDisplay {
            | #showPreset => preset->Preset.toString(~notDefinedPlaceholder=placeholder)
            | #showDateRange =>
              switch value {
              | Some(value) => formatDateRange(value)
              | None => placeholder
              }
            }
          }->React.string}
        </OverlayTriggerView>
      </Touchable>
      <Popover
        triggerRef=popoverTriggerRef
        placement=#"bottom start"
        modal=false
        state={
          opened: popover.opened,
          onRequestToggle: popover.onRequestToggle,
          onRequestClose: popover.onRequestClose,
        }>
        <Popover.Dialog ariaProps=popoverAriaProps.overlayProps>
          <DateRangePicker
            focusedInput
            visibleMonth
            activePreset=preset
            presets
            formatPreset={preset => Preset.toString(preset, ~notDefinedPlaceholder=placeholder)}
            isEqualPreset={Preset.isEqual}
            range=partialDateRange
            ?disabledResetButton
            onChangePreset
            onRequestReset
            onDatesChange
            onFocusChange
            onPrevMonthClick
            onNextMonthClick
          />
        </Popover.Dialog>
      </Popover>
    </>
  }
}

// NOTE — With Rescript, we can work with first-class modules.
// But this is not transformable for TypeScript, via GenType.
// So the TypeScript version is simpler.
module Gen = {
  @react.component
  let make = (
    ~value=?,
    ~label=?,
    ~placeholder=?,
    ~disabledResetButton=?,
    ~onChange,
    ~triggerLabelDisplay,
  ) =>
    <Component
      ?value
      ?label
      ?placeholder
      ?disabledResetButton
      preset=module(StandardPreset)
      presets=standardPresetsArray
      onChange
      triggerLabelDisplay
    />
}

@react.component
let make = (
  ~value=?,
  ~label=?,
  ~placeholder=?,
  ~disabledResetButton=?,
  ~preset=?,
  ~presets=?,
  ~onChange,
  ~triggerLabelDisplay,
) =>
  switch (preset, presets) {
  | (Some(preset), Some(presets)) =>
    <Component
      ?value ?label ?placeholder ?disabledResetButton preset presets onChange triggerLabelDisplay
    />
  | (_, None) | (None, _) =>
    <Component
      ?value
      ?label
      ?placeholder
      ?disabledResetButton
      preset=module(StandardPreset)
      presets=standardPresetsArray
      onChange
      triggerLabelDisplay
    />
  }
