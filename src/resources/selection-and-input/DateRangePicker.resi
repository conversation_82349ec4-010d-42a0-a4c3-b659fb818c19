let getMaxDate: unit => Js.Date.t
let isOutsideRange: Js.Date.t => bool

@genType @react.component
let make: (
  ~focusedInput: DayPickerRange.focusedInput=?,
  ~visibleMonth: Js.Date.t,
  ~activePreset: 'preset,
  ~presets: array<'preset>,
  ~formatPreset: 'preset => string,
  ~isEqualPreset: ('preset, 'preset) => bool,
  ~range: (option<Js.Date.t>, option<Js.Date.t>),
  ~disabledResetButton: bool=?,
  ~onChangePreset: 'preset => unit,
  ~onRequestReset: ReactAria.Button.pressEvent => unit,
  ~onDatesChange: ((option<Js.Date.t>, option<Js.Date.t>)) => unit,
  ~onFocusChange: Js.Nullable.t<DayPickerRange.focusedInput> => unit,
  ~onPrevMonthClick: Js.Date.t => unit,
  ~onNextMonthClick: Js.Date.t => unit,
) => React.element
