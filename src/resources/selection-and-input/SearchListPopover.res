// NOTE - Legacy component, ComboBox is better

open Intl
open StyleX
open WebAPI

let styles = StyleX.create({
  "wrapper": style(~alignSelf=#"flex-start", ~width="100%", ()),
  "append": style(
    ~display=#flex,
    ~flexDirection=#column,
    ~position=#absolute,
    ~width="40px",
    ~height="40px",
    ~top="0px",
    ~right="0px",
    ~bottom="0px",
    ~alignItems=#center,
    ~justifyContent=#center,
    ~backgroundColor=Colors.neutralAlpha100,
    (),
  ),
  "itemsList": style(
    ~overflow=#auto,
    ~maxHeight="210px",
    ~borderTop="1px solid " ++ Colors.neutralColor20,
    ~borderBottomLeftRadius="5px",
    ~borderBottomRightRadius="5px",
    (),
  ),
})

let wrapperStyleFromParams = (~expendableWidth) =>
  expendableWidth ? style(~width="100%", ()) : style()

@react.component
let make = (
  ~children,
  ~expendableWidth: bool,
  ~items: option<array<'item>>,
  ~itemToValue: 'item => string,
  ~itemToOption: 'item => string,
  ~opened=?,
  ~searchQueryDelay=400,
  ~onSearchQueryChange=?,
  ~onChange,
  ~onToggle,
  ~onEndReached,
  ~renderEndItem=() => React.null,
  ~renderEndActions: option<unit => array<React.element>>=?,
) => {
  let {ref: popoverTriggerRef, state: popover, ariaProps: popoverAriaProps} = Popover.useTrigger()

  let (searchQuery, setSearchQuery) = React.useState(() => "")
  let debouncedSearchQuery = ReactUpdateDebounced.use(searchQuery, ~delay=searchQueryDelay)

  let textInputRef = React.useRef(Js.Nullable.null)
  let itemsListRef = React.useRef(Js.Nullable.null)

  React.useEffect1(() => {
    switch opened {
    | Some(false) => popover.onRequestClose()
    | _ => ()
    }
    None
  }, [opened])

  // Executes onEndReached callback with scrolling meta data when event fires
  React.useEffect1(() => {
    let domElement = itemsListRef->ReactDomElement.fromRef
    let handler = _ =>
      domElement
      ->Option.map(domElement => {
        let offsetY = domElement->DomElement.scrollHeight
        let scrolledY = domElement->DomElement.scrollTop +. domElement->DomElement.offsetHeight
        let offsetFromEndReached = offsetY - scrolledY->Float.toInt

        if offsetFromEndReached < 100 {
          onEndReached(offsetFromEndReached)
        }
      })
      ->ignore

    domElement
    ->Option.map(domElement => domElement->DomElement.addEventListener("scroll", handler))
    ->ignore

    Some(
      () =>
        domElement
        ->Option.map(domElement => domElement->DomElement.removeEventListener("scroll", handler))
        ->ignore,
    )
  }, [itemsListRef.current])

  React.useEffect2(() => {
    onToggle(popover.opened)
    None
  }, (popover.opened, textInputRef.current))

  React.useEffect1(() => {
    switch onSearchQueryChange {
    | Some(onSearchQueryChange) => onSearchQueryChange(debouncedSearchQuery)
    | _ => ()
    }
    None
  }, [debouncedSearchQuery])

  let renderedItems = React.useMemo1(() =>
    switch items {
    | Some(items) =>
      items->Array.mapWithIndex((index, item) =>
        <SearchListItem
          key={item->itemToValue ++ index->Int.toString}
          name={item->itemToOption}
          onPress={_ => {
            onChange(item)
            popover.onRequestToggle()
          }}
        />
      )
    | _ => []
    }
  , [items])

  <>
    <DivX style={StyleX.props([styles["wrapper"], wrapperStyleFromParams(~expendableWidth)])}>
      <Touchable
        ref=popoverTriggerRef
        ariaProps=popoverAriaProps.triggerProps
        onPress={_ => popover.onRequestToggle()}>
        children
      </Touchable>
    </DivX>
    {if popover.opened {
      <Popover
        triggerRef=popoverTriggerRef
        state=popover
        placement=#"bottom start"
        modal=false // FIXME - tmp: doesn't open on iPad if true
        layout={expendableWidth ? #triggerMinWidth : #auto}>
        <Popover.Dialog
          ariaProps=popoverAriaProps.overlayProps style={ReactDOMStyle.make(~minWidth="200px", ())}>
          <InputSearch
            loading={items->Option.isNone}
            onRequestClear={() => setSearchQuery(_ => "")}
            autoFocus=true
            bordered=false
            placeholder={t("Search")}
            value=searchQuery
            onChange={searchQuery => setSearchQuery(_ => searchQuery)}
          />
          {switch items {
          | Some(_) =>
            <DivX ref=itemsListRef style={StyleX.props([styles["itemsList"]])}>
              <Box spaceX=#xnormal spaceTop=#small spaceBottom=#xxsmall>
                <TextStyle size=#xxsmall variation=#normal>
                  {t("Search results")->React.string}
                </TextStyle>
              </Box>
              {renderedItems
              ->Array.concat([<DivX key="end-list-item"> {renderEndItem()} </DivX>])
              ->React.array}
              {switch renderEndActions {
              | Some(renderEndActions) =>
                <>
                  <Box spaceLeft=#xnormal spaceTop=#xsmall spaceBottom=#xxsmall>
                    <TextStyle size=#xxsmall variation=#normal>
                      {t("Other suggestions")->React.string}
                    </TextStyle>
                  </Box>
                  {renderEndActions()
                  ->Array.mapWithIndex((index, element) =>
                    <DivX key={index->Int.toString}> element </DivX>
                  )
                  ->React.array}
                  <Box spaceBottom=#xsmall />
                </>
              | _ => React.null
              }}
            </DivX>
          | _ =>
            <DivX style={StyleX.props([styles["itemsList"]])}>
              <SearchListItem key="end-list-item"> {renderEndItem()} </SearchListItem>
            </DivX>
          }}
        </Popover.Dialog>
      </Popover>
    } else {
      React.null
    }}
  </>
}
