@react.component
let make: (
  ~loading: bool,
  ~onRequestClear: unit => unit,
  ~disabled: bool=?,
  ~bordered: bool=?,
  ~focused: bool=?,
  ~placeholder: string=?,
  ~autoFocus: bool=?,
  ~containerRef: ReactDOM.Ref.currentDomRef=?,
  ~inputRef: ReactDOM.Ref.currentDomRef=?,
  ~ariaProps: JsxDOM.domProps=?,
  ~value: string=?,
  ~onChange: string => unit=?,
  ~onFocus: unit => unit=?,
  ~onBlur: unit => unit=?,
) => React.element
