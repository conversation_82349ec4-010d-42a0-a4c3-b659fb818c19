@react.component
let make = (
  ~label,
  ~errorMessage=?,
  ~placeholder=?,
  ~required=?,
  ~disabled=?,
  ~tooltip=?,
  ~onChange,
  ~searchable=?,
  ~renderItemContent=?,
  ~renderTriggerView=?,
  ~value,
  ~sections,
) => {
  let renderTriggerView = renderTriggerView->Option.getWithDefault((
    ~children,
    ~item as _,
    ~hovered,
    ~active,
    ~focused,
  ) =>
    <OverlayTriggerView
      preset=#inputField({
        required: required->Option.getWithDefault(false),
        ?errorMessage,
        ?tooltip,
      })
      label
      ?disabled
      hovered
      active
      focused>
      children
    </OverlayTriggerView>
  )

  <Select
    preset=#filter
    label
    ?placeholder
    ?disabled
    ?searchable
    renderTriggerView
    ?renderItemContent
    sections
    value
    onChange
  />
}
