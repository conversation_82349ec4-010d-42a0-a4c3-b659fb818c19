open! Svg

type state =
  | Checked
  | Indeterminate
  | Unchecked

module CheckboxSvg = {
  @react.component
  let make = (~state, ~size, ~hovered, ~disabled) =>
    <Svg width={size->Float.toString} height={size->Float.toString} viewBox="0 0 16 16">
      <Path
        d={switch state {
        | Checked => "M13 0a3 3 0 013 3v10a3 3 0 01-3 3H3a3 3 0 01-3-3V3a3 3 0 013-3zm0 2H3a1 1 0 00-1 1v10a1 1 0 001 1h10a1 1 0 001-1V3a1 1 0 00-1-1zm-.448 3.349a1 1 0 01.083 1.32l-.083.094-4.687 4.687a1 1 0 01-1.325.08l-.094-.084-2.313-2.344a1 1 0 011.33-1.489l.094.084 1.605 1.627 3.976-3.975a1 1 0 011.414 0z"
        | Indeterminate => "M13 0a3 3 0 013 3v10a3 3 0 01-3 3H3a3 3 0 01-3-3V3a3 3 0 013-3zm0 2H3a1 1 0 00-1 1v10a1 1 0 001 1h10a1 1 0 001-1V3a1 1 0 00-1-1zm-2 5a1 1 0 010 2H5a1 1 0 110-2h6z"
        | Unchecked => "M14 0a2 2 0 012 2v12a2 2 0 01-2 2H2a2 2 0 01-2-2V2a2 2 0 012-2h12zm0 1H2a1 1 0 00-.993.883L1 2v12a1 1 0 00.883.993L2 15h12a1 1 0 00.993-.883L15 14V2a1 1 0 00-.883-.993L14 1z"
        }}
        fill={switch (state, hovered, disabled) {
        | (Checked | Indeterminate, _, false) => Colors.brandColor50
        | (Unchecked, true, false) => Colors.neutralColor25
        | (Unchecked, _, false) | (_, _, true) => Colors.neutralColor20
        }}
      />
    </Svg>
}

let makeStyle = (~size) =>
  ReactDOM.Style.make(
    ~position="relative",
    ~display="flex",
    ~alignSelf="start",
    ~margin="auto",
    ~width=size->Float.toString ++ "px",
    ~maxWidth=size->Float.toString ++ "px",
    ~backgroundColor=Colors.neutralColor00,
    ~borderRadius="5px",
    (),
  )

// NOTE - a proxy ghost input is behaving as an invisible overlay of the SVG
// to safely catch the click propagation that wouldn't be possible to stop when actually
// clicking on the SVG (synthetic and native events stopImmediatePropagation would fail).
let makeProxyInputStyle = (~size) =>
  ReactDOM.Style.make(
    ~position="absolute",
    ~width=size->Float.toString ++ "px",
    ~maxWidth=size->Float.toString ++ "px",
    ~height=size->Float.toString ++ "px",
    ~opacity="0",
    ~margin="0",
    (),
  )

@react.component
let make = (
  ~size=15.,
  ~indeterminate=false,
  ~disabled=false,
  ~accessibilityLabel="checkbox",
  ~ariaProps=?,
  ~value,
  ~onChange=?,
) => {
  let ref = React.useRef(Js.Nullable.null)
  let (_, hovered) = Hover.use(~ref, ())
  let domRef = ref->ReactDOM.Ref.domRef

  let defaultProps = {
    ReactAria.Checkbox.selected: value,
    \"aria-label": accessibilityLabel,
    indeterminate,
    disabled,
    ?onChange,
  }
  let props =
    ariaProps
    ->Option.map(props => {
      ...props,
      ReactAria.Checkbox.selected: defaultProps.selected,
      \"aria-label": defaultProps.\"aria-label",
    })
    ->Option.getWithDefault(defaultProps)

  let state = ReactStately.Toggle.useState(~props=Obj.magic(props))
  let {inputProps} = ReactAria.Checkbox.use(~props, ~state, ~ref=domRef)
  let {disabled, indeterminate} = props

  <label style={makeStyle(~size)}>
    <ReactAria.Spread props=inputProps>
      <input ref=domRef style={makeProxyInputStyle(~size)} />
    </ReactAria.Spread>
    {switch (value, indeterminate || disabled) {
    | (true, false) => <CheckboxSvg state=Checked size hovered disabled />
    | (_, true) => <CheckboxSvg state=Indeterminate size hovered disabled />
    | (false, _) => <CheckboxSvg state=Unchecked size hovered disabled />
    }}
  </label>
}

let make = React.memo(make)
