let defaultSectionTitle: string
let defaultItemsPerScroll: int

type size = [#normal | #compact]

@react.component
let make: (
  ~domRef: ReactDOM.domRef,
  ~state: ReactAria.ListBox.state,
  ~props: ReactAria.ListBox.props,
  ~size: size=?,
  ~itemsLimit: int=?,
  ~noResultLabel: string=?,
  ~loading: bool=?,
  ~footerElement: React.element=?,
  ~onScroll: ReactEvent.UI.t => unit=?,
) => React.element
