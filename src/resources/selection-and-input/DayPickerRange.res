// NOTE - react-dates is not maintened anymore (the last npm publication
// was 2 years ago) and does not suppoer react@18.
// > https://github.com/react-dates/react-dates/issues/2105
// Consider using @react-aria/datepicker and remove moment dep.

let {defaultFocusedInput} = module(ReactDates.DayPickerRangeController)

type focusedInput = ReactDates.DayPickerRangeController.focusedInput

@react.component
let make = (
  ~startDate,
  ~endDate,
  ~maxDate=?,
  ~initialVisibleMonth=?,
  ~isOutsideRange=?,
  ~focusedInput,
  ~onChange,
  ~onFocusChange,
  ~onPrevMonthClick=?,
  ~onNextMonthClick=?,
) =>
  <ReactDates.DayPickerRangeController
    initialVisibleMonth=?{initialVisibleMonth->Option.map((initialVisibleMonth, ()) => {
      Moment.make(initialVisibleMonth())
    })}
    startDate={startDate->Option.map(Moment.make)->Js.Nullable.fromOption}
    endDate={endDate->Option.map(Moment.make)->Js.Nullable.fromOption}
    noBorder=true
    focusedInput
    transitionDuration=0.
    onDatesChange={({startDate, endDate}) =>
      onChange((
        startDate->Js.Nullable.toOption->Option.map(Moment.toDate),
        endDate->Js.Nullable.toOption->Option.map(Moment.toDate),
      ))}
    onFocusChange
    onPrevMonthClick=?{onPrevMonthClick->Option.map((onPrevMonthClick, moment) =>
      onPrevMonthClick(moment->Moment.toDate)
    )}
    onNextMonthClick=?{onNextMonthClick->Option.map((onNextMonthClick, moment) =>
      onNextMonthClick(moment->Moment.toDate)
    )}
    maxDate=?{maxDate->Option.map(maxDate => Moment.make(maxDate))}
    isOutsideRange=?{isOutsideRange->Option.map((isOutsideRange, moment) =>
      isOutsideRange(moment->Moment.toDate)
    )}
    minimumNights=0
  />
