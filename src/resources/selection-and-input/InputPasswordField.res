open Intl
open StyleX

let styles = StyleX.create({
  "listTypingValidation": style(
    ~display=#flex,
    ~flexWrap=#wrap,
    ~paddingTop="8px",
    ~marginBottom="-10px",
    (),
  ),
  "itemTypingValidation": style(~display=#flex, ~flexBasis="50%", ()),
  "iconItemTypingValidation": style(~marginTop="4px", ~marginRight="-2px", ()),
  "textItemTypingValidation": style(
    ~font=`normal 400 13px "Libre Franklin"`,
    ~color=Colors.neutralColor70,
    (),
  ),
  "labelWrapper": style(~display=#flex, ()),
  "textLabel": style(~font=`normal 500 13px "Libre Franklin"`, ~color=Colors.neutralColor50, ()),
})

type rule =
  | MinStr
  | MinLower
  | MinUpper
  | MinDigit

let minStr = MinStr
let minLower = MinLower
let minUpper = MinUpper
let minDigit = MinDigit

// NOTE - these regex are also implied in FormSchema (grouped)
let ruleToReg = x =>
  switch x {
  | MinStr => %re("/.{10,}.*/")
  | MinLower => %re("/(.*[a-z]){1,}.*/")
  | MinUpper => %re("/(.*[A-Z]){1,}.*/")
  | MinDigit => %re("/(.*[0-9]){1,}.*/")
  }

let ruleToErrorMsg = x =>
  switch x {
  | MinStr => "At least 10 characters"
  | MinLower => "One lowercase letter"
  | MinUpper => "One uppercase letter"
  | MinDigit => "One digit"
  }

let applyRule = (value, rule) => Js.Re.test_(rule->ruleToReg, value)

type error = (bool, rule)

type state = {rules: array<error>}

type action = FieldValueChanged(string)

@react.component
let make = (
  ~label,
  ~value,
  ~focused,
  ~required=?,
  ~errorMessage=?,
  ~onChange=?,
  ~onFocus,
  ~onBlur,
  ~placeholder=?,
  ~showTypingValidation=false,
) => {
  let ({rules}, dispatch) = React.useReducer(
    (state, action) =>
      switch action {
      | FieldValueChanged(textField) => {
          rules: state.rules->Array.map(ruleState => {
            let (_, rule) = ruleState
            let isValid = textField->applyRule(rule)
            (isValid, rule)
          }),
        }
      },
    {
      rules: [(false, MinStr), (false, MinLower), (false, MinUpper), (false, MinDigit)],
    },
  )
  let (show, setShow) = React.useState(_ => true)

  React.useEffect1(() => {
    dispatch(FieldValueChanged(value))
    None
  }, [value])

  <Stack>
    <InputTextField
      label
      fieldAction={
        Field.Action.text: t(show ? "Show" : "Hide"),
        handler: Callback(_ => setShow(show => !show)),
      }
      ?required
      ?placeholder
      ?errorMessage
      focused
      secureTextEntry=show
      value
      ?onChange
      onFocus
      onBlur
    />
    {showTypingValidation
      ? <DivX style={StyleX.props([styles["listTypingValidation"]])}>
          {rules
          ->Array.mapWithIndex((i, rule) =>
            <DivX key={i->Int.toString} style={StyleX.props([styles["itemTypingValidation"]])}>
              <DivX style={StyleX.props([styles["iconItemTypingValidation"]])}>
                <Icon name=#oval fill={fst(rule) ? Colors.successColor50 : Colors.neutralColor15} />
              </DivX>
              <SpanX style={StyleX.props([styles["textItemTypingValidation"]])}>
                {t(snd(rule)->ruleToErrorMsg)->React.string}
              </SpanX>
            </DivX>
          )
          ->React.array}
        </DivX>
      : React.null}
  </Stack>
}

let make = React.memoCustomCompareProps(make, (oldProps, newProps) =>
  oldProps.label === newProps.label &&
  oldProps.errorMessage === newProps.errorMessage &&
  oldProps.value === newProps.value &&
  oldProps.focused === newProps.focused &&
  oldProps.placeholder === newProps.placeholder &&
  oldProps.onBlur === newProps.onBlur
)
