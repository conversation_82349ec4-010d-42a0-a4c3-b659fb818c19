// NOTE - latest versions of ReactAria/Stately doesn't support anymore overriding the selection manager
// behavior, in consequence the keyboard navigation is disabled when the search box is present.
// To resolve this issue, a mixed usage of useSelect and useAutoComplete must be considered.
// docs (experimental): https://react-spectrum.adobe.com/react-aria/useAutocomplete.html
// example: https://react-spectrum.adobe.com/react-aria/examples/searchable-select.html

// TODO - eventually should replace the following components:
// SearchListPopover ; SearchListItem

open Intl
open StyleX

type overlayFooterLink = {
  text: string,
  to: Navigation.to,
}

type item<'value> = {
  key: string,
  value: 'value,
  label: string,
  description?: string,
  disabled?: bool,
  sticky?: bool,
}

type section<'itemValue> = {
  items: array<item<'itemValue>>,
  title?: string,
}

type itemSet<'itemValue> = {
  sectionsItems: array<item<'itemValue>>,
  searchableSectionsItems: array<item<'itemValue>>,
  filteredSectionsItems: array<item<'itemValue>>,
}

module TriggerButton = {
  let styles = StyleX.create({
    "TriggerButton_grow": style(~flex="1", ()),
    "TriggerButton_disabled": style(~cursor=#default, ()),
  })

  let styleProps = (~grow, ~disabled) =>
    StyleX.props([
      grow ? styles["TriggerButton_grow"] : style(),
      disabled ? styles["TriggerButton_disabled"] : style(),
    ])

  @react.component
  let make = (
    ~triggerRef,
    ~variation: OverlayTriggerView.preset,
    ~size,
    ~grow,
    ~label=?,
    ~placeholder,
    ~state: ReactStately.Select.state,
    ~ariaProps: ReactAria.Button.props,
    ~item: option<item<'value>>,
    ~valueProps,
    ~hovered,
    ~focused,
    ~highlighted,
    ~excludeFromTabOrder,
    ~renderTriggerView=?,
  ) => {
    let disabled = ariaProps.disabled->Option.getWithDefault(false)
    let props = {
      ...ariaProps,
      disabled,
      excludeFromTabOrder,
      \"aria-labelledby": ?ariaProps.\"aria-labelledby"->Option.map(x =>
        x->Js.String2.replace(ariaProps.id->Option.getWithDefault(""), "")
      ),
    }
    let {buttonProps} = ReactAria.Button.use(~props, ~ref=triggerRef->ReactDOM.Ref.domRef, ())

    let contentElement =
      <ReactAria.Spread props=valueProps>
        {switch item {
        | Some({label}) => <span> {label->React.string} </span>
        // TODO - placeholder/unselected styling should be handled in OverlayTriggerView
        | _ =>
          <TextStyle variation=#normal size={size === #compact ? #xsmall : #normal}>
            {placeholder->React.string}
          </TextStyle>
        }}
      </ReactAria.Spread>

    let active = state.opened

    let {?style, ?className} = styleProps(~grow, ~disabled)

    <ReactAria.Spread props=buttonProps>
      <button ?style ?className ref={triggerRef->ReactDOM.Ref.domRef}>
        {switch renderTriggerView {
        | Some(render) =>
          // NOTE - `item` is passed instead of the render `state.selectedItem.rendered`
          // to get rid of the `item.description` rendered node.
          // REVIEW - an "unsafe" alternative could be to extract the `rendered.props.children[0]` element
          // this is the recommanded ReactAria way https://react-spectrum.adobe.com/react-aria/useListBox.html#complex-options
          render(~children=contentElement, ~item, ~hovered, ~active, ~focused) // REVIEW - yet to see if `highlighted` should be added
        | None =>
          <OverlayTriggerView
            preset=variation size ?label disabled hovered active focused highlighted>
            contentElement
          </OverlayTriggerView>
        }}
      </button>
    </ReactAria.Spread>
  }
}

module SelectSearchBox = {
  let styles = StyleX.create({
    "root": style(~borderBottom=`1px solid ${Colors.neutralColor15}`, ()),
  })

  @react.component
  let make = React.memo((~value, ~onChange, ~onRequestClear) =>
    // NOTE - makes component focusable to not close the overlay on click
    <div tabIndex=0>
      // NOTE - override the focus handling by <ReactAria.Overlay> of the Popover
      <ReactAria.Focus.Scope autoFocus=true restoreFocus=false>
        <DivX style={StyleX.props([styles["root"]])}>
          <InputSearch
            loading=false onRequestClear bordered=false placeholder={t("Search")} value onChange
          />
        </DivX>
      </ReactAria.Focus.Scope>
    </div>
  )
}

module SelectFooterLink = {
  let style = ReactDOM.Style.make(
    ~padding=`${Spaces.normal->Float.toString}px ${Spaces.xmedium->Float.toString}px`,
    ~border=`1px solid ${Colors.neutralColor15}`,
    (),
  )
  @react.component
  let make = React.memo((~text, ~to, ~onRequestClose) =>
    // NOTE - makes component focusable to not close the overlay on click
    <div tabIndex=0 style>
      <TextLink text icon=#external_link to openNewTab=true onPress={_ => onRequestClose()} />
    </div>
  )
}

let processItems = (~sections: array<section<'itemValue>>, ~filterByLabel) => {
  let sectionsItems = sections->Array.flatMap(section => section.items)
  let searchableSectionsItems =
    sectionsItems->Array.keep(item => !(item.sticky->Option.getWithDefault(false)))
  let filteredSectionsItems =
    sectionsItems->Array.keep(item =>
      !(searchableSectionsItems->Array.some(current => current === item)) ||
      filterByLabel(item.label)
    )

  {sectionsItems, searchableSectionsItems, filteredSectionsItems}
}

let itemDescriptionStyle = ReactDOM.Style.make(~color=Colors.neutralColor50, ())

type size = [#normal | #compact]

@react.component
let make = (
  ~label=?,
  ~placeholder=t("Select"),
  ~disabled=false,
  ~highlighted=false,
  ~loading=false,
  ~defaultOpen=false,
  ~searchable=?,
  ~sections,
  ~preset,
  ~grow=true,
  ~size=#normal,
  ~excludeFromTabOrder=false,
  ~overlayPlacement=#"bottom start",
  ~overlayNoResultLabel=?,
  ~overlayFooterLink=?,
  ~renderTriggerView=?,
  ~renderItemContent=?,
  ~value,
  ~onChange,
  ~onToggle=?,
) => {
  let triggerHighlighted = highlighted

  let {contains} = ReactAria.Filter.use({sensitivity: #base})
  let (itemsLimit, setItemsLimit) = React.useState(_ => ListBox.defaultItemsPerScroll)
  let (filterValue, setFilterValue) = React.useState(() => "")
  let triggerRef = React.useRef(Js.Nullable.null)
  let (_, triggerHovered) = Hover.use(~ref=triggerRef, ())
  let listBoxRef = React.useRef(Js.Nullable.null)->ReactDOM.Ref.domRef

  let filterByLabel = label => contains(label, filterValue)
  let {sectionsItems, searchableSectionsItems, filteredSectionsItems} = processItems(
    ~sections,
    ~filterByLabel,
  )
  let disabledKeys = sectionsItems->Array.keepMap(item =>
    switch item.disabled {
    | Some(true) => Some(item.key)
    | _ => None
    }
  )

  let nodeElements = React.useMemo2(() =>
    sections->Array.map(section => {
      let defaultSectionTitle =
        section.title->Option.mapWithDefault(React.null, title => title->React.string)
      let filteredSectionsItems =
        section.items->Array.keep(
          item => filteredSectionsItems->Array.some(current => current === item),
        )

      <ReactStately.Collection.Section title=defaultSectionTitle>
        {filteredSectionsItems
        ->Array.map(
          item =>
            <ReactStately.Collection.Item key=item.key textValue=item.label>
              {switch renderItemContent {
              | Some(render) => render(item)
              | None =>
                <span>
                  {item.label->React.string}
                  {item.description->Option.mapWithDefault(
                    React.null,
                    desc => <span style=itemDescriptionStyle> {(" " ++ desc)->React.string} </span>,
                  )}
                </span>
              }}
            </ReactStately.Collection.Item>,
        )
        ->React.array}
      </ReactStately.Collection.Section>
    })
  , (sections, searchableSectionsItems))

  let selectedKey = {
    // NOTE - deep comparison to ensure resolution for any datatype
    let matchingItem = sectionsItems->Array.getBy(item => item.value == value)
    let matchingKey = matchingItem->Option.map(item => item.key)
    matchingKey->Js.Nullable.fromOption
  }

  let props = {
    ReactStately.Select.\"aria-label": label->Option.getWithDefault("select"),
    children: nodeElements,
    defaultOpen,
    placeholder,
    disabled,
    disabledKeys,
    selectedKey,
    onSelectionChange: key =>
      key
      ->Js.Nullable.toOption
      ->Option.flatMap(key => sectionsItems->Array.getBy(item => item.key === key))
      ->Option.map(item => item.value)
      ->Option.forEach(onChange),
  }

  let state = ReactStately.Select.useState(~props)
  let {triggerProps, valueProps, menuProps} = ReactAria.Select.use(
    ~props,
    ~state,
    ~ref=triggerRef->ReactDOM.Ref.domRef,
  )

  let (focused, setFocused) = React.useState(() => false)
  let props = {
    ReactAria.Focus.Within.onFocusWithinChange: focused => setFocused(_ => focused),
  }
  let {focusWithinProps} = ReactAria.Focus.Within.use(~props)

  React.useEffect1(() => {
    setFilterValue(_ => "")
    None
  }, [state.selectedKey])

  // NOTE - necessary in nonModalMode to close the popover clicking on the trigger
  React.useEffect1(() => {
    if !state.focused && state.opened && !defaultOpen {
      state.onRequestClose()
    }
    None
  }, [state.focused])

  ReactUpdateEffect.use1(() => {
    onToggle->Option.forEach(fn => fn(state.opened))
    None
  }, [state.opened])

  React.useEffect1(() => {
    let firstKey =
      state.collection.getFirstKey(.)
      ->Js.Nullable.toOption
      ->Option.flatMap(key => state.collection.getKeyAfter(. ~key)->Js.Nullable.toOption)
    let noItemSelected = state.selectedKey->Js.Nullable.isNullable
    // NOTE - ensures overlay focusing in some rerendering/usage cases (i.e: no value selected)
    let firstItemSelected = firstKey === state.selectedKey->Js.Nullable.toOption

    if state.opened && (noItemSelected || firstItemSelected) {
      state.selectionManager.onRequestFocusedKey(. ~key=firstKey)
    }
    None
  }, [state.opened])

  let onScroll = React.useCallback0(event => {
    let element =
      event
      ->ReactEvent.UI.currentTarget
      ->ReactDomEventTarget.toUnsafeDomEventTarget
      ->WebAPI.EventTarget.unsafeAsDomElement
    let scrollHeight = element->WebAPI.DomElement.scrollHeight
    let clientHeight = element->WebAPI.DomElement.clientHeight
    let scrollOffset = scrollHeight - clientHeight * 2
    if element->WebAPI.DomElement.scrollTop > scrollOffset->Int.toFloat {
      setItemsLimit(itemsLimit => itemsLimit + ListBox.defaultItemsPerScroll)
    }
  })

  let searchable = searchable->Option.getWithDefault(sectionsItems->Array.length > 5)

  let listBoxProps = ReactAria.mergeProps2(menuProps, {"shouldUseVirtualFocus": searchable})
  let selectedItem =
    state.selectedKey
    ->Js.Nullable.toOption
    ->Option.flatMap(key => sectionsItems->Array.getBy(item => item.key === key))

  let footerElement = switch overlayFooterLink {
  | Some({text, to}) => <SelectFooterLink text to onRequestClose=state.onRequestClose />
  | None => React.null
  }

  <ReactAria.Spread props=focusWithinProps>
    <div style={ReactDOM.Style.make(~display="contents", ())}>
      <TriggerButton
        variation=preset
        size
        grow
        excludeFromTabOrder
        ?label
        placeholder
        focused
        hovered=triggerHovered
        highlighted=triggerHighlighted
        ?renderTriggerView
        triggerRef
        item=selectedItem
        valueProps
        state
        ariaProps=triggerProps
      />
      {if state.opened {
        <Popover
          size
          placement=overlayPlacement
          layout=#triggerMinWidth
          modal=searchable // NOTE - the searchbox taking the focus requires the modal mode
          triggerRef
          state={
            opened: state.opened,
            onRequestToggle: state.onRequestToggle,
            onRequestClose: state.onRequestClose,
          }>
          {if searchable {
            <SelectSearchBox
              value=filterValue
              onChange={value => setFilterValue(_ => value)}
              onRequestClear={() => setFilterValue(_ => "")}
            />
          } else {
            React.null
          }}
          <ListBox
            size
            loading
            noResultLabel=?overlayNoResultLabel
            itemsLimit
            domRef=listBoxRef
            props=listBoxProps
            state={state->ReactStately.ListBox.fromSelectState}
            footerElement
            onScroll
          />
        </Popover>
      } else {
        React.null
      }}
    </div>
  </ReactAria.Spread>
}
