type triggerLabelDisplay = [#showPreset | #showDateRange]

let formatDateRange: ((Js.Date.t, Js.Date.t)) => string

module type Preset = {
  type t
  let isCustomOrNotDefined: t => bool
  let toString: (~notDefinedPlaceholder: string, t) => string
  let toDateRange: t => option<(Js.Date.t, Js.Date.t)>
  let fromPartialDateRange: ((option<Js.Date.t>, option<Js.Date.t>), array<t>) => t
  let isEqual: (t, t) => bool
}

module StandardPreset: {
  type t =
    | Today
    | Yesterday
    | CurrentWeek
    | LastWeek
    | CurrentMonth
    | LastMonth
    | CurrentYear
    | LastYear
    | Custom((option<Js.Date.t>, option<Js.Date.t>))
    | NotDefined

  let isCustomOrNotDefined: t => bool
  let toString: (~notDefinedPlaceholder: string, t) => string
  let toDateRange: t => option<(Js.Date.t, Js.Date.t)>
  let fromPartialDateRange: ((option<Js.Date.t>, option<Js.Date.t>), array<t>) => t
  let isEqual: (t, t) => bool
}

let standardPresetsArray: array<StandardPreset.t>

let dateRangetoPartialDateRange: option<(Js.Date.t, Js.Date.t)> => (
  option<Js.Date.t>,
  option<Js.Date.t>,
)

let defaultFocusedInput: DayPickerRange.focusedInput

module Gen: {
  @genType @react.component
  let make: (
    ~value: (Js.Date.t, Js.Date.t)=?,
    ~label: string=?,
    ~placeholder: string=?,
    ~disabledResetButton: bool=?,
    ~onChange: option<(Js.Date.t, Js.Date.t)> => unit,
    ~triggerLabelDisplay: triggerLabelDisplay,
  ) => React.element
}

@react.component
let make: (
  ~value: (Js.Date.t, Js.Date.t)=?,
  ~label: string=?,
  ~placeholder: string=?,
  ~disabledResetButton: bool=?,
  ~preset: module(Preset)=?,
  ~presets: array<'preset>=?,
  ~onChange: option<(Js.Date.t, Js.Date.t)> => unit,
  ~triggerLabelDisplay: triggerLabelDisplay,
) => React.element
