@genType
type item<'value> = {
  key: string,
  label: string,
  value: 'value,
}

@genType @react.component
let make: (
  ~label: string,
  ~required: bool=?,
  ~disabled: bool=?,
  ~loading: bool=?,
  ~placeholder: string=?,
  ~errorMessage: string=?,
  ~sectionTitle: string=?,
  ~noResultLabel: string=?,
  ~showResults: bool=?,
  ~items: array<item<'value>>,
  ~value: string,
  ~onInputChange: string => unit,
  ~onSelectionChange: item<'value> => unit,
) => React.element
