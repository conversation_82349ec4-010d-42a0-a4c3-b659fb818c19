// NOTE - react-dates is not maintened anymore (the last npm publication
// was 2 years ago) and does not suppoer react@18.
// > https://github.com/react-dates/react-dates/issues/2105
// Consider using @react-aria/datepicker & remove moment dep.
@react.component
let make = React.memo((~value, ~onChange, ~isDayBlocked=?) => {
  let date = value->Option.map(Moment.make)->Js.Nullable.fromOption

  let onDateChange = React.useCallback1(date => onChange(date->Moment.toDate), [value])
  let isDayBlocked = React.useCallback1(
    day => isDayBlocked->Option.mapWithDefault(false, fn => fn(day->Moment.toDate)),
    [value],
  )

  <ReactDates.DayPickerSingleDateController
    date isDayBlocked focused=true noBorder=true transitionDuration=0. onDateChange
  />
})
