let internationalizationDatetimeFromJsDateObj: Js.Date.t => Internationalization.Date.Time.t
let internationalizationDatetimeToJsDateObj: Internationalization.Date.Time.t => Js.Date.t

@genType @react.component
let make: (
  ~label: string=?,
  ~required: bool=?,
  ~errorMessage: string=?,
  ~value: option<Js.Date.t>,
  ~onChange: Js.Date.t => unit,
  ~onFocus: unit => unit=?,
  ~onBlur: unit => unit=?,
  ~testLocalization: Intl.locale=?,
) => React.element
