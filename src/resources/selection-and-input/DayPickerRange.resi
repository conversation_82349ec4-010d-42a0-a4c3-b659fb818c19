@genType let defaultFocusedInput: ReactDates.DayPickerRangeController.focusedInput

@genType type focusedInput = ReactDates.DayPickerRangeController.focusedInput

@genType @react.component
let make: (
  ~startDate: option<Js.Date.t>,
  ~endDate: option<Js.Date.t>,
  ~maxDate: Js.Date.t=?,
  ~initialVisibleMonth: unit => Js.Date.t=?,
  ~isOutsideRange: Js.Date.t => bool=?,
  ~focusedInput: ReactDates.DayPickerRangeController.focusedInput,
  ~onChange: ((option<Js.Date.t>, option<Js.Date.t>)) => unit,
  ~onFocusChange: Js.Nullable.t<ReactDates.DayPickerRangeController.focusedInput> => unit,
  ~onPrevMonthClick: Js.Date.t => unit=?,
  ~onNextMonthClick: Js.Date.t => unit=?,
) => React.element
