let borderColor = (~focused, ~hovered, ~errored, ~disabled) =>
  if disabled {
    Colors.neutralColor15
  } else if errored && focused {
    Colors.dangerColor55
  } else if errored {
    Colors.dangerColor30
  } else if focused {
    Colors.neutralColor30
  } else if hovered {
    Colors.neutralColor25
  } else {
    Colors.neutralColor20
  }
let borderRadiusPx = "5px"
let backgroundColor = (~disabled) => disabled ? Colors.neutralColor05 : Colors.neutralColor00
let color = (~disabled) => disabled ? Colors.neutralColor35 : Colors.neutralColor90
let fontSize = FontSizes.normal
let fontSizePx = fontSize->Float.toString ++ "px"
