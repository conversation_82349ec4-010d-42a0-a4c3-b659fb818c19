open StyleX

let styles = StyleX.create({
  "view": style(
    ~position=#relative,
    ~display=#flex,
    ~width="40px",
    ~height="26px",
    ~border="1px solid " ++ Colors.neutralColor20,
    ~borderRadius="34px",
    (),
  ),
  "round": style(
    ~position=#absolute,
    ~height="20px",
    ~width="20px",
    ~left="2px",
    ~bottom="2px",
    ~borderRadius="100px",
    ~backgroundColor=Colors.neutralColor15,
    (),
  ),
})

let roundStyleFromParams = (~value) =>
  if value {
    style(
      ~transform="translateX(14px)",
      ~backgroundColor=Colors.brandColor50,
      ~transition="all .25s",
      (),
    )
  } else {
    style(~transform="translateX(0px)", ~transition="all .25s", ())
  }

let viewStyleFromParams = (~disabled) => disabled ? style(~opacity=0.4, ()) : style(~opacity=1., ())

@react.component
let make = (~value, ~onChange=?, ~disabled=false) => {
  <Touchable
    disabled
    onPress={_ =>
      switch onChange {
      | Some(handler) => handler(!value)
      | None => ()
      }}>
    <DivX style={StyleX.props([styles["view"], viewStyleFromParams(~disabled)])}>
      <DivX style={StyleX.props([styles["round"], roundStyleFromParams(~value)])} />
    </DivX>
  </Touchable>
}
