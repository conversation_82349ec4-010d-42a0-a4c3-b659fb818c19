type variant = [
  | #standard
  | #compact
]

type value = [
  | #currency(float, Intl.currency)
  | #percent(float)
  | #decimal(float)
  | #integer(int)
]

@genType let currencyValue: (float, ~currency: Intl.currency) => value
@genType let percentValue: float => value
@genType let decimalValue: float => value
@genType let integerValue: int => value

@genType @react.component
let make: (~value: value, ~variant: variant=?) => React.element
