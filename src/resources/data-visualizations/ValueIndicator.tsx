import { make } from './ValueIndicator.gen'

type ValueIndicatorProps = React.ComponentProps<typeof make>

const ValueIndicator = make as React.FunctionComponent<ValueIndicatorProps>

export { ValueIndicator, type ValueIndicatorProps }
export {
  currencyValue as valueIndicatorCurrencyValue,
  percentValue as valueIndicatorPercentValue,
  decimalValue as valueIndicatorDecimalValue,
  integerValue as valueIndicatorIntegerValue,
} from './ValueIndicator.gen'
