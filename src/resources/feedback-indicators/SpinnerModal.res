open Intl

let wait = ms => Promise.make((resolve, _) => Js.Global.setTimeout(() => resolve(. ()), ms)->ignore)

@react.component
let make = (~title, ~opened, ~loopMessages=[]) => {
  let url = Navigation.useUrl()

  let (message, setMessage) = React.useState(_ => t("Please wait until the operation is complete"))

  React.useEffect1(_ => {
    if opened {
      let index = ref(0)
      let loop = async () => {
        while index.contents <= Array.length(loopMessages) {
          let currentIndex = index.contents
          let randomTime = Js.Math.random() *. 3500.0 +. 1000.0
          await wait(randomTime->Int.fromFloat)
          setMessage(_ =>
            loopMessages[currentIndex]->Option.getWithDefault(
              t("Please wait until the operation is complete"),
            )
          )
          index := currentIndex + 1
        }
      }

      loop()->ignore
    }
    None
  }, [opened])

  // FIXME - callback is executed unexpectedly in some contexts
  let shouldBlockOnRouteChange = React.useCallback1(nextRoute => {
    opened && url.pathname !== nextRoute
  }, [opened])

  <>
    <Navigation.Prompt
      message={t("Warning: an operation is still in progress.")} shouldBlockOnRouteChange
    />
    <Modal opened title hideFooter=true>
      <Box spaceBottom=#large>
        <Placeholder status=Loading customText={message} />
      </Box>
    </Modal>
  </>
}

let make = React.memo(make)
