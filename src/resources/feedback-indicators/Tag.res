open StyleX

let styles = StyleX.create({
  "view": style(
    ~alignSelf=#"flex-start",
    ~paddingBlock="4px",
    ~paddingInline="8px",
    ~borderRadius="3px",
    ~backgroundColor=Colors.neutralColor15,
    (),
  ),
  "text": style(
    ~marginTop="1.5px",
    ~font=`normal 400 13px "Libre Franklin"`,
    ~color=Colors.neutralColor100,
    ~textAlign=#center,
    (),
  ),
})
@react.component
let make = (~children) =>
  <DivX style={StyleX.props([styles["view"]])}>
    <SpanX style={StyleX.props([styles["text"]])}> children </SpanX>
  </DivX>

let make = React.memo(make)
