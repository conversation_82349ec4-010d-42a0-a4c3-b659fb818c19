open StyleX

@inline let font = `normal 400 10px "Archivo"`

let styles = StyleX.create({
  "root": style(
    ~minWidth="8px",
    ~border="1px solid transparent",
    ~borderRadius="9px",
    ~font,
    ~textAlign=#center,
    (),
  ),
  "size_small": style(
    ~padding=`${Spaces.xxsmallPx} ${Spaces.xsmallPx}`,
    ~fontSize="10px",
    ~fontWeight=#bold,
    (),
  ),
  "size_medium": style(~padding=`${Spaces.xsmallPx} 6px`, ~fontSize="12px", ()),
  "size_large": style(~padding=`${Spaces.xsmallPx} 6px`, ~fontSize="14px", ()),
})

let colorFromStyleOptions = (~variation, ~tone, ~accent) =>
  switch (variation, tone) {
  | (#solid, #brand) => Colors.brandColor50
  | (#solid, #neutral) => Colors.neutralColor90
  | (#ghost, #brand) => accent ? Colors.brandColor05 : Colors.brandColor00
  | (#ghost, #neutral) => accent ? Colors.neutralColor15 : Colors.neutralColor10
  | (#outline, #brand) => "white"
  | (#outline, #neutral) => "white"
  }

let styleFromSize = (~size) =>
  switch size {
  | #small => styles["size_small"]
  | #medium => styles["size_medium"]
  | #large => styles["size_large"]
  }

let styleFromVariation = (~variation, ~tone, ~accent) => {
  let color = colorFromStyleOptions(~variation, ~tone, ~accent)
  switch variation {
  | #solid => style(~backgroundColor=color, ~color="white", ())
  | #ghost => style(~backgroundColor=color, ~color, ())
  | #outline => style(~backgroundColor=color, ~color, ~borderColor=color, ())
  }
}

let styleProps = (~size, ~variation, ~tone, ~accent) =>
  StyleX.props([
    styles["root"],
    styleFromSize(~size),
    styleFromVariation(~variation, ~tone, ~accent),
    styles["border"],
  ])

type variation = [#solid | #ghost | #outline]
type size = [#small | #medium | #large]
type tone = [#brand | #neutral]

@react.component
let make = (
  ~children,
  ~size: size=#medium,
  ~variation: variation=#solid,
  ~tone: tone=#brand,
  ~accent=false,
) => {
  let {?style, ?className} = styleProps(~size, ~variation, ~tone, ~accent)

  <span ?style ?className> {children} </span>
}

let make = React.memo(make)
