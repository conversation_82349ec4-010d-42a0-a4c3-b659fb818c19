type tone = [#primary | #neutral | #success]

let spinnerStyleFromTone = (~tone) => {
  let (gradientStartColor, gradientEndColor) = switch tone {
  | #primary => ("rgba(160, 30, 114, 0)", "rgb(208, 28, 145)") // REVIEW - in components
  | #neutral => ("rgba(160, 30, 114, 0)", "rgb(225, 225, 225)") // REVIEW - to be defined
  | #success => ("rgba(5, 180, 105, 0)", "rgb(5, 180, 105)") // REVIEW - in components
  }
  ReactDOM.Style.make(
    ~width="100%",
    ~height="100%",
    ~background=`conic-gradient(
        from 90deg,
        ${gradientStartColor} 0deg,
        ${gradientEndColor} 360deg
      )`,
    ~animation="spin 1.3s infinite", // NOTE - `spin` keyframes defined in public/animations.css
    ~animationTimingFunction="cubic-bezier(0.17, 0.67, 0.83, 0.67)",
    (),
  )
}

@react.component
let make = (~tone=#primary, ~size=24.) => {
  let svgStyle = ReactDOM.Style.make(~transform="scale(0.85)", ()) // NOTE - downscaled for spinner v2 migration
  let spinnerStyle = spinnerStyleFromTone(~tone)

  <svg
    role="status"
    ariaBusy=true
    ariaLabel={Intl.t("Loading...")}
    xmlns="http://www.w3.org/2000/svg"
    width={Float.toString(size)}
    height={Float.toString(size)}
    viewBox="0 0 20 20"
    fill="none"
    style=svgStyle>
    <mask id="path-1-inside-1_4945_34844" fill="white">
      <path
        d="M20 10C20 15.5228 15.5228 20 10 20C4.47715 20 0 15.5228 0 10C0 4.47715 4.47715 0 10 0C15.5228 0 20 4.47715 20 10Z"
      />
    </mask>
    <g clipPath="url(#paint0_angular_4945_34844_clip_path)" mask="url(#path-1-inside-1_4945_34844)">
      <g transform="matrix(0.00755906 0 0 0.00755906 10 10)">
        <foreignObject x="-1719.79" y="-1719.79" width="3439.58" height="3439.58">
          <div xmlns="http://www.w3.org/1999/xhtml" style=spinnerStyle />
        </foreignObject>
      </g>
    </g>
    <defs>
      <clipPath id="paint0_angular_4945_34844_clip_path">
        <path
          d="M20 10H17C17 13.866 13.866 17 10 17V20V23C17.1797 23 23 17.1797 23 10H20ZM10 20V17C6.13401 17 3 13.866 3 10H0H-3C-3 17.1797 2.8203 23 10 23V20ZM0 10H3C3 6.13401 6.13401 3 10 3V0V-3C2.8203 -3 -3 2.8203 -3 10H0ZM10 0V3C13.866 3 17 6.13401 17 10H20H23C23 2.8203 17.1797 -3 10 -3V0Z"
        />
      </clipPath>
    </defs>
  </svg>
}

let make = React.memo(make)
