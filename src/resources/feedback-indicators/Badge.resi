@genType
type size = [#small | #medium | #large]

@genType
type variation = [
  | #normal
  | #success
  | #danger
  | #warning
  | #primary
  | #neutral
  | #information
  | #important
]

@genType @react.component
let make: (
  ~children: React.element,
  ~highlighted: bool=?,
  ~size: size=?,
  ~variation: variation=?,
  ~borderColor: string=?,
  ~backgroundColor: string=?,
  ~foregroundColor: string=?,
) => React.element
