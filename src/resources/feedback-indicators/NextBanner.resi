type variation = [#info]

@genType
module Text: {
  type action =
    | Callback(ReactAria.Button.pressEvent => unit)
    | OpenLink(Navigation.to)

  @genType @react.component
  let make: (~text: string, ~italic: bool=?, ~bold: bool=?, ~action: action=?) => React.element
}

@genType
module Gap: {
  @genType @react.component
  let make: (~space: Spaces.t=?) => React.element
}

@genType @react.component
let make: (
  ~children: React.element,
  ~variation: variation,
  ~title: string=?,
  ~maxLines: int=?,
) => React.element
