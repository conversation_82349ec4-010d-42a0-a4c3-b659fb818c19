module RichText: {
  type link = {
    text: string,
    to: Navigation.to,
  }
  type t = {
    message: string,
    link: option<link>,
  }

  let make: (~markdown: string) => t
}

@genType type textStatus = Success(string) | Danger(string) | Warning(string) | Info(string)

@genType @react.component
let make: (
  ~textStatus: textStatus=?,
  ~details: array<string>=?,
  ~compact: bool=?,
  ~onRequestClose: unit => unit=?,
  ~helpCenterLink: bool=?,
) => React.element
