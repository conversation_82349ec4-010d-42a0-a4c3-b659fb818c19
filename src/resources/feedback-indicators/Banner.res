open Intl
open StyleX

type textStatus = Success(string) | Danger(string) | Warning(string) | Info(string)

let styles = StyleX.create({
  "container": style(~display=#flex, ~flexDirection=#row, ~borderRadius="4px", ()),
  "containerCompact": style(~paddingInline=Spaces.largePx, ~paddingBlock="9px", ()),
  "containerLarge": style(~paddingInline=Spaces.mediumPx, ~paddingBlock="18px", ()),
  "containerSuccess": style(~backgroundColor=Colors.successColor10, ()),
  "containerDanger": style(~backgroundColor=Colors.brandColor10, ()),
  "containerWarning": style(~backgroundColor=Colors.extraColorK70, ()),
  "containerInfo": style(~backgroundColor=Colors.secondaryColor05, ()),
  "content": style(
    ~display=#flex,
    ~flexDirection=#row,
    ~flexGrow="1",
    ~flexBasis="85%",
    ~paddingRight="10px",
    ~font=`normal 400 15px "Libre Franklin"`,
    (),
  ),
  "text": style(
    ~position=#relative,
    ~font=FontFaces.libreFranklinRegular,
    ~color=Colors.neutralColor70,
    ~lineHeight="18px",
    ~alignSelf=#center,
    (),
  ),
  "textLink": style(
    ~position=#relative,
    ~font=FontFaces.libreFranklinSemiBold,
    ~lineHeight="18px",
    ~alignSelf=#center,
    (),
  ),
  "textSuccess": style(~color=Colors.successColor70, ()),
  "textDanger": style(~color=Colors.dangerColor55, ()),
  "textWarning": style(~color=Colors.extraColorK80, ()),
  "textInfo": style(~color=Colors.secondaryColor70, ()),
})

let containerStyleFromParams = (~variation, ~compact) =>
  StyleX.props([
    styles["container"],
    switch variation {
    | #success => styles["containerSuccess"]
    | #danger => styles["containerDanger"]
    | #warning => styles["containerWarning"]
    | #info => styles["containerInfo"]
    },
    compact ? styles["containerCompact"] : styles["containerLarge"],
  ])

let textStyleFromParams = (~variation, ~hovered) =>
  StyleX.arrayStyle([
    switch variation {
    | #success => styles["textSuccess"]
    | #danger => styles["textDanger"]
    | #warning => styles["textWarning"]
    | #info => styles["textInfo"]
    },
    hovered ? style(~textDecorationLine=#underline, ()) : style(),
    style(~fontSize="14px", ()),
  ])

// NOTE - This parser doesn't support complete markdown AST (only url)
// TODO - Add support for multiples url with an array of Text and Link nodes
module RichText = {
  type link = {
    text: string,
    to: Navigation.to,
  }
  type t = {
    message: string,
    link: option<link>,
  }

  let make = (~markdown as message) => {
    let defaultValue = {message, link: None}
    let matches = Js.Re.exec_(%re("/.*(\[(.+)\]\(([^\s]+)\)).*/"), message)

    switch matches {
    | Some(result) =>
      let linkCaptures = result->Js.Re.captures->Array.keepMap(Js.Nullable.toOption)

      switch linkCaptures {
      | [_match, markdown, text, url] =>
        let message = message->Js.String2.replace(markdown, "")
        let to = try {
          Navigation.Url(url->Url.make)
        } catch {
        | _ => Route(url)
        }

        {message, link: Some({text, to})}
      | _ => defaultValue
      }
    | _ => defaultValue
    }
  }
}

module ExpandModal = {
  @react.component
  let make = (~children, ~message, ~variation) => {
    let (ref, hovered) = Hover.use()
    let (opened, setOpened) = React.useState(_ => false)

    let onRequestClose = () => setOpened(_ => false)

    let title = switch variation {
    | #danger => t("Errors details")
    | _ => t("Details")
    }
    let triggerText = switch variation {
    | #danger => t("See errors detail")
    | _ => t("See detail")
    }

    <>
      <Touchable ref onPress={_ => setOpened(_ => true)}>
        <DivX style={StyleX.props([style(~marginTop="4px", ~marginLeft="6px", ())])}>
          <Inline>
            <SpanX style={StyleX.props([textStyleFromParams(~variation, ~hovered=false)])}>
              {"➔"->React.string}
            </SpanX>
            <SpanX
              style={StyleX.props([styles["textLink"], textStyleFromParams(~variation, ~hovered)])}>
              {triggerText->React.string}
            </SpanX>
          </Inline>
        </DivX>
      </Touchable>
      <Modal title opened onRequestClose>
        <div style={ReactDOM.Style.make(~maxHeight="50vh", ~overflow="auto", ())}>
          <Box spaceY=#normal spaceX=#xlarge>
            <Box spaceY=#small>
              <TextStyle>
                {(message->Js.String2.replace(".", "") ++ " :")->React.string}
              </TextStyle>
            </Box>
            <Box grow=true>
              {children
              ->Array.map(detail =>
                <Box spaceY=#xxsmall key=detail>
                  <TextStyle> {(" •  " ++ detail)->React.string} </TextStyle>
                </Box>
              )
              ->React.array}
            </Box>
          </Box>
        </div>
      </Modal>
    </>
  }
}

@react.component
let make = (~textStatus=?, ~details=?, ~compact=true, ~onRequestClose=?, ~helpCenterLink=false) => {
  let (ref, linkHovered) = Hover.use()
  let (variation, text) = switch textStatus {
  | Some(Danger(text)) => (#danger, text)
  | Some(Info(text)) => (#info, text)
  | Some(Success(text)) => (#success, text)
  | Some(Warning(text)) => (#warning, text)
  | _ => (#info, "")
  }
  let {message, link} = RichText.make(~markdown=text)

  let onToggleHelpCenter = HelpCenter.showMessages

  <DivX style={containerStyleFromParams(~variation, ~compact)}>
    <DivX style={StyleX.props([styles["content"]])}>
      <SpanX
        style={StyleX.props([styles["text"], textStyleFromParams(~variation, ~hovered=false)])}>
        {message->React.string}
      </SpanX>
      {switch (link, helpCenterLink) {
      | (Some({text, to}), _) =>
        <DivX ref style={StyleX.props([style(~marginTop="2.5px", ())])}>
          <Inline>
            <SpanX style={StyleX.props([textStyleFromParams(~variation, ~hovered=false)])}>
              {" ➔"->React.string}
            </SpanX>
            <Navigation.Link to openNewTab=true>
              <SpanX
                style={StyleX.props([
                  styles["textLink"],
                  textStyleFromParams(~variation, ~hovered=linkHovered),
                ])}>
                {text->React.string}
              </SpanX>
            </Navigation.Link>
          </Inline>
        </DivX>
      | (_, true) =>
        <DivX ref style={StyleX.props([style(~marginTop="2.5px", ())])}>
          <Inline>
            <SpanX style={StyleX.props([textStyleFromParams(~variation, ~hovered=false)])}>
              {" ➔"->React.string}
            </SpanX>
            <Touchable wrap=false onPress={_ => onToggleHelpCenter()}>
              <SpanX
                style={StyleX.props([
                  styles["textLink"],
                  textStyleFromParams(~variation, ~hovered=linkHovered),
                ])}>
                {t("please visit the help center.")->React.string}
              </SpanX>
            </Touchable>
          </Inline>
        </DivX>
      | _ => React.null
      }}
      {switch details {
      | Some(details) if details->Array.length > 0 =>
        <ExpandModal message=text variation> details </ExpandModal>
      | _ => React.null
      }}
    </DivX>
    {switch onRequestClose {
    | Some(onRequestClose) =>
      <IconButton
        name=#close_light
        marginSize=#xxsmall
        onPress={_ => onRequestClose()}
        color={switch variation {
        | #success => Colors.successColor70
        | #danger => Colors.dangerColor55
        | #warning => Colors.extraColorK80
        | #info => Colors.secondaryColor70
        }}
        hoveredColor={switch variation {
        | #success => Colors.successColor80
        | #danger => Colors.dangerColor60
        | #warning => Colors.extraColorK80
        | #info => Colors.secondaryColor70
        }}
      />
    | _ => React.null
    }}
  </DivX>
}

let make = React.memoCustomCompareProps(make, (oldProps, newProps) =>
  oldProps.textStatus === newProps.textStatus &&
  oldProps.details === newProps.details &&
  oldProps.compact === newProps.compact
)
