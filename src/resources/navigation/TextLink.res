open StyleX

let styles = StyleX.create({
  "main": style(
    ~font=`normal 600 15px "Libre Franklin"`,
    ~color=Colors.neutralColor90,
    ~letterSpacing="0.125px",
    (),
  ),
  "highlighted": style(~color=Colors.brandColor50, ()),
  "hovered": style(~color=Colors.brandColor50, ~textDecorationLine=#underline, ()),
})

let styleFromParams = (~highlighted, ~hovered) =>
  StyleX.props([
    styles["main"],
    switch (highlighted, hovered) {
    | (true, false) => styles["highlighted"]
    | (_, true) => styles["hovered"]
    | (false, false) => style()
    },
  ])

let colorFromParams = (~highlighted, ~hovered) =>
  switch (highlighted, hovered) {
  | (true, false)
  | (_, true) => Colors.brandColor50
  | (false, false) => Colors.neutralColor90
  }

@react.component
let make = (~text, ~to, ~openNewTab=false, ~highlighted=false, ~icon=?, ~onPress=?) => {
  let (ref, hovered) = Hover.use()

  <Navigation.Link to openNewTab ?onPress>
    {switch icon {
    | Some(name) =>
      // NOTE - these wrappers cancel some specific wrapping inline behaviors props to inline-block
      <DivX ref>
        <Inline space=#small align=#spaceBetween>
          <SpanX ref style={styleFromParams(~highlighted, ~hovered)}> {text->React.string} </SpanX>
          <Icon name fill={colorFromParams(~highlighted, ~hovered)} size=18. />
        </Inline>
      </DivX>
    | None =>
      <SpanX ref style={styleFromParams(~highlighted, ~hovered)}> {text->React.string} </SpanX>
    }}
  </Navigation.Link>
}
