module Context: {
  type action = Toggled
  type state = {opened: bool}
  let initialState: state
  type dispatch = action => unit
  type context = (option<state>, dispatch)
  let context: React.Context.t<context>
  let use: unit => (state, dispatch)

  module Provider: {
    let make: React.component<React.Context.props<context>>
  }

  let getReducer: 'a => (state, action => unit)
}

module NavSection: {
  @react.component
  let make: (
    ~children: React.element,
    ~title: string,
    ~active: bool,
    ~icon: Icon.t,
    ~displayHighlightLinkIcon: bool=?,
    ~disabled: bool=?,
  ) => React.element
}

module NavLink: {
  @react.component
  let make: (
    ~to: Navigation.to,
    ~label: string,
    ~active: bool,
    ~icon: Icon.t=?,
    ~betaBadge: bool=?,
    ~displayHighlightLinkIcon: bool=?,
    ~restrictedMode: bool=?,
    ~disabled: bool=?,
    ~variation: [#important | #primary | #secondary]=?,
  ) => React.element
}

let openedSize: float
let closedSize: float

@react.component
let make: (
  ~children: React.element,
  ~userOrganizationName: string,
  ~userName: string,
  ~userProfilePictureUri: string=?,
  ~badge: React.element=?,
  ~userImpersonating: bool,
  ~userSettingsRoute: string,
  ~helpCenterUrl: Url.t,
  ~legacyDashboardUrl: Url.t,
  ~legacyDashboardText: string,
  ~onToggleHelpCenter: unit => unit,
  ~onRequestLogout: unit => unit,
) => React.element
