// TODO - props to trigger a scrollIntoView whenever `errorMessage` become defined
open StyleX
open Intl

module SubText = {
  let styles = StyleX.create({
    "SubText_root": style(
      ~paddingTop=Spaces.xsmallPx,
      ~lineHeight="1.3",
      ~font=`normal 400 13px "Libre Franklin"`,
      (),
    ),
    "SubText_information": style(~color=Colors.neutralColor50, ()),
    "SubText_error": style(~color=Colors.dangerColor55, ()),
  })

  @react.component
  let make = React.memo((~children, ~variation: [#error | #information]) =>
    <SpanX
      style={StyleX.props([
        styles["SubText_root"],
        switch variation {
        | #information => styles["SubText_information"]
        | #error => styles["SubText_error"]
        },
      ])}>
      children
    </SpanX>
  )
}

let styles = StyleX.create({
  "root": style(~display=#flex, ~flexDirection=#column, ~justifyContent=#center, ~flex="1", ()),
})

let makeLabel = (label, ~required) => {
  switch (label, required) {
  | ("", true) => t("Required field") ++ " *"
  | (_, true) => label ++ " *"
  | (_, false) => label
  }
}

module Action = {
  type handler =
    | Callback(unit => unit)
    | OpenLink(Navigation.to)
    | OpenLinkNewTab(Navigation.to)

  type t = {
    text: string,
    handler: handler,
  }
}

@react.component
let make = React.forwardRef((
  ~children,
  ~label=?,
  ~labelAriaProps=?,
  ~tooltip=?,
  ~errorMessage=?,
  ~action=?,
  ~required=false,
  ref,
) => {
  let ref = ref->Js.Nullable.toOption
  let labelRef = React.useRef(Js.Nullable.null)

  <DivX ?ref style={StyleX.props([styles["root"]])}>
    {switch (label, action) {
    | (Some(label), None) =>
      <DivX ref=labelRef style={StyleX.props([style(~width="fit-content", ())])}>
        <Inline space=#xsmall alignY=#top>
          <Label text={label->makeLabel(~required)} ariaProps=?labelAriaProps />
          {switch tooltip {
          | Some(elements) =>
            <TooltipIcon
              variation=#info placement=#"bottom start" crossOffset={-1.} altTriggerRef=labelRef>
              {elements}
            </TooltipIcon>
          | _ => React.null
          }}
        </Inline>
      </DivX>
    | (Some(label), Some({Action.text: text, handler})) =>
      <Inline align=#spaceBetween>
        <Label text={label->makeLabel(~required)} ariaProps=?labelAriaProps />
        {switch tooltip {
        | Some(elements) =>
          <TooltipIcon
            variation=#info placement=#"bottom start" crossOffset={-1.} altTriggerRef=labelRef>
            {elements}
          </TooltipIcon>
        | _ => React.null
        }}
        {switch handler {
        | Callback(action) =>
          <Touchable
            style={StyleX.style(~marginBottom="3px", ())}
            excludeFromTabOrder=true
            onPress={_ => action()}>
            <TextStyle size=#xsmall underlined=true> {text->React.string} </TextStyle>
          </Touchable>
        | OpenLink(to) => <TextLink text to />
        | OpenLinkNewTab(to) => <TextLink text to openNewTab=true />
        }}
      </Inline>
    | (None, _) => React.null
    }}
    children
    {switch errorMessage {
    | Some("") | None => React.null
    | Some(errorMessage) => <SubText variation=#error> {errorMessage->React.string} </SubText>
    }}
  </DivX>
})

// let make = React.memo(make)
