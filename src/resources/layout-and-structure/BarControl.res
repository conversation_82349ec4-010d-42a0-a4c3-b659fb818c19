@react.component
let make = (~filters=?, ~actions=?, ~banner=?) =>
  switch (filters, actions, banner) {
  | (None, None, None) => <Box spaceY=#normal />
  | _ =>
    <Box spaceY=#medium>
      <Inline
        align={switch filters {
        | Some(element) if element !== React.null => #spaceBetween
        | _ => #end
        }}>
        {switch filters {
        | Some(filters) => filters
        | _ => React.null
        }}
        {switch actions {
        | Some(actions) => actions
        | _ => React.null
        }}
      </Inline>
      {switch banner {
      | Some(banner) => banner
      | _ => React.null
      }}
    </Box>
  }
