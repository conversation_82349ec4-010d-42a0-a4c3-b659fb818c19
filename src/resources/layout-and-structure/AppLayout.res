open StyleX

let styles = StyleX.create({
  "container": style(
    ~display=#flex,
    ~flexDirection=#column,
    ~alignItems=#center,
    ~flex="1",
    ~minWidth="0px",
    ~backgroundColor=Colors.neutralColor10,
    ~paddingLeft="0px",
    ~transition="padding-left 0.25s ease-out",
    (),
  ),
  "wrapper": style(~width="100%", ~display=#flex, ~flexDirection=#column, ~flex="1", ~zIndex=1, ()),
})

let containerStyleFromParams = (~navOpened) => {
  let paddingLeft = navOpened ? Nav.openedSize : Nav.closedSize
  StyleX.props([styles["container"], style(~paddingLeft=Float.toString(paddingLeft) ++ "px", ())])
}

@react.component
let make = (~children, ~navBar, ~alertBar) => {
  let (navState, navDispatch) = Nav.Context.getReducer()

  <Nav.Context.Provider value=(Some(navState), navDispatch)>
    <DivX style={containerStyleFromParams(~navOpened=navState.opened)}>
      {navBar}
      <DivX style={StyleX.props([styles["wrapper"]])}>
        {alertBar}
        children
      </DivX>
    </DivX>
  </Nav.Context.Provider>
}

let make = React.memo(make)
