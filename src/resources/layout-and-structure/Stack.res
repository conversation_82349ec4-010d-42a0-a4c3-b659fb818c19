open StyleX

type align = [#center | #end | #start | #spaceBetween | #stretch]

let styles = StyleX.create({
  "root": style(~width="100%", ~display=#flex, ~flexDirection=#column, ()),
  "alignStart": style(~alignItems=#"flex-start", ()),
  "alignEnd": style(~alignItems=#"flex-end", ()),
  "alignCenter": style(~alignItems=#center, ()),
  "alignSpaceBetween": style(~alignItems=#baseline, ()),
  "alignStretch": style(~alignItems=#stretch, ()),
})

let styleProps = (~space, ~align: align) => {
  let gapValue = space->Spaces.toPx
  let minHorizontalGapValue = Spaces.xsmallPx
  StyleX.props([
    styles["root"],
    style(~rowGap=gapValue, ~columnGap=`max(calc(${gapValue} / 2), ${minHorizontalGapValue})`, ()),
    switch align {
    | #start => styles["alignStart"]
    | #end => styles["alignEnd"]
    | #center => styles["alignCenter"]
    | #spaceBetween => styles["alignSpaceBetween"]
    | #stretch => styles["alignStretch"]
    },
  ])
}

@react.component
let make = (~children, ~space=#xxsmall, ~align=#stretch) => {
  let children = children->React.Children.toArray->Array.keep(child => child !== React.null)
  let childrenLength = children->Array.size

  if childrenLength > 0 {
    let {?style, ?className} = styleProps(~space, ~align)
    <div ?style ?className> {children->React.array} </div>
  } else {
    React.null
  }
}
