open StyleX

let styles = StyleX.create({
  "container": style(~display=#flex, ()),
})

let containerStyleProps = (~wrap, ~spaceX, ~spaceY, ~alignY) => {
  let columnGapStr = spaceX->Spaces.toFloat->Float.toString
  let rowGapStr = spaceY->Spaces.toFloat->Float.toString
  StyleX.props([
    styles["container"],
    style(~columnGap=`${columnGapStr}px`, ~rowGap=`${rowGapStr}px`, ()),
    style(~flexWrap=wrap ? #wrap : #nowrap, ()),
    alignY->Option.mapWithDefault(style(), alignItems => style(~alignItems, ())),
  ])
}

let itemStyleProps = (~flexBasisPct, ~spaceX, ~gridLength, ~wrap, ~grow) => {
  let gapPx = `${spaceX->Spaces.toFloat->Float.toString}px`
  let gridLength = gridLength->Int.toString
  StyleX.props([
    style(
      ~display=#flex,
      ~alignItems=grow ? #stretch : #"flex-start",
      ~flexGrow=grow ? "1" : "0",
      ~flexShrink="1",
      ~flexBasis=`calc(${flexBasisPct} - ${gapPx} / ${gridLength} * (${gridLength} - 1))`,
      ~minWidth=wrap ? "auto" : "0px",
      (),
    ),
  ])
}

@react.component
let make = (
  ~children,
  ~grid=?,
  ~wrap=true,
  ~grow=true,
  ~spaceX=#large,
  ~spaceY=#large,
  ~alignY=?,
) => {
  let childrenLength = children->React.Children.toArray->Array.length
  let defaultFlexBasisPct = Js.Math.max_int(
    100 / childrenLength - childrenLength,
    childrenLength - 2,
  )

  let containerStyleProps = containerStyleProps(~wrap, ~spaceX, ~spaceY, ~alignY)
  let {style: ?containerStyle, className: ?containerClassName} = containerStyleProps

  <div role="presentation" style=?containerStyle className=?containerClassName>
    {children
    ->React.Children.toArray
    ->Array.mapWithIndex((idx, child) => {
      let flexBasisPct =
        grid
        ->Option.flatMap(grid => grid->Array.get(mod(idx, grid->Array.size)))
        ->Option.getWithDefault(`${defaultFlexBasisPct->Int.toString}%`)
      let {style: ?itemStyle, className: ?itemClassName} = itemStyleProps(
        ~flexBasisPct,
        ~spaceX,
        ~gridLength=grid->Option.mapWithDefault(childrenLength, value => value->Array.size),
        ~wrap,
        ~grow,
      )
      let rootKey = grid->Option.getWithDefault([])->Array.joinWith("", a => a)

      <div key={`${rootKey}${Int.toString(idx)}`} style=?itemStyle className=?itemClassName>
        {child}
      </div>
    })
    ->React.array}
  </div>
}

let make = React.memo(make)
