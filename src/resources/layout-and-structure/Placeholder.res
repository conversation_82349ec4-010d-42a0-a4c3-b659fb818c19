// TODO - handle debouncing to avoid UI flickering (example: request that responded under 2000ms)
open Intl
open StyleX

let styles = StyleX.create({
  "main": style(~flex="1", ~display=#flex, ~alignSelf=#center, ~alignItems=#center, ()),
  "mainDefault": style(
    ~display=#flex,
    ~flexDirection=#column,
    ~justifyContent=#center,
    ~paddingTop="5%",
    ~paddingBottom="5%",
    ~marginTop="-1.5%",
    (),
  ),
})

let viewStyleFromParams = (~compact) => !compact ? styles["mainDefault"] : style()

let renderSpinner = (~compact) => <Spinner size={compact ? 26. : 32.} />

type status =
  | Loading
  | NoDataAvailable
  | Error
  | Pending({title: string, message: string, illustration: option<Illustration.t>})

type resourceName = {
  singular: string,
  plural: string,
}

let statusToText = status =>
  switch status {
  | Loading => {"title": t("Loading..."), "text": t("Please wait.")}
  | Error => {
      "title": t("Loading issue."),
      "text": t("Please try refreshing the page."),
    }
  | NoDataAvailable => {
      "title": t("No data available."),
      "text": t("Try again with another keyword or filter."),
    }
  | Pending({title, message}) => {"title": title, "text": message}
  }

@react.component
let make = (~status: status, ~customText=?, ~compact=false, ~childComponent=?) =>
  <DivX style={StyleX.props([styles["main"], viewStyleFromParams(~compact)])}>
    <Box spaceY=#small>
      {switch status {
      | Loading => renderSpinner(~compact)
      | Error => <Illustration element=Illustration.error />
      | NoDataAvailable => <Illustration element=Illustration.create />
      | Pending({illustration: Some(element)}) => <Illustration element />
      | Pending(_) => <Illustration element=Illustration.notAsked />
      }}
    </Box>
    {if !compact {
      <Stack>
        <Box spaceY=#xsmall>
          <Title level=#3 weight=#medium align=#center>
            {t((status->statusToText)["title"])->React.string}
          </Title>
        </Box>
        <Box spaceX=#xhuge>
          <TextStyle variation=#normal align=#center>
            {switch customText {
            | Some(text) => text
            | _ => t((status->statusToText)["text"])
            }->React.string}
          </TextStyle>
        </Box>
        <Box spaceX=#xhuge spaceY=#large>
          {switch (status, childComponent) {
          | (Pending(_), Some(childComponent)) =>
            <Box spaceBottom=#xxlarge> {childComponent()} </Box>
          | _ => React.null
          }}
        </Box>
      </Stack>
    } else {
      React.null
    }}
  </DivX>

let make = React.memo(make)
