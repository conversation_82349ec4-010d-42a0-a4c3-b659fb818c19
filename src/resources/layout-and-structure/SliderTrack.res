open StyleX

type layoutProp = Adaptative | ItemsInView(int)

module PaginationIndicator = {
  open WebAPI

  let styles = StyleX.create({
    "PaginationIndicator_root": style(
      ~position=#absolute,
      ~top="0",
      ~right="0",
      ~zIndex=1,
      ~margin="-2px 0 12px",
      ~textAlign=#right,
      ~lineHeight="0px",
      (),
    ),
    "PaginationIndicator_indicator": style(
      ~display=#"inline-block",
      ~width="24px",
      ~height="2px",
      ~marginLeft="2px !important", // NOTE - higher priority than reset.css
      (),
    ),
  })

  let indicatorStyleProps = (~active) =>
    StyleX.props([
      styles["PaginationIndicator_indicator"],
      style(~backgroundColor=!active ? Colors.neutralColor20 : Colors.neutralColor90, ()),
    ])

  @react.component
  let make = (~itemsInView, ~gap, ~trackRef) => {
    let (totalPages, setTotalPages) = React.useState(() => 0)
    let (currentPage, setCurrentPage) = React.useState(() => 0)

    let handleTotalPages = React.useCallback2(() =>
      switch trackRef.React.current->Js.Nullable.toOption {
      | Some(domElement) =>
        let scrollWidth = domElement->DomElement.scrollWidth->Int.toFloat
        let clientWidth = domElement->DomElement.clientWidth->Int.toFloat
        let totalGapShift = gap->Spaces.toFloat *. itemsInView->Int.toFloat
        let nextTotalPages = Js.Math.ceil_int((scrollWidth -. totalGapShift) /. clientWidth)
        if totalPages !== nextTotalPages {
          // NOTE - setTimeout: sets state at the end of the call stack to improve performance
          Js.Global.setTimeout(() => setTotalPages(_ => nextTotalPages), 0)->ignore
        }
      | None => ()
      }
    , (trackRef, totalPages))

    ReactAria.Resize.useObserver({
      ref: trackRef->ReactDOM.Ref.domRef,
      onResize: handleTotalPages,
    })

    let handleCurrentPage = React.useCallback2(() =>
      switch trackRef.React.current->Js.Nullable.toOption {
      | Some(domElement) =>
        let scrollLeft = domElement->DomElement.scrollLeft
        let clientWidth = domElement->DomElement.clientWidth->Int.toFloat
        let gapShift = gap->Spaces.toFloat *. (currentPage - 1)->Int.toFloat
        let nextCurrentPage = Js.Math.ceil_int((scrollLeft -. gapShift) /. clientWidth) + 1
        if currentPage !== nextCurrentPage {
          // NOTE - setTimeout: sets state at the end of the call stack to improve performance
          Js.Global.setTimeout(() => setCurrentPage(_ => nextCurrentPage), 0)->ignore
        }
      | None => ()
      }
    , (trackRef, currentPage))

    React.useLayoutEffect3(() =>
      switch trackRef.React.current->Js.Nullable.toOption {
      | Some(domElement) =>
        handleTotalPages()
        handleCurrentPage()
        domElement->DomElement.addEventListener("scroll", handleCurrentPage)
        Some(() => domElement->DomElement.removeEventListener("scroll", handleCurrentPage))
      | None => None
      }
    , (trackRef.current, handleTotalPages, handleCurrentPage))

    // NOTE - (Safari) debounced to avoid glitch from scroll snapping
    let debouncedCurrentPage = ReactUpdateDebounced.use(currentPage, ~delay=20)

    let {?style, ?className} = StyleX.props([styles["PaginationIndicator_root"]])

    if totalPages <= 10 {
      <ul ?style ?className>
        {Array.makeBy(totalPages, index => {
          let active = index === debouncedCurrentPage - 1
          let {?style, ?className} = indicatorStyleProps(~active)
          <li key={index->Int.toString} ?style ?className />
        })->React.array}
      </ul>
    } else {
      React.null
    }
  }
}

module SideOverlay = {
  open WebAPI

  let styles = StyleX.create({
    "SideOverlay.shade": style(
      ~position=#absolute,
      ~zIndex=1,
      ~bottom="0",
      ~width="35px",
      ~height="100%",
      ~transition="opacity .25s ease",
      ~pointerEvents=#none,
      (),
    ),
    "SideOverlay.buttonContainer": style(
      ~position=#absolute,
      ~zIndex=10,
      ~top="50%",
      ~transition="opacity .15s ease",
      ~borderRadius="100%",
      (),
    ),
  })

  let shadeStyleProps = (~end, ~show) =>
    StyleX.props([
      styles["SideOverlay.shade"],
      style(
        ~left=end ? "initial" : "0",
        ~right=end ? "0" : "initial",
        ~opacity=show ? 1. : 0.,
        ~background=`linear-gradient(to ${end ? "left" : "right"},
                     ${Colors.neutralColor10} 0%, rgba(255, 255, 255, 0) 100%)`,
        ~boxShadow=`${end ? "" : "—"}20px 0px 20px 0px ${Colors.neutralColor10}`,
        (),
      ),
    ])

  let buttonContainerStyleProps = (~end, ~show, ~pressed) =>
    StyleX.props([
      styles["SideOverlay.buttonContainer"],
      style(
        ~left=!end ? "0" : "initial",
        ~right=!end ? "initial" : "0",
        ~opacity=show ? 1. : 0.,
        ~pointerEvents=show ? #auto : #none,
        ~transform=!end ? `translate(-35%, -50%)` : `translate(35%, -50%)`,
        ~boxShadow=`rgba(0, 0, 0, ${pressed ? "0" : "0.07"})
                    0px 3px 4px,` ++ "rgba(0, 0, 0, 0.04) 0px 0px 4px",
        (),
      ),
    ])

  @react.component
  let make = (~end=false, ~showScrollButton, ~gap, ~containerRef, ~trackRef) => {
    let endOverlay = end
    let (_, containerHovered) = Hover.use(~ref=containerRef, ())
    let (pressed, setPressed) = React.useState(() => false)
    let (scrollable, setScrollable) = React.useState(() => false)

    let handleControl = React.useCallback1(() =>
      switch trackRef.React.current->Js.Nullable.toOption {
      | Some(domElement) =>
        let gap = gap->Spaces.toFloat->Float.toInt
        let clientWidth = domElement->DomElement.clientWidth
        let scrollAmount = endOverlay ? clientWidth + gap : -clientWidth - gap
        domElement->DomElement.scrollBy(~options={left: scrollAmount, behavior: #smooth})
      | None => ()
      }
    , [trackRef])

    let handleScrollable = React.useCallback2(() =>
      switch trackRef.React.current->Js.Nullable.toOption {
      | Some(domElement) =>
        let next = if !endOverlay {
          domElement->DomElement.scrollLeft > 1.
        } else {
          domElement->DomElement.scrollLeft->Float.toInt + domElement->DomElement.clientWidth <
            domElement->DomElement.scrollWidth - 1
        }
        if scrollable !== next {
          setScrollable(_ => next)
        }
      | None => ()
      }
    , (trackRef, scrollable))

    React.useLayoutEffect2(() =>
      switch trackRef.React.current->Js.Nullable.toOption {
      | Some(domElement) =>
        handleScrollable()
        domElement->DomElement.addEventListener("scroll", handleScrollable)
        Some(() => domElement->DomElement.removeEventListener("scroll", handleScrollable))
      | None => None
      }
    , (trackRef.current, handleScrollable))

    let ariaProps = {
      ReactAria.Button.elementType: #div,
      onPressStart: _ => setPressed(_ => true),
      onPressEnd: _ => setPressed(_ => false),
    }
    let icon = !endOverlay ? #queue_arrow_left_light : #queue_arrow_right_light

    let {style: ?shadeStyle, className: ?shadeClassName} = shadeStyleProps(
      ~end=endOverlay,
      ~show=scrollable,
    )
    let {
      style: ?buttonContainerStyle,
      className: ?buttonContainerClassName,
    } = buttonContainerStyleProps(~end=endOverlay, ~show=containerHovered && scrollable, ~pressed)

    <>
      <div style=?shadeStyle className=?shadeClassName />
      {if showScrollButton {
        <div style=?buttonContainerStyle className=?buttonContainerClassName>
          <RoundButton icon focused=pressed ariaProps onPress={_ => handleControl()} />
        </div>
      } else {
        React.null
      }}
    </>
  }
}

let styles = StyleX.create({
  "root": style(~position=#relative, ~minWidth="0", ()),
  "track": style(
    ~display=#flex,
    ~overflow=#auto,
    ~scrollSnapType=#"inline mandatory",
    ~scrollbarWidth="0",
    (),
  ),
  "item": style(~flexShrink="0", ~scrollSnapAlign=#end, ()),
})

let trackStyleProps = (~gap) =>
  StyleX.props([styles["track"], style(~columnGap=gap->Spaces.toFloat->toPx, ())])

let itemStyleFromParams = (~cols, ~gap) =>
  StyleX.props([
    styles["item"],
    {
      let cols = cols->Int.toString
      let gapPx = gap->Spaces.toFloat->toPx
      style(~flexBasis=`calc(100% / ${cols} - ${gapPx} / ${cols} * (${cols} - 1))`, ())
    },
  ])

@react.component
let make = (
  ~children,
  ~compact=false,
  ~gap=#normal,
  ~layout=Adaptative,
  ~showIndicator=false,
  ~trackRef=?,
) => {
  let ref = React.useRef(Js.Nullable.null)
  let trackRef = trackRef->Option.getWithDefault(React.createRef())

  let {?style, ?className} = StyleX.props([styles["root"]])
  let {style: ?trackStyle, className: ?trackClassName} = trackStyleProps(~gap)

  <div ?style ?className ref={ref->ReactDOM.Ref.domRef}>
    <SideOverlay gap showScrollButton={!compact} containerRef=ref trackRef />
    <div
      ariaLabel="track"
      style=?trackStyle
      className=?trackClassName
      ref={trackRef->ReactDOM.Ref.domRef}>
      {children
      ->React.Children.toArray
      ->Array.mapWithIndex((idx, child) => {
        let {?style, ?className} = switch layout {
        | Adaptative => StyleX.props([styles["item"]])
        | ItemsInView(cols) => itemStyleFromParams(~cols, ~gap)
        }
        <div ?style ?className key={idx->Int.toString}> child </div>
      })
      ->React.array}
    </div>
    <SideOverlay end=true gap showScrollButton={!compact} containerRef=ref trackRef />
    {switch layout {
    | ItemsInView(itemsInView) if showIndicator => <PaginationIndicator itemsInView gap trackRef />
    | Adaptative | _ => React.null
    }}
  </div>
}

// let make = React.memo(make)
