open StyleX

let styles = StyleX.create({
  "root": style(~position=#relative, ()),
  "offset": style(~position=#absolute, ()),
})

let styleProps = (~width, ~height) =>
  StyleX.props([
    styles["root"],
    style(~width=width->Float.toString ++ "px", ~height=height->Float.toString ++ "px", ()),
  ])
let offsetStyleProps = (~top, ~right, ~bottom, ~left) =>
  StyleX.props([
    styles["offset"],
    style(
      ~top=?top->Option.map(top => top->Float.toString ++ "px"),
      ~right=?right->Option.map(right => right->Float.toString ++ "px"),
      ~bottom=?bottom->Option.map(bottom => bottom->Float.toString ++ "px"),
      ~left=?left->Option.map(left => left->Float.toString ++ "px"),
      (),
    ),
  ])

@react.component
let make = (~children, ~top=?, ~right=?, ~bottom=?, ~left=?, ~width=0., ~height=0.) => {
  let {?style, ?className} = styleProps(~width, ~height)
  let {style: ?offsetStyle, className: ?offsetClassName} = offsetStyleProps(
    ~top,
    ~right,
    ~bottom,
    ~left,
  )

  <div ?style ?className>
    <div style=?offsetStyle className=?offsetClassName> {children} </div>
  </div>
}

let make = React.memo(make)
