// REVIEW - maybe to be replaced by react-aria Group component?

open StyleX

type align = [#center | #start | #end | #spaceAround | #spaceBetween]
type alignY = [#center | #top | #bottom | #stretch | #baseline]

let styles = StyleX.create({
  "root": style(~display=#flex, ~flexDirection=#row, ()),
  "grow": style(~flex="1", ()),
  "wrap": style(~flexWrap=#wrap, ()),
  "alignStart": style(~justifyContent=#"flex-start", ()),
  "alignEnd": style(~justifyContent=#"flex-end", ()),
  "alignCenter": style(~justifyContent=#center, ()),
  "alignSpaceBetween": style(~justifyContent=#"space-between", ()),
  "alignSpaceAround": style(~justifyContent=#"space-around", ()),
  "alignYTop": style(~alignItems=#"flex-start", ()),
  "alignYBottom": style(~alignItems=#"flex-end", ()),
  "alignYCenter": style(~alignItems=#center, ()),
  "alignYStretch": style(~alignItems=#stretch, ()),
  "alignYBaseline": style(~alignItems=#baseline, ()),
})

let styleProps = (~grow, ~wrap, ~space, ~align: align, ~alignY: alignY) => {
  let gapValue = space->Spaces.toPx
  let minVerticalGapValue = Spaces.xsmallPx
  StyleX.props([
    styles["root"],
    style(~columnGap=gapValue, ~rowGap=`max(calc(${gapValue} / 2), ${minVerticalGapValue})`, ()),
    grow ? styles["grow"] : style(),
    wrap ? styles["wrap"] : style(),
    switch align {
    | #start => styles["alignStart"]
    | #end => styles["alignEnd"]
    | #center => styles["alignCenter"]
    | #spaceBetween => styles["alignSpaceBetween"]
    | #spaceAround => styles["alignSpaceAround"]
    },
    switch alignY {
    | #top => styles["alignYTop"]
    | #bottom => styles["alignYBottom"]
    | #center => styles["alignYCenter"]
    | #stretch => styles["alignYStretch"]
    | #baseline => styles["alignYBaseline"]
    },
  ])
}

@react.component
let make = (
  ~children,
  ~space=#xxsmall,
  ~align=#start,
  ~alignY=#center,
  ~grow=false,
  ~wrap=false,
) => {
  let {?style, ?className} = styleProps(~grow, ~wrap, ~align, ~alignY, ~space)
  <div ?style ?className> {children} </div>
}
