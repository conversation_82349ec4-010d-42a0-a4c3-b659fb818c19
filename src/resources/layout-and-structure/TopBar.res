open StyleX

let styles = StyleX.create({
  "container": style(
    ~paddingTop="15px",
    ~height="70px",
    ~backgroundColor=Colors.neutralColor00,
    ~boxShadow=`4px 4px 3px 0px ${Colors.neutralColor50}1F`,
    (),
  ),
  "burger": style(~marginLeft="-10px", ()),
})

@react.component
let make = (~title, ~onRequestNavBarToggle) => {
  <DivX style={StyleX.props([styles["container"]])}>
    <DivX style={StyleX.props([styles["burger"]])}>
      <Burger onPress={_ => onRequestNavBarToggle()} />
    </DivX>
    <Title level=#1> {title->React.string} </Title>
  </DivX>
}

let make = React.memo(make)
