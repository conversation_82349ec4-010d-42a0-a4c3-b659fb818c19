open StyleX

let styles = StyleX.create({
  "root": style(~display=#flex, ~flexDirection=#column, ()),
  "grow": style(~flex="1", ()),
})

let styleFromSpace = (~spaceX, ~spaceY) => {
  let styles = []
  let styles = switch spaceX {
  | Some(spaceX) => styles->Array.concat([style(~paddingInline=spaceX->Spaces.toPx, ())])
  | None => styles
  }
  let styles = switch spaceY {
  | Some(spaceY) => styles->Array.concat([style(~paddingBlock=spaceY->Spaces.toPx, ())])
  | None => styles
  }
  StyleX.arrayStyle(styles)
}

let styleExceptionFromSpace = (~spaceTop, ~spaceBottom, ~spaceLeft, ~spaceRight) => {
  let styles = []
  let styles = switch spaceTop {
  | Some(spaceTop) => styles->Array.concat([style(~paddingTop=spaceTop->Spaces.toPx, ())])
  | None => styles
  }
  let styles = switch spaceBottom {
  | Some(spaceBottom) => styles->Array.concat([style(~paddingBottom=spaceBottom->Spaces.toPx, ())])
  | None => styles
  }
  let styles = switch spaceLeft {
  | Some(spaceLeft) => styles->Array.concat([style(~paddingLeft=spaceLeft->Spaces.toPx, ())])
  | None => styles
  }
  let styles = switch spaceRight {
  | Some(spaceRight) => styles->Array.concat([style(~paddingRight=spaceRight->Spaces.toPx, ())])
  | None => styles
  }
  StyleX.arrayStyle(styles)
}

let styleProps = (
  ~spaceX,
  ~spaceY,
  ~spaceTop,
  ~spaceBottom,
  ~spaceLeft,
  ~spaceRight,
  ~grow,
  ~additionalStyle,
) =>
  StyleX.props([
    styles["root"],
    styleFromSpace(~spaceX, ~spaceY),
    styleExceptionFromSpace(~spaceTop, ~spaceBottom, ~spaceLeft, ~spaceRight),
    grow ? styles["grow"] : style(),
    additionalStyle,
  ])

@react.component
let make = React.forwardRef((
  ~children=?,
  ~spaceX=?,
  ~spaceY=?,
  ~spaceTop=?,
  ~spaceBottom=?,
  ~spaceLeft=?,
  ~spaceRight=?,
  ~grow=false,
  ~style=StyleX.style(),
  ref,
) => {
  let ref = ref->Js.Nullable.toOption->Option.map(ReactDOM.Ref.domRef)
  let blankContent = switch children {
  | Some(children) =>
    children->React.Children.toArray->Array.every(element => element === React.null)
  | None => false
  }

  let stylePropsResult = styleProps(
    ~spaceX,
    ~spaceY,
    ~spaceTop,
    ~spaceBottom,
    ~spaceLeft,
    ~spaceRight,
    ~grow,
    ~additionalStyle=style,
  )
  let style = stylePropsResult.style
  let className = stylePropsResult.className

  if !blankContent {
    <div ?ref ?style ?className>
      {switch children {
      | Some(content) => content
      | None => React.null
      }}
    </div>
  } else {
    React.null
  }
})
