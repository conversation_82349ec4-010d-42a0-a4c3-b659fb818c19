open StyleX

let styles = StyleX.create({
  "root": style(~width="1px", ~height="30px", ~backgroundColor=Colors.neutralColor20, ()),
})

let styleProps = (~size) => {
  let height = switch size {
  | #tiny | #xxsmall | #xsmall | #small | #normal => "30px"
  | #medium | #large | #xlarge => "40px"
  }
  StyleX.props([styles["root"], style(~height, ())])
}

// TODO - https://react-spectrum.adobe.com/react-aria/useSeparator.html
// TODO - merge the Separator.res and Divider.res as they share identical logic/purpose
@react.component
let make = (~space=#xsmall, ~size=#normal) => {
  let {?style, ?className} = styleProps(~size)

  <Box spaceX=space>
    <div ?style ?className />
  </Box>
}

let make = React.memo(make)
