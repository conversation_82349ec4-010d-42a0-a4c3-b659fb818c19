open StyleX

let styles = StyleX.create({
  "container": style(
    ~display=#flex,
    ~flexDirection=#column,
    ~flex="1",
    ~paddingInline=Spaces.xxlargePx,
    ~paddingTop=Spaces.largePx,
    (),
  ),
  "header": style(~display=#flex, ~alignItems=#center, ()),
  "title": style(~display=#flex, ~flexDirection=#column, ~flex="1", ()),
  "content": style(~display=#flex, ~flexDirection=#column, ~flex="1", ()),
})

let containerStyleFromParams = (~variation) =>
  switch variation {
  | #compact => style(~paddingBottom="20px", ())
  | #standard => style(~paddingBottom="90px", ())
  }

type variation = [#standard | #compact]

@react.component
let make = (
  ~children,
  ~variation: variation=#standard,
  ~title=?,
  ~subtitle=?,
  ~renderTitleEnd=() => React.null,
  ~renderActions=?,
  ~renderHeaderActions=?,
) => {
  let (canGoBack, _) = Navigation.useGoBack()

  <DivX style={StyleX.props([styles["container"], containerStyleFromParams(~variation)])}>
    {switch variation {
    | #standard if canGoBack =>
      <Box
        spaceBottom={switch subtitle {
        | Some(_) => #medium
        | _ => #small
        }}>
        <GoBackButton />
      </Box>
    | _ => React.null
    }}
    {switch title {
    | Some(title) =>
      <DivX style={StyleX.props([styles["header"]])}>
        <DivX style={StyleX.props([styles["title"]])}>
          <Inline space=#medium>
            <Stack space=#xsmall>
              <Inline space=#medium alignY=#bottom wrap=true>
                <Title level=#2> {title->React.string} </Title>
                {renderTitleEnd()}
              </Inline>
              {switch subtitle {
              | Some(value) =>
                <TextStyle variation=#normal size=#small> {value->React.string} </TextStyle>
              | _ => React.null
              }}
            </Stack>
          </Inline>
        </DivX>
        {switch renderActions {
        | Some(renderActions) => renderActions()
        | None => React.null
        }}
      </DivX>
    | _ => React.null
    }}
    {switch renderHeaderActions {
    | Some(renderHeaderActions) => <Box spaceTop=#medium> {renderHeaderActions()} </Box>
    | None => React.null
    }}
    <DivX style={StyleX.props([styles["content"]])}> children </DivX>
  </DivX>
}

let make = React.memo(make)
