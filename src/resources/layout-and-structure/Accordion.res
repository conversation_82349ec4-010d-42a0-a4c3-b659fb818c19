open StyleX

let styles = StyleX.create({
  "icon": style(~display=#flex, ~paddingInline="3px", ()),
  "contentView": style(
    ~overflow=#hidden,
    ~transition="all .5s cubic-bezier(0.190, 1.000, 0.220, 1.000) 0s",
    (),
  ),
})

let contentViewStyleFromParams = (~opened) =>
  StyleX.props([
    styles["contentView"],
    opened
      ? style(~opacity=1., ~paddingTop=Spaces.xsmallPx, ())
      : style(~opacity=0., ~height="0px", ~paddingBlock="0px", ()),
  ])

@react.component
let make = (~children, ~triggerShowView, ~triggerHideView, ~onToggle=?) => {
  let (opened, setOpened) = React.useState(() => false)
  let (ref, hovered) = Hover.use()

  let onPress = _ => {
    setOpened(_ => !opened)
    switch onToggle {
    | Some(onToggle) => onToggle(!opened)
    | _ => ()
    }
  }

  <DivX>
    <Inline>
      <Touchable ref onPress>
        {if opened {
          <Inline>
            <DivX style={StyleX.props([styles["icon"]])}>
              <Icon name=#arrow_up_light />
            </DivX>
            triggerHideView
          </Inline>
        } else {
          <DivX
            style={StyleX.props([
              style(
                ~borderBottom="1px solid " ++ (
                  hovered ? Colors.neutralColor20 : Colors.transparent
                ),
                (),
              ),
            ])}>
            <Inline>
              <DivX style={StyleX.props([styles["icon"]])}>
                <Icon name=#arrow_down_light />
              </DivX>
              triggerShowView
            </Inline>
          </DivX>
        }}
      </Touchable>
    </Inline>
    <DivX style={contentViewStyleFromParams(~opened)}> children </DivX>
  </DivX>
}

let make = React.memo(make)
