import { make } from './Illustration.gen'

type IllustrationProps = React.ComponentProps<typeof make>

const Illustration = make as React.FunctionComponent<IllustrationProps>

export { Illustration, type IllustrationProps }
export {
  error as Error,
  create as Create,
  notFound as NotFound,
  notAsked as NotAsked,
  shopMissing as ShopMissing,
  warningTriangle as WarningTriangle,
} from './Illustration.gen'
