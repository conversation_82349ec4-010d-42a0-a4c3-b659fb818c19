// ReactiveConf 2019 - <PERSON>: Rethinking Design Practices
// > https://www.youtube.com/watch?v=jnV1u67_yVg
// Space in Design Systems
// > https://medium.com/eightshapes-llc/space-in-design-systems-188bcbae0d62

let xxsmall = 2.
let xsmall = 4.
let small = 8.
let xnormal = 10.
let normal = 12.
let xmedium = 14.
let medium = 16.
let large = 20.
let xlarge = 24.
let xxlarge = 32.
let huge = 48.
let xhuge = 64.
let xxhuge = 96.

@inline let xxsmallPx = "2px"
@inline let xsmallPx = "4px"
@inline let smallPx = "8px"
@inline let xnormalPx = "10px"
@inline let normalPx = "12px"
@inline let xmediumPx = "14px"
@inline let mediumPx = "16px"
@inline let largePx = "20px"
@inline let xlargePx = "24px"
@inline let xxlargePx = "32px"
@inline let hugePx = "48px"
@inline let xhugePx = "64px"
@inline let xxhugePx = "96px"

@genType
type t = [
  | #none
  | #xxsmall
  | #xsmall
  | #small
  | #xnormal
  | #normal
  | #xmedium
  | #medium
  | #large
  | #xlarge
  | #xxlarge
  | #huge
  | #xhuge
  | #xxhuge
]

let toFloat = value =>
  switch value {
  | #none => 0.
  | #xxsmall => xxsmall
  | #xsmall => xsmall
  | #small => small
  | #xnormal => xnormal
  | #normal => normal
  | #xmedium => xmedium
  | #medium => medium
  | #large => large
  | #xlarge => xlarge
  | #xxlarge => xxlarge
  | #huge => huge
  | #xhuge => xhuge
  | #xxhuge => xhuge
  }

let toPx = value =>
  switch value {
  | #none => "0px"
  | #xxsmall => xxsmallPx
  | #xsmall => xsmallPx
  | #small => smallPx
  | #xnormal => xnormalPx
  | #normal => normalPx
  | #xmedium => xmediumPx
  | #medium => mediumPx
  | #large => largePx
  | #xlarge => xlargePx
  | #xxlarge => xxlargePx
  | #huge => hugePx
  | #xhuge => xhugePx
  | #xxhuge => xhugePx
  }
