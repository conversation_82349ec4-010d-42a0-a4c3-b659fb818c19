// TODO - should be base upon different iPads resolutions (i.e 1188px)
let xsmall = "(min-width: 24rem)" // 384px
let small = "(min-width: 34rem)" // 544px
let medium = "(min-width: 48rem)" // 768px
let large = "(min-width: 64rem)" // 1024px
let huge = "(min-width: 87rem)" // 1392px
let massive = "(min-width: 100rem)" /* 1600p */

type t = [#xsmall | #small | #medium | #large | #huge | #massive]

let toMediaQuery = breakpoint =>
  switch breakpoint {
  | #xsmall => xsmall
  | #small => small
  | #medium => medium
  | #large => large
  | #huge => huge
  | #massive => massive
  }
