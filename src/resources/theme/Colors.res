// Transparency
@genType @inline let transparent = "transparent"
@genType @inline let neutralAlpha100 = "#ffffffFF"

// Primary tones - Disc
@genType @inline let brandColor00 = "#fdf2fb"
@genType @inline let brandColor05 = "#fbe8f8"
@genType @inline let brandColor10 = "#f4e4ee"
@genType @inline let brandColor30 = "#dcadcb"
@genType @inline let brandColor40 = "#c04695"
@genType let nonInlineBrandColor40 = brandColor40
@genType @inline let brandColor50 = "#a01e72"
@genType let nonInlineBrandColor50 = brandColor50
@genType @inline let brandColor55 = "#960F6F"
@genType @inline let brandColor60 = "#8a0262"
@genType let nonInlineBrandColor60 = brandColor60

// Secondary tones - Havelock
@genType @inline let secondaryColor05 = "#d4e1ff"
@genType @inline let secondaryColor10 = "#c8d6f9"
@genType @inline let secondaryColor40 = "#90a3e4"
@genType @inline let secondaryColor50 = "#647ccc"
@genType @inline let secondaryColor60 = "#4962b6"
@genType @inline let secondaryColor65 = "#455fb2"
@genType @inline let secondaryColor70 = "#3B54A5"

// Neutral tones - Cadet
@genType @inline let neutralColor00 = "#fefefe"
@genType @inline let neutralColor05 = "#f8f8fb"
@genType @inline let neutralColor10 = "#f3f3f7"
@genType @inline let neutralColor15 = "#e7e7ee"
@genType @inline let neutralColor20 = "#d7d7e0"
@genType @inline let neutralColor25 = "#bdbdca"
@genType @inline let neutralColor30 = "#a2a1b0"
@genType @inline let neutralColor35 = "#8c8B9b"
@genType @inline let neutralColor45 = "#797785"
@genType @inline let neutralColor50 = "#797885"
@genType @inline let neutralColor55 = "#767589"
@genType @inline let neutralColor70 = "#535165"
@genType @inline let neutralColor80 = "#35334b"
@genType @inline let neutralColor90 = "#25243a"
@genType @inline let neutralColor100 = "#07061e"

// Green tones - Mountain Meadow
@genType @inline let successColor10 = "#bef1dc"
@genType @inline let successColor20 = "#8fe7c3"
@genType @inline let successColor50 = "#1dbb7a"
@genType @inline let successColor60 = "#0da365"
@genType @inline let successColor70 = "#125135"
@genType @inline let successColor80 = "#0b5135"

// Warning tones - TestUrinaire
@genType @inline let warningColor10 = "#fff7e1"
@genType @inline let warningColor20 = "#fff4cc"
@genType @inline let warningColor25 = "#fff2ba"
@genType @inline let warningColor30 = "#faebaf"
@genType @inline let warningColor50 = "#ffec97"
@genType @inline let warningColor70 = "#ffdb3c"
@genType @inline let warningColor90 = "#867026"

// Red tones - Amaranthapprox
@genType @inline let dangerColor05 = "#ffedef"
@genType @inline let dangerColor10 = "#ffe0e9"
@genType @inline let dangerColor30 = "#e186a2"
@genType @inline let dangerColor50 = "#e61e5a"
@genType @inline let dangerColor55 = "#c81c51"
@genType @inline let dangerColor60 = "#ce0340"
@genType @inline let dangerColor70 = "#b01745"

// Extra tones - Yolo
@genType @inline let extraColorA50 = "#35334b"
@genType @inline let extraColorB50 = "#681e52"
@genType @inline let extraColorC50 = "#345e5d"
@genType @inline let extraColorG50 = "#ec5757"
@genType @inline let extraColorH50 = "#647ccc"
@genType @inline let extraColorI50 = "#dea439"
@genType @inline let extraColorK10 = "#ffdbe0"
@genType @inline let extraColorK30 = "#f9acb4"
@genType @inline let extraColorK50 = "#41c2aa"
@genType @inline let extraColorK60 = "#eb9ea7"
@genType @inline let extraColorK70 = "#ffe1Ca"
@genType @inline let extraColorK80 = "#ab4319"

// Product related colors
module Product = {
  @genType @inline let whiteBadgeColor = "#fff4cc"
  @genType @inline let whitePastilleColor = "#EED87A"
  @genType @inline let whiteTextColor = "#907b1e"

  @genType @inline let blondBadgeColor = "#f8e7cc"
  @genType @inline let blondTextColor = "#dd8700"

  @genType @inline let amberBadgeColor = "#ead5d2"
  @genType @inline let amberTextColor = "#982d1f"

  @genType @inline let darkBadgeColor = "#ffcfce"
  @genType @inline let darkTextColor = "#5f0f0c"

  @genType @inline let blackBadgeColor = "#ead5d2"
  @genType @inline let blackTextColor = "#300c0a"

  @genType @inline let redBadgeColor = "#f5E8f0"
  @genType @inline let redTextColor = "#a01e72"

  @genType @inline let roseBadgeColor = "#fce5e8"
  @genType @inline let roseTextColor = "#e87582"
}

// Document template colors
@genType @inline let titleDefaultColortemplate = "#1a191b"
@genType @inline let theadTextDefaultColortemplate = "#122a4f"
@genType @inline let backgroundDefaultColortemplate = "#f8f8fb"
@genType @inline let borderDefaultColortemplate = "#19355f"
@genType @inline let linkColor = "#0b5135"
// NOTE - for HTML DOM input also modify this color in style.css
@inline let placeholderTextColor = "#bdbdca" // neutralColor25
