// NOTE - "With the current architecture every file compilation can be cached. Only if a file changes do we need to re-compile it.
// "If we added support for imports, we would need to invalidate the cache of all dependent files whenever a file changed."
// from: https://github.com/facebook/stylex/issues/238#issuecomment-1869161263

module Primitive = {
  @inline let white50 = "rgba(255, 255, 255, 0.05)"
  @inline let white100 = "rgba(255, 255, 255, 0.1)"
  @inline let white200 = "rgba(255, 255, 255, 0.2)"
  @inline let white300 = "rgba(255, 255, 255, 0.3)"
  @inline let white400 = "rgba(255, 255, 255, 0.4)"
  @inline let white500 = "rgba(255, 255, 255, 0.5)"
  @inline let white600 = "rgba(255, 255, 255, 0.6)"
  @inline let white700 = "rgba(255, 255, 255, 0.7)"
  @inline let white800 = "rgba(255, 255, 255, 0.8)"
  @inline let white900 = "rgba(255, 255, 255, 0.9)"
  @inline let white950 = "rgba(255, 255, 255, 0.95)"
  @inline let white = "rgba(255, 255, 255, 1)"

  @inline let positive50 = "#EEFFF6"
  @inline let positive100 = "#D8FFEE"
  @inline let positive200 = "#B4FEDD"
  @inline let positive300 = "#79FCC3"
  @inline let positive400 = "#38F0A0"
  @inline let positive500 = "#0ED982"
  @inline let positive600 = "#05B469"
  @inline let positive700 = "#088D55"
  @inline let positive800 = "#0C6F46"
  @inline let positive900 = "#0B5135"
  @inline let positive950 = "#00331F"

  @inline let destructive50 = "#FDF2F7"
  @inline let destructive100 = "#FCE7F1"
  @inline let destructive200 = "#FAD0E3"
  @inline let destructive300 = "#F8A9CC"
  @inline let destructive400 = "#F274A8"
  @inline let destructive500 = "#EA4A88"
  @inline let destructive600 = "#D92964"
  @inline let destructive700 = "#C81C51"
  @inline let destructive800 = "#9B193F"
  @inline let destructive900 = "#821938"
  @inline let destructive950 = "#4F081C"
}

/* ---------- Neutral-Inverse ---------- */
@genType @inline let textNeutralInverseDefault = "rgba(255, 255, 255, 0.3)" // white300
@genType @inline let textNeutralInverseHover = "rgba(255, 255, 255, 0.7)" // white700
@genType @inline let textNeutralInverseAccent = "white" // white
@genType @inline let textNeutralInverseDisabled = "rgba(255, 255, 255, 0.5)" // white500

/* ---------- Success ---------- */
@genType @inline let fillSuccessDefault = "#05B469" // positive600
@genType @inline let fillSuccessHover = "#088D55" // positive700
@genType @inline let fillSuccessPressed = "#B4FEDD" // positive200
@genType @inline let fillSuccessAccent = "#0B5135" // positive900
@genType @inline let fillSuccessFaded = "#D8FFEE" // positive100
@genType @inline let fillSuccessFadedHover = "#B4FEDD" // positive200
@genType @inline let fillSuccessWeak = "#EEFFF6" // positive50
@genType @inline let fillSuccessWeakHover = "#D8FFEE" // positive100
@genType @inline let fillSuccessDisabled = "#EEFFF6" // positive50

@genType @inline let borderSuccessDefault = "#05B469" // positive600
@genType @inline let borderSuccessHover = "#088D55" // positive700
@genType @inline let borderSuccessFocus = "#B4FEDD" // positive200
@genType @inline let borderSuccessPressed = "#0ED982" // positive500
@genType @inline let borderSuccessAccent = "#0B5135" // positive900
@genType @inline let borderSuccessFaded = "#79FCC3" // positive300
@genType @inline let borderSuccessFadedHover = "#38F0A0" // positive400
@genType @inline let borderSuccessDisabled = "#B4FEDD" // positive200

@genType @inline let textSuccessDefault = "#05B469" // positive600
@genType @inline let textSuccessHover = "#088D55" // positive700
@genType @inline let textSuccessAccent = "#0C6F46" // positive800
@genType @inline let textSuccessDisabled = "#B4FEDD" // positive200

/* ---------- Danger ---------- */
@genType @inline let fillDangerDefault = "#D92964" // destructive600
@genType @inline let fillDangerHover = "#C81C51" // destructive700
@genType @inline let fillDangerPressed = "#FAD0E3" // destructive200
@genType @inline let fillDangerAccent = "#821938" // destructive900
@genType @inline let fillDangerFaded = "#FCE7F1" // destructive100
@genType @inline let fillDangerFadedHover = "#FAD0E3" // destructive200
@genType @inline let fillDangerWeak = "#FDF2F7" // destructive50
@genType @inline let fillDangerWeakHover = "#FCE7F1" // destructive100
@genType @inline let fillDangerDisabled = "#FDF2F7" // destructive50

@genType @inline let borderDangerDefault = "#D92964" // destructive600
@genType @inline let borderDangerHover = "#C81C51" // destructive700
@genType @inline let borderDangerFocus = "#FAD0E3" // destructive200
@genType @inline let borderDangerPressed = "#EA4A88" // destructive500
@genType @inline let borderDangerAccent = "#821938" // destructive900
@genType @inline let borderDangerFaded = "#F8A9CC" // destructive300
@genType @inline let borderDangerFadedHover = "#F274A8" // destructive400
@genType @inline let borderDangerDisabled = "#FAD0E3" // destructive200

@genType @inline let textDangerDefault = "#D92964" // destructive600
@genType @inline let textDangerHover = "#C81C51" // destructive700
@genType @inline let textDangerAccent = "#821938" // destructive900
@genType @inline let textDangerDisabled = "#FAD0E3" // destructive200
