let tiny: float
let xxsmall: float
let xsmall: float
let small: float
let normal: float
let large: float
let xlarge: float
let huge: float

@inline let tinyPx: string
@inline let xxsmallPx: string
@inline let xsmallPx: string
@inline let smallPx: string
@inline let normalPx: string
@inline let largePx: string
@inline let xlargePx: string
@inline let hugePx: string

@genType
type t = [#tiny | #xxsmall | #xsmall | #small | #normal | #large | #xlarge | #huge]

let toPx: t => string
