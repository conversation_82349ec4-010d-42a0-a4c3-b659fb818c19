let tiny = 11.0
let xxsmall = 12.0
let xsmall = 13.0
let small = 14.0
let normal = 15.0
let large = 16.0
let xlarge = 17.0
let huge = 18.0

@inline let tinyPx = "11px"
@inline let xxsmallPx = "12px"
@inline let xsmallPx = "13px"
@inline let smallPx = "14px"
@inline let normalPx = "15px"
@inline let largePx = "16px"
@inline let xlargePx = "17px"
@inline let hugePx = "18px"

type t = [#tiny | #xxsmall | #xsmall | #small | #normal | #large | #xlarge | #huge]

let toPx = size =>
  switch size {
  | #tiny => tinyPx
  | #xxsmall => xxsmallPx
  | #xsmall => xsmallPx
  | #small => smallPx
  | #normal => normalPx
  | #large => largePx
  | #xlarge => xlargePx
  | #huge => hugePx
  }
