let fontFamilyFromFontName = fontName =>
  switch fontName {
  | #Archivo => "Archivo"
  | #LibreFranklin => "Libre Franklin"
  }

// NOTE - interface file doesn't support @inline
@inline let archivoRegular = `normal 400 14px "Archivo"`
@inline let archivoMedium = `normal 500 14px "Archivo"`
@inline let archivoSemiBold = `normal 600 14px "Archivo"`
@inline let archivoBold = `normal 700 14px "Archivo"`
@inline let libreFranklinRegular = `normal 400 14px "Libre Franklin"`
@inline let libreFranklinMedium = `normal 500 14px "Libre Franklin"`
@inline let libreFranklinSemiBold = `normal 600 14px "Libre Franklin"`
@inline let libreFranklinBold = `normal 700 14px "Libre Franklin"`
