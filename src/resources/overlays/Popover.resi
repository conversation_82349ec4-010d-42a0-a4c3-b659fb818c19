type t = ReactStately.Overlay.Trigger.state

type trigger = {
  state: t,
  ariaProps: ReactAria.Overlay.Trigger.t,
  ref: React.ref<Js.Nullable.t<Dom.element>>,
}

let useTrigger: (~defaultOpened: bool=?, ~modalPopover: bool=?, unit) => trigger

module Dialog: {
  @react.component
  let make: (
    ~children: React.element,
    ~ariaLabel: string=?,
    ~ariaProps: ReactAria.Overlay.overlayProps,
    ~style: ReactDOM.style=?,
    ~className: string=?,
  ) => React.element
}

module Context: {
  module Provider: {
    let make: React.component<React.Context.props<option<ReactStately.Overlay.Trigger.state>>>
  }

  let use: unit => t
}

let useState: unit => t

type layout = [#auto | #triggerMinWidth | #triggerStrictWidth]
type variation = [#normal | #arrowed]
type size = [#normal | #compact]

@react.component
let make: (
  ~children: React.element,
  ~popoverRef: ReactDOM.Ref.currentDomRef=?,
  ~triggerRef: ReactDOM.Ref.currentDomRef,
  ~state: t,
  ~variation: variation=?,
  ~borderColor: string=?,
  ~size: size=?,
  ~placement: ReactAria.Overlay.Position.placement=?,
  ~layout: layout=?,
  ~animation: AnimatedRender.animation=?,
  ~overlayPriority: bool=?,
  ~dismissable: bool=?,
  ~shouldUpdatePosition: bool=?,
  ~modal: bool=?,
  ~offset: float=?,
  ~crossOffset: float=?,
) => React.element
