// TODO - to rename as <InputView> / <TriggerView> / <TextTrigger> ?
open Intl
open StyleX

let styles = StyleX.create({
  "root": style(
    ~display=#flex,
    ~alignItems=#center,
    ~justifyContent=#"space-between",
    ~gap=Spaces.xsmallPx,
    ~overflow=#hidden,
    ~border="solid 1px " ++ Colors.transparent,
    ~borderRadius="5px",
    ~boxSizing=#"border-box",
    (),
  ),
  "filter": style(
    ~height="42px",
    ~paddingInline=Spaces.xmediumPx,
    ~backgroundColor=Colors.neutralColor15,
    (),
  ),
  // TODO – should use TextFieldStyle
  "field": style(
    ~height="40px",
    ~paddingInline=Spaces.normalPx,
    ~backgroundColor=Colors.neutralColor00,
    ~borderColor=Colors.neutralColor20,
    (),
  ),
  "textContainer": style(~display=#flex, ~gap="5px", ()),
  "text": style(
    ~font=`normal 400 15px "Libre Franklin"`,
    ~color=Colors.neutralColor90,
    ~letterSpacing="0.125px",
    ~whiteSpace=#nowrap,
    (),
  ),
  "textLabel": style(
    ~color=Colors.neutralColor50,
    ~letterSpacing="-0.2px",
    ~paddingRight="0px",
    (),
  ),
  "textDisabled": style(~color=Colors.neutralColor35, ()),
  "filterActive": style(~backgroundColor=Colors.neutralColor20, ()),
  "fieldActive": style(~borderColor=Colors.neutralColor25, ()),
  "filterDisabled": style(~backgroundColor=Colors.neutralColor20, ()),
  "fieldDisabled": style(
    ~backgroundColor=Colors.neutralColor05,
    ~borderColor=Colors.neutralColor15,
    (),
  ),
  "filterFocused": style(~borderColor=Colors.neutralColor25, ()),
  "filterHighlighted": style(~borderColor=Colors.neutralColor30, ()),
  "fieldFocused": style(~borderColor=Colors.neutralColor30, ()),
  "fieldHighlighted": style(~borderColor=Colors.neutralColor35, ()),
  "fieldFocusedAndErrored": style(~borderColor=Colors.dangerColor55, ()),
  "fieldErrored": style(~borderColor=Colors.dangerColor30, ()),
})

let filterStyleProps = (~hovered, ~focused, ~active, ~highlighted, ~disabled, ~size) =>
  StyleX.props([
    styles["root"],
    styles["filter"],
    switch (hovered, active, disabled) {
    | (true, _, false) | (_, true, false) => styles["filterActive"]
    | (_, _, true) => styles["filterDisabled"]
    | (false, false, false) => style()
    },
    highlighted ? styles["filterHighlighted"] : style(),
    focused ? styles["filterFocused"] : style(),
    switch size {
    | #normal => style(~minWidth="160px", ())
    | #compact =>
      style(
        ~minWidth="130px",
        ~height="30px",
        ~padding=`${Spaces.smallPx} ${Spaces.normalPx}`,
        ~borderRadius="4px",
        (),
      )
    },
  ])

let fieldStyleProps = (~hovered, ~focused, ~active, ~highlighted, ~disabled, ~errored, ~size) =>
  StyleX.props([
    styles["root"],
    styles["field"],
    switch (hovered, active, disabled) {
    | (true, _, false)
    | (_, true, false) =>
      styles["fieldActive"]
    | (_, _, true) => styles["fieldDisabled"]
    | (false, false, false) => style()
    },
    switch (focused, errored) {
    | (true, true) => styles["fieldFocusedAndErrored"]
    | (true, false) => styles["fieldFocused"]
    | (false, true) => styles["fieldErrored"]
    | (false, false) => style()
    },
    highlighted ? styles["fieldHighlighted"] : style(),
    switch size {
    | #normal => style(~minWidth="160px", ())
    | #compact =>
      style(
        ~minWidth="130px",
        ~height="30px",
        ~paddingInline=Spaces.normalPx,
        ~paddingBlock=Spaces.smallPx,
        ~borderRadius="4px",
        (),
      )
    },
  ])

let textContainerStyleProps = () => StyleX.props([styles["textContainer"]])

let textStyleProps = (~disabled, ~size, ~label, ~grow) =>
  StyleX.props([
    styles["text"],
    label ? styles["textLabel"] : style(),
    disabled ? styles["textDisabled"] : style(),
    style(~flexGrow=grow ? "1" : "0", ()),
    switch size {
    | #compact => style(~fontSize="13px", ())
    | #normal => style(~fontSize="15px", ())
    },
  ])

type size = [#normal | #compact]
type inputFieldPresetProps = {
  required: bool,
  tooltip?: React.element,
  errorMessage?: string,
}
type preset = [
  | #filter
  | #inputField(inputFieldPresetProps)
]

@react.component
let make = (
  ~children,
  ~preset: preset,
  ~size=#normal,
  ~growContent=false,
  ~label=?,
  ~icon=?,
  ~hideIcon=false,
  ~hovered,
  ~active=false,
  ~focused=false,
  ~highlighted=false,
  ~disabled=false,
) => {
  let iconComponent = {
    let name = switch icon {
    | Some(name) => name
    | None => active ? #arrow_up_light : #arrow_down_light
    }
    let size = switch size {
    | #compact => 16.
    | #normal => 20.
    }
    let fill = if highlighted {
      Colors.neutralColor90
    } else if hovered || focused {
      Colors.neutralColor70
    } else {
      Colors.neutralColor50
    }

    <div
      style={ReactDOM.Style.make(
        ~display="flex",
        ~marginRight="-2px",
        ~paddingLeft=Spaces.xxsmallPx,
        (),
      )}>
      <Icon name size fill />
    </div>
  }

  let {?style, ?className} = switch preset {
  | #filter => filterStyleProps(~hovered, ~focused, ~highlighted, ~active, ~size, ~disabled)
  | #inputField({?errorMessage}) =>
    fieldStyleProps(
      ~hovered,
      ~active,
      ~focused,
      ~highlighted,
      ~disabled,
      ~errored=errorMessage->Option.isSome,
      ~size,
    )
  }
  let {style: ?textContainerStyle, className: ?textContainerClassName} = textContainerStyleProps()
  let {style: ?textLabelStyle, className: ?textLabelClassName} = textStyleProps(
    ~disabled,
    ~size,
    ~label=true,
    ~grow=growContent,
  )
  let {style: ?textStyle, className: ?textClassName} = textStyleProps(
    ~disabled,
    ~size,
    ~label=false,
    ~grow=growContent,
  )

  switch preset {
  | #filter =>
    <div ?style ?className>
      <div style=?textContainerStyle className=?textContainerClassName>
        {switch label {
        | Some(label) =>
          <span style=?textLabelStyle className=?textLabelClassName>
            {(label ++ (t(":") ++ " "))->React.string}
          </span>
        | _ => React.null
        }}
        <span style=?textStyle className=?textClassName> children </span>
      </div>
      {if !hideIcon {
        iconComponent
      } else {
        React.null
      }}
    </div>
  | #inputField({required, ?errorMessage, ?tooltip}) =>
    <Field ?label required ?errorMessage ?tooltip>
      <div ?style ?className>
        <span style=?textStyle className=?textClassName> children </span>
        {if !hideIcon {
          iconComponent
        } else {
          React.null
        }}
      </div>
    </Field>
  }
}

let make = React.memo(make)
