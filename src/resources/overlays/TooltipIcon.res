open StyleX

type variation = [#info | #alert]

let styles = StyleX.create({
  "root": style(~display=#contents, ~cursor=#help, ()),
})
let styleProps = () => StyleX.props([styles["root"]])

let delay = 75
let closeDelay = 100

@react.component
let make = (
  ~children,
  ~variation=#info,
  ~arrowed=false,
  ~placement=#"bottom start",
  ~altTriggerRef: option<ReactDOM.Ref.currentDomRef>=?,
  ~offset=2.,
  ~crossOffset=-1.,
) => {
  let (ref, hovered) = Hover.use(~ref=?altTriggerRef, ()) // ref to control the tooltip opening state instead
  let (opened, setOpened) = React.useState(() => false)

  let debouncedHovered = ReactUpdateDebounced.use(hovered, ~delay={!hovered ? closeDelay : delay})
  let opened = altTriggerRef->Option.mapWithDefault(opened, _ => debouncedHovered)

  let onOpenChange = opened =>
    if altTriggerRef->Option.isNone {
      setOpened(_ => opened)
    }

  React.useEffect1(() => {
    switch altTriggerRef->Option.flatMap(ref => ref.current->Js.Nullable.toOption) {
    | Some(domElement) =>
      ReactDomElement.setDomElementStyleProp(~domElement, ~styleProp="cursor", ~value="help")
    | None => ()
    }
    None
  }, [altTriggerRef])

  let {?style, ?className} = styleProps()

  <Tooltip
    content=children arrowed placement offset crossOffset delay closeDelay opened onOpenChange>
    <div ref={ref->ReactDOM.Ref.domRef} ?style ?className>
      <Icon
        size=13.
        name={switch variation {
        | #info => #info_tip
        | #alert => #alert_tip
        }}
        fill={switch (variation, debouncedHovered || opened) {
        | (#info, false) => Colors.neutralColor30
        | (#alert, false) => Colors.neutralColor70
        | (#info | #alert, true) => Colors.neutralColor90
        }}
      />
    </div>
  </Tooltip>
}
