@react.component
let make: (
  ~children: React.element,
  ~opened: bool,
  ~title: string,
  ~variation: [#primary | #secondary]=?,
  ~compact: bool=?,
  ~backgroundColor: string=?,
  ~hideFooter: bool=?,
  ~renderStartText: unit => React.element=?,
  ~criticalButtonText: string=?,
  ~criticalButtonCallback: unit => unit=?,
  ~abortButtonText: string=?,
  ~allowCommit: bool=?,
  ~commitButtonVariation: Button.variation=?,
  ~commitButtonText: string=?,
  ~commitButtonCallback: unit => unit=?,
  ~onRequestClose: unit => unit=?,
) => React.element
