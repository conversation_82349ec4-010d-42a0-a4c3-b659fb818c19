module Section: {
  @react.component
  let make: (
    ~children: React.element,
    ~title: string=?,
    ~tooltip: React.element=?,
    ~collapsable: bool=?,
    ~defaultCollapsed: bool=?,
  ) => React.element
}

module Tab = ReactStately.Collection.Item

type headerTitle = {text: string, titleStartElement?: React.element}
type headerTab = {disabledKeys?: array<ReactStately.key>, onChange?: ReactStately.key => unit}
type header = Title(headerTitle) | Tabs(headerTab)

@react.component
let make: (
  ~ariaProps: ReactAria.Overlay.overlayProps,
  ~children: React.element,
  ~width: float=?,
  ~header: header,
  ~commitDisabled: bool=?,
  ~commitLoading: bool=?,
  ~commitButtonText: string=?,
  ~onRequestCommit: unit => unit,
  ~onRequestDiscard: unit => unit,
) => React.element
