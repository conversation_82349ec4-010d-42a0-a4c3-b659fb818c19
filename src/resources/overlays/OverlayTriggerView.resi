@genType type size = [#normal | #compact]

type inputFieldPresetProps = {
  required: bool,
  tooltip?: React.element,
  errorMessage?: string,
}
@genType
type preset = [
  | #filter
  | #inputField(inputFieldPresetProps)
]

@genType @react.component
let make: (
  ~children: React.element,
  ~preset: preset,
  ~size: size=?,
  ~growContent: bool=?,
  ~label: string=?,
  ~icon: Icon.t=?,
  ~hideIcon: bool=?,
  ~hovered: bool,
  ~active: bool=?,
  ~focused: bool=?,
  ~highlighted: bool=?,
  ~disabled: bool=?,
) => React.element
