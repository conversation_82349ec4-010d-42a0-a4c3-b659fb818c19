// TODO - should rely on <Popover> OR implements `useModalOverlay`
// TODO - add story (once reworked with React Aria)

open StyleX
open WebAPI

module ModalFooter = {
  open Intl

  let styles = StyleX.create({
    "footerSecondary": style(
      ~zIndex=2,
      ~position=#relative,
      ~backgroundColor=Colors.neutralColor00,
      ~boxShadow=`2px 0px 10px 0px ${Colors.neutralColor50}33`,
      (),
    ),
  })

  let footerViewStyleFromParams = (~variation) =>
    StyleX.props([
      switch variation {
      | #primary => style()
      | #secondary => styles["footerSecondary"]
      },
    ])

  type variation = [#primary | #secondary]

  @react.component
  let make = (
    ~variation: variation,
    ~renderStartText=() => React.null,
    ~criticalButtonText,
    ~criticalButtonCallback,
    ~abortButtonText,
    ~allowCommit=true,
    ~commitButtonVariation,
    ~commitButtonText,
    ~commitButtonCallback,
    ~onRequestClose=() => (),
  ) =>
    <DivX style={footerViewStyleFromParams(~variation)}>
      {switch variation {
      | #primary =>
        <Box spaceX=#large spaceTop=#xsmall spaceBottom=#xlarge>
          <Inline space=#normal align=#end>
            <Button variation=#neutral onPress={_ => onRequestClose()}>
              {switch (commitButtonText, commitButtonCallback) {
              | (Some(_), Some(_)) => t("Cancel")
              | _ => t("Close")
              }->React.string}
            </Button>
            {switch (commitButtonText, commitButtonCallback) {
            | (Some(text), Some(callback)) =>
              <Button
                disabled={!allowCommit}
                variation=commitButtonVariation
                onPress={_ => {
                  onRequestClose()
                  callback()
                }}>
                {text->React.string}
              </Button>
            | _ => React.null
            }}
          </Inline>
        </Box>
      | #secondary =>
        <Box spaceX=#large spaceY=#small>
          <Inline align=#spaceBetween>
            {renderStartText()}
            <Inline space=#small>
              {switch (criticalButtonText, criticalButtonCallback) {
              | (Some(text), Some(callback)) =>
                <Inline>
                  <Button
                    variation=#danger
                    onPress={_ => {
                      onRequestClose()
                      callback()
                    }}>
                    {text->React.string}
                  </Button>
                </Inline>
              | _ => React.null
              }}
              {switch abortButtonText {
              | Some(text) =>
                <Button variation=#neutral onPress={_ => onRequestClose()}>
                  {text->React.string}
                </Button>
              | _ => React.null
              }}
              {switch (commitButtonText, commitButtonCallback) {
              | (Some(text), Some(callback)) =>
                <Button
                  disabled={!allowCommit}
                  variation=commitButtonVariation
                  onPress={_ => {
                    onRequestClose()
                    callback()
                  }}>
                  {text->React.string}
                </Button>
              | _ => React.null
              }}
            </Inline>
          </Inline>
        </Box>
      }}
    </DivX>

  let make = React.memo(make)
}

let styles = StyleX.create({
  "header": style(~borderBottom="2px solid " ++ Colors.neutralColor15, ()),
  "wrapper": style(
    ~position=#fixed,
    ~left="0px",
    ~top="0px",
    ~right="0px",
    ~bottom="0px",
    ~zIndex=100,
    ~display=#flex,
    ~justifyContent=#center,
    ~alignItems=#center,
    ~backgroundColor="transparent",
    (),
  ),
  "backdrop": style(
    ~zIndex=0,
    ~opacity=0.0,
    ~width="100vw",
    ~height="100vh",
    ~cursor=#auto,
    ~backgroundColor="rgba(7, 6, 30, 0.8)",
    (),
  ),
  "inner": style(
    ~position=#absolute,
    ~zIndex=1,
    ~minWidth="580px",
    ~maxWidth="60%",
    ~minHeight="225px",
    ~maxHeight="90%",
    ~backgroundColor=Colors.neutralColor00,
    ~borderRadius="5px",
    ~boxShadow=`0px 0px 20px 0px ${Colors.neutralColor100}1A`,
    (),
  ),
  "body": style(~flex="1", ()),
  "wrapperViewOpened": style(~zIndex=90, ~transition="all 0.1s ease-out 0s", ()),
  "wrapperViewClosed": style(~zIndex=-1, ~transition="all 0.1s ease-out 0s", ()),
  "backdropViewOpened": style(~opacity=0.75, ~transition="all 0.1s ease-out 0s", ()),
  "backdropViewClosed": style(~opacity=0.0, ~transition="all 0.1s ease-out 0s", ()),
  "innerViewOpened": style(
    ~opacity=1.0,
    ~transform="translateY(0px) scale(1)",
    ~transition="all 0.2s cubic-bezier(0.190, 1.000, 0.220, 1.000) 0s",
    (),
  ),
  "innerViewClosed": style(
    ~opacity=0.0,
    ~transform="translateY(-5px) scale(0.55)",
    ~transition="all 0.1s linear 0s",
    (),
  ),
})

let wrapperViewStyleFromParams = (~opened) =>
  opened ? styles["wrapperViewOpened"] : styles["wrapperViewClosed"]

let backdropViewStyleFromParams = (~opened) =>
  opened ? styles["backdropViewOpened"] : styles["backdropViewClosed"]

let innerViewStyleFromParams = (~opened, ~compact) =>
  StyleX.props([
    styles["inner"],
    opened ? styles["innerViewOpened"] : styles["innerViewClosed"],
    !compact ? style(~width="60%", ()) : style(),
  ])

let bodyViewStyleFromParams = (~backgroundColor) => style(~backgroundColor, ())

@react.component
let make = (
  ~children,
  ~opened,
  ~title,
  ~variation=#primary,
  ~compact=true,
  ~backgroundColor=Colors.neutralColor05,
  ~hideFooter=false,
  ~renderStartText=() => React.null,
  ~criticalButtonText=?,
  ~criticalButtonCallback=?,
  ~abortButtonText=?,
  ~allowCommit=true,
  ~commitButtonVariation=#success,
  ~commitButtonText=?,
  ~commitButtonCallback=?,
  ~onRequestClose=?,
) => {
  let (modalRoot, setModalRoute) = React.useState(_ =>
    document->Document.querySelector("#portals-modal")
  )

  React.useEffect2(() => {
    switch (modalRoot, document->Document.querySelector("body")) {
    | (None, Some(body)) =>
      let element = document->Document.createElement("div")
      element->DomElement.setAttribute("id", "portals-modal")
      setModalRoute(_ => Some(element))
      body->DomElement.asNode->DomNode.appendChild(~child=element)
    | (_, _) => ()
    }

    let handler = evt => {
      let ctrlKeyPressed = KeyboardEvent.ctrlKey(evt)
      switch KeyboardEvent.key(evt) {
      | "Enter" if ctrlKeyPressed =>
        commitButtonCallback->Option.forEach(commit => commit())
        onRequestClose->Option.forEach(close => close())
      | "Escape" => onRequestClose->Option.forEach(fn => fn())
      | _ => ()
      }
    }

    let domDocument = document->Document.asDomElement
    if opened {
      domDocument->DomElement.addKeyDownEventListener(handler)
    } else {
      domDocument->DomElement.removeKeyDownEventListener(handler)
    }

    Some(() => domDocument->DomElement.removeKeyDownEventListener(handler))
  }, (modalRoot, commitButtonCallback))

  switch (modalRoot, opened) {
  | (Some(portal), true) =>
    ReactDOM.createPortal(
      <DivX style={StyleX.props([styles["wrapper"], wrapperViewStyleFromParams(~opened)])}>
        <Touchable onPress={_ => onRequestClose->Option.forEach(fn => fn())}>
          <DivX style={StyleX.props([styles["backdrop"], backdropViewStyleFromParams(~opened)])} />
        </Touchable>
        <DivX style={innerViewStyleFromParams(~opened, ~compact)}>
          <DivX style={StyleX.props([styles["header"]])}>
            <Box spaceX=#large spaceY=#large>
              <Inline grow=true align={#spaceBetween}>
                <Title level=#3 weight=#medium> {title->React.string} </Title>
                {switch onRequestClose {
                | Some(onRequestClose) =>
                  <IconButton
                    name=#close_bold
                    color=Colors.neutralColor30
                    hoveredColor=Colors.neutralColor50
                    onPress={_ => onRequestClose()}
                  />
                | None => React.null
                }}
              </Inline>
            </Box>
          </DivX>
          <DivX style={StyleX.props([styles["body"], bodyViewStyleFromParams(~backgroundColor)])}>
            <Box grow=true> children </Box>
            {if !hideFooter {
              <ModalFooter
                variation
                allowCommit
                renderStartText
                commitButtonText
                commitButtonVariation
                abortButtonText
                criticalButtonText
                commitButtonCallback
                criticalButtonCallback
                ?onRequestClose
              />
            } else {
              React.null
            }}
          </DivX>
        </DivX>
      </DivX>,
      portal,
    )
  | (_, _) => React.null
  }
}

let make = React.memo(make)
