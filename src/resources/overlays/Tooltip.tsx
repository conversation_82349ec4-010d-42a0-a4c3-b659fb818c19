import { make, Span_make, Gap_make } from './Tooltip.gen'

type TooltipProps = React.ComponentProps<typeof make>
const Tooltip = make as React.FunctionComponent<TooltipProps>

type TooltipSpanProps = React.ComponentProps<typeof Span_make>
const TooltipSpan = Span_make as React.FunctionComponent<TooltipSpanProps>

type TooltipGapProps = React.ComponentProps<typeof Gap_make>
const TooltipGap = Gap_make as React.FunctionComponent<TooltipGapProps>

export {
  Tooltip,
  TooltipSpan,
  TooltipGap,
  type TooltipProps,
  type TooltipSpanProps,
  type TooltipGapProps,
}
