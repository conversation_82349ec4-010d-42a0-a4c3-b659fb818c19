open StyleX

let styles = StyleX.create({
  "root": style(
    ~display=#inline,
    ~backgroundColor=Colors.neutralColor10,
    ~padding="2px 6px",
    ~border="1px solid " ++ Colors.neutralColor15,
    ~borderRadius="3px",
    ~font=`normal 400 13px "Libre Franklin"`,
    ~color=Colors.neutralColor80,
    ~lineHeight="15px",
    ~whiteSpace=#nowrap,
    (),
  ),
})

let styleProps = (~hovered) =>
  StyleX.props([styles["root"], hovered ? style(~borderColor=Colors.neutralColor20, ()) : style()])

@react.component
let make = (~children, ~hovered=false) => {
  let {?style, ?className} = styleProps(~hovered)
  <kbd ?style ?className> children </kbd>
}

let make = React.memo(make)
