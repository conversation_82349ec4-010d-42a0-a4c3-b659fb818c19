open StyleX

let styles = StyleX.create({
  "root": style(
    ~overflowWrap=#anywhere,
    ~whiteSpace=#"pre-line",
    ~font=FontFaces.libreFranklinRegular,
    ~letterSpacing="0.125px",
    (),
  ),
  "neutralView": style(~color=Colors.neutralColor90, ()),
  "normalView": style(~color=Colors.neutralColor50, ()),
  "successView": style(~color=Colors.successColor50, ()),
  "negativeView": style(~color=Colors.dangerColor50, ()),
  "primaryView": style(~color=Colors.brandColor50, ()),
  "secondaryView": style(~color=Colors.secondaryColor50, ()),
  "placeholderView": style(~color=Colors.placeholderTextColor, ()),
  "subduedView": style(~color=Colors.neutralColor50, ~fontStyle=#italic, ()),
  "alignLeft": style(~textAlign=#left, ()),
  "alignCenter": style(~textAlign=#center, ()),
  "alignRight": style(~textAlign=#right, ()),
})

let weightFromParams = (~weight) =>
  switch weight {
  | #regular => style(~fontWeight=#400, ())
  | #medium => style(~fontWeight=#500, ())
  | #semibold => style(~fontWeight=#600, ())
  | #strong => style(~fontWeight=#700, ())
  }

let lineHeightFromParams = (~lineHeight, ~fontSize) =>
  switch lineHeight {
  | Some(lineHeight) => style(~lineHeight=lineHeight->Spaces.toPx, ())
  | None =>
    switch fontSize {
    | #tiny | #xxsmall | #xsmall => style(~lineHeight=Spaces.mediumPx, ())
    | #small | #normal => style(~lineHeight="18px", ())
    | #large => style(~lineHeight=Spaces.largePx, ())
    | #xlarge | #huge => style()
    }
  }

let alignFromParams = (~align) =>
  switch align {
  | #start => styles["alignLeft"]
  | #center => styles["alignCenter"]
  | #end => styles["alignRight"]
  }

let baseStyleFromParams = (~variation, ~underlined, ~maxLines, ~wrap, ~direction) =>
  arrayStyle([
    styles["root"],
    switch variation {
    | #normal => styles["normalView"]
    | #success => styles["successView"]
    | #negative => styles["negativeView"]
    | #subdued => styles["subduedView"]
    | #neutral => styles["neutralView"]
    | #primary => styles["primaryView"]
    | #secondary => styles["secondaryView"]
    | #placeholder => styles["placeholderView"]
    },
    underlined ? style(~textDecorationLine=#underline, ()) : style(),
    !wrap ? style(~whiteSpace=#nowrap, ()) : style(),
    switch maxLines {
    | Some(0 | 1) =>
      style(~overflow=#hidden, ~whiteSpace=#nowrap, ~textOverflow=#ellipsis, ~direction, ())
    | Some(qty) =>
      // NOTE - multi lines clamping doesn't work on Firefox
      style(
        ~overflow=#hidden,
        ~display=#"-webkit-box",
        ~\"WebkitBoxOrient"=#vertical,
        ~\"WebkitLineClamp"=Int.toString(qty),
        (),
      )
    | None => style()
    },
  ])

@react.component
let make = (
  ~children,
  ~align=#start,
  ~variation=#neutral,
  ~weight=#regular,
  ~size=#normal,
  ~lineHeight=?,
  ~underlined=false,
  ~wrap=true,
  ~maxLines=?,
  ~direction=#ltr,
  ~opacity=1.,
) => {
  let {?style, ?className} = StyleX.props([
    baseStyleFromParams(~variation, ~underlined, ~wrap, ~maxLines, ~direction),
    alignFromParams(~align),
    weightFromParams(~weight),
    lineHeightFromParams(~lineHeight, ~fontSize=size),
    style(~opacity, ~fontSize=size->FontSizes.toPx, ()),
  ])

  // TODO - add a native HTML `title` props when `maxLines`
  <span ?style ?className> children </span>
}

let make = React.memo(make)
