open StyleX

type alignment = [#start | #center | #end]
type fontWeight = [#regular | #medium | #strong]
type level = [#1 | #2 | #3 | #4 | #5 | #6]

@inline let fontTitleLevel1 = `normal bold 26px "Archivo"`
@inline let fontTitleLevel2 = `normal bold 22px "Archivo"`
@inline let fontTitleLevel3 = `normal normal 18px "Archivo"`
@inline let fontTitleLevel4 = `normal 500 16px "Archivo"` // TODO - weight to define
@inline let fontTitleLevel5 = `normal 500 14px "Archivo"`
@inline let fontTitleLevel6 = `normal 400 12px "Archivo"` // TODO - weight to define

let styles = StyleX.create({
  "h1": style(~font=fontTitleLevel1, ~color=Colors.neutralColor90, ()),
  "h2": style(~font=fontTitleLevel2, ~color=Colors.neutralColor90, ()),
  "h3": style(~font=fontTitleLevel3, ~color=Colors.neutralColor90, ()),
  "h4": style(~font=fontTitleLevel4, ~color=Colors.neutralColor90, ()),
  "h5": style(~font=fontTitleLevel5, ~color=Colors.neutralColor30, ()),
  "h6": style(~font=fontTitleLevel6, ~color=Colors.neutralColor90, ()),
  "alignLeft": style(~textAlign=#left, ()),
  "alignCenter": style(~textAlign=#center, ()),
  "alignRight": style(~textAlign=#right, ()),
})

let styleFromLevel = (~level) =>
  switch level {
  | #1 => styles["h1"]
  | #2 => styles["h2"]
  | #3 => styles["h3"]
  | #4 => styles["h4"]
  | #5 => styles["h5"]
  | #6 => styles["h6"]
  }

let styleFromAlign = (~align) =>
  switch align {
  | #center => styles["alignCenter"]
  | #end => styles["alignRight"]
  | #start => styles["alignLeft"]
  }

let styleFromWeight = (~weight) =>
  StyleX.arrayStyle([
    switch weight {
    | Some(#regular) => style(~fontWeight=#400, ())
    | Some(#medium) => style(~fontWeight=#500, ())
    | Some(#strong) => style(~fontWeight=#600, ())
    | None => style()
    },
  ])

let styleProps = (~level, ~align, ~weight, ~color) =>
  StyleX.props([
    styleFromLevel(~level),
    styleFromAlign(~align),
    styleFromWeight(~weight),
    style(~color?, ()),
  ])

@react.component
let make = (~children, ~level=#1, ~align=#start, ~weight=?, ~color=?) => {
  let {?style, ?className} = styleProps(~level, ~align, ~weight, ~color)

  switch level {
  | #1 => <h1 ?style ?className> children </h1>
  | #2 => <h2 ?style ?className> children </h2>
  | #3 => <h3 ?style ?className> children </h3>
  | #4 => <h4 ?style ?className> children </h4>
  | #5 => <h5 ?style ?className> children </h5>
  | #6 => <h6 ?style ?className> children </h6>
  }
}

let make = React.memo(make)
