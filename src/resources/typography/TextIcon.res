open StyleX

let styles = StyleX.create({
  "wrapper": style(~flex="1", ()),
  "view": style(
    ~display=#flex,
    ~flexDirection=#row,
    ~justifyContent=#"flex-start",
    ~alignItems=#center,
    (),
  ),
  "hoveredView": style(~backgroundColor=Colors.neutralColor05, ()),
  "iconView": style(~display=#flex, ~marginRight="8px", ()),
  "xxsmallView": style(~height="36px", ()),
  "xsmallView": style(~height="40px", ()),
  "smallView": style(~height="44px", ()),
  "normalView": style(~height="48px", ()),
  "mediumView": style(~height="52px", ()),
  "largeView": style(~height="60px", ()),
  "xlargeView": style(~height="68px", ()),
  "text": style(~font=`normal 600 15px "Archivo"`, ()),
  "lightText": style(~color=Colors.neutralColor20, ()),
  "grayText": style(~color=Colors.neutralColor100, ()),
  "darkText": style(~color=Colors.brandColor60, ()),
})

let viewStyleFromParams = (~size) =>
  switch size {
  | #xsmall => styles["xsmallView"]
  | #small => styles["smallView"]
  | #large => styles["largeView"]
  | #normal | _ => styles["normalView"]
  }

let textStyleFromParams = (~size, ~disabled, ~hovered) =>
  StyleX.arrayStyle([
    style(~fontSize=size->FontSizes.toPx, ()),
    switch (disabled, hovered) {
    | (true, _) => styles["lightText"]
    | (false, true) => styles["darkText"]
    | (false, _) => styles["grayText"]
    },
  ])

let iconColorFromParams = (~disabled, ~hovered) =>
  switch (disabled, hovered) {
  | (true, _) => Colors.neutralColor20
  | (false, true) => Colors.brandColor60
  | (false, _) => Colors.neutralColor100
  }

@react.component
let make = (~children, ~size=#normal, ~hideMargin=false, ~disabled=false, ~icon) => {
  let (ref, hovered) = Hover.use()

  <DivX ref style={StyleX.props([styles["wrapper"]])}>
    <DivX
      style={StyleX.props([styles["view"], !hideMargin ? viewStyleFromParams(~size) : style()])}>
      <DivX style={StyleX.props([styles["iconView"]])}>
        <Icon fill={iconColorFromParams(~disabled, ~hovered)} name=icon />
      </DivX>
      <SpanX
        style={StyleX.props([styles["text"], textStyleFromParams(~size, ~disabled, ~hovered)])}>
        children
      </SpanX>
    </DivX>
  </DivX>
}

let make = React.memo(make)
