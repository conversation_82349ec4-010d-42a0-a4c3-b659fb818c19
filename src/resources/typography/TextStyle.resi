@genType @react.component
let make: (
  ~children: React.element,
  ~align: [#start | #center | #end]=?,
  ~variation: [
    | #neutral
    | #success
    | #normal
    | #negative
    | #subdued
    | #primary
    | #secondary
    | #placeholder
  ]=?,
  ~weight: [#regular | #medium | #semibold | #strong]=?,
  ~size: FontSizes.t=?,
  ~lineHeight: Spaces.t=?,
  ~underlined: bool=?,
  ~wrap: bool=?,
  ~maxLines: int=?,
  ~direction: StyleX.direction=?,
  ~opacity: float=?,
) => React.element
