let window = WebAPI.window

@react.component
let make = (~label=?, ~disabled=false, ~cropValueAt=6, ~value) => {
  let (hoveredRef, hovered) = Hover.use()
  let (copy, setCopy) = React.useState(_ => None)
  let onRequestCopy = value => setCopy(_ => Some(value))

  React.useEffect1(() => {
    switch copy {
    | Some(text) =>
      let clipboard = window->WebAPI.Window.navigator->WebAPI.Clipboard.make
      clipboard->WebAPI.Clipboard.writeText(text)

      let timer = Js.Global.setTimeout(() => setCopy(_ => None), 1400)
      Some(() => Js.Global.clearTimeout(timer))
    | _ => None
    }
  }, [copy])

  <Touchable onPress={_ => onRequestCopy(value)} disabled>
    <div
      style={ReactDOM.Style.make(
        ~display="inline-flex",
        ~alignItems="center",
        ~columnGap="7px",
        ~flexWrap="wrap",
        (),
      )}
      ref={hoveredRef->ReactDOM.Ref.domRef}>
      {switch label {
      | None
      | Some("") => React.null
      | Some(label) => <TextStyle variation=#normal> {label->React.string} </TextStyle>
      }}
      {if cropValueAt > 0 {
        <Kbd hovered>
          {(Js.String.slice(~from=0, ~to_=cropValueAt, value) ++ "...")->React.string}
        </Kbd>
      } else {
        React.null
      }}
      <Offset top={-1.} left={-2.} width=16. height=16.>
        {switch copy {
        | None =>
          <Icon
            name=#clipboard
            fill={switch (hovered, disabled) {
            | (true, false) => Colors.brandColor60
            | _ => Colors.neutralColor50
            }}
            size=16.
          />
        | Some(_) => <Icon name=#tick_bold fill=Colors.brandColor60 size=16. />
        }}
      </Offset>
    </div>
  </Touchable>
}

let make = React.memo(make)
