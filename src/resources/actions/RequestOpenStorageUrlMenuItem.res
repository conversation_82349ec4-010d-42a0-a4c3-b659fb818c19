open Intl
open Request

type error = LinkingOpenUrlError | RequestError(error)

let failureErrorToString = (error, ~exportName) =>
  switch error {
  | LinkingOpenUrlError => t("An issue when downloading the file occurred with this navigator.")
  | RequestError(_) =>
    template(
      t("An issue when attempting downloading {{export}} occurred."),
      ~values={"export": t(exportName)},
      (),
    ) ++
    " " ++
    Request.serverErrorMessage
  }

let decodeAndMakeUrl = json =>
  Option.orElse(
    json->Json.decodeDict->Json.flatDecodeDictFieldString("url"),
    json
    ->Json.decodeDict
    ->Json.flatDecodeDictField("file", Json.decodeDict)
    ->Json.flatDecodeDictFieldString("url"),
  )->Option.map(Url.make)

@react.component
let make = (~text, ~textError=t("Download failed"), ~operableRequest, ~onSuccess, ~onFailure) => {
  let (loading, setLoading) = React.useState(() => false)

  let onChange = React.useCallback0(asyncResult => {
    setLoading(_ => asyncResult->AsyncResult.isBusy)
    switch asyncResult {
    | Done(Ok(json)) =>
      switch decodeAndMakeUrl(json) {
      | Some(fileUrl) =>
        fileUrl
        ->TriggerDownload.fromUrl
        ->Future.tapOk(_ => Some(fileUrl)->onSuccess)
        ->Future.tapError(_ => LinkingOpenUrlError->onFailure)
        ->ignore
      | None => None->onSuccess
      }
    | Done(Error(requestError)) => RequestError(requestError)->onFailure
    | _ => ()
    }->ignore
  })

  <>
    <SpinnerModal title=text opened=loading />
    <RequestMenuItem text textError operableRequest onChange />
  </>
}

let make = React.memo(make)
