open StyleX

type action =
  | Callback(unit => unit)
  | OpenLink(Navigation.to)

let styles = StyleX.create({
  "root": style(
    ~display=#flex,
    ~alignItems=#center,
    ~justifyContent=#center,
    ~width="40px",
    ~height="40px",
    ~border="1px solid " ++ Colors.neutralColor20,
    ~borderRadius="5px",
    ~boxSizing=#"border-box",
    ~\":hover"=style(~borderColor=Colors.neutralColor25, ()),
    (),
  ),
  "focused": style(
    ~borderColor=Colors.neutralColor30,
    ~\":hover"=style(~borderColor=Colors.neutralColor30, ()),
    (),
  ),
  "disabled": style(
    ~backgroundColor=Colors.neutralColor10,
    ~borderColor=Colors.neutralColor20,
    ~\":hover"=style(~borderColor=Colors.neutralColor20, ()),
    (),
  ),
  "statusLight": style(
    ~position=#absolute,
    ~right="0px",
    ~width="8px",
    ~height="8px",
    ~backgroundColor=Colors.brandColor50,
    ~outline="2px solid white",
    ~borderRadius="50%",
    (),
  ),
})
let styleProps = (~focused, ~disabled, ~bordered) =>
  StyleX.props([
    styles["root"],
    focused ? styles["focused"] : style(),
    disabled ? styles["disabled"] : style(),
    !bordered ? style(~border="none", ~width="20px", ()) : style(),
  ])
let iconStyleProps = (~pressed) =>
  StyleX.props([
    style(
      ~height="20px",
      ~position=#relative,
      ~transform=pressed ? "scale3d(0.9, 0.9, 1)" : "initial",
      (),
    ),
  ])
let iconStatusLightStyleProps = () => StyleX.props([styles["statusLight"]])

@react.component
let make = React.forwardRef((
  ~name: Icon.t,
  ~disabled=false,
  ~focused=false,
  ~bordered=true,
  ~statusLight=false,
  ~tooltipText=?,
  ~ariaProps=?,
  ~action,
  ref,
) => {
  let (ref, hovered) = Hover.use(~ref=?ref->Js.Nullable.toOption, ())
  let (pressed, setPressed) = React.useState(() => false)

  let buttonProps = {
    ReactAria.Button.elementType: switch action {
    | Callback(_) => #div
    | OpenLink(_) => #a
    },
    onPressStart: _ => setPressed(_ => true),
    onPressEnd: _ => setPressed(_ => false),
  }

  let {?style, ?className} = styleProps(~focused, ~disabled, ~bordered)
  let {style: ?iconStyle} = iconStyleProps(~pressed)
  let {
    style: ?iconStatusLightStyle,
    className: ?iconStatusLightClassName,
  } = iconStatusLightStyleProps()

  let fill = (!focused && !hovered) || disabled ? Colors.neutralColor50 : Colors.neutralColor90
  let ariaProps = ReactAria.mergeProps3(
    {ReactAria.Button.\"aria-label": (name :> string)},
    ariaProps,
    buttonProps,
  )

  <Tooltip
    content={<Tooltip.Span text={tooltipText->Option.getWithDefault("")} />}
    disabled={tooltipText->Option.isNone}
    arrowed=false
    placement=#"top start"
    offset=3.
    closeDelay=0>
    {switch action {
    | Callback(onPress) =>
      <Touchable disabled ref ariaProps onPress={_ => onPress()}>
        <div ?style ?className>
          <div style=?iconStyle>
            <AnimatedRender displayed=statusLight animation=#fadeBubbleTranslation duration=500>
              <div style=?iconStatusLightStyle className=?iconStatusLightClassName />
            </AnimatedRender>
            <Icon name fill />
          </div>
        </div>
      </Touchable>
    | OpenLink(to) =>
      let ref = ref->ReactDOM.Ref.domRef
      <Navigation.Link to disabled ariaProps>
        <div ref ?style ?className>
          <div style=?iconStyle>
            <AnimatedRender displayed=statusLight animation=#fadeBubbleTranslation duration=500>
              <div style=?iconStatusLightStyle className=?iconStatusLightClassName />
            </AnimatedRender>
            <Icon name fill />
          </div>
        </div>
      </Navigation.Link>
    }}
  </Tooltip>
})
