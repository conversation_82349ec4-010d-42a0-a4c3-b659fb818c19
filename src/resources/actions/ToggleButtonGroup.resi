type size = [#large | #medium | #small | #xlarge]
type variant = [#ghost | #outline | #solid | #subtle]
type tone = [#danger | #success]

type item = {
  value: ReactStately.key,
  label: string,
  tone: tone,
}

@react.component
let make: (
  ~size: size=?,
  ~variant: variant=?,
  ~grow: bool=?,
  ~selectionMode: [#multiple | #single]=?,
  ~items: array<item>,
  ~value: array<ReactStately.key>,
  ~onChange: array<ReactStately.key> => unit,
) => React.element
