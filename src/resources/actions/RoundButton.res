open StyleX

let styles = StyleX.create({
  "view": style(
    ~display=#flex,
    ~flexDirection=#column,
    ~justifyContent=#center,
    ~alignItems=#center,
    ~borderRadius="50px",
    ~border="1px solid " ++ Colors.neutralColor20,
    ~backgroundImage="linear-gradient(0deg, rgba(243,243,247,1) 0%, rgba(243,243,247,1) 50%, rgba(255,255,255,1) 100%)",
    (),
  ),
  "smallView": style(~width="35px", ~height="35px", ()),
  "normalView": style(~width="40px", ~height="40px", ()),
  "mediumView": style(~width="56px", ~height="56px", ()),
  "viewDisabled": style(~borderColor=Colors.neutralColor15, ()),
  "viewFocused": style(~borderColor=Colors.neutralColor30, ()),
  "viewHovered": style(~borderColor=Colors.neutralColor25, ()),
})

let viewStyleFromParams = (~size, ~disabled, ~focused, ~hovered) =>
  StyleX.arrayStyle([
    switch size {
    | #small => styles["smallView"]
    | #medium => styles["mediumView"]
    | #normal => styles["normalView"]
    | _ => styles["smallView"]
    },
    switch (disabled, focused, hovered) {
    | (true, _, _) => styles["viewDisabled"]
    | (_, true, _) => styles["viewFocused"]
    | (_, _, true) => styles["viewHovered"]
    | _ => style()
    },
  ])

@react.component
let make = React.forwardRef((
  ~ariaProps=?,
  ~icon,
  ~size=#normal,
  ~disabled=false,
  ~focused=false,
  ~onPress,
  ref,
) => {
  let (hoveredRef, hovered) = Hover.use(~ref=?ref->Js.Nullable.toOption, ())

  let iconColor = switch (disabled, hovered) {
  | (true, _) => Colors.neutralColor30
  | (_, true) => Colors.brandColor50
  | _ => Colors.neutralColor70
  }

  <Touchable ?ariaProps disabled onPress>
    <DivX
      ref=hoveredRef
      style={StyleX.props([
        styles["view"],
        viewStyleFromParams(~size, ~disabled, ~focused, ~hovered),
      ])}>
      <Icon name=icon fill=iconColor />
    </DivX>
  </Touchable>
})

let make = React.memo(make)
