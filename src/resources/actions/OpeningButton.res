open StyleX

let styles = StyleX.create({
  "root": style(
    ~display=#flex,
    ~alignItems=#center,
    ~gap=Spaces.xsmallPx,
    ~height="40px",
    ~padding=Spaces.smallPx,
    ~border="1px solid " ++ Colors.transparent,
    ~borderRadius="5px",
    ~boxSizing=#"border-box",
    ~\":hover"=style(
      ~backgroundColor=Colors.neutralColor05,
      ~borderColor=Colors.neutralColor25,
      (),
    ),
    (),
  ),
  "active": style(
    ~backgroundColor=Colors.neutralColor00,
    ~borderColor=Colors.neutralColor30,
    ~\":hover"=style(
      ~backgroundColor=Colors.neutralColor00,
      ~borderColor=Colors.neutralColor30,
      (),
    ),
    (),
  ),
  "disabled": style(
    ~borderColor=Colors.transparent,
    ~\":hover"=style(~borderColor=Colors.transparent, ()),
    (),
  ),
  "iconWrapper": style(~marginTop="1px", ~marginRight="-1px", ()),
})
let styleProps = (~disabled, ~active) =>
  StyleX.props([
    styles["root"],
    active ? styles["active"] : style(),
    disabled ? styles["disabled"] : style(),
  ])
let iconWrapperStyleProps = () => StyleX.props([styles["iconWrapper"]])

@react.component
let make = React.forwardRef((
  ~children,
  ~ariaProps=?,
  ~opened,
  ~disabled=false,
  ~onPress=?,
  ref,
) => {
  let ref = Js.Nullable.toOption(ref)
  let (ref, hovered) = Hover.use(~ref?, ())

  let {?style, ?className} = styleProps(~disabled, ~active=opened)
  let {style: ?iconWrapperStyle, className: ?iconWrapperClassName} = iconWrapperStyleProps()
  let iconColor = if disabled {
    Colors.neutralColor25
  } else if opened || hovered {
    Colors.neutralColor90
  } else {
    Colors.neutralColor50
  }

  <Touchable
    ref ?ariaProps disabled style={StyleX.style(~marginLeft=`-${Spaces.smallPx}`, ())} ?onPress>
    <div ?style ?className>
      {children}
      <div style=?iconWrapperStyle className=?iconWrapperClassName>
        <Icon name={opened ? #arrow_up_light : #arrow_down_light} fill=iconColor size=14. />
      </div>
    </div>
  </Touchable>
})

let make = React.memo(make)
