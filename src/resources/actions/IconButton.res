open StyleX

let styles = StyleX.create({
  "view": style(
    ~display=#flex,
    ~justifyContent=#center,
    ~alignItems=#center,
    ~borderRadius="50px",
    (),
  ),
  "xxsmallView": style(~width="20px", ~height="26px", ()),
  "xsmallView": style(~width="26px", ~height="26px", ()),
  "smallView": style(~width="35px", ~height="35px", ()),
  "normalView": style(~width="40px", ~height="40px", ()),
})

let viewStyleFromParams = (~marginSize as size, ~borderedColor, ~hoveredColor) => [
  switch size {
  | Some(#xxsmall) => styles["xxsmallView"]
  | Some(#xsmall) => styles["xsmallView"]
  | Some(#small) => styles["smallView"]
  | Some(#normal) => styles["normalView"]
  | _ => style()
  },
  switch borderedColor {
  | Some(color) =>
    style(~borderWidth="1px", ~borderStyle=#solid, ~borderColor=color, ~borderRadius="5px", ())
  | None => style()
  },
  switch hoveredColor {
  | Some(color) => style(~borderColor=color, ())
  | None => style(~borderColor=Colors.neutralColor20, ())
  },
]

type iconName = Icon.t

@react.component
let make = React.forwardRef((
  ~name: iconName,
  ~size=?,
  ~marginSize=?,
  ~color=Colors.neutralColor50,
  ~hoveredColor=?,
  ~borderedColor=?,
  ~bold=false,
  ~disabled=false,
  ~ariaProps=?,
  ~onPress,
  ref,
) => {
  let (ref, hovered) = Hover.use(~ref=?ref->Js.Nullable.toOption, ())

  let fill = switch (hovered, hoveredColor) {
  | (true, Some(hoveredColor)) if !disabled && borderedColor->Option.isNone => hoveredColor
  | _ => color
  }
  let hoveredColor = hovered ? hoveredColor : None
  let ariaProps = ReactAria.mergeProps2(
    {ReactAria.Button.\"aria-label": (name :> string)},
    ariaProps,
  )

  <Touchable ref ariaProps disabled excludeFromTabOrder=true onPress>
    <DivX
      style={StyleX.props(
        [styles["view"]]->Array.concat(
          viewStyleFromParams(~marginSize, ~hoveredColor, ~borderedColor),
        ),
      )}>
      <Icon name ?size fill stroke=?{bold ? Some(fill) : None} />
    </DivX>
  </Touchable>
})

let make = React.memo(make)
