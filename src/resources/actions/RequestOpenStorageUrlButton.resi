type error = LinkingOpenUrlError | RequestError(Request.error) | MissingFileUrl
let failureErrorToString: (error, ~exportName: string) => string

type variation = [#normal | #compact]

@react.component
let make: (
  ~variation: variation=?,
  ~statusLight: bool=?,
  ~text: string,
  ~textError: string=?,
  ~operableRequest: result<unit => Future.t<result<Json.t, Request.error>>, string>,
  ~onFailure: error => unit,
  ~onSuccess: unit => unit=?,
) => React.element
