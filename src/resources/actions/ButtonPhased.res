open StyleX

let styles = StyleX.create({
  "container": style(
    ~display=#flex,
    ~flexDirection=#column,
    ~flex="1",
    ~alignSelf=#"flex-start",
    ~minHeight="40px",
    ~paddingInline=Spaces.normalPx,
    ~paddingBlock=Spaces.smallPx,
    ~justifyContent=#center,
    ~borderRadius="5px",
    ~border="1px solid " ++ Colors.transparent,
    (),
  ),
  "containerInteracted": style(~backgroundColor=Colors.neutralColor05, ()),
  "containerHovered": style(
    ~border="1px solid " ++ Colors.neutralColor20,
    ~backgroundColor=Colors.neutralColor15,
    ~zIndex=10,
    (),
  ),
  "containerFocused": style(
    ~border="1px solid " ++ Colors.neutralColor25,
    ~backgroundColor=Colors.neutralColor15,
    (),
  ),
  "containerErrored": style(~border="1px solid " ++ Colors.dangerColor50, ()),
  "text": style(
    ~textOverflow=#ellipsis,
    ~whiteSpace=#nowrap,
    ~overflow=#hidden,
    ~textAlign=#left,
    ~direction=#rtl,
    (),
  ),
})

let styleProps = (~disabled, ~hovered, ~focused, ~errored) =>
  StyleX.props([
    styles["container"],
    switch (disabled, hovered, focused) {
    | (false, true, true) => styles["containerInteracted"]
    | _ => style()
    },
    switch (disabled, hovered, focused, errored) {
    | (false, _, _, true) => styles["containerErrored"]
    | (false, _, true, _) => styles["containerFocused"]
    | (false, true, _, _) => styles["containerHovered"]
    | _ => style()
    },
  ])

let textStyleProps = (~compact) => StyleX.props([compact ? styles["text"] : style()])

type variation = [#normal | #neutral | #subdued | #important]

module OptionalTouchable = {
  @react.component
  let make = (~children, ~disabled, ~onPress=?) => {
    switch onPress {
    | Some(onPress) => <Touchable disabled onPress={_ => onPress()}> children </Touchable>
    | None => children
    }
  }
}

@react.component
let make = React.forwardRef((
  ~children,
  ~variation: variation=#normal,
  ~hovered,
  ~compact=false,
  ~focused=false,
  ~errored=false,
  ~disabled=false,
  ~onPress=?,
  ref,
) => {
  let ref = ref->Js.Nullable.toOption->Option.map(ReactDOM.Ref.domRef)

  let {?style, ?className} = styleProps(~disabled, ~hovered, ~focused, ~errored)
  let {style: ?textStyle, className: ?textClassName} = textStyleProps(~compact)

  <OptionalTouchable disabled ?onPress>
    <div ?ref ?style ?className>
      <span style=?textStyle className=?textClassName>
        <TextStyle
          wrap={!compact}
          variation={switch variation {
          | #normal | #important => #neutral
          | #neutral => #normal
          | #subdued => #subdued
          }}
          weight={switch variation {
          | #normal | #neutral | #subdued => #regular
          | #important => #medium
          }}
          size={compact ? #xsmall : #normal}>
          children
        </TextStyle>
      </span>
    </div>
  </OptionalTouchable>
})

let make = React.memo(make)
