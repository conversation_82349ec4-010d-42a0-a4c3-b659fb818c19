open Intl
open Request

type error = LinkingOpenUrlError | RequestError(error) | MissingFileUrl
type variation = [#normal | #compact]

module RequestButton = {
  @react.component
  let make = (
    ~variation=#normal,
    ~statusLight=?,
    ~text,
    ~textError=?,
    ~operableRequest,
    ~onChange,
  ) => {
    let (requestResult, setRequestResult) = React.useState(() => AsyncData.NotAsked)

    ReactUpdateEffect.use1(() => {
      onChange(requestResult)
      None
    }, [requestResult])

    let onPress = React.useCallback1(() =>
      switch operableRequest {
      | Ok(request) =>
        switch requestResult {
        | Done(_) | NotAsked =>
          setRequestResult(_ => Loading)
          request()->Future.get(result => setRequestResult(_ => Done(result)))
        | Loading | Reloading(_) => ()
        }
      | Error(_) => ()
      }
    , [operableRequest])

    let text = switch (requestResult, textError) {
    | (Done(Error(_)), Some(textError)) => textError
    | _ => text
    }

    <Tooltip
      content={<Tooltip.Span
        text={switch operableRequest {
        | Ok(_) => t("Cannot run request")
        | Error(errorMessage) => errorMessage
        }}
      />}
      placement=#start
      disabled={operableRequest->Result.isOk}
      delay=75>
      {switch variation {
      | #normal =>
        <Button
          disabled={requestResult->AsyncData.isBusy || operableRequest->Result.isError}
          variation={switch requestResult {
          | Done(Error(_)) => #danger
          | _ => #neutral
          }}
          size=#normal
          onPress={_ => onPress()}>
          {text->React.string}
        </Button>
      | #compact =>
        // REVIEW - yet to decide on error visual to integrate
        <ShortIconButton
          name=#export_download
          ?statusLight
          tooltipText=text
          disabled={requestResult->AsyncData.isBusy || operableRequest->Result.isError}
          action={Callback(onPress)}
        />
      }}
    </Tooltip>
  }
}

let failureErrorToString = (error, ~exportName) =>
  switch error {
  | LinkingOpenUrlError => t("An issue when downloading the file occurred with this navigator.")
  | RequestError(_) =>
    template(
      t("An issue occurred when attempting to download the {{export}} file."),
      ~values={"export": t(exportName)},
      (),
    ) ++
    " " ++
    Request.serverErrorMessage
  | MissingFileUrl =>
    template(
      t("An issue occurred while generating the URL for the {{export}} file."),
      ~values={"export": t(exportName)},
      (),
    )
  }

let decodeAndMakeUrl = json =>
  Option.orElse(
    json->Json.decodeDict->Json.flatDecodeDictFieldString("url"),
    json
    ->Json.decodeDict
    ->Json.flatDecodeDictField("file", Json.decodeDict)
    ->Json.flatDecodeDictFieldString("url"),
  )->Option.map(Url.make)

@react.component
let make = (
  ~variation=#normal,
  ~statusLight=false,
  ~text,
  ~textError=t("Download failed"),
  ~operableRequest,
  ~onFailure,
  ~onSuccess=?,
) => {
  let (loading, setLoading) = React.useState(() => false)

  let onChange = React.useCallback0(asyncResult => {
    setLoading(_ => asyncResult->AsyncResult.isBusy)
    switch asyncResult {
    | Done(Ok(json)) =>
      switch decodeAndMakeUrl(json) {
      | Some(fileUrl) =>
        fileUrl
        ->TriggerDownload.fromUrl
        ->Future.get(result =>
          switch result {
          | Ok(_) => onSuccess->Option.forEach(fn => fn())
          | Error(_) => onFailure(LinkingOpenUrlError)
          }
        )
        ->ignore
      | None => onFailure(MissingFileUrl)
      }
    | Done(Error(requestError)) => RequestError(requestError)->onFailure
    | _ => ()
    }->ignore
  })

  <>
    <SpinnerModal title=text opened=loading />
    <RequestButton variation statusLight text textError operableRequest onChange />
  </>
}

let make = React.memo(make)
