open StyleX

type size = [#small | #medium | #large | #xlarge]
type variant = [#solid | #ghost | #outline | #subtle]
type tone = [#success | #danger]
type state = [#default | #hovered | #pressed | #disabled]

type toneColorSet = {
  default: string,
  hovered: string,
  pressed: string,
  disabled: string,
}

type variantToneSet = {
  solid: toneColorSet,
  ghost: toneColorSet,
  outlineOrSubtle: toneColorSet,
}

let textSolidVariant = CanaryColors.textNeutralInverseAccent

let backgroundVariantSuccessToneSet = {
  solid: {
    default: CanaryColors.fillSuccessDefault,
    hovered: CanaryColors.fillSuccessHover,
    pressed: CanaryColors.fillSuccessAccent,
    disabled: CanaryColors.fillSuccessDisabled,
  },
  ghost: {
    default: CanaryColors.fillSuccessWeak,
    hovered: CanaryColors.fillSuccessWeakHover,
    pressed: CanaryColors.fillSuccessPressed,
    disabled: CanaryColors.fillSuccessDisabled,
  },
  outlineOrSubtle: {
    default: "transparent",
    hovered: CanaryColors.fillSuccessWeak,
    pressed: CanaryColors.fillSuccessPressed,
    disabled: "transparent",
  },
}
let textVariantSuccessToneSet = {
  default: CanaryColors.textSuccessDefault,
  hovered: CanaryColors.textSuccessHover,
  pressed: CanaryColors.textSuccessHover,
  disabled: CanaryColors.textSuccessDisabled,
}
let borderVariantSuccessToneSet = {
  default: CanaryColors.borderSuccessDefault,
  hovered: CanaryColors.borderSuccessHover,
  pressed: CanaryColors.borderSuccessPressed,
  disabled: CanaryColors.borderSuccessDisabled,
}

let backgroundVariantDangerToneSet = {
  solid: {
    default: CanaryColors.fillDangerDefault,
    hovered: CanaryColors.fillDangerHover,
    pressed: CanaryColors.fillDangerAccent,
    disabled: CanaryColors.fillDangerDisabled,
  },
  ghost: {
    default: CanaryColors.fillDangerWeak,
    hovered: CanaryColors.fillDangerWeakHover,
    pressed: CanaryColors.fillDangerPressed,
    disabled: CanaryColors.fillDangerDisabled,
  },
  outlineOrSubtle: {
    default: "transparent",
    hovered: CanaryColors.fillDangerWeak,
    pressed: CanaryColors.fillDangerPressed,
    disabled: "transparent",
  },
}
let textVariantDangerToneSet = {
  default: CanaryColors.textDangerDefault,
  hovered: CanaryColors.textDangerHover,
  pressed: CanaryColors.textDangerHover, // REVIEW - textDangerPressed ?
  disabled: CanaryColors.textDangerDisabled,
}
let borderVariantDangerToneSet = {
  default: CanaryColors.borderDangerDefault,
  hovered: CanaryColors.borderDangerHover,
  pressed: CanaryColors.borderDangerDefault,
  disabled: CanaryColors.borderDangerDisabled,
}

let styles = StyleX.create({
  "root": style(
    ~display=#"inline-flex",
    ~alignItems=#center,
    ~justifyContent=#center,
    ~borderRadius=Spaces.xsmallPx,
    ~boxSizing=#"border-box",
    ~font=`normal 700 12px "Archivo"`,
    ~textAlign=#center,
    ~cursor=#pointer,
    ~transition="130ms cubic-bezier(0.18, 0.89, 0.32, 1.28)",
    (),
  ),
  "grow": style(~flex="1", ()),
  "bordered": style(~border="1px solid", ()),
  "sizeSmall": style(
    ~maxHeight="32px",
    ~minWidth="60px",
    ~padding="10px",
    ~gap="10px",
    ~fontSize="12px",
    (),
  ),
  "sizeMedium": style(
    ~maxHeight="40px",
    ~minWidth="80px",
    ~padding="12px",
    ~gap="12px",
    ~fontSize="14px",
    (),
  ),
  "sizeLarge": style(
    ~maxHeight="48px",
    ~minWidth="90px",
    ~padding="16px",
    ~gap="16px",
    ~fontSize="16px",
    (),
  ),
  "sizeXLarge": style(
    ~maxHeight="60px",
    ~minWidth="100px",
    ~padding="20px",
    ~gap="20px",
    ~fontSize="20px",
    (),
  ),
  /* ---------- tone Success ---------- */
  "successSolidDefault": style(
    ~backgroundColor=backgroundVariantSuccessToneSet.solid.default,
    ~color=textSolidVariant,
    (),
  ),
  "successSolidHovered": style(
    ~backgroundColor=backgroundVariantSuccessToneSet.solid.hovered,
    ~color=textSolidVariant,
    (),
  ),
  "successSolidPressed": style(
    ~backgroundColor=backgroundVariantSuccessToneSet.solid.pressed,
    ~color=textSolidVariant,
    (),
  ),
  "successSolidDisabled": style(
    ~backgroundColor=backgroundVariantSuccessToneSet.solid.disabled,
    ~color=textVariantSuccessToneSet.disabled,
    (),
  ),
  "successGhostDefault": style(
    ~backgroundColor=backgroundVariantSuccessToneSet.ghost.default,
    ~color=textVariantSuccessToneSet.default,
    (),
  ),
  "successGhostHovered": style(
    ~backgroundColor=backgroundVariantSuccessToneSet.ghost.hovered,
    ~color=textVariantSuccessToneSet.hovered,
    (),
  ),
  "successGhostPressed": style(
    ~backgroundColor=backgroundVariantSuccessToneSet.ghost.pressed,
    ~color=textVariantSuccessToneSet.pressed,
    (),
  ),
  "successGhostDisabled": style(
    ~backgroundColor=backgroundVariantSuccessToneSet.ghost.disabled,
    ~color=textVariantSuccessToneSet.disabled,
    (),
  ),
  "successOutlineOrSubtleDefault": style(
    ~backgroundColor=backgroundVariantSuccessToneSet.outlineOrSubtle.default,
    ~borderColor=borderVariantSuccessToneSet.default,
    ~color=textVariantSuccessToneSet.default,
    (),
  ),
  "successOutlineOrSubtleHovered": style(
    ~backgroundColor=backgroundVariantSuccessToneSet.outlineOrSubtle.hovered,
    ~borderColor=borderVariantSuccessToneSet.hovered,
    ~color=textVariantSuccessToneSet.hovered,
    (),
  ),
  "successOutlineOrSubtlePressed": style(
    ~backgroundColor=backgroundVariantSuccessToneSet.outlineOrSubtle.pressed,
    ~borderColor=borderVariantSuccessToneSet.pressed,
    ~color=textVariantSuccessToneSet.pressed,
    (),
  ),
  "successOutlineOrSubtleDisabled": style(
    ~backgroundColor=backgroundVariantSuccessToneSet.outlineOrSubtle.disabled,
    ~borderColor=borderVariantSuccessToneSet.disabled,
    ~color=textVariantSuccessToneSet.disabled,
    (),
  ),
  /* ---------- tone Danger ---------- */
  "dangerSolidDefault": style(
    ~backgroundColor=backgroundVariantDangerToneSet.solid.default,
    ~color=textSolidVariant,
    (),
  ),
  "dangerSolidHovered": style(
    ~backgroundColor=backgroundVariantDangerToneSet.solid.hovered,
    ~color=textSolidVariant,
    (),
  ),
  "dangerSolidPressed": style(
    ~backgroundColor=backgroundVariantDangerToneSet.solid.pressed,
    ~color=textSolidVariant,
    (),
  ),
  "dangerSolidDisabled": style(
    ~backgroundColor=backgroundVariantDangerToneSet.solid.disabled,
    ~color=textVariantDangerToneSet.disabled,
    (),
  ),
  "dangerGhostDefault": style(
    ~backgroundColor=backgroundVariantDangerToneSet.ghost.default,
    ~color=textVariantDangerToneSet.default,
    (),
  ),
  "dangerGhostHovered": style(
    ~backgroundColor=backgroundVariantDangerToneSet.ghost.hovered,
    ~color=textVariantDangerToneSet.hovered,
    (),
  ),
  "dangerGhostPressed": style(
    ~backgroundColor=backgroundVariantDangerToneSet.ghost.pressed,
    ~color=textVariantDangerToneSet.pressed,
    (),
  ),
  "dangerGhostDisabled": style(
    ~backgroundColor=backgroundVariantDangerToneSet.ghost.disabled,
    ~color=textVariantDangerToneSet.disabled,
    (),
  ),
  "dangerOutlineOrSubtleDefault": style(
    ~backgroundColor=backgroundVariantDangerToneSet.outlineOrSubtle.default,
    ~borderColor=borderVariantDangerToneSet.default,
    ~color=textVariantDangerToneSet.default,
    (),
  ),
  "dangerOutlineOrSubtleHovered": style(
    ~backgroundColor=backgroundVariantDangerToneSet.outlineOrSubtle.hovered,
    ~borderColor=borderVariantDangerToneSet.hovered,
    ~color=textVariantDangerToneSet.hovered,
    (),
  ),
  "dangerOutlineOrSubtlePressed": style(
    ~backgroundColor=backgroundVariantDangerToneSet.outlineOrSubtle.pressed,
    ~borderColor=borderVariantDangerToneSet.pressed,
    ~color=textVariantDangerToneSet.pressed,
    (),
  ),
  "dangerOutlineOrSubtleDisabled": style(
    ~backgroundColor=backgroundVariantDangerToneSet.outlineOrSubtle.disabled,
    ~borderColor=borderVariantDangerToneSet.disabled,
    ~color=textVariantDangerToneSet.disabled,
    (),
  ),
})

let styleFor = (~tone, ~variant, ~state) =>
  switch (tone, variant, state) {
  /* ---------- tone Success ---------- */
  | (#success, #solid, #default) => styles["successSolidDefault"]
  | (#success, #solid, #hovered) => styles["successSolidHovered"]
  | (#success, #solid, #pressed) => styles["successSolidPressed"]
  | (#success, #solid, #disabled) => styles["successSolidDisabled"]

  | (#success, #ghost, #default) => styles["successGhostDefault"]
  | (#success, #ghost, #hovered) => styles["successGhostHovered"]
  | (#success, #ghost, #pressed) => styles["successGhostPressed"]
  | (#success, #ghost, #disabled) => styles["successGhostDisabled"]

  | (#success, #outline | #subtle, #default) => styles["successOutlineOrSubtleDefault"]
  | (#success, #outline | #subtle, #hovered) => styles["successOutlineOrSubtleHovered"]
  | (#success, #outline | #subtle, #pressed) => styles["successOutlineOrSubtlePressed"]
  | (#success, #outline | #subtle, #disabled) => styles["successOutlineOrSubtleDisabled"]

  /* ---------- tone Danger ---------- */
  | (#danger, #solid, #default) => styles["dangerSolidDefault"]
  | (#danger, #solid, #hovered) => styles["dangerSolidHovered"]
  | (#danger, #solid, #pressed) => styles["dangerSolidPressed"]
  | (#danger, #solid, #disabled) => styles["dangerSolidDisabled"]

  | (#danger, #ghost, #default) => styles["dangerGhostDefault"]
  | (#danger, #ghost, #hovered) => styles["dangerGhostHovered"]
  | (#danger, #ghost, #pressed) => styles["dangerGhostPressed"]
  | (#danger, #ghost, #disabled) => styles["dangerGhostDisabled"]

  | (#danger, #outline | #subtle, #default) => styles["dangerOutlineOrSubtleDefault"]
  | (#danger, #outline | #subtle, #hovered) => styles["dangerOutlineOrSubtleHovered"]
  | (#danger, #outline | #subtle, #pressed) => styles["dangerOutlineOrSubtlePressed"]
  | (#danger, #outline | #subtle, #disabled) => styles["dangerOutlineOrSubtleDisabled"]
  }

let styleProps = (~size: size, ~tone: tone, ~variant: variant, ~state: state, ~grow) =>
  StyleX.props([
    styles["root"],
    grow ? styles["grow"] : style(),
    switch size {
    | #small => styles["sizeSmall"]
    | #medium => styles["sizeMedium"]
    | #large => styles["sizeLarge"]
    | #xlarge => styles["sizeXLarge"]
    },
    styleFor(~tone, ~variant, ~state),
    variant === #outline ? styles["bordered"] : style(),
  ])

@react.component
let make = React.forwardRef((
  ~children,
  ~variant=#solid,
  ~tone=#success,
  ~size=#medium,
  ~disabled=false,
  ~grow=false,
  ~excludeFromTabOrder=?,
  ~onPress,
  ref,
) => {
  let (ref, hovered) = Hover.use(~ref=?ref->Js.Nullable.toOption, ())

  let props = {
    ReactAria.Button.elementType: #div,
    disabled,
    ?excludeFromTabOrder,
    onPress,
  }
  let {buttonProps, pressed} = ReactAria.Button.use(~props, ())

  let state = if disabled {
    #disabled
  } else if pressed {
    #pressed
  } else if hovered {
    #hovered
  } else {
    #default
  }

  let {?style, ?className} = styleProps(~variant, ~tone, ~size, ~state, ~grow)

  <ReactAria.Spread props=buttonProps>
    <div ?style ?className ref={ref->ReactDOM.Ref.domRef}> children </div>
  </ReactAria.Spread>
})

let make = React.memo(make)
