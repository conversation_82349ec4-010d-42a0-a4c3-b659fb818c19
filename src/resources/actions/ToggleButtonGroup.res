open StyleX

type size = [#small | #medium | #large | #xlarge]
type variant = [#solid | #ghost | #outline | #subtle]
type tone = [#success | #danger]
type state = [#default | #hovered | #pressed | #disabled]

type item = {
  value: ReactStately.key,
  label: string,
  tone: tone,
}

type toneColorSet = {
  default: string,
  hovered: string,
  pressed: string,
  disabled: string,
}

type variantToneSet = {
  solid: toneColorSet,
  ghost: toneColorSet,
  outline: toneColorSet,
  subtle: toneColorSet,
}

let textSolidVariant = CanaryColors.textNeutralInverseAccent

let backgroundVariantSuccessToneSet = {
  solid: {
    default: CanaryColors.fillSuccessDefault,
    hovered: CanaryColors.Primitive.white,
    pressed: CanaryColors.fillSuccessAccent,
    disabled: CanaryColors.fillSuccessAccent,
  },
  ghost: {
    default: CanaryColors.fillSuccessWeak,
    hovered: CanaryColors.fillSuccessWeakHover,
    pressed: CanaryColors.fillSuccessPressed,
    disabled: CanaryColors.fillSuccessWeak,
  },
  outline: {
    default: CanaryColors.Primitive.white,
    hovered: CanaryColors.fillSuccessDefault,
    pressed: CanaryColors.fillSuccessWeak,
    disabled: CanaryColors.fillSuccessWeak,
  },
  subtle: {
    default: "transparent",
    hovered: CanaryColors.fillSuccessDefault,
    pressed: "transparent",
    disabled: "transparent",
  },
}
let textVariantSuccessToneSet = {
  default: CanaryColors.textSuccessDefault,
  hovered: CanaryColors.textSuccessHover,
  pressed: CanaryColors.textSuccessHover,
  disabled: CanaryColors.textSuccessDisabled,
}
let borderVariantSuccessToneSet = {
  default: CanaryColors.borderSuccessDefault,
  hovered: CanaryColors.borderSuccessHover,
  pressed: CanaryColors.borderSuccessHover,
  disabled: "transparent",
}

let backgroundVariantDangerToneSet = {
  solid: {
    default: CanaryColors.fillDangerDefault,
    hovered: CanaryColors.Primitive.white,
    pressed: CanaryColors.fillDangerAccent,
    disabled: CanaryColors.fillDangerAccent,
  },
  ghost: {
    default: CanaryColors.fillDangerWeak,
    hovered: CanaryColors.fillDangerWeakHover,
    pressed: CanaryColors.fillDangerPressed,
    disabled: CanaryColors.fillDangerWeak,
  },
  outline: {
    default: CanaryColors.Primitive.white,
    hovered: CanaryColors.fillDangerDefault,
    pressed: CanaryColors.fillDangerWeak,
    disabled: CanaryColors.fillDangerWeak,
  },
  subtle: {
    default: "transparent",
    hovered: CanaryColors.fillDangerDefault,
    pressed: "transparent",
    disabled: "transparent",
  },
}
let textVariantDangerToneSet = {
  default: CanaryColors.textDangerDefault,
  hovered: CanaryColors.textDangerHover,
  pressed: CanaryColors.textDangerHover,
  disabled: CanaryColors.textDangerDisabled,
}
let borderVariantDangerToneSet = {
  default: CanaryColors.borderDangerDefault,
  hovered: CanaryColors.borderDangerHover,
  pressed: CanaryColors.borderDangerHover,
  disabled: "transparent",
}

let styles = StyleX.create({
  "root": style(~display=#flex, ~alignItems=#center, ()),
  "grow": style(~flex="1", ()),
  "sizeSmall": style(~gap="6px", ()),
  "sizeMedium": style(~gap="8px", ()),
  "sizeLarge": style(~gap="12px", ()),
  "sizeXLarge": style(~gap="16px", ()),
  "ToggleButtonGroupItem_root": style(
    ~position=#relative,
    ~display=#flex,
    ~alignItems=#center,
    ~justifyContent=#center,
    ~borderRadius=Spaces.xsmallPx,
    ~border="1px solid transparent",
    ~outline="1px solid transparent",
    ~boxSizing=#"border-box",
    ~font=`normal 700 12px "Archivo"`,
    ~textAlign=#center,
    ~cursor=#pointer,
    ~transition="130ms all cubic-bezier(0.18, 0.89, 0.32, 1.28)",
    (),
  ),
  "ToggleButtonGroupItem_sizeSmall": style(
    ~maxHeight="32px",
    ~minWidth="60px",
    ~padding="10px",
    ~gap="10px",
    ~fontSize="12px",
    (),
  ),
  "ToggleButtonGroupItem_sizeMedium": style(
    ~maxHeight="40px",
    ~minWidth="80px",
    ~padding="12px",
    ~gap="12px",
    ~fontSize="14px",
    (),
  ),
  "ToggleButtonGroupItem_sizeLarge": style(
    ~maxHeight="48px",
    ~minWidth="90px",
    ~padding="16px",
    ~gap="16px",
    ~fontSize="16px",
    (),
  ),
  "ToggleButtonGroupItem_sizeXLarge": style(
    ~maxHeight="60px",
    ~minWidth="100px",
    ~padding="20px",
    ~gap="20px",
    ~fontSize="20px",
    (),
  ),
  /* ---------- tone Success ---------- */
  "ToggleButtonGroupItem_successDefaultSelected": style(
    ~outline=`1px solid ${borderVariantSuccessToneSet.default} !important`,
    ~outlineOffset="-1.5px !important",
    (),
  ),
  "ToggleButtonGroupItem_successHoveredOrPressedSelected": style(
    ~outline=`1px solid ${borderVariantSuccessToneSet.hovered} !important`,
    ~outlineOffset="-1.5px !important",
    (),
  ),
  "ToggleButtonGroupItem_successSolidDefault": style(
    ~backgroundColor=backgroundVariantSuccessToneSet.solid.default,
    ~color=textSolidVariant,
    (),
  ),
  "ToggleButtonGroupItem_successSolidHovered": style(
    ~backgroundColor=backgroundVariantSuccessToneSet.solid.hovered,
    ~color=textVariantSuccessToneSet.default,
    (),
  ),
  "ToggleButtonGroupItem_successSolidPressed": style(
    ~backgroundColor=backgroundVariantSuccessToneSet.solid.pressed,
    ~color=textSolidVariant,
    (),
  ),
  "ToggleButtonGroupItem_successGhostDefault": style(
    ~backgroundColor=backgroundVariantSuccessToneSet.ghost.default,
    ~color=textVariantSuccessToneSet.default,
    (),
  ),
  "ToggleButtonGroupItem_successGhostHovered": style(
    ~backgroundColor=backgroundVariantSuccessToneSet.ghost.hovered,
    ~borderColor=borderVariantSuccessToneSet.hovered,
    ~color=textVariantSuccessToneSet.hovered,
    (),
  ),
  "ToggleButtonGroupItem_successGhostPressed": style(
    ~backgroundColor=backgroundVariantSuccessToneSet.ghost.pressed,
    ~borderColor=borderVariantSuccessToneSet.pressed,
    ~color=textVariantSuccessToneSet.pressed,
    (),
  ),
  "ToggleButtonGroupItem_successGhostDisabled": style(
    ~backgroundColor=backgroundVariantSuccessToneSet.ghost.disabled,
    ~color=textVariantSuccessToneSet.disabled,
    ~borderColor="transparent !important",
    ~outlineColor="transparent !important",
    (),
  ),
  "ToggleButtonGroupItem_successOutlineDefault": style(
    ~backgroundColor=backgroundVariantSuccessToneSet.outline.default,
    ~borderColor=borderVariantSuccessToneSet.default,
    ~color=textVariantSuccessToneSet.default,
    (),
  ),
  "ToggleButtonGroupItem_successSubtleDefault": style(
    ~backgroundColor=backgroundVariantSuccessToneSet.subtle.default,
    ~borderColor=borderVariantSuccessToneSet.default,
    ~color=textVariantSuccessToneSet.default,
    (),
  ),
  "ToggleButtonGroupItem_successOutlinePressed": style(
    ~backgroundColor=backgroundVariantSuccessToneSet.outline.pressed,
    ~borderColor=borderVariantSuccessToneSet.pressed,
    ~color=textVariantSuccessToneSet.pressed,
    (),
  ),
  "ToggleButtonGroupItem_successSubtlePressed": style(
    ~backgroundColor=backgroundVariantSuccessToneSet.subtle.pressed,
    ~borderColor=borderVariantSuccessToneSet.pressed,
    ~color=textVariantSuccessToneSet.pressed,
    (),
  ),
  "ToggleButtonGroupItem_successOutlineHovered": style(
    ~backgroundColor=backgroundVariantSuccessToneSet.outline.hovered,
    ~color=textVariantSuccessToneSet.hovered,
    (),
  ),
  "ToggleButtonGroupItem_successSubtleHovered": style(
    ~backgroundColor=backgroundVariantSuccessToneSet.subtle.hovered,
    ~color=textVariantSuccessToneSet.hovered,
    (),
  ),
  /* ---------- tone Danger ---------- */
  "ToggleButtonGroupItem_dangerDefaultSelected": style(
    ~outline=`1px solid ${borderVariantDangerToneSet.default} !important`,
    ~outlineOffset="-1.5px !important",
    (),
  ),
  "ToggleButtonGroupItem_dangerHoveredOrPressedSelected": style(
    ~outline=`1px solid ${borderVariantDangerToneSet.hovered} !important`,
    ~outlineOffset="-1.5px !important",
    (),
  ),
  "ToggleButtonGroupItem_dangerSolidDefault": style(
    ~backgroundColor=backgroundVariantDangerToneSet.solid.default,
    ~color=textSolidVariant,
    (),
  ),
  "ToggleButtonGroupItem_dangerSolidPressed": style(
    ~backgroundColor=backgroundVariantDangerToneSet.solid.pressed,
    ~color=textSolidVariant,
    (),
  ),
  "ToggleButtonGroupItem_dangerSolidHovered": style(
    ~backgroundColor=backgroundVariantDangerToneSet.solid.hovered,
    ~borderColor=borderVariantDangerToneSet.hovered,
    ~color=textVariantDangerToneSet.default,
    (),
  ),
  "ToggleButtonGroupItem_dangerGhostDefault": style(
    ~backgroundColor=backgroundVariantDangerToneSet.ghost.default,
    ~color=textVariantDangerToneSet.default,
    (),
  ),
  "ToggleButtonGroupItem_dangerGhostHovered": style(
    ~backgroundColor=backgroundVariantDangerToneSet.ghost.hovered,
    ~borderColor=borderVariantDangerToneSet.hovered,
    ~color=textVariantDangerToneSet.hovered,
    (),
  ),
  "ToggleButtonGroupItem_dangerGhostPressed": style(
    ~backgroundColor=backgroundVariantDangerToneSet.ghost.pressed,
    ~borderColor=borderVariantDangerToneSet.pressed,
    ~color=textVariantDangerToneSet.pressed,
    (),
  ),
  "ToggleButtonGroupItem_dangerGhostDisabled": style(
    ~backgroundColor=backgroundVariantDangerToneSet.ghost.disabled,
    ~color=textVariantDangerToneSet.disabled,
    ~borderColor="transparent !important",
    ~outlineColor="transparent !important",
    (),
  ),
  "ToggleButtonGroupItem_dangerOutlineDefault": style(
    ~backgroundColor=backgroundVariantDangerToneSet.outline.default,
    ~borderColor=borderVariantDangerToneSet.default,
    ~color=textVariantDangerToneSet.default,
    (),
  ),
  "ToggleButtonGroupItem_dangerSubtleDefault": style(
    ~backgroundColor=backgroundVariantDangerToneSet.subtle.default,
    ~borderColor=borderVariantDangerToneSet.default,
    ~color=textVariantDangerToneSet.default,
    (),
  ),
  "ToggleButtonGroupItem_dangerOutlinePressed": style(
    ~backgroundColor=backgroundVariantDangerToneSet.outline.pressed,
    ~borderColor=borderVariantDangerToneSet.pressed,
    ~color=textVariantDangerToneSet.pressed,
    (),
  ),
  "ToggleButtonGroupItem_dangerSubtleHovered": style(
    ~backgroundColor=backgroundVariantDangerToneSet.subtle.hovered,
    ~color=textVariantDangerToneSet.hovered,
    (),
  ),
  "ToggleButtonGroupItem_dangerOutlinePressed": style(
    ~backgroundColor=backgroundVariantDangerToneSet.outline.pressed,
    ~borderColor=borderVariantDangerToneSet.pressed,
    ~color=textVariantDangerToneSet.pressed,
    (),
  ),
  "ToggleButtonGroupItem_dangerSubtleHovered": style(
    ~backgroundColor=backgroundVariantDangerToneSet.subtle.hovered,
    ~color=textVariantDangerToneSet.hovered,
    (),
  ),
})

module ToggleButtonGroupItem = {
  let styleFor = (~tone: tone, ~variant: variant, ~state: state) =>
    switch (tone, variant, state) {
    /* ---------- tone Success ---------- */
    | (#success, #solid, #default) => styles["ToggleButtonGroupItem_successSolidDefault"]
    | (#success, #solid, #hovered) => styles["ToggleButtonGroupItem_successSolidHovered"]
    | (#success, #solid, #pressed) => styles["ToggleButtonGroupItem_successSolidPressed"]
    | (#success, #solid, #disabled) => styles["ToggleButtonGroupItem_successSolidDisabled"]

    | (#success, #ghost, #default) => styles["ToggleButtonGroupItem_successGhostDefault"]
    | (#success, #ghost, #hovered) => styles["ToggleButtonGroupItem_successGhostHovered"]
    | (#success, #ghost, #pressed) => styles["ToggleButtonGroupItem_successGhostPressed"]
    | (#success, #ghost, #disabled) => styles["ToggleButtonGroupItem_successGhostDisabled"]

    | (#success, #outline, #default) => styles["ToggleButtonGroupItem_successOutlineDefault"]
    | (#success, #outline, #hovered) => styles["ToggleButtonGroupItem_successOutlineHovered"]
    | (#success, #outline, #pressed) => styles["ToggleButtonGroupItem_successOutlinePressed"]
    | (#success, #outline, #disabled) => styles["ToggleButtonGroupItem_successOutlineDisabled"]

    | (#success, #subtle, #default) => styles["ToggleButtonGroupItem_successSubtleDefault"]
    | (#success, #subtle, #hovered) => styles["ToggleButtonGroupItem_successSubtleHovered"]
    | (#success, #subtle, #pressed) => styles["ToggleButtonGroupItem_successSubtlePressed"]
    | (#success, #subtle, #disabled) => styles["ToggleButtonGroupItem_successSubtleDisabled"]

    /* ---------- tone Danger ---------- */
    | (#danger, #solid, #default) => styles["ToggleButtonGroupItem_dangerSolidDefault"]
    | (#danger, #solid, #hovered) => styles["ToggleButtonGroupItem_dangerSolidHovered"]
    | (#danger, #solid, #pressed) => styles["ToggleButtonGroupItem_dangerSolidPressed"]
    | (#danger, #solid, #disabled) => styles["ToggleButtonGroupItem_dangerSolidDisabled"]

    | (#danger, #ghost, #default) => styles["ToggleButtonGroupItem_dangerGhostDefault"]
    | (#danger, #ghost, #hovered) => styles["ToggleButtonGroupItem_dangerGhostHovered"]
    | (#danger, #ghost, #pressed) => styles["ToggleButtonGroupItem_dangerGhostPressed"]
    | (#danger, #ghost, #disabled) => styles["ToggleButtonGroupItem_dangerGhostDisabled"]

    | (#danger, #outline, #default) => styles["ToggleButtonGroupItem_dangerOutlineDefault"]
    | (#danger, #outline, #hovered) => styles["ToggleButtonGroupItem_dangerOutlineHovered"]
    | (#danger, #outline, #pressed) => styles["ToggleButtonGroupItem_dangerOutlinePressed"]
    | (#danger, #outline, #disabled) => styles["ToggleButtonGroupItem_dangerOutlineDisabled"]

    | (#danger, #subtle, #default) => styles["ToggleButtonGroupItem_dangerSubtleDefault"]
    | (#danger, #subtle, #hovered) => styles["ToggleButtonGroupItem_dangerSubtleHovered"]
    | (#danger, #subtle, #pressed) => styles["ToggleButtonGroupItem_dangerSubtlePressed"]
    | (#danger, #subtle, #disabled) => styles["ToggleButtonGroupItem_dangerSubtleDisabled"]
    }

  let styleForSelected = (~tone: tone, ~state: state) =>
    switch (tone, state) {
    /* ---------- tone Success ---------- */
    | (#success, #default) => styles["ToggleButtonGroupItem_successDefaultSelected"]
    | (#success, #hovered | #pressed) =>
      styles["ToggleButtonGroupItem_successHoveredOrPressedSelected"]
    /* ---------- tone Danger ---------- */
    | (#danger, #default) => styles["ToggleButtonGroupItem_dangerDefaultSelected"]
    | (#danger, #hovered | #pressed) =>
      styles["ToggleButtonGroupItem_dangerHoveredOrPressedSelected"]
    | _ => style()
    }

  let styleProps = (~size: size, ~tone: tone, ~variant: variant, ~state: state, ~selected, ~grow) =>
    StyleX.props([
      styles["ToggleButtonGroupItem_root"],
      grow ? styles["grow"] : style(),
      switch size {
      | #small => styles["ToggleButtonGroupItem_sizeSmall"]
      | #medium => styles["ToggleButtonGroupItem_sizeMedium"]
      | #large => styles["ToggleButtonGroupItem_sizeLarge"]
      | #xlarge => styles["ToggleButtonGroupItem_sizeXLarge"]
      },
      styleFor(~tone, ~variant, ~state),
      selected ? styleForSelected(~tone, ~state) : style(),
    ])

  @react.component
  let make = React.memo((~variant, ~size, ~grow, ~item, ~groupState) => {
    let (ref, hovered) = Hover.use()
    let ref = ref->ReactDOM.Ref.domRef

    let {value, label, tone} = item
    let {buttonProps, pressed, selected, disabled} = ReactAria.ToggleButtonGroup.Item.use(
      ~props={id: value},
      ~state=groupState,
      ~ref,
    )

    let state = if disabled {
      #disabled
    } else if pressed {
      #pressed
    } else if hovered {
      #hovered
    } else {
      #default
    }

    let {?style, ?className} = styleProps(~variant, ~tone, ~size, ~state, ~selected, ~grow)

    <ReactAria.Spread props=buttonProps>
      <button ref ?style ?className> {label->React.string} </button>
    </ReactAria.Spread>
  })
}

let styleProps = (~size, ~grow) =>
  StyleX.props([
    styles["root"],
    grow ? styles["grow"] : style(),
    switch size {
    | #small => styles["sizeSmall"]
    | #medium => styles["sizeMedium"]
    | #large => styles["sizeLarge"]
    | #xlarge => styles["sizeXLarge"]
    },
  ])

@new external arrayToKeySet: array<ReactStately.key> => ReactStately.jsSet<ReactStately.key> = "Set"
let arrayFromKeySet = Js.Array.from

@react.component
let make = (
  ~size=#medium,
  ~variant=#solid,
  ~grow=false,
  ~selectionMode=#single,
  ~items: array<item>,
  ~value,
  ~onChange,
) => {
  let props = {
    ReactStately.ToggleGroup.selectionMode,
    disallowEmptySelection: true,
    selectedKeys: arrayToKeySet(value),
    // NOTE - get around for polymorphic `selection` type
    // TODO - use `Untagged variants` ReScript v11 feature
    onSelectionChange: keys =>
      switch Js.typeof(keys) {
      | "string" => onChange(items->Array.map(item => item.value))
      | _ => onChange(arrayFromKeySet(keys))
      },
  }
  let state = ReactStately.ToggleGroup.useState(~props)
  let ref = React.useRef(Js.Nullable.null)->ReactDOM.Ref.domRef
  let {groupProps} = ReactAria.ToggleButtonGroup.use(~props, ~state, ~ref)

  let {?style, ?className} = styleProps(~size, ~grow)

  <ReactAria.Spread props=groupProps>
    <div ref ?style ?className>
      {items
      ->Array.map(item =>
        <ToggleButtonGroupItem key=item.value variant size grow item groupState=state />
      )
      ->React.array}
    </div>
  </ReactAria.Spread>
}

let make = React.memo(make)
