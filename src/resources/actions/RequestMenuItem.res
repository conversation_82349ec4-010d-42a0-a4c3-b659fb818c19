open Intl

@react.component
let make = (~text, ~textError=?, ~operableRequest, ~onChange) => {
  let (requestResult, setRequestResult) = React.useState(() => AsyncData.NotAsked)

  ReactUpdateEffect.use1(() => {
    onChange(requestResult)
    None
  }, [requestResult])

  let onPress = React.useCallback1(() =>
    switch operableRequest {
    | Ok(request) =>
      switch requestResult {
      | Done(_) | NotAsked =>
        setRequestResult(_ => Loading)
        request()->Future.get(result => setRequestResult(_ => Done(result)))
      | Loading | Reloading(_) => ()
      }
    | Error(_) => ()
    }
  , [operableRequest])

  let text = switch (requestResult, textError) {
  | (Done(Error(_)), Some(textError)) => textError
  | _ => text
  }

  <Tooltip
    content={<Tooltip.Span
      text={switch operableRequest {
      | Ok(_) => t("Cannot run request")
      | Error(errorMessage) => errorMessage
      }}
    />}
    placement=#start
    disabled={operableRequest->Result.isOk}
    delay=75>
    <MenuItem
      size=#normal
      textVariation={switch requestResult {
      | Done(Error(_)) => #danger
      | _ => #normal
      }}
      disabled={requestResult->AsyncData.isBusy || operableRequest->Result.isError}
      content=Text(text)
      shouldCloseOnPress=false
      action=Callback(onPress)
    />
  </Tooltip>
}
