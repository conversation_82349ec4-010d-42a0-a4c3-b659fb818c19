type error = LinkingOpenUrlError | RequestError(Request.error)
let failureErrorToString: (error, ~exportName: string) => string
let decodeAndMakeUrl: Json.t => option<Url.t>

@react.component
let make: (
  ~text: string,
  ~textError: string=?,
  ~operableRequest: result<unit => Future.t<result<Json.t, Request.error>>, string>,
  ~onSuccess: option<Url.t> => unit,
  ~onFailure: error => unit,
) => React.element
