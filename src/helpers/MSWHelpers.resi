open MSW
open MSW.GraphQL

type exn += OperationNameNotFound

let apolloQueryExn: (
  link,
  module(ApolloClient__Core_ApolloClient.Operation with
    type t = 'data
    and type Raw.t = 'jsData
    and type t_variables = 'variables
    and type Raw.t_variables = 'jsVariables
  ),
  (request<'jsVariables>, context<'jsData> => 'result, context<'jsData>) => 'result,
) => requestHandler

let apolloQueryWithDelayExn: (
  link,
  module(ApolloClient__Core_ApolloClient.Operation with
    type t = 'data
    and type Raw.t = 'jsData
    and type t_variables = 'variables
    and type Raw.t_variables = 'jsVariables
  ),
  (request<'jsVariables>, (. delay, context<'jsData>) => 'result, context<'jsData>) => 'result,
) => requestHandler

type pageInfo = {
  endCursor: option<string>,
  hasNextPage: option<bool>,
}
type result<'data> = {
  edges: 'data,
  pageInfo: pageInfo,
  totalCount: int,
}

type exn += InvalidArgumentFirst
type exn += InvalidArgumentAfter

let cursorPaginateExn: (array<'a>, ~first: int=?, ~after: string=?, unit) => result<array<'a>>
