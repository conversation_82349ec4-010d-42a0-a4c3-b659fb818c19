let queryResultToAsyncResult: ApolloClient__React_Hooks_UseQuery.QueryResult.t<
  'data,
  'jsData,
  'variables,
  'jsVariables,
> => AsyncResult.t<'data, ApolloClient__React_Types.ApolloError.t>

let useQueryResultToAsyncResultWithVariablesReloading1: (
  ApolloClient__React_Hooks_UseQuery.QueryResult.t<'data, 'jsData, 'variables, 'jsVariables>,
  'variableA,
) => AsyncResult.t<'data, ApolloClient__React_Types.ApolloError.t>

let useQueryResultToAsyncResultWithVariablesReloading2: (
  ApolloClient__React_Hooks_UseQuery.QueryResult.t<'data, 'jsData, 'variables, 'jsVariables>,
  'variableA,
  'variableB,
) => AsyncResult.t<'data, ApolloClient__React_Types.ApolloError.t>

let useQueryResultToAsyncResultWithVariablesReloading3: (
  ApolloClient__React_Hooks_UseQuery.QueryResult.t<'data, 'jsData, 'variables, 'jsVariables>,
  'variableA,
  'variableB,
  'variableC,
) => AsyncResult.t<'data, ApolloClient__React_Types.ApolloError.t>

let mutationPromiseToFutureResult: Js.Promise.t<
  result<ApolloClient__React_Types.FetchResult.t__ok<'a>, ApolloClient__React_Types.ApolloError.t>,
> => Future.t<result<'a, string>>

let mergeFutureResults: array<Future.t<result<'a, 'b>>> => Future.t<result<'a, string>>
