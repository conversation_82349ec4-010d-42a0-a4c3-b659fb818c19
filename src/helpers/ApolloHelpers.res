module FetchResult = ApolloClient__React_Types.FetchResult
module ApolloError = ApolloClient__React_Types.ApolloError
module QueryResult = ApolloClient__React_Hooks_UseQuery.QueryResult

let queryResultToAsyncResult = queryResult =>
  switch queryResult {
  // Assumes the user reloaded the query by changing its parameters
  | {QueryResult.loading: true, data: Some(data), networkStatus: SetVariables}
  | {loading: true, previousData: Some(Ok(data)), networkStatus: SetVariables | FetchMore} =>
    AsyncData.Reloading(Ok(data))
  // Assumes the user reloaded the query from a mutation side-effect
  | {loading: true, data: Some(data), networkStatus: Loading}
  | {loading: true, previousData: Some(Ok(data)), networkStatus: Loading} =>
    Done(Ok(data))
  | {loading: false, error: None, data: Some(data)} => Done(Ok(data))
  | {loading: true} => Loading
  | {error: Some(error)} => Done(Error(error))
  | {error: None, loading: false, data: None} => NotAsked
  }

// NOTE - maps to a Loading state whenever a query variable's value changed
let useQueryResultToAsyncResult = (queryResult, ~loading, ~onDone) => {
  let asyncResult = queryResult->queryResultToAsyncResult

  React.useEffect1(() => {
    switch (loading, asyncResult) {
    | (true, Done(_)) => onDone()
    | _ => ()
    }
    None
  }, [asyncResult])

  switch (asyncResult, loading) {
  | (Reloading(_), true) => AsyncData.Loading
  | (asyncResult, _) => asyncResult
  }
}

let useQueryResultToAsyncResultWithVariablesReloading1 = (queryResult, variableA) => {
  let loadingRef = React.useRef(true)

  React.useEffect1(() => {
    loadingRef.current = true
    None
  }, [variableA])

  queryResult->useQueryResultToAsyncResult(~loading=loadingRef.current, ~onDone=() =>
    loadingRef.current = false
  )
}

let useQueryResultToAsyncResultWithVariablesReloading2 = (queryResult, variableA, variableB) => {
  let loadingRef = React.useRef(true)

  React.useEffect2(() => {
    loadingRef.current = true
    None
  }, (variableA, variableB))

  queryResult->useQueryResultToAsyncResult(~loading=loadingRef.current, ~onDone=() =>
    loadingRef.current = false
  )
}

let useQueryResultToAsyncResultWithVariablesReloading3 = (
  queryResult,
  variableA,
  variableB,
  variableC,
) => {
  let loadingRef = React.useRef(true)

  React.useEffect3(() => {
    loadingRef.current = true
    None
  }, (variableA, variableB, variableC))

  queryResult->useQueryResultToAsyncResult(~loading=loadingRef.current, ~onDone=() =>
    loadingRef.current = false
  )
}

let mutationPromiseToFutureResult = response =>
  response
  ->FuturePromise.fromPromise
  ->Future.map(mutation =>
    switch mutation {
    | Ok(Ok({FetchResult.data: data, error: None})) => Ok(data)
    | Ok(Error({ApolloError.networkError: Some(FetchFailure(exn))})) =>
      Error(Js.Exn.message(exn)->Option.getExn)
    | Ok(Ok({error: Some(error)}))
    | Ok(Error(error)) =>
      Error(error.message)
    | Error(_) => Error(Request.serverErrorMessage)
    }
  )

let mergeFutureResults = all =>
  all
  ->Future.all
  ->Future.map(results =>
    switch (
      results[0],
      results->Array.every(result =>
        switch result {
        | Ok(_) => true
        | _ => false
        }
      ),
    ) {
    | (Some(Ok(ok)), true) => Ok(ok)
    | _ => Error(Request.serverErrorMessage)
    }
  )
