let copyDate = date => date->Js.Date.getTime->Js.Date.fromFloat

let startOfDay = date =>
  Js.Date.setHoursMSMs(
    date->copyDate,
    ~hours=0.,
    ~minutes=0.,
    ~seconds=0.,
    ~milliseconds=0.,
    (),
  )->Js.Date.fromFloat

let endOfDay = date =>
  Js.Date.setHoursMSMs(
    date->copyDate,
    ~hours=23.,
    ~minutes=59.,
    ~seconds=59.,
    ~milliseconds=999.,
    (),
  )->Js.Date.fromFloat

let isSameDay = (dateA, dateB) =>
  dateA->copyDate->startOfDay->Js.Date.getTime === dateB->copyDate->startOfDay->Js.Date.getTime

let addDays = (date, days) =>
  Js.Date.setDate(date->copyDate, Js.Date.getDate(date) +. days)->Js.Date.fromFloat

let subDays = (date, days) => addDays(date, -.days)

let startOfWeek = date => {
  let startWeekDay = 1. // Monday
  let day = Js.Date.getDay(date)
  let diff = (day < startWeekDay ? 7. : 0.) +. day -. startWeekDay
  Js.Date.setDate(date->copyDate, Js.Date.getDate(date) -. diff)->Js.Date.fromFloat->startOfDay
}

let endOfWeek = date => {
  let startWeekDay = 1. // Monday
  let day = Js.Date.getDay(date)
  let diff = (day < startWeekDay ? -7. : 0.) +. 6. -. (day -. startWeekDay)
  Js.Date.setDate(date->copyDate, Js.Date.getDate(date) +. diff)->Js.Date.fromFloat->endOfDay
}

let addWeeks = (date, weeks) => addDays(date, weeks *. 7.)

let subWeeks = (date, weeks) => addWeeks(date, -.weeks)

let startOfMonth = date => Js.Date.setDate(date->copyDate, 1.)->Js.Date.fromFloat->startOfDay

let lastDayOfMonthDate = date =>
  Js.Date.makeWithYMD(
    ~year=Js.Date.getFullYear(date),
    ~month=Js.Date.getMonth(date) +. 1.,
    ~date=0.,
    (),
  )

let getDaysInMonth = date => lastDayOfMonthDate(date)->Js.Date.getDate

let addMonths = (date, months) => {
  let year = Js.Date.getFullYear(date)
  let month = Js.Date.getMonth(date) +. months
  let hours = Js.Date.getHours(date)
  let minutes = Js.Date.getMinutes(date)
  let seconds = Js.Date.getSeconds(date)
  let daysInMonth = getDaysInMonth(Js.Date.makeWithYMD(~year, ~month, ~date=1., ()))
  let date = Js.Math.min_float(daysInMonth, Js.Date.getDate(date))
  Js.Date.makeWithYMDHMS(~year, ~month, ~date, ~hours, ~minutes, ~seconds, ())
}

let subMonths = (date, months) => addMonths(date, -.months)

let isSameMonth = (dateA, dateB) =>
  dateA->startOfMonth->Js.Date.getTime === dateB->startOfMonth->Js.Date.getTime

let endOfMonth = date => date->lastDayOfMonthDate->endOfDay

let startOfYear = date =>
  Js.Date.makeWithYMDHMS(
    ~year=Js.Date.getFullYear(date),
    ~month=0.,
    ~date=1.,
    ~hours=0.,
    ~minutes=0.,
    ~seconds=0.,
    (),
  )

let addYears = (date, years) => addMonths(date, 12. *. years)

let subYears = (date, years) => addYears(date, -.years)

let endOfYear = date =>
  Js.Date.makeWithYMD(~year=Js.Date.getFullYear(date), ~month=11., ~date=31., ())->endOfDay

let diffInDays = (firstDate, lastDate) => {
  let dayMs = 24. *. 60. *. 60. *. 1000.
  let diff = lastDate->Js.Date.getTime -. firstDate->Js.Date.getTime
  Js.Math.abs_float(diff /. dayMs)->Js.Math.round->Float.toInt // FIXME - should use Math.floor
}

let diffInHours = (firstDate, lastDate) => {
  let hourMs = 60. *. 60. *. 1000.
  let diff = firstDate->Js.Date.getTime -. lastDate->Js.Date.getTime
  Js.Math.abs_float(diff /. hourMs)->Js.Math.round->Float.toInt // FIXME - should use Math.floor
}

let getPreviousPeriod = (startDate, endDate) => {
  let startDatePreviousPeriod = startDate->subDays(diffInDays(startDate, endDate)->Int.toFloat)
  let endDatePreviousPeriod = endDate->subDays(diffInDays(startDate, endDate)->Int.toFloat)
  (startDatePreviousPeriod, endDatePreviousPeriod)
}

let getPreviousYearPeriod = (startDate, endDate) => {
  let startDatePreviousPeriod = startDate->subYears(1.)
  let endDatePreviousPeriod = endDate->subYears(1.)
  (startDatePreviousPeriod, endDatePreviousPeriod)
}
