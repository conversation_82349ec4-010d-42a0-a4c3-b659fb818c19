open MSW
open MSW.GraphQL

exception OperationNameNotFound

module GraphQLGetFromAst = ApolloClient__Utilities_Graphql_GetFromAst

let apolloQueryExn:
  type data jsData variables jsVariables. (
    link,
    module(ApolloClient__Core_ApolloClient.Operation with
      type t = data
      and type Raw.t = jsData
      and type t_variables = variables
      and type Raw.t_variables = jsVariables
    ),
    (request<jsVariables>, context<jsData> => 'result, context<jsData>) => 'result,
  ) => requestHandler =
  (graphQLLink, module(Operation), resolver) => {
    let documentNode = Operation.query
    let operationDefinition = documentNode->GraphQLGetFromAst.getOperationDefinition

    switch operationDefinition {
    | Some({name: Some({value: operationName})}) => graphQLLink->query(operationName, resolver)
    | _ => raise(OperationNameNotFound)
    }
  }

let apolloQueryWithDelayExn:
  type data jsData variables jsVariables. (
    link,
    module(ApolloClient__Core_ApolloClient.Operation with
      type t = data
      and type Raw.t = jsData
      and type t_variables = variables
      and type Raw.t_variables = jsVariables
    ),
    (request<jsVariables>, (. delay, context<jsData>) => 'result, context<jsData>) => 'result,
  ) => requestHandler =
  (graphQLLink, module(Operation), resolver) => {
    let documentNode = Operation.query
    let operationDefinition = documentNode->GraphQLGetFromAst.getOperationDefinition

    switch operationDefinition {
    | Some({name: Some({value: operationName})}) =>
      graphQLLink->queryWithDelay(operationName, resolver)
    | _ => raise(OperationNameNotFound)
    }
  }

type pageInfo = {
  endCursor: option<string>,
  hasNextPage: option<bool>,
}

type result<'data> = {
  edges: 'data,
  pageInfo: pageInfo,
  totalCount: int,
}

exception InvalidArgumentFirst
exception InvalidArgumentAfter

// NOTE - For simplicity the cursor is based on the index as mocked
// data items don't necessary have a cursor to be identified with.
let cursorPaginateExn = (data, ~first=20, ~after=?, ()) => {
  let totalCount = data->Array.length
  let emptyResult = {
    edges: [],
    pageInfo: {
      endCursor: None,
      hasNextPage: None,
    },
    totalCount,
  }

  switch (first <= 0, after->Option.flatMap(Int.fromString)) {
  | (true, _) => raise(InvalidArgumentFirst)
  | (_, None) if totalCount === 0 => emptyResult
  | (_, Some(cursor)) if cursor < 0 || cursor > totalCount => emptyResult
  | _ =>
    let cursor = after->Option.mapWithDefault(0, cursor =>
      switch cursor->Int.fromString {
      | Some(cursorIndex) => cursorIndex + 1
      | None => raise(InvalidArgumentAfter)
      }
    )
    let dataFetched = data->Array.slice(~offset=cursor, ~len=first)
    let endCursor = cursor + first > totalCount - 1 ? totalCount - 1 : cursor + first - 1
    let hasNextPage = endCursor < totalCount - 1

    {
      edges: dataFetched,
      pageInfo: {
        endCursor: Some(endCursor->Int.toString),
        hasNextPage: Some(hasNextPage),
      },
      totalCount,
    }
  }
}
