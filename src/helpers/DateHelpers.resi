let startOfDay: Js.Date.t => Js.Date.t
let endOfDay: Js.Date.t => Js.Date.t

let isSameDay: (Js.Date.t, Js.Date.t) => bool

let addDays: (Js.Date.t, float) => Js.Date.t
let subDays: (Js.Date.t, float) => Js.Date.t

let startOfWeek: Js.Date.t => Js.Date.t
let endOfWeek: Js.Date.t => Js.Date.t

let addWeeks: (Js.Date.t, float) => Js.Date.t
let subWeeks: (Js.Date.t, float) => Js.Date.t

let startOfMonth: Js.Date.t => Js.Date.t
let endOfMonth: Js.Date.t => Js.Date.t

let isSameMonth: (Js.Date.t, Js.Date.t) => bool
let lastDayOfMonthDate: Js.Date.t => Js.Date.t
let getDaysInMonth: Js.Date.t => float

let addMonths: (Js.Date.t, float) => Js.Date.t
let subMonths: (Js.Date.t, float) => Js.Date.t

let startOfYear: Js.Date.t => Js.Date.t
let endOfYear: Js.Date.t => Js.Date.t

let addYears: (Js.Date.t, float) => Js.Date.t
let subYears: (Js.Date.t, float) => Js.Date.t

let diffInDays: (Js.Date.t, Js.Date.t) => int
let diffInHours: (Js.Date.t, Js.Date.t) => int

let getPreviousPeriod: (Js.Date.t, Js.Date.t) => (Js.Date.t, Js.Date.t)
let getPreviousYearPeriod: (Js.Date.t, Js.Date.t) => (Js.Date.t, Js.Date.t)
