// TODO - Upgrade to react router 6 (v5 currently):
// this takes advantage of React 18 with concurent mode
// and can unlock animated page transition (bonus).

type t = {
  history: History.t,
  location: History.location,
}

@module("react-router")
external contextRouter: React.Context.t<t> = "__RouterContext"

module Router = {
  @react.component @module("react-router")
  external make: (~children: React.element=?, ~history: History.t=?) => React.element = "Router"
}

module Link = {
  type to = {
    pathname: string,
    search?: string,
    hash?: string,
    state?: Json.t,
  }

  @react.component @module("react-router-dom")
  external make: (
    ~children: React.element,
    ~to: to,
    ~target: [#_blank | #_self]=?,
    ~replace: bool=?,
    ~onClick: unit => unit=?,
    ~style: ReactDOM.Style.t=?,
    ~className: string=?,
  ) => React.element = "Link"
}

module Redirect = {
  type to = {
    pathname: string,
    search?: string,
    hash?: string,
    state?: Js.<PERSON>son.t,
  }

  @react.component @module("react-router-dom")
  external make: (~to: to) => React.element = "Redirect"
}
