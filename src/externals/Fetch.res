// NOTE — Inspired from https://github.com/glennsl/rescript-fetch

module Blob = {
  type t
}

module FormData = {
  type t
}

module AbortSignal = {
  type t
  @get external aborted: t => bool = "aborted"
  @set external onabort: (t, Dom.event => unit) => unit = "onabort"
}

module AbortController = {
  type t
  @new external new: unit => t = "AbortController"
  @get external signal: t => AbortSignal.t = "signal"
  @send external abort: t => unit = "abort"
}

type method = [#GET | #HEAD | #POST | #PUT | #DELETE | #CONNECT | #OPTIONS | #TRACE | #PATCH]
type referrerPolicy = [
  | #""
  | #"no-referrer"
  | #"no-referrer-when-downgrade"
  | #"same-origin"
  | #origin
  | #"strict-origin"
  | #"origin-when-cross-origin"
  | #"strict-origin-when-cross-origin"
  | #"unsafe-url"
]
type requestDestination = [
  | #""
  | #audio
  | #audioworklet
  | #document
  | #embed
  | #font
  | #frame
  | #iframe
  | #image
  | #manifest
  | #object
  | #paintworklet
  | #report
  | #script
  | #sharedworker
  | #style
  | #track
  | #video
  | #worker
  | #xslt
]
type requestMode = [#navigate | #"same-origin" | #"no-cors" | #cors]
type requestCredentials = [#omit | #"same-origin" | #"include"]
type requestCache = [
  | #default
  | #"no-store"
  | #reload
  | #"no-cache"
  | #"force-cache"
  | #"only-if-cached"
]
type requestRedirect = [#follow | #error | #manual]
type requestDuplex = [#half]
type responseType = [#basic | #cors | #default | #error | #opaque | #opaqueredirect]

module Body = {
  type t

  external string: string => t = "%identity"
  external blob: Blob.t => t = "%identity"
  external formData: FormData.t => t = "%identity"
  @val external null: t = "null"
}

module Headers = {
  type t

  module Init = {
    type t
    external object: {..} => t = "%identity"
    external array: array<(string, string)> => t = "%identity"
  }

  @new external empty: t = "Headers"
  @new external make: Init.t => t = "Headers"

  @new external clone: t => t = "Headers"
  @new external fromObject: {..} => t = "Headers"
  @new external fromArray: array<(string, string)> => t = "Headers"

  @send external append: (t, string, string) => unit = "append"
  @send external delete: (t, string) => unit = "delete"
  @send @return(nullable) external get: (t, string) => option<string> = "get"
  @send external has: (t, string) => bool = "has"
  @send external set: (t, string, string) => unit = "set"

  @send external forEach: (t, @uncurry (string, string, t) => unit) => unit = "forEach"

  @send external entries: t => Js.Array.array_like<(string, string)> = "entries"
  @send external keys: t => Js.Array.array_like<string> = "keys"
  @send external values: t => Js.Array.array_like<string> = "values"
}

module Request = {
  type t
  type init = {
    method?: method,
    body?: Body.t,
    headers?: Headers.t,
    referrer?: string,
    referrerPolicy?: referrerPolicy,
    mode?: requestMode,
    credentials?: requestCredentials,
    cache?: requestCache,
    redirect?: requestRedirect,
    integrity?: string,
    keepalive?: bool,
    signal?: AbortSignal.t,
    duplex?: requestDuplex,
  }

  @new external make: (string, init) => t = "Request"
  @new external fromRequest: (t, init) => t = "Request"

  @get external method: t => method = "method"
  @get external url: t => string = "url"
  @get external headers: t => Headers.t = "headers"

  @get external bodyUsed: t => bool = "bodyUsed"
  @send external text: t => Promise.t<string> = "text"
  @send external json: t => Promise.t<Json.t> = "json"
  @send external blob: t => Promise.t<Blob.t> = "blob"
  @send external formData: t => Promise.t<FormData.t> = "formData"
}

module Response = {
  type t

  @get external type_: t => responseType = "type"

  @get external url: t => string = "url"
  @get external redirected: t => bool = "redirected"
  @get external status: t => int = "status"
  @get external ok: t => bool = "ok"
  @get external statusText: t => string = "statusText"
  @get external headers: t => Headers.t = "headers"

  @send external clone: t => t = "clone"

  @get external bodyUsed: t => bool = "bodyUsed"
  @send external text: t => Promise.t<string> = "text"
  @send external json: t => Promise.t<Json.t> = "json"
  @send external blob: t => Promise.t<Blob.t> = "blob"
  @send external formData: t => Promise.t<FormData.t> = "formData"
}

@val external make: (string, Request.init) => Promise.t<Response.t> = "fetch"
