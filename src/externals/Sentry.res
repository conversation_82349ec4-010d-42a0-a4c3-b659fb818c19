module ErrorBoundary = {
  @react.component @module("@sentry/react")
  external make: (~children: React.element, ~fallback: exn => React.element) => React.element =
    "ErrorBoundary"
}

@module("@sentry/react")
external captureException: Js.Exn.t => unit = "captureException"

@module("@sentry/react")
external captureMessage: string => unit = "captureMessage"

type contextUser = {
  userId: string,
  username: string,
  shopId: option<string>,
  shopName?: option<string>,
  deviceId?: option<string>,
}

@module("@sentry/react") external setUser: contextUser => unit = "setUser"

type scope = {"setExtra": (. string, string) => unit}

@module("@sentry/react")
external configureScope: (scope => unit) => unit = "configureScope"
