module DayPickerSingleDateController = {
  @react.component @module("react-dates")
  external make: (
    ~date: Js.Nullable.t<Moment.t>,
    ~onDateChange: Moment.t => unit,
    ~onPrevMonthClick: Moment.t => unit=?,
    ~onNextMonthClick: Moment.t => unit=?,
    ~onFocusChange: {"focused": bool} => unit=?,
    ~isDayBlocked: Moment.t => bool=?,
    ~displayFormat: string=?,
    ~monthFormat: string=?,
    ~weekDayFormat: string=?,
    ~transitionDuration: float=?,
    ~noBorder: bool=?,
    ~focused: bool=?,
  ) => React.element = "DayPickerSingleDateController"
}

module DayPickerRangeController = {
  @genType.import(("./ReactDates", "Dates"))
  type dates = {
    startDate: Js.Nullable.t<Moment.t>,
    endDate: Js.Nullable.t<Moment.t>,
  }

  type focusedInput = [#startDate | #endDate]
  @genType let defaultFocusedInput: focusedInput = #startDate

  @react.component @module("react-dates")
  external make: (
    ~startDate: Js.Nullable.t<Moment.t>,
    ~endDate: Js.Nullable.t<Moment.t>,
    ~onDatesChange: dates => unit,
    ~onPrevMonthClick: Moment.t => unit=?,
    ~onNextMonthClick: Moment.t => unit=?,
    ~onFocusChange: Js.Nullable.t<focusedInput> => unit,
    ~displayFormat: string=?,
    ~monthFormat: string=?,
    ~weekDayFormat: string=?,
    ~transitionDuration: float=?,
    ~noBorder: bool=?,
    ~focusedInput: focusedInput=?,
    ~initialVisibleMonth: unit => Moment.t=?,
    ~maxDate: Moment.t=?,
    ~isOutsideRange: Moment.t => bool=?,
    ~minimumNights: int=?,
  ) => React.element = "DayPickerRangeController"
}
