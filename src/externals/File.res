@genType.as("File")
type t

type filePropertyBag

external toBlob: t => Blob.t = "%identity"
external fromBlob: Blob.t => t = "%identity"

@obj
external makeFilePropertyBag: (
  ~endings: Blob.endingType=?,
  ~_type: string=?,
  ~lastModified: float=?,
  unit,
) => filePropertyBag = ""

@get external type_: t => string = "type"
@get external size: t => float = "size"

@new external make: (array<Blob.blobPart>, string) => t = "File"
@new external makeWithOptions: (array<Blob.blobPart>, string, filePropertyBag) => t = "File"

@get external lastModified: t => float = "lastModified"

@get external name: t => string = "name"
