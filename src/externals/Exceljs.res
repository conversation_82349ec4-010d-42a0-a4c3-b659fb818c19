module WorksheetCell = {
  type tJs = {"value": Js.Nullable.t<string>}
}

module WorksheetRow = {
  type t = {values: array<option<Json.t>>}

  @send @return(nullable) external getRowJsCell: (t, int) => option<WorksheetCell.tJs> = "getCell"
}

module Worksheet = {
  type t

  @send external addRow: (t, array<string>) => unit = "addRow"
  @send external getRow: (t, int) => WorksheetRow.t = "getRow"
  @send external eachRow: (t, (WorksheetRow.t, int) => unit) => unit = "eachRow"
}

module Workbook = {
  type t
  @new @module("exceljs") external make: unit => t = "Workbook"

  @send @return(nullable) external getWorksheet: (t, int) => option<Worksheet.t> = "getWorksheet"
  @send external addWorksheet: (t, string) => Worksheet.t = "addWorksheet"
}

module Xlsx = {
  type t

  @get external fromWorkbook: Workbook.t => t = "xlsx"
  @send external writeBuffer: t => Promise.t<Js.Typed_array.ArrayBuffer.t> = "writeBuffer"
  @send external load: (t, Js.Typed_array.ArrayBuffer.t) => Promise.t<Workbook.t> = "load"
}
