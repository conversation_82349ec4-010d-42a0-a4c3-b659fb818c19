type t

@module("@testing-library/user-event") @scope("default") external setup: unit => t = "setup"
type setupOptions = {delay?: int, skipClick?: bool, writeToClipboard?: bool}
@module("@testing-library/user-event") @scope("default")
external setupWithOptions: setupOptions => t = "setup"

@send external keyboard: (t, string) => Js.Promise.t<unit> = "keyboard"

@send external tab: t => Js.Promise.t<unit> = "tab"
@send external tabShift: (t, @as("json`{ shift: true }`") _) => Js.Promise.t<unit> = "tab"

@send external click: (t, Dom.element) => Js.Promise.t<unit> = "click"

@send external hover: (t, Dom.element) => Js.Promise.t<unit> = "hover"
@send external unhover: (t, Dom.element) => Js.Promise.t<unit> = "unhover"

@send external type_: (t, Dom.element, string) => Js.Promise.t<unit> = "type"
type typeOptions = {initialSelectionStart?: int, initialSelectionEnd?: int}
@send external typeWithOptions: (t, Dom.element, string, typeOptions) => Js.Promise.t<unit> = "type"

@send external clear: (t, Dom.element) => Js.Promise.t<unit> = "clear"
