type parseConfigInput<'a> = {data: 'a}

type parseConfig<'a> = {
  header?: bool,
  step: parseConfigInput<'a> => unit,
  complete: unit => unit,
  error: unit => unit,
  delimiter?: string,
  skipEmptyLines?: Json.t, // TODO — Polymorphic value: true | false | "greedy". Need ReScript v11 to handle.
  worker: bool,
}

@module("papaparse") @deprecated("Use parse")
external unsafeParse: (File.t, parseConfig<array<string>>) => unit = "parse"

@module("papaparse")
external parse: (File.t, parseConfig<Json.t>) => unit = "parse"

type unparseConfig = {
  quotes?: bool,
  quoteChar?: char,
  escapeChar?: char,
  delimiter?: string,
  header?: bool,
  newline?: string,
  skipEmptyLines?: bool,
  columns?: Js.Nullable.t<array<string>>,
}

@module("papaparse")
external unparse: (array<array<string>>, ~config: unparseConfig=?) => string = "unparse"
