// NOTE — It’s oftentimes easier maintaining our own Web bindings and copy paste
// the relevant bits from projects like rescript-webapi.
// The only thing ReScript officially provides are some abstract Dom types
// to allow bindings to align on the same base types at least.
// > https://rescript-lang.org/docs/manual/latest/api/dom
// It's a good thing it stays that way, unlike typescript, which is a superset of
// JavaScript language. It might feel weird, because ecostyem users tend
// to build generalized bindings for every use-case of a library / JS api
// and try to stick to the DRY principle as much as possible.
//
// UPSIDE: Very easy to adapt the code to our actual needs without hidden runtime code
// (= cleaner JS output) + it’s also easier to navigate / find the code
// you are interested in.
// DOWNSIDES: It’s very important to get a good understanding on the interop layer
// of the ReScript language, so it's highly recommend getting your hands dirty
// on the binding topic as soon as possible.

module DomEvent = {
  type t = Dom.event

  // This feature is no longer recommended. Though some browsers might still support it,
  // it may have already been removed from the relevant web standards, may be in the process
  // of being dropped, or may only be kept for compatibility purposes. Avoid using it, and
  // update existing code if possible; see the compatibility table at the bottom of this page
  // to guide your decision. Be aware that this feature may cease to work at any time.
  @deprecated @set external setReturnValue: (t, string) => unit = "returnValue"

  @send external stopPropagation: t => unit = "stopPropagation"
}

module KeyboardEvent = {
  type t = Dom.keyboardEvent

  @get external key: t => string = "key"
  @send external continuePropagation: t => unit = "continuePropagation"
  @send external stopPropagation: t => unit = "stopPropagation"
  @send external preventDefault: t => unit = "preventDefault"
  @get external ctrlKey: t => bool = "ctrlKey"
}

module DomNodeLike = {
  type t<'a> = Dom.node_like<'a>
}

module DomNode = {
  type t = Dom.node

  @send external appendChild: (t, ~child: DomNodeLike.t<'a>) => unit = "appendChild"
  @send external removeChild: (t, ~child: DomNodeLike.t<'a>) => DomNodeLike.t<'a> = "removeChild"
}

module HtmlOptionElement = {
  type t = Dom.htmlOptionElement

  @get external textContent: t => string = "textContent"
}

module HtmlCollection = {
  type t = Dom.htmlCollection
}

module DomRect = {
  type t = Dom.domRect

  @new
  external make: (~x: float, ~y: float, ~width: float, ~height: float) => t =
    "DOMRect" /* experimental */

  @get external top: t => float = "top"
  @get external bottom: t => float = "bottom"
  @get external left: t => float = "left"
  @get external right: t => float = "right"
  @get external height: t => float = "height"
  @get external width: t => float = "width"
  @get external x: t => float = "x"
  @get external y: t => float = "y"
}

module DomElement = {
  type t = Dom.element

  external asNode: t => DomNode.t = "%identity"
  external asHtmlOptionElement: t => HtmlOptionElement.t = "%identity"
  external ofDomNode: DomNode.t => Dom.element = "%identity"

  @send external blur: t => unit = "blur"
  @send @return(nullable) external getAttribute: (t, string) => option<string> = "getAttribute"
  @send @return(nullable) external closest: (t, string) => option<t> = "closest"
  @send external getElementsByTagName: (t, string) => HtmlCollection.t = "getElementsByTagName"
  @val @scope(("Array", "prototype", "slice"))
  external fromCollectionArray: HtmlCollection.t => array<t> = "call"
  @send external setAttribute: (t, string, string) => unit = "setAttribute"
  @send external addEventListener: (t, string, 'a => unit) => unit = "addEventListener"
  @send external removeEventListener: (t, string, 'a => unit) => unit = "removeEventListener"
  @get external offsetWidth: t => float = "offsetWidth"
  @get external offsetHeight: t => float = "offsetHeight"
  @get external scrollHeight: t => int = "scrollHeight" /* experimental, but widely supported */
  @get external scrollWidth: t => int = "scrollWidth" /* experimental, but widely supported */
  @get external scrollLeft: t => float = "scrollLeft" /* experimental */
  @set external setScrollLeft: (t, float) => unit = "scrollLeft" /* experimental */
  @get external scrollTop: t => float = "scrollTop" /* experimental, but widely supported */
  @set external setScrollTop: (t, float) => unit = "scrollTop" /* expt, but widely supported */
  @get external clientHeight: t => int = "clientHeight" /* experimental */
  @get external clientLeft: t => int = "clientLeft" /* experimental */
  @get external clientTop: t => int = "clientTop" /* experimental */
  @get external clientWidth: t => int = "clientWidth" /* experimental */
  @send external getBoundingClientRect: t => DomRect.t = "getBoundingClientRect"
  @get external className: t => string = "className"
  @set external setClassName: (t, string) => unit = "className"
  @get external checked: t => bool = "checked"
  @get @return(nullable) external parentElement: t => option<t> = "parentElement"
  @send
  external addKeyDownEventListener: (t, @as("keydown") _, KeyboardEvent.t => unit) => unit =
    "addEventListener"
  @send
  external removeKeyDownEventListener: (t, @as("keydown") _, KeyboardEvent.t => unit) => unit =
    "removeEventListener"

  type scrollOptions = {
    top?: int,
    left?: int,
    behavior?: [#auto | #smooth],
  }

  @send external scrollBy: (t, ~options: scrollOptions) => unit = "scrollBy"
  @send external scrollTo: (t, ~options: scrollOptions) => unit = "scrollTo"

  type scrollIntoViewOptions = {
    inline?: [#nearest | #start | #center | #end],
    block?: [#nearest | #start | #center | #end],
    behavior?: [#auto | #smooth],
  }
  @send external scrollIntoView: (t, ~options: scrollIntoViewOptions) => unit = "scrollIntoView"
}

module HtmlDocument = {
  type t = Dom.htmlDocument

  @get @return(nullable) external activeElement: t => option<Dom.element> = "activeElement"
}

module Document = {
  type t = Dom.document

  external asDomElement: t => Dom.element = "%identity"
  external asHtmlDocument: t => Dom.htmlDocument = "%identity"

  @send @return(nullable)
  external querySelector: (t, string) => option<Dom.element> = "querySelector"
  @send @return(nullable)
  external querySelectorAll: (t, string) => option<array<Dom.element>> = "querySelectorAll"
  @send @return(nullable)
  external getElementById: (t, string) => option<Dom.element> = "getElementById"
  @send external createElement: (t, string) => Dom.element = "createElement"
}
@val external document: Document.t = "document"

module MediaQueryList = {
  type t

  @get external matches: t => bool = "matches"
  @deprecated @send external addListener: (t, 'event => unit) => unit = "addListener"
  @deprecated @send external removeListener: (t, 'event => unit) => unit = "removeListener"
}

module Navigator = {
  type t

  /**
   Browser identification based on detecting the user agent string is unreliable
   and is not recommended, as the user agent string is user configurable. 

   ```rescript
   open WebAPI
   let userAgent = window->Window.navigator->Navigator.userAgent
   ````
   */
  @get
  external userAgent: t => string = "userAgent"
}

module Window = {
  type t = Dom.window

  @send @return(nullable) external openUrl: (t, string) => option<t> = "open"
  @send @return(nullable) external openUrlWithTarget: (t, string, string) => option<t> = "open"
  @send external close: t => unit = "close"
  @set external setLocation: (t, string) => unit = "location"

  /**
   The method addEventListener() works by adding a function, or an object that
   implements EventListener, to the list of event listeners for the specified event
   type on the EventTarget on which it's called. If the function or object is already
   in the list of event listeners for this target, the function or object is not added
   a second time.

   ```rescript
   open WebAPI
   let onScroll = _ => ()
   window->Window.addEventListener("scroll", onScroll)
   ````
   */
  @send
  external addEventListener: (t, string, DomEvent.t => unit) => unit = "addEventListener"
  @send
  external removeEventListener: (t, string, DomEvent.t => unit) => unit = "removeEventListener"
  @send external matchMedia: (t, string) => MediaQueryList.t = "matchMedia"
  @get external pageYOffset: t => float = "pageYOffset"
  @get external scrollY: t => float = "scrollY"
  @get external navigator: t => Navigator.t = "navigator"

  /**
   Window.scrollToWithOptions() scrolls to a particular set 
   of coordinates in the document.

   ````rescript
   open WebAPI
   window->Window.scrollToWithOptions({"top": 0., "left": 0., "behavior": "smooth"})->ignore
   ````
   */
  @send
  external scrollToWithOptions: (t, {"top": float, "left": float, "behavior": string}) => unit =
    "scrollTo"
}
@val external window: Window.t = "window"

module Location = {
  type t = Dom.location

  /**
   The reload() method of the Location interface reloads the current URL,
   like the Refresh button.
   The Location interface represents the location (URL) of the object
   it is linked to. Changes done on it are reflected on the object it relates to.
   Both the Document and Window interface have such a linked Location,
   accessible via Document.location and Window.location respectively.

   ```rescript
   WebAPI.location->WebAPI.Location.reload
   ```
   */
  @send
  external reload: t => unit = "reload"
}
@val @scope("window") external location: Location.t = "location"

module Clipboard = {
  type t

  @get external make: Navigator.t => t = "clipboard"
  @send external writeText: (t, string) => unit = "writeText"
}

module HtmlElement = {
  type t = Dom.htmlElement

  external ofDomElement: Dom.element => option<t> = "%identity"
  external ofDomNode: DomNode.t => option<t> = "%identity"

  @send external blur: t => unit = "blur"
  @send external click: t => unit = "click"
  @send external focus: t => unit = "focus"
}

module EventTarget = {
  type t = Dom.eventTarget

  external unsafeAsDomElement: t => Dom.element = "%identity"
}

module DataTransfer = {
  type t = Dom.dataTransfer

  @get external files: t => array<File.t> = "files"
}

module DragEvent = {
  type t = Dom.dragEvent

  @send external preventDefault: t => unit = "preventDefault"
  @send external stopPropagation: t => unit = "stopPropagation"
  @get external dataTransfer: t => Dom.dataTransfer = "dataTransfer"
  @get external target: t => EventTarget.t = "target"
}

@val external documentBody: Dom.element = "document.body"
@val external requestAnimationFrame: (float => unit) => unit = "requestAnimationFrame"
