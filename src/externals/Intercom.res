type space = [
  | #home
  | #messages
  | #help
  | #news
  | #tasks
  | #tickets
]

type updateFields = {
  name: string,
  email: Js.Nullable.t<string>,
  @as("user_id") userId: Js.Nullable.t<string>,
}

type bootParams = {
  @as("app_id") appId: string,
  @as("hide_default_launcher") hideDefaultLauncher: bool,
}

// NOTE - https://developers.intercom.com/installing-intercom/docs/intercom-javascript
@val external emit: string => unit = "window.Intercom"
@val external update: (~method: @as("update") _, updateFields) => unit = "window.Intercom"
@val external showSpace: (~method: @as("showSpace") _, space) => unit = "window.Intercom"
@val external showNewMessage: (~method: @as("showNewMessage") _, string) => unit = "window.Intercom"
@val external showArticle: (~method: @as("showArticle") _, string) => unit = "window.Intercom"
@val external boot: (~method: @as("boot") _, bootParams) => unit = "window.Intercom"
@val external on: (string, unit => unit) => unit = "window.Intercom"

let install: string => unit = %raw(`function(appId){var w=window;var ic=w.Intercom;if(typeof ic==="function"){ic('reattach_activator');ic('update',w.intercomSettings);}else{var d=document;var i=function(){i.c(arguments);};i.q=[];i.c=function(args){i.q.push(args);};w.Intercom=i;var l=function(){var s=d.createElement('script');s.type='text/javascript';s.async=true;s.src='https://widget.intercom.io/widget/' + appId;var x=d.getElementsByTagName('script')[0];x.parentNode.insertBefore(s, x);};if(document.readyState==='complete'){l();}else if(w.attachEvent){w.attachEvent('onload',l);}else{w.addEventListener('load',l,false);}}}`)
