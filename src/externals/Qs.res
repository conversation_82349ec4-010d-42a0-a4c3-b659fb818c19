@genType type t

type stringifyOptions = {
  encodeValuesOnly: bool,
  encoder: Json.t => string,
}

@module("qs")
external stringifyWithOptions: (Json.t, ~options: stringifyOptions) => t = "stringify"

type parseOptions = {
  ignoreQueryPrefix: bool,
  decoder: (string, @ignore unit, @ignore unit, ~type_: [#key | #value]) => Json.t,
}

@module("qs")
external parseWithOptions: (t, ~options: parseOptions) => Json.t = "parse"
