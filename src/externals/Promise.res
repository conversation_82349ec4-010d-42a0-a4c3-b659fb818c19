// NOTE - This module tries to be runtime free as much as possible and
// also tries to stick to JS' promise apis.
// Promise seems included in the next rescript std lib.
// > https://github.com/rescript-association/rescript-core

type t<+'a> = Js.Promise.t<'a>

@new external make: ((@uncurry (. 'a) => unit, (. 'e) => unit) => unit) => t<'a> = "Promise"

@val @scope("Promise")
external resolve: 'a => t<'a> = "resolve"
@send external then: (t<'a>, @uncurry ('a => t<'b>)) => t<'b> = "then"
@send external thenResolve: (t<'a>, @uncurry ('a => 'b)) => t<'b> = "then"
@send external finally: (t<'a>, unit => unit) => t<'a> = "finally"

@send external then2: (t<'a>, 'a => unit, Js.Promise.error => unit) => unit = "then"

@scope("Promise") @val external reject: exn => t<_> = "reject"

@scope("Promise") @val external all: array<t<'a>> => t<array<'a>> = "all"
@scope("Promise") @val external all2: ((t<'a>, t<'b>)) => t<('a, 'b)> = "all"
@scope("Promise") @val external all3: ((t<'a>, t<'b>, t<'c>)) => t<('a, 'b, 'c)> = "all"
@scope("Promise") @val external all4: ((t<'a>, t<'b>, t<'c>, t<'d>)) => t<('a, 'b, 'c, 'd)> = "all"
@scope("Promise") @val
external all5: ((t<'a>, t<'b>, t<'c>, t<'d>, t<'e>)) => t<('a, 'b, 'c, 'd, 'e)> = "all"
@scope("Promise") @val
external all6: ((t<'a>, t<'b>, t<'c>, t<'d>, t<'e>, t<'f>)) => t<('a, 'b, 'c, 'd, 'e, 'f)> = "all"

external ignore: promise<'a> => unit = "%identity"
