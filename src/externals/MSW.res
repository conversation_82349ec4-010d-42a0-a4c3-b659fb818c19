// NOTE — The module does not respect the module X = {type t} pattern
// and consequently suffers from poor IDE support.

type nodeServer
type requestHandler

@module("msw/node") @val @variadic
external setupNodeServer: array<requestHandler> => nodeServer = "setupServer"

@send @variadic external use: (nodeServer, array<requestHandler>) => unit = "use"

// > https://mswjs.io/docs/api/setup-server/listen#onunhandledrequest
type listenOptions = {onUnhandledRequest?: [#warn | #error | #bypass]}

@send external listen: (nodeServer, listenOptions) => unit = "listen"
@send external close: nodeServer => unit = "close"
@send external resetHandlers: nodeServer => unit = "resetHandlers"

type events
type eventReq = {id: string, url: Url.t}
@get external events: nodeServer => events = "events"
@send external on: (events, string, eventReq => unit) => unit = "on"

type context<'data>

type delay
@send external ctxDelay: (context<'data>, unit) => delay = "delay"
@send external ctxDelayDuration: (context<'data>, int) => delay = "delay"

module GraphQL = {
  type requestBody = {operationName: option<string>}
  type request<'queryVariables> = {
    body: requestBody,
    variables: 'queryVariables,
  }

  type link

  @module("msw") @scope("graphql")
  external makeLink: string => link = "link"

  @send
  external query: (
    link,
    string,
    @uncurry (request<'queryVariables>, context<'data> => 'result, context<'data>) => 'result,
  ) => requestHandler = "query"

  @send
  external queryWithDelay: (
    link,
    string,
    @uncurry (
      request<'queryVariables>,
      (. delay, context<'data>) => 'result,
      context<'data>,
    ) => 'result,
  ) => requestHandler = "query"

  @send external ctxData: (context<'data>, 'data) => context<'data> = "data"
}

module Rest = {
  module Request = {
    type t
    @send external json: t => Promise.t<Json.t> = "json"
  }

  type response<'data>

  @send external ctxText: (context<'data>, string) => response<'data> = "text"
  @send external ctxUnsafeObject: (context<'data>, {..}) => response<'data> = "json"
  @send external ctxStatus: (context<'data>, int) => response<'data> = "status"

  type endpoint = string

  @module("msw") @scope("rest")
  external post: (
    endpoint,
    @uncurry (Request.t, response<'data> => 'result, context<'data>) => 'result,
  ) => requestHandler = "post"

  @module("msw") @scope("rest")
  external asyncPost: (
    endpoint,
    @uncurry (Request.t, response<'data> => 'result, context<'data>) => Promise.t<'result>,
  ) => requestHandler = "post"

  @module("msw") @scope("rest")
  external postWithDelay: (
    endpoint,
    @uncurry (Request.t, (. delay, response<'data>) => 'result, context<'data>) => 'result,
  ) => requestHandler = "post"

  @module("msw") @scope("rest")
  external postWithStatus: (
    endpoint,
    @uncurry (
      Request.t,
      (. response<'data>, response<'data>) => 'result,
      context<'data>,
    ) => 'result,
  ) => requestHandler = "post"

  @module("msw") @scope("rest")
  external asyncPostWithDelayAndStatus: (
    endpoint,
    @uncurry (
      Request.t,
      (. delay, response<'data>, response<'data>) => 'result,
      context<'data>,
    ) => Promise.t<'result>,
  ) => requestHandler = "post"

  @module("msw") @scope("rest")
  external patch: (
    endpoint,
    @uncurry (Request.t, response<'data> => 'result, context<'data>) => 'result,
  ) => requestHandler = "patch"

  @module("msw") @scope("rest")
  external asyncPatch: (
    endpoint,
    @uncurry (Request.t, response<'data> => 'result, context<'data>) => Promise.t<'result>,
  ) => requestHandler = "patch"

  @module("msw") @scope("rest")
  external get: (
    endpoint,
    @uncurry (Request.t, response<'data> => 'result, context<'data>) => 'result,
  ) => requestHandler = "get"

  @module("msw") @scope("rest")
  external asyncGet: (
    endpoint,
    @uncurry (Request.t, response<'data> => 'result, context<'data>) => Promise.t<'result>,
  ) => requestHandler = "get"
}
