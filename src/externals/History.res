type location = {
  mutable pathname: string,
  mutable search: string,
  mutable hash?: string,
  mutable state?: Json.t,
}

type action = [#PUSH | #REPLACE | #POP]

// NOTE - https://github.com/remix-run/history/blob/dev/docs/api-reference.md#history.listen
type listener = location => unit
type unlistener = unit => unit

@genType
type t = {
  index?: int, // not defined with browser history
  location: location,
  length: int,
  goBack: unit => unit,
  block: (@uncurry (location, action) => option<string>) => @uncurry (unit => unit),
  push: @uncurry (string, option<Json.t>) => unit,
  replace: @uncurry (string, option<Json.t>) => unit,
  listen: listener => unlistener,
}

type options = {initialEntries: array<location>}

@module("history") external createBrowserHistory: unit => t = "createBrowserHistory"
type memoryOptions = {initialEntries?: array<string>, initialIndex?: int}
@module("history")
external createMemoryHistory: (~options: memoryOptions=?, unit) => t = "createMemoryHistory"
