// NOTE - https://docs.logrocket.com/reference

type logrocket

@module("logrocket")
external logrocket: logrocket = "default"

@module("logrocket-react")
external setupLogRocketReact: logrocket => unit = "default"

type metadata = {name: string, email: string}

@module("logrocket") @scope("default")
external identify: (string, ~metadata: metadata) => unit = "identify"

@module("logrocket") @scope("default")
external captureException: Js.Exn.t => unit = "captureException"

@module("logrocket") @scope("default")
external sessionURL: string = "sessionURL"

@module("logrocket") @scope("default")
external getSessionURL: (string => unit) => unit = "getSessionURL"
