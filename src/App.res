open Intl

// TODO - Implement better routing system with:
// + nested layouts
// + render-as-you-fetch
// + granular preloading strategies and priorities (when in view, on intent, etc.)
// + preload data and code with full/zero effort type safety
// + fine grained control over rendering, suspense and better error boundaries
// + full type safety
// + first class query param support based on QueryString prim
// + scroll restoration
// + automatic code splitting + dynamic import
// + and remove LegacyRouter module !

let maintenance = false

module AlertBar = {
  open StyleX

  type variation = [#danger | #warning]
  type textContent = {title: string, subtitle: string}
  type textStatus = Danger(textContent) | Warning(textContent)

  let styles = StyleX.create({
    "container": style(
      ~display=#flex,
      ~alignItems=#center,
      ~padding="12px",
      ~paddingRight="24px",
      (),
    ),
    "content": style(
      ~flex="1",
      ~display=#flex,
      ~flexDirection=#row,
      ~flexWrap=#wrap,
      ~justifyContent=#"space-between",
      ~alignItems=#"flex-end",
      ~paddingLeft="12px",
      (),
    ),
    "title": style(~fontSize="15px", ~fontWeight=#900, ~marginRight="6px", ()),
    "subtitle": style(~fontSize="14px", ~fontWeight=#400, ~marginRight="12px", ()),
    "textLink": style(
      ~fontSize="15px",
      ~fontWeight=#500,
      ~fontStyle=#normal,
      ~\":hover"=style(~textDecorationLine=#underline, ()),
      (),
    ),
  })

  let containerStyle = (~variation) =>
    StyleX.props([
      styles["container"],
      switch variation {
      | #danger => style(~backgroundColor=Colors.dangerColor50, ~color=Colors.neutralAlpha100, ())
      | #warning => style(~backgroundColor=Colors.extraColorK70, ~color=Colors.extraColorK80, ())
      },
    ])

  let contentStyle = StyleX.props([styles["content"]])
  let titleStyle = StyleX.props([styles["title"]])
  let subtitleStyle = StyleX.props([styles["subtitle"]])
  let textLinkStyle = (~variation) =>
    StyleX.props([
      styles["textLink"],
      switch variation {
      | #danger => style(~color=Colors.neutralColor00, ())
      | #warning => style(~color=Colors.extraColorK80, ())
      },
    ])

  @react.component
  let make = (~variation: variation, ~title, ~subtitle, ~link, ~textLink, ~onPress=?) => {
    let {style: ?containerStyle, className: ?containerClassName} = containerStyle(~variation)
    let {style: ?contentStyle, className: ?contentClassName} = contentStyle
    let {style: ?titleStyle, className: ?titleClassName} = titleStyle
    let {style: ?subtitleStyle, className: ?subtitleClassName} = subtitleStyle
    let {style: ?textLinkStyle, className: ?textLinkClassName} = textLinkStyle(~variation)

    <div style=?containerStyle className=?containerClassName>
      <Icon
        name=#warning
        fill={switch variation {
        | #danger => Colors.neutralAlpha100
        | #warning => Colors.extraColorK80
        }}
      />
      <div style=?contentStyle className=?contentClassName>
        <span>
          <span style=?titleStyle className=?titleClassName>
            {(title ++ t(":"))->React.string}
          </span>
          <span style=?subtitleStyle className=?subtitleClassName> {subtitle->React.string} </span>
        </span>
        <Inline space=#small>
          <Navigation.Link to=link ?onPress>
            <span style=?textLinkStyle className=?textLinkClassName>
              {textLink->React.string}
            </span>
          </Navigation.Link>
          <span> {"➔"->React.string} </span>
        </Inline>
      </div>
    </div>
  }

  let make = React.memo(make)
}

module NavBar = {
  @react.component
  let make = (
    ~userOrganizationName,
    ~userProfilePictureUri=?,
    ~userName,
    ~userCanUseImpersonation,
    ~userImpersonating,
    ~activeBaseRoute,
    ~activeRoute,
    ~disabledBecauseOfBillingIssue,
  ) => {
    let navigate = Navigation.useNavigate()

    let scope = Auth.useScope()
    let role = Auth.useRole()

    let testMode = Env.context() !== #production

    let badge = if !testMode && userCanUseImpersonation {
      Some(<Badge variation=#primary> {t("Support")->React.string} </Badge>)
    } else if !testMode && userImpersonating {
      Some(<Badge variation=#important> {("⚠️ " ++ t("Impersonation"))->React.string} </Badge>)
    } else if testMode && userCanUseImpersonation {
      Some(<Badge variation=#primary> {(t("Support") ++ " — Test mode")->React.string} </Badge>)
    } else if testMode && userImpersonating {
      let stringLiteral = "⚠️ " ++ t("Impersonation — Test mode")
      Some(<Badge variation=#important> {stringLiteral->React.string} </Badge>)
    } else if testMode {
      Some(<Badge> {"Test mode"->React.string} </Badge>)
    } else {
      None
    }

    let onRequestLogout = () =>
      if userImpersonating {
        Navigation.closeBrowserTab()
      } else {
        navigate(AuthRoutes.logoutRoute)
      }

    let onToggleHelpCenter = HelpCenter.showMessages

    let restrictedModeForAnalytics =
      !Auth.isAuthorizedAccess(~role, ~targetPathname=AnalyticsRouter.overviewRoute) ||
      disabledBecauseOfBillingIssue

    <Nav
      userOrganizationName
      userName
      ?badge
      ?userProfilePictureUri
      userImpersonating
      onToggleHelpCenter
      onRequestLogout
      legacyDashboardUrl={Url.make("https://dashboard.wino.fr")}
      legacyDashboardText="dashboard.wino.fr"
      helpCenterUrl={Url.make("https://help.wino.fr")}
      userSettingsRoute=SettingsRoutes.userRoute>
      <Nav.NavSection
        title={t("Activity reports")}
        active={activeBaseRoute === AnalyticsRouter.baseRoute}
        icon=#analytics_bold>
        <Nav.NavLink
          to=Route(AnalyticsRouter.overviewRoute)
          active={activeRoute === AnalyticsRouter.overviewRoute}
          label={t("General performances")}
          restrictedMode=restrictedModeForAnalytics
          disabled=disabledBecauseOfBillingIssue
        />
        {switch scope {
        | Organisation(_) =>
          <Nav.NavLink
            to=Route(AnalyticsRouter.shopsRoute)
            active={activeRoute === AnalyticsRouter.shopsRoute}
            label={t("Performances per shop")}
            restrictedMode=restrictedModeForAnalytics
            disabled=disabledBecauseOfBillingIssue
          />
        | _ => React.null
        }}
        <Nav.NavLink
          to=Route(AnalyticsRouter.topPerformingProductsRoute)
          active={activeRoute === AnalyticsRouter.topPerformingProductsRoute}
          label={t("Top sold products")}
          restrictedMode=restrictedModeForAnalytics
          disabled=disabledBecauseOfBillingIssue
        />
        <Nav.NavLink
          to=Route(AnalyticsRouter.cashFlowRoute)
          active={activeRoute === AnalyticsRouter.cashFlowRoute}
          label={t("Cash flow")}
          restrictedMode=restrictedModeForAnalytics
          disabled=disabledBecauseOfBillingIssue
        />
      </Nav.NavSection>
      <Nav.NavSection
        title={t("Products")}
        active={activeBaseRoute === Catalog->LegacyRouter.routeToPathname ||
        activeBaseRoute === Order->LegacyRouter.routeToPathname ||
        activeBaseRoute === StockActivityRouter.baseRoute ||
        activeBaseRoute === StockTransferRouter.baseRoute}
        icon=#products_bold>
        <Nav.NavLink
          to=Route(Catalog->LegacyRouter.routeToPathname)
          active={activeBaseRoute === Catalog->LegacyRouter.routeToPathname}
          restrictedMode={!Auth.isAuthorizedAccess(
            ~role,
            ~targetPathname=Order->LegacyRouter.routeToPathname,
          ) ||
          disabledBecauseOfBillingIssue}
          disabled=disabledBecauseOfBillingIssue
          label={t("Catalog")}
        />
        <Nav.NavLink
          to=Route(Order->LegacyRouter.routeToPathname)
          active={activeBaseRoute === Order->LegacyRouter.routeToPathname}
          label={t("Orders")}
          restrictedMode={!Auth.isAuthorizedAccess(
            ~role,
            ~targetPathname=Order->LegacyRouter.routeToPathname,
          ) ||
          disabledBecauseOfBillingIssue}
          disabled=disabledBecauseOfBillingIssue
        />
        <Nav.NavLink
          to=Route(StockActivityRouter.baseRoute)
          active={activeBaseRoute === StockActivityRouter.baseRoute}
          label={t("Stock activities")}
          restrictedMode={!Auth.isAuthorizedAccess(
            ~role,
            ~targetPathname=StockActivityRouter.baseRoute,
          ) ||
          disabledBecauseOfBillingIssue}
          disabled=disabledBecauseOfBillingIssue
        />
        {switch scope {
        | Organisation(_) =>
          <Nav.NavLink
            to=Route(StockTransferRouter.baseRoute)
            active={activeBaseRoute === StockTransferRouter.baseRoute}
            label={t("Stock transfers")}
            restrictedMode={!Auth.isAuthorizedAccess(
              ~role,
              ~targetPathname=StockTransferRouter.baseRoute,
            ) ||
            disabledBecauseOfBillingIssue}
            disabled=disabledBecauseOfBillingIssue
          />
        | _ => React.null
        }}
      </Nav.NavSection>
      {switch scope {
      | Single({kind: #WAREHOUSE}) => React.null
      | _ =>
        <Nav.NavSection
          title={t("Sales")}
          active={activeBaseRoute === Promotion->LegacyRouter.routeToPathname}
          icon=#sales_bold>
          <Nav.NavLink
            to=Route(SalesRouter.baseRoute)
            active={activeBaseRoute === SalesRouter.baseRoute}
            label={t("Receipts & Invoices")}
            restrictedMode={!Auth.isAuthorizedAccess(
              ~role,
              ~targetPathname=SalesRouter.baseRoute,
            ) ||
            disabledBecauseOfBillingIssue}
            disabled=disabledBecauseOfBillingIssue
          />
          <Nav.NavLink
            to=Route(AccountingRouter.baseRoute)
            active={activeBaseRoute === AccountingRouter.baseRoute}
            label={t("Accounting entries")}
            betaBadge=true
            restrictedMode={!Auth.isAuthorizedAccess(
              ~role,
              ~targetPathname=AccountingRouter.baseRoute,
            ) ||
            disabledBecauseOfBillingIssue}
            disabled=disabledBecauseOfBillingIssue
          />
          <Nav.NavLink
            to=Route(Promotion->LegacyRouter.routeToPathname)
            active={activeBaseRoute === Promotion->LegacyRouter.routeToPathname}
            label={t("Promotional campaigns")}
            restrictedMode={!Auth.isAuthorizedAccess(
              ~role,
              ~targetPathname=Promotion->LegacyRouter.routeToPathname,
            ) ||
            disabledBecauseOfBillingIssue}
            disabled=disabledBecauseOfBillingIssue
          />
        </Nav.NavSection>
      }}
      <Nav.NavSection
        title={t("Contacts")}
        active={activeBaseRoute === SupplierRoutes.baseRoute}
        icon=#contacts_bold>
        <Nav.NavLink
          to=Route(SupplierRoutes.baseRoute)
          active={activeBaseRoute === SupplierRoutes.baseRoute}
          label={t("Suppliers")}
          restrictedMode={!Auth.isAuthorizedAccess(
            ~role,
            ~targetPathname=SupplierRoutes.baseRoute,
          ) ||
          disabledBecauseOfBillingIssue}
          disabled=disabledBecauseOfBillingIssue
        />
        <Nav.NavLink
          to=Route(CustomerRouter.baseRoute)
          active={activeBaseRoute === CustomerRouter.baseRoute}
          label={t("Customers")}
          restrictedMode={!Auth.isAuthorizedAccess(
            ~role,
            ~targetPathname=CustomerRouter.baseRoute,
          ) ||
          disabledBecauseOfBillingIssue}
          disabled=disabledBecauseOfBillingIssue
        />
      </Nav.NavSection>
      <Nav.NavSection
        title={t("Settings")}
        displayHighlightLinkIcon=disabledBecauseOfBillingIssue
        active={activeBaseRoute === SettingsRoutes.baseRoute}
        icon=#settings_bold>
        <Nav.NavLink
          to={Route(SettingsRoutes.shopsRoute)}
          active={activeRoute === SettingsRoutes.shopsRoute}
          label={switch scope {
          | Single(_) => t("Shop")
          | Organisation(_) => t("Shops")
          }}
          restrictedMode={!Auth.isAuthorizedAccess(
            ~role,
            ~targetPathname=SettingsRoutes.baseRoute,
          ) ||
          disabledBecauseOfBillingIssue}
          disabled=disabledBecauseOfBillingIssue
        />
        <Nav.NavLink
          to={Route(SettingsRoutes.labelPrinterRoute)}
          active={activeRoute === SettingsRoutes.labelPrinterRoute}
          label={t("Label printer")}
          restrictedMode={!Auth.isAuthorizedAccess(
            ~role,
            ~targetPathname=SettingsRoutes.baseRoute,
          ) ||
          disabledBecauseOfBillingIssue}
          disabled=disabledBecauseOfBillingIssue
        />
        <Nav.NavLink
          to={Route(SettingsRoutes.billingAccountShowRoute())}
          active={activeRoute === SettingsRoutes.billingAccountShowRoute()}
          label={t("Subscription and billing")}
          restrictedMode={!Auth.isAuthorizedAccess(~role, ~targetPathname=SettingsRoutes.baseRoute)}
          displayHighlightLinkIcon={disabledBecauseOfBillingIssue}
        />
      </Nav.NavSection>
      {if userCanUseImpersonation {
        <>
          <Divider spaceY=#medium />
          <Nav.NavSection
            title={t("Customer support")}
            active={activeBaseRoute === AdminRouter.baseRoute}
            icon=#customer_support>
            <Nav.NavLink
              to={Route(AdminRouter.impersonationRoute)}
              active={activeRoute === AdminRouter.impersonationRoute}
              label={t("Impersonation")}
            />
            <Nav.NavLink
              to={Route(AdminRouter.shopsRoute)}
              active={activeRoute === AdminRouter.shopsRoute}
              label={t("Shops")}
            />
          </Nav.NavSection>
        </>
      } else {
        React.null
      }}
    </Nav>
  }
}

let appIndexRoute = (~userRole) => {
  let defaultIndexRoute = AnalyticsRouter.overviewRoute
  if Auth.isAuthorizedAccess(~role=userRole, ~targetPathname=defaultIndexRoute) {
    defaultIndexRoute
  } else {
    // NOTE - in the current state of the specs, Catalog page is assumed to be a permanent authorized route.
    CatalogRoutes.baseRoute
  }
}

@react.component
let make = () => {
  let navigate = Navigation.useNavigate()
  let url = Navigation.useUrl()
  let urlPath = url.path->List.fromArray
  let route = url->Navigation.urlToRoute
  let auth = Auth.useState()
  let captureEvent = SessionTracker.useCaptureEvent()

  let (routeAfterSessionExpiration, setRouteAfterSessionExpiration) = React.useState(() => route)
  let (billingIssue, setBillingIssue) = React.useState(() => None)

  let appIndexRoute = appIndexRoute(~userRole=Auth.useRole())

  React.useEffect1(() => {
    HelpCenter.setup(~appId=Env.intercomAppID())
    None
  }, [])

  let requestBillingStatuses = () =>
    BillingAccount.StatusesRequest.make()
    ->Future.tapOk(statuses => setBillingIssue(_ => statuses))
    ->Future.map(result =>
      switch result {
      | Ok(_) => Ok()
      | Error(_) => Error()
      }
    )

  let reloadAlertBar = () => requestBillingStatuses()

  React.useEffect1(() => {
    switch auth {
    | Unlogged | Logging(_) => HelpCenter.updateUserFields(~name=t("Guest"), ())
    | Logged({user: {impersonating: true}}) => HelpCenter.updateUserFields(~name=t("Imposter"), ())
    | Logged({
        user: {impersonating: false, username: email, id: userId, name},
        activeShop: None | Some({legalRepresentative: None}),
      }) =>
      HelpCenter.updateUserFields(~name, ~email, ~userId, ())
    | Logged({
        user: {impersonating: false, id: userId, username: email},
        activeShop: Some({legalRepresentative: Some(name)}),
      }) =>
      HelpCenter.updateUserFields(~name, ~email, ~userId, ())
    }
    switch auth {
    | Unlogged | Logging(_) => ()
    | Logged({user: {id: userId, name: userName}, activeShop: None}) =>
      BugTracker.updateUserFields(~userId, ~userName, ())
    | Logged({
        user: {id: userId, name: userName},
        activeShop: Some({id: shopId, name: shopName, activeWebDeviceId: deviceId}),
      }) =>
      BugTracker.updateUserFields(~userId, ~userName, ~shopId, ~shopName, ~deviceId, ())
    }
    switch auth {
    | Unlogged | Logging(_) | Logged({user: {impersonating: true}}) => ()
    | Logged({user: {id: userId, name, username: email, impersonating: false}}) =>
      SessionTracker.updateUserFields(~userId, ~name, ~email)
      SessionTracker.onSession(session => BugTracker.updateSession(session))
    }
    switch auth {
    | Unlogged | Logging(_) => ()
    | Logged(_) => requestBillingStatuses()->ignore
    }
    None
  }, [auth])

  React.useEffect2(() => {
    switch auth {
    | Logged(_) if route === "/" || Js.String2.startsWith(route, AuthRoutes.loginRoute) =>
      if (
        !Js.String2.startsWith(routeAfterSessionExpiration, AuthRoutes.baseRoute) &&
        route === AuthRoutes.loginSessionExpiredRoute
      ) {
        navigate(routeAfterSessionExpiration)
      } else {
        navigate(appIndexRoute)
      }
    | Unlogged
      if route === AuthRoutes.logoutRoute || !Js.String2.startsWith(route, AuthRoutes.baseRoute) =>
      navigate(AuthRoutes.loginRoute)
    | Unlogged if route === AuthRoutes.logoutSessionExpiredRoute =>
      navigate(AuthRoutes.loginSessionExpiredRoute)
    | Unlogged if route === AuthRoutes.logoutImpersonationFailureRoute =>
      navigate(AuthRoutes.loginImpersonationFailureRoute)
    | _ => ()
    }
    None
  }, (route, auth))

  React.useEffect1(() => {
    if !Js.String2.startsWith(route, AuthRoutes.baseRoute) {
      setRouteAfterSessionExpiration(_ => route)
    }
    None
  }, [route])

  if maintenance {
    <MaintenancePage />
  } else {
    switch (urlPath, auth) {
    | (list{basePathnam, ...subUrlPath}, _) if basePathnam === AuthRoutes.basePathname =>
      <AuthRouter subUrlPath appIndexRoute />
    | (_, Unlogged | Logging(_)) | (list{}, Logged(_)) => <LoadingPage />
    | (list{basePathname, ...subUrlPath}, Logged({user})) =>
      let baseRoute = "/" ++ basePathname
      let {
        profilePictureUri: userProfilePictureUri,
        organizationName: userOrganizationName,
        name: userName,
        canUseImpersonation: userCanUseImpersonation,
        impersonating: userImpersonating,
      } = user
      let navBar =
        <NavBar
          userOrganizationName
          ?userProfilePictureUri
          userName
          userCanUseImpersonation
          userImpersonating
          activeBaseRoute=baseRoute
          activeRoute=route
          disabledBecauseOfBillingIssue={switch billingIssue {
          | Some({issue: BillingAccount.BillingIssue.PaymentOverdue(_)}) => true
          | _ => false
          }}
        />

      let alertBarContent = switch billingIssue {
      | Some({shopId, issue: MissingPaymentMethod}) =>
        Some((
          t("No valid payment method is associated with your account"),
          t("please add or update your payment information to avoid any service interruption."),
          t("Add a payment method"),
          #danger,
          Navigation.Route(
            SettingsRoutes.billingAccountShowRouteWithModal(~shopId, ~modal=EditPaymentMethod),
          ),
        ))

      | Some({shopId, issue: InvalidBillingMandate}) =>
        Some((
          t("Confirmation of your SEPA direct debit authorization required"),
          t("Go to the 'Subscription and Billing' page to complete this step."),
          t("Confirm SEPA mandate"),
          #warning,
          Navigation.Route(
            SettingsRoutes.billingAccountShowRouteWithModal(~shopId, ~modal=ConfirmMandate),
          ),
        ))
      | Some({shopId, issue: PaymentOverdue(_)}) =>
        Some((
          t("One or more of your invoices are overdue"),
          t(
            "Your account has been blocked due to unpaid invoices. Please settle your account immediately.",
          ),
          t("Pay overdue invoice"),
          #danger,
          Navigation.Route(
            SettingsRoutes.billingAccountShowRouteWithModal(~shopId, ~modal=EditPaymentMethod),
          ),
        ))
      | Some({shopId, issue: InvoiceGracePeriod(dueDate)}) =>
        Some((
          t("One or more of your invoices are overdue"),
          template(
            t(
              "Your account will be blocked on {date} day(s) if unpaid. Please settle your account.",
            ),
            ~values={"date": dueDate},
            (),
          ),
          t("Pay overdue invoice"),
          #danger,
          Navigation.Route(SettingsRoutes.billingAccountShowRoute(~shopId, ())),
        ))
      | Some({shopId, issue: InvoicePending}) =>
        Some((
          t("One or more of your invoices require payment"),
          t(
            "Your account has unpaid invoices. Please settle your account to avoid service interruption.",
          ),
          t("Pay invoice"),
          #warning,
          Navigation.Route(SettingsRoutes.billingAccountShowRoute(~shopId, ())),
        ))
      | _ => None
      }

      let alertBar = switch alertBarContent {
      | Some(title, subtitle, textLink, variation, link) =>
        <AlertBar
          variation
          title
          subtitle
          textLink
          link
          onPress={_ => captureEvent(#alert_bar_click_payment_overdue)}
        />
      | None => React.null
      }

      <AppLayout navBar alertBar>
        {if baseRoute === AnalyticsRouter.baseRoute {
          <AnalyticsRouter subUrlPath />
        } else if baseRoute === StockActivityRouter.baseRoute {
          <StockActivityRouter subUrlPath />
        } else if baseRoute === LoadingRouter.baseRoute() {
          <LoadingRouter subUrlPath />
        } else if baseRoute === StockTransferRouter.baseRoute {
          <StockTransferRouter subUrlPath />
        } else if baseRoute === SettingsRoutes.baseRoute {
          <SettingsRouter subUrlPath appIndexRoute reloadAlertBar />
        } else if baseRoute === AdminRouter.baseRoute {
          <AdminRouter subUrlPath appIndexRoute />
        } else if baseRoute === SupplierRoutes.baseRoute {
          <SupplierRouter subUrlPath />
        } else if baseRoute === AccountingRouter.baseRoute {
          <AccountingRouter subUrlPath />
        } else if baseRoute === CustomerRouter.baseRoute {
          <CustomerRouter subUrlPath />
        } else if baseRoute === SalesRouter.baseRoute {
          <SalesRouter subUrlPath />
          // NOTE - Legacy module below
        } else if baseRoute === Order->LegacyRouter.routeToPathname {
          <OrderRouter />
        } else if baseRoute === Promotion->LegacyRouter.routeToPathname {
          <PromotionRouter />
        } else if baseRoute === Catalog->LegacyRouter.routeToPathname {
          <CatalogRouter />
        } else {
          <NotFoundPage />
        }}
      </AppLayout>
    }
  }
}
