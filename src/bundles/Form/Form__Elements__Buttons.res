module Submission = Form__Submission

module type Params = {
  type lensesState
  type submitProps = {disabled: bool, submission: Submission.t, onSubmit: unit => unit}

  let useSubmit: (
    ~handler: (option<string>, lensesState) => Future.t<Result.t<option<string>, string>>,
  ) => submitProps
  let useCancel: unit => (bool, React.callback<unit, unit>)
  let useReset: unit => React.callback<unit, unit>
}

module Make = (Params: Params) => {
  module SubmitButton = {
    @react.component
    let make = (~variation=#primary, ~size=#large, ~onSubmit, ~text) => {
      let {disabled, submission, onSubmit} = Params.useSubmit(~handler=onSubmit)

      let loading = submission == Requested
      let disabled = disabled && !loading

      <Button loading size variation disabled onPress={_ => onSubmit()}>
        {text->React.string}
      </Button>
    }
  }

  module CancelButton = {
    @react.component
    let make = (~text, ~size=#large) => {
      let (disabled, onCancel) = Params.useCancel()

      <Button variation=#neutral size disabled onPress={_ => onCancel()}>
        {text->React.string}
      </Button>
    }
  }

  module ResetButton = {
    @deprecated("Use <XForm.CancelButton /> instead") @react.component
    let make = (~variation=#secondary, ~size=#large, ~onPress=?, ~text) => {
      let onReset = Params.useReset()

      <Button
        variation
        size
        onPress={_ => {
          switch onPress {
          | Some(onPress) => onPress()
          | _ => ()
          }
          onReset()
        }}>
        {text->React.string}
      </Button>
    }
  }
}
