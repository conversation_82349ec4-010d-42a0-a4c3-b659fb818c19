// NOTE — Code that deals with strongly typed forms can quickly become walls of
// repeated text. We created this Form bundle to be both deadly simple and to
// make forms sound good leveraging ReScript's typesytem. Even the schemas
// we use are nothing more than constructors built-in in the language itself
// with a small size footprint.
// The Form bundle can handle side-effects, custom validation rules … but has some
// limits to setup complexe forms with arrays of objects or forms split over multiple
// pages (i.e. "wizards").

// NOTE — At some point, it will be possible to stop relying on React.context
// to build a form and have a straigh-forward API. We should also no longer have
// Form__Elements__* functors but use only dumb components + props drilling.
//
// One proposal:
//
// ```rescript
// module MyLenses = %lenses(
//   type state = {
//     fullName: string,
//     email: string,
//   }
// )
//
// module MyForm = Form.Make(MyLenses)
//
// let initialValues = ...
// let schema = []
//
// @react.component
// let make = () => {
//   let onSubmit = (_, {MyLenses.email: email, fullName}) =>
//     Future.make(resolve => {
//       let timeoutId = Js.Global.setTimeout(() => resolve(Ok(Some(email ++ fullName))), 300)
//       Some(() => Js.Global.clearTimeout(timeoutId))
//     })
//
//   let useFormPropState = Form.useFormPropState({
//     initialValues,
//     schema,
//     onSubmitFailure: ?onSubmitFailure->Option.map(fn),
//     onSubmitSuccess: ?onSubmitSuccess->Option.map(fn),
//   })
//
//   <FormLayout>
//     <InputTextField {...formPropState} field=Email label="Email" />
//     <InputTextField {...formPropState} field=FullName label="Full name" />
//     <SubmitButton {...formPropState} text="Submit" onSubmit />
//   </FormLayout>
// }
// ```

// TODO — add the missing interface file + merge all sub modules into this file module

module type Lenses = {
  type state
  type field<'a>
  let get: (state, field<'value>) => 'value
  let set: (state, field<'value>, 'value) => state
}

module Status = Form__Status
module Submission = Form__Submission

module Make = (FormLenses: Lenses) => {
  module Core = Form__Core.Make(FormLenses)

  module Schema = Core.Schema

  module FormLegacyProvider = Core.LegacyProvider
  module FormProvider = Core.Provider

  include Form__Elements__Inputs.Make({
    include Core
    type field<'value> = FormLenses.field<'value>
  })

  include Form__Elements__Buttons.Make({
    type lensesState = FormLenses.state
    include Core
  })

  include Form__Elements__Accessibility.Make({
    type lensesState = FormLenses.state
    include Core
  })

  let {useFormPropState, useField, useFormState, useFormDispatch} = module(Core)

  let {validate} = module(Core.Schema)
}
