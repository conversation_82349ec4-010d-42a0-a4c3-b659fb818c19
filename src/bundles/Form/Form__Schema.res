open Intl

module type Lenses = {
  type state
  type field<'a>
  let get: (state, field<'value>) => 'value
  let set: (state, field<'value>, 'value) => state
}

module Make = (Lenses: Lenses) => {
  type rec field = Field(Lenses.field<'value>): field

  type rec rule =
    | StringMin(Lenses.field<string>, int)
    | StringNotEmpty(Lenses.field<string>)
    | IntRange(Lenses.field<int>, option<int>, option<int>)
    | FloatRange(Lenses.field<float>, option<float>, option<float>)
    | PhoneNumber(Lenses.field<string>)
    | Email(Lenses.field<string>)
    | Password(Lenses.field<string>)
    | Custom(Lenses.field<'value>, Lenses.state => Result.t<unit, string>): rule
    | CustomString(Lenses.field<string>, (string, Lenses.state) => Result.t<unit, string>)
    | ArrayMin(Lenses.field<array<'item>>, int): rule

  let assertion = (field, comparator, error) =>
    if comparator {
      None
    } else {
      Some((Field(field), error))
    }

  let validate = (~schema, ~values) => {
    let errors = schema->Array.keepMap(rule =>
      switch rule {
      | StringMin(field, min) =>
        let value = Lenses.get(values, field)
        let stringLiteral = "This value must be at least {{min}} character(s)."
        let error = template(t(stringLiteral), ~values={"min": min}, ())
        assertion(field, value->String.length >= min, error)

      | StringNotEmpty(field) =>
        let value = Lenses.get(values, field)
        let error = t("Please fulfill this field.")
        assertion(field, value->String.length >= 1, error)

      | IntRange(field, min, max) =>
        let value = Lenses.get(values, field)
        switch (min, max) {
        | (Some(min), Some(max)) =>
          let stringLiteral = "This number must range between {{min}} and {{max}} inclusive."
          let error = template(t(stringLiteral), ~values={"min": min, "max": max}, ())
          assertion(field, value >= min && value <= max, error)
        | (Some(min), None) =>
          let stringLiteral = "This number must be superior or equal to {{min}}."
          let error = template(t(stringLiteral), ~values={"min": min}, ())
          assertion(field, value >= min, error)
        | (None, Some(max)) =>
          let stringLiteral = "This number must be inferior or equal to {{max}}."
          let error = template(t(stringLiteral), ~values={"max": max}, ())
          assertion(field, value <= max, error)
        | (None, None) => None
        }

      | FloatRange(field, min, max) =>
        let value = Lenses.get(values, field)
        switch (min, max) {
        | (Some(min), Some(max)) =>
          let stringLiteral = "This number must range between {{min}} and {{max}} inclusive."
          let error = template(t(stringLiteral), ~values={"min": min, "max": max}, ())
          assertion(field, value >= min && value <= max, error)
        | (Some(min), None) =>
          let stringLiteral = "This number must be superior or equal to {{min}}."
          let error = template(t(stringLiteral), ~values={"min": min -. 1.}, ())
          assertion(field, value >= min, error)
        | (None, Some(max)) =>
          let stringLiteral = "This number must be inferior or equal to {{max}}."
          let error = template(t(stringLiteral), ~values={"max": max +. 1.}, ())
          assertion(field, value <= max, error)
        | (None, None) => None
        }

      | PhoneNumber(field) =>
        let re = %re("/^(?:(?:\+|00)33|0)\s*[1-9](?:[\s.-]*\d{2}){4}$/")
        let value = Lenses.get(values, field)
        let error = t("This value is not a valid phone number.")
        assertion(field, Js.Re.test_(re, value), error)

      | Email(field) =>
        let re = %re(
          "/^(([^<>()[\]\.,;:\s@\"]+(\.[^<>()[\]\.,;:\s@\"]+)*)|(\".+\"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/"
        )
        let value = Lenses.get(values, field)
        let error = t("Invalid email address.")
        assertion(field, Js.Re.test_(re, value), error)

      | Password(field) =>
        let re = %re("/^(?=.{10,}$)(?=.*?[a-z])(?=.*?[A-Z])(?=.*?[0-9]).*$/")
        let value = Lenses.get(values, field)
        let error = t("Invalid password.")
        assertion(field, Js.Re.test_(re, value), error)

      | Custom(field, rule) =>
        switch rule(values) {
        | Error(error) => Some(Field(field), error)
        | Ok() => None
        }

      | CustomString(field, rule) =>
        switch rule(Lenses.get(values, field), values) {
        | Error(error) => Some(Field(field), error)
        | Ok() => None
        }

      | ArrayMin(field, min) =>
        let value = Lenses.get(values, field)
        let stringLiteral = t("This value must contain at least {{min}} item(s).")
        let error = template(stringLiteral, ~values={"min": min}, ())
        assertion(field, value->Array.size >= min, error)
      }
    )

    if errors->Array.size === 0 {
      Ok()
    } else {
      Error(errors)
    }
  }

  let required = (~schema, ~field) =>
    schema->Array.some(rule =>
      switch rule {
      | StringMin(fieldName, _)
      | StringNotEmpty(fieldName)
      | PhoneNumber(fieldName)
      | CustomString(fieldName, _)
      | Password(fieldName)
      | Email(fieldName) =>
        Field(fieldName) == Field(field)
      | IntRange(fieldName, _, _) => Field(fieldName) == Field(field)
      | FloatRange(fieldName, _, _) => Field(fieldName) == Field(field)
      | ArrayMin(fieldName, _) => Field(fieldName) == Field(field)
      | Custom(fieldName, _) => Field(fieldName) == Field(field)
      }
    )
}
