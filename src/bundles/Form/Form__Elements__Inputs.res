module type Params = {
  type field<'value>

  type fieldProps<'value> = {
    value: 'value,
    focused: bool,
    error: option<string>,
    required: bool,
    onChange: React.callback<'value, unit>,
    onFocus: React.callback<unit, unit>,
    onBlur: React.callback<unit, unit>,
  }

  let useField: (
    field<'value>,
    ~hideError: bool=?,
    ~hideRequired: bool=?,
    ~enableTouchAfterValueChange: bool=?,
    unit,
  ) => fieldProps<'value>
}

module Make = (Params: Params) => {
  let {useField} = module(Params)

  module InputText = {
    @react.component
    let make = (
      ~field: Params.field<string>,
      ~label,
      ~placeholder=?,
      ~hideError=false,
      ~hideRequired=false,
      ~autoTrim=true,
      ~onFocus=() => (),
    ) => {
      let onInputFocus = onFocus

      let props = useField(field, ~hideError, ~hideRequired, ())
      let {value, focused, error, required, onChange, onFocus, onBlur} = props

      <InputTextField
        label
        focused
        required
        ?placeholder
        errorMessage=?error
        autoTrim
        value
        onChange
        onFocus={_ => {
          onFocus()
          onInputFocus()
        }}
        onBlur
      />
    }
  }

  module InputSuggestionComboBox = {
    @react.component
    let make = (
      ~field: Params.field<'value>,
      ~label,
      ~placeholder=?,
      ~hideError=false,
      ~hideRequired=false,
      ~items,
    ) => {
      let props = useField(field, ~hideError, ~hideRequired, ())
      let {value, focused, error, required, onChange, onFocus, onBlur} = props

      <InputSuggestionComboBoxField
        label focused required ?placeholder errorMessage=?error items value onChange onFocus onBlur
      />
    }
  }

  module InputSelect = {
    @react.component
    let make = (
      ~field: Params.field<'value>,
      ~label,
      ~tooltip=?,
      ~disabled=?,
      ~placeholder=?,
      ~searchable=?,
      ~renderItemContent=?,
      ~sections,
      ~renderTriggerView=?,
      ~hideError=false,
      ~hideRequired=false,
    ) => {
      let props = useField(field, ~hideError, ~hideRequired, ~enableTouchAfterValueChange=true, ())
      let {value, error: errorMessage, required, onChange} = props

      <InputSelectField
        label
        required
        ?tooltip
        ?disabled
        ?placeholder
        ?searchable
        ?renderItemContent
        ?errorMessage
        ?renderTriggerView
        sections
        value
        onChange
      />
    }
  }

  module InputCountrySelect = {
    @react.component
    let make = (
      ~field: Params.field<'value>,
      ~subdivisionsIncluded=false,
      ~hideError=false,
      ~hideRequired=false,
    ) => {
      let props = useField(field, ~hideError, ~hideRequired, ~enableTouchAfterValueChange=true, ())
      let {value, error: errorMessage, required, onChange} = props

      <InputCountrySelectField required ?errorMessage subdivisionsIncluded value onChange />
    }
  }

  module InputTextArea = {
    @react.component
    let make = (~field: Params.field<string>, ~label, ~hideError=false, ~hideRequired=false) => {
      let props = useField(field, ~hideError, ~hideRequired, ())
      let {value, focused, error, required, onChange, onFocus, onBlur} = props

      <InputTextAreaField
        label focused required errorMessage=?error value onChange onFocus onBlur
      />
    }
  }

  module InputPassword = {
    @react.component
    let make = (
      ~field: Params.field<string>,
      ~label,
      ~placeholder=?,
      ~hideError=false,
      ~hideRequired=false,
      ~showTypingValidation=?,
    ) => {
      let props = useField(field, ~hideError, ~hideRequired, ())
      let {value, focused, error, required, onChange, onFocus, onBlur} = props

      <InputPasswordField
        label
        required
        focused
        ?showTypingValidation
        ?placeholder
        errorMessage=?error
        value
        onChange
        onFocus
        onBlur
      />
    }
  }

  // NOTE - (minor) caveat: this component takes a float value which forces to
  // do a lot of integer convertions and makes the states less representatives.
  module InputNumber = {
    @react.component
    let make = (
      ~field: Params.field<float>,
      ~label=?,
      ~tooltip=?,
      ~hideError=false,
      ~hideRequired=false,
      ~hideStepper=false,
      ~disabled: bool=false,
      ~placeholder=?,
      ~useGrouping=true,
      ~appender=?,
      ~minValue=?,
      ~maxValue=?,
      ~step=?,
      ~precision=2,
      ~shrinkInput=false,
      ~autoFocused=false,
    ) => {
      let props = useField(field, ~hideError, ~hideRequired, ())
      let {value, focused, error: errorMessage, required, onChange, onFocus, onBlur} = props

      <InputNumberField
        ?label
        ?tooltip
        ?placeholder
        ?appender
        ?errorMessage
        ?minValue
        ?maxValue
        ?step
        hideStepper
        disabled
        focused
        required
        useGrouping
        precision
        shrinkInput
        autoFocused
        value
        onChange
        onFocus
        onBlur
      />
    }
  }

  module InputOptionalNumber = {
    @react.component
    let make = (
      ~field: Params.field<option<float>>,
      ~label=?,
      ~tooltip=?,
      ~hideError=false,
      ~hideRequired=false,
      ~hideStepper=false,
      ~disabled: bool=false,
      ~placeholder=?,
      ~useGrouping=true,
      ~appender=?,
      ~minValue=?,
      ~maxValue=?,
      ~step=?,
      ~precision=2,
      ~shrinkInput=false,
      ~autoFocused=false,
    ) => {
      let props = useField(field, ~hideError, ~hideRequired, ())
      let {value, focused, error: errorMessage, required, onChange, onFocus, onBlur} = props

      <InputNumberField.OptionalValue
        ?label
        ?tooltip
        ?placeholder
        ?appender
        ?errorMessage
        ?minValue
        ?maxValue
        ?step
        hideStepper
        disabled
        focused
        required
        useGrouping
        precision
        shrinkInput
        autoFocused
        value
        onChange
        onFocus
        onBlur
      />
    }
  }

  module InputSegmentedControls = {
    @react.component
    let make = (
      ~field: Params.field<'value>,
      ~label,
      ~options,
      ~optionToText,
      ~hideError=false,
      ~hideRequired=false,
    ) => {
      let props = useField(field, ~hideError, ~hideRequired, ~enableTouchAfterValueChange=true, ())
      let {value, error: errorMessage, required, onChange} = props

      <InputSegmentedControlsField
        label options optionToText value ?errorMessage required onChange
      />
    }
  }

  module InputRadioGroup = {
    @react.component
    let make = (
      ~label,
      ~field: Params.field<'value>,
      ~options,
      ~optionToText,
      ~hideError=false,
      ~hideRequired=false,
    ) => {
      let props = useField(field, ~hideError, ~hideRequired, ~enableTouchAfterValueChange=true, ())
      let {value, error: errorMessage, required, onChange} = props

      <InputRadioGroupField label options optionToText value ?errorMessage required onChange />
    }
  }

  module InputPhone = {
    @react.component
    let make = (
      ~label,
      ~field: Params.field<string>,
      ~hideError=false,
      ~hideRequired=false,
      ~placeholder=?,
    ) => {
      let props = useField(field, ~hideError, ~hideRequired, ())
      let {value, focused, error, required, onChange, onFocus, onBlur} = props

      <InputTextField
        label
        focused
        required
        ?placeholder
        errorMessage=?error
        value={value->Intl.phoneNumberFormat}
        onChange
        onFocus
        onBlur
      />
    }
  }

  module InputCheckbox = {
    @react.component
    let make = (
      ~label,
      ~text,
      ~field: Params.field<bool>,
      ~hideError=false,
      ~hideRequired=false,
      ~disabled=false,
    ) => {
      let props = useField(field, ~hideError, ~hideRequired, ~enableTouchAfterValueChange=true, ())
      let {value, error: errorMessage, required, onChange} = props

      <InputCheckboxField label text value onChange ?errorMessage required disabled />
    }
  }

  module InputToggleSwitch = {
    @react.component
    let make = (
      ~label,
      ~badge=?,
      ~field: Params.field<bool>,
      ~hideError=false,
      ~hideRequired=false,
      ~disabled=false,
    ) => {
      let props = useField(field, ~hideError, ~hideRequired, ~enableTouchAfterValueChange=true, ())
      let {value, error: errorMessage, required, onChange} = props

      <InputToggleSwitchField label ?badge value onChange ?errorMessage required disabled />
    }
  }

  module InputDate = {
    @react.component
    let make = (~label, ~field: Params.field<Js.Date.t>, ~hideError=false, ~hideRequired=false) => {
      let props = useField(field, ~hideError, ~hideRequired, ~enableTouchAfterValueChange=true, ())
      let {value, error: errorMessage, required, onChange} = props

      <InputDateField label value=Some(value) required ?errorMessage onChange />
    }
  }

  module InputDateRange = {
    @react.component
    let make = (
      ~label,
      ~placeholder=?,
      ~field: Params.field<option<(Js.Date.t, Js.Date.t)>>,
      ~hideError=false,
      ~hideRequired=false,
    ) => {
      let props = useField(field, ~hideError, ~hideRequired, ~enableTouchAfterValueChange=true, ())
      let {value, error: errorMessage, required, onChange} = props

      <InputDateRangeField label value ?placeholder ?errorMessage required onChange />
    }
  }
}
