open Intl

// NOTE — The Form bundle uses Generalized Algebraic Data (GADT) under the hood.
// In a nutshell GADTs allow us more control on type safety than simple ADTs offer.
// More details here > https://sketch.sh/s/yH0MJiujNSiofDWOU85loX/
//
// A simple example of using GADT :
// type rec number<_> = Int(int): number<int> | Float(float): number<float>
// let add:
//   type a. (number<a>, number<a>) => a =
//   (a, b) =>
//     switch (a, b) {
//     | (Int(a), Int(b)) => a + b
//     | (Float(a), Float(b)) => a +. b
//     }
// let foo = add(Int(1), Int(2))
// let bar = add(Int(1), Float(2.0)) // -> error !

module type Lenses = {
  type state
  type field<'a>
  let get: (state, field<'value>) => 'value
  let set: (state, field<'value>, 'value) => state
}

module Make = (Lenses: Lenses) => {
  // TODO — The whole Form bundle should rely on our core primitives
  // AsyncData.t and AsyncResult.t. No need custom Status.t and
  // Submission.t types.
  module Status = Form__Status
  module Submission = Form__Submission

  module Schema = Form__Schema.Make(Lenses)

  type rec action =
    | ResetRequested(Lenses.state)
    | ValidationRequested
    | FieldsValuesChanged(Lenses.state => Lenses.state): action
    | FieldValueChanged(Lenses.field<'value>, 'value => 'value): action
    | CancelButtonClicked
    | SubmitButtonClicked(
        (option<string>, Lenses.state) => Future.t<Result.t<option<string>, string>>,
      ): action
    | SubmissionSucceeded(option<string>)
    | SubmissionFailed(string)
    | IdAndInitialValuesUpdated(option<string>, Lenses.state)

  // TODO — at some point, we'll have to turn to it:
  // type state<'ok, 'error> = {
  //   id?: string,
  //   initialValues: Lenses.state,
  //   values: Lenses.state,
  //   schema: array<Schema.rule>,
  //   validation: AsyncData.t<Result.t<unit, array<(Schema.field, string)>>>,
  //   submission: AsyncData.t<Result.t<'ok, 'error>>,
  // }
  type state = {
    id: option<string>,
    initialValues: Lenses.state,
    values: Lenses.state,
    status: Status.t,
    submission: Submission.t,
    schema: array<Schema.rule>,
    validation: Result.t<unit, array<(Schema.field, string)>>,
  }

  let useReducer = (~initialValues, ~schema, ~id) =>
    ReactUpdateReducer.useWithMapState(
      (state, action) =>
        switch action {
        | IdAndInitialValuesUpdated(idUpdated, values) =>
          Update({
            ...state,
            id: idUpdated,
            initialValues: values,
            values,
            status: Pristine,
            submission: Pending,
          })
        | ValidationRequested =>
          let validation = Schema.validate(~schema, ~values=state.values)
          let status = switch validation {
          | Error(_) => Status.Errored
          | Ok() => Status.Valid
          }

          // NOTE — deep equal comparaison
          if validation == state.validation {
            Update({...state, status})
          } else {
            Update({...state, status, validation})
          }
        | SubmitButtonClicked(handler) =>
          let validation = Schema.validate(~schema, ~values=state.values)
          let status = switch validation {
          | Error(_) => Status.Errored
          | Ok() => Valid
          }
          let submission = Status.isErrored(status) ? Submission.Pending : Requested
          UpdateWithSideEffects(
            {...state, status, validation, submission},
            self => {
              switch status {
              | Valid =>
                handler(state.id, state.values)->Future.get(result =>
                  self.dispatch(
                    switch result {
                    | Ok(result) => SubmissionSucceeded(result)
                    | Error(error) => SubmissionFailed(error)
                    },
                  )
                )
              | Errored =>
                let stringLiteral = "There are some errors in the form, please correct them before trying to send it again."
                self.dispatch(SubmissionFailed(t(stringLiteral)))
              | _ => ()
              }
              None
            },
          )
        | ResetRequested(values) =>
          Update({
            ...state,
            status: Pristine,
            initialValues: values,
            values,
          })
        | CancelButtonClicked =>
          Update({
            ...state,
            status: Pristine,
            submission: Pending,
            values: state.initialValues,
          })
        | FieldsValuesChanged(update) =>
          UpdateWithSideEffects(
            {...state, values: update(state.values)},
            self => {
              self.dispatch(ValidationRequested)
              None
            },
          )
        | FieldValueChanged(field, update) =>
          UpdateWithSideEffects(
            {
              ...state,
              values: state.values->Lenses.set(field, update(state.values->Lenses.get(field))),
            },
            self => {
              self.dispatch(ValidationRequested)
              None
            },
          )
        | SubmissionFailed(error) => Update({...state, submission: Failed(error)})
        | SubmissionSucceeded(ok) => Update({...state, submission: Succeeded(ok)})
        },
      () => {
        id,
        initialValues,
        values: initialValues,
        status: Pristine,
        submission: Pending,
        schema,
        validation: Schema.validate(~schema, ~values=initialValues),
      },
    )

  type formProps = {
    id?: string,
    initialValues: Lenses.state,
    schema: array<Schema.rule>,
    resetValuesAfterSubmission?: bool,
    onSubmitFailure?: string => unit,
    onSubmitSuccess?: option<string> => unit,
  }

  let useFormPropState = ({
    initialValues,
    schema,
    ?id,
    ?onSubmitSuccess,
    ?onSubmitFailure,
    ?resetValuesAfterSubmission,
  }) => {
    let (state, dispatch) = useReducer(~initialValues, ~schema, ~id)

    // TODO — when a new schema is sent, the form must be validated again
    // however, we can't do this at the moment because of the OrderContainer module
    // which doesn't have a pure schema ... and it makes no sense for the OrderContainer module
    // use the Form maker module.
    //
    // React.useEffect1(() => {
    //   if mounted.current {
    //     dispatch(ValidationRequested)
    //   }
    //   None
    // }, [schema])

    React.useEffect1(() => {
      if state.id !== id {
        dispatch(IdAndInitialValuesUpdated(id, initialValues))
      }
      None
    }, [id])

    React.useEffect1(() => {
      onSubmitFailure->Option.forEach(fn => {
        switch state.submission {
        | Failed(failure) => fn(failure)
        | Succeeded(_) | Requested | Pending => ()
        }
      })
      onSubmitSuccess->Option.forEach(fn => {
        switch state.submission {
        | Succeeded(ok) => fn(ok)
        | Failed(_) | Requested | Pending => ()
        }
      })

      None
    }, [state.submission])

    React.useEffect1(() => {
      if state.submission->Submission.isSucceeded {
        let values = switch resetValuesAfterSubmission {
        | Some(true) => initialValues
        | Some(false) | None => state.values
        }
        dispatch(ResetRequested(values))
      }

      None
    }, [state.submission])

    (state, dispatch)
  }

  type context = option<(state, ReactUpdateReducer.dispatch<action>)>
  let context: React.Context.t<context> = React.createContext(None)

  module ContextProvider = {
    let make = React.Context.provider(context)
    let makeProps = (~value, ~children, ()) =>
      {
        "value": value,
        "children": children,
      }
  }

  let useFormContext = () => {
    let value = React.useContext(context)
    switch value {
    | Some(value) => value
    | None => failwith("Could not find form context value")
    }
  }

  let useFormState = () => {
    let (state, _) = useFormContext()
    state
  }

  let useFormDispatch = () => {
    let (_, dispatch) = useFormContext()
    dispatch
  }

  let useFormValues = () => {
    let state = useFormState()
    state.values
  }

  let useFormStatus = () => {
    let state = useFormState()
    state.status
  }

  module Provider = {
    @react.component
    let make = (~children, ~propState) =>
      <ContextProvider value=Some(propState)> children </ContextProvider>
  }

  module LegacyProvider = {
    @deprecated("Use <FormProvider /> with useFormPropState() instead") @react.component
    let make = (
      ~initialValues,
      ~schema,
      ~children,
      ~id=?,
      ~onSubmitFailure=?,
      ~onSubmitSuccess=?,
      ~resetValuesAfterSubmission=?,
    ) => {
      let propState = useFormPropState({
        initialValues,
        schema,
        ?id,
        ?onSubmitFailure,
        ?onSubmitSuccess,
        ?resetValuesAfterSubmission,
      })
      <Provider propState> {children} </Provider>
    }
  }

  type fieldProps<'value> = {
    value: 'value,
    focused: bool,
    error: option<string>,
    required: bool,
    onChange: React.callback<'value, unit>,
    onFocus: React.callback<unit, unit>,
    onBlur: React.callback<unit, unit>,
  }

  // NOTE — This hook relies on a React.context and is not optimized for performance.
  // At some point, the module form should no longer use a context, but should be based
  // on props drilling to optimize performance, or on the React.useSyncExternalStore.
  let useField = (
    field,
    ~hideError=false,
    ~hideRequired=false,
    ~enableTouchAfterValueChange=?,
    (),
  ) => {
    let (state, dispatch) = useFormContext()
    let value = state.values->Lenses.get(field)

    let (focused, setFocused) = React.useState(_ => false)
    let (touched, setTouched) = React.useState(_ => false)

    let validation = switch state.validation {
    | Ok() => Ok()
    | Error(errors) =>
      switch errors->Array.getBy(error => fst(error) == Field(field)) {
      | Some(error) => Error(snd(error))
      | None => Ok()
      }
    }
    let error = switch (validation, hideError, state.status, state.submission, touched) {
    | (Ok(), _, _, _, _) | (Error(_), true, _, _, _) | (Error(_), _, _, Pending, false) => None
    | (Error(error), false, _, _, _) => Some(error)
    }
    let required = !hideRequired && Schema.required(~field, ~schema=state.schema)

    let onChange = React.useCallback2(value => {
      if touched === false && enableTouchAfterValueChange === Some(true) {
        setTouched(_ => true)
      }
      dispatch(FieldValueChanged(field, _prev => value))
    }, (enableTouchAfterValueChange, touched))
    let onFocus = React.useCallback1(_ => setFocused(_ => true), [])
    let onBlur = React.useCallback2(_ => {
      setTouched(_ => true)
      setFocused(_ => false)
    }, (touched, value))

    {value, focused, error, required, onChange, onFocus, onBlur}
  }

  type submitProps = {disabled: bool, submission: Submission.t, onSubmit: unit => unit}

  let useSubmit = (~handler) => {
    let (state, dispatch) = useFormContext()
    let {submission} = state

    let disabled = Submission.isRequested(submission)
    let onSubmit = () =>
      if !disabled {
        dispatch(SubmitButtonClicked(handler))
      }

    {disabled, submission, onSubmit}
  }

  let useCancel = () => {
    let (state, dispatch) = useFormContext()

    let disabled = state.status->Status.isPristine || state.submission->Submission.isRequested

    let onCancel = React.useCallback1(() => dispatch(CancelButtonClicked), [])

    (disabled, onCancel)
  }

  let useReset = () => {
    let (state, dispatch) = useFormContext()

    React.useCallback1(_ => dispatch(ResetRequested(state.initialValues)), [])
  }
}
