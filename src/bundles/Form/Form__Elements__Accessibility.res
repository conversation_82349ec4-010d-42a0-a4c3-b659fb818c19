module Status = Form__Status
module Submission = Form__Submission

module type Params = {
  type lensesState
  type submitProps = {disabled: bool, submission: Submission.t, onSubmit: unit => unit}

  let useFormValues: unit => lensesState
  let useFormStatus: unit => Status.t

  let useSubmit: (
    ~handler: (option<string>, lensesState) => Future.t<Result.t<option<string>, string>>,
  ) => submitProps
}

module Make = (Params: Params) => {
  module ControlEnterKey = {
    open WebAPI

    @react.component
    let make = (~onSubmit) => {
      let ref = React.useRef(Js.Nullable.null)
      let {disabled, onSubmit} = Params.useSubmit(~handler=onSubmit)

      let onKeyDown = React.useCallback1(evt =>
        switch KeyboardEvent.key(evt) {
        | "Enter" if !disabled =>
          let htmlDocument = document->Document.asHtmlDocument
          let parentDomElement =
            ref->ReactDomElement.fromRef->Option.flatMap(DomElement.parentElement)

          switch (parentDomElement, htmlDocument->HtmlDocument.activeElement) {
          | (Some(parentDomElement), Some(activeElement)) =>
            parentDomElement
            ->DomElement.getElementsByTagName("input")
            ->DomElement.fromCollectionArray
            ->Array.forEach(inputNode =>
              if inputNode === activeElement {
                onSubmit()
              }
            )
          | _ => ()
          }
        | _ => ()
        }
      , [disabled])

      React.useEffect1(() => {
        let domElement = document->Document.asDomElement
        domElement->DomElement.addKeyDownEventListener(onKeyDown)
        Some(() => domElement->DomElement.removeKeyDownEventListener(onKeyDown))
      }, [ref])

      <DivX ref />
    }
  }

  module AutoSave = {
    @react.component
    let make = (~onSubmit, ~disabled=false, ~delay=750) => {
      let {disabled: submitDisabled, onSubmit} = Params.useSubmit(~handler=onSubmit)
      let status = Params.useFormStatus()
      let values = Params.useFormValues()
      let debouncedValues = ReactUpdateDebounced.use(values, ~delay)

      ReactUpdateEffect.use1(() => {
        if Status.isValid(status) && !submitDisabled && !disabled {
          onSubmit()
        }
        None
      }, [debouncedValues])

      React.null
    }
  }
}
