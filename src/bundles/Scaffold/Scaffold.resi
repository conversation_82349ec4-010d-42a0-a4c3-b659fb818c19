module ResetFiltersButton: {
  @react.component
  let make: (~onPress: unit => unit) => React.element
}

type connectionArguments = {
  first?: int,
  last?: int,
  after?: string,
  before?: string,
}

type column<'row> = {
  name?: string,
  layout?: Table.layout,
  render: 'row => React.element,
}

type state<'filters> = {
  currentPage: int,
  previousPage: int,
  searchQuery: option<string>,
  filters: 'filters,
  connectionArguments: connectionArguments,
}

let makeTotalPages: (int, int) => int
let edgesPerPage: int
let useQueryStringPersistReducer: (JsonCodec.t<'a>, ('a, 'b) => 'a, 'a) => ('a, 'b => unit)

@deprecated("Use <LegacyResourceListPage /> with ResourceList.use() instead")
module Make: (
  Config: {
    type filters
    let useFiltersJsonCodec: unit => JsonCodec.t<filters>

    module QueryInner: {
      type t
      type t_variables

      module Raw: {
        type t
        type t_variables
      }
    }

    type queryVariableFilterBy
    let useQuery: (
      ~client: ApolloClient__React_Hooks_UseQuery.ApolloClient.t=?,
      ~context: Json.t=?,
      ~displayName: string=?,
      ~errorPolicy: ApolloClient__React_Hooks_UseQuery.ErrorPolicy.t=?,
      ~fetchPolicy: ApolloClient__React_Hooks_UseQuery.WatchQueryFetchPolicy.t=?,
      ~nextFetchPolicy: ApolloClient__React_Hooks_UseQuery.WatchQueryFetchPolicy.t=?,
      ~mapJsVariables: QueryInner.Raw.t_variables => QueryInner.Raw.t_variables=?,
      ~notifyOnNetworkStatusChange: bool=?,
      ~onCompleted: ApolloClient__React_Hooks_UseQuery.Types.parseResult<QueryInner.t> => unit=?,
      ~onError: ApolloClient__React_Hooks_UseQuery.ApolloError.t => unit=?,
      ~partialRefetch: bool=?,
      ~pollInterval: int=?,
      ~skip: bool=?,
      ~ssr: bool=?,
      QueryInner.t_variables,
    ) => ApolloClient__React_Hooks_UseQuery.QueryResult.t<
      QueryInner.t,
      QueryInner.Raw.t,
      QueryInner.t_variables,
      QueryInner.Raw.t_variables,
    >

    let makeQueryVariables: (
      QueryInner.t_variables,
      ~connectionArguments: connectionArguments,
      ~search: string=?,
      ~filterBy: queryVariableFilterBy=?,
      unit,
    ) => QueryInner.t_variables

    let makeQueryVariablesFilterBy: filters => queryVariableFilterBy

    let cursorsFromQueryData: QueryInner.t => (option<string>, option<string>)
    let totalCountFromQueryData: QueryInner.t => int

    type row
    let rowsFromQueryDataAndState: (QueryInner.t, state<filters>) => array<row>

    let keyExtractor: row => string
  },
) =>
{
  type action =
    | Navigated({
        nextPage: int,
        queryTotalCount: int,
        queryCursors: (option<string>, option<string>),
      })
    | Searched(string)
    | FiltersUpdated(Config.filters => Config.filters)
    | Reset(state<Config.filters>)

  let makeInitialState: (~filters: Config.filters) => state<Config.filters>
  let use: (unit => state<Config.filters>) => (state<Config.filters>, action => unit)

  let makeNextPage: (
    ~state: state<Config.filters>,
    ~action: LegacyPagination.action,
    ~totalPages: int,
  ) => option<int>

  let makeConnectionArguments: (
    ~currentPage: int,
    ~previousPage: int,
    ~totalCount: int,
    ~cursors: (option<string>, option<string>),
  ) => option<connectionArguments>

  let makeQueryVariables: (
    Config.QueryInner.t_variables,
    ~connectionArguments: connectionArguments,
    ~search: string=?,
    ~filterBy: Config.queryVariableFilterBy=?,
    unit,
  ) => Config.QueryInner.t_variables

  let makeQueryVariablesFilterBy: Config.filters => Config.queryVariableFilterBy
  let cursorsFromQueryData: Config.QueryInner.t => (option<string>, option<string>)
  let totalCountFromQueryData: Config.QueryInner.t => int

  let rowsFromQueryDataAndState: (Config.QueryInner.t, state<Config.filters>) => array<Config.row>
  let keyExtractor: Config.row => string

  @react.component
  let make: (
    ~title: string,
    ~subtitle: string=?,
    ~renderTitleEnd: unit => React.element=?,
    ~state: state<Config.filters>,
    ~dispatch: action => unit,
    ~columns: array<column<Config.row>>,
    ~filters: React.element=?,
    ~actions: React.element=?,
    ~banner: React.element=?,
    ~searchBar: React.element=?,
    ~emptyState: React.element,
    ~defaultQueryVariables: Config.QueryInner.t_variables,
  ) => React.element
}
