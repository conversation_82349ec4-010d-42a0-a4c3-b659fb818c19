@genType type t = Auth__Reducer.state
type auth = Auth__Types.auth
type user = Auth__Types.user
type role = Auth__Types.role
type shop = Auth__Types.shop
type scope = Auth__Types.scope

module SelectShopFilter: {
  type variation = [#primary | #secondary]

  @react.component
  let make: (
    ~variation: variation=?, // NOTE - temporary until the new design system is applied globally
    ~value: shop=?,
    ~disabledIds: array<string>=?,
    ~hideLabel: bool=?,
    ~truncateItemLabel: bool=?,
    ~onChange: option<shop> => unit=?,
  ) => React.element
}

module SelectSingleShopFilter: {
  @react.component
  let make: (
    ~value: shop,
    ~disabledIds: array<string>=?,
    ~onChange: shop => unit=?,
  ) => React.element
}

module InputSelectSingleShopField: {
  @react.component
  let make: (
    ~value: shop,
    ~required: bool,
    ~disabledIds: array<string>=?,
    ~onChange: shop => unit=?,
  ) => React.element
}

module Provider: {
  let contextProvider: React.component<
    React.Context.props<(option<Auth__Reducer.state>, Auth__Reducer.action => unit)>,
  >

  @react.component
  let make: (~children: React.element, ~initialState: t=?) => React.element
}

let encodeHttpContext: (~jwt: string) => Json.t

let getJwt: unit => option<string>
let removeJwt: unit => unit

let hasCavavinPermission: (
  ~username: string,
  ~organizationName: string,
  ~shops: array<shop>,
) => bool
let isAuthorizedAccess: (~role: option<role>, ~targetPathname: string) => bool
let useRole: unit => option<role>

let useLogUser: (~impersonate: bool=?, unit, string) => unit
let useUnlogUser: (unit, unit) => unit
let useState: unit => Auth__Reducer.state
let useDispatch: (unit, Auth__Reducer.action) => unit
let useShops: unit => array<shop>
let useActiveShop: unit => option<shop>
let useActiveShopExn: unit => shop
let useScope: unit => scope
