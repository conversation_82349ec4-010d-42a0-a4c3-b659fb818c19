type t = Auth__Reducer.state
type auth = Auth__Types.auth
type user = Auth__Types.user
type role = Auth__Types.role
type shop = Auth__Types.shop
type scope = Auth__Types.scope

module SelectShopFilter = Auth__Elements.SelectShopFilter
module SelectSingleShopFilter = Auth__Elements.SelectSingleShopFilter
module InputSelectSingleShopField = Auth__Elements.InputSelectSingleShopField

module Provider = Auth__Context.Provider

let {encodeHttpContext} = module(Auth__Http)
let {get: getJwt, remove: removeJwt} = module(Auth__Jwt)

let {hasCavavinPermission, useRole, isAuthorizedAccess} = module(Auth__Permissions)
let {
  useLogUser,
  useUnlogUser,
  useState,
  useDispatch,
  useShops,
  useActiveShop,
  useActiveShopExn,
  useScope,
} = module(Auth__Hooks)
