open Auth__Types

let {fetchUser, fetchShopsWithWebDevice} = module(Auth__Fetcher)

type jwt = string

@genType
type state =
  | Unlogged
  | Logging({jwt: string, impersonating?: bool})
  | Logged(auth)

type action =
  | LogRequested({jwt: string, impersonate?: bool})
  | LogSucceeded(auth)
  | LogFailed
  | ActiveShopSet(option<shop>)
  | Unlogged

let encodeUser = user => {
  let dict = Js.Dict.fromArray([
    ("organizationName", user.organizationName->Json.encodeString),
    ("id", user.id->Json.encodeString),
    ("name", user.name->Json.encodeString),
    ("username", user.username->Json.encodeString),
    ("canUseImpersonation", user.canUseImpersonation->Json.encodeBoolean),
    ("impersonating", user.impersonating->Json.encodeBoolean),
  ])
  switch user.profilePictureUri {
  | Some(profilePictureUri) =>
    dict->Js.Dict.set("profilePictureUri", profilePictureUri->Json.encodeString)
  | None => ()
  }
  dict->Json.encodeDict
}

let decodeUser = json => {
  let dict = json->Json.decodeDict
  switch dict {
  | Some(dict) =>
    switch {
      id: dict->Json.decodeDictFieldExn("id", Json.decodeString),
      username: dict->Json.decodeDictFieldExn("username", Json.decodeString),
      name: dict->Json.decodeDictFieldExn("name", Json.decodeString),
      organizationName: dict->Json.decodeDictFieldExn("organizationName", Json.decodeString),
      profilePictureUri: dict->Json.decodeDictOptionalField("profilePictureUri", Json.decodeString),
      canUseImpersonation: dict
      ->Json.decodeDictOptionalField("canUseImpersonation", Json.decodeBoolean)
      ->Option.getWithDefault(false),
      impersonating: dict
      ->Json.decodeDictOptionalField("impersonating", Json.decodeBoolean)
      ->Option.getWithDefault(false),
    } {
    | exception _ => None
    | user => Some(user)
    }
  | None => None
  }
}

let encodeShop = shop => {
  let dict = Js.Dict.empty()
  dict->Js.Dict.set("name", shop.name->Json.encodeString)
  dict->Js.Dict.set("id", shop.id->Json.encodeString)
  dict->Js.Dict.set("corporateName", shop.corporateName->Json.encodeString)
  dict->Js.Dict.set("activeWebDeviceId", shop.activeWebDeviceId->Json.encodeString)
  dict->Js.Dict.set(
    "kind",
    switch shop.kind {
    | #INTEGRATED => "INTEGRATED"
    | #AFFILIATED => "AFFILIATED"
    | #FRANCHISED => "FRANCHISED"
    | #WAREHOUSE => "WAREHOUSE"
    | #INDEPENDENT => "INDEPENDENT"
    }->Json.encodeString,
  )
  dict->Js.Dict.set("address", shop.address->Json.encodeString)
  dict->Js.Dict.set("postalCode", shop.postalCode->Json.encodeString)
  dict->Js.Dict.set("city", shop.city->Json.encodeString)
  dict->Js.Dict.set("country", shop.country->Json.encodeString)
  dict->Js.Dict.set("phoneNumber", shop.phoneNumber->Json.encodeString)
  dict->Js.Dict.set("email", shop.email->Json.encodeString)

  switch shop.legalRepresentative {
  | Some(legalRepresentative) =>
    dict->Js.Dict.set("legalRepresentative", legalRepresentative->Json.encodeString)
  | None => ()
  }

  switch shop.bankName {
  | Some(bankName) => dict->Js.Dict.set("bankName", bankName->Json.encodeString)
  | None => ()
  }

  switch shop.cityOfRegistryOffice {
  | Some(cityOfRegistryOffice) =>
    dict->Js.Dict.set("cityOfRegistryOffice", cityOfRegistryOffice->Json.encodeString)
  | None => ()
  }

  switch shop.website {
  | Some(website) => dict->Js.Dict.set("website", website->Json.encodeString)
  | None => ()
  }

  switch shop.fiscalYearEndClosingMonth {
  | Some(fiscalYearEndClosingMonth) =>
    dict->Js.Dict.set("fiscalYearEndClosingMonth", fiscalYearEndClosingMonth->Json.encodeString)
  | None => ()
  }

  switch shop.legalForm {
  | Some(legalForm) => dict->Js.Dict.set("legalForm", legalForm->Json.encodeString)
  | None => ()
  }

  switch shop.amountOfShareCapital {
  | Some(amountOfShareCapital) =>
    dict->Js.Dict.set("amountOfShareCapital", amountOfShareCapital->Json.encodeString)
  | None => ()
  }

  switch shop.tvaNumber {
  | Some(tvaNumber) => dict->Js.Dict.set("tvaNumber", tvaNumber->Json.encodeString)
  | None => ()
  }

  switch shop.siretNumber {
  | Some(siretNumber) => dict->Js.Dict.set("siretNumber", siretNumber->Json.encodeString)
  | None => ()
  }

  switch shop.rcsNumber {
  | Some(rcsNumber) => dict->Js.Dict.set("rcsNumber", rcsNumber->Json.encodeString)
  | None => ()
  }

  switch shop.apeNafCode {
  | Some(apeNafCode) => dict->Js.Dict.set("apeNafCode", apeNafCode->Json.encodeString)
  | None => ()
  }

  switch shop.bankCode {
  | Some(bankCode) => dict->Js.Dict.set("bankCode", bankCode->Json.encodeString)
  | None => ()
  }

  switch shop.bankAccountHolder {
  | Some(bankAccountHolder) =>
    dict->Js.Dict.set("bankAccountHolder", bankAccountHolder->Json.encodeString)
  | None => ()
  }

  switch shop.bankAccountNumber {
  | Some(bankAccountNumber) =>
    dict->Js.Dict.set("bankAccountNumber", bankAccountNumber->Json.encodeString)
  | None => ()
  }

  switch shop.bicCode {
  | Some(bicCode) => dict->Js.Dict.set("bicCode", bicCode->Json.encodeString)
  | None => ()
  }

  switch shop.ibanNumber {
  | Some(ibanNumber) => dict->Js.Dict.set("ibanNumber", ibanNumber->Json.encodeString)
  | None => ()
  }

  switch shop.logoUri {
  | Some(logoUri) => dict->Js.Dict.set("logoUri", logoUri->Json.encodeString)
  | None => ()
  }
  dict->Json.encodeDict
}

let decodeShopKind = value =>
  switch value->Json.decodeString {
  | Some("INTEGRATED") => Some(#INTEGRATED)
  | Some("AFFILIATED") => Some(#AFFILIATED)
  | Some("FRANCHISED") => Some(#FRANCHISED)
  | Some("WAREHOUSE") => Some(#WAREHOUSE)
  | _ => Some(#INDEPENDENT)
  }

let decodeShop = json =>
  switch json->Json.decodeDict {
  | Some(dict) =>
    switch {
      id: dict->Json.decodeDictFieldExn("id", Json.decodeString),
      name: dict->Json.decodeDictFieldExn("name", Json.decodeString),
      corporateName: dict->Json.decodeDictFieldExn("corporateName", Json.decodeString),
      kind: dict
      ->Json.decodeDictOptionalField("kind", decodeShopKind)
      ->Option.getWithDefault(#INDEPENDENT),
      address: dict->Json.decodeDictFieldExn("address", Json.decodeString),
      postalCode: dict->Json.decodeDictFieldExn("postalCode", Json.decodeString),
      city: dict->Json.decodeDictFieldExn("city", Json.decodeString),
      country: dict->Json.decodeDictFieldExn("country", Json.decodeString),
      phoneNumber: dict->Json.decodeDictFieldExn("phoneNumber", Json.decodeString),
      email: dict->Json.decodeDictFieldExn("email", Json.decodeString),
      logoUri: dict->Json.decodeDictOptionalField("logoUri", Json.decodeString),
      activeWebDeviceId: dict->Json.decodeDictFieldExn("activeWebDeviceId", Json.decodeString),
      legalRepresentative: dict->Json.decodeDictOptionalField(
        "legalRepresentative",
        Json.decodeString,
      ),
      bankName: dict->Json.decodeDictOptionalField("bankName", Json.decodeString),
      cityOfRegistryOffice: dict->Json.decodeDictOptionalField(
        "cityOfRegistryOffice",
        Json.decodeString,
      ),
      website: dict->Json.decodeDictOptionalField("website", Json.decodeString),
      fiscalYearEndClosingMonth: dict->Json.decodeDictOptionalField(
        "fiscalYearEndClosingMonth",
        Json.decodeString,
      ),
      legalForm: dict->Json.decodeDictOptionalField("legalForm", Json.decodeString),
      amountOfShareCapital: dict->Json.decodeDictOptionalField(
        "amountOfShareCapital",
        Json.decodeString,
      ),
      tvaNumber: dict->Json.decodeDictOptionalField("tvaNumber", Json.decodeString),
      siretNumber: dict->Json.decodeDictOptionalField("siretNumber", Json.decodeString),
      rcsNumber: dict->Json.decodeDictOptionalField("rcsNumber", Json.decodeString),
      apeNafCode: dict->Json.decodeDictOptionalField("apeNafCode", Json.decodeString),
      bankCode: dict->Json.decodeDictOptionalField("bankCode", Json.decodeString),
      bankAccountHolder: dict->Json.decodeDictOptionalField("bankAccountHolder", Json.decodeString),
      bankAccountNumber: dict->Json.decodeDictOptionalField("bankAccountNumber", Json.decodeString),
      bicCode: dict->Json.decodeDictOptionalField("bicCode", Json.decodeString),
      ibanNumber: dict->Json.decodeDictOptionalField("ibanNumber", Json.decodeString),
    } {
    | exception _exn => None
    | shop => Some(shop)
    }
  | None => None
  }

let encode = state =>
  switch state {
  | Logged(auth) => {
      let json = Js.Dict.empty()
      json->Js.Dict.set("user", auth.user->encodeUser)
      let shops = auth.shops->Array.map(encodeShop)
      json->Js.Dict.set("shops", shops->Json.encodeArray)
      switch auth.activeShop {
      | Some(shop) => json->Js.Dict.set("activeShop", shop->encodeShop)
      | None => ()
      }
      Some(json->Json.encodeDict)
    }

  | _ => None
  }

let stringify = state => state->encode->Option.map(Json.stringify)

let decode = json => {
  let auth = json->Json.decodeDict
  let user = auth->Json.flatDecodeDictField("user", decodeUser)
  let shops =
    auth
    ->Json.flatDecodeDictField("shops", Json.decodeArray)
    ->Option.map(shops => shops->Array.keepMap(decodeShop))
    ->Option.getWithDefault([])
  let activeShop =
    auth
    ->Option.flatMap(json => json->Js.Dict.get("activeShop"))
    ->Option.flatMap(decodeShop)
    ->Option.flatMap(activeShop => shops->Array.getBy(shop => shop.id === activeShop.id))

  switch (user, activeShop) {
  | (Some(user), activeShop) if shops->Array.size > 0 => Ok({user, shops, activeShop})
  | _ => Error()
  }
}

let parse = string => {
  switch string->Json.parseExn {
  | exception _ => Error()
  | json => json->decode
  }
}

let make = (state, action) =>
  switch action {
  | LogRequested({jwt, ?impersonate}) =>
    ReactUpdateReducer.UpdateWithSideEffects(
      switch (state, impersonate) {
      | (Logged(_), Some(false) | None) => state
      | _ => Logging({jwt, impersonating: ?impersonate})
      },
      self => {
        Future.all2((fetchUser(~jwt), fetchShopsWithWebDevice(~jwt)))->Future.get(result => {
          switch (state, result) {
          | (Logged(auth), (Ok(user), Ok(shops))) =>
            self.dispatch(
              LogSucceeded({
                user,
                shops,
                activeShop: switch (auth.activeShop, shops) {
                | (Some(shop), _) if shops->Array.some(({id}) => id === shop.id) => Some(shop)
                | (_, [shop]) => Some(shop)
                | _ => None
                },
              }),
            )
          | (_, (Ok(user), Ok(shops))) =>
            self.dispatch(
              LogSucceeded({
                user,
                shops,
                activeShop: switch shops {
                | [shop] => Some(shop)
                | _ => None
                },
              }),
            )
          | _ => self.dispatch(LogFailed)
          }
        })
        None
      },
    )
  | LogSucceeded(auth) =>
    switch state {
    | Logged(_) as previousState if previousState->stringify === Logged(auth)->stringify => NoUpdate
    | _ => Update(Logged(auth))
    }
  | LogFailed | Unlogged => Update(Unlogged)
  | ActiveShopSet(shop) =>
    switch state {
    | Logged(auth) => Update(Logged({...auth, activeShop: shop}))
    | _ => NoUpdate
    }
  }
