open Dom.Storage2

let storageItemKey = "jwt"

// NOTE - A default token is returned for testing purpose
let isTesting = Env.context() === #test
let testingStorageItemValue = ref(isTesting ? Some("dummy-token") : None)

let isImpersonated = jwt =>
  Jwt.decode(jwt)
  ->Option.flatMap(Json.decodeDict)
  ->Json.flatDecodeDictFieldDict("scope")
  ->Json.flatDecodeDictField("impersonationUser", Json.decodeDict)
  ->Json.flatDecodeDictFieldString("id")
  ->Option.isSome

// NOTE - A JWT needs to be stored in a safe place inside the user’s browser.
// The jwt is stored inside localStorage so it’s accessible by any script inside
// the app. If any of the third-party scripts we include in this app is compromised,
// it can access all our users’ tokens.
// > https://owasp.org/www-community/attacks/Cross_Site_Tracing
// Using HTTP-Only cookie is safer. To investigate later with the backend team.

let get = () =>
  if isTesting {
    testingStorageItemValue.contents
  } else {
    sessionStorage->getItem(storageItemKey)->Option.orElse(localStorage->getItem(storageItemKey))
  }

let set = (token, ~impersonating=?, ()) =>
  if isTesting {
    testingStorageItemValue := Some(token)
  } else if impersonating === Some(true) {
    sessionStorage->setItem(storageItemKey, token)
  } else {
    localStorage->setItem(storageItemKey, token)
  }

let remove = () =>
  if isTesting {
    testingStorageItemValue := None
  } else {
    sessionStorage->removeItem(storageItemKey)
    localStorage->removeItem(storageItemKey)
  }
