// NOTE - There is a lack of type safety in this bundle today
// the modeling is not optimal and the use of aliases has IDE issues.
// Moreover, the isolation in an X__Types module doesn't make sense.
//
// A (partial) proposal:
//
// // Auth__Shop.res
// type t = {
//   id: ShopID.t, // use abstract type
//   ...,
//   activeWebDeviceId: DeviceID.t, // use abstract type
// }
//
// // Auth__Scope.res
// type t =
//   | Organisation({activeShop: option<shop>, shops: OneAndMore(shop, array<shop>)})
//   | Single(shop)
//
// // Auth__User.res
// type t = {
//   id: UserID.t, // use abstract type
//   ...,
//   scope: scope,
// }

type user = {
  id: string,
  name: string,
  organizationName: string,
  profilePictureUri: option<string>,
  username: string,
  canUseImpersonation: bool,
  impersonating: bool,
}

type restrictedAccess = {restrictedRoutes: array<string>}
type role = Admin | LimitedUser(restrictedAccess)

type shopKind = [#INTEGRATED | #AFFILIATED | #FRANCHISED | #INDEPENDENT | #WAREHOUSE]

type shop = {
  id: string,
  kind: shopKind,
  name: string,
  corporateName: string,
  address: string,
  postalCode: string,
  city: string,
  country: string,
  phoneNumber: string,
  email: string,
  legalRepresentative: option<string>,
  logoUri: option<string>,
  activeWebDeviceId: string,
  bankName: option<string>,
  cityOfRegistryOffice: option<string>,
  website: option<string>,
  fiscalYearEndClosingMonth: option<string>,
  legalForm: option<string>,
  amountOfShareCapital: option<string>,
  tvaNumber: option<string>,
  siretNumber: option<string>,
  rcsNumber: option<string>,
  apeNafCode: option<string>,
  bankCode: option<string>,
  bankAccountHolder: option<string>,
  bankAccountNumber: option<string>,
  bicCode: option<string>,
  ibanNumber: option<string>,
}

@genType
type auth = {
  user: user,
  shops: array<shop>,
  activeShop: option<shop>,
}

type scope =
  | Organisation({activeShop: option<shop>, shops: array<shop>})
  | Single(shop)
