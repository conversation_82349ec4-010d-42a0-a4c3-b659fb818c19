open Auth__Types

let mockUser = () => {
  id: "mock-user-id",
  name: "mock-name-id",
  organizationName: "mock-organization-name",
  profilePictureUri: None,
  username: "mock-username",
  canUseImpersonation: false,
  impersonating: false,
}

let mockShop = (
  ~id="mock-shop-id",
  ~name="mock-shop-name",
  ~corporateName="mock-shop-corporateName",
  ~email="mock-shop-email",
  ~kind=#INTEGRATED,
  (),
) => {
  id,
  kind,
  name,
  corporateName,
  address: "mock-shop-address",
  postalCode: "mock-shop-postal-code",
  city: "mock-shop-city",
  country: "mock-shop-country",
  phoneNumber: "**********",
  email,
  activeWebDeviceId: "mock-shop-active-web-device-id",
  logoUri: None,
  legalRepresentative: Some("mock-shop-legal-representative"),
  bankName: Some("mock-shop-bankName"),
  cityOfRegistryOffice: Some("mock-shop-cityOfRegistryOffice"),
  website: Some("mock-shop-website"),
  fiscalYearEndClosingMonth: Some("mock-shop-fiscalYearEndClosingMonth"),
  legalForm: Some("mock-shop-legalForm"),
  amountOfShareCapital: Some("mock-shop-amountOfShareCapital"),
  tvaNumber: Some("mock-shop-tvaNumber"),
  siretNumber: Some("mock-shop-siretNumber"),
  rcsNumber: Some("mock-shop-rcsNumber"),
  apeNafCode: Some("mock-shop-apeNafCode"),
  bankCode: Some("mock-shop-bankCode"),
  bankAccountHolder: Some("mock-shop-bankAccountHolder"),
  bankAccountNumber: Some("mock-shop-bankAccountNumber"),
  bicCode: Some("mock-shop-bicCode"),
  ibanNumber: Some("mock-shop-ibanNumber"),
}

let mockAuthState = (~activeShop=Some(mockShop()), ~shops=[mockShop()], ()) => {
  user: mockUser(),
  shops,
  activeShop,
}

let mockAuthOrganisationScope = (
  ~activeShop=None,
  ~shops=[mockShop(), mockShop()],
  (),
) => Organisation({
  activeShop,
  shops,
})

let mockAuthSingleScope = (~shop=mockShop(), ()) => Single(shop)
