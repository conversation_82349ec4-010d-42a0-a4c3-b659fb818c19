open Auth__Types

module ApolloLink = ApolloClient.Link
module ApolloOperation = ApolloClient.Types.Operation
module Observable = ApolloClient.Bindings.ZenObservable.Observable

let {isImpersonated: isImpersonatedJwt} = module(Auth__Jwt)
let {encodeHttpContext} = module(Auth__Http)

module ShopsQuery = %graphql(`
  query ShopsQuery {
    shops(first: 50) {
      pageInfo {
        startCursor
        endCursor
      }
      totalCount
      edges {
        node {
          id
          type @ppxOmitFutureValue
          name
          corporateName
          address
          postalCode
          city
          country
          legalRepresentative
          email
          phoneNumber
          logoUri
          bankName
          cityOfRegistryOffice
          website
          fiscalYearEndClosingMonth
          legalForm
          amountOfShareCapital
          tvaNumber
          siretNumber
          rcsNumber
          apeNafCode
          bankCode
          bankAccountHolder
          bankAccountNumber
          bicCode
          ibanNumber
        }
      }
    }
  }
`)

module DevicesQuery = %graphql(`
  query DevicesQuery {
    devices(first: 50, filterBy: {
      name: { _equals: "Web" },
      slug: {_equals: "W"}
    }) {
      pageInfo {
        startCursor
        endCursor
      }
      edges {
        node {
          id
          shop {
            id
          }
        }
      }
      totalCount
    }
  }
`)

let httpLink = ApolloLink.HttpLink.make(~uri=_ => Env.gatewayUrl() ++ "/graphql", ())

let authMiddleware = (~jwt) => {
  ApolloLink.make((operation, forward) => {
    operation->ApolloOperation.Js_.setContext(encodeHttpContext(~jwt))->ignore
    Some(operation->forward->Observable.fromJs)
  })
}

let fetchShopsWithWebDevice = (~jwt) => {
  let client = ApolloClient.make(
    ~link=ApolloLink.from([authMiddleware(~jwt), httpLink]),
    ~cache=ApolloClient.Cache.InMemoryCache.make(),
    (),
  )

  let shopsRequest =
    client.query(~query=module(ShopsQuery), ~fetchPolicy=NetworkOnly, ShopsQuery.makeVariables())
    ->FuturePromise.fromPromise
    ->Future.map(result =>
      switch result {
      | Ok(Ok({data: {shops: {edges}}, error: None})) => Ok(edges->Array.map(edge => edge.node))
      | _ => Error()
      }
    )

  let devicesRequets =
    client.query(
      ~query=module(DevicesQuery),
      ~fetchPolicy=NetworkOnly,
      DevicesQuery.makeVariables(),
    )
    ->FuturePromise.fromPromise
    ->Future.map(result =>
      switch result {
      | Ok(Ok({data: {devices: {edges}}, error: None})) => Ok(edges->Array.map(edge => edge.node))
      | _ => Error()
      }
    )

  Future.all2((shopsRequest, devicesRequets))->Future.map(((shops, devices)) =>
    switch (shops, devices) {
    | (Ok(shops), Ok(devices)) =>
      let shopsWithWebDevice = shops->Array.keepMap(shop => {
        let maybeWebDevice = devices->Array.getBy(device => device.shop.id === shop.id)
        switch maybeWebDevice {
        | Some(webDevice) =>
          Some({
            id: shop.id,
            kind: shop.type_->Option.getWithDefault(#INDEPENDENT),
            name: shop.name,
            corporateName: shop.corporateName,
            address: shop.address,
            postalCode: shop.postalCode,
            city: shop.city,
            country: shop.country,
            email: shop.email->Option.getWithDefault("?"),
            phoneNumber: shop.phoneNumber->Option.getWithDefault("?"),
            legalRepresentative: Some(shop.legalRepresentative),
            logoUri: shop.logoUri,
            activeWebDeviceId: webDevice.id,
            bankName: shop.bankName,
            cityOfRegistryOffice: shop.cityOfRegistryOffice,
            website: shop.website,
            fiscalYearEndClosingMonth: shop.fiscalYearEndClosingMonth,
            legalForm: shop.legalForm,
            amountOfShareCapital: shop.amountOfShareCapital,
            tvaNumber: shop.tvaNumber,
            siretNumber: shop.siretNumber,
            rcsNumber: shop.rcsNumber,
            apeNafCode: shop.apeNafCode,
            bankCode: shop.bankCode,
            bankAccountHolder: shop.bankAccountHolder,
            bankAccountNumber: shop.bankAccountNumber,
            bicCode: shop.bicCode,
            ibanNumber: shop.ibanNumber,
          })
        | None => None
        }
      })
      switch shopsWithWebDevice {
      | [] => Error()
      | shopsWithWebDevice => Ok(shopsWithWebDevice)
      }
    | _ => Error()
    }
  )
}

let decodeUserResultWithJwt = (~userResult, ~jwt) => {
  let maybeUserDict = userResult->Json.decodeDict
  let maybeUserScopeDict = maybeUserDict->Json.flatDecodeDictFieldDict("scope")

  switch (maybeUserDict, maybeUserScopeDict) {
  | (Some(userDict), Some(userScopeDict)) =>
    switch {
      id: userDict->Json.decodeDictFieldExn("id", Json.decodeString),
      username: userDict->Json.decodeDictFieldExn("username", Json.decodeString),
      name: userDict->Json.decodeDictFieldExn("name", Json.decodeString),
      organizationName: userDict->Json.decodeDictFieldExn("organizationName", Json.decodeString),
      profilePictureUri: userDict->Json.decodeDictOptionalField(
        "profilePictureUri",
        Json.decodeString,
      ),
      canUseImpersonation: userScopeDict
      ->Json.decodeDictOptionalField("canUseImpersonation", Json.decodeBoolean)
      ->Option.getWithDefault(false),
      impersonating: isImpersonatedJwt(jwt),
    } {
    | exception _ => Error()
    | user => Ok(user)
    }
  | _ => Error()
  }
}

let fetchUser = (~jwt) =>
  Fetch.make(
    Env.gatewayUrl() ++ "/users/me",
    {
      headers: Fetch.Headers.fromObject({
        "Content-Type": "application/json",
        "Authorization": `Bearer ${jwt}`,
      }),
    },
  )
  ->Promise.then(Fetch.Response.json)
  ->FuturePromise.fromPromise
  ->Future.mapError(_ => ())
  ->Future.mapResult(userResult => decodeUserResultWithJwt(~userResult, ~jwt))
