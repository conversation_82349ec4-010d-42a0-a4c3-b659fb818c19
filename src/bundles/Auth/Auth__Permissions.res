// NOTE - in the future the permissions will most likely be managed by defined roles
// with associated presets of permissions on the different routers/pages.

open Auth__Types

// NOTE - temporary hardcoded users before serverside handles roles.
let limitedUsers = [
  (
    "<EMAIL>",
    {restrictedRoutes: ["/analytics", "/sales", "/accounting", "/settings"]},
  ),
]

let useRole = () => {
  switch Auth__Hooks.useState() {
  | Logged({user: {username}}) =>
    let role = switch limitedUsers->Array.getBy(((limitedUsername, _)) =>
      limitedUsername === username
    ) {
    | Some((_, restrictedAccess)) => LimitedUser(restrictedAccess)
    | None => Admin
    }
    Some(role)
  | Unlogged | Logging(_) => None
  }
}

let isAuthorizedAccess = (~role, ~targetPathname) =>
  switch role {
  | Some(Admin) | None => true
  | Some(LimitedUser({restrictedRoutes})) =>
    restrictedRoutes->Array.every(restrictedRoute =>
      targetPathname->Js.String2.startsWith(restrictedRoute) === false
    )
  }

let cavavinCorporateNameRegex = %re(`/^witradis$/i`)
let cavavinOrganizationNameRegex = %re(`/cavavin/i`)
let cavavinMailDomainRegex = %re(`/^[a-zA-Z0-9._%+-]+@cavavin\.([a-z]{2})$/`)
let winoMailDomainRegex = %re(`/^[a-zA-Z0-9._%+-]+@wino\.fr$/`)

// NOTE - heuristics to detect whether the account is a cavavin account or not
// such permissions or roles will be ultimately handled by the backend.
let hasCavavinPermission = (~username, ~organizationName, ~shops) => {
  let shopsCorporateNameOk =
    shops->Array.every(shop =>
      cavavinCorporateNameRegex->Js.Re.test_(shop.Auth__Types.corporateName)
    )
  let emailOk = cavavinMailDomainRegex->Js.Re.test_(username)
  let cavavinOrganizationNameOk = cavavinOrganizationNameRegex->Js.Re.test_(organizationName)
  let winoEmailOk = cavavinMailDomainRegex->Js.Re.test_(username)

  shopsCorporateNameOk || emailOk || winoEmailOk || cavavinOrganizationNameOk
}
