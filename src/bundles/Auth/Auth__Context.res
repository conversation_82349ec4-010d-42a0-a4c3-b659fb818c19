open Dom.Storage2

module Reducer = Auth__Reducer
module Mock = Auth__Mock
module Jwt = Auth__Jwt

let storageKey = "auth"

let context = React.createContext((None, _ => ignore()))

let use = () => {
  let (state, dispatch) = React.useContext(context)
  switch state {
  | None => failwith("Could not find Auth context value")
  | Some(state) => (state, dispatch)
  }
}

let createState = (~initialState=?, ()) => {
  let mounted = React.useRef(false)

  let (state, dispatch) = ReactUpdateReducer.useWithMapState(Reducer.make, () =>
    switch (initialState, Jwt.get()) {
    | (Some(_) | None, None) => Unlogged
    | (Some(state), Some(_)) => state
    | (None, Some(jwt)) =>
      switch localStorage->getItem(storageKey)->Option.map(string => string->Reducer.parse) {
      | Some(Ok({user: {canUseImpersonation: false}} as auth)) => Logged(auth)
      | Some(Ok({user: {canUseImpersonation: true}})) =>
        Logging({jwt, impersonating: Jwt.isImpersonated(jwt)})
      | None | Some(Error(_)) => Unlogged
      }
    }
  )

  React.useEffect0(() => {
    if !mounted.current && Env.context() !== #test {
      switch (state, Jwt.get()) {
      | (Logged({user: {impersonating: false}}), Some(jwt)) => dispatch(LogRequested({jwt: jwt}))
      | (Logging({impersonating}), Some(jwt)) =>
        dispatch(LogRequested({jwt, impersonate: impersonating}))
      | _ => ()
      }
    }
    None
  })

  React.useEffect1(() => {
    if mounted.current {
      switch (state, state->Reducer.stringify) {
      | (Logged({user: {impersonating: false}}), Some(itemValue)) =>
        localStorage->setItem(storageKey, itemValue)
      | _ => ()
      }
    }

    switch state {
    | Logging({jwt, ?impersonating}) => Jwt.set(jwt, ~impersonating?, ())
    | Unlogged =>
      localStorage->removeItem(storageKey)
      Jwt.remove()
    | _ => ()
    }
    None
  }, [state])

  React.useEffect0(() => {
    mounted.current = true
    Some(() => mounted.current = false)
  })

  (Some(state), dispatch)
}

module ContextProvider = {
  let make = React.Context.provider(context)
}

module Provider = {
  let contextProvider = React.Context.provider(context)

  @react.component
  let make = (~children, ~initialState=?) => {
    let initialState = switch (initialState, Env.context()) {
    | (None, #test) => Some(Reducer.Logged(Mock.mockAuthState()))
    | (None, _) => None
    | _ => initialState
    }

    let value = createState(~initialState?, ())
    <ContextProvider value> children </ContextProvider>
  }
}
