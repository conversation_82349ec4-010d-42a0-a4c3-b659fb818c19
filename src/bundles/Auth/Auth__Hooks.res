// NOTE - the hooks below expose the modelling
// of the auth bundle which is not ok (see note in Auth__Types).
// At some point, we could also stop using these context hooks
// and rely on props-drilling only.

open Auth__Types

module Context = Auth__Context

let useLogUser = (~impersonate=false, ()) => {
  let (_, dispatch) = Context.use()
  jwt => LogRequested({jwt, impersonate})->dispatch
}

let useUnlogUser = () => {
  let (_, dispatch) = Context.use()
  () => Unlogged->dispatch
}

let useState = () => {
  let (state, _) = Context.use()
  state
}

let useDispatch = () => {
  let (_, dispatch) = Context.use()
  dispatch
}

let useShops = () => {
  let (state, _) = Context.use()
  switch state {
  | Logged({shops}) => shops
  | _ => []
  }
}

let useActiveShop = () => {
  let (state, _) = Context.use()
  switch state {
  | Logged({activeShop}) => activeShop
  | _ => None
  }
}

let useActiveShopExn = () => useActiveShop()->Option.getExn

let useScope = () => {
  let shops = useShops()
  let activeShop = useActiveShop()

  React.useMemo2(
    () =>
      shops->Array.length > 1
        ? Organisation({activeShop, shops})
        : Single(activeShop->Option.getExn),
    (shops, activeShop),
  )
}
