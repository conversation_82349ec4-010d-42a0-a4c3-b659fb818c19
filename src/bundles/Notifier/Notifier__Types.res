type notificationKind = [#success | #error | #warning]

type notificationContent = {
  message: string,
  details: option<array<string>>,
  kind: notificationKind,
}

type notification = {
  id: Uuid.t,
  content: notificationContent,
  onClose: unit => unit,
}

type notificationRaw = {
  id: Uuid.t,
  content: notificationContent,
}

type notificationAction =
  | Success(string)
  | Error(string)
  | Warning(string)

type clearPolicy =
  | ClearOnHistoryChanges
  | KeepOnHistoryChanges
