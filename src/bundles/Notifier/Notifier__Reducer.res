open Notifier__Types

type node = {
  kind: notificationKind,
  message: string,
  details: option<array<string>>,
  reset: bool,
}

type action =
  | AddRequested(node)
  | RemoveRequested(Uuid.t)
  | ClearRequested

type state = list<notificationRaw>

let initialState = list{}

let reducer = (state, action) =>
  switch action {
  | AddRequested({reset: true, message, details, kind}) =>
    list{
      {
        id: Uuid.make(),
        content: {
          message,
          details,
          kind,
        },
      },
    }
  | AddRequested({message, details, kind}) =>
    state->List.add({
      id: Uuid.make(),
      content: {
        message,
        details,
        kind,
      },
    })
  | RemoveRequested(id) => state->List.keep(n => n.id !== id)
  | ClearRequested => initialState
  }
