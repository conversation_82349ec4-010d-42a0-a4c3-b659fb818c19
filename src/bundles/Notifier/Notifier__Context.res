module Context = {
  type dispatch = Notifier__Reducer.action => unit
  type context = (option<Notifier__Reducer.state>, dispatch)

  let context: React.Context.t<context> = React.createContext((None, _ => ignore()))

  let use = () => {
    let (state, dispatch) = React.useContext(context)

    switch state {
    | None => failwith("Could not find Notifier context value")
    | Some(state) => (state, dispatch)
    }
  }
}

module Provider = {
  let make = React.Context.provider(Context.context)
  let makeProps = (~value, ~children, ()) =>
    {
      "value": value,
      "children": children,
    }
}
