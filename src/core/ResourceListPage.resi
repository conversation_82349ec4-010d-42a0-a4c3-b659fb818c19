module ResourceListSearchBar: {
  @react.component
  let make: (~placeholder: string, ~value: string=?, ~onChange: string => unit) => React.element
}

@react.component
let make: (
  ~title: string,
  ~shopFilter: React.element=?,
  ~filters: array<React.element>=?,
  ~activeFiltersCount: int=?,
  ~mainAction: React.element=?,
  ~actions: React.element=?,
  ~searchBar: React.element=?,
  ~tableData: AsyncResult.t<array<'item>, unit>,
  ~tableColumns: Array.t<Table.column<'item>>,
  ~tableKeyExtractor: 'item => ReactStately.key,
  ~tableEmptyState: React.element,
  ~paginationCurrentPage: int,
  ~paginationTotalCount: int,
  ~paginationTotalPages: int,
  ~paginationLoading: bool,
  ~paginationLastDirection: ResourceListPagination.direction,
  ~tableSortDescriptor: ReactStately.Table.sortDescriptor=?,
  ~onTableSortChange: ReactStately.Table.sortDescriptor => unit=?,
  ~onRequestPaginate: ResourceListPagination.action => unit,
  ~onRequestResetFilters: unit => unit=?,
) => React.element
