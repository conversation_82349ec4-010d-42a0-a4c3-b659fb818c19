module Datetime: {
  type t = Js.Date.t
  exception CannotParseDatetime
  exception CannotSerializeDatetime
  let parse: Json.t => Js.Date.t
  let serialize: Js.Date.t => Json.t
}

module Text: {
  type t = string
  exception CannotParseText
  exception CannotSerializeText
  let parse: Json.t => string
  let serialize: string => Json.t
}

module CKU: {
  type t = string
  exception CannotParse<PERSON>ku
  exception CannotSerializeCku
  let parse: Json.t => string
  let serialize: string => Json.t
}
