type t = [#"catalog-page-settings" | #"catalog-labels-options"]

type methods<'state> = {
  read: unit => option<'state>,
  store: 'state => unit,
  useRead: unit => option<'state>,
  usePersistOnChange: ('state, ~disabled: bool=?, ~debounceDelay: int=?, unit) => bool,
}

let read = (~key: t, ~decoder) =>
  try {
    switch Dom.Storage2.localStorage
    ->Dom.Storage2.getItem((key :> string))
    ->Option.map(Json.parseExn) {
    | Some(jsonState) =>
      switch decoder(jsonState) {
      | Ok(persistedState) => Some(persistedState)
      | Error(_invalidState) =>
        Dom.Storage2.localStorage->Dom.Storage2.removeItem((key :> string))
        None
      }
    | None => None
    }
  } catch {
  | _parsingError =>
    Dom.Storage2.localStorage->Dom.Storage2.removeItem((key :> string))
    None
  }

let store = (state, ~key: t, ~encoder) => {
  let jsonStringifiedState = encoder(state)->Json.stringify
  Dom.Storage2.localStorage->Dom.Storage2.setItem((key :> string), jsonStringifiedState)
}

let useRead = (~key, ~decoder) => {
  let (persistedState, _) = React.useState(() => read(~key, ~decoder))
  persistedState
}

let usePersistOnChange = (
  state,
  ~key,
  ~disabled=false,
  ~debounceDelay=0,
  ~encoder,
  ~decoder,
  (),
) => {
  let initialValues = useRead(~key, ~decoder)
  let (persisted, setPersisted) = React.useState(() => initialValues->Option.isSome)
  let debouncedValues = ReactUpdateDebounced.use(state, ~delay=debounceDelay)
  let debouncing = state != debouncedValues // NOTE - deep comparison

  ReactUpdateEffect.use1(() => {
    if !disabled && !debouncing {
      store(state, ~key, ~encoder)
      setPersisted(_ => true)
    }
    None
  }, [debouncing])

  persisted && !debouncing
}

let make = (~key, ~encoder, ~decoder) => {
  let read = () => read(~key, ~decoder)
  let store = state => store(state, ~key, ~encoder)
  let useRead = () => useRead(~key, ~decoder)
  let usePersistOnChange = (state, ~disabled=false, ~debounceDelay=0, ()) =>
    usePersistOnChange(state, ~disabled, ~debounceDelay, ~key, ~encoder, ~decoder, ())

  {read, store, useRead, usePersistOnChange}
}
