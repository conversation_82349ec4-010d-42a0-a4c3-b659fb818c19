// NOTE — With ReScript v12, it should be possible
// to use tagged variants for better DX
// module ArticleID = {
//   type t =
//     | @as("8202106") HowCentralizeCatalog
//     | @as("8155377") PrepareImportInventory
//     ...
// }
module ArticleID = {
  type t = string
}

let howCentralizeCatalog = "8202106"
let prepareImportInventory = "8155377"
let automaticOrdering = "7068311"
let orderLabelSheets = "7118898"
let setupLabelPrinterAndStarPrinterOnline = "9113414"
let getLabelPrintFeature = "9204256"
let termsAndConditions = "8889741"
let setupAndExportAccountingEntries = "********"
let useAutomaticOrderFeature = "7068311"

let gettingStartedGuideLink = "https://quirky-amaranthus-c07.notion.site/Vos-premiers-pas-chez-Wino-9ea8719601494a748a4ee1a83a7d31b9"

let install = (~appId) => Intercom.install(appId)
let setup = (~appId) => Intercom.boot({appId, hideDefaultLauncher: true})

let updateUserFields = (~name, ~userId=?, ~email=?, ()) =>
  Intercom.update({
    name,
    userId: Js.Nullable.fromOption(userId),
    email: Js.Nullable.fromOption(email),
  })

let show = () => Intercom.emit("show")
let showArticle = id => Intercom.showArticle(id)
let showHome = () => Intercom.showSpace(#home)
let showMessages = () => Intercom.showSpace(#messages)
let openNewMessage = (~content) => Intercom.showNewMessage(content)
