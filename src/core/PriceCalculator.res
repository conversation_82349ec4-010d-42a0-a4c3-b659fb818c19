// NOTE - to keep a perfect pitch precision, rounding isn't handled in this module,
// typically the rounding is deffered to the latest step, at formatting/displaying time.

open! Big.Operators

let sumManyCharges = charges =>
  charges
  ->Array.reduce(Big.fromFloat(0.), (acc, charge) => acc->Big.plus(Big.fromFloat(charge)))
  ->Big.toFloat

module Purchase = {
  let toNetValue = (value, ~discount) => {
    let discount = discount >= 0. ? discount : 0.
    let result = Big.toFloat(Big.fromFloat(value) -. Big.fromFloat(discount))
    result >= 0. ? result : 0.
  }
  let fromNetValue = (value, ~discount) => {
    let discount = discount >= 0. ? discount : 0.
    let result = Big.toFloat(Big.fromFloat(value) +. Big.fromFloat(discount))
    result >= 0. ? result : 0.
  }

  let toValueWithCharges = (value, ~charges) => {
    let charges = charges >= 0. ? charges : 0.
    Big.toFloat(Big.fromFloat(value) +. Big.fromFloat(charges))
  }
  let fromValueWithCharges = (value, ~charges) => {
    let charges = charges >= 0. ? charges : 0.
    Big.toFloat(Big.fromFloat(value) -. Big.fromFloat(charges))
  }
}

// NOTE - The retail price is baseline: value excluding tax, with charges.
// NOTE - the xBack suffix indicates the method is used to return to
// the originate value, typically used on an 'onChange' handler.
module Retail = {
  type priceExclTaxWithCharges = float

  module Charges = {
    let remove = (value, ~charges) => {
      let charges = charges >= 0. ? charges : 0.
      Big.toFloat(Big.fromFloat(value) -. Big.fromFloat(charges))
    }
    let addBack = (value, ~charges) => {
      let charges = charges >= 0. ? charges : 0.
      Big.toFloat(Big.fromFloat(value) +. Big.fromFloat(charges))
    }
  }

  module Tax = {
    let add = (value, ~taxRate) => {
      let taxRate = taxRate >= 0. ? taxRate : 0.
      Big.toFloat(
        Big.round(
          Big.fromFloat(value) +.
          Big.fromFloat(value) *. Big.fromFloat(taxRate) /. Big.fromFloat(100.),
          10,
        ),
      )
    }
    let removeBack = (value, ~taxRate) => {
      let taxRate = taxRate >= 0. ? taxRate : 0.
      Big.toFloat(
        Big.round(
          Big.fromFloat(value) *.
          Big.fromFloat(100.) /.
          (Big.fromFloat(100.) +. Big.fromFloat(taxRate)),
          10,
        ),
      )
    }
  }

  module Margin = {
    let compute = (value, ~purchasePrice) => {
      let purchasePrice = purchasePrice >= 0. ? purchasePrice : 0.
      Big.toFloat(Big.fromFloat(value) -. Big.fromFloat(purchasePrice))
    }
  }

  module Rate = {
    type t = Coefficient | MarginRate | MarkupRate

    let apply = (priceValue, ~kind, ~purchasePrice, ~charges, ~taxRate) => {
      let priceExcludingChargesAndTax = priceValue->Charges.remove(~charges)
      let rateValue = switch kind {
      | Coefficient if purchasePrice !== 0. =>
        let priceIncludingChargesAndTax = priceExcludingChargesAndTax->Tax.add(~taxRate)
        Some(Big.fromFloat(priceIncludingChargesAndTax) /. Big.fromFloat(purchasePrice))
      | MarkupRate if purchasePrice !== 0. =>
        Some(
          (Big.fromFloat(priceExcludingChargesAndTax) -. Big.fromFloat(purchasePrice)) /.
          Big.fromFloat(purchasePrice) *.
          Big.fromFloat(100.),
        )
      | MarginRate if priceExcludingChargesAndTax !== 0. =>
        Some(
          (Big.fromFloat(priceExcludingChargesAndTax) -. Big.fromFloat(purchasePrice)) /.
          Big.fromFloat(priceExcludingChargesAndTax) *.
          Big.fromFloat(100.),
        )
      | _ => None
      }
      rateValue->Option.map(Big.toFloat)
    }

    let undo = (rateValue, ~kind, ~purchasePrice, ~charges, ~taxRate) => {
      let priceValue = switch (kind, rateValue) {
      | (Coefficient, coefficient) =>
        let priceIncludingChargesAndTax = Big.toFloat(
          Big.fromFloat(coefficient) *. Big.fromFloat(purchasePrice),
        )
        let priceExcludingChargesAndTax = priceIncludingChargesAndTax->Tax.removeBack(~taxRate)
        Some(priceExcludingChargesAndTax)
      | (MarkupRate, markupRate) =>
        Some(
          Big.toFloat(
            Big.fromFloat(purchasePrice) *.
            (Big.fromFloat(1.) +. Big.fromFloat(markupRate) /. Big.fromFloat(100.)),
          ),
        )
      | (MarginRate, marginRate) =>
        let operand = Big.fromFloat(1.) -. Big.fromFloat(marginRate) /. Big.fromFloat(100.)
        if Big.toFloat(operand) !== 0. {
          Some(Big.toFloat(Big.fromFloat(purchasePrice) /. operand))
        } else {
          None
        }
      }
      priceValue->Option.map(value => value->Charges.addBack(~charges))
    }
  }
}
