open StyleX
open Intl

module ResourceListFiltersBarLayout = {
  let styles = StyleX.create({
    "FiltersBarLayout_root": style(
      ~display=#flex,
      ~alignItems=#center,
      ~justifyContent=#"space-between",
      ~overflow=#hidden,
      ~padding=`0 ${Spaces.largePx}`,
      ~borderBottom="1px solid " ++ Colors.neutralColor15,
      ~boxSizing=#"border-box",
      ~transition="height .2s cubic-bezier(.4, 0, .2, 1), opacity .15s cubic-bezier(.4, 0, .2, 1)",
      (),
    ),
  })
  let styleProps = (~opened) =>
    StyleX.props([
      styles["FiltersBarLayout_root"],
      style(~height=opened ? "60px" : "0px", ~opacity=opened ? 1. : 0., ()),
    ])

  @react.component
  let make = (~filters as children, ~opened, ~onRequestToggle, ~onRequestReset=?) => {
    let {?style, ?className} = styleProps(~opened)

    // NOTE - not yet binded https://developer.mozilla.org/fr/docs/Web/HTML/Global_attributes/inert
    <ReactAria.Spread props={!opened ? Obj.magic({"inert": "true"}) : None}>
      <div ?style ?className>
        <SliderTrack layout=Adaptative gap=#small compact=true>
          {children->React.array}
        </SliderTrack>
        <Inline>
          <Separator space=#xnormal />
          {switch onRequestReset {
          | Some(onRequestReset) =>
            <TextIconButton
              variation=#normal size=#xsmall icon=#reset onPress={_ => onRequestReset()}>
              {t("Erase")->React.string}
            </TextIconButton>
          | None =>
            <TextIconButton
              variation=#normal size=#xsmall icon=#close_light onPress={_ => onRequestToggle()}>
              {t("Close")->React.string}
            </TextIconButton>
          }}
        </Inline>
      </div>
    </ReactAria.Spread>
  }
}

module ResourceListSearchBar = {
  let styles = StyleX.create({
    "SearchBar_root": style(
      ~height="40px",
      ~overflow=#hidden,
      ~display=#flex,
      ~flexDirection=#row,
      ~alignItems=#center,
      ~boxSizing=#"border-box",
      ~padding=`0 ${Spaces.normalPx}`,
      ~gap=Spaces.smallPx,
      ~border="1px solid",
      ~transition="width .2s cubic-bezier(.6, 0.7, .5, 1)",
      (),
    ),
    "SearchBar_searchIconWrapper": style(
      ~display=#flex,
      ~justifyContent=#center,
      ~alignItems=#center,
      ~position=#absolute,
      ~zIndex=3,
      ~top="-20px",
      ~left="-12.5px",
      ~height="40px",
      ~width="40px",
      (),
    ),
    "SearchBar_searchIconSvg": style(~position=#absolute, ~positionArea=#left, ()),
    "SearchBar_textInput": style(
      ~flex="1",
      ~minWidth="50%",
      ~width="150px",
      ~maxWidth="150px",
      ~height="35px",
      ~alignContent=#center,
      ~textOverflow=#ellipsis,
      ~overflow=#hidden,
      ~cursor=#text,
      (),
    ),
  })

  let styleProps = (~focused, ~active, ~hovered) =>
    StyleX.props([
      styles["SearchBar_root"],
      style(
        ~width=active ? "20%" : "40px",
        ~maxWidth="209px",
        ~cursor=active ? #auto : #pointer,
        ~backgroundColor=TextFieldStyle.backgroundColor(~disabled=false),
        ~borderRadius=TextFieldStyle.borderRadiusPx,
        ~borderColor=TextFieldStyle.borderColor(
          ~focused,
          ~hovered,
          ~errored=false,
          ~disabled=false,
        ),
        (),
      ),
    ])

  let searchIconWrapperStyleProps = () => StyleX.props([styles["SearchBar_searchIconWrapper"]])
  let searchIconSvgStyleProps = () => StyleX.props([styles["SearchBar_searchIconSvg"]])

  let textInputStyleProps = (~active) =>
    StyleX.props([
      styles["SearchBar_textInput"],
      style(
        ~display=#flex,
        ~opacity=active ? 1. : 0.,
        ~transition="opacity .1s ease",
        ~color=TextFieldStyle.color(~disabled=false),
        ~fontSize=TextFieldStyle.fontSizePx,
        (),
      ),
    ])
  // NOTE - necessary on firefox/safari
  let textInputDOMStyle = ReactDOM.Style.make(~flex="1", ~width="0", ~marginLeft="16px", ())

  @react.component
  let make = React.memo((~placeholder, ~value="", ~onChange) => {
    let inputRef = React.useRef(Js.Nullable.null)
    let (ref, hovered) = Hover.use()
    let (focused, setFocused) = React.useState(() => false)
    let (searchQuery, setSearchQuery) = React.useState(() => value)
    let debouncedSearchQuery = ReactUpdateDebounced.use(searchQuery, ~delay=500)

    ReactUpdateEffect.use1(() => {
      onChange(debouncedSearchQuery)
      None
    }, [debouncedSearchQuery])

    ReactUpdateEffect.use1(() => {
      setSearchQuery(_ => value)
      None
    }, [value])

    let focusInput = () =>
      switch inputRef.current->Js.Nullable.toOption {
      | Some(input) => ReactDomElement.focus(input)
      | None => ()
      }
    let blurInput = () =>
      switch inputRef.current->Js.Nullable.toOption {
      | Some(input) => ReactDomElement.blur(input)
      | None => ()
      }

    let onKeyDown = React.useCallback1(event => {
      let key = WebAPI.KeyboardEvent.key(event)
      let inputs = WebAPI.document->WebAPI.Document.querySelectorAll("input")
      let allowFocusInput =
        switch inputs {
        | Some([]) | None => true
        | Some([searchInput]) if inputRef.current === searchInput->Js.Nullable.return => true
        | _ => false
        } &&
        ((%re(`/^[a-zA-Z0-9]$/`)->Js.Re.test_(key) || key === "Backspace") &&
        !WebAPI.KeyboardEvent.ctrlKey(event))
      if allowFocusInput {
        focusInput()
      } else if key === "Escape" && focused {
        setSearchQuery(_ => "")
        blurInput()
      } else {
        ()
      }
    }, [focused])

    React.useEffect1(() => {
      let document = WebAPI.document->WebAPI.Document.asDomElement
      document->WebAPI.DomElement.addKeyDownEventListener(onKeyDown)
      Some(_ => document->WebAPI.DomElement.removeKeyDownEventListener(onKeyDown))
    }, [onKeyDown])

    let active = focused || searchQuery !== ""

    let {?style, ?className} = styleProps(~focused, ~active, ~hovered)
    let {
      style: ?searchIconWrapperStyle,
      className: ?searchIconWrapperClassName,
    } = searchIconWrapperStyleProps()
    let {style: ?searchIconSvgStyle, className: ?searchIconSvgClassName} = searchIconSvgStyleProps()
    let {style: ?textInputStyle, className: ?textInputClassName} = textInputStyleProps(~active)

    <div ref={ref->ReactDOM.Ref.domRef} ?style ?className>
      <Touchable disabled=active excludeFromTabOrder=true onPress={_ => focusInput()}>
        <div style=?searchIconWrapperStyle className=?searchIconWrapperClassName>
          <Svg
            width="17.5"
            height="17.5"
            viewBox="0 0 20 20"
            style=?searchIconSvgStyle
            className=?searchIconSvgClassName>
            <Svg.Path
              d="M9.5 2.5a7 7 0 015.292 11.583l3.062 3.064a.5.5 0 01-.708.706l-3.061-3.063A7 7 0 119.5 2.5zm0 1a6 6 0 100 12 6 6 0 000-12z"
              fill={hovered && !active ? Colors.neutralColor90 : Colors.neutralColor50}
            />
          </Svg>
        </div>
      </Touchable>
      <div style=?textInputStyle className=?textInputClassName>
        <TextInput
          inputRef={inputRef->ReactDOM.Ref.domRef}
          style=textInputDOMStyle
          placeholder
          value=searchQuery
          onChange={value => setSearchQuery(_ => value)}
          onFocus={_ => setFocused(_ => true)}
          onBlur={_ => setFocused(_ => false)}
        />
      </div>
      <IconButton
        name=#close_medium
        size=17.
        color=Colors.neutralColor25
        hoveredColor=Colors.neutralColor50
        onPress={_ => setSearchQuery(_ => "")}
      />
    </div>
  })
}

module ResourceListHeader = {
  module HeaderFiltersBarTrigger = {
    let styles = StyleX.create({
      "HeaderFiltersBarTrigger_root": style(
        ~display=#flex,
        ~alignItems=#center,
        ~columnGap=Spaces.xnormalPx,
        ~height="40px",
        ~paddingInline=Spaces.normalPx,
        ~border="1px solid " ++ Colors.neutralColor20,
        ~borderRadius="5px",
        ~backgroundColor=Colors.neutralColor00,
        ~boxSizing=#"border-box",
        ~\":hover"=style(~borderColor=Colors.neutralColor25, ()),
        (),
      ),
      "HeaderFiltersBarTrigger_focused": style(
        ~borderColor=Colors.neutralColor30,
        ~\":hover"=style(~borderColor=Colors.neutralColor50, ()),
        (),
      ),
      "HeaderFiltersBarTrigger_active": style(
        ~backgroundColor=Colors.neutralColor10,
        ~borderColor=Colors.neutralColor50,
        ~\":hover"=style(~borderColor=Colors.neutralColor90, ()),
        (),
      ),
      "HeaderFiltersBarTrigger_pressed": style(
        ~backgroundColor=Colors.neutralColor05,
        ~borderColor=Colors.neutralColor30,
        (),
      ),
    })

    let styleProps = (~active, ~focused, ~pressed) =>
      StyleX.props([
        styles["HeaderFiltersBarTrigger_root"],
        active ? styles["HeaderFiltersBarTrigger_active"] : style(),
        focused ? styles["HeaderFiltersBarTrigger_focused"] : style(),
        pressed && !active ? styles["HeaderFiltersBarTrigger_pressed"] : style(),
      ])

    @react.component
    let make = (~filtersBarOpened, ~activeFiltersCount, ~onRequestToggle) => {
      let (ref, hovered) = Hover.use()
      let (pressed, setPressed) = React.useState(() => false)

      let buttonProps = {
        ReactAria.Button.elementType: #div,
        onPressStart: _ => setPressed(_ => true),
        onPressEnd: _ => setPressed(_ => false),
      }
      let active = activeFiltersCount > 0
      let focused = filtersBarOpened
      let {?style, ?className} = styleProps(~active, ~focused, ~pressed)
      let iconFill = hovered ? Colors.neutralColor90 : Colors.neutralColor50

      let handleToggle = _ => {
        WebAPI.window
        ->WebAPI.Window.scrollToWithOptions({"top": 0., "left": 0., "behavior": "smooth"})
        ->ignore
        if !filtersBarOpened || (filtersBarOpened && WebAPI.window->WebAPI.Window.scrollY < 15.) {
          onRequestToggle()
        }
      }

      <Touchable ref ariaProps=buttonProps onPress=handleToggle>
        <div ?style ?className>
          <Icon name=#filter size=15. fill=iconFill />
          <Inline space=#small>
            <TextStyle variation={!active ? #normal : #neutral} wrap=false>
              {t("Filters")->React.string}
            </TextStyle>
            {if active {
              <CountBadge size=#small>
                {Int.toString(activeFiltersCount)->React.string}
              </CountBadge>
            } else {
              React.null
            }}
          </Inline>
        </div>
      </Touchable>
    }
  }

  let styles = StyleX.create({
    "Header_root": style(
      ~position=#sticky,
      ~top="0",
      ~zIndex=3,
      ~display=#flex,
      ~flexDirection=#row,
      ~alignItems=#center,
      ~gap=Spaces.largePx,
      ~height="60px",
      ~padding=`0 ${Spaces.largePx}`,
      ~borderBottom="1px solid " ++ Colors.neutralColor15,
      ~boxSizing=#"border-box",
      ~background=Colors.neutralColor00,
      ~boxShadow=`0px 2px 6px -2px ${Colors.neutralColor15}`,
      (),
    ),
  })

  @react.component
  let make = (
    ~title,
    ~shopFilter,
    ~filters,
    ~activeFiltersCount,
    ~mainAction,
    ~actions,
    ~searchBar,
    ~onRequestResetFilters=?,
  ) => {
    let (filtersBarOpened, setFiltersBarOpened) = React.useState(() => activeFiltersCount > 0)

    let handleFiltersBar = () => setFiltersBarOpened(opened => !opened)

    let {?style, ?className} = StyleX.props([styles["Header_root"]])

    <>
      <div ?style ?className>
        <Title level=#2> {title->React.string} </Title>
        <Inline grow=true>
          <Inline space=#small grow=true>
            {mainAction}
            {if mainAction !== React.null {
              <Separator size=#large />
            } else {
              React.null
            }}
            {shopFilter}
            <HeaderFiltersBarTrigger
              filtersBarOpened activeFiltersCount onRequestToggle=handleFiltersBar
            />
            {searchBar}
            <Box spaceRight=#xsmall />
          </Inline>
          {actions}
        </Inline>
      </div>
      <ResourceListFiltersBarLayout
        filters
        opened=filtersBarOpened
        onRequestReset=?onRequestResetFilters
        onRequestToggle=handleFiltersBar
      />
    </>
  }
}

module ResourceListLayout = {
  let styles = StyleX.create({
    "root": style(
      ~zIndex=1,
      ~display=#flex,
      ~flexDirection=#column,
      ~flex="1",
      ~height="100%",
      ~background=Colors.neutralColor00,
      ~borderLeft="1px solid " ++ Colors.neutralColor15,
      (),
    ),
  })

  @react.component
  let make = (~children) => {
    let {?style, ?className} = StyleX.props([styles["root"]])

    <div ?style ?className> children </div>
  }

  let make = React.memo(make)
}

@react.component
let make = (
  ~title,
  ~shopFilter=React.null,
  ~filters=[],
  ~activeFiltersCount=0,
  ~mainAction=React.null,
  ~actions=React.null,
  ~searchBar=React.null,
  ~tableData,
  ~tableColumns,
  ~tableKeyExtractor,
  ~tableEmptyState,
  ~paginationCurrentPage,
  ~paginationTotalCount,
  ~paginationTotalPages,
  ~paginationLoading,
  ~paginationLastDirection,
  ~tableSortDescriptor=?,
  ~onTableSortChange=?,
  ~onRequestPaginate,
  ~onRequestResetFilters=?,
) => {
  <ResourceListLayout>
    <ResourceListHeader
      title
      searchBar
      shopFilter
      filters
      activeFiltersCount
      mainAction
      actions
      ?onRequestResetFilters
    />
    <div style={ReactDOMStyle.make(~display="flex", ~flex="1", ~paddingTop=Spaces.xnormalPx, ())}>
      <TableView
        columns=tableColumns
        data=tableData
        keyExtractor=tableKeyExtractor
        placeholderEmptyState=tableEmptyState
        hideReloadingPlaceholder=true
        hideCard=true
        compactRows=true
        sortDescriptor=?tableSortDescriptor
        onSortChange=?onTableSortChange
      />
    </div>
    <ResourceListPagination
      currentPage=paginationCurrentPage
      totalPages=paginationTotalPages
      totalCount=paginationTotalCount
      loading=paginationLoading
      lastDirection=paginationLastDirection
      itemsPerPage=ResourceList.defaultEdgesPerPage
      onRequestPaginate
    />
  </ResourceListLayout>
}
