let edgesPerPage = 10

let totalPages = (totalCount, nodesPerPage) =>
  if nodesPerPage <= 0 || totalCount <= 0 {
    1
  } else if mod(totalCount, nodesPerPage) === 0 {
    totalCount / nodesPerPage
  } else {
    totalCount / nodesPerPage + 1
  }

type connectionArguments = {first?: int, last?: int, after?: string, before?: string}

let connectionArguments = (~currentPage, ~previousPage, ~totalCount, ~cursors) =>
  switch (currentPage, cursors) {
  | (1, _) => Some({first: edgesPerPage})
  | (currentPage, (Some(startCursor), _)) if currentPage === previousPage - 1 =>
    Some({last: edgesPerPage, before: startCursor})
  | (currentPage, (_, Some(endCursor))) if currentPage === previousPage + 1 =>
    Some({first: edgesPerPage, after: endCursor})
  | (currentPage, _) if currentPage === totalPages(totalCount, edgesPerPage) =>
    Some({last: edgesPerPage})
  | _ => None
  }

type state<'filters> = {
  currentPage: int,
  previousPage: int,
  searchQuery?: string,
  filters: 'filters,
  connectionArguments: connectionArguments,
}

type action<'filters> =
  | Navigated({nextPage: int, totalCount: int, cursors: (option<string>, option<string>)})
  | Searched(string)
  | FiltersUpdated('filters => 'filters)
  | Reset(state<'filters>)

type dispatch<'filters> = action<'filters> => unit

let initialState = (~filters) => {
  currentPage: 1,
  previousPage: -1,
  filters,
  connectionArguments: {first: 10},
}

let reducer = (state, action) =>
  switch action {
  | Navigated({nextPage, totalCount, cursors}) =>
    let currentPage = nextPage
    let previousPage = state.currentPage
    let maybeConnectionArguments = connectionArguments(
      ~currentPage,
      ~previousPage,
      ~totalCount,
      ~cursors,
    )
    switch maybeConnectionArguments {
    | Some(connectionArguments) => {
        ...state,
        currentPage,
        previousPage,
        connectionArguments,
      }
    | None => state
    }
  | Searched(searchQuery) => {
      searchQuery: ?switch searchQuery {
      | "" => None
      | _ => Some(searchQuery)
      },
      currentPage: 1,
      previousPage: -1,
      connectionArguments: {first: 10},
      filters: state.filters,
    }
  | FiltersUpdated(updateStateFilters) => {
      filters: updateStateFilters(state.filters),
      currentPage: 1,
      previousPage: -1,
      connectionArguments: {first: 10},
      searchQuery: ?state.searchQuery,
    }
  | Reset(newState) => newState
  }

let nextPage = (~state, ~action, ~totalPages) =>
  switch (action, state.currentPage, totalPages) {
  | (_, 0 | 1, 0 | 1) => None
  | (LegacyPagination.First, currentPage, _) if currentPage >= 1 => Some(1)
  | (Prev, currentPage, totalPages) if currentPage > totalPages => Some(totalPages)
  | (Prev, currentPage, _) if currentPage > 1 => Some(currentPage - 1)
  | (Next, currentPage, totalPages) if currentPage > totalPages => Some(totalPages)
  | (Next, currentPage, _) if currentPage >= 1 && currentPage < totalPages => Some(currentPage + 1)
  | (Last, currentPage, _) if currentPage >= 1 => Some(totalPages)
  | _ => None
  }

module CodecState = {
  let encoder = ({currentPage, previousPage, ?searchQuery, filters, connectionArguments}) => (
    Some(currentPage),
    Some(previousPage),
    searchQuery,
    Some(filters),
    connectionArguments.first,
    connectionArguments.last,
    connectionArguments.after,
    connectionArguments.before,
  )

  let decoder = (
    initialFilters,
    (
      currentPage,
      previousPage,
      searchQuery,
      filters,
      connectionArgumentsFirst,
      connectionArgumentsLast,
      connectionArgumentsAfter,
      connectionArgumentsBefore,
    ),
  ) => Ok({
    currentPage: currentPage->Option.getWithDefault(1),
    previousPage: previousPage->Option.getWithDefault(-1),
    ?searchQuery,
    filters: filters->Option.getWithDefault(initialFilters),
    connectionArguments: {
      first: ?connectionArgumentsFirst,
      last: ?connectionArgumentsLast,
      after: ?connectionArgumentsAfter,
      before: ?connectionArgumentsBefore,
    },
  })

  let value = (~filtersJsonCodec, ~initialFilters) =>
    JsonCodec.object8(
      encoder,
      decoder(initialFilters),
      JsonCodec.field("page", JsonCodec.int)->JsonCodec.optional,
      JsonCodec.field("previousPage", JsonCodec.int)->JsonCodec.optional,
      JsonCodec.field("search", JsonCodec.string)->JsonCodec.optional,
      JsonCodec.field("filters", filtersJsonCodec)->JsonCodec.optional,
      JsonCodec.field("first", JsonCodec.int)->JsonCodec.optional,
      JsonCodec.field("last", JsonCodec.int)->JsonCodec.optional,
      JsonCodec.field("after", JsonCodec.string)->JsonCodec.optional,
      JsonCodec.field("before", JsonCodec.string)->JsonCodec.optional,
    )
}

let useQueryStringPersistReducer = (codec, reducer, initialState) => {
  let navigate = Navigation.useNavigate()
  let url = Navigation.useUrl()

  let initialState = React.useMemo0(() => {
    let urlState = url.query->QueryString.parse->JsonCodec.decodeWith(codec)

    switch (urlState, url.query->QueryString.toString) {
    | (Ok(_), "") | (Error(_), _) => initialState
    | (Ok(state), _) => state
    }
  })

  let (state, dispatch) = React.useReducer(reducer, initialState)

  React.useEffect1(() => {
    let query = state->JsonCodec.encodeWith(codec)->QueryString.stringify
    let route = url.pathname ++ "?" ++ query->QueryString.toString

    if query !== url.query {
      navigate(route, ~replace=true)
    }
    None
  }, [state])

  (state, dispatch)
}

let use = (~initialState, ~filtersJsonCodec) =>
  useQueryStringPersistReducer(
    CodecState.value(~filtersJsonCodec, ~initialFilters=initialState.filters),
    reducer,
    initialState,
  )
