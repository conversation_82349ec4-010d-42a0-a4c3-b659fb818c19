module ActionsBar: {
  @genType @react.component
  let make: (~items: array<React.element>) => React.element
}

module NotificationBanner: {
  @genType @react.component
  let make: (~value: result<string, string>, ~onRequestClose: unit => unit) => React.element
}

@genType type variation = [#standard | #compact]

@genType @react.component
let make: (
  ~variation: variation=?,
  ~title: string,
  ~subtitle: string=?,
  ~actionsBar: React.element=?,
  ~notificationBanner: React.element=?,
  ~statusBadge: React.element=?,
  ~children: React.element,
) => React.element
