@val external processEnv: Js.Dict.t<string> = "import.meta.env"

let stringOptionalEnvVar = name => processEnv->Js.Dict.get(name)
let stringEnvVar = name => stringOptionalEnvVar(name)->Option.getExn

let gatewayUrl = () => stringEnvVar("VITE_GATEWAY_URL")
let analyticsUrl = () => stringEnvVar("VITE_ANALYTICS_URL")
let cavavinUrl = () => stringEnvVar("VITE_CAVAVIN_URL")
let sheetUrl = () => stringEnvVar("VITE_SHEET_URL")
let pdfUrl = () => stringEnvVar("VITE_PDF_URL")
let sentryDsn = () => stringOptionalEnvVar("VITE_SENTRY_DSN")

let intercomAppID = () => "m4f5bzdq"
let logrocketAppID = () => "u0ukt2/wino"
let postHogAppKey = () => "phc_hesp7A4JQKciBO9H7krMBC9Hm6Ru71HmAqmR2rZyAQD"

let isTestEnv = () => stringOptionalEnvVar("VITEST")->Option.isSome

let context = () =>
  if isTestEnv() {
    #test
  } else {
    switch stringOptionalEnvVar("VITE_CONTEXT") {
    | Some("production") => #production
    | Some("staging") => #staging
    | Some(_) | None => #dev
    }
  }
