// NOTE - This module tracks native JavaScript exception (panic("…")) and
// native OCaml/ReScript exception (via raise(exn)).
// For a good error tracking with <PERSON><PERSON> (js), it's better to use panic("…") introduced
// in the new drop-in standard library for ReScript. The panic exception is a native
// JavaScript exception that is not intended to be caught and handled. Compared to a
// ReScript exception this will give a better stack trace and debugging experience.
// Our project currently relies on Belt which uses raise and not panic, the migration
// from Belt to RescriptCore will also help.

let reportErrorMessage = Sentry.captureMessage
let reportException = Sentry.captureException

let contextIsOk = Env.context() === #production || Env.context() === #staging

let updateUserFields = (~userId, ~userName, ~shopId=?, ~shopName=?, ~deviceId=?, ()) =>
  if contextIsOk {
    Sentry.setUser({
      userId,
      username: userName,
      shopId,
      shopName,
      deviceId,
    })
  }

let updateSession = session =>
  if contextIsOk {
    Sentry.configureScope(scope => scope["setExtra"](. "sessionURL", session))
  }

module Boundary = {
  @react.component
  let make = (~children) => {
    if contextIsOk {
      let fallback = error =>
        switch error {
        | otherError =>
          switch Js.Exn.asJsExn(otherError) {
          | Some(exn) => LogRocket.captureException(exn)
          | None => ()
          }

          <FatalErrorPage />
        }

      <Sentry.ErrorBoundary fallback> children </Sentry.ErrorBoundary>
    } else {
      children
    }
  }

  let make = React.memo(make)
}
