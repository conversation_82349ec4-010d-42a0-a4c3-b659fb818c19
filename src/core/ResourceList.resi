let defaultEdgesPerPage: int

let totalPages: (int, int) => int

type connectionArguments = {first?: int, last?: int, after?: string, before?: string}
let connectionArguments: (
  ~currentPage: int,
  ~previousPage: int,
  ~totalCount: int,
  ~cursors: (option<string>, option<string>),
  ~edgesPerPage: int=?,
  unit,
) => option<connectionArguments>

type state<'filters, 'sorting, 'extra> = {
  currentPage: int,
  previousPage: int,
  searchQuery?: string,
  filters: 'filters,
  sorting: 'sorting,
  extra?: 'extra,
  connectionArguments: connectionArguments,
}
type action<'filters, 'sorting, 'extra> =
  | Navigated({nextPage: int, totalCount: int, cursors: (option<string>, option<string>)})
  | Searched(string)
  | FiltersUpdated('filters => 'filters)
  | SortingUpdated('sorting)
  | ExtraUpdated('extra)
  | Reset(state<'filters, 'sorting, 'extra>)
type dispatch<'filters, 'sorting, 'extra> = action<'filters, 'sorting, 'extra> => unit
let initialState: (
  ~first: int=?,
  ~filters: 'filters,
  ~sorting: 'sorting,
  ~extra: 'extra=?,
  unit,
) => state<'filters, 'sorting, 'extra>
let reducer: (
  state<'filters, 'sorting, 'extra>,
  action<'filters, 'sorting, 'extra>,
) => state<'filters, 'sorting, 'extra>

let nextPage: (
  ~state: state<'filters, 'sorting, 'extra>,
  ~action: ResourceListPagination.action,
  ~totalPages: int,
) => option<int>

let use: (
  ~initialState: state<'filters, 'sorting, 'extra>,
  ~filtersJsonCodec: JsonCodec.t<'filters>,
  ~sortingJsonCodec: JsonCodec.t<'sorting>,
  ~extraJsonCodec: JsonCodec.t<'extra>=?,
  unit,
) => (state<'filters, 'sorting, 'extra>, action<'filters, 'sorting, 'extra> => unit)
