let sumManyCharges: array<float> => float

module Purchase: {
  let toNetValue: (float, ~discount: float) => float
  let fromNetValue: (float, ~discount: float) => float

  let toValueWithCharges: (float, ~charges: float) => float
  let fromValueWithCharges: (float, ~charges: float) => float
}

// NOTE - The retail price is baseline: value excluding tax, with charges.
// NOTE - the xBack suffix indicates the method is used to return to
// the originate value, typically used on an 'onChange' handler.
module Retail: {
  type priceExclTaxWithCharges = float

  module Charges: {
    let remove: (float, ~charges: float) => float
    let addBack: (float, ~charges: float) => float
  }

  module Tax: {
    let add: (float, ~taxRate: float) => float
    let removeBack: (float, ~taxRate: float) => float
  }

  module Margin: {
    let compute: (float, ~purchasePrice: float) => float
  }

  module Rate: {
    type t = Coefficient | MarginRate | MarkupRate

    let apply: (
      priceExclTaxWithCharges,
      ~kind: t,
      ~purchasePrice: float,
      ~charges: float,
      ~taxRate: float,
    ) => option<float>

    let undo: (
      float,
      ~kind: t,
      ~purchasePrice: float,
      ~charges: float,
      ~taxRate: float,
    ) => option<priceExclTaxWithCharges>
  }
}
