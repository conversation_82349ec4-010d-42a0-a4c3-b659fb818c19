// NOTE - Scala<PERSON> must obey to GraphQL customField PPX pattern:
// > https://graphql-ppx.com/docs/custom-fields

module Datetime = {
  exception CannotParseDatetime
  exception CannotSerializeDatetime

  type t = Js.Date.t

  let parse = json =>
    switch json->Json.decodeString->Option.map(Js.Date.fromString) {
    | Some(datetime) =>
      if datetime->Js.Date.getTime->Js.Float.isNaN {
        raise(CannotParseDatetime)
      } else {
        datetime
      }
    | _ => raise(CannotParseDatetime)
    }

  let isDate: Js.Date.t => bool = %raw(`date => date instanceof Date`)
  let serialize = date =>
    if date->isDate {
      date->Js.Date.toISOString->Json.encodeString
    } else {
      raise(CannotSerializeDatetime)
    }
}

module Text = {
  exception CannotParseText
  exception CannotSerializeText

  type t = string

  let parse = json =>
    switch json->Json.decodeString {
    | Some(string) => string
    | None => raise(CannotParseText)
    }
  let serialize = text => {
    switch text->Js.typeof {
    | "string" => text->Json.encodeString
    | _ => raise(CannotSerializeText)
    }
  }
}

module CKU = {
  exception CannotParseCku
  exception CannotSerializeCku

  type t = string

  let parse = json => {
    switch json
    ->Json.decodeString
    ->Option.flatMap(cku => cku->Uuid.fromString)
    ->Option.map(uuid => (uuid->Uuid.version, uuid->Uuid.toString)) {
    | exception _ => raise(CannotParseCku)
    | Some(4, uuidv4) => uuidv4
    | _ => raise(CannotParseCku)
    }
  }
  let serialize = cku => {
    switch cku->Uuid.fromString->Option.map(Uuid.version) {
    | Some(4) => cku->Json.encodeString
    | _ => raise(CannotSerializeCku)
    }
  }
}
