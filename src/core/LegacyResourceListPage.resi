module ResetFiltersButton: {
  @react.component
  let make: (~onPress: unit => unit) => React.element
}

@react.component
let make: (
  ~title: string,
  ~subtitle: string=?,
  ~filters: React.element=?,
  ~actions: React.element=?,
  ~banner: React.element=?,
  ~searchBar: React.element=?,
  ~tableData: AsyncResult.t<array<'item>, unit>,
  ~tableColumns: Array.t<Table.column<'item>>,
  ~tableKeyExtractor: 'item => ReactStately.key,
  ~tableEmptyState: React.element,
  ~paginationCurrentPage: int,
  ~paginationTotalPages: int,
  ~onRequestPaginate: LegacyPagination.action => unit,
) => React.element
