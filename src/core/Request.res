exception UnauthorizedRequestError
exception UnknownRequestError
exception RequestError(string)
exception ClientError(string)

type invalidRequestFailure = {
  message: string,
  kind: string,
  data: option<Json.t>,
}

type error =
  | UnexpectedServerError
  | ServerError(string)
  | ClientError(string)
  | InvalidRequestFailures(array<invalidRequestFailure>)
  | MalformedResponse
  | JwtAuthenticationRedirection

let serverErrorMessage = Intl.t("<PERSON><PERSON> did not respond, please reiterate your request.")

let decodeInvalidRequestFailure = (json: Json.t) => {
  let dict = Json.decodeDict(json)

  let message = dict->Json.flatDecodeDictFieldString("message")
  let kind = dict->Json.flatDecodeDictFieldString("kind")
  let data = dict->Json.flatDecodeDictFieldDict("data")->Option.map(Js.Json.object_)

  switch (message, kind, data) {
  | (Some(message), Some(kind), data) => Some({message, kind, data})
  | _ => None
  }
}

let decodeInvalidRequestFailures = arr => {
  let failures = arr->Array.keepMap(decodeInvalidRequestFailure)
  if failures->Array.size === arr->Array.size {
    Some(failures)
  } else {
    None
  }
}

let decodeResult = json => {
  let data = Option.orElse(
    Option.orElse(
      json->Json.decodeDict->Json.flatDecodeDictFieldDict("data")->Option.map(Js.Json.object_),
      json->Json.decodeDict->Json.flatDecodeDictFieldArray("data")->Option.map(Js.Json.array),
    ),
    json
    ->Json.decodeDict
    ->Json.flatDecodeDictField("data", Json.decodeNull)
    ->Option.map(_ => Json.encodedNull),
  )
  let errors = json->Json.decodeDict->Json.flatDecodeDictFieldArray("errors")

  switch (data, errors) {
  | (Some(data), None) => Ok(Some(data))
  | (None, Some(jsonFailures)) => Error(jsonFailures->decodeInvalidRequestFailures)
  | _ => Error(None)
  }
}

let make = (
  ~method=#GET,
  ~bodyJson=?,
  ~authTokenRequired=true,
  ~skipMalformedOkResult=false,
  endpoint,
) => {
  let jwt = Auth.getJwt()

  if jwt->Option.isNone && authTokenRequired {
    Navigation.openRoute(AuthRoutes.logoutSessionExpiredRoute)
    Future.value(Error(JwtAuthenticationRedirection))
  } else {
    let abortController = Fetch.AbortController.new()
    let abortSignal = abortController->Fetch.AbortController.signal

    let headers = switch jwt {
    | Some(token) if authTokenRequired =>
      Fetch.Headers.fromObject({
        "Content-Type": "application/json",
        "Authorization": `Bearer ${token}`,
      })
    | _ => Fetch.Headers.fromObject({"Content-Type": "application/json"})
    }

    let init = {
      Fetch.Request.method,
      body: ?bodyJson->Option.map(bodyJson => bodyJson->Json.stringify->Fetch.Body.string),
      headers,
      signal: abortSignal,
    }

    let fetchPromise = Fetch.make(endpoint, init)->Promise.then(response => {
      let status = response->Fetch.Response.status
      let contentType = response->Fetch.Response.headers->Fetch.Headers.get("content-type")
      if status === 401 {
        Promise.reject(UnauthorizedRequestError)
      } else {
        switch contentType {
        // NOTE — The backend is supposed to return failures
        // only in the case of a 400 <= status < 500.
        // But this rule is not always respected.
        | Some(contentType) if contentType->Js.String2.startsWith("application/json") =>
          Fetch.Response.json(response)
        | Some(contentType) if contentType->Js.String2.startsWith("text/plain") && status >= 500 =>
          Fetch.Response.text(response)->Promise.then(text => Promise.reject(RequestError(text)))
        | Some(_) | None => Promise.reject(UnknownRequestError)
        }
      }
    })

    Future.make(resolve => {
      fetchPromise->Promise.then2(ok => resolve(Ok(ok)), error => resolve(Error(error)))
      // NOTE — The future is cancellable, but beware: if there's a Future.map next
      // and propagateCancel isn't set to true, it won't work.
      // If a flatMap is used, cancel propagation cannot work.
      // At some point, it would be a good idea to set propagateCancel=true by default.
      Some(() => abortController->Fetch.AbortController.abort)
    })
    ->Future.mapError(~propagateCancel=true, error =>
      switch Js.Exn.anyToExnInternal(error) {
      | Js.Exn.Error(error) =>
        switch Js.Exn.message(error) {
        | None => UnknownRequestError
        | Some(message) => ClientError(message)
        }
      | exnInternal => exnInternal
      }
    )
    ->Future.map(~propagateCancel=true, result =>
      switch result {
      | Ok(json) => Ok(json)
      | Error(error) =>
        switch error {
        | UnauthorizedRequestError => Error(JwtAuthenticationRedirection)
        | ClientError(message) => Error(ClientError(message))
        | RequestError(message) => Error(ServerError(message))
        | UnknownRequestError | _ => Error(UnexpectedServerError)
        }
      }
    )
    ->Future.map(~propagateCancel=true, result =>
      switch result {
      | Ok(ok) =>
        switch decodeResult(ok) {
        | Ok(Some(data)) => Ok(data)
        | Error(None) | Ok(None) if skipMalformedOkResult === false => Error(MalformedResponse)
        | Error(None) | Ok(None) => Ok(ok)
        | Error(Some(failures)) => Error(InvalidRequestFailures(failures))
        }
      | Error(_) as error => error
      }
    )
  }
}
