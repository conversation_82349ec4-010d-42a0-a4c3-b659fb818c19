let edgesPerPage: int

let totalPages: (int, int) => int

type connectionArguments = {first?: int, last?: int, after?: string, before?: string}
let connectionArguments: (
  ~currentPage: int,
  ~previousPage: int,
  ~totalCount: int,
  ~cursors: (option<string>, option<string>),
) => option<connectionArguments>

type state<'filters> = {
  currentPage: int,
  previousPage: int,
  searchQuery?: string,
  filters: 'filters,
  connectionArguments: connectionArguments,
}
type action<'filters> =
  | Navigated({nextPage: int, totalCount: int, cursors: (option<string>, option<string>)})
  | Searched(string)
  | FiltersUpdated('filters => 'filters)
  | Reset(state<'filters>)
type dispatch<'filters> = action<'filters> => unit
let initialState: (~filters: 'filters) => state<'filters>
let reducer: (state<'filters>, action<'filters>) => state<'filters>

let nextPage: (
  ~state: state<'filters>,
  ~action: LegacyPagination.action,
  ~totalPages: int,
) => option<int>

let use: (
  ~initialState: state<'filters>,
  ~filtersJsonCodec: JsonCodec.t<'filters>,
) => (state<'filters>, action<'filters> => unit)
