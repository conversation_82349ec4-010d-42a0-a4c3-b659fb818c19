module ArticleID: {
  type t = string
}

let howCentralizeCatalog: ArticleID.t
let prepareImportInventory: ArticleID.t
let automaticOrdering: ArticleID.t
let orderLabelSheets: ArticleID.t
let setupLabelPrinterAndStarPrinterOnline: ArticleID.t
let getLabelPrintFeature: ArticleID.t
let termsAndConditions: ArticleID.t
let setupAndExportAccountingEntries: ArticleID.t
let useAutomaticOrderFeature: ArticleID.t

let gettingStartedGuideLink: string

@genType let install: (~appId: string) => unit

let setup: (~appId: string) => unit

let updateUserFields: (~name: string, ~userId: string=?, ~email: string=?, unit) => unit
let show: unit => unit
let showArticle: ArticleID.t => unit
let showHome: unit => unit
let showMessages: unit => unit
let openNewMessage: (~content: string) => unit
