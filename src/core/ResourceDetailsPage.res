module ActionsBar = {
  @react.component
  let make = (~items) => <Inline space=#small> {items->React.array} </Inline>
}

module NotificationBanner = {
  @react.component
  let make = (~value, ~onRequestClose) =>
    <Banner
      onRequestClose
      textStatus={switch value {
      | Ok(ok) => Success(ok)
      | Error(error) => Banner.Danger(error)
      }}
    />
}

type variation = [#standard | #compact]

@react.component
let make = (
  ~variation=?,
  ~title,
  ~subtitle=?,
  ~actionsBar=?,
  ~notificationBanner=?,
  ~statusBadge=?,
  ~children,
) => {
  let ({Nav.Context.opened: navOpened}, _) = Nav.Context.use()
  let renderActionsBar = actionsBar->Option.map((element, ()) => element)
  let renderTitleEnd = statusBadge->Option.map((element, ()) => element)

  <Page ?variation title ?subtitle renderActions=?renderActionsBar ?renderTitleEnd>
    {switch notificationBanner {
    | Some(notificationBanner) => <Box spaceTop=#medium> notificationBanner </Box>
    | None => React.null
    }}
    <Box spaceTop=#large grow=true> {children} </Box>
    {switch renderActionsBar {
    | Some(renderEnd) => <PageBottomActionsBar renderEnd navOpened />
    | None => React.null
    }}
  </Page>
}
