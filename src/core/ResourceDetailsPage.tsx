import {
  ActionsBar_make,
  NotificationBanner_make,
  make,
} from './ResourceDetailsPage.gen'

type ResourceDetailsActionsBarProps = React.ComponentProps<
  typeof ActionsBar_make
>
const ResourceDetailsActionsBar =
  ActionsBar_make as React.FunctionComponent<ResourceDetailsActionsBarProps>

type ResourceDetailsNotificationBannerProps = React.ComponentProps<
  typeof NotificationBanner_make
>
const ResourceDetailsNotificationBanner =
  NotificationBanner_make as React.FunctionComponent<ResourceDetailsNotificationBannerProps>

type ResourceDetailsPageProps = React.ComponentProps<typeof make>
const ResourceDetailsPage =
  make as React.FunctionComponent<ResourceDetailsPageProps>

export {
  ResourceDetailsActionsBar,
  type ResourceDetailsActionsBarProps,
  ResourceDetailsPage,
  type ResourceDetailsPageProps,
  ResourceDetailsNotificationBanner,
  type ResourceDetailsNotificationBannerProps,
}
