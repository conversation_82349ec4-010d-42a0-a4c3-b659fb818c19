module ApolloLink = ApolloClient.Link
module ApolloOperation = ApolloClient.Types.Operation
module Observable = ApolloClient.Bindings.ZenObservable.Observable

let edgesPerFetchLimit = 50
let mutationDebounceInterval = 750

// NOTE - LogRocket fix https://docs.logrocket.com/docs/graphql#apollo-client-with-graphql
let fetcher = %raw(` (...args) => window.fetch(...args) `)

let httpGatewayLink = switch Env.context() {
| #test => ApolloLink.HttpLink.make(~uri=_ => "http://localhost/graphql", ())
| _ => ApolloLink.HttpLink.make(~uri=_ => Env.gatewayUrl() ++ "/graphql", ~fetch=fetcher, ())
}

let httpAnalyticsLink = switch Env.context() {
| #test => ApolloLink.HttpLink.make(~uri=_ => "http://localhost/graphql", ())
| _ => ApolloLink.HttpLink.make(~uri=_ => Env.analyticsUrl() ++ "/graphql", ~fetch=fetcher, ())
}

let terminatingLink = ApolloClient.Link.split(~test=({query}) =>
  switch ApolloClient.Utilities.getOperationDefinition(query) {
  | Some({kind, operation, name: Some({value})}) =>
    kind === "OperationDefinition" &&
    operation === "query" &&
    value->Js.String2.startsWith("Analytics")
  | _ => false
  }
, ~whenTrue=httpAnalyticsLink, ~whenFalse=httpGatewayLink)

let authMiddleware = ApolloLink.make((operation, forward) => {
  switch Auth.getJwt() {
  | Some(jwt) => operation->ApolloOperation.Js_.setContext(Auth.encodeHttpContext(~jwt))->ignore
  | _ => ()
  }
  Some(operation->forward->Observable.fromJs)
})

let errorMiddleware = () =>
  ApolloLink.ErrorLink.make(errors => {
    switch errors.graphQLErrors {
    | Some(errors) =>
      errors->Array.forEach(err =>
        switch err.originalError->Js.Nullable.toOption {
        | Some(exn) =>
          BugTracker.reportException(exn)
          LogRocket.captureException(exn)
        | _ => ()
        }
      )
    | _ => ()
    }
    switch errors.networkError {
    | Some(error) =>
      switch error {
      | FetchFailure(exn) =>
        BugTracker.reportException(exn)
        LogRocket.captureException(exn)
      | BadStatus(401, _) => Navigation.openRoute(AuthRoutes.logoutSessionExpiredRoute)
      | _ => ()
      }
    | _ => ()
    }
    None
  })

let makeClient = () =>
  ApolloClient.make(
    ~link=ApolloLink.from([errorMiddleware(), authMiddleware, terminatingLink]),
    ~cache=ApolloClient.Cache.InMemoryCache.make(),
    ~connectToDevTools=Env.context() === #dev,
    (),
  )

module Provider = {
  @react.component
  let make = (~children) => {
    let authState = Auth.useState()
    let client = makeClient()

    React.useEffect1(() => {
      switch authState {
      | Unlogged => ignore(client.clearStore())
      | _ => ()
      }
      None
    }, [authState])

    <ApolloClient.React.ApolloProvider client> children </ApolloClient.React.ApolloProvider>
  }
}
