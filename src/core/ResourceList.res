let defaultEdgesPerPage = 50

let totalPages = (totalCount, edgesPerPage) =>
  if edgesPerPage <= 0 || totalCount <= 0 {
    1
  } else if mod(totalCount, edgesPerPage) === 0 {
    totalCount / edgesPerPage
  } else {
    totalCount / edgesPerPage + 1
  }

type connectionArguments = {first?: int, last?: int, after?: string, before?: string}

// NOTE - the backend cursor generation based on offset is used here to handle n+2 pagination:
// https://github.com/winoteam/wino/blob/2ae47550ff91b4e7f0af8b03b5502d7a48d645a2/packages/core-sdk/src/misc/pagination.ts#L66
module ResourceListServerOps = {
  @val external btoa: string => string = "btoa"

  // NOTE - defined in server pagination function
  let cursorPrefix = "9252fe5d140e19d308f2037404a0536a"

  let generateCursor = (~page, ~edgesPerPage, ~totalCount) => {
    let maxPage = totalPages(totalCount, edgesPerPage)
    if page >= 1 && page <= maxPage {
      let index = (page - 1) * edgesPerPage
      let cursor = cursorPrefix ++ index->Int.toString
      Ok(btoa(cursor))
    } else {
      Error()
    }
  }
}

let connectionArguments = (
  ~currentPage,
  ~previousPage,
  ~totalCount,
  ~cursors,
  ~edgesPerPage=defaultEdgesPerPage,
  (),
) =>
  switch (currentPage, cursors) {
  | (1, _) => Some({first: edgesPerPage})
  | (currentPage, (Some(startCursor), _)) if currentPage === previousPage - 1 =>
    Some({last: edgesPerPage, before: startCursor})
  | (currentPage, (Some(_startCursor), _)) if currentPage === previousPage - 2 =>
    let startCursor = ResourceListServerOps.generateCursor(
      ~page=currentPage,
      ~edgesPerPage,
      ~totalCount,
    )
    switch startCursor {
    | Ok(startCursor) => Some({last: edgesPerPage, before: startCursor})
    | Error() => None
    }
  | (currentPage, (_, Some(endCursor))) if currentPage === previousPage + 1 =>
    Some({first: edgesPerPage, after: endCursor})
  | (currentPage, (_, Some(_))) if currentPage === previousPage + 2 =>
    let endCursor = ResourceListServerOps.generateCursor(
      ~page=currentPage,
      ~edgesPerPage,
      ~totalCount,
    )
    switch endCursor {
    | Ok(endCursor) => Some({first: edgesPerPage, after: endCursor})
    | Error() => None
    }
  | (currentPage, _) if currentPage === totalPages(totalCount, edgesPerPage) =>
    Some({last: edgesPerPage})
  | _ => None
  }

type state<'filters, 'sorting, 'extra> = {
  currentPage: int,
  previousPage: int,
  searchQuery?: string,
  filters: 'filters,
  sorting: 'sorting,
  extra?: 'extra,
  connectionArguments: connectionArguments,
}

type action<'filters, 'sorting, 'extra> =
  | Navigated({nextPage: int, totalCount: int, cursors: (option<string>, option<string>)})
  | Searched(string)
  | FiltersUpdated('filters => 'filters)
  | SortingUpdated('sorting)
  | ExtraUpdated('extra)
  | Reset(state<'filters, 'sorting, 'extra>)

type dispatch<'filters, 'sorting, 'extra> = action<'filters, 'sorting, 'extra> => unit

let initialState = (~first=defaultEdgesPerPage, ~filters, ~sorting, ~extra=?, ()) => {
  currentPage: 1,
  previousPage: -1,
  filters,
  sorting,
  ?extra,
  connectionArguments: {first: first},
}

let reducer = (state, action) => {
  let edgesPerPage = switch state.connectionArguments {
  | {first: edgesPerPage} => edgesPerPage
  | {last: edgesPerPage} => edgesPerPage
  | _ => defaultEdgesPerPage
  }
  switch action {
  | Navigated({nextPage, totalCount, cursors}) =>
    let currentPage = nextPage
    let previousPage = state.currentPage
    let maybeConnectionArguments = connectionArguments(
      ~currentPage,
      ~previousPage,
      ~totalCount,
      ~cursors,
      ~edgesPerPage,
      (),
    )
    switch maybeConnectionArguments {
    | Some(connectionArguments) => {
        ...state,
        currentPage,
        previousPage,
        connectionArguments,
      }
    | None => state
    }
  | Searched(searchQuery) => {
      searchQuery: ?switch searchQuery {
      | "" => None
      | _ => Some(searchQuery)
      },
      currentPage: 1,
      previousPage: -1,
      connectionArguments: {first: edgesPerPage},
      filters: state.filters,
      sorting: state.sorting,
      extra: ?state.extra,
    }
  | FiltersUpdated(updateStateFilters) => {
      filters: updateStateFilters(state.filters),
      sorting: state.sorting,
      extra: ?state.extra,
      currentPage: 1,
      previousPage: -1,
      connectionArguments: {first: edgesPerPage},
      searchQuery: ?state.searchQuery,
    }
  | SortingUpdated(sorting) => {
      sorting,
      filters: state.filters,
      extra: ?state.extra,
      currentPage: 1,
      previousPage: -1,
      connectionArguments: {first: edgesPerPage},
      searchQuery: ?state.searchQuery,
    }
  | ExtraUpdated(extra) => {
      ...state,
      extra,
    }
  | Reset(newState) => newState
  }
}

let nextPage = (~state, ~action, ~totalPages) =>
  switch (action, state.currentPage, totalPages) {
  | (_, 0 | 1, 0 | 1) => None
  | (ResourceListPagination.First, currentPage, _) if currentPage >= 1 => Some(1)
  | (PrevPrev, currentPage, _) if currentPage >= 1 => Some(currentPage - 2)
  | (Prev, currentPage, totalPages) if currentPage > totalPages => Some(totalPages)
  | (Prev, currentPage, _) if currentPage > 1 => Some(currentPage - 1)
  | (Next, currentPage, totalPages) if currentPage > totalPages => Some(totalPages)
  | (Next, currentPage, _) if currentPage >= 1 && currentPage < totalPages => Some(currentPage + 1)
  | (NextNext, currentPage, _) if currentPage >= 1 && currentPage < totalPages - 1 =>
    Some(currentPage + 2)
  | (Last, currentPage, _) if currentPage >= 1 => Some(totalPages)
  | _ => None
  }

module CodecState = {
  let encoder = ({
    currentPage,
    previousPage,
    ?searchQuery,
    filters,
    sorting,
    ?extra,
    connectionArguments,
  }) => (
    Some(currentPage),
    Some(previousPage),
    searchQuery,
    Some(filters),
    Some(sorting),
    extra,
    connectionArguments.first,
    connectionArguments.last,
    connectionArguments.after,
    connectionArguments.before,
  )

  let decoder = (
    initialFilters,
    initialSorts,
    initialExtra,
    (
      currentPage,
      previousPage,
      searchQuery,
      filters,
      sorting,
      extra,
      connectionArgumentsFirst,
      connectionArgumentsLast,
      connectionArgumentsAfter,
      connectionArgumentsBefore,
    ),
  ) => Ok({
    currentPage: currentPage->Option.getWithDefault(1),
    previousPage: previousPage->Option.getWithDefault(-1),
    ?searchQuery,
    filters: filters->Option.getWithDefault(initialFilters),
    sorting: sorting->Option.getWithDefault(initialSorts),
    extra: ?extra->Option.orElse(initialExtra),
    connectionArguments: {
      first: ?connectionArgumentsFirst,
      last: ?connectionArgumentsLast,
      after: ?connectionArgumentsAfter,
      before: ?connectionArgumentsBefore,
    },
  })

  let value = (
    ~filtersJsonCodec,
    ~sortingJsonCodec,
    ~extraJsonCodec,
    ~initialFilters,
    ~initialSorts,
    ~initialExtra,
  ) =>
    JsonCodec.object10(
      encoder,
      decoder(initialFilters, initialSorts, initialExtra),
      JsonCodec.field("page", JsonCodec.int)->JsonCodec.optional,
      JsonCodec.field("previousPage", JsonCodec.int)->JsonCodec.optional,
      JsonCodec.field("search", JsonCodec.string)->JsonCodec.optional,
      JsonCodec.field("filters", filtersJsonCodec)->JsonCodec.optional,
      JsonCodec.field("sorting", sortingJsonCodec)->JsonCodec.optional,
      JsonCodec.field("extra", extraJsonCodec->Option.getUnsafe)->JsonCodec.optional,
      JsonCodec.field("first", JsonCodec.int)->JsonCodec.optional,
      JsonCodec.field("last", JsonCodec.int)->JsonCodec.optional,
      JsonCodec.field("after", JsonCodec.string)->JsonCodec.optional,
      JsonCodec.field("before", JsonCodec.string)->JsonCodec.optional,
    )
}

let useQueryStringPersistReducer = (codec, reducer, initialState) => {
  let navigate = Navigation.useNavigate()
  let url = Navigation.useUrl()

  let initialState = React.useMemo0(() => {
    let urlState = url.query->QueryString.parse->JsonCodec.decodeWith(codec)

    switch (urlState, url.query->QueryString.toString) {
    | (Ok(_), "") | (Error(_), _) => initialState
    | (Ok(state), _) => state
    }
  })

  let (state, dispatch) = React.useReducer(reducer, initialState)

  React.useEffect1(() => {
    let query = state->JsonCodec.encodeWith(codec)->QueryString.stringify
    let route = url.pathname ++ "?" ++ query->QueryString.toString

    if query !== url.query {
      navigate(route, ~replace=true)
    }
    None
  }, [state])

  (state, dispatch)
}

let use = (~initialState, ~filtersJsonCodec, ~sortingJsonCodec, ~extraJsonCodec=?, ()) =>
  useQueryStringPersistReducer(
    CodecState.value(
      ~filtersJsonCodec,
      ~sortingJsonCodec,
      ~extraJsonCodec,
      ~initialFilters=initialState.filters,
      ~initialSorts=initialState.sorting,
      ~initialExtra=initialState.extra,
    ),
    reducer,
    initialState,
  )
