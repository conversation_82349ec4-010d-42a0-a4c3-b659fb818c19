module Address = {
  type t = {
    address: string,
    postalCode: string,
    city: string,
    country: string,
  }
  let initialValue = {address: "", postalCode: "", city: "", country: ""}
  let equal = (a1, a2) =>
    a1.address === a2.address &&
    a1.postalCode === a2.postalCode &&
    a1.city === a2.city &&
    a1.country === a2.country
}

module CompanyLegalForm = {
  type t =
    | SA
    | SAS
    | SARL
    | EI
    | EIRL
    | EURL
    | MICRO
    | SNC
    | SCS
    | SCA

  let values = [SA, SAS, SARL, EI, EIRL, EURL, MICRO, SNC, SCS, SCA]

  let toString = value =>
    switch value {
    | SA => "SA"
    | SAS => "SAS"
    | SARL => "SARL"
    | EI => "EI"
    | EIRL => "EIRL"
    | EURL => "EURL"
    | MICRO => "MICRO"
    | SNC => "SNC"
    | SCS => "SCS"
    | SCA => "SCA"
    }

  let fromString = value =>
    switch value {
    | "SA" => Ok(SA)
    | "SAS" => Ok(SAS)
    | "SARL" => Ok(SARL)
    | "EI" => Ok(EI)
    | "EIRL" => Ok(EIRL)
    | "EURL" => Ok(EURL)
    | "MICRO" => Ok(MICRO)
    | "SNC" => Ok(SNC)
    | "SCS" => Ok(SCS)
    | "SCA" => Ok(SCA)
    | _ => Error("Unknown legal form")
    }

  let compare = (a, b) => String.length(b) - String.length(a)
}

module Iban = {
  let sanitize = (value: string) => value->Js.String2.replaceByRe(%re("/\s+/g"), "")

  let stripIban = (value: string) =>
    Js.String2.replaceByRe(value, Js.Re.fromString("[^A-Za-z0-9]+"), "")->Js.String2.toUpperCase

  let getIbanComponents = (ibanStripped: string) =>
    Js.Re.exec_(Js.Re.fromString("^([A-Z]{2})([0-9]{2})([A-Z0-9]{9,30})$"), ibanStripped)

  let getOptionString = optNullable =>
    switch optNullable {
    | Some(nullableString) => Js.Nullable.toOption(nullableString)
    | None => None
    }

  let numericiseIban = (bban: string, country: string, checkDigits: string) =>
    (bban ++ country ++ checkDigits)
    ->Js.String2.split("")
    ->Array.map(ch =>
      switch ch {
      | char
        if Js.String2.charCodeAt(char, 0) >= "A"->Js.String2.charCodeAt(0) &&
          Js.String2.charCodeAt(char, 0) <= "Z"->Js.String2.charCodeAt(0) =>
        (Js.String2.charCodeAt(char, 0) -. 55.)->Float.toString
      | char => char
      }
    )
    ->Array.joinWith("", a => a)

  let findAllMatches = (regex, numbericed) => {
    let rec loop = (acc, regex, numbericed) => {
      switch Js.Re.exec_(regex, numbericed) {
      | Some(result) =>
        let match = Js.Re.captures(result)->Array.get(0)->Option.getExn
        loop(Array.concat(acc, [match]), regex, numbericed)
      | None => acc
      }
    }
    loop([], regex, numbericed)
  }

  let computeMod97 = matches =>
    matches->Array.reduce("", (total, curr) => {
      mod(
        (total ++ curr->Js.Nullable.toOption->Option.getWithDefault(""))
        ->Int.fromString
        ->Option.getWithDefault(0),
        97,
      )->Int.toString
    })

  // Based on https://stackoverflow.com/questions/44656264/iban-regex-design
  let validate = (value: string) => {
    let ibanRegex = "^([A-Za-z]{2}[ \\-]?[0-9]{2})(?=(?:[ \\-]?[A-Za-z0-9]){9,30}$)((?:[ \\-]?[A-Za-z0-9]{3,5}){2,6})([ \\-]?[A-Za-z0-9]{1,3})?$"
    let trimmedIban = value->sanitize

    switch Js.Re.fromString(ibanRegex)->Js.Re.test_(trimmedIban) {
    | true =>
      let ibanStripped = stripIban(trimmedIban)
      let ibanComponents = getIbanComponents(ibanStripped)

      switch ibanComponents {
      | Some(match) =>
        let captures = Js.Re.captures(match)
        let country = getOptionString(captures[1])
        let checkDigits = getOptionString(captures[2])
        let bban = getOptionString(captures[3])

        switch (country, checkDigits, bban) {
        | (Some(country), Some(checkDigits), Some(bban)) =>
          let numbericed = numericiseIban(bban, country, checkDigits)
          let regex = Js.Re.fromStringWithFlags("\\d{1,7}", ~flags="g")
          let matches = findAllMatches(regex, numbericed)
          computeMod97(matches) == "1"
        | _ => false
        }

      | None => false
      }
    | false => false
    }
  }
}

module SepaMandate = {
  type status = Active | Inactive | Pending
  type t = {status: status, acceptedAt: option<float>}
}

module CorporateName = {
  let prefixes = CompanyLegalForm.values->Array.map(CompanyLegalForm.toString)

  let sortByStringLengthDesc = (arr: array<string>): array<string> =>
    arr->SortArray.stableSortBy(CompanyLegalForm.compare)

  let strippedCorporateName = (corporateName: string) =>
    prefixes
    ->sortByStringLengthDesc
    ->Array.reduce(corporateName, (acc, prefixe) => acc->Js.String2.replace(prefixe, ""))
    ->Js.String2.trim

  let validate = (corporateName: string) => strippedCorporateName(corporateName)->String.length > 3
}
