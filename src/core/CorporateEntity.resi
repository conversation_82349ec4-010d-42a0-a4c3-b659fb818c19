module Address: {
  type t = {address: string, postalCode: string, city: string, country: string}
  let initialValue: t
  let equal: (t, t) => bool
}

module CompanyLegalForm: {
  type t = SA | SAS | SARL | EI | EIRL | EURL | MICRO | SNC | SCS | SCA
  let values: array<t>
  let toString: t => string
  let fromString: string => result<t, string>
  let compare: (string, string) => int
}

module Iban: {
  let sanitize: string => string
  let validate: string => bool
}

module SepaMandate: {
  type status = Active | Inactive | Pending
  type t = {status: status, acceptedAt: option<float>}
}

module CorporateName: {
  let validate: string => bool
}
