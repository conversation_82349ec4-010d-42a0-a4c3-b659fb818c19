type invalidRequestFailure = {
  message: string,
  kind: string,
  data: option<Json.t>,
}

type error =
  | UnexpectedServerError
  | ServerError(string)
  | ClientError(string)
  | InvalidRequestFailures(array<invalidRequestFailure>)
  | MalformedResponse
  | JwtAuthenticationRedirection

let serverErrorMessage: string

let decodeInvalidRequestFailure: Json.t => option<invalidRequestFailure>
let decodeInvalidRequestFailures: Array.t<Json.t> => option<array<invalidRequestFailure>>
let decodeResult: Json.t => result<option<Json.t>, option<array<invalidRequestFailure>>>

let make: (
  ~method: Fetch.method=?,
  ~bodyJson: Json.t=?,
  ~authTokenRequired: bool=?,
  ~skipMalformedOkResult: bool=?,
  string,
) => Future.t<result<Json.t, error>>
