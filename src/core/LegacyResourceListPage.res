module ResetFiltersButton = {
  @react.component
  let make = React.memo((~onPress) =>
    <Box spaceLeft=#small>
      <TextIconButton size=#small icon=#close_light onPress={_ => onPress()}>
        {Intl.t("Reset")->React.string}
      </TextIconButton>
    </Box>
  )
}

@react.component
let make = (
  ~title,
  ~subtitle=?,
  ~filters=React.null,
  ~actions=React.null,
  ~banner=React.null,
  ~searchBar=React.null,
  ~tableData,
  ~tableColumns,
  ~tableKeyExtractor,
  ~tableEmptyState,
  ~paginationCurrentPage,
  ~paginationTotalPages,
  ~onRequestPaginate,
) => {
  let pageVariation = switch subtitle {
  | Some(_) => #standard
  | None => #compact
  }

  let searchBar = <Box spaceX=#large spaceBottom=#xmedium> searchBar </Box>

  <Page title ?subtitle variation=pageVariation>
    <BarControl filters actions banner />
    <TableView
      columns=tableColumns
      data=tableData
      keyExtractor=tableKeyExtractor
      placeholderEmptyState=tableEmptyState
      hideReloadingPlaceholder=true
      searchBar
    />
    <LegacyPagination
      currentPage=paginationCurrentPage totalPages=paginationTotalPages onRequestPaginate
    />
  </Page>
}
