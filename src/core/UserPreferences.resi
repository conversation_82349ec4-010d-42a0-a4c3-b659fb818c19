type t = [#"catalog-labels-options" | #"catalog-page-settings"]

type methods<'state> = {
  read: unit => option<'state>,
  store: 'state => unit,
  useRead: unit => option<'state>,
  usePersistOnChange: ('state, ~disabled: bool=?, ~debounceDelay: int=?, unit) => bool,
}

let read: (~key: t, ~decoder: Json.t => result<'state, 'error>) => option<'state>
let store: ('state, ~key: t, ~encoder: 'state => Json.t) => unit
let useRead: (~key: t, ~decoder: Json.t => result<'state, 'error>) => option<'state>
let usePersistOnChange: (
  'state,
  ~key: t,
  ~disabled: bool=?,
  ~debounceDelay: int=?,
  ~encoder: 'state => Json.t,
  ~decoder: Json.t => result<'state, 'error>,
  unit,
) => bool

let make: (
  ~key: t,
  ~encoder: 'state => Json.t,
  ~decoder: Json.t => result<'state, 'error>,
) => methods<'state>
