# Assets

### 🌍 Countries & Subdivisions (ISO 3166-1 / ISO 3166-2)

This project uses two datasets based on the ISO 3166 standard to represent countries and some first-level administrative subdivisions.

The datasets follow the naming pattern `countries-xx-XX.json` and `subdivisions-xx-XX.json`, where `xx-XX` represents the locale code. The structure and usage are the same regardless of the language or locale.

- **`countries-xx-XX.json`** — a complete list of countries identified by their ISO 3166-1 Alpha-2 codes.
- **`subdivisions-xx-XX.json`** — a set of first-level administrative divisions (states, provinces, regions, etc.) identified by ISO 3166-2 codes.

> ---
>
> ##### Why two files?
>
> - Modules like **Suppliers** or **Customers** typically only require the list of countries, so only the `countries` dataset is used.
> - For modules like **Catalog**, which require to specify regions of origin (Scotland, Quebec...), the countries and subdivisions datasets are merged to provide both levels of detail.
>
> ---

#### Sources

[Reference spreadsheet](https://docs.google.com/spreadsheets/d/1UtbZn3dGiMCsJoc6eXy-J8B33--8TctkSHoiYcJ9bQ0/edit?usp=sharing) (continuously updated), based on:

- Public repository of ISO 3166-1 codes: <https://documentation.abes.fr/sudoc/formats/CodesPays.htm>
- Public repository of ISO 3166-2 codes: <https://www.ip2location.com/free/iso3166-2>

---
