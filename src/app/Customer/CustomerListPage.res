open Intl
open StyleX

@module("./customer_empty_placeholder.png") external imageUri: string = "default"

let styles = StyleX.create({
  "image": style(~height="225px", ~width="300px", ~alignSelf=#center, ()),
  "tags": style(~alignSelf=#center, ~paddingTop=Spaces.mediumPx, ()),
  "action": style(~alignSelf=#center, ~paddingTop=Spaces.mediumPx, ()),
})

module CustomersExportRequest = {
  let endpoint = Env.sheetUrl() ++ "/customers"

  let encodeRequestBodyJson = (~shopIds, ()) =>
    Js.Dict.fromArray([
      ("shopIds", shopIds->Array.map(shopId => shopId->Json.encodeString)->Json.encodeArray),
    ])->Json.encodeDict

  let make = (~shopIds) =>
    Request.make(endpoint, ~method=#POST, ~bodyJson=encodeRequestBodyJson(~shopIds, ()))
}

module CustomersExportButton = {
  @react.component
  let make = (~shopIds, ~text, ~request, ~onNotification) => {
    let captureEvent = SessionTracker.useCaptureEvent()
    let request = () => {
      captureEvent(#download_customer_export_file)
      request(~shopIds)
    }

    let onFailure = error => {
      let errorMessage =
        error->RequestOpenStorageUrlButton.failureErrorToString(~exportName=t("customers"))
      onNotification(Banner.Danger(errorMessage))
    }

    let onSuccess = () =>
      onNotification(Banner.Success(t("The file has been downloaded successfully.")))

    let operableRequest = Ok(request)

    <RequestOpenStorageUrlButton text operableRequest onFailure onSuccess />
  }
}

@react.component
let make = (~request) => {
  let scope = Auth.useScope()
  let organisationAccount = switch scope {
  | Organisation(_) => true
  | Single(_) => false
  }
  let shopIds = switch scope {
  | Single(shop) | Organisation({activeShop: Some(shop)}) => [shop]
  | Organisation({shops}) => shops
  }->Array.map(shop => shop.id)

  let shopName: option<string> = switch scope {
  | Organisation({activeShop: Some(shop)}) => Some(shop.name)
  | _ => None
  }

  let (notification, setNotfication) = React.useState(() => None)
  let onNotification = message => setNotfication(_ => Some(message))

  let renderHeaderActions = () =>
    if organisationAccount {
      <Box spaceBottom=#normal>
        <Inline space=#small>
          <Auth.SelectShopFilter />
        </Inline>
      </Box>
    } else {
      <Box spaceBottom=#none />
    }

  <Page title={t("Customers")} renderHeaderActions variation=#compact>
    {switch notification {
    | Some(notification) =>
      <Box spaceBottom=#normal>
        <Banner textStatus=notification />
      </Box>
    | None => React.null
    }}
    <Card grow=true centerContent=true>
      <ImgX src={imageUri} style={StyleX.props([styles["image"]])} />
      <Stack space=#small>
        <Title align=#center level=#2>
          {t("The display of customer contacts is not yet available.")->React.string}
        </Title>
        <TextStyle align=#center>
          {t("Meanwhile, you can still export them.")->React.string}
        </TextStyle>
        <Stack space=#small>
          {switch shopName {
          | Some(name) =>
            <DivX style={StyleX.props([styles["tags"]])}>
              <Inline space={#small}>
                <Tag> {name->React.string} </Tag>
              </Inline>
            </DivX>
          | None => React.null
          }}
          <DivX style={StyleX.props([styles["action"]])}>
            <CustomersExportButton
              shopIds onNotification text={t("Export customer file")} request
            />
          </DivX>
        </Stack>
      </Stack>
    </Card>
  </Page>
}
