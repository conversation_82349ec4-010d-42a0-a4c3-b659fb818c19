open Intl
open StyleX

@module("./access_denied.svg") external imageUri: string = "default"

let styles = StyleX.create({
  "root": style(
    ~display=#flex,
    ~flexDirection=#column,
    ~alignSelf=#center,
    ~justifyContent=#center,
    ~width="auto",
    ~height="100%",
    ~marginTop="-" ++ Spaces.hugePx,
    ~gap=Spaces.xxlargePx,
    (),
  ),
  "image": style(~width="32%", ~alignSelf=#center, ()),
  "textContainer": style(~display=#flex, ~flexDirection=#column, ~gap="6px", ()),
})

let styleProps = () => StyleX.props([styles["root"]])
let imageStyleProps = () => StyleX.props([styles["image"]])
let textContainerStyleProps = () => StyleX.props([styles["textContainer"]])

@react.component
let make = () => {
  let {?style, ?className} = styleProps()
  let {style: ?imageStyle, className: ?imageClassName} = imageStyleProps()
  let {style: ?textContainerStyle, className: ?textContainerClassName} = textContainerStyleProps()

  <div ?style ?className>
    <img src=imageUri style=?imageStyle className=?imageClassName />
    <div style=?textContainerStyle className=?textContainerClassName>
      <Title align=#center> {t("access_denied_page.title.text")->React.string} </Title>
      <TextStyle align=#center size=#large>
        {t("access_denied_page.subtitle.text")->React.string}
      </TextStyle>
    </div>
  </div>
}
