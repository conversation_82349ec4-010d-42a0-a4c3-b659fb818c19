open Accounting.Types

type stock = {
  productId: string,
  rawQuantity: int,
}

let updateCartStocks = (
  cart: Accounting.Types.cart,
  ~stocks: option<array<stock>>,
  ~statuses: array<OrderStatus.t>,
) =>
  switch (stocks, statuses) {
  | (Some(stocks), statuses) if OrderEdit.isBeforeReception(~statuses) => {
      ...cart,
      products: cart.products->Array.map(product =>
        switch product {
        | Unit(product) =>
          Unit({
            ...product,
            stock: switch stocks->Array.getBy(updatedStock =>
              updatedStock.productId === product.id
            ) {
            | Some(updatedStock) => updatedStock.rawQuantity
            | _ => product.stock
            },
          })
        | Bulk(product, precision) =>
          Bulk(
            {
              ...product,
              stock: switch stocks->Array.getBy(updatedStock =>
                updatedStock.productId === product.id
              ) {
              | Some(updatedStock) =>
                updatedStock.rawQuantity->Accounting.fromRawProductQuantity(
                  ~capacityPrecision=precision,
                )
              | _ => product.stock
              },
            },
            precision,
          )
        }
      ),
    }
  | _ => cart
  }

type formPersistanceExtraData = {
  statuses: array<OrderStatus.t>,
  stocks: option<array<stock>>,
  shopId: string,
  deviceId: string,
}

// NOTE - make sure state fields names match the object properties names
let serializeForm = ((), state) => {
  let serializedCart = state.OrderEditForm.Lenses.cart->Accounting.Serializer.serialize
  let obj = {
    "condition": state.condition,
    "noteForSupplier": state.noteForSupplier,
    "supplierId": state.supplierId,
    "issueDate": state.issueDate,
    "estimatedReceptionDate": state.estimatedReceptionDate,
    "note": state.note,
    "cart": serializedCart,
  }
  obj->Json.stringifyAny
}

let unserializeForm = (extraData, serializedState) => {
  let state = Obj.magic(serializedState->Json.parseExn)
  let unserializedCart = state["cart"]->Accounting.Serializer.deserialize
  let issueDate = state["issueDate"]->Js.Date.fromString
  let estimatedReceptionDate = state["estimatedReceptionDate"]->Js.Date.fromString

  {
    ...state,
    OrderEditForm.Lenses.issueDate,
    estimatedReceptionDate,
    shopId: extraData.shopId,
    deviceId: extraData.deviceId,
    cart: switch extraData {
    | {stocks, statuses} if !(statuses->OrderStatus.has(#DRAFT)) =>
      unserializedCart->updateCartStocks(~stocks, ~statuses)
    | _ => unserializedCart
    },
  }
}
