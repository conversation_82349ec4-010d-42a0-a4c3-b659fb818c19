module Utils = OrderEdit__Utils
module OrderQuery = OrderEdit__Queries.OrderQuery
module OrderVariantsStockQuery = OrderEdit__Queries.OrderVariantsStockQuery

type data = {
  name: string,
  statuses: array<OrderStatus.t>,
  updatedAt: option<Js.Date.t>,
  initialValues: OrderEditForm.Lenses.state,
  updatedStocks: option<array<Utils.stock>>,
  receptionFinishedAt: option<Js.Date.t>,
}

type t =
  | Error
  | Loading
  | Data(data)

type orderResponse = ApolloClient__React_Types.LazyQueryResult.t<
  OrderQuery.OrderQuery_inner.t,
  OrderQuery.OrderQuery_inner.Raw.t,
  OrderQuery.OrderQuery_inner.t_variables,
  OrderQuery.OrderQuery_inner.Raw.t_variables,
>

type orderVariantsResponse = ApolloClient.Types.QueryResult.t<
  OrderVariantsStockQuery.OrderVariantsStockQuery_inner.t,
  OrderVariantsStockQuery.OrderVariantsStockQuery_inner.Raw.t,
  OrderVariantsStockQuery.OrderVariantsStockQuery_inner.t_variables,
  OrderVariantsStockQuery.OrderVariantsStockQuery_inner.Raw.t_variables,
>

type derived = {
  id: option<string>,
  response: orderResponse,
  authState: Auth.t,
}

let initialCart =
  OrderEditForm.initialCart
  ->Accounting.Maker.Cart.make
  ->Accounting.Computer.make
  ->Accounting.Formatter.make

let makeInitialOrder = (~supplierId="", ~shopId="", ~deviceId="", ()) => Data({
  name: "Draft",
  updatedAt: None,
  statuses: [#DRAFT],
  updatedStocks: None,
  receptionFinishedAt: Some(Js.Date.now()->Js.Date.fromFloat),
  initialValues: {
    cart: initialCart,
    note: "",
    condition: "",
    noteForSupplier: "",
    supplierId,
    issueDate: Js.Date.now()->Js.Date.fromFloat,
    estimatedReceptionDate: Js.Date.now()->Js.Date.fromFloat,
    shopId,
    deviceId,
    supplierName: "",
    supplierAddress: "",
    supplierPostalCode: "",
    supplierCity: "",
    supplierCountry: "",
  },
})

let make = (
  ~id,
  ~orderQuery: orderResponse,
  ~orderVariantsStockQuery: orderVariantsResponse,
  ~authState: Auth.t,
  ~supplierId: option<string>,
  ~shopId: option<string>,
) =>
  switch (id, orderQuery, orderVariantsStockQuery, authState) {
  // Loading:
  // When authenticating, fetching order || variants stocks
  | (Some(_), Unexecuted(_), _, _)
  | (Some(_), Executed({loading: true}), _, _)
  | (Some(_), _, {loading: true}, _)
  | (_, _, _, Logged({shops: []}))
  | (_, _, _, Logging(_))
  | (_, _, _, Unlogged) =>
    Loading
  // Data:
  // Before Order draft creation (presets activeShop|Device depending on shop filter)
  | (None, _, _, Logged({activeShop, shops})) =>
    let (shopId, deviceId) = switch (shopId, activeShop) {
    | (Some(shopId), _) =>
      let shop = shops->Array.getBy(shop => shop.id === shopId)
      (shop->Option.map(shop => shop.id), shop->Option.map(shop => shop.activeWebDeviceId))
    | (_, Some({id, activeWebDeviceId})) => (Some(id), Some(activeWebDeviceId))
    | _ => (None, None)
    }

    makeInitialOrder(~supplierId?, ~shopId?, ~deviceId?, ())
  // Data:
  // Before Order reception (with stocks updating)
  | (
      Some(_),
      Executed({data: Some({order: Some(order)})}),
      {data: Some({order: Some({products: {edges: orderProducts}})})},
      Logged(_),
    ) =>
    let updatedStocks = Some(
      orderProducts->Array.keepMap(({node: orderProduct}) =>
        switch orderProduct.variant {
        | Some({stock: {rawQuantity: Some(quantity)}}) =>
          Some({Utils.productId: orderProduct.id, rawQuantity: quantity})
        | _ => None
        }
      ),
    )

    Data({
      name: order.formattedName,
      updatedAt: Some(order.updatedAt),
      statuses: order.formattedStatus,
      updatedStocks,
      receptionFinishedAt: order.receptionFinishedAt,
      initialValues: {
        cart: order.cart
        ->Accounting.Serializer.deserialize
        ->Utils.updateCartStocks(~stocks=updatedStocks, ~statuses=order.formattedStatus),
        note: order.note->Option.getWithDefault(""),
        condition: order.condition->Option.getWithDefault(""),
        noteForSupplier: order.noteForSupplier->Option.getWithDefault(""),
        supplierId: order.supplier
        ->Option.map(supplier => supplier.id)
        ->Option.getWithDefault(supplierId->Option.getWithDefault("")),
        issueDate: order.issueDate,
        estimatedReceptionDate: order.estimatedReceptionDate,
        shopId: order.shop.id,
        deviceId: order.device.id,
        supplierName: order.supplier
        ->Option.map(supplier => supplier.companyName)
        ->Option.getWithDefault(""),
        supplierAddress: order.supplier
        ->Option.map(supplier =>
          supplier.locations.edges
          ->Array.keepMap(({node}) =>
            switch node.address {
            | Some(_) => node.address
            | _ => None
            }
          )
          ->Array.reduce("", (acc, address) =>
            acc ++ (acc->Js.String2.length > 0 ? " | " : "") ++ address
          )
        )
        ->Option.getWithDefault(""),
        supplierPostalCode: order.supplier
        ->Option.map(supplier =>
          supplier.locations.edges
          ->Array.keepMap(({node}) =>
            switch node.postalCode {
            | Some(_) => node.postalCode
            | _ => None
            }
          )
          ->Array.reduce("", (acc, postalCode) =>
            acc ++ (acc->Js.String2.length > 0 ? " | " : "") ++ postalCode
          )
        )
        ->Option.getWithDefault(""),
        supplierCity: order.supplier
        ->Option.map(supplier =>
          supplier.locations.edges
          ->Array.keepMap(({node}) =>
            switch node.city {
            | Some(_) => node.city
            | _ => None
            }
          )
          ->Array.reduce("", (acc, city) =>
            acc ++ (acc->Js.String2.length > 0 ? " | " : "") ++ city
          )
        )
        ->Option.getWithDefault(""),
        supplierCountry: order.supplier
        ->Option.map(supplier =>
          supplier.locations.edges
          ->Array.keepMap(({node}) =>
            switch node.country {
            | Some(_) => node.country
            | _ => None
            }
          )
          ->Array.reduce("", (acc, country) =>
            acc ++ (acc->Js.String2.length > 0 ? " | " : "") ++ country
          )
        )
        ->Option.getWithDefault(""),
      },
    })
  // Data:
  // After Order reception (without stocks updating)
  | (Some(_), Executed({data: Some({order: Some(order)})}), {data: None, networkStatus}, Logged(_))
    if networkStatus === SkippedOrNotPresent || networkStatus === Ready =>
    Data({
      name: order.formattedName,
      updatedAt: Some(order.updatedAt),
      statuses: order.formattedStatus,
      updatedStocks: None,
      receptionFinishedAt: order.receptionFinishedAt,
      initialValues: {
        cart: order.cart->Accounting.Serializer.deserialize,
        note: order.note->Option.getWithDefault(""),
        condition: order.condition->Option.getWithDefault(""),
        noteForSupplier: order.noteForSupplier->Option.getWithDefault(""),
        supplierId: order.supplier->Option.map(supplier => supplier.id)->Option.getWithDefault(""),
        issueDate: order.issueDate,
        estimatedReceptionDate: order.estimatedReceptionDate,
        shopId: order.shop.id,
        deviceId: order.device.id,
        supplierName: order.supplier
        ->Option.map(supplier => supplier.companyName)
        ->Option.getWithDefault(""),
        supplierAddress: order.supplier
        ->Option.map(supplier =>
          supplier.locations.edges
          ->Array.keepMap(({node}) =>
            switch node.address {
            | Some(_) => node.address
            | _ => None
            }
          )
          ->Array.reduce("", (acc, address) =>
            acc ++ (acc->Js.String2.length > 0 ? " | " : "") ++ address
          )
        )
        ->Option.getWithDefault(""),
        supplierPostalCode: order.supplier
        ->Option.map(supplier =>
          supplier.locations.edges
          ->Array.keepMap(({node}) =>
            switch node.postalCode {
            | Some(_) => node.postalCode
            | _ => None
            }
          )
          ->Array.reduce("", (acc, postalCode) =>
            acc ++ (acc->Js.String2.length > 0 ? " | " : "") ++ postalCode
          )
        )
        ->Option.getWithDefault(""),
        supplierCity: order.supplier
        ->Option.map(supplier =>
          supplier.locations.edges
          ->Array.keepMap(({node}) =>
            switch node.city {
            | Some(_) => node.city
            | _ => None
            }
          )
          ->Array.reduce("", (acc, city) =>
            acc ++ (acc->Js.String2.length > 0 ? " | " : "") ++ city
          )
        )
        ->Option.getWithDefault(""),
        supplierCountry: order.supplier
        ->Option.map(supplier =>
          supplier.locations.edges
          ->Array.keepMap(({node}) =>
            switch node.country {
            | Some(_) => node.country
            | _ => None
            }
          )
          ->Array.reduce("", (acc, country) =>
            acc ++ (acc->Js.String2.length > 0 ? " | " : "") ++ country
          )
        )
        ->Option.getWithDefault(""),
      },
    })
  // Failed:
  // When there's a graphql timeout or DB synchronisation issue
  | _ => Error
  }

let use = (~id, ~authState, ~orderQuery as response, ~supplierId, ~shopId) => {
  let (derived, setDerived) = React.useState(_ => {
    id,
    response,
    authState,
  })

  React.useEffect2(() => {
    switch (response, derived.response, derived.id) {
    | (Executed({loading: true}), Executed({data: None}), None) => ()
    | (Executed({loading: true}), Executed({data: Some(_)}), _) => ()
    | _ => setDerived(_ => {response, id, authState})
    }
    None
  }, (response, authState))

  make(
    ~id=derived.id,
    ~orderQuery=derived.response,
    ~authState=derived.authState,
    ~supplierId,
    ~shopId,
  )
}
