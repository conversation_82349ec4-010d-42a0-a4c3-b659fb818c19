open Intl

module Config = OrderEdit__Config

module OrderQuery = OrderEdit__Queries.OrderQuery
module OrderVariantsStockQuery = OrderEdit__Queries.OrderVariantsStockQueryHook

@react.component
let make = (~id=?, ~supplierId=?, ~shopId=?) => {
  let authState = Auth.useState()
  let navigate = Navigation.useNavigate()
  let notifier = Notifier.use()
  let (cartRowErrors, setCartRowErrors) = React.useState(_ => [])
  let (executeQuery, queryResults) = OrderQuery.useLazy()

  React.useEffect1(() => {
    if id->Option.isSome {
      executeQuery({id: id->Option.getWithDefault("")})->ignore
    }
    None
  }, [id])

  let orderVariantsStockQuery = OrderVariantsStockQuery.use(
    ~skip=switch queryResults {
    | Executed({data: Some({order: Some({formattedStatus})})}) =>
      !OrderEdit.isBeforeReception(~statuses=formattedStatus)
    | _ => true
    },
    ~ready=switch queryResults {
    | Executed({data: Some({order: Some(_)})}) => true
    | _ => false
    },
    ~id,
  )
  let config = Config.use(
    ~orderQuery=queryResults,
    ~orderVariantsStockQuery,
    ~authState,
    ~id,
    ~supplierId,
    ~shopId,
  )

  let onSubmitSuccess = React.useCallback1(submissionResponse =>
    switch (submissionResponse, id) {
    | (Some(id), None) =>
      navigate(Order->LegacyRouter.routeToPathname ++ "/" ++ id, ~replace=true)
      notifier.clear()
    | (Some(id), Some(_)) =>
      executeQuery(OrderQuery.makeVariables(~id, ()))->ignore
      notifier.clear()
    | (_, _) => notifier.add(Error(Request.serverErrorMessage), ())
    }
  , [id])
  let onSubmitFailure = React.useCallback(message => notifier.reset(Error(message), ()))

  switch config {
  | Error => <Placeholder status=Error />
  | Loading => <Placeholder status=Loading />
  | Data({initialValues, name, statuses, updatedAt, receptionFinishedAt}) =>
    <OrderEditForm.FormLegacyProvider
      ?id
      initialValues
      schema=[
        OrderEditForm.Schema.CustomString(
          SupplierId,
          (value, _) =>
            switch value {
            | "" => Error(t("Missing supplier"))
            | _ => Ok()
            },
        ),
        Custom(
          Cart,
          ({cart}) => {
            let cartErrors = cart.products->Array.keepMap(product => {
              let (productId, quantity) = switch product {
              | Unit({id, quantity}) => (id, quantity->Int.toFloat)
              | Bulk({id, quantity}, _) => (id, quantity->Big.toFloat)
              }

              quantity <= 0.
                ? Some({
                    Table.key: productId,
                    message: t("Ordered quantity must be greater than 0."),
                  })
                : None
            })
            let validated = if !OrderEdit.isBeforeReception(~statuses) {
              cart.products->Array.size > 0
            } else {
              setCartRowErrors(previousCartRowErrors => {
                // NOTE — perf optimization
                if previousCartRowErrors->Array.length === 0 && cartErrors->Array.length === 0 {
                  previousCartRowErrors
                } else {
                  cartErrors
                }
              })
              cart.products->Array.size > 0 && cartErrors->Array.size === 0
            }

            validated ? Ok() : Error("The order should have at least one product in cart.")
          },
        ),
      ]
      onSubmitFailure
      onSubmitSuccess>
      <OrderEditPage
        id
        name
        statuses
        updatedAt
        cartRowErrors
        receptionFinishedAt
        onRequestCartRowsError={errors => setCartRowErrors(_ => errors)}
      />
    </OrderEditForm.FormLegacyProvider>
  }
}

let make = React.memo(make)
