module OrderQuery = %graphql(`
    query OrderQuery($id: ID!) {
      order(id: $id) {
        id
        updatedAt
        formattedName
        formattedStatus @ppxOmitFutureValue
        cart
        note
        condition
        noteForSupplier
        supplier {
          id
          companyName
          locations {
            edges {
              node {
                address
                postalCode
                city
                country
              }
            }
          }
        }
        issueDate
        estimatedReceptionDate
        receptionFinishedAt
        shop { id }
        device { id }
      }
    }
  `)

module OrderVariantsStockQuery = %graphql(`
    query OrderVariantsStockQuery($id: ID!, $first: Int, $after: String) {
      order(id: $id) {
        products(first: $first, after: $after) {
          pageInfo {
            endCursor
            hasNextPage
          }
          edges {
            node {
              id
              variant {
                stock {
                  rawQuantity
                }
              }
            }
          }
        }
      }
    }
  `)

module OrderVariantsStockQueryHook = {
  let use = (~skip, ~ready, ~id) => {
    let terminated = React.useRef(false)

    React.useEffect1(() => {
      if ready && skip {
        terminated.current = true
      }
      None
    }, [ready, skip])

    let query = OrderVariantsStockQuery.use(
      OrderVariantsStockQuery.makeVariables(~id=id->Option.getWithDefault(""), ~first=5, ()),
      ~skip=terminated.current || ready ? skip : true,
    )

    switch query {
    | {data: Some({order: Some(order)}), networkStatus: Ready} =>
      switch (order.products.pageInfo.endCursor, order.products.pageInfo.hasNextPage) {
      | (Some(cursor), Some(true)) =>
        query.fetchMore(
          ~variables=OrderVariantsStockQuery.makeVariables(
            ~id=id->Option.getWithDefault(""),
            ~first=5,
            ~after=cursor,
            (),
          ),
          ~updateQuery=(prevResult, {fetchMoreResult}) =>
            switch (prevResult, fetchMoreResult) {
            | ({order: Some(prevOrder)}, Some({order: Some(newOrder)})) => {
                order: Some({
                  ...newOrder,
                  products: {
                    ...newOrder.products,
                    edges: prevOrder.products.edges->Array.concat(newOrder.products.edges),
                  },
                }),
              }
            | _ => prevResult
            },
          (),
        )->ignore
      | (_, Some(false)) => terminated.current = true
      | _ => ()
      }
    | _ => ()
    }

    {
      ...query,
      loading: !terminated.current,
      networkStatus: switch (query.networkStatus, terminated.current) {
      | (Loading, true) => Ready
      | _ => query.networkStatus
      },
    }
  }
}
