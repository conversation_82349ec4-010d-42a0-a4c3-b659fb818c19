let baseRoute = "/orders"

@react.component
let make = () => {
  let userRole = Auth.useRole()
  let url = Navigation.useUrl()
  let (supplierId, shopId) = OrderUrlQueryString.CreateOrder.decode(url.query)

  if Auth.isAuthorizedAccess(~role=userRole, ~targetPathname=baseRoute) {
    <Notifier.Provider value={Notifier.createContext()}>
      {switch url.path->List.fromArray {
      | list{"orders", "create"} => <OrderEditContainer ?supplierId ?shopId />
      | list{"orders", id} => <OrderEditContainer id />
      | list{_} => <OrderListPage />
      | _ => <Navigation.Redirect route=baseRoute />
      }}
    </Notifier.Provider>
  } else {
    <AccessDeniedPage />
  }
}
