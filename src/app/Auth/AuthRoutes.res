let basePathname = "auth"
let baseRoute = "/" ++ basePathname

let loginRoute = baseRoute ++ "/login"
let loginSessionFromWebsiteRoute = loginRoute ++ "/from-website"
let loginSessionExpiredRoute = loginRoute ++ "/session-expired"
let loginImpersonationFailureRoute = loginRoute ++ "/impersonation-failure"
let loginRecoverySuccessRoute = loginRoute ++ "/password-reset"
let loginUsernameUpdateSuccessRoute = loginRoute ++ "/username-updated"
let logoutRoute = baseRoute ++ "/logout"
let logoutSessionExpiredRoute = logoutRoute ++ "/session-expired"
let logoutImpersonationFailureRoute = logoutRoute ++ "/impersonation-failure"
let recoveryRoute = baseRoute ++ "/recovery"
let resetRoute = (~token) => baseRoute ++ "/reset/" ++ token
let impersonationRoute = (~userId) => baseRoute ++ "/impersonation/" ++ userId
let loginRecoveryEmailSentRoute = loginRoute ++ "/recovery-email-sent"
let signupSuccess = baseRoute ++ "/signup/success"
