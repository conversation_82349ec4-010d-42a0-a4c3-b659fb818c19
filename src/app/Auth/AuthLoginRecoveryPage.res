open Intl
open StyleX

module LoginRecoveryFormLenses = %lenses(type state = {email: string})
module LoginRecoveryForm = Form.Make(LoginRecoveryFormLenses)

let styles = StyleX.create({
  "root": style(
    ~display=#flex,
    ~flex="1",
    ~flexDirection=#column,
    ~justifyContent=#center,
    ~alignItems=#center,
    ~width="465px",
    ~marginTop="-22%",
    (),
  ),
  "title": style(
    ~display=#flex,
    ~flexDirection=#column,
    ~width="100%",
    ~alignItems=#center,
    ~paddingBlock=Spaces.largePx,
    (),
  ),
  "subtitle": style(
    ~textAlign=#center,
    ~backgroundColor=Colors.backgroundDefaultColortemplate,
    ~marginVertical=Spaces.normalPx,
    ~marginHorizontal="15px",
    ~font=FontFaces.libreFranklinRegular,
    ~color=Colors.neutralColor70,
    ~lineHeight="21px",
    (),
  ),
})

let styleProps = () => StyleX.props([styles["root"]])
let titleStyleProps = () => StyleX.props([styles["title"]])
let subtitleStyleProps = () => StyleX.props([styles["subtitle"]])

module PasswordRecoveryRequest = {
  let endpoint = Env.gatewayUrl() ++ "/auth/password-forgot"

  let encodeBody = (~username) =>
    Js.Dict.fromArray([("username", username->Json.encodeString)])->Json.encodeDict

  let make = (~username) =>
    Request.make(endpoint, ~method=#POST, ~bodyJson=encodeBody(~username), ~authTokenRequired=false)
}

let passwordRecoveryRequest = PasswordRecoveryRequest.make

let schema = [LoginRecoveryForm.Schema.Email(Email)]

@react.component
let make = (~loginRecoveryEmailSentRoute, ~passwordRecoveryRequest) => {
  let (notification, setNotfication) = React.useState(() => None)
  let onSubmit = (_, {LoginRecoveryFormLenses.email: username}) =>
    passwordRecoveryRequest(~username)
    ->Future.mapOk(_ => Some(t("You should have received the recovery link by the next minutes.")))
    ->Future.mapError(_ =>
      t("An unexpected error occured. Please try again or contact the support.")
    )

  let navigate = Navigation.useNavigate()
  let onSubmitSuccess = res =>
    switch res {
    | Some(_) => navigate(loginRecoveryEmailSentRoute)
    | _ =>
      setNotfication(_ => Some(
        Banner.Danger(t("An unexpected error occured. Please try again or contact the support.")),
      ))
    }

  let onSubmitFailure = error => setNotfication(_ => Some(Banner.Danger(error)))

  let formPropState = LoginRecoveryForm.useFormPropState({
    initialValues: {email: ""},
    schema,
    onSubmitSuccess,
    onSubmitFailure,
  })

  let onRequestCloseNotificationBanner = () => setNotfication(_ => None)

  let {?style, ?className} = styleProps()
  let {style: ?titleStyle, className: ?titleClassName} = titleStyleProps()
  let {style: ?subtitleStyle, className: ?subtitleClassName} = subtitleStyleProps()

  <div ?style ?className>
    <div style=?titleStyle className=?titleClassName>
      <Title level=#2 align=#center> {t("Recover your password")->React.string} </Title>
      {switch notification {
      | Some(notification) =>
        <AuthLoginPage.NotificationBanner
          notification onRequestClose=onRequestCloseNotificationBanner
        />
      | None => React.null
      }}
      <span style=?subtitleStyle className=?subtitleClassName>
        {t(
          "Type the email address associated with your account, and we will send you a link to reset your password. If you don’t receive the email, please check your spam folder. If no email arrives, it means there is no account associated with that address.",
        )->React.string}
      </span>
    </div>
    <LoginRecoveryForm.FormProvider propState=formPropState>
      <LoginRecoveryForm.ControlEnterKey onSubmit />
      <Stack space=#large>
        <LoginRecoveryForm.InputText
          field=Email label={t("Email")} placeholder={t("<EMAIL>")} hideRequired=true
        />
        <Box spaceY=#xsmall>
          <LoginRecoveryForm.SubmitButton text={t("Send a recovery link")} onSubmit />
        </Box>
      </Stack>
    </LoginRecoveryForm.FormProvider>
  </div>
}
