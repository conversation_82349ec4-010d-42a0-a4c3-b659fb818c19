open Intl
open StyleX

module LoginResetFormLenses = %lenses(type state = {password: string, confirm: string})
module LoginResetForm = Form.Make(LoginResetFormLenses)

let styles = StyleX.create({
  "container": style(
    ~flex="1",
    ~display=#flex,
    ~flexDirection=#column,
    ~justifyContent=#center,
    ~alignItems=#center,
    ~width="465px",
    ~marginTop="-10%",
    (),
  ),
  "title": style(
    ~width="100%",
    ~display=#flex,
    ~flexDirection=#column,
    ~alignItems=#center,
    ~paddingBlock=Spaces.largePx,
    (),
  ),
})

// TODO - Here we extract the error message from the JSON body, this should be standard and done in Request.res
type queryResult = Success | ResetPasswordServerError(option<string>)

module ResetPasswordRequest = {
  let endpoint = Env.gatewayUrl() ++ "/auth/password-reset"

  let encodeBody = (~tokenId, ~token, ~userId, ~newPassword) =>
    Js.Dict.fromArray([
      ("tokenId", tokenId->Json.encodeString),
      ("token", token->Json.encodeString),
      ("userId", userId->Json.encodeString),
      ("newPassword", newPassword->Json.encodeString),
    ])->Json.encodeDict

  type serverFailure =
    ExpiredOneTimeToken | NotFoundPasswordResetOneTimeToken | UnknownServerFailure

  let decodeInvalidRequestFailure = error =>
    switch error {
    | {Request.kind: "ExpiredOneTimeToken"} => ExpiredOneTimeToken
    | {Request.kind: "NotFoundPasswordResetOneTimeToken"} => NotFoundPasswordResetOneTimeToken
    | _ => UnknownServerFailure
    }

  let make = (~tokenId, ~token, ~userId, ~newPassword) =>
    Request.make(
      endpoint,
      ~method=#POST,
      ~bodyJson=encodeBody(~tokenId, ~token, ~userId, ~newPassword),
      ~authTokenRequired=false,
    )->Future.mapError(error =>
      switch error {
      | InvalidRequestFailures(invalidRequestFailures) =>
        invalidRequestFailures[0]->Option.map(decodeInvalidRequestFailure)
      | _ => None
      }
    )
}

let resetPasswordRequest = ResetPasswordRequest.make

let schema = [
  LoginResetForm.Schema.Password(Password),
  CustomString(
    Confirm,
    (value, values) => {
      if values.password === value {
        Ok()
      } else {
        Error(t("Password and its confirmation must be identical"))
      }
    },
  ),
]

@react.component
let make = (~userId, ~tokenId, ~token, ~successRoute, ~resetPasswordRequest) => {
  let (notification, setNotfication) = React.useState(() => None)
  let navigate = Navigation.useNavigate()

  let onSubmitSuccess = res =>
    switch res {
    | Some(message) if message === t("Your password has been correctly modified") =>
      navigate(successRoute)
    | Some(message) => setNotfication(_ => Some(Banner.Danger(message)))
    | _ => ()
    }

  let onSubmitFailure = error => {
    setNotfication(_ => Some(Banner.Danger(error)))
  }

  let formPropState = LoginResetForm.useFormPropState({
    initialValues: {password: "", confirm: ""},
    schema,
    onSubmitSuccess,
    onSubmitFailure,
  })

  let onSubmit = (_, {LoginResetFormLenses.password: newPassword}) =>
    resetPasswordRequest(~tokenId, ~token, ~userId, ~newPassword)
    ->Future.mapOk(_ => Some(t("Your password has been correctly modified")))
    ->Future.mapError(error =>
      switch error {
      | Some(ResetPasswordRequest.ExpiredOneTimeToken) | Some(NotFoundPasswordResetOneTimeToken) =>
        t("The link has expired")
      | _ => t("An unexpected error occured. Please try again or contact the support.")
      }
    )

  let onRequestCloseNotificationBanner = () => setNotfication(_ => None)

  <DivX style={StyleX.props([styles["container"]])}>
    <DivX style={StyleX.props([styles["title"]])}>
      <Title level=#1> {t("Password resetting")->React.string} </Title>
    </DivX>
    {switch notification {
    | Some(notification) =>
      <AuthLoginPage.NotificationBanner
        notification onRequestClose=onRequestCloseNotificationBanner
      />
    | None => React.null
    }}
    <LoginResetForm.FormProvider propState=formPropState>
      <LoginResetForm.ControlEnterKey onSubmit />
      <Stack space=#large>
        <LoginResetForm.InputPassword
          field=Password
          label={t("New password")}
          placeholder={t("New password")}
          showTypingValidation=true
          hideError=true
          hideRequired=true
        />
        <LoginResetForm.InputPassword
          field=Confirm
          label={t("New password confirmation")}
          placeholder={t("New password confirmation")}
          hideRequired=true
        />
        <Box spaceY=#xsmall>
          <LoginResetForm.SubmitButton text={t("Reset password")} onSubmit />
        </Box>
      </Stack>
    </LoginResetForm.FormProvider>
  </DivX>
}
