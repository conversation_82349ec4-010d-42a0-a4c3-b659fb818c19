open Intl
open StyleX

module GettingStartedTextIconLink = {
  let styles = StyleX.create({
    "root": style(
      ~font=FontFaces.libreFranklinSemiBold,
      ~color=Colors.neutralColor90,
      ~letterSpacing="0.125px",
      (),
    ),
    "hovered": style(~color=Colors.brandColor50, ~textDecorationLine=#underline, ()),
  })

  let textStyleProps = (~hovered) =>
    StyleX.props([styles["root"], hovered ? styles["hovered"] : style()])

  let iconColorFromHovered = (~hovered) => hovered ? Colors.brandColor60 : Colors.neutralColor100

  @react.component
  let make = () => {
    let (ref, hovered) = Hover.use()
    let {style: ?textStyle, className: ?textClassName} = textStyleProps(~hovered)

    <Navigation.Link
      to=Navigation.Url(Url.make(HelpCenter.gettingStartedGuideLink)) openNewTab=true>
      <Box ref>
        <Inline space=#small>
          <Icon name=#book_bold fill={iconColorFromHovered(~hovered)} />
          <span style=?textStyle className=?textClassName>
            {t("Getting started guide")->React.string}
          </span>
        </Inline>
      </Box>
    </Navigation.Link>
  }
}

module Header = {
  @module("./wino_logo.png") external imageUri: string = "default"

  @react.component
  let make = () => {
    <Inline align=#spaceBetween>
      <Box>
        <Inline space=#medium>
          <img
            style={ReactDOM.Style.make(~height="40px", ~width="40px", ~alignSelf="center", ())}
            src=imageUri
          />
          <TextStyle size=#normal>
            {t("How to contact us? By <NAME_EMAIL>")->React.string}
          </TextStyle>
        </Inline>
      </Box>
      <Box>
        <GettingStartedTextIconLink />
      </Box>
    </Inline>
  }
}

let styles = StyleX.create({
  "root": style(
    ~width="100%",
    ~height="100%",
    ~maxWidth="1480px",
    ~paddingTop=Spaces.xlargePx,
    ~paddingBottom=Spaces.xxhugePx,
    ~paddingInline=Spaces.xlargePx,
    (),
  ),
  "content": style(
    ~alignSelf=#center,
    ~justifyContent=#center,
    ~height="75%",
    ~maxWidth="724px",
    (),
  ),
})
let styleProps = () => StyleX.props([styles["root"]])
let contentStyleProps = () => StyleX.props([styles["content"]])

let textConfirmation = t(
  "A confirmation email will be sent to you in the next few minutes. Our team will contact you soon to set up the software. You can now close this window.",
)

@react.component
let make = () => {
  let {?style, ?className} = styleProps()
  let {style: ?contentStyle, className: ?contentClassName} = contentStyleProps()

  <div ?style ?className>
    <Header />
    <div style=?contentStyle className=?contentClassName>
      <Stack align=#center space=#normal>
        <Title level=#1 align=#center>
          {t("🎉 Your account has been created successfully!")->React.string}
        </Title>
        <TextStyle align=#center variation=#normal> {textConfirmation->React.string} </TextStyle>
      </Stack>
    </div>
  </div>
}

let make = React.memo(make)
