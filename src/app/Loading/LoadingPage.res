open Intl
open StyleX

let styles = StyleX.create({
  "root": style(
    ~flex="1",
    ~display=#flex,
    ~flexDirection=#column,
    ~justifyContent=#center,
    ~alignItems=#center,
    ~marginTop="-100px",
    (),
  ),
})

@react.component
let make = (~text=t("Loading...")) =>
  <DivX style={StyleX.props([styles["root"]])}>
    <Box spaceY=#small>
      <Spinner size=40. />
    </Box>
    <Stack>
      <Box spaceY=#xsmall>
        <Title level=#3 align=#center> {text->React.string} </Title>
      </Box>
      <Box spaceX=#xhuge>
        <TextStyle variation=#normal align=#center> {t("Please wait.")->React.string} </TextStyle>
      </Box>
    </Stack>
  </DivX>
