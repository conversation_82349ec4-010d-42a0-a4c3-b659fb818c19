let baseRoute = "/stocktransfer"
let createRoute = baseRoute ++ "/create"

@react.component
let make = (~subUrlPath) => {
  let userRole = Auth.useRole()

  if Auth.isAuthorizedAccess(~role=userRole, ~targetPathname=baseRoute) {
    <Notifier.Provider value={Notifier.createContext()}>
      {switch subUrlPath {
      | list{} => <StockTransferListPage stockTransferCreateRoute=createRoute />
      | list{"create"} => <StockTransferCreatePage />
      | _ => <Navigation.Redirect route=baseRoute />
      }}
    </Notifier.Provider>
  } else {
    <AccessDeniedPage />
  }
}
