open Intl

module Query = %graphql(`
  query StockTransferListPageQuery($search: String, $filterBy: InputStockTransfersQueryFilter, $before: String, $after: String, $first: Int, $last: Int) {
    stockTransfers(search: $search, filterBy: $filterBy, before: $before, after: $after, first: $first, last: $last) {
      pageInfo {
        startCursor
        endCursor
      }
      totalCount
      edges {
        node {
          id
          code
          createdAt
          senderShop {
            name
          }
          recipientShop {
            name
          }
          products {
            totalCount
          }
        }
      }
    }
  }
`)

module Filters = {
  type t = {createdAtDateRange: option<(Js.Date.t, Js.Date.t)>}

  let encoder = ({createdAtDateRange}) =>
    createdAtDateRange->Option.map(((start, end)) => [start->Js.Date.valueOf, end->Js.Date.valueOf])

  let decoder = createdAtDateRange => Ok({
    createdAtDateRange: createdAtDateRange->Option.flatMap(range =>
      switch range {
      | [start, end] => Some((start->Js.Date.fromFloat, end->Js.Date.fromFloat))
      | _ => None
      }
    ),
  })

  let useJsonCodec = () =>
    JsonCodec.object1(
      encoder,
      decoder,
      JsonCodec.field("createdAtDateRange", JsonCodec.array(JsonCodec.float))->JsonCodec.optional,
    )
}

module Row = {
  type t = {
    id: string,
    code: string,
    date: Js.Date.t,
    senderShop: string,
    recipientShop: string,
    productsCount: int,
  }
}

module Scaffolded = Scaffold.Make({
  type filters = Filters.t
  let useFiltersJsonCodec = Filters.useJsonCodec

  module QueryInner = Query.Query_inner
  type queryVariableFilterBy = Query.t_variables_InputStockTransfersQueryFilter
  let useQuery = Query.use

  let makeQueryVariables = (
    _defaultQueryVariables,
    ~connectionArguments,
    ~search=?,
    ~filterBy=?,
    (),
  ) => {
    QueryInner.first: connectionArguments.first,
    last: connectionArguments.last,
    before: connectionArguments.Scaffold.before,
    after: connectionArguments.after,
    search,
    filterBy,
  }

  let makeQueryVariablesFilterBy = ({Filters.createdAtDateRange: createdAtDateRange}) => {
    Query.createdAt: switch createdAtDateRange {
    | Some((start, end)) =>
      Some({
        _after: None,
        _before: None,
        _between: Some([start->Scalar.Datetime.serialize, end->Scalar.Datetime.serialize]),
      })
    | _ => None
    },
  }

  let totalCountFromQueryData = ({Query.stockTransfers: stockTransfers}) =>
    stockTransfers.totalCount
  let cursorsFromQueryData = ({Query.stockTransfers: stockTransfers}) => (
    stockTransfers.pageInfo.startCursor,
    stockTransfers.pageInfo.endCursor,
  )

  type row = Row.t
  let rowsFromQueryDataAndState = ({Query.stockTransfers: stockTransfers}, _) =>
    stockTransfers.edges->Array.map(edge => {
      Row.id: edge.node.id,
      code: edge.node.code,
      date: edge.node.createdAt,
      senderShop: edge.node.senderShop.name,
      recipientShop: edge.node.recipientShop.name,
      productsCount: edge.node.products.totalCount,
    })
  let keyExtractor = ({Row.id: id}) => id
})

let scaffoldedColumns = [
  {
    Scaffold.name: t("Reference"),
    layout: {minWidth: 120.->#px, sticky: true},
    render: ({Row.code: code}) => <TextStyle> {code->React.string} </TextStyle>,
  },
  {
    name: t("Datetime"),
    layout: {minWidth: 120.->#px},
    render: ({date}) => <TableDatetimeCell value=date />,
  },
  {
    name: t("Issuing shop"),
    layout: {width: 25.->#pct},
    render: ({senderShop}) => <TextStyle> {senderShop->React.string} </TextStyle>,
  },
  {
    name: t("Recipient shop"),
    layout: {width: 25.->#pct},
    render: ({recipientShop}) => <TextStyle> {recipientShop->React.string} </TextStyle>,
  },
  {
    layout: {width: 200.->#px},
    name: t("Number of unique references"),
    render: ({productsCount}) => <TextStyle> {productsCount->React.int} </TextStyle>,
  },
  {
    layout: {minWidth: 70.->#px, width: 70.->#px},
    render: ({id: queryVariableId, code}) => {
      let notifier = Notifier.use()

      <StockTransferTableRowActions
        id=queryVariableId code onError={message => notifier.add(Error(message), ())}
      />
    },
  },
]

@react.component
let make = (~stockTransferCreateRoute) => {
  let notifier = Notifier.use()

  let initialFilters = {Filters.createdAtDateRange: None}
  let initialState = Scaffolded.makeInitialState(~filters=initialFilters)
  let (state, dispatch) = Scaffolded.use(() => initialState)

  let defaultQueryVariables = Query.makeVariables()

  let filters =
    <Inline space=#small>
      <SelectDateRangeFilter
        placeholder={t("Since the beginning")}
        value=?state.filters.createdAtDateRange
        onChange={createdAtDateRange =>
          FiltersUpdated(_ => {createdAtDateRange: createdAtDateRange})->dispatch}
        triggerLabelDisplay=#showPreset
      />
      {switch state.filters {
      | {createdAtDateRange: Some(_)} =>
        <Scaffold.ResetFiltersButton
          onPress={() => FiltersUpdated(_ => initialState.filters)->dispatch}
        />
      | _ => React.null
      }}
    </Inline>

  let actions =
    <ButtonLink variation=#primary to=Route(stockTransferCreateRoute)>
      {t("Create stock transfer")->React.string}
    </ButtonLink>

  let banner = <Notifier.Banner notifier />

  let searchBar =
    <SearchBar
      value=?state.searchQuery
      placeholder={t("Search a stock transfer")}
      onChange={searchQuery => Searched(searchQuery)->dispatch}
    />

  let emptyState = switch state {
  | {currentPage: 1, searchQuery: None, filters: {createdAtDateRange: None}} =>
    <EmptyState
      illustration=Illustration.create title={t("Welcome to the stock transfers space.")}
    />
  | _ =>
    <EmptyState
      illustration=Illustration.notFound
      title={t("No result were found.")}
      text={t("Try again with another keyword/filter or:")}>
      <Button variation=#neutral onPress={_ => Reset(initialState)->dispatch}>
        {t("Clear search query and filters")->React.string}
      </Button>
    </EmptyState>
  }

  <Scaffolded
    title={t("Stock transfers")}
    state
    dispatch
    columns=scaffoldedColumns
    filters
    actions
    banner
    searchBar
    emptyState
    defaultQueryVariables
  />
}

let make = React.memo(make)
