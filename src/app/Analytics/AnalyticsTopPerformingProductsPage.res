open Intl

module Query = %graphql(`
  query AnalyticsTopPerformingProductsPageQuery($filterBy: TopPerformingProductsQueryFilter, $sortBy: TopPerformingProductsQuerySort) @ppxConfig(schema: "graphql_schema_analytics.json") {
    topPerformingProducts(filterBy: $filterBy, sortBy: $sortBy) {
      pageInfo {
        startCursor
        endCursor
      }
      edges {
        node {
          margin
          marginRate
          markupRate
          totalPurchaseCost
          revenueIncludingTaxes
          revenueExcludingTaxes
          occurrences
          quantity
          capacityPrecision
          capacityUnit
          name
          color @ppxOmitFutureValue
          kind @ppxOmitFutureValue
          stockRawQuantity
          stockKeepingUnit
          purchasedPrice
          cku
          description
          variantId
        } 
      }
      totalCount
    }
  }
`)

module SortBy = {
  type t = Quantity | RevenueIncludingTaxes | Margin

  let values = [Quantity, RevenueIncludingTaxes, Margin]
  let toLabel = value =>
    switch value {
    | Quantity => t("Sold quantity")
    | RevenueIncludingTaxes => t("Revenue")
    | Margin => t("Margin")
    }
  let toRawString = value =>
    switch value {
    | Quantity => "QUANTITY"
    | RevenueIncludingTaxes => "REVENUE_INCLUDING_TAXES"
    | Margin => "MARGIN"
    }
  let toQueryVariableValue = value =>
    switch value {
    | Quantity => #QUANTITY
    | RevenueIncludingTaxes => #REVENUE_INCLUDING_TAXES
    | Margin => #MARGIN
    }
}

module Filters = {
  type t = {
    shop: Auth.shop,
    dateRange: (Js.Date.t, Js.Date.t),
  }

  let encoder = ({shop, dateRange: (start, end)}) => (
    shop.id,
    [start->Js.Date.valueOf, end->Js.Date.valueOf],
  )

  let decoder = (~shops, (shopId, dateRange)) => Ok({
    shop: {
      let maybeShop = shops->Array.getBy((shop: Auth.shop) => shop.id === shopId)
      switch maybeShop {
      | Some(shop) => shop
      | None => shops->Array.getUnsafe(0)
      }
    },
    dateRange: {
      let startDate = dateRange->Array.getUnsafe(0)
      let endDate = dateRange->Array.getUnsafe(1)
      (startDate->Js.Date.fromFloat, endDate->Js.Date.fromFloat)
    },
  })

  let useJsonCodec = () => {
    let shops = Auth.useShops()

    JsonCodec.object2(
      encoder,
      decoder(~shops),
      JsonCodec.field("shopId", JsonCodec.string),
      JsonCodec.field("dateRange", JsonCodec.array(JsonCodec.float)),
    )
  }
}

module Row = {
  type t = {
    margin: string,
    marginRate: string,
    markupRate: string,
    totalPurchaseCost: string,
    revenueIncludingTaxes: float,
    revenueExcludingTaxes: float,
    unitSoldQuantity: int,
    bulkSoldQuantity: option<string>,
    stockQuantity: string,
    purchasedPrice: string,
    name: string,
    color: option<CatalogProduct.Color.t>,
    kind: CatalogProduct.Kind.t,
    stockKeepingUnit: option<string>,
    cku: option<string>,
    description: string,
    variantId: string,
  }

  // NOTE - Until now, the GraphQL API sends us the formatted values.
  // In the case of this page (and the topProducts query), the frontend is
  // in charge of formatting the displayed values, which is fine.
  // At some point, the following functions can be moved into some primitives
  // (Intl module ?) that can be used throughout the whole repo.
  // It is also discussed to share some formatting utilities between the backend
  // (sheet service, pdf service …) and the frontend. To investigate later.

  type intl = Intl(Intl.numberUnit) | NonIntl(string)
  type capacity = Bulk(intl, int) | Unit

  let unapplyArbitraryPrecision = (quantity, ~precision) =>
    quantity->Int.toFloat /. Js.Math.pow_float(~base=10., ~exp=precision->Int.toFloat)

  let formatStockQuantity = (quantity, ~capacity) =>
    switch capacity {
    | Bulk(unit, precision) =>
      let quantity = unapplyArbitraryPrecision(quantity, ~precision)

      switch unit {
      | Intl(intlUnit) => quantity->Intl.unitFormat(~unit=intlUnit)
      | NonIntl(unit) => quantity->Intl.decimalFormat ++ " " ++ unit
      }
    | Unit => quantity->Int.toFloat->Intl.decimalFormat
    }

  let formatPurchasedPrice = (value, ~capacity, ~currency) =>
    switch capacity {
    | Bulk(unit, _) =>
      value->Intl.currencyFormat(~currency, ~maximumFractionDigits=3) ++
      "/" ++
      switch unit {
      | Intl(unit) => unit->numberUnitToString
      | NonIntl(unit) => unit
      }
    | Unit => value->Intl.currencyFormat(~currency, ~maximumFractionDigits=3)
    }

  let fromQueryDataEdge = ({Query.node: node}) => {
    let currency = #EUR
    let capacity = switch (node.capacityPrecision, node.capacityUnit) {
    | (Some(precision), Some("g") | Some("G")) => Bulk(Intl(#gram), precision)
    | (Some(precision), Some("kg") | Some("KG")) => Bulk(Intl(#kilogram), precision)
    | (Some(precision), Some("l") | Some("L")) => Bulk(Intl(#liter), precision)
    | (Some(precision), Some(unit)) => Bulk(NonIntl(unit), precision)
    | (_, _) => Unit
    }

    {
      variantId: node.variantId,
      cku: node.cku,
      name: node.name,
      description: node.description,
      color: node.color,
      kind: node.kind,
      margin: node.margin->currencyFormat(~currency),
      marginRate: (node.marginRate /. 100.)
        ->percentFormat(~minimumFractionDigits=2, ~maximumFractionDigits=2),
      markupRate: (node.markupRate /. 100.)
        ->percentFormat(~minimumFractionDigits=2, ~maximumFractionDigits=2),
      totalPurchaseCost: node.totalPurchaseCost->currencyFormat(~currency),
      revenueExcludingTaxes: node.revenueExcludingTaxes,
      revenueIncludingTaxes: node.revenueIncludingTaxes,
      unitSoldQuantity: switch capacity {
      | Bulk(_) => node.occurrences
      | Unit => node.quantity
      },
      bulkSoldQuantity: switch capacity {
      | Bulk(_) => Some(node.quantity->formatStockQuantity(~capacity))
      | Unit => None
      },
      stockKeepingUnit: node.stockKeepingUnit,
      stockQuantity: node.stockRawQuantity->Option.mapWithDefault(
        "0",
        formatStockQuantity(~capacity),
      ),
      purchasedPrice: node.purchasedPrice->Option.mapWithDefault(
        "—",
        formatPurchasedPrice(~currency, ~capacity),
      ),
    }
  }
}

module Scaffolded = Scaffold.Make({
  type filters = Filters.t
  let useFiltersJsonCodec = Filters.useJsonCodec

  module QueryInner = Query.Query_inner
  type queryVariableFilterBy = Query.t_variables_TopPerformingProductsQueryFilter
  let useQuery = Query.use

  let makeQueryVariables = (
    defaultQueryVariables,
    ~connectionArguments,
    ~search=?,
    ~filterBy=?,
    (),
  ) => {
    ignore((search, connectionArguments))
    {...defaultQueryVariables, QueryInner.filterBy}
  }

  let makeQueryVariablesFilterBy = ({Filters.shop: shop, dateRange: (startDate, endDate)}) =>
    Query.makeInputObjectTopPerformingProductsQueryFilter(
      ~shopIds={_in: [shop.id]},
      ~startDate=startDate->Scalar.Datetime.serialize,
      ~endDate=endDate->Scalar.Datetime.serialize,
      (),
    )

  let totalCountFromQueryData = ({QueryInner.topPerformingProducts: topPerformingProducts}) =>
    topPerformingProducts.totalCount
  let cursorsFromQueryData = ({QueryInner.topPerformingProducts: topPerformingProducts}) => (
    topPerformingProducts.pageInfo.startCursor,
    topPerformingProducts.pageInfo.endCursor,
  )

  type row = Row.t
  let rowsFromQueryDataAndState = (
    {QueryInner.topPerformingProducts: topPerformingProducts},
    {Scaffold.currentPage: currentPage},
  ) =>
    topPerformingProducts.edges
    ->Array.slice(~offset=(currentPage - 1) * Scaffold.edgesPerPage, ~len=Scaffold.edgesPerPage)
    ->Array.map(Row.fromQueryDataEdge)

  let keyExtractor = ({Row.variantId: variantId}) => variantId
})

let scaffoldedColumns = [
  {
    Scaffold.name: t("Name and description"),
    layout: {minWidth: 250.->#px, width: 3.5->#fr, margin: #xxlarge, sticky: true},
    render: ({Row.name: name, color, description, cku, stockKeepingUnit}) => {
      let pastilleColor =
        color->Option.map(color =>
          (color->CatalogProduct.Color.toColorSet(~variation=#pastille)).foregroundColor
        )
      let description =
        stockKeepingUnit->Option.mapWithDefault("", sku => `SKU${t(":")} ${sku}, `) ++ description
      <LegacyProductReferenceTableCell ?cku ?pastilleColor name description />
    },
  },
  {
    name: t("Sold Quantity"),
    layout: {alignX: #center},
    render: ({unitSoldQuantity, bulkSoldQuantity}) =>
      <Inline align=#center>
        <Stack>
          <TextStyle> {unitSoldQuantity->React.int} </TextStyle>
          {switch bulkSoldQuantity {
          | Some(bulkSoldQuantity) =>
            <TextStyle size=#xxsmall variation=#subdued>
              {template(t("For {{value}}"), ~values={"value": bulkSoldQuantity}, ())->React.string}
            </TextStyle>
          | None => React.null
          }}
        </Stack>
      </Inline>,
  },
  {
    name: t("Revenue excl. VAT"),
    layout: {minWidth: 110.->#px, margin: #small},
    render: ({revenueExcludingTaxes, revenueIncludingTaxes}) =>
      <AmountTableCell value=revenueExcludingTaxes secondaryValue=revenueIncludingTaxes />,
  },
  {
    name: t("Margin"),
    layout: {minWidth: 135.->#px, width: 1.5->#fr, alignX: #flexEnd, margin: #large},
    render: ({margin}) => <TextStyle weight=#semibold> {margin->React.string} </TextStyle>,
  },
  {
    name: t("Margin/Markup rt"),
    layout: {minWidth: 100.->#px, width: 1.5->#fr},
    render: ({marginRate, markupRate}) =>
      <Stack space=#xxsmall>
        <TextStyle> {marginRate->React.string} </TextStyle>
        <TextStyle size=#xxsmall variation=#normal> {markupRate->React.string} </TextStyle>
      </Stack>,
  },
  {
    name: t("Cost of sales"),
    layout: {width: 1.5->#fr, alignX: #flexEnd},
    render: ({totalPurchaseCost}) => <TextStyle> {totalPurchaseCost->React.string} </TextStyle>,
  },
  {
    name: t("Purchase price"),
    layout: {minWidth: 110.->#px, alignX: #flexEnd},
    render: ({purchasedPrice}) => <TextStyle> {purchasedPrice->React.string} </TextStyle>,
  },
  {
    name: t("Stock"),
    layout: {minWidth: 75.->#px, alignX: #center},
    render: ({stockQuantity}) => <TextStyle> {stockQuantity->React.string} </TextStyle>,
  },
]

let now = Js.Date.make()
let initialDateRange = (now->DateHelpers.startOfDay, now->DateHelpers.endOfDay)

@react.component
let make = () => {
  let scope = Auth.useScope()
  let organisationAccount = switch scope {
  | Organisation(_) => true
  | Single(_) => false
  }
  let activeShop = switch scope {
  | Single(shop) | Organisation({activeShop: Some(shop)}) => shop
  | Organisation({activeShop: None, shops}) => shops->Array.getUnsafe(0)
  }

  let initialState = Scaffolded.makeInitialState(
    ~filters={
      Filters.shop: activeShop,
      dateRange: initialDateRange,
    },
  )

  let (state, dispatch) = Scaffolded.use(() => initialState)
  let (sortBy, setSortBy) = React.useState(() => SortBy.Quantity)

  let defaultQueryVariables = Query.makeVariables(~sortBy=SortBy.toQueryVariableValue(sortBy), ())

  let filters =
    <Inline space=#small>
      {if organisationAccount {
        <Auth.SelectSingleShopFilter
          value=state.filters.shop
          onChange={shop => FiltersUpdated(prev => {...prev, shop})->dispatch}
        />
      } else {
        React.null
      }}
      {if organisationAccount {
        <Separator />
      } else {
        React.null
      }}
      <Select
        preset=#filter
        label={t("Sorting")}
        sections={
          let items = SortBy.values->Array.map(value => {
            Select.label: value->SortBy.toLabel,
            key: value->SortBy.toRawString,
            value: Some(value),
          })

          [{title: t("Options"), items}]
        }
        value=Some(sortBy)
        onChange={value => setSortBy(_ => value->Option.getExn)}
      />
      <SelectDateRangeFilter
        value=state.filters.dateRange
        placeholder={t("Select a period")}
        disabledResetButton=true
        onChange={dateRange => {
          let dateRange = dateRange->Option.getWithDefault(initialDateRange)
          dispatch(FiltersUpdated(prev => {...prev, dateRange}))
        }}
        triggerLabelDisplay=#showPreset
      />
    </Inline>

  let emptyState =
    <EmptyState
      illustration=Illustration.notFound
      title={t("No result were found.")}
      text={t("Try again with different filters.")}
    />

  <Scaffolded
    title={t("Top sold products")}
    state
    dispatch
    columns=scaffoldedColumns
    filters
    emptyState
    defaultQueryVariables
  />
}

let make = React.memo(make)
