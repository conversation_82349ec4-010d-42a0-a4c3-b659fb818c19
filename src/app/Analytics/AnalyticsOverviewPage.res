open Intl

// TODO: use a fragment here (https://github.com/jeddeloh/rescript-apollo-client/tree/master/EXAMPLES/src/fragmentsUsage)
module AnalyticsPerformanceIndicatorsQueries = %graphql(`
  query AnalyticsOverviewQuery($shopIds: [String!]!, $startDateSelectedPeriod: Datetime!, $endDateSelectedPeriod: Datetime!, $startDateComparisonPeriod: Datetime!, $endDateComparisonPeriod: Datetime! ) @ppxConfig(schema: "graphql_schema_analytics.json") {
   indicatorsCurrent:generalKeyPerformanceIndicators(startDate: $startDateSelectedPeriod, endDate: $endDateSelectedPeriod, shopIds: $shopIds) {
     ticketsRevenue {
        includingTaxes
        excludingTaxes
      }
      invoicesRevenue {
        includingTaxes
        excludingTaxes
      }
      globalRevenue {
        includingTaxes
        excludingTaxes
      }
      salesCount
      salesIndex
      shoppingCartAverage
      totalAmountOfTaxes
      productsSoldCount
      margin
      marginRate
      markupRate
      totalPurchaseCost
    }
   indicatorPrevious:generalKeyPerformanceIndicators(startDate: $startDateComparisonPeriod, endDate: $endDateComparisonPeriod, shopIds: $shopIds) {
     ticketsRevenue {
        includingTaxes
        excludingTaxes
      }
      invoicesRevenue {
        includingTaxes
        excludingTaxes
      }
      globalRevenue {
        includingTaxes
        excludingTaxes
      }
      salesCount
      salesIndex
      shoppingCartAverage
      totalAmountOfTaxes
      productsSoldCount
      margin
      marginRate
      markupRate
      totalPurchaseCost
    }
  }
`)

module AnalyticsShopsInventoryValuationQuery = %graphql(`
  query AnalyticsShopsInventoryValuationQuery($shopIds: [String!]!) @ppxConfig(schema: "graphql_schema_analytics.json") {
    shopsInventoryValuation(shopIds: $shopIds) {
      inventoryValuation
      shop {
        id
        name
      }
    }
  }
`)

let getPercentageChange = (firstValue: float, secondValue: float): float => {
  (secondValue -. firstValue) /. firstValue
}

type comparisonMode =
  | PreviousPeriod
  | PreviousYearPeriod

module Progression = {
  type t =
    Currency(float, float) | Percentage(float, float) | Quantity(int, int) | Index(float, float)

  let getPeriodName = mode => {
    switch mode {
    | PreviousYearPeriod => t("Previous year")
    | PreviousPeriod => t("Previous period")
    }
  }

  let makeTooltipElement = (~amount, ~previousPeriod) => {
    <>
      <Tooltip.Span text=amount bold=true size=#small />
      <Tooltip.Gap space=#xxsmall />
      <Tooltip.Span text=previousPeriod />
    </>
  }

  @react.component
  let make = (~value as progression: t, ~mode as comparisonMode) => {
    switch progression {
    | Currency(previousPeriodValue, selectedPeriodValue) => {
        let formattedAmount = {
          let difference = selectedPeriodValue -. previousPeriodValue
          let formattedDifference =
            difference->Intl.currencyFormat(
              ~currency=#EUR,
              ~minimumFractionDigits=2,
              ~maximumFractionDigits=2,
            )

          difference > 0. ? "+" ++ formattedDifference : formattedDifference
        }
        let formattedPreviousPeriod =
          getPeriodName(comparisonMode) ++
          " : " ++
          previousPeriodValue->Intl.currencyFormat(
            ~currency=#EUR,
            ~minimumFractionDigits=2,
            ~maximumFractionDigits=2,
          )

        makeTooltipElement(~amount=formattedAmount, ~previousPeriod=formattedPreviousPeriod)
      }

    | Percentage(previousPeriodValue, selectedPeriodValue) => {
        let formattedAmount = {
          let difference = selectedPeriodValue -. previousPeriodValue
          let formattedDifference =
            difference->Intl.percentFormat(~minimumFractionDigits=2, ~maximumFractionDigits=2)

          difference > 0. ? "+" ++ formattedDifference : formattedDifference
        }
        let formattedPreviousPeriod =
          getPeriodName(comparisonMode) ++
          " : " ++
          previousPeriodValue->Intl.percentFormat(
            ~minimumFractionDigits=2,
            ~maximumFractionDigits=2,
          )

        makeTooltipElement(~amount=formattedAmount, ~previousPeriod=formattedPreviousPeriod)
      }

    | Quantity(previousPeriodValue, selectedPeriodValue) => {
        let formattedAmount = {
          let difference = selectedPeriodValue - previousPeriodValue

          difference > 0 ? "+" ++ difference->Int.toString : difference->Int.toString
        }

        let formattedPreviousPeriod =
          getPeriodName(comparisonMode) ++ " : " ++ previousPeriodValue->Int.toString

        makeTooltipElement(~amount=formattedAmount, ~previousPeriod=formattedPreviousPeriod)
      }

    | Index(previousPeriodValue, selectedPeriodValue) => {
        let formattedAmount = {
          let difference = selectedPeriodValue -. previousPeriodValue
          let formattedDifference =
            difference->Intl.decimalFormat(~minimumFractionDigits=2, ~maximumFractionDigits=2)

          difference > 0. ? "+" ++ formattedDifference : formattedDifference
        }
        let formattedPreviousPeriod =
          getPeriodName(comparisonMode) ++
          " : " ++
          previousPeriodValue->Intl.decimalFormat(
            ~minimumFractionDigits=2,
            ~maximumFractionDigits=2,
          )

        makeTooltipElement(~amount=formattedAmount, ~previousPeriod=formattedPreviousPeriod)
      }
    }
  }
  let make = React.memo(make)
}

module TicketsRevenueMetric = {
  @react.component
  let make = (~loading, ~value, ~secondaryValue, ~currency, ~progression, ~tooltip) =>
    <Metric title={t("Tickets revenue excl. VAT")} loading progression tooltip>
      <Stack space=#normal>
        <ValueIndicator value=#currency(value, currency) />
        <Stack space=#xsmall>
          <TextStyle variation=#normal size=#xxsmall>
            {t("Tickets revenue incl. VAT")->React.string}
          </TextStyle>
          <ValueIndicator variant=#compact value=#currency(secondaryValue, currency) />
        </Stack>
      </Stack>
    </Metric>
}

module InvoicesRevenueMetric = {
  @react.component
  let make = (~loading, ~value, ~secondaryValue, ~currency, ~progression, ~tooltip) =>
    <Metric title={t("Invoices revenue excl. VAT")} loading progression tooltip>
      <Stack space=#normal>
        <ValueIndicator value=#currency(value, currency) />
        <Stack space=#xsmall>
          <TextStyle variation=#normal size=#xxsmall>
            {t("Invoices revenue incl. VAT")->React.string}
          </TextStyle>
          <ValueIndicator variant=#compact value=#currency(secondaryValue, currency) />
        </Stack>
      </Stack>
    </Metric>
}

module GlobalRevenueMetric = {
  @react.component
  let make = (~loading, ~value, ~secondaryValue, ~currency, ~progression, ~tooltip) =>
    <Metric title={t("Global revenue excl. VAT")} loading progression tooltip>
      <Stack space=#normal>
        <ValueIndicator value=#currency(value, currency) />
        <Stack space=#xsmall>
          <TextStyle variation=#normal size=#xxsmall>
            {t("Global revenue incl. VAT")->React.string}
          </TextStyle>
          <ValueIndicator variant=#compact value=#currency(secondaryValue, currency) />
        </Stack>
      </Stack>
    </Metric>
}

module MarginMetric = {
  @react.component
  let make = (~loading, ~value, ~currency, ~progression, ~tooltip) =>
    <Metric title={t("Margin")} loading progression tooltip>
      <ValueIndicator value=#currency(value, currency) />
    </Metric>
}

module MarginRateMetric = {
  @react.component
  let make = (~loading, ~value, ~progression, ~tooltip) =>
    <Metric title={t("Margin rate")} loading progression tooltip>
      <ValueIndicator value=#percent(value /. 100.) />
    </Metric>
}

module MarkupRateMetric = {
  @react.component
  let make = (~loading, ~value, ~progression, ~tooltip) =>
    <Metric title={t("Markup rate")} loading progression tooltip>
      <ValueIndicator value=#percent(value /. 100.) />
    </Metric>
}

module TotalAmountOfTaxesMetric = {
  @react.component
  let make = (~loading, ~value, ~currency) =>
    <Metric title={t("Total amount of VAT")} loading>
      <ValueIndicator value=#currency(value, currency) />
    </Metric>
}

module TotalPurchaseCostMetric = {
  @react.component
  let make = (~loading, ~value, ~currency) =>
    <Metric title={t("Cost of sales")} loading>
      <ValueIndicator value=#currency(value, currency) />
    </Metric>
}

module AverageCartMetric = {
  @react.component
  let make = (~loading, ~value, ~currency, ~progression, ~tooltip) =>
    <Metric title={t("Average cart incl. VAT")} loading progression tooltip>
      <ValueIndicator value=#currency(value, currency) />
    </Metric>
}

module SalesCountMetric = {
  @react.component
  let make = (~loading, ~value, ~progression, ~tooltip) =>
    <Metric title={t("Number of sales")} loading progression tooltip>
      <ValueIndicator value=#integer(value) />
    </Metric>
}

module SalesIndexMetric = {
  @react.component
  let make = (~loading, ~value, ~progression, ~tooltip) =>
    <Metric title={t("Sales index")} loading progression tooltip>
      <ValueIndicator value=#decimal(value) />
    </Metric>
}

module SoldProductsMetric = {
  @react.component
  let make = (~loading, ~value, ~progression, ~tooltip) =>
    <Metric title={t("Sold products")} loading progression tooltip>
      <ValueIndicator value=#integer(value) />
    </Metric>
}

module InventoryValuationMetric = {
  @react.component
  let make = (~loading, ~value, ~currency, ~disabled=false) => {
    let tooltip = disabled
      ? Some(t("The stock value is computed only on today's date")->React.string)
      : None
    <Metric title={t("Inventory Valuation")} loading tooltip=?{tooltip}>
      {disabled
        ? <TextStyle variation=#normal>
            {t("No data to display for the selected period")->React.string}
          </TextStyle>
        : <ValueIndicator value=#currency(value, currency) />}
    </Metric>
  }
}

let {getPreviousYearPeriod, getPreviousPeriod} = module(DateHelpers)

type period = (Js.Date.t, Js.Date.t)

module Reducer = {
  type state = {
    selectedPeriod: period,
    comparisonPeriod: period,
    comparisonMode: comparisonMode,
    previousYearPeriod: period,
    previousPeriod: period,
  }

  type action =
    | SelectedPeriodChanged(period)
    | ComparisonModeChanged(comparisonMode)

  let make = (state, action) => {
    switch action {
    | SelectedPeriodChanged(dateRange) => {
        let (startDate, endDate) = dateRange
        let previousPeriod = getPreviousPeriod(startDate, endDate)
        let previousYearPeriod = getPreviousYearPeriod(startDate, endDate)
        let comparisonPeriod = switch state.comparisonMode {
        | PreviousPeriod => previousPeriod
        | PreviousYearPeriod => previousYearPeriod
        }

        {
          ...state,
          selectedPeriod: dateRange,
          comparisonPeriod,
          previousYearPeriod,
          previousPeriod,
        }
      }

    | ComparisonModeChanged(comparisonMode) => {
        let comparisonPeriod = switch comparisonMode {
        | PreviousPeriod => state.previousPeriod
        | PreviousYearPeriod => state.previousYearPeriod
        }
        {
          ...state,
          comparisonPeriod,
          comparisonMode,
        }
      }
    }
  }
}

let now = Js.Date.make()
let (initialStartDate, initialEndDate) = (now->DateHelpers.startOfDay, now->DateHelpers.endOfDay)

@react.component
let make = () => {
  let scope = Auth.useScope()
  let organisationAccount = switch scope {
  | Organisation(_) => true
  | Single(_) => false
  }
  let shopIds = switch scope {
  | Single(shop) | Organisation({activeShop: Some(shop)}) => [shop]
  | Organisation({shops}) => shops
  }->Array.map(shop => shop.id)

  let (state, dispatch) = React.useReducer(
    Reducer.make,
    {
      Reducer.selectedPeriod: (initialStartDate, initialEndDate),
      Reducer.comparisonPeriod: getPreviousPeriod(initialStartDate, initialEndDate),
      Reducer.comparisonMode: PreviousPeriod,
      Reducer.previousPeriod: getPreviousPeriod(initialStartDate, initialEndDate),
      Reducer.previousYearPeriod: getPreviousYearPeriod(initialStartDate, initialEndDate),
    },
  )
  let (
    startDateComparisonPeriodPreviousPeriod,
    endDateComparisonPeriodPreviousPeriod,
  ) = state.previousPeriod

  let (
    startDateComparisonPeriodPreviousYear,
    endDateComparisonPeriodPreviousYear,
  ) = state.previousYearPeriod

  let (startDateSelectedPeriod, endDateSelectedPeriod) = state.selectedPeriod
  let (startDateComparisonPeriod, endDateComparisonPeriod) = state.comparisonPeriod
  let isDateRangeDifferentThanToday = !(
    DateHelpers.isSameDay(initialStartDate, startDateSelectedPeriod) &&
    DateHelpers.isSameDay(initialEndDate, endDateSelectedPeriod)
  )

  let analyticsPerformanceIndicatorsQueriesResult =
    AnalyticsPerformanceIndicatorsQueries.use(
      ~fetchPolicy=CacheAndNetwork,
      AnalyticsPerformanceIndicatorsQueries.makeVariables(
        ~shopIds,
        ~startDateSelectedPeriod=startDateSelectedPeriod->Scalar.Datetime.serialize,
        ~endDateSelectedPeriod=endDateSelectedPeriod->Scalar.Datetime.serialize,
        ~startDateComparisonPeriod=startDateComparisonPeriod->Scalar.Datetime.serialize,
        ~endDateComparisonPeriod=endDateComparisonPeriod->Scalar.Datetime.serialize,
        (),
      ),
    )
    ->ApolloHelpers.queryResultToAsyncResult
    ->AsyncResult.mapOk(data => data)
    ->AsyncResult.mapError(_ => ())

  let analyticsShopsInventoryValuationQueryResult =
    AnalyticsShopsInventoryValuationQuery.use(
      ~fetchPolicy=CacheAndNetwork,
      AnalyticsShopsInventoryValuationQuery.makeVariables(~shopIds, ()),
    )
    ->ApolloHelpers.queryResultToAsyncResult
    ->AsyncResult.mapOk(({shopsInventoryValuation}) => shopsInventoryValuation)
    ->AsyncResult.mapError(_ => ())

  let renderHeaderActions = () =>
    <Inline space=#small>
      {if organisationAccount {
        <Auth.SelectShopFilter />
      } else {
        React.null
      }}
      {if organisationAccount {
        <Separator />
      } else {
        React.null
      }}
      <SelectDateRangeFilter
        placeholder={t("Select a period")}
        value=state.selectedPeriod
        disabledResetButton=true
        onChange={selectedDateRange =>
          selectedDateRange->Option.forEach(dateRange =>
            SelectedPeriodChanged(dateRange)->dispatch
          )}
        triggerLabelDisplay=#showPreset
      />
      <Select
        grow=false
        preset=#filter
        label={t("Compare period to")}
        value={state.comparisonMode}
        sections={[
          {
            items: [
              {
                key: "select_previous_period",
                label: t("Previous period"),
                value: PreviousPeriod,
                description: "\u00A0" ++
                startDateComparisonPeriodPreviousPeriod->dateTimeFormat(~dateStyle=#medium) ++
                " - " ++
                endDateComparisonPeriodPreviousPeriod->dateTimeFormat(~dateStyle=#medium),
              },
              {
                key: "select_previous_year",
                label: t("Previous year"),
                value: PreviousYearPeriod,
                description: "\u00A0" ++
                startDateComparisonPeriodPreviousYear->dateTimeFormat(~dateStyle=#medium) ++
                " - " ++
                endDateComparisonPeriodPreviousYear->dateTimeFormat(~dateStyle=#medium),
              },
            ],
          },
        ]}
        onChange={value => dispatch(ComparisonModeChanged(value))}
      />
    </Inline>

  <Page title={t("General performances")} renderHeaderActions variation=#compact>
    <Box spaceTop=#xlarge>
      {switch analyticsPerformanceIndicatorsQueriesResult {
      | NotAsked | Loading | Reloading(Error()) => <Placeholder status=Loading />
      | Done(Error()) => <Placeholder status=Error />
      | Reloading(Ok(data)) | Done(Ok(data)) =>
        let currency = #EUR
        let loading = analyticsPerformanceIndicatorsQueriesResult->AsyncResult.isReloading

        <Group grid=["33%", "33%", "33%"] spaceX=#normal spaceY=#large>
          <TicketsRevenueMetric
            loading
            value={data.indicatorsCurrent.ticketsRevenue.excludingTaxes}
            secondaryValue={data.indicatorsCurrent.ticketsRevenue.includingTaxes}
            currency
            progression={getPercentageChange(
              data.indicatorPrevious.ticketsRevenue.excludingTaxes,
              data.indicatorsCurrent.ticketsRevenue.excludingTaxes,
            )}
            tooltip={<Progression
              value=Currency(
                data.indicatorPrevious.ticketsRevenue.excludingTaxes,
                data.indicatorsCurrent.ticketsRevenue.excludingTaxes,
              )
              mode=state.comparisonMode
            />}
          />
          <InvoicesRevenueMetric
            loading
            value={data.indicatorsCurrent.invoicesRevenue.excludingTaxes}
            secondaryValue={data.indicatorsCurrent.invoicesRevenue.includingTaxes}
            currency
            progression={getPercentageChange(
              data.indicatorPrevious.invoicesRevenue.excludingTaxes,
              data.indicatorsCurrent.invoicesRevenue.excludingTaxes,
            )}
            tooltip={<Progression
              value=Currency(
                data.indicatorPrevious.invoicesRevenue.excludingTaxes,
                data.indicatorsCurrent.invoicesRevenue.excludingTaxes,
              )
              mode=state.comparisonMode
            />}
          />
          <GlobalRevenueMetric
            loading
            value={data.indicatorsCurrent.globalRevenue.excludingTaxes}
            secondaryValue={data.indicatorsCurrent.globalRevenue.includingTaxes}
            currency
            progression={getPercentageChange(
              data.indicatorPrevious.globalRevenue.excludingTaxes,
              data.indicatorsCurrent.globalRevenue.excludingTaxes,
            )}
            tooltip={<Progression
              value=Currency(
                data.indicatorPrevious.globalRevenue.excludingTaxes,
                data.indicatorsCurrent.globalRevenue.excludingTaxes,
              )
              mode=state.comparisonMode
            />}
          />
          <MarginMetric
            loading
            value={data.indicatorsCurrent.margin}
            currency
            progression={getPercentageChange(
              data.indicatorPrevious.margin,
              data.indicatorsCurrent.margin,
            )}
            tooltip={<Progression
              value=Currency(data.indicatorPrevious.margin, data.indicatorsCurrent.margin)
              mode=state.comparisonMode
            />}
          />
          <MarginRateMetric
            loading
            value={data.indicatorsCurrent.marginRate}
            progression={getPercentageChange(
              data.indicatorPrevious.marginRate,
              data.indicatorsCurrent.marginRate,
            )}
            tooltip={<Progression
              value={Percentage(
                data.indicatorPrevious.marginRate /. 100.,
                data.indicatorsCurrent.marginRate /. 100.,
              )}
              mode=state.comparisonMode
            />}
          />
          <MarkupRateMetric
            loading
            value={data.indicatorsCurrent.markupRate}
            progression={getPercentageChange(
              data.indicatorPrevious.markupRate,
              data.indicatorsCurrent.markupRate,
            )}
            tooltip={<Progression
              value={Percentage(
                data.indicatorPrevious.markupRate /. 100.,
                data.indicatorsCurrent.markupRate /. 100.,
              )}
              mode=state.comparisonMode
            />}
          />
          <TotalPurchaseCostMetric
            loading value={data.indicatorsCurrent.totalPurchaseCost} currency
          />
          <TotalAmountOfTaxesMetric
            loading value={data.indicatorsCurrent.totalAmountOfTaxes} currency
          />
          <AverageCartMetric
            loading
            value={data.indicatorsCurrent.shoppingCartAverage}
            currency
            progression={getPercentageChange(
              data.indicatorPrevious.shoppingCartAverage,
              data.indicatorsCurrent.shoppingCartAverage,
            )}
            tooltip={<Progression
              value=Currency(
                data.indicatorPrevious.shoppingCartAverage,
                data.indicatorsCurrent.shoppingCartAverage,
              )
              mode=state.comparisonMode
            />}
          />
          <SalesCountMetric
            loading
            value={data.indicatorsCurrent.salesCount}
            progression={getPercentageChange(
              data.indicatorPrevious.salesCount->Int.toFloat,
              data.indicatorsCurrent.salesCount->Int.toFloat,
            )}
            tooltip={<Progression
              value=Quantity(data.indicatorPrevious.salesCount, data.indicatorsCurrent.salesCount)
              mode=state.comparisonMode
            />}
          />
          <SalesIndexMetric
            loading
            value={data.indicatorsCurrent.salesIndex}
            progression={getPercentageChange(
              data.indicatorPrevious.salesIndex,
              data.indicatorsCurrent.salesIndex,
            )}
            tooltip={<Progression
              value=Index(data.indicatorPrevious.salesIndex, data.indicatorsCurrent.salesIndex)
              mode=state.comparisonMode
            />}
          />
          <SoldProductsMetric
            loading
            value={data.indicatorsCurrent.productsSoldCount}
            progression={getPercentageChange(
              data.indicatorPrevious.productsSoldCount->Int.toFloat,
              data.indicatorsCurrent.productsSoldCount->Int.toFloat,
            )}
            tooltip={<Progression
              value=Quantity(
                data.indicatorPrevious.productsSoldCount,
                data.indicatorsCurrent.productsSoldCount,
              )
              mode=state.comparisonMode
            />}
          />
          {switch analyticsShopsInventoryValuationQueryResult {
          | NotAsked | Loading | Reloading(Error()) =>
            <InventoryValuationMetric loading={true} value={0.} currency />
          | Done(Error()) => React.null
          | Reloading(Ok(shopsInventoryValuation)) | Done(Ok(shopsInventoryValuation)) =>
            <InventoryValuationMetric
              loading={analyticsShopsInventoryValuationQueryResult->AsyncResult.isReloading}
              value={shopsInventoryValuation->Array.reduce(0., (acc, next) => {
                acc +. next.inventoryValuation
              })}
              disabled={isDateRangeDifferentThanToday}
              currency
            />
          }}
          <div />
          <div />
        </Group>
      }}
    </Box>
  </Page>
}

let make = React.memo(make)
