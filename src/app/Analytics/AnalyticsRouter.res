let baseRoute = "/analytics"

let overviewRoute = baseRoute ++ "/overview"
let shopsRoute = baseRoute ++ "/shops"
let topPerformingProductsRoute = baseRoute ++ "/top-performing-products"
let cashFlowRoute = baseRoute ++ "/cash-flow"

@react.component
let make = (~subUrlPath) => {
  let userRole = Auth.useRole()

  if Auth.isAuthorizedAccess(~role=userRole, ~targetPathname=baseRoute) {
    switch subUrlPath {
    | list{"overview"} => <AnalyticsOverviewPage />
    | list{"shops"} => <AnalyticsShopsPage />
    | list{"top-performing-products"} => <AnalyticsTopPerformingProductsPage />
    | list{"cash-flow"} => <AnalyticsCashFlowPage />
    | _ => <Navigation.Redirect route=overviewRoute />
    }
  } else {
    <AccessDeniedPage />
  }
}
