open Intl

// TODO - this query needs to support `search` argument
module AnalyticsShopsQuery = %graphql(`
  query AnalyticsShopsPageQuery($filterBy: ShopKeyPerformanceIndicatorsQueryFilter) @ppxConfig(schema: "graphql_schema_analytics.json") {
    shopsKeyPerformanceIndicators(first: 50, filterBy: $filterBy) {
      pageInfo {
        startCursor
        endCursor
      }
      edges {
        node {
          shop {
            id
            name
          }
          ticketsRevenue {
            includingTaxes
            excludingTaxes
          }
          invoicesRevenue {
            includingTaxes
            excludingTaxes
          }
          globalRevenue {
            includingTaxes
            excludingTaxes
          }
          salesCount
          salesIndex
          shoppingCartAverage
          totalAmountOfTaxes
          productsSoldCount
          totalPurchaseCost
          margin
          marginRate
          markupRate
        }
      }
      totalCount
    }
  }
`)

module AnalyticsShopsFilters = {
  type t = {
    shop: option<Auth.shop>,
    dateRange: option<(Js.Date.t, Js.Date.t)>,
  }

  let jsonCodec = (~shops) =>
    JsonCodec.object2(
      ({shop, dateRange}) => (
        shop->Option.map(shop => shop.id),
        dateRange->Option.map(((start, end)) => [start->Js.Date.valueOf, end->Js.Date.valueOf]),
      ),
      ((shopId, dateRange)) => Ok({
        shop: shops->Array.getBy((shop: Auth.shop) => Some(shop.id) === shopId),
        dateRange: dateRange->Option.flatMap(range =>
          switch range {
          | [start, end] => Some((start->Js.Date.fromFloat, end->Js.Date.fromFloat))
          | _ => None
          }
        ),
      }),
      JsonCodec.field("shopId", JsonCodec.string)->JsonCodec.optional,
      JsonCodec.field("dateRange", JsonCodec.array(JsonCodec.float))->JsonCodec.optional,
    )
}

module AnalyticsShopsTableRow = {
  type t = {
    shopId: string,
    shopName: string,
    ticketsRevenueIncludingTaxes: float,
    ticketsRevenueExcludingTaxes: float,
    invoicesRevenueIncludingTaxes: float,
    invoicesRevenueExcludingTaxes: float,
    globalRevenueIncludingTaxes: float,
    globalRevenueExcludingTaxes: float,
    salesCount: int,
    margin: float,
    marginRate: float,
    markupRate: float,
    salesIndex: float,
    shoppingCartAverage: float,
    productsSoldCount: int,
    totalAmountOfTaxes: float,
    totalPurchaseCost: float,
    currency: Intl.currency,
  }

  let rowsPerPage = 50
  let totalPages = rows =>
    (rows->Array.size->Float.fromInt /. rowsPerPage->Float.fromInt)->Js.Math.ceil_float->Float.toInt

  let paginate = (rows, currentPage) =>
    rows->Array.slice(~len=rowsPerPage, ~offset=(currentPage - 1) * rowsPerPage)
  let getPage = (~paginateAction, ~prevPage, ~totalPages) =>
    switch (paginateAction, prevPage, totalPages) {
    | (LegacyPagination.First, _, _) | (Prev, 1, _) => 1
    | (Prev, _, _) => prevPage - 1
    | (Next, prevPage, totalPages) if prevPage >= totalPages => prevPage
    | (Next, _, _) => prevPage + 1
    | (Last, _, _) => totalPages
    }

  let keyExtractor = ({shopId}) => shopId
}

let analyticsShopsTableRowsFromQueryResult = queryResult =>
  queryResult.AnalyticsShopsQuery.shopsKeyPerformanceIndicators.edges->Array.map(({node}) => {
    {
      AnalyticsShopsTableRow.shopId: node.shop.id,
      shopName: node.shop.name,
      ticketsRevenueIncludingTaxes: node.ticketsRevenue.includingTaxes,
      ticketsRevenueExcludingTaxes: node.ticketsRevenue.excludingTaxes,
      invoicesRevenueIncludingTaxes: node.invoicesRevenue.includingTaxes,
      invoicesRevenueExcludingTaxes: node.invoicesRevenue.excludingTaxes,
      globalRevenueIncludingTaxes: node.globalRevenue.includingTaxes,
      globalRevenueExcludingTaxes: node.globalRevenue.excludingTaxes,
      margin: node.margin,
      marginRate: node.marginRate,
      markupRate: node.markupRate,
      salesCount: node.salesCount,
      salesIndex: node.salesIndex,
      shoppingCartAverage: node.shoppingCartAverage,
      productsSoldCount: node.productsSoldCount,
      totalAmountOfTaxes: node.totalAmountOfTaxes,
      totalPurchaseCost: node.totalPurchaseCost,
      // TODO - Currency should be provided by the GraphQL API
      currency: #EUR,
    }
  })

let analyticsShopsVariablesFilterBy = ({AnalyticsShopsFilters.shop: shop, dateRange}) =>
  AnalyticsShopsQuery.makeInputObjectShopKeyPerformanceIndicatorsQueryFilter(
    ~shopIds=?{
      switch shop {
      | Some({id: shopId}) => Some(AnalyticsShopsQuery.makeInputObjectInFilter(~_in=[shopId], ()))
      | None => None
      }
    },
    ~startDate=?dateRange->Option.map(((startDate, _)) => startDate->Scalar.Datetime.serialize),
    ~endDate=?dateRange->Option.map(((_, endDate)) => endDate->Scalar.Datetime.serialize),
    (),
  )

let columns = [
  {
    Scaffold.name: t("Shop"),
    layout: {minWidth: 160.->#px, width: 20.->#pct, margin: #small, sticky: true},
    render: ({AnalyticsShopsTableRow.shopName: shopName}) =>
      <TextStyle> {shopName->React.string} </TextStyle>,
  },
  {
    name: t("Revenue excl. VAT"),
    layout: {minWidth: 110.->#px, width: 2.->#fr, sticky: true},
    render: ({globalRevenueExcludingTaxes, globalRevenueIncludingTaxes}) =>
      <AmountTableCell
        value=globalRevenueExcludingTaxes secondaryValue=globalRevenueIncludingTaxes
      />,
  },
  {
    name: t("Margin"),
    layout: {minWidth: 110.->#px, width: 1.5->#fr},
    render: ({margin, currency}) =>
      <TextStyle weight=#semibold> {margin->currencyFormat(~currency)->React.string} </TextStyle>,
  },
  {
    name: t("Margin/Markup rt"),
    layout: {minWidth: 100.->#px, width: 2.5->#fr},
    render: ({marginRate, markupRate}) => {
      let formattedMarginRate =
        (marginRate /. 100.)->percentFormat(~minimumFractionDigits=2, ~maximumFractionDigits=2)
      let formattedMarkupRate =
        (markupRate /. 100.)->percentFormat(~minimumFractionDigits=2, ~maximumFractionDigits=2)

      <Stack space=#xxsmall>
        <TextStyle> {formattedMarginRate->React.string} </TextStyle>
        <TextStyle size=#xxsmall variation=#normal> {formattedMarkupRate->React.string} </TextStyle>
      </Stack>
    },
  },
  {
    name: t("Cost of sales"),
    layout: {minWidth: 110.->#px, width: 2.->#fr, alignX: #flexEnd, margin: #small},
    render: ({totalPurchaseCost, currency}) =>
      <TextStyle> {totalPurchaseCost->currencyFormat(~currency)->React.string} </TextStyle>,
  },
  {
    name: t("Avg. basket"),
    layout: {alignX: #flexEnd, margin: #small},
    render: ({shoppingCartAverage, currency}) =>
      <TextStyle> {shoppingCartAverage->currencyFormat(~currency)->React.string} </TextStyle>,
  },
  {
    name: t("Sales"),
    layout: {minWidth: 70.->#px, width: 120.->#px, alignX: #center},
    render: ({salesCount}) => <TextStyle> {salesCount->React.int} </TextStyle>,
  },
  {
    name: t("Sales index"),
    layout: {minWidth: 70.->#px, width: 130.->#px, alignX: #center},
    render: ({salesIndex}) => <TextStyle> {salesIndex->decimalFormat->React.string} </TextStyle>,
  },
  {
    name: t("Products sold"),
    layout: {minWidth: 70.->#px, width: 130.->#px, alignX: #center},
    render: ({productsSoldCount}) => <TextStyle> {productsSoldCount->React.int} </TextStyle>,
  },
]

let analyticsShopsQueryVariables = ({LegacyResourceList.filters: filters}) => {
  AnalyticsShopsQuery.filterBy: Some(analyticsShopsVariablesFilterBy(filters)),
}

@react.component
let make = () => {
  let shops = Auth.useShops()
  let activeShop = Auth.useActiveShop()
  let (currentPage, setCurrentPage) = React.useState(() => 1)

  let initialFilters = {
    let now = Js.Date.make()
    {
      AnalyticsShopsFilters.shop: activeShop,
      dateRange: Some((now->DateHelpers.startOfDay, now->DateHelpers.endOfDay)),
    }
  }
  let initialState = LegacyResourceList.initialState(~filters=initialFilters)
  let filtersJsonCodec = AnalyticsShopsFilters.jsonCodec(~shops)
  let resourceListPropState = LegacyResourceList.use(~initialState, ~filtersJsonCodec)
  let (state, dispatch) = resourceListPropState

  let queryAsyncResult =
    AnalyticsShopsQuery.use(
      analyticsShopsQueryVariables(state),
    )->ApolloHelpers.queryResultToAsyncResult

  let tableColumns = [
    {
      Table.key: "shop",
      name: t("Shop"),
      layout: {minWidth: 160.->#px, width: 20.->#pct, margin: #small, sticky: true},
      render: ({data: {AnalyticsShopsTableRow.shopName: shopName}}) =>
        <TextStyle> {shopName->React.string} </TextStyle>,
    },
    {
      key: "revenues",
      name: t("Revenue excl. VAT"),
      layout: {minWidth: 110.->#px, width: 2.->#fr, sticky: true},
      render: ({data: {globalRevenueExcludingTaxes, globalRevenueIncludingTaxes}}) =>
        <AmountTableCell
          value=globalRevenueExcludingTaxes secondaryValue=globalRevenueIncludingTaxes
        />,
    },
    {
      key: "margin",
      name: t("Margin"),
      layout: {minWidth: 110.->#px, width: 1.5->#fr},
      render: ({data: {margin, currency}}) =>
        <TextStyle weight=#semibold> {margin->currencyFormat(~currency)->React.string} </TextStyle>,
    },
    {
      key: "margin-markup-rates",
      name: t("Margin/Markup rt"),
      layout: {minWidth: 100.->#px, width: 2.5->#fr},
      render: ({data: {marginRate, markupRate}}) => {
        let formattedMarginRate =
          (marginRate /. 100.)->percentFormat(~minimumFractionDigits=2, ~maximumFractionDigits=2)
        let formattedMarkupRate =
          (markupRate /. 100.)->percentFormat(~minimumFractionDigits=2, ~maximumFractionDigits=2)

        <Stack space=#xxsmall>
          <TextStyle> {formattedMarginRate->React.string} </TextStyle>
          <TextStyle size=#tiny variation=#normal> {formattedMarkupRate->React.string} </TextStyle>
        </Stack>
      },
    },
    {
      key: "cost-of-sales",
      name: t("Cost of sales"),
      layout: {minWidth: 110.->#px, width: 2.->#fr, alignX: #flexEnd, margin: #small},
      render: ({data: {totalPurchaseCost, currency}}) =>
        <TextStyle> {totalPurchaseCost->currencyFormat(~currency)->React.string} </TextStyle>,
    },
    {
      key: "avg-basket",
      name: t("Avg. basket"),
      layout: {alignX: #flexEnd, margin: #small},
      render: ({data: {shoppingCartAverage, currency}}) =>
        <TextStyle> {shoppingCartAverage->currencyFormat(~currency)->React.string} </TextStyle>,
    },
    {
      key: "sales",
      name: t("Sales"),
      layout: {minWidth: 70.->#px, width: 120.->#px, alignX: #center},
      render: ({data: {salesCount}}) => <TextStyle> {salesCount->React.int} </TextStyle>,
    },
    {
      key: "sales-index",
      name: t("Sales index"),
      layout: {minWidth: 70.->#px, width: 130.->#px, alignX: #center},
      render: ({data: {salesIndex}}) =>
        <TextStyle> {salesIndex->decimalFormat->React.string} </TextStyle>,
    },
    {
      key: "products-sold",
      name: t("Products sold"),
      layout: {minWidth: 70.->#px, width: 130.->#px, alignX: #center},
      render: ({data: {productsSoldCount}}) =>
        <TextStyle> {productsSoldCount->React.int} </TextStyle>,
    },
  ]

  let filters =
    <Inline space=#small>
      <Auth.SelectShopFilter
        value=?state.filters.shop
        onChange={shop => FiltersUpdated(prev => {...prev, shop})->dispatch}
      />
      <Separator />
      <SelectDateRangeFilter
        value=?state.filters.dateRange
        placeholder={t("Select a period")}
        disabledResetButton=true
        onChange={dateRange => FiltersUpdated(prev => {...prev, dateRange})->dispatch}
        triggerLabelDisplay=#showPreset
      />
    </Inline>

  let tableRows =
    queryAsyncResult
    ->AsyncResult.mapOk(analyticsShopsTableRowsFromQueryResult)
    ->AsyncResult.mapError(_ => ())
  let paginatedTableRows =
    tableRows->AsyncResult.mapOk(rows => rows->AnalyticsShopsTableRow.paginate(currentPage))

  let totalPages = switch tableRows {
  | Reloading(Ok(rows)) | Done(Ok(rows)) => rows->AnalyticsShopsTableRow.totalPages
  | _ => 1
  }

  let onRequestPaginate = paginateAction =>
    setCurrentPage(prevPage =>
      AnalyticsShopsTableRow.getPage(~paginateAction, ~prevPage, ~totalPages)
    )

  <Page title={t("Performances per shop")} variation=#compact>
    <BarControl filters />
    <TableView
      columns=tableColumns
      data=paginatedTableRows
      keyExtractor=AnalyticsShopsTableRow.keyExtractor
      placeholderEmptyState=EmptyState.error
      hideReloadingPlaceholder=true
    />
    <LegacyPagination currentPage totalPages onRequestPaginate />
  </Page>
}

let make = React.memo(make)
