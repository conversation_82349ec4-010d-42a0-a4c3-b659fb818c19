module PromotionByCkuQuery = %graphql(`
  query promotionCampaignsByCku($cku: CKU!) {
    promotionCampaignsByCku(cku: $cku, first: 50) {
      edges {
        node {
          id
          name
          startDate
          endDate
          formattedStatus @ppxOmitFutureValue
          creatorIdentifier
          shop {
            id
            name
          }
          price {
            id
            name
          }
        }
      }
    }
  }
`)

module PromotionDiscountsQuery = %graphql(`
  query promotionCampaign($id: ID!, $after: String, $variantPricesFilterBy: InputVariantVariantPricesFilter) {
    promotionCampaign(id: $id) {
      discounts(first: 50, after: $after) {
        pageInfo {
          endCursor
          hasNextPage
        }
        edges {
          node {
            id
            value
            kind @ppxOmitFutureValue
            variant {
              id
              cku
              formattedName
              formattedDescription
              stockKeepingUnit
              purchasedPrice
              capacityUnit
              bulk
              variantPrices(filterBy: $variantPricesFilterBy) {
                edges {
                  node {
                    valueIncludingTax
                  }
                }
              }
            }
          }
        }
      }
    }
  }
`)

type discountQueryResult = Js.Promise.t<
  Result.t<
    ApolloClient__React_Hooks_UseApolloClient.ApolloClient.ApolloQueryResult.t__ok<
      PromotionDiscountsQuery.t,
    >,
    ApolloClient__React_Hooks_UseApolloClient.ApolloClient.ApolloError.t,
  >,
>
type discountsData = PromotionDiscountsQuery.PromotionDiscountsQuery_inner.t_promotionCampaign_discounts_edges

type fetchingStatus =
  | Loading
  | Success(array<discountsData>)
  | Error

// TODO - usePaginate https://github.com/winoteam/pos/pull/416#discussion_r801739276
let rec runScanDiscounts = (
  ~discountsFetch: (~after: option<string>) => discountQueryResult,
  ~cursor: option<string>=?,
  ~data: array<discountsData>=[],
  ~onDone: fetchingStatus => unit,
  (),
) =>
  discountsFetch(~after=cursor)
  ->FuturePromise.fromPromise
  ->Future.mapOk(response =>
    switch response {
    | Ok({
        data: {
          promotionCampaign: Some({
            discounts: {pageInfo: {endCursor, hasNextPage: Some(true)}, edges: discounts},
          }),
        },
        error: None,
      }) =>
      runScanDiscounts(
        ~discountsFetch,
        ~data=data->Array.concat(discounts),
        ~cursor=?endCursor,
        ~onDone,
        (),
      )
    | Ok({data: {promotionCampaign: Some({discounts: {edges: discounts}})}, error: None}) =>
      onDone(Success(data->Array.concat(discounts)))
    | _ => onDone(Error)
    }
  )
  ->ignore
