open Intl

module Query = %graphql(`
  query PromotionListPageQuery($first: Int, $last: Int, $before: String, $after: String, $search: String, $filterBy: InputPromotionCampaignsQueryFilter) {
    promotionCampaignsDistinctOnCku(filterBy: $filterBy, search: $search, first: $first, last: $last, before: $before, after: $after) {
      pageInfo {
        startCursor
        endCursor
      }
      edges {
        node {
          id
          cku
          name
          startDate
          endDate
          discounts {
            totalCount
          }
          formattedShopsNames
          price {
            name
          }
          formattedStatus @ppxOmitFutureValue
        }
      }
      totalCount
    }
  }
`)

module Filters = {
  type t = {shop: option<Auth.shop>}

  let encoder = ({shop}) => shop->Option.map(shop => shop.id)

  let decoder = (~shops, shopId) => Ok({
    shop: shops->Array.getBy((shop: Auth.shop) => Some(shop.id) === shopId),
  })

  let useJsonCodec = () => {
    let shops = Auth.useShops()

    JsonCodec.object1(
      encoder,
      decoder(~shops),
      JsonCodec.field("shopId", JsonCodec.string)->JsonCodec.optional,
    )
  }
}

module Row = {
  type t = {
    cku: string,
    id: string,
    name: string,
    startDate: Js.Date.t,
    endDate: Js.Date.t,
    discountsCount: int,
    priceName: string,
    formattedShopsNames: string,
    formattedStatus: PromotionStatus.t,
  }
}

module Scaffolded = Scaffold.Make({
  type filters = Filters.t
  let useFiltersJsonCodec = Filters.useJsonCodec

  module QueryInner = Query.Query_inner
  type queryVariableFilterBy = Query.t_variables_InputPromotionCampaignsQueryFilter
  let useQuery = Query.use

  let makeQueryVariables = (
    _defaultQueryVariables,
    ~connectionArguments,
    ~search=?,
    ~filterBy=?,
    (),
  ) => {
    QueryInner.first: connectionArguments.Scaffold.first,
    last: connectionArguments.last,
    before: connectionArguments.before,
    after: connectionArguments.after,
    search,
    filterBy,
  }

  let makeQueryVariablesFilterBy = ({Filters.shop: shop}) => {
    Query.shopIds: switch shop {
    | Some({id: shopId}) => Some(Query.makeInputObjectInFilter(~_in=[shopId], ()))
    | None => None
    },
    archived: Some(#EXCLUDED),
  }

  let totalCountFromQueryData = ({
    Query.promotionCampaignsDistinctOnCku: promotionCampaignsDistinctOnCku,
  }) => promotionCampaignsDistinctOnCku.totalCount
  let cursorsFromQueryData = ({
    Query.promotionCampaignsDistinctOnCku: promotionCampaignsDistinctOnCku,
  }) => (
    promotionCampaignsDistinctOnCku.pageInfo.startCursor,
    promotionCampaignsDistinctOnCku.pageInfo.endCursor,
  )

  type row = Row.t
  let rowsFromQueryDataAndState = (
    {Query.promotionCampaignsDistinctOnCku: promotionCampaignsDistinctOnCku},
    _,
  ) =>
    promotionCampaignsDistinctOnCku.edges->Array.map(edge => {
      {
        Row.cku: edge.node.cku,
        id: edge.node.id,
        name: edge.node.name,
        startDate: edge.node.startDate,
        endDate: edge.node.endDate,
        discountsCount: edge.node.discounts.totalCount,
        priceName: edge.node.price.name,
        formattedShopsNames: edge.node.formattedShopsNames,
        formattedStatus: edge.node.formattedStatus,
      }
    })

  let keyExtractor = ({Row.id: id}) => id
})

let scaffoldedColumns = [
  {
    Scaffold.name: t("Name"),
    layout: {minWidth: 200.->#px, width: 20.->#pct, sticky: true},
    render: ({Row.name: name, cku}) =>
      <TextLink text=name to=Route(Promotion->LegacyRouter.routeToPathname ++ "/" ++ cku) />,
  },
  {
    name: t("Start date"),
    layout: {minWidth: 110.->#px},
    render: ({startDate}) =>
      <TextStyle> {startDate->Intl.dateTimeFormat(~dateStyle=#short)->React.string} </TextStyle>,
  },
  {
    name: t("End date"),
    layout: {minWidth: 110.->#px},
    render: ({endDate}) =>
      <TextStyle> {endDate->Intl.dateTimeFormat(~dateStyle=#short)->React.string} </TextStyle>,
  },
  {
    name: t("Discounted products"),
    layout: {minWidth: 60.->#px, width: 170.->#px},
    render: ({discountsCount}) =>
      <TextStyle> {discountsCount->Int.toString->React.string} </TextStyle>,
  },
  {
    name: t("Price list"),
    layout: {minWidth: 100.->#px},
    render: ({priceName}) => <TextStyle> {priceName->React.string} </TextStyle>,
  },
  {
    name: t("Active shops"),
    layout: {minWidth: 100.->#px},
    render: ({formattedShopsNames}) => <TextStyle> {formattedShopsNames->React.string} </TextStyle>,
  },
  {
    name: t("Status"),
    layout: {minWidth: 90.->#px, width: 1.->#fr},
    render: ({formattedStatus}) => <PromotionStatusBadge status=formattedStatus />,
  },
  {
    layout: {minWidth: 70.->#px, width: 70.->#px},
    render: ({cku, name, startDate, id}) => {
      let notifier = Notifier.use()
      <Inline align=#end>
        <PromotionTableMoreActionsMenu id cku name startDate notifier />
      </Inline>
    },
  },
]

@react.component
let make = (~authorizedCreateRoute) => {
  let notifier = Notifier.use()
  let scope = Auth.useScope()
  let activeShop = Auth.useActiveShop()

  let initialFilters = {Filters.shop: activeShop}
  let initialState = Scaffolded.makeInitialState(~filters=initialFilters)
  let (state, dispatch) = Scaffolded.use(() => initialState)
  let defaultQueryVariables = Query.makeVariables()

  let filters = switch scope {
  | Organisation(_) =>
    <Auth.SelectShopFilter
      value=?state.filters.shop
      onChange={shop => FiltersUpdated(_ => {Filters.shop: shop})->dispatch}
    />
  | _ => React.null
  }

  let actions = switch authorizedCreateRoute {
  | Ok(createRoute) =>
    <ButtonLink variation=#primary to=Route(createRoute)>
      {t("Create campaign")->React.string}
    </ButtonLink>
  | Error() => React.null
  }

  let emptyState = switch state {
  | {currentPage: 1, searchQuery: None, filters: {shop}} if shop === activeShop =>
    <EmptyState
      illustration=Illustration.create
      title={t("Welcome to the promotional campaigns space.")}
      text=?{authorizedCreateRoute->Result.isOk
        ? Some(t("Start creating your first campaign."))
        : None}>
      actions
    </EmptyState>
  | _ =>
    <EmptyState
      illustration=Illustration.notFound
      title={t("No result were found.")}
      text={t("Try again with another keyword/filter or:")}>
      <Button variation=#neutral onPress={_ => Reset(initialState)->dispatch}>
        {t("Clear search query and filters")->React.string}
      </Button>
    </EmptyState>
  }

  let banner = <Notifier.Banner notifier />

  let searchBar =
    <SearchBar
      value=?state.searchQuery
      placeholder={t("Search a campaign")}
      onChange={searchQuery => Searched(searchQuery)->dispatch}
    />

  <Scaffolded
    title={t("Promotional campaigns")}
    state
    dispatch
    filters
    columns=scaffoldedColumns
    actions
    banner
    searchBar
    emptyState
    defaultQueryVariables
  />
}

let make = React.memo(make)
