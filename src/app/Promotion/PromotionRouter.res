let baseRoute = "/promotions"
let route = baseRoute ++ "/create"

let createRoute = (~authScope) =>
  switch (authScope: Auth.scope) {
  | Organisation({shops}) if shops->Array.length < 6 => Ok(route)
  | Organisation(_) | Single({kind: #WAREHOUSE}) => Error()
  | Single(_) => Ok(route)
  }

@react.component
let make = () => {
  let url = Navigation.useUrl()
  let userRole = Auth.useRole()
  let authScope = Auth.useScope()
  let notifierContext = Notifier.createContext()

  let authorizedCreateRoute = createRoute(~authScope)
  let canShopKindEdit = Result.isOk(authorizedCreateRoute)

  if Auth.isAuthorizedAccess(~role=userRole, ~targetPathname=baseRoute) {
    <Notifier.Provider value=notifierContext>
      {switch url.path->List.fromArray {
      | list{"promotions"} => <PromotionListPage authorizedCreateRoute />
      | list{"promotions", "create"} if canShopKindEdit => <PromotionEditPage canShopKindEdit />
      | list{"promotions", cku} => <PromotionEditPage cku canShopKindEdit />
      | _ => <Navigation.Redirect route=baseRoute />
      }}
    </Notifier.Provider>
  } else {
    <AccessDeniedPage />
  }
}
