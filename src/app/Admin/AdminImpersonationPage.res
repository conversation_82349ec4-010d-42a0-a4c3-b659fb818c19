open Intl

module QueryAllUsersRequest = {
  type item = {
    id: string,
    organizationName: string,
    username: string,
    name: string,
    shopsIds: array<string>,
  }

  let decodeResultItem = jsonUser => {
    let userDict = jsonUser->Json.decodeDict
    let userScopeDict = userDict->Json.flatDecodeDictFieldDict("scope")
    switch (
      userDict->Json.flatDecodeDictFieldString("id"),
      userDict->Json.flatDecodeDictFieldString("organizationName"),
      userDict->Json.flatDecodeDictFieldString("username"),
      userDict->Json.flatDecodeDictFieldString("name"),
      userScopeDict
      ->Json.flatDecodeDictFieldArray("shopIds")
      ->Option.map(shopIds => shopIds->Array.keepMap(Json.decodeString)),
    ) {
    | (Some(id), Some(organizationName), Some(username), Some(name), Some(shopsIds)) =>
      Some({id, organizationName, username, name, shopsIds})
    | _ => None
    }
  }

  let decodeResult = json =>
    switch json->Json.decodeArray {
    | Some(resultItems) => resultItems->Array.keepMap(decodeResultItem)
    | None => []
    }

  let endpoint = Env.gatewayUrl() ++ "/admin/users"

  let make = () => Request.make(endpoint, ~method=#GET)->Future.mapOk(decodeResult)
}

module QueryAllShopsRequest = {
  type item = {
    id: string,
    name: string,
    type_: option<string>,
  }

  let decodeResultItem = json => {
    let dict = json->Json.decodeDict
    let field = Json.flatDecodeDictFieldString
    switch (dict->field("id"), dict->field("name"), dict->field("type")) {
    | (Some(id), Some(name), type_) => Some({id, name, type_})
    | _ => None
    }
  }

  let decodeResult = json =>
    switch json->Json.decodeArray {
    | Some(resultItems) => resultItems->Array.keepMap(decodeResultItem)
    | None => []
    }

  let endpoint = Env.gatewayUrl() ++ "/admin/shops"

  let make = () => Request.make(endpoint, ~method=#GET)->Future.mapOk(decodeResult)
}

module QueryAllUsersWithAssociatedShopsRequest = {
  type shop = {name: string, type_: option<string>}
  type item = {
    id: string,
    organizationName: string,
    username: string,
    name: string,
    shops: array<shop>,
  }

  let mergeQueryAllUsersResultWithQueryAllShopsResult = ((queryAllUsers, queryAllShops)) =>
    switch (queryAllUsers, queryAllShops) {
    | (Ok(queryAllUsers), Ok(queryAllShops))
      if queryAllUsers->Array.size > 0 && queryAllShops->Array.size > 0 =>
      let hashMapShops =
        queryAllShops
        ->Array.map(({QueryAllShopsRequest.id: id, name, type_}) => (id, {name, type_}))
        ->HashMap.String.fromArray
      let items = queryAllUsers->Array.map(user => {
        id: user.QueryAllUsersRequest.id,
        organizationName: user.organizationName,
        username: user.username,
        name: user.name,
        shops: user.shopsIds->Array.keepMap(HashMap.String.get(hashMapShops)),
      })
      Ok(items)
    | _ => Error()
    }

  let make = () => {
    let futures = Future.all2((QueryAllUsersRequest.make(), QueryAllShopsRequest.make()))
    futures->Future.map(mergeQueryAllUsersResultWithQueryAllShopsResult)
  }
}

module TableRow = {
  type shop = {name: string, kind: Auth__Types.shopKind}
  type t = {
    id: string,
    organizationName: string,
    userName: string,
    email: string,
    shops: array<shop>,
  }

  let keyExtractor = row => row.id

  let sanitize = string =>
    string
    ->Js.String2.toLowerCase
    ->Js.String2.normalizeByForm("NFD")
    ->Js.String2.replaceByRe(%re("/[\u0300-\u036f]/g"), "")
    ->Js.String2.replaceByRe(%re("/[^a-zA-Z0-9 ]/g"), "")

  let matchShopsNames = (shopsNames, query) =>
    shopsNames
    ->Array.map(sanitize)
    ->Array.some(shopName => shopName->Js.String2.includes(sanitize(query)))

  let match = (row, query) =>
    switch query {
    | "" => true
    | query =>
      row.id === query ||
      row.shops->Array.map(shop => shop.name)->matchShopsNames(query) ||
      sanitize(row.email)->Js.String2.includes(sanitize(query)) ||
      sanitize(row.organizationName)->Js.String2.includes(sanitize(query)) ||
      sanitize(row.userName)->Js.String2.includes(sanitize(query))
    }

  let fromQueryItem = queryItem => {
    id: queryItem.QueryAllUsersWithAssociatedShopsRequest.id,
    organizationName: queryItem.organizationName,
    shops: queryItem.shops->Array.map(shop => {
      name: shop.name,
      kind: switch shop.type_ {
      | Some("INTEGRATED") => #INTEGRATED
      | Some("AFFILIATED") => #AFFILIATED
      | Some("FRANCHISED") => #FRANCHISED
      | Some("WAREHOUSE") => #WAREHOUSE
      | _ => #INDEPENDENT
      },
    }),
    email: queryItem.username,
    userName: queryItem.name,
  }
}

module TableRows = {
  let rowsPerPage = 10
  let totalPages = rows =>
    (rows->Array.size->Float.fromInt /. rowsPerPage->Float.fromInt)->Js.Math.ceil_float->Float.toInt

  let search = (rows, searchQuery) => rows->Array.keep(row => TableRow.match(row, searchQuery))
  let paginate = (rows, currentPage) =>
    rows->Array.slice(~len=rowsPerPage, ~offset=(currentPage - 1) * rowsPerPage)

  let fromQueryAllUsers = queryAllUsers => queryAllUsers->Array.map(TableRow.fromQueryItem)
}

module TableItemShopsCell = {
  @react.component
  let make = (~shopsNames) => {
    let ref = React.useRef(Js.Nullable.null)
    let domRef = ref->ReactDOM.Ref.domRef

    let formattedText = switch shopsNames {
    | [] => t("No shops associated")
    | [shopName] => shopName
    | shopsNames => template(t("{{count}} shops"), ~values={"count": shopsNames->Array.size}, ())
    }
    let tooltipText = switch shopsNames {
    | [] | [_] => None
    | shopsNames =>
      Some(shopsNames->Array.reduce("", (current, shopName) => current ++ `•  ${shopName}\n`))
    }

    switch tooltipText {
    | Some(tooltipText) =>
      <div
        style={ReactDOMStyle.make(~display="flex", ~gridGap="6px", ~alignItems="center", ())}
        ref=domRef>
        <TextStyle variation=#subdued> {formattedText->React.string} </TextStyle>
        <TooltipIcon variation=#info altTriggerRef=ref>
          <Tooltip.Span text=tooltipText />
        </TooltipIcon>
      </div>
    | None => <TextStyle> {formattedText->React.string} </TextStyle>
    }
  }
}

let tableColumns = (~handleImpersonate) => [
  {
    Table.key: "name",
    name: t("Name / organization"),
    layout: {minWidth: 200.->#px, sticky: true},
    render: ({data: {TableRow.userName: userName, organizationName}}) =>
      <Box spaceY=#xxsmall>
        <Stack space=#xxsmall>
          <Strong> {userName->React.string} </Strong>
          <TextStyle size=#xxsmall variation=#normal> {organizationName->React.string} </TextStyle>
        </Stack>
      </Box>,
  },
  {
    key: "email",
    name: t("Email"),
    layout: {minWidth: 320.->#px},
    render: ({data: {email}}) => <TextStyle> {email->React.string} </TextStyle>,
  },
  {
    key: "shops",
    name: t("Shop(s)"),
    layout: {minWidth: 250.->#px, margin: #large},
    render: ({data: {shops}}) => {
      let shopsNames = shops->Array.map(shop => shop.name)
      <TableItemShopsCell shopsNames />
    },
  },
  {
    key: "kind",
    name: t("Kind"),
    layout: {minWidth: 160.->#px},
    render: ({data: {shops}}) => {
      let shopKinds = shops->Array.map(shop => shop.kind)
      let sharedKind =
        shopKinds[0]->Option.flatMap(firstKind =>
          shopKinds->Array.every(kind => kind === firstKind) ? Some(firstKind) : None
        )
      switch sharedKind {
      | Some(#INDEPENDENT) =>
        <Badge variation=#information> {t("Independent")->React.string} </Badge>
      | Some(#INTEGRATED) => <Badge variation=#primary> {t("Integrated")->React.string} </Badge>
      | Some(#FRANCHISED) => <Badge variation=#warning> {t("Franchised")->React.string} </Badge>
      | Some(#AFFILIATED) => <Badge variation=#important> {t("Affiliated")->React.string} </Badge>
      | Some(#WAREHOUSE) => <Badge variation=#neutral> {t("Warehouse")->React.string} </Badge>
      | None =>
        <TextStyle variation=#subdued size=#xsmall> {t("Differing")->React.string} </TextStyle>
      }
    },
  },
  {
    key: "action",
    layout: {minWidth: 110.->#px, alignX: #flexEnd},
    render: ({data: {id}}) =>
      <Button size=#small variation=#neutral onPress={_ => handleImpersonate(id)}>
        {t("Impersonate")->React.string}
      </Button>,
  },
]

module Reducer = {
  type state = {
    searchQuery: string,
    currentPage: int,
    asyncResult: AsyncResult.t<array<QueryAllUsersWithAssociatedShopsRequest.item>, unit>,
  }

  let initialState = {
    searchQuery: "",
    currentPage: 1,
    asyncResult: AsyncResult.notAsked(),
  }

  type action =
    | SearchQueryChanged(string)
    | AsyncResultGet(AsyncResult.t<array<QueryAllUsersWithAssociatedShopsRequest.item>, unit>)
    | Paginated(LegacyPagination.action, int)

  let make = (prevState, action) =>
    switch action {
    | SearchQueryChanged(searchQuery) => {...prevState, currentPage: 1, searchQuery}
    | AsyncResultGet(asyncResult) => {...prevState, asyncResult, currentPage: 1}
    | Paginated(paginateAction, totalPages) => {
        ...prevState,
        currentPage: switch (paginateAction, prevState.currentPage, totalPages) {
        | (LegacyPagination.First, _, _) | (Prev, 1, _) => 1
        | (Prev, _, _) => prevState.currentPage - 1
        | (Next, prevPage, totalPages) if prevPage >= totalPages => prevPage
        | (Next, _, _) => prevState.currentPage + 1
        | (Last, _, _) => totalPages
        },
      }
    }
}

@react.component
let make = () => {
  let mounted = React.useRef(false)

  let (state, dispatch) = React.useReducer(Reducer.make, Reducer.initialState)
  let {asyncResult, currentPage, searchQuery} = state

  let handleImpersonate = userId =>
    Navigation.openNewTabRoute(AuthRoutes.impersonationRoute(~userId))

  React.useEffect0(() => {
    if !mounted.current {
      let request = QueryAllUsersWithAssociatedShopsRequest.make()

      dispatch(AsyncResultGet(AsyncResult.loading()))
      request->Future.get(result => dispatch(AsyncResultGet(AsyncResult.done(result))))

      Some(() => request->Future.cancel)
    } else {
      None
    }
  })

  React.useEffect0(() => {
    mounted.current = true
    Some(() => mounted.current = false)
  })

  let columns = tableColumns(~handleImpersonate)

  let tableRows = asyncResult->AsyncResult.mapOk(TableRows.fromQueryAllUsers)
  let searchedTableRows = tableRows->AsyncResult.mapOk(rows => rows->TableRows.search(searchQuery))

  let totalPages = switch searchedTableRows {
  | Reloading(Ok(rows)) | Done(Ok(rows)) => rows->TableRows.totalPages
  | _ => 1
  }

  let searchedAndPaginatedTableRows =
    searchedTableRows->AsyncResult.mapOk(rows => rows->TableRows.paginate(currentPage))

  let onRequestSearch = queryString => dispatch(SearchQueryChanged(queryString))
  let onRequestPaginate = paginateAction => dispatch(Paginated(paginateAction, totalPages))

  let placeholderEmptyState = switch (searchedAndPaginatedTableRows, state.asyncResult) {
  | (Done(Ok([])), Done(Ok(rows))) if rows->Array.size > 0 =>
    <EmptyState
      illustration=Illustration.notFound
      title={t("No result were found.")}
      text={t("Try again with another keyword or:")}>
      <Button variation=#neutral onPress={_ => dispatch(SearchQueryChanged(""))}>
        {t("Clear search query")->React.string}
      </Button>
    </EmptyState>
  | _ => EmptyState.error
  }

  let searchBar =
    <Box spaceX=#large spaceBottom=#xmedium>
      <SearchBar value=searchQuery placeholder={t("Search an user")} onChange=onRequestSearch />
    </Box>

  <Page
    variation=#compact
    title={t("Impersonation")}
    subtitle={t("List of users")}
    renderTitleEnd={() => <Badge variation=#primary> {t("Support")->React.string} </Badge>}>
    <BarControl />
    <TableView
      columns
      data=searchedAndPaginatedTableRows
      keyExtractor=TableRow.keyExtractor
      placeholderEmptyState
      searchBar
    />
    <LegacyPagination currentPage totalPages onRequestPaginate />
  </Page>
}

let make = React.memo(make)
