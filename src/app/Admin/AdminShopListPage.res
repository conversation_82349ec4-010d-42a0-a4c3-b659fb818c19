open Intl

module QueryAllShopsRequest = {
  type item = {
    id: string,
    name: string,
    corporateName: string,
    legalRepresentative: string,
    email: string,
    type_: option<string>,
  }

  let decodeResultItem = json => {
    let dict = json->Json.decodeDict
    let field = Json.flatDecodeDictFieldString

    switch (
      dict->field("id"),
      dict->field("name"),
      dict->field("corporateName"),
      dict->field("legalRepresentative"),
      dict->field("email"),
      dict->field("type"),
    ) {
    | (Some(id), Some(name), Some(corporateName), Some(legalRepresentative), Some(email), type_) =>
      Some({
        id,
        name,
        corporateName,
        legalRepresentative,
        email,
        type_,
      })
    | _ => None
    }
  }

  let decodeResult = json =>
    switch json->Json.decodeArray {
    | Some(resultItems) => resultItems->Array.keepMap(decodeResultItem)
    | None => []
    }

  let endpoint = Env.gatewayUrl() ++ "/admin/shops"

  let make = () => Request.make(endpoint, ~method=#GET)->Future.mapOk(decodeResult)
}

module TableRow = {
  type t = {
    id: string,
    name: string,
    corporateName: string,
    legalRepresentative: string,
    email: string,
    kind: Auth__Types.shopKind,
  }

  let keyExtractor = row => row.id

  let sanitize = string =>
    string
    ->Js.String2.toLowerCase
    ->Js.String2.normalizeByForm("NFD")
    ->Js.String2.replaceByRe(%re("/[\u0300-\u036f]/g"), "")
    ->Js.String2.replaceByRe(%re("/[^a-zA-Z0-9 ]/g"), "")

  let match = (row, query) =>
    switch query {
    | "" => true
    | query =>
      row.id === query ||
      sanitize(row.name)->Js.String2.includes(sanitize(query)) ||
      sanitize(row.corporateName)->Js.String2.includes(sanitize(query)) ||
      sanitize(row.legalRepresentative)->Js.String2.includes(sanitize(query)) ||
      sanitize(row.email)->Js.String2.includes(sanitize(query))
    }

  let fromQueryItem = queryItem => {
    id: queryItem.QueryAllShopsRequest.id,
    name: queryItem.name,
    corporateName: queryItem.corporateName,
    legalRepresentative: queryItem.legalRepresentative,
    email: queryItem.email,
    kind: switch queryItem.type_ {
    | Some("INTEGRATED") => #INTEGRATED
    | Some("AFFILIATED") => #AFFILIATED
    | Some("FRANCHISED") => #FRANCHISED
    | Some("WAREHOUSE") => #WAREHOUSE
    | _ => #INDEPENDENT
    },
  }
}

module TableRows = {
  let rowsPerPage = 10
  let totalPages = rows =>
    (rows->Array.size->Float.fromInt /. rowsPerPage->Float.fromInt)->Js.Math.ceil_float->Float.toInt

  let search = (rows, searchQuery) => rows->Array.keep(row => TableRow.match(row, searchQuery))
  let paginate = (rows, currentPage) =>
    rows->Array.slice(~len=rowsPerPage, ~offset=(currentPage - 1) * rowsPerPage)

  let fromQueryAllShops = queryAllShops => queryAllShops->Array.map(TableRow.fromQueryItem)
}

let tableColumns = [
  {
    Table.key: "name",
    name: t("Name"),
    layout: {minWidth: 260.->#px, width: 1.5->#fr},
    render: ({data: {TableRow.name: name, corporateName}}) =>
      <Box spaceY=#xxsmall>
        <Stack space=#xxsmall>
          <Strong> {name->React.string} </Strong>
          <TextStyle size=#xxsmall variation=#normal> {corporateName->React.string} </TextStyle>
        </Stack>
      </Box>,
  },
  {
    key: "legal-representative",
    name: t("Legal representative"),
    layout: {minWidth: 160.->#px},
    render: ({data: {legalRepresentative}}) =>
      <TextStyle> {legalRepresentative->React.string} </TextStyle>,
  },
  {
    key: "email",
    name: t("Email"),
    layout: {minWidth: 320.->#px, margin: #large},
    render: ({data: {email}}) => <TextStyle> {email->React.string} </TextStyle>,
  },
  {
    key: "kind",
    name: t("Kind"),
    layout: {minWidth: 160.->#px},
    render: ({data: {kind}}) =>
      switch kind {
      | #INDEPENDENT => <Badge variation=#information> {t("Independent")->React.string} </Badge>
      | #INTEGRATED => <Badge variation=#primary> {t("Integrated")->React.string} </Badge>
      | #FRANCHISED => <Badge variation=#warning> {t("Franchised")->React.string} </Badge>
      | #AFFILIATED => <Badge variation=#important> {t("Affiliated")->React.string} </Badge>
      | #WAREHOUSE => <Badge variation=#neutral> {t("Warehouse")->React.string} </Badge>
      },
  },
  {
    key: "action",
    name: t("Copy ID"),
    layout: {minWidth: 160.->#px, alignX: #flexEnd},
    render: ({data: {id}}) => <KbdClipboard value=id cropValueAt=10 />,
  },
]

module Reducer = {
  type state = {
    searchQuery: string,
    currentPage: int,
    asyncResult: AsyncResult.t<array<QueryAllShopsRequest.item>, unit>,
  }

  let initialState = {
    searchQuery: "",
    currentPage: 1,
    asyncResult: AsyncResult.notAsked(),
  }

  type action =
    | SearchQueryChanged(string)
    | AsyncResultGet(AsyncResult.t<array<QueryAllShopsRequest.item>, unit>)
    | Paginated(LegacyPagination.action, int)

  let make = (prevState, action) =>
    switch action {
    | SearchQueryChanged(searchQuery) => {...prevState, currentPage: 1, searchQuery}
    | AsyncResultGet(asyncResult) => {...prevState, asyncResult, currentPage: 1}
    | Paginated(paginateAction, totalPages) => {
        ...prevState,
        currentPage: switch (paginateAction, prevState.currentPage, totalPages) {
        | (LegacyPagination.First, _, _) | (Prev, 1, _) => 1
        | (Prev, _, _) => prevState.currentPage - 1
        | (Next, prevPage, totalPages) if prevPage >= totalPages => prevPage
        | (Next, _, _) => prevState.currentPage + 1
        | (Last, _, _) => totalPages
        },
      }
    }
}

@react.component
let make = () => {
  let mounted = React.useRef(false)

  let (state, dispatch) = React.useReducer(Reducer.make, Reducer.initialState)
  let {asyncResult, currentPage, searchQuery} = state

  React.useEffect0(() => {
    if !mounted.current {
      let request = QueryAllShopsRequest.make()->Future.mapError(_ => ())

      dispatch(AsyncResultGet(AsyncResult.loading()))
      request->Future.get(result => dispatch(AsyncResultGet(AsyncResult.done(result))))

      Some(() => request->Future.cancel)
    } else {
      None
    }
  })

  React.useEffect0(() => {
    mounted.current = true
    Some(() => mounted.current = false)
  })

  let tableRows = asyncResult->AsyncResult.mapOk(TableRows.fromQueryAllShops)
  let searchedTableRows = tableRows->AsyncResult.mapOk(rows => rows->TableRows.search(searchQuery))

  let totalPages = switch searchedTableRows {
  | Reloading(Ok(rows)) | Done(Ok(rows)) => rows->TableRows.totalPages
  | _ => 1
  }

  let searchedAndPaginatedTableRows =
    searchedTableRows->AsyncResult.mapOk(rows => rows->TableRows.paginate(currentPage))

  let onRequestSearch = queryString => dispatch(SearchQueryChanged(queryString))
  let onRequestPaginate = paginateAction => dispatch(Paginated(paginateAction, totalPages))

  let placeholderEmptyState = switch (searchedAndPaginatedTableRows, state.asyncResult) {
  | (Done(Ok([])), Done(Ok(rows))) if rows->Array.size > 0 =>
    <EmptyState
      illustration=Illustration.notFound
      title={t("No result were found.")}
      text={t("Try again with another keyword or:")}>
      <Button variation=#neutral onPress={_ => dispatch(SearchQueryChanged(""))}>
        {t("Clear search query")->React.string}
      </Button>
    </EmptyState>
  | _ => EmptyState.error
  }

  let searchBar =
    <Box spaceX=#large spaceBottom=#xmedium>
      <SearchBar value=searchQuery placeholder={t("Search a shop")} onChange=onRequestSearch />
    </Box>

  <Page
    variation=#compact
    title={t("Shops")}
    subtitle={t("List of shops")}
    renderTitleEnd={() => <Badge variation=#primary> {t("Support")->React.string} </Badge>}>
    <BarControl />
    <TableView
      columns=tableColumns
      data=searchedAndPaginatedTableRows
      keyExtractor=TableRow.keyExtractor
      placeholderEmptyState
      searchBar
    />
    <LegacyPagination currentPage totalPages onRequestPaginate />
  </Page>
}

let make = React.memo(make)
