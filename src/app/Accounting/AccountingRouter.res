let baseRoute = "/accounting"
let configurationRoute = (~shopId, ~vendor) =>
  baseRoute ++ "/" ++ shopId ++ "/configuration/" ++ vendor->AccountingVendor.toLowercaseString

let {getAllShopTaxesRequest} = module(AccountingConfigurationPage)
let {createOrUpdateShopAccountingConfigurationRequest} = module(AccountingConfigurationPage)
let {request: getShopAccountingConfigurationRequest} = module(AccountingConfiguration)

@react.component
let make = (~subUrlPath) => {
  let userRole = Auth.useRole()

  if Auth.isAuthorizedAccess(~role=userRole, ~targetPathname=baseRoute) {
    switch subUrlPath {
    | list{} => <AccountingPage configurationRoute getShopAccountingConfigurationRequest />
    | list{shopId, "configuration", vendor} =>
      switch vendor->AccountingVendor.fromString {
      | Some(vendor) =>
        <AccountingConfigurationPage
          getAllShopTaxesRequest
          getShopAccountingConfigurationRequest
          createOrUpdateShopAccountingConfigurationRequest
          configurationRoute
          baseRoute
          vendor
          shopId
        />
      | None => <Navigation.Redirect route={baseRoute} />
      }
    | _ => <Navigation.Redirect route={baseRoute} />
    }
  } else {
    <AccessDeniedPage />
  }
}
