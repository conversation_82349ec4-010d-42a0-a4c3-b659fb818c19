open Intl

// NOTE — This module includes a significant amount of boilerplate for building form,
// with a focus on keeping abstractions low for clarity and control.
// However, some utilities could eventually be added to the Form core module
// to streamline advanced forms (e.g., forms with array-based fieldsets),
// making the process more straightforward while retaining a primitive approach.

let shopAccountingConfigurationRequestBaseUrl =
  Env.gatewayUrl() ++ "/accounting-export-configurations/"

module CreateOrUpdateShopAccountingConfigurationRequest = {
  let createEndpoint = () => shopAccountingConfigurationRequestBaseUrl
  let updateEndpoint = (~shopId) => shopAccountingConfigurationRequestBaseUrl ++ shopId

  let encodeBodyJson = value => {
    let dict = Js.Dict.fromArray([
      ("shopId", value.AccountingConfiguration.shopId->Json.encodeString),
      ("fiscalYearOpeningMonth", value.fiscalYearOpeningMonth->FiscalYearOpeningMonth.encodeToJson),
      (
        "isaComptaAccountNumber",
        switch value.isaComptaAccountNumber {
        | Some(isaComptaAccountNumber) => isaComptaAccountNumber->Json.encodeString
        | None => Json.encodedNull
        },
      ),
      (
        "accountingExportTaxAccounts",
        value.taxesAccounts
        ->Array.map(AccountingConfiguration.TaxAccount.encodeToJson)
        ->Json.encodeArray,
      ),
      (
        "breakdownOfConsumerSalesByCashRegisterDailyReport",
        value.breakdownOfConsumerSalesByCashRegisterDailyReport->Json.encodeBoolean,
      ),
      (
        "includeCashFromIndividualsInPaymentJournal",
        value.includeCashFromIndividualsInPaymentJournal->Json.encodeBoolean,
      ),
    ])

    switch value.maybePaymentsAccounts {
    | Some(paymentsAccounts) =>
      dict->Js.Dict.set(
        "accountingExportPaymentAccounts",
        paymentsAccounts
        ->Array.map(AccountingConfiguration.PaymentAccount.encodeToJson)
        ->Json.encodeArray,
      )
    | None => ()
    }

    dict->Json.encodeDict
  }

  let make = (~getShopAccountingConfigurationRequest, value) => {
    let bodyJson = encodeBodyJson(value)
    getShopAccountingConfigurationRequest(~shopId=value.shopId)
    ->Future.mapError(_ => ())
    ->Future.flatMapOk(shopAccountingConfiguration => {
      // TODO — The backend should have a unique create/update request
      switch shopAccountingConfiguration {
      | Some(_) => Request.make(updateEndpoint(~shopId=value.shopId), ~method=#PATCH, ~bodyJson)
      | None => Request.make(createEndpoint(), ~method=#POST, ~bodyJson)
      }->Future.mapError(_ => ())
    })
    ->Future.flatMapOk(_ => getShopAccountingConfigurationRequest(~shopId=value.shopId))
    ->Future.map(result =>
      switch result {
      | Ok(Some(response)) => Ok(response)
      | Ok(None) | Error(_) => Error()
      }
    )
  }
}

// NOTE — At some point, the backend should handle this business logic.
module GetAllShopTaxesRequest = {
  module Query = %graphql(`
    query QueryAllShopTaxes($filterBy: InputTaxQueryFilter) {
       taxes(filterBy: $filterBy) {
        edges {
          node {
            id
            value
          }
        }
      }
    }
  `)

  type item = {id: Uuid.t, label: string, rate: float}

  let queryEdgesToItems = edges =>
    edges
    ->Array.keepMap(({Query.node: {id, value}}) =>
      Uuid.fromString(id)->Option.map(id => {
        id,
        label: t("VAT rate") ++ " " ++ Intl.percentFormat(value /. 100., ~maximumFractionDigits=1),
        rate: value,
      })
    )
    ->SortArray.stableSortBy(({rate: currentRate}, {rate: nextRate}) =>
      Int.fromFloat(currentRate) < Int.fromFloat(nextRate) ? 1 : -1
    )

  let make = (~shopId, ~apolloClient: ApolloClient.t) =>
    apolloClient.query(
      ~query=module(Query),
      ~fetchPolicy=NetworkOnly,
      {filterBy: Some({shopIds: Some({_in: [shopId]})})},
    )
    ->FuturePromise.fromPromise
    ->Future.map(value =>
      switch value {
      | Ok(Ok({data: {taxes: {edges}}})) => Ok(queryEdgesToItems(edges))
      | _ => Error()
      }
    )
}

module FormField = {
  type t<'value> = {
    value: 'value,
    touched: bool,
    errorMessage: option<string>,
  }

  let make = (~value) => {value, touched: false, errorMessage: None}

  let mapErrorMessage = ({errorMessage}) => errorMessage

  let nonEmptyStringErrorMessageStringLiteral = t("Please fulfill this field.")
  let noDuplicateErrorMessageStringLiteral = t(
    "This field must be unique; the value {{value}} has already been entered.",
  )
  let stringMinLengthErrorMessageStringLiteral = t("This field must contain {{length}} characters.")
  let stringLengthErrorMessageStringLiteral = t(
    "This field must contain between {{minLength}} and {{maxLength}} characters.",
  )
  let stringPatternErrorMessageStringLiteral = t("This field should be of format {{pattern}}.")

  let validateNonEmptyStringValue = value =>
    value === "" ? Some(nonEmptyStringErrorMessageStringLiteral) : None

  let validateNoDuplicate = (value, duplicates) =>
    duplicates->Array.some(item => item === value)
      ? Some(template(noDuplicateErrorMessageStringLiteral, ~values={"value": value}, ()))
      : None

  let valideNonEmptyStringLength = (length, value) => {
    value->Js.String.length !== length
      ? Some(template(stringMinLengthErrorMessageStringLiteral, ~values={"length": length}, ()))
      : None
  }

  let validateStringLength = (minLength, maxLength, value) =>
    Js.String.length(value) >= minLength && Js.String.length(value) <= maxLength
      ? None
      : Some(
          template(
            stringLengthErrorMessageStringLiteral,
            ~values={"minLength": minLength, "maxLength": maxLength},
            (),
          ),
        )

  let errorMessageFromValidationRules = errors => {
    let errors = errors->Array.keep(Option.isSome)
    switch errors[0] {
    | Some(error) => error
    | None => None
    }
  }
}

module TaxAccountFormFieldset = {
  module ID: {
    type t
    let fromUuid: Uuid.t => t
    let unsafeFromString: string => t
    let toString: t => string
    let equal: (t, t) => bool
  } = {
    type t = Uuid.t
    let fromUuid = value => value
    let unsafeFromString = Uuid.unsafeFromString
    let toString = Uuid.toString
    let equal = (a, b) => a === b
  }

  type productsSoldFormFields = {
    accountNumberFormField: FormField.t<string>,
    labelFormField: FormField.t<string>,
  }

  type deductibleTaxFormFields = {
    accountNumberFormField: FormField.t<string>,
    labelFormField: FormField.t<string>,
  }

  type t = {
    id: ID.t,
    rate: float,
    label: string,
    isaComptaCodeFormField: option<FormField.t<string>>,
    productsSoldFormFields: productsSoldFormFields,
    deductibleTaxFormFields: option<deductibleTaxFormFields>,
  }

  let isDeductibleTax = (~taxRate) => taxRate !== 0.

  let isErrored = ({isaComptaCodeFormField, deductibleTaxFormFields, productsSoldFormFields}) => {
    let {mapErrorMessage} = module(FormField)
    Option.isSome(isaComptaCodeFormField->Option.flatMap(mapErrorMessage)) ||
    Option.isSome(mapErrorMessage(productsSoldFormFields.accountNumberFormField)) ||
    Option.isSome(mapErrorMessage(productsSoldFormFields.labelFormField)) ||
    Option.isSome(
      deductibleTaxFormFields->Option.flatMap(({accountNumberFormField}) =>
        mapErrorMessage(accountNumberFormField)
      ),
    ) ||
    Option.isSome(
      deductibleTaxFormFields->Option.flatMap(({labelFormField}) =>
        mapErrorMessage(labelFormField)
      ),
    )
  }

  let isPristine = ({isaComptaCodeFormField, productsSoldFormFields, deductibleTaxFormFields}) =>
    isaComptaCodeFormField->Option.mapWithDefault(true, ({touched}) => !touched) &&
    !productsSoldFormFields.accountNumberFormField.touched &&
    !productsSoldFormFields.labelFormField.touched &&
    deductibleTaxFormFields->Option.mapWithDefault(true, ({
      accountNumberFormField,
      labelFormField,
    }) => !accountNumberFormField.touched && !labelFormField.touched)

  type field =
    | IsaComptaCode
    | DeductibleTaxAccountNumber
    | DeductibleTaxLabel
    | ProductsSoldAccountNumber
    | ProductsSoldLabel

  let fillWithStandard = (taxAccountFormFieldset, ~vendor) => {
    let standardTaxAccount = AccountingConfiguration.TaxAccount.fillWithStandard(
      ~taxRate=taxAccountFormFieldset.rate,
      ~taxId=taxAccountFormFieldset.id->ID.toString,
    )
    {
      ...taxAccountFormFieldset,
      isaComptaCodeFormField: switch vendor {
      | AccountingVendor.IsaCompta =>
        standardTaxAccount.isaComptaCode->Option.map(value => FormField.make(~value))
      | _ => None
      },
      deductibleTaxFormFields: isDeductibleTax(~taxRate=taxAccountFormFieldset.rate)
        ? standardTaxAccount.deductibleTax->Option.map(((accountNumber, label)) => {
            accountNumberFormField: FormField.make(~value=accountNumber),
            labelFormField: FormField.make(~value=label),
          })
        : None,
      productsSoldFormFields: switch standardTaxAccount.productsSold {
      | (accountNumber, label) => {
          accountNumberFormField: FormField.make(~value=accountNumber),
          labelFormField: FormField.make(~value=label),
        }
      },
    }
  }

  let changeFormFieldValue = (fieldset, ~field, ~value: string) => {
    ...fieldset,
    isaComptaCodeFormField: switch field {
    | IsaComptaCode =>
      fieldset.isaComptaCodeFormField->Option.map(formField => {...formField, value})
    | _ => fieldset.isaComptaCodeFormField
    },
    deductibleTaxFormFields: fieldset.deductibleTaxFormFields->Option.map(formFields => {
      accountNumberFormField: switch field {
      | DeductibleTaxAccountNumber => {...formFields.accountNumberFormField, value}
      | _ => formFields.accountNumberFormField
      },
      labelFormField: switch field {
      | DeductibleTaxLabel => {...formFields.labelFormField, value}
      | _ => formFields.labelFormField
      },
    }),
    productsSoldFormFields: switch fieldset.productsSoldFormFields {
    | formFields => {
        accountNumberFormField: switch field {
        | ProductsSoldAccountNumber => {...formFields.accountNumberFormField, value}
        | _ => formFields.accountNumberFormField
        },
        labelFormField: switch field {
        | ProductsSoldLabel => {...formFields.labelFormField, value}
        | _ => formFields.labelFormField
        },
      }
    },
  }

  let blurFormField = (fieldset, ~field) => {
    ...fieldset,
    isaComptaCodeFormField: switch field {
    | IsaComptaCode =>
      fieldset.isaComptaCodeFormField->Option.map(formField => {...formField, touched: true})
    | _ => fieldset.isaComptaCodeFormField
    },
    deductibleTaxFormFields: fieldset.deductibleTaxFormFields->Option.map(formFields => {
      accountNumberFormField: switch field {
      | DeductibleTaxAccountNumber => {...formFields.accountNumberFormField, touched: true}
      | _ => formFields.accountNumberFormField
      },
      labelFormField: switch field {
      | DeductibleTaxLabel => {...formFields.labelFormField, touched: true}
      | _ => formFields.labelFormField
      },
    }),
    productsSoldFormFields: switch fieldset.productsSoldFormFields {
    | formFields => {
        accountNumberFormField: switch field {
        | ProductsSoldAccountNumber => {...formFields.accountNumberFormField, touched: true}
        | _ => formFields.accountNumberFormField
        },
        labelFormField: switch field {
        | ProductsSoldLabel => {...formFields.labelFormField, touched: true}
        | _ => formFields.labelFormField
        },
      }
    },
  }

  let mapFormFieldValiation = (
    value,
    accountNumberDuplicates: array<string>,
    isaComptaCodesDuplicates: array<string>,
  ) => {
    let {isaComptaCodeFormField, deductibleTaxFormFields, productsSoldFormFields} = value
    let {
      validateNonEmptyStringValue,
      validateNoDuplicate,
      validateStringLength,
      errorMessageFromValidationRules,
    } = module(FormField)
    {
      ...value,
      isaComptaCodeFormField: switch isaComptaCodeFormField {
      | Some(isaComptaCodeFormField) =>
        Some({
          ...isaComptaCodeFormField,
          errorMessage: errorMessageFromValidationRules([
            validateNonEmptyStringValue(isaComptaCodeFormField.value),
            validateStringLength(1, 2, isaComptaCodeFormField.value),
            validateNoDuplicate(isaComptaCodeFormField.value, isaComptaCodesDuplicates),
          ]),
        })
      | None => None
      },
      deductibleTaxFormFields: deductibleTaxFormFields->Option.map(({
        accountNumberFormField,
        labelFormField,
      }) => {
        accountNumberFormField: {
          ...accountNumberFormField,
          errorMessage: errorMessageFromValidationRules([
            validateNonEmptyStringValue(accountNumberFormField.value),
            validateNoDuplicate(accountNumberFormField.value, accountNumberDuplicates),
          ]),
        },
        labelFormField: {
          ...labelFormField,
          errorMessage: errorMessageFromValidationRules([
            validateNonEmptyStringValue(labelFormField.value),
          ]),
        },
      }),
      productsSoldFormFields: switch productsSoldFormFields {
      | {accountNumberFormField, labelFormField} => {
          accountNumberFormField: {
            ...accountNumberFormField,
            errorMessage: errorMessageFromValidationRules([
              validateNoDuplicate(accountNumberFormField.value, accountNumberDuplicates),
              validateNonEmptyStringValue(accountNumberFormField.value),
            ]),
          },
          labelFormField: {
            ...labelFormField,
            errorMessage: validateNonEmptyStringValue(labelFormField.value),
          },
        }
      },
    }
  }
}

module PaymentAccountFormFieldset = {
  type t = {
    paymentMethod: PaymentMethod.t,
    accountNumberFormField: FormField.t<string>,
    accountLabelFormField: FormField.t<string>,
    isaComptaJournalCodeFormField: option<FormField.t<string>>,
  }

  let fromAccountingConfigurationPaymentAccount = (
    {
      AccountingConfiguration.PaymentAccount.paymentMethod: paymentMethod,
      accountNumber,
      accountLabel,
      isaComptaJournalCode,
    },
    ~vendor,
  ) => {
    paymentMethod,
    accountNumberFormField: FormField.make(~value=accountNumber),
    accountLabelFormField: FormField.make(~value=accountLabel),
    isaComptaJournalCodeFormField: if vendor->AccountingVendor.isExcel {
      None
    } else {
      Some(
        isaComptaJournalCode->Option.mapWithDefault(FormField.make(~value=""), value =>
          FormField.make(~value)
        ),
      )
    },
  }

  let make = (~paymentMethod, ~vendor) => {
    paymentMethod,
    accountNumberFormField: FormField.make(~value=""),
    accountLabelFormField: FormField.make(
      ~value=template(
        t("Payment {{paymentMethod}}"),
        ~values={"paymentMethod": paymentMethod->PaymentMethod.toLabel},
        (),
      ),
    ),
    isaComptaJournalCodeFormField: switch vendor {
    | AccountingVendor.Excel => None
    | IsaCompta => Some(FormField.make(~value="R1"))
    },
  }

  let isPristine = ({
    isaComptaJournalCodeFormField,
    accountNumberFormField,
    accountLabelFormField,
  }) =>
    isaComptaJournalCodeFormField->Option.mapWithDefault(true, ({touched}) => !touched) &&
    !accountNumberFormField.touched &&
    !accountLabelFormField.touched

  let isErrored = ({
    isaComptaJournalCodeFormField,
    accountNumberFormField,
    accountLabelFormField,
  }) => {
    let {mapErrorMessage} = module(FormField)
    Option.isSome(isaComptaJournalCodeFormField->Option.flatMap(mapErrorMessage)) ||
    Option.isSome(mapErrorMessage(accountNumberFormField)) ||
    Option.isSome(mapErrorMessage(accountLabelFormField))
  }

  type field = AccountNumber | AccountLabel | IsaComptaJournalCode

  let changeFormFieldValue = (fieldset, ~field, ~value) => {
    ...fieldset,
    accountNumberFormField: switch field {
    | AccountNumber => {...fieldset.accountNumberFormField, value}
    | _ => fieldset.accountNumberFormField
    },
    accountLabelFormField: switch field {
    | AccountLabel => {...fieldset.accountLabelFormField, value}
    | _ => fieldset.accountLabelFormField
    },
    isaComptaJournalCodeFormField: switch field {
    | IsaComptaJournalCode =>
      fieldset.isaComptaJournalCodeFormField->Option.map(isaComptaJournalCodeFormField => {
        ...isaComptaJournalCodeFormField,
        value,
      })
    | _ => fieldset.isaComptaJournalCodeFormField
    },
  }

  let blurFormField = (fieldset, ~field) => {
    ...fieldset,
    accountLabelFormField: switch field {
    | AccountLabel => {...fieldset.accountLabelFormField, touched: true}
    | _ => fieldset.accountLabelFormField
    },
    accountNumberFormField: switch field {
    | AccountNumber => {...fieldset.accountNumberFormField, touched: true}
    | _ => fieldset.accountNumberFormField
    },
    isaComptaJournalCodeFormField: switch field {
    | IsaComptaJournalCode =>
      fieldset.isaComptaJournalCodeFormField->Option.map(formField => {...formField, touched: true})
    | _ => fieldset.isaComptaJournalCodeFormField
    },
  }
  let validateIsaComptaCodeFormat = value =>
    Js.Re.test_(%re("/^R[0-9]$/"), value)
      ? None
      : Some(
          template(
            FormField.stringPatternErrorMessageStringLiteral,
            ~values={"pattern": "R0 à R9"},
            (),
          ),
        )

  let mapFormFieldValiation = (
    {accountNumberFormField, accountLabelFormField, isaComptaJournalCodeFormField, paymentMethod},
    accountNumberDuplicates,
    isaComptaCodesDuplicates,
  ) => {
    let {
      validateNonEmptyStringValue,
      valideNonEmptyStringLength,
      validateNoDuplicate,
      errorMessageFromValidationRules,
    } = module(FormField)
    let accountNumberFormField = {
      ...accountNumberFormField,
      errorMessage: errorMessageFromValidationRules([
        validateNoDuplicate(accountNumberFormField.value, accountNumberDuplicates),
        validateNonEmptyStringValue(accountNumberFormField.value),
      ]),
    }
    let accountLabelFormField = {
      ...accountLabelFormField,
      errorMessage: validateNonEmptyStringValue(accountLabelFormField.value),
    }

    let isaComptaJournalCodeFormField = switch isaComptaJournalCodeFormField {
    | Some(isaComptaJournalCodeFormField) =>
      Some({
        ...isaComptaJournalCodeFormField,
        errorMessage: errorMessageFromValidationRules([
          validateNonEmptyStringValue(isaComptaJournalCodeFormField.value),
          valideNonEmptyStringLength(2, isaComptaJournalCodeFormField.value),
          validateIsaComptaCodeFormat(isaComptaJournalCodeFormField.value),
          validateNoDuplicate(isaComptaJournalCodeFormField.value, isaComptaCodesDuplicates),
        ]),
      })
    | None => None
    }

    {
      paymentMethod,
      accountNumberFormField,
      accountLabelFormField,
      isaComptaJournalCodeFormField,
    }
  }
}

module Reducer = {
  type action =
    | AsyncDataLoading
    | AsyncDataDoneOk({
        vendor: AccountingVendor.t,
        allShopTaxes: array<GetAllShopTaxesRequest.item>,
        accountingConfiguration?: AccountingConfiguration.t,
      })
    | AsyncDataDoneError
    | SubmitButtonClicked
    | SubmissionSucceeded({
        vendor: AccountingVendor.t,
        allShopTaxes: array<GetAllShopTaxesRequest.item>,
        accountingConfiguration: AccountingConfiguration.t,
      })
    | SubmissionFailed(string)
    | NotificationBannerCloseButtonClicked
    | AutoCompleteWithStandardTaxesAccounts
    | VendorChanged(AccountingVendor.t)
    | FiscalYearOpeningMonthChanged(FiscalYearOpeningMonth.t)
    | IsaComptaAccountNumberFormFieldChanged(string)
    | IsaComptaAccountNumberFormFieldBlured
    | BreakdownOfConsumerSalesByCashRegisterDailyReportChanged(bool)
    | IncludeCashFromIndividualsInPaymentJournalChanged(bool)
    | TaxAccountFormFieldChanged(TaxAccountFormFieldset.ID.t, TaxAccountFormFieldset.field, string)
    | TaxAccountFormFieldBlured(TaxAccountFormFieldset.ID.t, TaxAccountFormFieldset.field)
    | PaymentsAccountsFormArrayFieldsetToggled
    | AutocompleteBasicPaymentsAccounts
    | PaymentsAccountsFormFieldsetAdded
    | PaymentsAccountsFormFieldsetRemoved(PaymentMethod.t)
    | PaymentAccountFormFieldChanged(PaymentMethod.t, PaymentAccountFormFieldset.field, string)
    | PaymentAccountFormFieldBlured(PaymentMethod.t, PaymentAccountFormFieldset.field)
    | PaymentAccountPaymentMethodChanged({previous: PaymentMethod.t, next: PaymentMethod.t})

  type data = {
    submission: AsyncResult.t<unit, string>,
    initialAccountingConfiguration: option<AccountingConfiguration.t>,
    vendor: AccountingVendor.t,
    fiscalYearOpeningMonth: FiscalYearOpeningMonth.t,
    breakdownOfConsumerSalesByCashRegisterDailyReport: bool,
    includeCashFromIndividualsInPaymentJournal: bool,
    isaComptaAccountNumberFormField: option<FormField.t<string>>,
    taxesAccountsFormArrayFieldset: array<TaxAccountFormFieldset.t>,
    maybePaymentsAccountsFormArrayFieldset: option<array<PaymentAccountFormFieldset.t>>,
  }

  let isDataErrored = ({
    isaComptaAccountNumberFormField,
    taxesAccountsFormArrayFieldset,
    maybePaymentsAccountsFormArrayFieldset,
  }) =>
    Option.isSome(
      isaComptaAccountNumberFormField->Option.flatMap(({errorMessage}) => errorMessage),
    ) ||
    Array.some(taxesAccountsFormArrayFieldset, TaxAccountFormFieldset.isErrored) ||
    maybePaymentsAccountsFormArrayFieldset->Option.mapWithDefault(
      false,
      paymentsAccountsFormArrayFieldset =>
        paymentsAccountsFormArrayFieldset->Array.some(PaymentAccountFormFieldset.isErrored),
    )

  let findDuplicates = arr => {
    let seen = Js.Dict.empty()
    let duplicates = Js.Dict.empty()

    arr->Array.forEach(value => {
      if value != "" {
        switch Js.Dict.get(seen, value) {
        | Some(_) => Js.Dict.set(duplicates, value, true)
        | None => Js.Dict.set(seen, value, true)
        }
      }
    })

    Js.Dict.keys(duplicates)
  }

  let mapDataFormFieldValiation = data => {
    let {validateNonEmptyStringValue, errorMessageFromValidationRules} = module(FormField)

    let isaComptaAccountNumberFormField = switch data.isaComptaAccountNumberFormField {
    | Some(isaComptaAccountNumberFormField) =>
      Some({
        ...isaComptaAccountNumberFormField,
        errorMessage: errorMessageFromValidationRules([
          validateNonEmptyStringValue(isaComptaAccountNumberFormField.value),
        ]),
      })
    | None => None
    }

    let taxesAccountsIsaComptaCodes =
      data.taxesAccountsFormArrayFieldset->Array.keepMap(item =>
        item.isaComptaCodeFormField->Option.map(f => f.value)
      )
    let paymentsAccountsIsaComptaCodes = switch data.maybePaymentsAccountsFormArrayFieldset {
    | Some(fieldSet) =>
      fieldSet->Array.keepMap(item => item.isaComptaJournalCodeFormField->Option.map(f => f.value))
    | None => []
    }
    let taxesAccountsIsaComptaCodesDuplicates = findDuplicates(taxesAccountsIsaComptaCodes)
    let paymentsAccountsIsaComptaCodesDuplicates = findDuplicates(paymentsAccountsIsaComptaCodes)

    let deductibleTaxAccountNumbers =
      data.taxesAccountsFormArrayFieldset->Array.keepMap(item =>
        item.deductibleTaxFormFields->Option.map(f => f.accountNumberFormField.value)
      )
    let taxAccountNumbers =
      data.taxesAccountsFormArrayFieldset->Array.map(item =>
        item.productsSoldFormFields.accountNumberFormField.value
      )
    let paymentAccountNumbers =
      data.maybePaymentsAccountsFormArrayFieldset
      ->Option.map(paymentsAccountsFormArrayFieldset =>
        paymentsAccountsFormArrayFieldset->Array.map(paymentAccountFormFieldset =>
          paymentAccountFormFieldset.accountNumberFormField.value
        )
      )
      ->Option.getWithDefault([])

    let accountNumberDuplicates = findDuplicates(
      Array.concatMany([deductibleTaxAccountNumbers, taxAccountNumbers, paymentAccountNumbers]),
    )

    let taxesAccountsFormArrayFieldset =
      data.taxesAccountsFormArrayFieldset->Array.map(fieldSet =>
        TaxAccountFormFieldset.mapFormFieldValiation(
          fieldSet,
          accountNumberDuplicates,
          taxesAccountsIsaComptaCodesDuplicates,
        )
      )

    let maybePaymentsAccountsFormArrayFieldset =
      data.maybePaymentsAccountsFormArrayFieldset->Option.map(paymentsAccountsFormArrayFieldset =>
        paymentsAccountsFormArrayFieldset->Array.map(fieldSet =>
          PaymentAccountFormFieldset.mapFormFieldValiation(
            fieldSet,
            accountNumberDuplicates,
            paymentsAccountsIsaComptaCodesDuplicates,
          )
        )
      )
    {
      ...data,
      isaComptaAccountNumberFormField,
      taxesAccountsFormArrayFieldset,
      maybePaymentsAccountsFormArrayFieldset,
    }
  }

  type state = AsyncResult.t<data, unit>

  let isSubmitting = state =>
    switch state {
    | AsyncData.Done(Ok({submission})) | Reloading(Ok({submission})) => AsyncData.isBusy(submission)
    | _ => false
    }

  let isPristine = state =>
    switch state {
    | AsyncData.Done(Ok(data)) | Reloading(Ok(data)) =>
      let {
        initialAccountingConfiguration,
        fiscalYearOpeningMonth,
        breakdownOfConsumerSalesByCashRegisterDailyReport,
        includeCashFromIndividualsInPaymentJournal,
        isaComptaAccountNumberFormField,
        taxesAccountsFormArrayFieldset,
        maybePaymentsAccountsFormArrayFieldset,
      } = data
      let initialFiscalYearOpeningMonth =
        initialAccountingConfiguration->Option.mapWithDefault(FiscalYearOpeningMonth.January, ({
          fiscalYearOpeningMonth,
        }) => fiscalYearOpeningMonth)
      let initialBreakdownOfConsumerSalesByCashRegisterDailyReport =
        initialAccountingConfiguration->Option.mapWithDefault(true, ({
          breakdownOfConsumerSalesByCashRegisterDailyReport,
        }) => breakdownOfConsumerSalesByCashRegisterDailyReport)

      let initialIncludeCashFromIndividualsInPaymentJournal =
        initialAccountingConfiguration->Option.mapWithDefault(true, ({
          includeCashFromIndividualsInPaymentJournal,
        }) => includeCashFromIndividualsInPaymentJournal)

      FiscalYearOpeningMonth.isEqual(initialFiscalYearOpeningMonth, fiscalYearOpeningMonth) &&
      breakdownOfConsumerSalesByCashRegisterDailyReport ===
        initialBreakdownOfConsumerSalesByCashRegisterDailyReport &&
      includeCashFromIndividualsInPaymentJournal ===
        initialIncludeCashFromIndividualsInPaymentJournal &&
      isaComptaAccountNumberFormField->Option.mapWithDefault(true, ({touched}) => !touched) &&
      taxesAccountsFormArrayFieldset->Array.every(TaxAccountFormFieldset.isPristine) &&
      maybePaymentsAccountsFormArrayFieldset->Option.mapWithDefault(
        true,
        paymentsAccountsFormArrayFieldset =>
          paymentsAccountsFormArrayFieldset->Array.every(PaymentAccountFormFieldset.isPristine),
      )
    | _ => true
    }

  let hasBeenSubmitted = state =>
    switch state {
    | AsyncData.Done(Ok({submission: Done(_)})) | Reloading(Ok({submission: Done(_)})) => true
    | _ => false
    }

  let initialState = () => AsyncData.notAsked()

  let initialStateDataFromOkResult = (~vendor, ~allShopTaxes, ~accountingConfiguration=?, ()) => {
    submission: AsyncResult.notAsked(),
    initialAccountingConfiguration: accountingConfiguration,
    vendor,
    fiscalYearOpeningMonth: accountingConfiguration->Option.mapWithDefault(
      FiscalYearOpeningMonth.January,
      ({AccountingConfiguration.fiscalYearOpeningMonth: fiscalYearOpeningMonth}) =>
        fiscalYearOpeningMonth,
    ),
    breakdownOfConsumerSalesByCashRegisterDailyReport: accountingConfiguration->Option.mapWithDefault(
      true,
      ({
        AccountingConfiguration.breakdownOfConsumerSalesByCashRegisterDailyReport:
          breakdownOfConsumerSalesByCashRegisterDailyReport,
      }) => breakdownOfConsumerSalesByCashRegisterDailyReport,
    ),
    includeCashFromIndividualsInPaymentJournal: accountingConfiguration->Option.mapWithDefault(
      false,
      ({
        AccountingConfiguration.includeCashFromIndividualsInPaymentJournal:
          includeCashFromIndividualsInPaymentJournal,
      }) => includeCashFromIndividualsInPaymentJournal,
    ),
    isaComptaAccountNumberFormField: switch (vendor, accountingConfiguration) {
    | (IsaCompta, Some(accountingConfiguration)) =>
      let value = accountingConfiguration.isaComptaAccountNumber->Option.getWithDefault("")
      Some(FormField.make(~value))
    | (IsaCompta, None) => Some(FormField.make(~value=""))
    | (Excel, _) => None
    },
    taxesAccountsFormArrayFieldset: allShopTaxes->Array.map(shopTax => {
      let {isIsaCompta} = module(AccountingVendor)
      let {isDeductibleTax} = module(TaxAccountFormFieldset)
      let shopTaxId = shopTax.GetAllShopTaxesRequest.id
      let id = TaxAccountFormFieldset.ID.fromUuid(shopTaxId)
      let initialTaxAccount =
        accountingConfiguration->Option.flatMap(({taxesAccounts}) =>
          taxesAccounts->Array.getBy(({taxId}) => taxId === Uuid.toString(shopTaxId))
        )
      switch initialTaxAccount {
      | Some(initialTaxAccount) => {
          TaxAccountFormFieldset.id,
          rate: shopTax.rate,
          label: shopTax.label,
          isaComptaCodeFormField: if isIsaCompta(vendor) {
            let value = initialTaxAccount.isaComptaCode->Option.getWithDefault("")
            Some(FormField.make(~value))
          } else {
            None
          },
          deductibleTaxFormFields: isDeductibleTax(~taxRate=shopTax.rate)
            ? switch initialTaxAccount.deductibleTax {
              | Some((accountNumber, label)) =>
                Some({
                  accountNumberFormField: FormField.make(~value=accountNumber),
                  labelFormField: FormField.make(~value=label),
                })
              | None =>
                Some({
                  accountNumberFormField: FormField.make(~value=""),
                  labelFormField: FormField.make(~value=""),
                })
              }
            : None,
          productsSoldFormFields: switch initialTaxAccount.productsSold {
          | (accountNumber, label) => {
              accountNumberFormField: FormField.make(~value=accountNumber),
              labelFormField: FormField.make(~value=label),
            }
          },
        }
      | None => {
          TaxAccountFormFieldset.id,
          rate: shopTax.rate,
          label: shopTax.label,
          isaComptaCodeFormField: if isIsaCompta(vendor) {
            Some(FormField.make(~value=""))
          } else {
            None
          },
          deductibleTaxFormFields: isDeductibleTax(~taxRate=shopTax.rate)
            ? Some({
                accountNumberFormField: FormField.make(~value=""),
                labelFormField: FormField.make(~value=""),
              })
            : None,
          productsSoldFormFields: {
            accountNumberFormField: FormField.make(~value=""),
            labelFormField: FormField.make(~value=""),
          },
        }
      }
    }),
    maybePaymentsAccountsFormArrayFieldset: accountingConfiguration
    ->Option.flatMap(({maybePaymentsAccounts}) => maybePaymentsAccounts)
    ->Option.map(paymentsAccounts =>
      paymentsAccounts->Array.map(paymentAccount =>
        PaymentAccountFormFieldset.fromAccountingConfigurationPaymentAccount(
          paymentAccount,
          ~vendor,
        )
      )
    ),
  }

  let initialStateFromOkResult = (~vendor, ~allShopTaxes, ~accountingConfiguration=?, ()) =>
    AsyncResult.doneOk(
      initialStateDataFromOkResult(~vendor, ~allShopTaxes, ~accountingConfiguration?, ()),
    )

  let make = (state, action) =>
    switch action {
    | AsyncDataLoading => state->AsyncResult.toBusy
    | AsyncDataDoneOk({allShopTaxes, vendor, ?accountingConfiguration}) =>
      initialStateFromOkResult(~allShopTaxes, ~vendor, ~accountingConfiguration?, ())
    | AsyncDataDoneError => AsyncResult.done(Error())
    | _ =>
      state->AsyncResult.mapOk(data =>
        switch action {
        | AsyncDataDoneOk(_) | AsyncDataDoneError | AsyncDataLoading => data
        | SubmitButtonClicked => {...data, submission: Loading}
        | SubmissionSucceeded({allShopTaxes, vendor, accountingConfiguration}) => {
            ...initialStateDataFromOkResult(~allShopTaxes, ~vendor, ~accountingConfiguration, ()),
            submission: Done(Ok()),
          }
        | SubmissionFailed(message) => {...data, submission: Done(Error(message))}
        | NotificationBannerCloseButtonClicked => {...data, submission: NotAsked}
        | AutoCompleteWithStandardTaxesAccounts => {
            ...data,
            taxesAccountsFormArrayFieldset: data.taxesAccountsFormArrayFieldset->Array.map(
              taxAccountFormFieldset =>
                TaxAccountFormFieldset.fillWithStandard(
                  taxAccountFormFieldset,
                  ~vendor=data.vendor,
                ),
            ),
          }
        | VendorChanged(vendor) => {
            ...data,
            vendor,
            isaComptaAccountNumberFormField: switch vendor {
            | Excel => None
            | IsaCompta => Some(FormField.make(~value=""))
            },
            taxesAccountsFormArrayFieldset: data.taxesAccountsFormArrayFieldset->Array.map(
              taxAccount => {
                ...taxAccount,
                isaComptaCodeFormField: switch vendor {
                | Excel => None
                | IsaCompta => Some(FormField.make(~value=""))
                },
              },
            ),
            maybePaymentsAccountsFormArrayFieldset: data.maybePaymentsAccountsFormArrayFieldset->Option.map(
              paymentsAccountsFormArrayFieldset =>
                paymentsAccountsFormArrayFieldset->Array.map(
                  paymentAccountFormFieldset => {
                    ...paymentAccountFormFieldset,
                    isaComptaJournalCodeFormField: switch vendor {
                    | Excel => None
                    | IsaCompta => Some(FormField.make(~value=""))
                    },
                  },
                ),
            ),
          }
        | FiscalYearOpeningMonthChanged(value) => {
            ...data,
            fiscalYearOpeningMonth: value,
          }
        | BreakdownOfConsumerSalesByCashRegisterDailyReportChanged(value) => {
            ...data,
            breakdownOfConsumerSalesByCashRegisterDailyReport: value,
          }
        | IncludeCashFromIndividualsInPaymentJournalChanged(value) => {
            ...data,
            includeCashFromIndividualsInPaymentJournal: value,
          }

        | IsaComptaAccountNumberFormFieldChanged(value) => {
            ...data,
            isaComptaAccountNumberFormField: data.isaComptaAccountNumberFormField->Option.map(
              field => {...field, value},
            ),
          }
        | IsaComptaAccountNumberFormFieldBlured => {
            ...data,
            isaComptaAccountNumberFormField: data.isaComptaAccountNumberFormField->Option.map(
              field => {...field, touched: true},
            ),
          }
        | TaxAccountFormFieldChanged(id, field, value) => {
            ...data,
            taxesAccountsFormArrayFieldset: data.taxesAccountsFormArrayFieldset->Array.map(
              taxAccountFormFielset =>
                if TaxAccountFormFieldset.ID.equal(id, taxAccountFormFielset.id) {
                  taxAccountFormFielset->TaxAccountFormFieldset.changeFormFieldValue(~field, ~value)
                } else {
                  taxAccountFormFielset
                },
            ),
          }
        | TaxAccountFormFieldBlured(id, field) => {
            ...data,
            taxesAccountsFormArrayFieldset: data.taxesAccountsFormArrayFieldset->Array.map(
              taxAccountFormFielset =>
                if TaxAccountFormFieldset.ID.equal(id, taxAccountFormFielset.id) {
                  taxAccountFormFielset->TaxAccountFormFieldset.blurFormField(~field)
                } else {
                  taxAccountFormFielset
                },
            ),
          }
        | PaymentsAccountsFormArrayFieldsetToggled => {
            ...data,
            maybePaymentsAccountsFormArrayFieldset: switch data.maybePaymentsAccountsFormArrayFieldset {
            | Some(_) => None
            | None => Some([])
            },
          }
        | AutocompleteBasicPaymentsAccounts => {
            ...data,
            maybePaymentsAccountsFormArrayFieldset: {
              let {vendor} = data
              Some(
                AccountingConfiguration.autocompleteBasicPaymentsAccounts(
                  ~vendor,
                )->Array.map(paymentAccount =>
                  PaymentAccountFormFieldset.fromAccountingConfigurationPaymentAccount(
                    paymentAccount,
                    ~vendor,
                  )
                ),
              )
            },
          }
        | PaymentsAccountsFormFieldsetAdded => {
            ...data,
            maybePaymentsAccountsFormArrayFieldset: data.maybePaymentsAccountsFormArrayFieldset->Option.map(
              paymentsAccountsFormFieldset => {
                let nextPaymentMethods =
                  PaymentMethod.values->Array.keep(
                    value =>
                      paymentsAccountsFormFieldset
                      ->Array.getBy(({paymentMethod}) => PaymentMethod.equal(paymentMethod, value))
                      ->Option.isNone,
                  )
                switch nextPaymentMethods->Array.get(0) {
                | Some(nextPaymentMethod) =>
                  paymentsAccountsFormFieldset->Array.concat([
                    PaymentAccountFormFieldset.make(
                      ~paymentMethod=nextPaymentMethod,
                      ~vendor=data.vendor,
                    ),
                  ])
                | None => paymentsAccountsFormFieldset
                }
              },
            ),
          }
        | PaymentsAccountsFormFieldsetRemoved(paymentMethod) => {
            ...data,
            maybePaymentsAccountsFormArrayFieldset: data.maybePaymentsAccountsFormArrayFieldset->Option.map(
              paymentsAccountsFormFieldset =>
                paymentsAccountsFormFieldset->Array.keep(
                  payementAccountArrayFieldset =>
                    payementAccountArrayFieldset.paymentMethod !== paymentMethod,
                ),
            ),
          }
        | PaymentAccountFormFieldChanged(activePaymentMethod, field, value) => {
            ...data,
            maybePaymentsAccountsFormArrayFieldset: data.maybePaymentsAccountsFormArrayFieldset->Option.map(
              paymentsAccountsFormArrayFieldset =>
                paymentsAccountsFormArrayFieldset->Array.map(
                  paymentAccountFormFieldset => {
                    let {paymentMethod} = paymentAccountFormFieldset
                    if PaymentMethod.equal(paymentMethod, activePaymentMethod) {
                      paymentAccountFormFieldset->PaymentAccountFormFieldset.changeFormFieldValue(
                        ~field,
                        ~value,
                      )
                    } else {
                      paymentAccountFormFieldset
                    }
                  },
                ),
            ),
          }
        | PaymentAccountFormFieldBlured(activePaymentMethod, field) => {
            ...data,
            maybePaymentsAccountsFormArrayFieldset: data.maybePaymentsAccountsFormArrayFieldset->Option.map(
              paymentsAccountsFormArrayFieldset =>
                paymentsAccountsFormArrayFieldset->Array.map(
                  paymentAccountFormFieldset => {
                    let {paymentMethod} = paymentAccountFormFieldset
                    if PaymentMethod.equal(paymentMethod, activePaymentMethod) {
                      paymentAccountFormFieldset->PaymentAccountFormFieldset.blurFormField(~field)
                    } else {
                      paymentAccountFormFieldset
                    }
                  },
                ),
            ),
          }
        | PaymentAccountPaymentMethodChanged({previous, next: nextPaymentMethod}) => {
            ...data,
            maybePaymentsAccountsFormArrayFieldset: data.maybePaymentsAccountsFormArrayFieldset->Option.map(
              paymentsAccountsFormArrayFieldset =>
                paymentsAccountsFormArrayFieldset->Array.map(
                  paymentAccountFormFieldset =>
                    if PaymentMethod.equal(previous, paymentAccountFormFieldset.paymentMethod) {
                      PaymentAccountFormFieldset.make(
                        ~paymentMethod=nextPaymentMethod,
                        ~vendor=data.vendor,
                      )
                    } else {
                      paymentAccountFormFieldset
                    },
                ),
            ),
          }
        }
      )
    }->AsyncResult.mapOk(mapDataFormFieldValiation)
}

let getAllShopTaxesRequest = GetAllShopTaxesRequest.make
let createOrUpdateShopAccountingConfigurationRequest = CreateOrUpdateShopAccountingConfigurationRequest.make

let mapReducerValuesAndShopIdToAccountingConfiguration = (~shopId, ~values) => {
  let {
    Reducer.fiscalYearOpeningMonth: fiscalYearOpeningMonth,
    isaComptaAccountNumberFormField,
    taxesAccountsFormArrayFieldset,
    breakdownOfConsumerSalesByCashRegisterDailyReport,
    includeCashFromIndividualsInPaymentJournal,
    maybePaymentsAccountsFormArrayFieldset,
  } = values
  {
    AccountingConfiguration.shopId,
    breakdownOfConsumerSalesByCashRegisterDailyReport,
    includeCashFromIndividualsInPaymentJournal,
    fiscalYearOpeningMonth,
    isaComptaAccountNumber: isaComptaAccountNumberFormField->Option.map(({value}) => value),
    taxesAccounts: taxesAccountsFormArrayFieldset->Array.map(taxAccountFormFieldset => {
      AccountingConfiguration.TaxAccount.taxId: taxAccountFormFieldset.id->TaxAccountFormFieldset.ID.toString,
      deductibleTax: taxAccountFormFieldset.deductibleTaxFormFields->Option.map(({
        accountNumberFormField: {value: accountNumber},
        labelFormField: {value: label},
      }) => (accountNumber, label)),
      productsSold: switch taxAccountFormFieldset.productsSoldFormFields {
      | {accountNumberFormField: {value: accountNumber}, labelFormField: {value: label}} => (
          accountNumber,
          label,
        )
      },
      isaComptaCode: taxAccountFormFieldset.isaComptaCodeFormField->Option.map(({value}) => value),
    }),
    maybePaymentsAccounts: maybePaymentsAccountsFormArrayFieldset->Option.map(
      paymentsAccountsFormArrayFieldset =>
        paymentsAccountsFormArrayFieldset->Array.map(paymentAccountFormFieldset => {
          AccountingConfiguration.PaymentAccount.paymentMethod: paymentAccountFormFieldset.paymentMethod,
          isaComptaJournalCode: paymentAccountFormFieldset.isaComptaJournalCodeFormField->Option.map(
            ({value}) => value,
          ),
          accountLabel: paymentAccountFormFieldset.accountLabelFormField.value,
          accountNumber: paymentAccountFormFieldset.accountNumberFormField.value,
        }),
    ),
  }
}

@react.component
let make = (
  ~getAllShopTaxesRequest,
  ~getShopAccountingConfigurationRequest,
  ~createOrUpdateShopAccountingConfigurationRequest,
  ~configurationRoute,
  ~baseRoute,
  ~shopId,
  ~vendor,
) => {
  let apolloClient = ApolloClient.React.useApolloClient()
  let navigate = Navigation.useNavigate()

  let shops = Auth.useShops()
  let shop = shops->Array.getBy(shop => shop.id === shopId)->Option.getUnsafe

  let (state, dispatch) = React.useReducer(Reducer.make, Reducer.initialState())

  let submitting = Reducer.isSubmitting(state)
  let isPristine = Reducer.isPristine(state)
  let hasBeenSubmitted = Reducer.hasBeenSubmitted(state) && !isPristine

  let handleLoadAsyncData = (~shopId) => {
    dispatch(AsyncDataLoading)
    let futures = Future.all2((
      getAllShopTaxesRequest(~shopId, ~apolloClient),
      getShopAccountingConfigurationRequest(~shopId),
    ))
    futures->Future.get(((allShopTaxes, shopAccountingConfiguration)) =>
      switch (allShopTaxes, shopAccountingConfiguration) {
      | (_, Error()) => dispatch(AsyncDataDoneError)
      | (Ok(allShopTaxes), Ok(None)) => dispatch(AsyncDataDoneOk({allShopTaxes, vendor: Excel}))
      | (Ok(allShopTaxes), Ok(Some(accountingConfiguration))) =>
        dispatch(AsyncDataDoneOk({allShopTaxes, vendor, accountingConfiguration}))
      | _ => ()
      }
    )
    futures
  }

  React.useEffect0(() => {
    let future = handleLoadAsyncData(~shopId)
    Some(() => future->Future.cancel)
  })

  React.useEffect1(() => {
    if submitting {
      switch state {
      | Done(Ok(data)) if Reducer.isDataErrored(data) =>
        let message = "There are some errors in the form, please correct them before trying to send it again."
        dispatch(SubmissionFailed(t(message)))
      | Done(Ok(values)) =>
        Future.all2((
          getAllShopTaxesRequest(~shopId, ~apolloClient),
          createOrUpdateShopAccountingConfigurationRequest(
            ~getShopAccountingConfigurationRequest,
            mapReducerValuesAndShopIdToAccountingConfiguration(~values, ~shopId),
          ),
        ))->Future.get(result =>
          switch result {
          | (Ok(allShopTaxes), Ok(accountingConfiguration)) =>
            dispatch(SubmissionSucceeded({accountingConfiguration, vendor, allShopTaxes}))
          | (Error(_), _) | (_, Error(_)) =>
            let message = "An unexpected error occured. Please try again or contact the support."
            dispatch(SubmissionFailed(t(message)))
          }
        )
      | NotAsked | Loading | Done(Error(_)) | Reloading(Ok(_) | Error(_)) =>
        let message = "An unexpected error occured. Please try again or contact the support."
        dispatch(SubmissionFailed(t(message)))
      }
    }

    None
  }, [submitting])

  let activeVendor = switch state {
  | Done(Ok({vendor})) | Reloading(Ok({vendor})) => Some(vendor)
  | _ => None
  }

  React.useEffect1(() => {
    switch activeVendor {
    | Some(activeVendor) if !AccountingVendor.equal(vendor, activeVendor) =>
      navigate(configurationRoute(~shopId, ~vendor=activeVendor))
    | _ => ()
    }

    None
  }, [activeVendor])

  let titlePage = t("Setup my accounting accounts")
  let titleSubtitle = shop.name

  switch state {
  | Done(Ok(values)) | Reloading(Ok(values)) =>
    let handleCancel = _ => handleLoadAsyncData(~shopId)->ignore
    let handleSubmit = _ => dispatch(SubmitButtonClicked)

    let actionsBar =
      <ResourceDetailsPage.ActionsBar
        items=[
          isPristine
            ? <Button onPress={_ => navigate(baseRoute)} variation={#neutral} size=#medium>
                {t("Return to export interface")->React.string}
              </Button>
            : <Button onPress={_ => handleCancel()} variation={#neutral} size=#medium>
                {t("Cancel")->React.string}
              </Button>,
          <Button onPress={handleSubmit} loading=submitting variation={#success} size=#medium>
            {t("Save")->React.string}
          </Button>,
        ]
      />

    let onRequestCloseNotificationBanner = () => dispatch(NotificationBannerCloseButtonClicked)

    let handleAutocompleteStandardTaxesAccounts = () =>
      dispatch(AutoCompleteWithStandardTaxesAccounts)

    let notificationBanner = switch values.submission {
    | Done(Error(errorMessage)) =>
      <ResourceDetailsPage.NotificationBanner
        value=Error(errorMessage) onRequestClose=onRequestCloseNotificationBanner
      />
    | Done(Ok()) =>
      <ResourceDetailsPage.NotificationBanner
        value=Ok(t("The configuration has been successfully saved"))
        onRequestClose=onRequestCloseNotificationBanner
      />
    | NotAsked | Loading | Reloading(_) => React.null
    }

    <ResourceDetailsPage title=titlePage subtitle=titleSubtitle actionsBar notificationBanner>
      <Stack space=#large>
        <FieldsetLayoutPanel
          title={t("accounting_configuration.settings_general.title")}
          description={t("accounting_configuration.settings_general.description")}>
          <InputSelectField
            label={t("Format")}
            required=true
            onChange={value => dispatch(VendorChanged(value))}
            value={values.vendor}
            sections={[{items: AccountingVendor.values->Array.map(AccountingVendor.toSelectItem)}]}
          />
          <Group>
            {switch values.isaComptaAccountNumberFormField {
            | None => React.null
            | Some({value, errorMessage, touched}) =>
              <InputTextField
                label={t("IsaCompta file number")}
                value
                onChange={value => dispatch(IsaComptaAccountNumberFormFieldChanged(value))}
                errorMessage=?{touched || hasBeenSubmitted ? errorMessage : None}
                onBlur={() => dispatch(IsaComptaAccountNumberFormFieldBlured)}
              />
            }}
            <InputSelectField
              label={t("Fiscal year (per month of opening)")}
              required=true
              searchable=false
              onChange={value => dispatch(FiscalYearOpeningMonthChanged(value))}
              value={values.fiscalYearOpeningMonth}
              sections={[
                {
                  items: FiscalYearOpeningMonth.values->Array.map(
                    FiscalYearOpeningMonth.toSelectItem,
                  ),
                },
              ]}
            />
          </Group>
          <InputCheckboxField
            label={t("Breakdown of sales to consumers")}
            text={t("Display of daily cash register reports instead of receipts")}
            onChange={value =>
              dispatch(BreakdownOfConsumerSalesByCashRegisterDailyReportChanged(!value))}
            value={!values.breakdownOfConsumerSalesByCashRegisterDailyReport}
          />
        </FieldsetLayoutPanel>
        <FieldsetLayoutPanel
          title={t("accounting_configuration.setting_tax_rates.title")}
          description={t("accounting_configuration.setting_tax_rates.description")}>
          {if Option.isNone(values.initialAccountingConfiguration) {
            <Box spaceBottom={#medium}>
              <Stack align={#start} space={#normal}>
                <TextStyle size={#small}>
                  {t("accounting.configuration_page.help_with_accouting_standard")->React.string}
                </TextStyle>
                <Button
                  size={#small}
                  variation={#primary}
                  onPress={_ => handleAutocompleteStandardTaxesAccounts()}>
                  {t("Autocomplete with accounting standard")->React.string}
                </Button>
              </Stack>
              <Divider spaceY=#medium />
              <TextAction
                onPress={() => HelpCenter.showArticle("")}
                text={t("Learn more about accounting entries")}
              />
            </Box>
          } else {
            React.null
          }}
          <InputTextField label={t("Journal code")} required=true disabled=true value="VE" />
          <Divider />
          {values.taxesAccountsFormArrayFieldset
          ->Array.mapWithIndex((
            index,
            {id, label, isaComptaCodeFormField, deductibleTaxFormFields, productsSoldFormFields},
          ) =>
            <React.Fragment key={TaxAccountFormFieldset.ID.toString(id)}>
              <Stack space=#small>
                <Inline space={#xsmall}>
                  <TextStyle weight=#semibold> {(label ++ " ")->React.string} </TextStyle>
                  {if deductibleTaxFormFields->Option.isNone {
                    <Offset height=13. top={-1.}>
                      <TooltipIcon variation=#alert crossOffset={-2.}>
                        <Tooltip.Span text={t("Used for overseas sales")} />
                      </TooltipIcon>
                    </Offset>
                  } else {
                    React.null
                  }}
                </Inline>
                <Group>
                  {switch deductibleTaxFormFields {
                  | Some({accountNumberFormField: {value, errorMessage, touched}}) =>
                    <InputTextField
                      label={t("Tax account")}
                      required=true
                      value
                      errorMessage=?{touched || hasBeenSubmitted ? errorMessage : None}
                      onChange={value =>
                        dispatch(TaxAccountFormFieldChanged(id, DeductibleTaxAccountNumber, value))}
                      onBlur={() =>
                        dispatch(TaxAccountFormFieldBlured(id, DeductibleTaxAccountNumber))}
                    />
                  | None =>
                    <InputTextField
                      label={t("Tax account")}
                      placeholder={t("Non-deductible VAT")}
                      disabled=true
                      value=""
                    />
                  }}
                  {switch deductibleTaxFormFields {
                  | Some({labelFormField: {value, errorMessage, touched}}) =>
                    <InputTextField
                      label={t("Associated label")}
                      required=true
                      value
                      errorMessage=?{touched || hasBeenSubmitted ? errorMessage : None}
                      onChange={value =>
                        dispatch(TaxAccountFormFieldChanged(id, DeductibleTaxLabel, value))}
                      onBlur={() => dispatch(TaxAccountFormFieldBlured(id, DeductibleTaxLabel))}
                    />
                  | None =>
                    <InputTextField
                      label={t("Associated label")} disabled=true value="" onChange={value => ()}
                    />
                  }}
                  {switch productsSoldFormFields.accountNumberFormField {
                  | {value, touched, errorMessage} =>
                    <InputTextField
                      label={t("Goods account")}
                      required=true
                      value
                      errorMessage=?{touched || hasBeenSubmitted ? errorMessage : None}
                      onChange={value =>
                        dispatch(TaxAccountFormFieldChanged(id, ProductsSoldAccountNumber, value))}
                      onBlur={() =>
                        dispatch(TaxAccountFormFieldBlured(id, ProductsSoldAccountNumber))}
                    />
                  }}
                  {switch productsSoldFormFields.labelFormField {
                  | {value, touched, errorMessage} =>
                    <InputTextField
                      label={t("Associated label")}
                      required=true
                      value
                      errorMessage=?{touched || hasBeenSubmitted ? errorMessage : None}
                      onChange={value =>
                        dispatch(TaxAccountFormFieldChanged(id, ProductsSoldLabel, value))}
                      onBlur={() => dispatch(TaxAccountFormFieldBlured(id, ProductsSoldLabel))}
                    />
                  }}
                  {switch isaComptaCodeFormField {
                  | None => React.null
                  | Some({value, errorMessage, touched}) =>
                    <InputTextField
                      label={t("IsaCompta code")}
                      required=true
                      errorMessage=?{touched || hasBeenSubmitted ? errorMessage : None}
                      value
                      onChange={value =>
                        dispatch(TaxAccountFormFieldChanged(id, IsaComptaCode, value))}
                      onBlur={() => dispatch(TaxAccountFormFieldBlured(id, IsaComptaCode))}
                    />
                  }}
                </Group>
              </Stack>
              {index < values.taxesAccountsFormArrayFieldset->Array.size - 1
                ? <Divider />
                : React.null}
            </React.Fragment>
          )
          ->React.array}
        </FieldsetLayoutPanel>
        <FieldsetLayoutPanel
          title={t("accounting_configuration.setting_payments.title")}
          description={t("accounting_configuration.setting_payments.description")}>
          <InputCheckboxField
            label=""
            text={t("If checked, the payment journal will be exported.")}
            onChange={value => dispatch(PaymentsAccountsFormArrayFieldsetToggled)}
            value={values.maybePaymentsAccountsFormArrayFieldset->Option.isSome}
          />
          {switch values.maybePaymentsAccountsFormArrayFieldset {
          | Some(paymentsAccountsFormArrayFieldset) =>
            <>
              {if paymentsAccountsFormArrayFieldset->Array.size === 0 {
                <Box spaceBottom={#medium}>
                  <Stack align={#start} space={#normal}>
                    <TextStyle size={#small}>
                      {t(
                        "accounting.configuration_page.setting_payments.help_with_autocomplete",
                      )->React.string}
                    </TextStyle>
                    <Button
                      size={#small}
                      variation={#primary}
                      onPress={_ => dispatch(AutocompleteBasicPaymentsAccounts)}>
                      {t("Autocomplete")->React.string}
                    </Button>
                  </Stack>
                </Box>
              } else {
                React.null
              }}
              {if values.vendor->AccountingVendor.isExcel {
                <InputTextField label={t("Journal code")} required=true disabled=true value="RE" />
              } else {
                React.null
              }}
              <Divider />
              {paymentsAccountsFormArrayFieldset
              ->Array.mapWithIndex((
                index,
                {
                  paymentMethod,
                  isaComptaJournalCodeFormField,
                  accountNumberFormField,
                  accountLabelFormField,
                },
              ) =>
                <React.Fragment key={paymentMethod->PaymentMethod.toLabel ++ index->Int.toString}>
                  <Group
                    grid={values.vendor->AccountingVendor.isExcel
                      ? ["28%", "28%", "28%", "auto"]
                      : ["22%", "22%", "22%", "22%", "auto"]}>
                    <InputSelectField
                      label={t("Payment method")}
                      value={paymentMethod}
                      onChange={value =>
                        dispatch(
                          PaymentAccountPaymentMethodChanged({
                            previous: paymentMethod,
                            next: value,
                          }),
                        )}
                      sections=[
                        {
                          title: "",
                          items: PaymentMethod.values
                          ->Array.keep(value =>
                            PaymentMethod.equal(paymentMethod, value) ||
                            paymentsAccountsFormArrayFieldset
                            ->Array.getBy(
                              ({paymentMethod}) => PaymentMethod.equal(paymentMethod, value),
                            )
                            ->Option.isNone
                          )
                          ->Array.map(value => {
                            Select.key: value->PaymentMethod.toLabel,
                            value,
                            label: value->PaymentMethod.toLabel,
                          }),
                        },
                      ]
                    />
                    {switch accountNumberFormField {
                    | {value, touched, errorMessage} =>
                      <InputTextField
                        label={t("Accounting account")}
                        required=true
                        tooltip=?{switch (
                          paymentMethod,
                          values.includeCashFromIndividualsInPaymentJournal,
                        ) {
                        | (Cash, true) =>
                          Some(
                            <Tooltip.Span
                              text={t(
                                "accounting_configuration.setting_payments.cash_accounting_account_tooltip",
                              )}
                            />,
                          )
                        | _ => None
                        }}
                        errorMessage=?{touched || hasBeenSubmitted ? errorMessage : None}
                        value
                        onChange={value =>
                          dispatch(
                            PaymentAccountFormFieldChanged(paymentMethod, AccountNumber, value),
                          )}
                        onBlur={() =>
                          dispatch(PaymentAccountFormFieldBlured(paymentMethod, AccountNumber))}
                      />
                    }}
                    {switch accountLabelFormField {
                    | {value, touched, errorMessage} =>
                      <InputTextField
                        label={t("Associated label")}
                        required=true
                        errorMessage=?{touched || hasBeenSubmitted ? errorMessage : None}
                        value
                        onChange={value =>
                          dispatch(
                            PaymentAccountFormFieldChanged(paymentMethod, AccountLabel, value),
                          )}
                        onBlur={() =>
                          dispatch(PaymentAccountFormFieldBlured(paymentMethod, AccountLabel))}
                      />
                    }}
                    {switch isaComptaJournalCodeFormField {
                    | None => React.null
                    | Some({value, errorMessage, touched}) =>
                      <InputTextField
                        label={t("IsaCompta journal code")}
                        required=true
                        errorMessage=?{touched || hasBeenSubmitted ? errorMessage : None}
                        tooltip=?{switch (
                          paymentMethod,
                          values.includeCashFromIndividualsInPaymentJournal,
                        ) {
                        | (Cash, true) =>
                          Some(
                            <Tooltip.Span
                              text={t(
                                "accounting_configuration.setting_payments.isacompta_journal_code_tooltip",
                              )}
                            />,
                          )
                        | _ => None
                        }}
                        value
                        onChange={value =>
                          dispatch(
                            PaymentAccountFormFieldChanged(
                              paymentMethod,
                              IsaComptaJournalCode,
                              value,
                            ),
                          )}
                        onBlur={() =>
                          dispatch(
                            PaymentAccountFormFieldBlured(paymentMethod, IsaComptaJournalCode),
                          )}
                      />
                    }}
                    <Box spaceTop={#medium}>
                      <RoundButton
                        icon=#delete_light
                        onPress={_ => dispatch(PaymentsAccountsFormFieldsetRemoved(paymentMethod))}
                      />
                    </Box>
                  </Group>
                  {index < paymentsAccountsFormArrayFieldset->Array.size - 1
                    ? <Divider />
                    : React.null}
                </React.Fragment>
              )
              ->React.array}
              <Box spaceTop=#small>
                <TextIconButton
                  icon=#plus_light
                  disabled={PaymentMethod.values
                  ->Array.keep(value =>
                    paymentsAccountsFormArrayFieldset
                    ->Array.getBy(({paymentMethod}) => PaymentMethod.equal(paymentMethod, value))
                    ->Option.isNone
                  )
                  ->Array.length === 0}
                  onPress={_ => dispatch(PaymentsAccountsFormFieldsetAdded)}>
                  {t("Add")->React.string}
                </TextIconButton>
              </Box>
            </>
          | None => React.null
          }}
        </FieldsetLayoutPanel>
        <FieldsetLayoutPanel
          title={t("accounting_configuration.setting_cashflow.title")}
          description={t("accounting_configuration.setting_cashflow.description")}>
          <InputCheckboxField
            label={t("Cash payments in payments journal")}
            text={t(
              "If this option is checked, cash payments will be recorded in the cash journal.\n Otherwise it will be recorded in the payments journal.",
            )}
            onChange={value => dispatch(IncludeCashFromIndividualsInPaymentJournalChanged(!value))}
            value={!values.includeCashFromIndividualsInPaymentJournal}
          />
          <Divider />
          <InputTextField
            label={t("Journal code")}
            tooltip={<Tooltip.Span
              text={t("accounting_configuration.setting_cashflow.journal_code.tooltip")}
            />}
            required=true
            disabled=true
            value="Cx"
          />
          <Divider />
          <Group>
            <InputTextField
              label={t("Cash flow accounting account")} required=true disabled=true value="********"
            />
            <InputTextField
              label={t("Cash flow label account")} required=true disabled=true value="Caisse"
            />
          </Group>
          <Group>
            <InputTextField
              label={t("Internal transfer account")}
              tooltip={<Tooltip.Span
                text={t(
                  "accounting_configuration.setting_cashflow.internal_transfer_account.tooltip",
                )}
              />}
              required=true
              disabled=true
              value="********"
            />
            <InputTextField
              label={t("Internal transfer label account")}
              required=true
              disabled=true
              value="Virements internes"
            />
          </Group>
          <Group>
            <InputTextField
              label={t("Negative cash difference account")}
              required=true
              disabled=true
              value="********"
            />
            <InputTextField
              label={t("Negative cash difference label account")}
              required=true
              disabled=true
              value="Charges div-gestion courante"
            />
          </Group>
          <Group>
            <InputTextField
              label={t("Positive cash difference account")}
              required=true
              disabled=true
              value="********"
            />
            <InputTextField
              label={t("Positive cash difference label account")}
              required=true
              disabled=true
              value="Produits div-gestion courante"
            />
          </Group>
        </FieldsetLayoutPanel>
      </Stack>
    </ResourceDetailsPage>
  | Done(Error()) =>
    <ResourceDetailsPage title=titlePage subtitle=titleSubtitle>
      <Placeholder status=Error />
    </ResourceDetailsPage>
  | NotAsked | Loading | Reloading(Error()) =>
    <ResourceDetailsPage title=titlePage subtitle=titleSubtitle>
      <Placeholder status=Loading />
    </ResourceDetailsPage>
  }
}
