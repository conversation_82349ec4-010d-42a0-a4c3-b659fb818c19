open Intl
open StyleX

@module("./accounting_figure.png") external figureUri: string = "default"

module DateRangePreset = {
  type t =
    | Yesterday
    | LastWeek
    | LastMonth
    | CurrentFiscalYear({fiscalYearOpeningMonth: FiscalYearOpeningMonth.t})
    | LastFiscalYear({fiscalYearOpeningMonth: FiscalYearOpeningMonth.t})
    | Custom((option<Js.Date.t>, option<Js.Date.t>))
    | NotDefined

  let isEqual = (a, b) =>
    switch (a, b) {
    | (Yesterday, Yesterday)
    | (LastWeek, LastWeek)
    | (LastMonth, LastMonth)
    | (Custom((_, _)), Custom((_, _)))
    | (NotDefined, NotDefined) => true
    | (
      CurrentFiscalYear({fiscalYearOpeningMonth: fiscalYearOpeningMonthA}),
      CurrentFiscalYear({fiscalYearOpeningMonth: fiscalYearOpeningMonthB}),
    )
    | (
      LastFiscalYear({fiscalYearOpeningMonth: fiscalYearOpeningMonthA}),
      LastFiscalYear({fiscalYearOpeningMonth: fiscalYearOpeningMonthB}),
    ) =>
      FiscalYearOpeningMonth.isEqual(fiscalYearOpeningMonthA, fiscalYearOpeningMonthB)
    | _ => false
    }

  let isCustomOrNotDefined = value =>
    switch value {
    | Custom(_) | NotDefined => true
    | _ => false
    }

  let toString = (~notDefinedPlaceholder, preset) => {
    let divider = ` → `
    switch preset {
    | Custom((Some(startDate), None)) =>
      startDate->Intl.dateTimeFormat(~dateStyle=#medium) ++ divider ++ ` …`
    | Custom((Some(startDate), Some(endDate))) if DateHelpers.isSameDay(startDate, endDate) =>
      startDate->Intl.dateTimeFormat(~dateStyle=#medium)
    | Custom((Some(startDate), Some(endDate))) =>
      startDate->Intl.dateTimeFormat(~dateStyle=#medium) ++
      divider ++
      endDate->Intl.dateTimeFormat(~dateStyle=#medium)
    | NotDefined => notDefinedPlaceholder
    | Yesterday => t("Yesterday")
    | LastWeek => t("Last week")
    | LastMonth => t("Last month")
    | CurrentFiscalYear(_) => t("Current fiscal year")
    | LastFiscalYear(_) => t("Last fiscal year")
    | Custom(_) => t("Custom")
    }
  }

  let getCurrentFiscalYearStart = (~now, ~fiscalYearOpeningMonth) => {
    let fiscalOpeningMonth =
      fiscalYearOpeningMonth->FiscalYearOpeningMonth.toDateMonthJsIndex->Int.toFloat

    if Js.Date.getMonth(now) < fiscalOpeningMonth {
      Js.Date.getFullYear(now) -. 1.
    } else {
      Js.Date.getFullYear(now)
    }
  }

  let toDateRange = preset => {
    let {
      startOfDay,
      endOfDay,
      subDays,
      startOfWeek,
      subWeeks,
      endOfWeek,
      startOfMonth,
      subMonths,
      endOfMonth,
    } = module(DateHelpers)
    let now = Js.Date.fromFloat(Js.Date.now())
    switch preset {
    | Yesterday => Some((now->startOfDay->subDays(1.), now->subDays(1.)->endOfDay))
    | LastWeek => Some((now->subWeeks(1.)->startOfWeek, now->subWeeks(1.)->endOfWeek))
    | LastMonth => Some((now->subMonths(1.)->startOfMonth, now->subMonths(1.)->endOfMonth))
    | CurrentFiscalYear({fiscalYearOpeningMonth}) =>
      let startDate =
        Js.Date.makeWithYMD(
          ~year=getCurrentFiscalYearStart(~now, ~fiscalYearOpeningMonth),
          ~month=fiscalYearOpeningMonth->FiscalYearOpeningMonth.toDateMonthJsIndex->Int.toFloat,
          ~date=1.,
          (),
        )->DateHelpers.startOfDay
      let endDate = startDate->DateHelpers.addMonths(11.)->DateHelpers.endOfMonth
      Some((startDate, endDate))
    | LastFiscalYear({fiscalYearOpeningMonth}) =>
      let startDate = Js.Date.makeWithYMD(
        ~year=getCurrentFiscalYearStart(~now, ~fiscalYearOpeningMonth) -. 1.,
        ~month=fiscalYearOpeningMonth->FiscalYearOpeningMonth.toDateMonthJsIndex->Int.toFloat,
        ~date=1.,
        (),
      )
      let endDate = startDate->DateHelpers.addMonths(11.)->DateHelpers.endOfMonth
      Some((startDate, endDate))
    | Custom((Some(startDate), Some(endDate))) => Some((startDate, endDate))
    | Custom(_) | NotDefined => None
    }
  }

  let fromPartialDateRange = (range, presets) => {
    switch range {
    | (Some(startDateA), Some(endDateA)) =>
      let preset = presets->Array.getBy(preset =>
        switch preset->toDateRange {
        | Some(startDateB, endDateB)
          if DateHelpers.isSameDay(startDateA, startDateB) &&
          DateHelpers.isSameDay(endDateA, endDateB) => true
        | _ => false
        }
      )
      preset->Option.getWithDefault(Custom(range))
    | (Some(_), None) => Custom(range)
    | (None, Some(_)) | (None, None) => NotDefined
    }
  }
}

module PageLayout = {
  @react.component
  let make = (~title, ~children) => {
    <Page title variation=#compact>
      <Box spaceTop={#large} grow=true>
        <Card grow=true centerContent=true>
          <Stack space=#small align={#center}> {children} </Stack>
        </Card>
      </Box>
    </Page>
  }
}

type failure =
  | MissingConfigurationForIsacomptaVendorFailure
  | NotFoundAccountingConfigurationFailure
  | InvalidSaleTaxFailure({name: string, issueDate: Js.Date.t})
  | InvalidFiscalExportDateRangeFailure
  | MissingPaymentMethodAccountFailure(PaymentMethod.t)
  | UnknownFailure

module GetShopAccountingExportRequest = {
  let endpoint = (~vendor, ~shopId) => {
    let baseUrl = Env.sheetUrl() ++ "/accounting-export/" ++ shopId
    switch vendor {
    | AccountingVendor.Excel => baseUrl ++ "/xlsx-format"
    | IsaCompta => baseUrl ++ "/ecr-format"
    }
  }

  let encodeBodyJson = (~shopId, ~dateRange) =>
    Js.Dict.fromArray([
      ("shopId", Json.encodeString(shopId)),
      ("startDate", Json.encodeNumber(Js.Date.getTime(fst(dateRange)))),
      ("endDate", Json.encodeNumber(Js.Date.getTime(snd(dateRange)))),
      ("timeZone", Json.encodeString(Intl.timeZone)),
      ("accountingEntriesView", Json.encodeString("sales")),
    ])->Json.encodeDict

  let decodeInvalidRequestFailure = ({Request.kind: kind, data}) =>
    switch kind {
    | "NotFoundAccountingExportConfiguration" => NotFoundAccountingConfigurationFailure
    | "IsaComptaTaxCodeNotDefined" | "IsaComptaAccountNumberNotDefined" =>
      MissingConfigurationForIsacomptaVendorFailure
    | "InvalidFiscalExportDateRange" => InvalidFiscalExportDateRangeFailure
    | "InvalidInvoiceTax" | "InvalidCreditNoteTax" | "InvalidRefundReceiptTax" =>
      let dict = data->Option.flatMap(Json.decodeDict)
      let name = dict->Json.flatDecodeDictFieldString("name")
      let issueDate = dict->Json.flatDecodeDictFieldFloat("date")->Option.map(Js.Date.fromFloat)
      switch (name, issueDate) {
      | (Some(name), Some(issueDate)) => InvalidSaleTaxFailure({name, issueDate})
      | (None, _) | (_, None) => UnknownFailure
      }
    | "AccountingPaymentExportItemMissingPaymentMethodConfiguration" =>
      let dict = data->Option.flatMap(Json.decodeDict)
      switch dict->Json.flatDecodeDictField("paymentMethod", PaymentMethod.decodeFromJson) {
      | Some(paymentMethod) => MissingPaymentMethodAccountFailure(paymentMethod)
      | None => UnknownFailure
      }
    | _ => UnknownFailure
    }

  let decodeRequestError = error =>
    switch error {
    | Request.UnexpectedServerError
    | ClientError(_)
    | ServerError(_)
    | MalformedResponse
    | JwtAuthenticationRedirection =>
      UnknownFailure
    | InvalidRequestFailures(invalidRequestFailures) =>
      invalidRequestFailures
      ->Array.get(0)
      ->Option.map(decodeInvalidRequestFailure)
      ->Option.getWithDefault(UnknownFailure)
    }

  let decodeResponse = result =>
    result->Json.decodeDict->Json.flatDecodeDictFieldString("url")->Option.map(Url.make)

  let make = (~shopId, ~dateRange, ~vendor) =>
    Request.make(
      endpoint(~shopId, ~vendor),
      ~method=#POST,
      ~bodyJson=encodeBodyJson(~dateRange, ~shopId),
    )
    ->Future.mapError(decodeRequestError)
    ->Future.flatMapOk(result =>
      Future.value(
        switch decodeResponse(result) {
        | Some(url) => Ok(url)
        | None => Error(UnknownFailure)
        },
      )
    )
}

let styles = StyleX.create({
  "img": style(~height="auto", ~maxWidth="300px", ~marginTop="-50px", ~marginBottom="12px", ()),
  "responsiveImg": {
    "@media screen and (min-height: 900px)": style(~maxWidth="375px", ~marginBottom="18px", ()),
  },
  "content": style(~paddingTop="10px", ()),
})

module State = {
  type t = {
    submission: AsyncResult.t<unit, failure>,
    shop: Auth.shop,
    dateRange: (Js.Date.t, Js.Date.t),
    fiscalYearOpeningMonth: FiscalYearOpeningMonth.t,
    vendor: AccountingVendor.t,
  }
  let now = Js.Date.make()
  let startPreviousMonth = now->DateHelpers.subMonths(1.)->DateHelpers.startOfMonth
  let endPreviousMonth = now->DateHelpers.subMonths(1.)->DateHelpers.endOfMonth

  let make = (~shop, ~vendor, ~fiscalYearOpeningMonth) => {
    submission: NotAsked,
    vendor,
    shop,
    fiscalYearOpeningMonth,
    dateRange: (startPreviousMonth, endPreviousMonth),
  }
}

@react.component
let make = (~configurationRoute, ~getShopAccountingConfigurationRequest) => {
  let shops = Auth.useShops()
  let shop = Auth.useActiveShop()->Option.getWithDefault(shops->Array.getUnsafe(0))
  let captureEvent = SessionTracker.useCaptureEvent()

  let (asyncState, setAsyncState) = React.useState(() => AsyncData.notAsked())

  let navigate = Navigation.useNavigate()

  React.useEffect0(() => {
    setAsyncState(prevState => prevState->AsyncData.toBusy)
    let future = getShopAccountingConfigurationRequest(~shopId=shop.id)
    future->Future.get(shopAccountingConfiguration =>
      switch shopAccountingConfiguration {
      | Error() => setAsyncState(_ => Done(Error()))
      | Ok(None) =>
        setAsyncState(
          _ => Done(Ok(State.make(~shop, ~vendor=Excel, ~fiscalYearOpeningMonth=January))),
        )
      | Ok(Some({AccountingConfiguration.isaComptaAccountNumber: None, fiscalYearOpeningMonth})) =>
        setAsyncState(_ => Done(Ok(State.make(~shop, ~vendor=Excel, ~fiscalYearOpeningMonth))))
      | Ok(Some({
          AccountingConfiguration.isaComptaAccountNumber: Some(_),
          fiscalYearOpeningMonth,
        })) =>
        setAsyncState(_ => Done(Ok(State.make(~shop, ~vendor=IsaCompta, ~fiscalYearOpeningMonth))))
      }
    )
    Some(() => future->Future.cancel)
  })

  let pageTitle = {t("Accounting entries")}

  switch asyncState {
  | NotAsked | Loading | Reloading(Ok(_) | Error(_)) =>
    <ResourceDetailsPage title=pageTitle variation={#compact}>
      <Placeholder status=Loading />
    </ResourceDetailsPage>
  | Done(Error()) =>
    <ResourceDetailsPage title=pageTitle variation={#compact}>
      <Placeholder status=Error />
    </ResourceDetailsPage>
  | Done(Ok(state)) =>
    let setState = fn => setAsyncState(prevState => prevState->AsyncResult.mapOk(fn))

    let handleSubmit = _ => {
      setState(prevState => {...prevState, State.submission: Loading})
      switch state.vendor {
      | Excel => captureEvent(#download_accounting_excel_export_file)
      | IsaCompta => captureEvent(#download_accounting_isacompta_export_file)
      }
      GetShopAccountingExportRequest.make(
        ~dateRange=state.dateRange,
        ~shopId=state.shop.id,
        ~vendor=state.vendor,
      )
      ->Future.flatMapOk(url => TriggerDownload.fromUrl(url)->Future.mapError(_ => UnknownFailure))
      ->Future.get(result => setState(prevState => {...prevState, submission: Done(result)}))
    }

    let handleCloseBanner = () => setState(prevState => {...prevState, submission: NotAsked})

    // TODO — Spreading props with ReScript v11
    let {style: ?contentStyle, className: ?contentClassName} = StyleX.props([styles["content"]])
    let {style: ?imgStyle, className: ?imgClassName} = StyleX.props([
      styles["img"],
      atRulesStyle(styles["responsiveImg"]),
    ])

    <PageLayout title=pageTitle>
      <img src={figureUri} style=?imgStyle className=?imgClassName />
      <Title align=#center level=#2> {t("Export my accounting entries")->React.string} </Title>
      <div style=?{contentStyle} className=?{contentClassName}>
        {switch state.submission {
        | Done(Ok(_)) =>
          let message = t("Your accounting entries have been successfully exported.")
          <Box spaceBottom={#medium}>
            <Banner onRequestClose=handleCloseBanner textStatus={Success(message)} />
          </Box>
        | Done(Error(failure)) =>
          <Box spaceBottom={#medium}>
            <Banner
              onRequestClose=handleCloseBanner
              textStatus={Danger(
                switch failure {
                | UnknownFailure =>
                  t("An unexpected error occured. Please try again or contact the support.")
                | NotFoundAccountingConfigurationFailure =>
                  t("accounting.not_found_accounting_configuration_failure")
                | MissingConfigurationForIsacomptaVendorFailure =>
                  t("accounting.missing_configuration_for_isacompta_vendor_failure")
                | InvalidFiscalExportDateRangeFailure =>
                  t(
                    "The selected date range exceeds the boundaries of the fiscal year. Please ensure your start and end dates fall within a fiscal year.",
                  )
                | InvalidSaleTaxFailure({name, issueDate}) =>
                  template(
                    t("accounting.invalid_sale_tax_failure{{name}}{{issueDate}}"),
                    ~values={
                      "name": name,
                      "issueDate": Intl.dateTimeFormat(issueDate, ~dateStyle=#short),
                    },
                    (),
                  )
                | MissingPaymentMethodAccountFailure(paymentMethod) =>
                  template(
                    t("accounting.missing_payment_method_account_failure{{paymentMethod}}"),
                    ~values={
                      "paymentMethod": paymentMethod->PaymentMethod.toLabel->Js.String.toLowerCase,
                    },
                    (),
                  )
                },
              )}
            />
          </Box>
        | NotAsked | Loading | Reloading(Ok(_) | Error(_)) => React.null
        }}
        <Inline align={#center} space=#normal>
          {if shops->Array.size > 1 {
            <Auth.SelectSingleShopFilter
              value=state.shop onChange={shop => setState(prevState => {...prevState, shop})}
            />
          } else {
            React.null
          }}
          <Select
            preset={#filter}
            label={t("Format")}
            sections=[{items: AccountingVendor.values->Array.map(AccountingVendor.toSelectItem)}]
            value=state.vendor
            onChange={value => setState(prevState => {...prevState, vendor: value})}
          />
          {switch state {
          | {fiscalYearOpeningMonth} =>
            <SelectDateRangeFilter
              label={t("Period")}
              placeholder={t("Select a period")}
              preset=module(DateRangePreset)
              presets=[
                DateRangePreset.Yesterday,
                LastWeek,
                LastMonth,
                CurrentFiscalYear({fiscalYearOpeningMonth: fiscalYearOpeningMonth}),
                LastFiscalYear({fiscalYearOpeningMonth: fiscalYearOpeningMonth}),
              ]
              disabledResetButton=true
              value=state.dateRange
              onChange={dateRange =>
                dateRange->Option.forEach(value =>
                  setState(prevState => {...prevState, dateRange: value})
                )}
              triggerLabelDisplay=#showDateRange
            />
          }}
          <Button
            betaBadge=true loading={state.submission->AsyncData.isBusy} onPress={handleSubmit}>
            {t("Export")->React.string}
          </Button>
        </Inline>
        <Box spaceTop=#medium>
          <Stack space=#small align=#center>
            <TextStyle align=#center> {t("accounting_main.description")->React.string} </TextStyle>
            <TextIconButton
              icon=#edit_light
              onPress={_ =>
                navigate(configurationRoute(~shopId=state.shop.id, ~vendor=state.vendor))}>
              {t("Setup my accounting accounts")->React.string}
            </TextIconButton>
          </Stack>
        </Box>
        <Divider spaceTop=#large spaceBottom=#xlarge />
        <Stack align=#center>
          <TextAction
            onPress={() => HelpCenter.showArticle(HelpCenter.setupAndExportAccountingEntries)}
            text={t("Learn more about accounting entries")}
          />
        </Stack>
      </div>
    </PageLayout>
  }
}
