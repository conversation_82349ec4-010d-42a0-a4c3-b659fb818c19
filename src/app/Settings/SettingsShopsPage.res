open Intl

module UpdateShopRequest = {
  let encodeBody = (
    ~id,
    ~name,
    ~address,
    ~postalCode,
    ~city,
    ~cityOfRegistryOffice,
    ~country,
    ~phoneNumber,
    ~email,
    ~website,
    ~fiscalYearEndClosingMonth,
    ~corporateName,
    ~legalRepresentative,
    ~legalForm,
    ~amountOfShareCapital,
    ~tvaNumber,
    ~siretNumber,
    ~rcsNumber,
    ~apeNafCode,
    ~bankName,
    ~bankCode,
    ~bankAccountHolder,
    ~bankAccountNumber,
    ~bicCode,
    ~ibanNumber,
  ) =>
    Js.Dict.fromArray([
      ("id", id->Json.encodeString),
      ("name", name->Json.encodeString),
      ("address", address->Json.encodeString),
      ("postalCode", postalCode->Json.encodeString),
      ("city", city->Json.encodeString),
      ("cityOfRegistryOffice", cityOfRegistryOffice->Json.encodeString),
      ("country", country->Json.encodeString),
      ("phoneNumber", phoneNumber->Json.encodeString),
      ("email", email->Json.encodeString),
      ("website", website->Json.encodeString),
      ("fiscalYearEndClosingMonth", fiscalYearEndClosingMonth->Json.encodeString),
      ("corporateName", corporateName->Json.encodeString),
      ("legalRepresentative", legalRepresentative->Json.encodeString),
      ("legalForm", legalForm->Json.encodeString),
      ("amountOfShareCapital", amountOfShareCapital->Json.encodeString),
      ("tvaNumber", tvaNumber->Json.encodeString),
      ("siretNumber", siretNumber->Json.encodeString),
      ("rcsNumber", rcsNumber->Json.encodeString),
      ("apeNafCode", apeNafCode->Json.encodeString),
      ("bankName", bankName->Json.encodeString),
      ("bankCode", bankCode->Json.encodeString),
      ("bankAccountHolder", bankAccountHolder->Json.encodeString),
      ("bankAccountNumber", bankAccountNumber->Json.encodeString),
      ("bicCode", bicCode->Json.encodeString),
      ("ibanNumber", ibanNumber->Json.encodeString),
    ])->Json.encodeDict

  type serverFailures =
    | ShopArchivedFailure
    | AuthUserFromHttpRequestFailureCase
    | CheckUserPermissionsFailureCase
    | NotFoundShopFailure
    | UnknownFailure

  let decodeInvalidRequestFailure = serverFailure =>
    switch serverFailure {
    | {Request.kind: "AuthUserFromHttpRequestFailureCase"} => AuthUserFromHttpRequestFailureCase
    | {kind: "CheckUserPermissionsFailureCase"} => CheckUserPermissionsFailureCase
    | {kind: "NotFoundShopFailure"} => NotFoundShopFailure
    | {kind: "ShopArchivedFailure"} => ShopArchivedFailure
    | _ => UnknownFailure
    }

  let endpoint = Env.gatewayUrl() ++ "/shop"

  let make = (
    ~id,
    ~name,
    ~address,
    ~postalCode,
    ~city,
    ~cityOfRegistryOffice,
    ~country,
    ~phoneNumber,
    ~email,
    ~website,
    ~fiscalYearEndClosingMonth,
    ~corporateName,
    ~legalRepresentative,
    ~legalForm,
    ~amountOfShareCapital,
    ~tvaNumber,
    ~siretNumber,
    ~rcsNumber,
    ~apeNafCode,
    ~bankName,
    ~bankCode,
    ~bankAccountHolder,
    ~bankAccountNumber,
    ~bicCode,
    ~ibanNumber,
  ) =>
    Request.make(
      endpoint,
      ~method=#PATCH,
      ~bodyJson=encodeBody(
        ~id,
        ~name,
        ~address,
        ~postalCode,
        ~city,
        ~cityOfRegistryOffice,
        ~country,
        ~phoneNumber,
        ~email,
        ~website,
        ~fiscalYearEndClosingMonth,
        ~corporateName,
        ~legalRepresentative,
        ~legalForm,
        ~amountOfShareCapital,
        ~tvaNumber,
        ~siretNumber,
        ~rcsNumber,
        ~apeNafCode,
        ~bankName,
        ~bankCode,
        ~bankAccountHolder,
        ~bankAccountNumber,
        ~bicCode,
        ~ibanNumber,
      ),
    )->Future.mapError(error =>
      switch error {
      | InvalidRequestFailures(invalidRequestFailures) =>
        invalidRequestFailures[0]->Option.map(decodeInvalidRequestFailure)
      | _ => None
      }
    )
}

module ShopEditFormLenses = %lenses(
  type state = {
    name: string,
    email: string,
    address: string,
    postalCode: string,
    city: string,
    cityOfRegistryOffice: string,
    country: string,
    phoneNumber: string,
    website: string,
    fiscalYearEndClosingMonth: string,
    corporateName: string,
    legalRepresentative: string,
    legalForm: string,
    amountOfShareCapital: string,
    tvaNumber: string,
    siretNumber: string,
    rcsNumber: string,
    apeNafCode: string,
    bankName: string,
    bankCode: string,
    bankAccountHolder: string,
    bankAccountNumber: string,
    bicCode: string,
    ibanNumber: string,
  }
)

module ShopEditForm = Form.Make(ShopEditFormLenses)

module ShopEditFormBankingInformationFieldset = {
  open ShopEditForm

  @react.component
  let make = () =>
    <FieldsetLayoutPanel
      title={t("Banking information")} description={t("Complete your banking information.")}>
      <Group>
        <InputText field=BankName label={t("Bank name")} />
        <InputText field=BankCode label={t("Bank code")} />
      </Group>
      <Group>
        <InputText field=BankAccountHolder label={t("Bank account holder")} />
        <InputText field=BankAccountNumber label={t("Bank account number")} />
      </Group>
      <Group>
        <InputText field=BicCode label={t("BIC code")} />
        <InputText field=IbanNumber label={t("IBAN number")} />
      </Group>
    </FieldsetLayoutPanel>

  let make = React.memo(make)
}

module ShopEditFormLegalInformationFieldset = {
  open ShopEditForm

  @react.component
  let make = () =>
    <FieldsetLayoutPanel
      title={t("Legal information")}
      description={t("Complete your legal information. It must be displayed on your documents.")}>
      <Group>
        <InputText field=CorporateName label={t("Corporate name")} />
        <InputText field=LegalForm label={t("Legal form")} />
      </Group>
      <InputText field=LegalRepresentative label={t("Legal representative")} />
      <InputText field=AmountOfShareCapital label={t("Amount of share capital")} />
      <Group>
        <InputText field=CityOfRegistryOffice label={t("City of registry office")} />
        <InputText field=RcsNumber label={t("Company Registration Number")} />
      </Group>
      <Group>
        <InputText field=SiretNumber label={t("SIRET number")} />
        <InputText field=ApeNafCode label={t("APE/NAF code")} />
      </Group>
      <InputText field=TvaNumber label={t("VAT number")} />
    </FieldsetLayoutPanel>

  let make = React.memo(make)
}

module ShopEditFormAddressFieldset = {
  open ShopEditForm

  @react.component
  let make = () => {
    let dispatch = useFormDispatch()
    let {values} = useFormState()

    let onChangeAddress = value => dispatch(FieldValueChanged(Address, _ => value))
    let onRequestAutoComplete = (address: AddressComboBoxField.address) => {
      dispatch(FieldValueChanged(Address, _ => address.name))
      dispatch(FieldValueChanged(PostalCode, _ => address.postcode))
      dispatch(FieldValueChanged(City, _ => address.city))
      dispatch(FieldValueChanged(Country, _ => address.country))
    }

    <FieldsetLayoutPanel
      title={t("Address")}
      description={t(
        "Provide your address. It will appear on the documents issued on app.wino.fr.",
      )}>
      <AddressComboBoxField
        addressName=values.address onInputChange=onChangeAddress onRequestAutoComplete
      />
      <Group>
        <InputText field=PostalCode label={t("Postal code")} />
        <InputText field=City label={t("City")} />
      </Group>
      <InputCountrySelect field=Country />
    </FieldsetLayoutPanel>
  }

  let make = React.memo(make)
}

module ShopEditFormGeneralInformationFieldset = {
  open ShopEditForm

  @react.component
  let make = () =>
    <FieldsetLayoutPanel
      title={t("General information")}
      description={t("Complete your store name and contact information.")}>
      <InputText field=Name label={t("Shop name")} />
      <InputText field=Email label={t("Email")} />
      <InputText field=PhoneNumber label={t("Phone")} />
    </FieldsetLayoutPanel>

  let make = React.memo(make)
}

let shopInputValues = (selectedShop: Auth.shop) => {
  ShopEditFormLenses.name: selectedShop.name,
  address: selectedShop.address,
  postalCode: selectedShop.postalCode,
  city: selectedShop.city,
  email: selectedShop.email,
  corporateName: selectedShop.corporateName,
  country: selectedShop.country,
  phoneNumber: selectedShop.phoneNumber,
  cityOfRegistryOffice: selectedShop.cityOfRegistryOffice->Option.getWithDefault(""),
  website: selectedShop.website->Option.getWithDefault(""),
  fiscalYearEndClosingMonth: selectedShop.fiscalYearEndClosingMonth->Option.getWithDefault(""),
  legalRepresentative: selectedShop.legalRepresentative->Option.getWithDefault(""),
  legalForm: selectedShop.legalForm->Option.getWithDefault(""),
  amountOfShareCapital: selectedShop.amountOfShareCapital->Option.getWithDefault(""),
  tvaNumber: selectedShop.tvaNumber->Option.getWithDefault(""),
  siretNumber: selectedShop.siretNumber->Option.getWithDefault(""),
  rcsNumber: selectedShop.rcsNumber->Option.getWithDefault(""),
  apeNafCode: selectedShop.apeNafCode->Option.getWithDefault(""),
  bankName: selectedShop.bankName->Option.getWithDefault(""),
  bankCode: selectedShop.bankCode->Option.getWithDefault(""),
  bankAccountHolder: selectedShop.bankAccountHolder->Option.getWithDefault(""),
  bankAccountNumber: selectedShop.bankAccountNumber->Option.getWithDefault(""),
  bicCode: selectedShop.bicCode->Option.getWithDefault(""),
  ibanNumber: selectedShop.ibanNumber->Option.getWithDefault(""),
}

let schema = [
  ShopEditForm.Schema.StringNotEmpty(Name),
  StringNotEmpty(Address),
  StringNotEmpty(PostalCode),
  StringNotEmpty(City),
  PhoneNumber(PhoneNumber),
  Email(Email),
]

@react.component
let make = (~updateShopRequest) => {
  let (notification, setNotification) = React.useState(_ => None)

  let shops = Auth.useShops()
  let activeShop = Auth.useActiveShop()
  let activeShop = activeShop->Option.getWithDefault(shops->Array.getUnsafe(0))
  let scope = Auth.useScope()
  let logUser = Auth.useLogUser()

  let jwt = Auth.getJwt()->Option.getWithDefault("")

  let onSubmitSuccess = _ => {
    logUser(jwt)
    setNotification(_ => Some(
      Ok(
        t(
          "You have made changes to your store information; please now verify your billing information in Subscription and Billing.",
        ),
      ),
    ))
  }

  let onSubmitFailure = message => setNotification(_ => Some(Error(message)))

  let (formState, formDispatch) = ShopEditForm.useFormPropState({
    id: activeShop.id,
    initialValues: shopInputValues(activeShop),
    schema,
    onSubmitSuccess,
    onSubmitFailure,
  })

  let handleFormSubmit = (
    _,
    {
      ShopEditFormLenses.name: name,
      address,
      postalCode,
      city,
      cityOfRegistryOffice,
      country,
      phoneNumber,
      email,
      website,
      fiscalYearEndClosingMonth,
      corporateName,
      legalRepresentative,
      legalForm,
      amountOfShareCapital,
      tvaNumber,
      siretNumber,
      rcsNumber,
      apeNafCode,
      bankName,
      bankCode,
      bankAccountHolder,
      bankAccountNumber,
      bicCode,
      ibanNumber,
    },
  ) => {
    updateShopRequest(
      ~id=activeShop.id,
      ~name,
      ~address,
      ~postalCode,
      ~city,
      ~cityOfRegistryOffice,
      ~country,
      ~phoneNumber,
      ~email,
      ~website,
      ~fiscalYearEndClosingMonth,
      ~corporateName,
      ~legalRepresentative,
      ~legalForm,
      ~amountOfShareCapital,
      ~tvaNumber,
      ~siretNumber,
      ~rcsNumber,
      ~apeNafCode,
      ~bankName,
      ~bankCode,
      ~bankAccountHolder,
      ~bankAccountNumber,
      ~bicCode,
      ~ibanNumber,
    )
    ->Future.mapOk(_ => Some(t("Your email address has been updated successfully.")))
    ->Future.mapError(error => {
      switch error {
      | Some(UpdateShopRequest.ShopArchivedFailure) => t("This shop has been archived.")
      | Some(NotFoundShopFailure) => t("This shop does not exist.")
      | _ => t("An unexpected error occured. Please try again or contact the support.")
      }
    })
  }

  let actionsBar =
    <ResourceDetailsPage.ActionsBar
      items=[
        <ShopEditForm.CancelButton size=#medium text={t("Cancel")} />,
        <ShopEditForm.SubmitButton
          size=#medium text={t("Save")} variation=#success onSubmit=handleFormSubmit
        />,
      ]
    />

  let notificationBanner = switch notification {
  | Some(value) =>
    <ResourceDetailsPage.NotificationBanner
      value onRequestClose={_ => setNotification(_ => None)}
    />
  | None => React.null
  }

  <ShopEditForm.FormProvider propState=(formState, formDispatch)>
    <ResourceDetailsPage
      title={switch scope {
      | Single(_) => t("Shop")
      | Organisation(_) => t("Shops")
      }}
      actionsBar
      notificationBanner>
      <Stack space=#xlarge>
        {switch scope {
        | Single(_) => React.null
        | Organisation(_) =>
          <FieldsetLayoutPanel
            title={t("Selected shop")}
            description={t("Select the store for which you want to view or modify information.")}>
            <Auth.InputSelectSingleShopField value=activeShop required=false />
          </FieldsetLayoutPanel>
        }}
        <ShopEditFormGeneralInformationFieldset />
        <ShopEditFormAddressFieldset />
        <ShopEditFormLegalInformationFieldset />
        <ShopEditFormBankingInformationFieldset />
      </Stack>
    </ResourceDetailsPage>
  </ShopEditForm.FormProvider>
}
