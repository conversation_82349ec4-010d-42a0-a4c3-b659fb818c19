open Intl

let {invoicesRequest} = module(BillingAccount)

@react.component
let make = (~subUrlPath, ~appIndexRoute, ~reloadAlertBar) => {
  let url = Navigation.useUrl()
  let userRole = Auth.useRole()
  let scope = Auth.useScope()
  let shop = switch scope {
  | Organisation({activeShop: None, shops}) => shops->Array.getUnsafe(0)
  | Organisation({activeShop: Some(shop)}) | Single(shop) => shop
  }

  if Auth.isAuthorizedAccess(~role=userRole, ~targetPathname=SettingsRoutes.baseRoute) {
    switch subUrlPath {
    | list{"user"} =>
      let {updateEmailRequest, updatePasswordRequest, updateUserNamesRequest} = module(
        SettingsUserPage
      )
      <SettingsUserPage updateEmailRequest updatePasswordRequest updateUserNamesRequest />
    | list{"shops"} =>
      <SettingsShopsPage updateShopRequest=SettingsShopsPage.UpdateShopRequest.make />
    | list{"label-printer"} => <SettingsLabelPrinterPage />
    | list{"billing-account", shopId, "invoices"} =>
      <SettingsBillingAccountListInvoicesPage shopId invoicesRequest />
    | list{"billing-account", "edit"} =>
      let decodeUrlResult = SettingsRoutes.decodeEditBillingAccountQueryString(url.query)
      let {requestBillingAccountUpdate} = module(SettingsBillingAccountEditPage)
      switch decodeUrlResult {
      | Ok({
          SettingsRoutes.BillingAccountEditQueryStringCodecs.activeShopId: activeShopId,
          corporateName,
          shopName,
          email,
          phone,
          vatNumber,
          billingAddress,
          shippingAddress,
        }) =>
        <SettingsBillingAccountEditPage
          activeShopId
          requestBillingAccountUpdate
          corporateName
          shopName
          email
          phone
          vatNumber
          billingAddress
          shippingAddress
        />
      | _ => <Navigation.Redirect route={appIndexRoute} />
      }
    | list{"billing-account"} =>
      <Navigation.Redirect route={SettingsRoutes.billingAccountShowRoute(~shopId=shop.id, ())} />
    | list{"billing-account", "payment-succeeded"} =>
      let {
        billingAccountRequest,
        sepaMandateRequest,
        billingStatusRequest,
        updatePaymentMethodRequest,
        subscriptionsRequest,
        confirmSepaMandateRequest,
      } = module(SettingsBillingAccountShowPage)
      <SettingsBillingAccountShowPage
        shopId=shop.id
        confirmSepaModalOpened=false
        editPaymentMethodModalOpened=false
        billingAccountRequest
        sepaMandateRequest
        billingStatusRequest
        updatePaymentMethodRequest
        invoicesRequest
        subscriptionsRequest
        confirmSepaMandateRequest
        ipAddressRequest=IpAddress.request
        reloadAlertBar
        notification={Banner.Success(
          t("Your payment has been successfully processed. You can now fully enjoy our services."),
        )}
      />
    | list{"billing-account", "payment-failed"} =>
      let {
        billingAccountRequest,
        sepaMandateRequest,
        billingStatusRequest,
        updatePaymentMethodRequest,
        subscriptionsRequest,
        confirmSepaMandateRequest,
      } = module(SettingsBillingAccountShowPage)
      <SettingsBillingAccountShowPage
        shopId=shop.id
        confirmSepaModalOpened=false
        editPaymentMethodModalOpened=false
        billingAccountRequest
        sepaMandateRequest
        billingStatusRequest
        updatePaymentMethodRequest
        invoicesRequest
        subscriptionsRequest
        confirmSepaMandateRequest
        ipAddressRequest=IpAddress.request
        reloadAlertBar
        notification={Banner.Danger(
          t(
            "We were unable to complete your payment due to a technical error. Please try again or contact us for assistance.",
          ),
        )}
      />
    | list{"billing-account", shopId} =>
      let {
        billingAccountRequest,
        sepaMandateRequest,
        billingStatusRequest,
        updatePaymentMethodRequest,
        subscriptionsRequest,
        confirmSepaMandateRequest,
      } = module(SettingsBillingAccountShowPage)
      <SettingsBillingAccountShowPage
        shopId
        confirmSepaModalOpened=false
        editPaymentMethodModalOpened=false
        billingAccountRequest
        sepaMandateRequest
        billingStatusRequest
        updatePaymentMethodRequest
        invoicesRequest
        subscriptionsRequest
        confirmSepaMandateRequest
        ipAddressRequest=IpAddress.request
        reloadAlertBar
      />
    | list{"billing-account", shopId, modal} =>
      module BillingAccountShowPageModal = SettingsRoutes.BillingAccountShowPageModal
      switch BillingAccountShowPageModal.fromString(modal) {
      | Some(modal) =>
        let {
          billingAccountRequest,
          sepaMandateRequest,
          billingStatusRequest,
          updatePaymentMethodRequest,
          subscriptionsRequest,
          confirmSepaMandateRequest,
        } = module(SettingsBillingAccountShowPage)
        <SettingsBillingAccountShowPage
          shopId
          confirmSepaModalOpened={BillingAccountShowPageModal.isConfirmMandate(modal)}
          editPaymentMethodModalOpened={BillingAccountShowPageModal.isEditPaymentMethod(modal)}
          billingAccountRequest
          sepaMandateRequest
          billingStatusRequest
          updatePaymentMethodRequest
          invoicesRequest
          subscriptionsRequest
          confirmSepaMandateRequest
          ipAddressRequest=IpAddress.request
          reloadAlertBar
        />
      | None => <Navigation.Redirect route={SettingsRoutes.billingAccountShowRoute(~shopId, ())} />
      }
    | _ => <Navigation.Redirect route={appIndexRoute} />
    }
  } else {
    <AccessDeniedPage />
  }
}
