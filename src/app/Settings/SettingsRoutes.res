let baseRoute = "/settings"

let userRoute = baseRoute ++ "/user"
let shopsRoute = baseRoute ++ "/shops"
let labelPrinterRoute = baseRoute ++ "/label-printer"
let billingAccountBaseRoute = baseRoute ++ "/billing-account"

module BillingAccountShowPageModal = {
  type t = EditPaymentMethod | ConfirmMandate
  let isEditPaymentMethod = value =>
    switch value {
    | EditPaymentMethod => true
    | ConfirmMandate => false
    }
  let isConfirmMandate = value =>
    switch value {
    | EditPaymentMethod => false
    | ConfirmMandate => true
    }
  let fromString = string =>
    switch string {
    | "edit-payment-method" => Some(EditPaymentMethod)
    | "confirm-mandate" => Some(ConfirmMandate)
    | _ => None
    }
  let toString = value =>
    switch value {
    | EditPaymentMethod => "edit-payment-method"
    | ConfirmMandate => "confirm-mandate"
    }
}

let billingAccountShowRoute = (~shopId=?, ()) =>
  switch shopId {
  | Some(shopId) => billingAccountBaseRoute ++ "/" ++ shopId
  | None => billingAccountBaseRoute
  }

let billingAccountShowRouteWithModal = (~shopId, ~modal) =>
  billingAccountShowRoute(~shopId, ()) ++ "/" ++ BillingAccountShowPageModal.toString(modal)
let billingAccountEditRoute = billingAccountBaseRoute ++ "/edit"
let billingAccountInvoicesRoute = (~shopId) =>
  billingAccountBaseRoute ++ "/" ++ shopId ++ "/invoices"

module BillingAccountEditQueryStringCodecs = {
  type t = {
    activeShopId: string,
    corporateName: string,
    shopName: string,
    email: string,
    phone: string,
    vatNumber: option<string>,
    billingAddress: option<CorporateEntity.Address.t>,
    shippingAddress: option<CorporateEntity.Address.t>,
  }

  let encoder = ({
    activeShopId,
    corporateName,
    shopName,
    email,
    phone,
    vatNumber,
    billingAddress,
    shippingAddress,
  }) => (
    activeShopId,
    corporateName,
    shopName,
    email,
    phone,
    vatNumber,
    billingAddress,
    shippingAddress,
  )

  let decoder = ((
    activeShopId,
    corporateName,
    shopName,
    email,
    phone,
    vatNumber,
    billingAddress,
    shippingAddress,
  )) => Ok({
    activeShopId,
    corporateName,
    shopName,
    email,
    phone,
    vatNumber,
    billingAddress,
    shippingAddress,
  })

  let address = JsonCodec.object4(
    ({CorporateEntity.Address.address: address, postalCode, city, country}) => (
      address,
      postalCode,
      city,
      country,
    ),
    ((address, postalCode, city, country)) => Ok({
      CorporateEntity.Address.address,
      postalCode,
      city,
      country,
    }),
    JsonCodec.field("address", JsonCodec.string),
    JsonCodec.field("postalCode", JsonCodec.string),
    JsonCodec.field("city", JsonCodec.string),
    JsonCodec.field("country", JsonCodec.string),
  )

  let value = JsonCodec.object8(
    encoder,
    decoder,
    JsonCodec.field("activeShopId", JsonCodec.string),
    JsonCodec.field("corporateName", JsonCodec.string),
    JsonCodec.field("shopName", JsonCodec.string),
    JsonCodec.field("email", JsonCodec.string),
    JsonCodec.field("phone", JsonCodec.string),
    JsonCodec.field("vatNumber", JsonCodec.string)->JsonCodec.optional,
    JsonCodec.field("billingAddress", address)->JsonCodec.optional,
    JsonCodec.field("shippingAddress", address)->JsonCodec.optional,
  )
}

let decodeEditBillingAccountQueryString = query =>
  switch query->QueryString.parse->JsonCodec.decodeWith(BillingAccountEditQueryStringCodecs.value) {
  | Ok(values) => Ok(values)
  | _ => Error()
  }

let encodeEditBillingAccountQueryString = (
  ~activeShopId,
  ~corporateName,
  ~shopName,
  ~email,
  ~phone,
  ~billingAddress,
  ~shippingAddress,
  ~vatNumber,
) =>
  JsonCodec.encodeWith(
    {
      BillingAccountEditQueryStringCodecs.activeShopId,
      corporateName,
      shopName,
      email,
      phone,
      billingAddress,
      shippingAddress,
      vatNumber,
    },
    BillingAccountEditQueryStringCodecs.value,
  )->QueryString.stringify
