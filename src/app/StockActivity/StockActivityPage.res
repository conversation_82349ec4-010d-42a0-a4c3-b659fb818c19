open Intl

module Query = %graphql(`
  query StockActivityPageQuery($search: String, $before: String, $after: String, $first: Int, $last: Int, $filterBy: InputStockActivitiesQueryFilter) {
    stockActivities(search: $search, before: $before, after: $after, first: $first, last: $last, filterBy: $filterBy) {
      pageInfo {
        startCursor
        endCursor
      }
      edges {
        node {
          id
          variant {
            id
            cku
            formattedName
            formattedDescription
            stockKeepingUnit
            capacityPrecision
            capacityUnit
            bulk
            product {
              color @ppxOmitFutureValue
            }
          }
          kind @ppxOmitFutureValue
          reason @ppxOmitFutureValue
          comment
          quantity
          createdAt
          shop { name }
          device { name slug }
        }
      }
      totalCount
    }
  }
`)

module Filters = {
  type kind = ExcludingReset | Kind(StockActivityKind.t)
  type t = {
    shop: option<Auth.shop>,
    dateRange: option<(Js.Date.t, Js.Date.t)>,
    kind: option<kind>,
    reason: option<StockActivityReason.t>,
  }

  let encoder = ({shop, dateRange, kind, reason}) => (
    shop->Option.map(shop => shop.id),
    dateRange->Option.map(((start, end)) => [start->Js.Date.valueOf, end->Js.Date.valueOf]),
    switch kind {
    | Some(Kind(kind)) => Some(kind->StockActivityKind.toString)
    | Some(ExcludingReset) => Some("EXCLUDING_RESET")
    | None => None
    },
    reason->Option.map(StockActivityReason.toString),
  )

  let decoder = (~shops, (shopId, dateRange, kind, reason)) => Ok({
    shop: shops->Array.getBy((shop: Auth.shop) => Some(shop.id) === shopId),
    dateRange: dateRange->Option.flatMap(range =>
      switch range {
      | [start, end] => Some((start->Js.Date.fromFloat, end->Js.Date.fromFloat))
      | _ => None
      }
    ),
    kind: switch kind {
    | Some("EXCLUDING_RESET") => Some(ExcludingReset)
    | Some(kind) => Some(Kind(kind->StockActivityKind.fromStringExn))
    | None => None
    },
    reason: reason->Option.map(StockActivityReason.fromStringExn),
  })

  let useJsonCodec = () => {
    let shops = Auth.useShops()

    JsonCodec.object4(
      encoder,
      decoder(~shops),
      JsonCodec.field("shopId", JsonCodec.string)->JsonCodec.optional,
      JsonCodec.field("dateRange", JsonCodec.array(JsonCodec.float))->JsonCodec.optional,
      JsonCodec.field("kind", JsonCodec.string)->JsonCodec.optional,
      JsonCodec.field("reason", JsonCodec.string)->JsonCodec.optional,
    )
  }
}

module Row = {
  type t = {
    id: string,
    variantId: string,
    variantCku: string,
    variantName: string,
    variantDescription: string,
    variantStockKeepingUnit: option<string>,
    variantCapacityPrecision: option<int>,
    variantCapacityUnit: option<string>,
    color: option<CatalogProduct.Color.t>,
    formattedKind: string,
    kind: StockActivityKind.t,
    reason: option<StockActivityReason.t>,
    quantity: int,
    description: option<string>,
    date: Js.Date.t,
    shopName: string,
    deviceName: string,
  }
}

module NotificationBanner = {
  @react.component
  let make = React.memo((~notification, ~onRequestClose) =>
    switch notification {
    | Some(result) =>
      <Box spaceTop=#medium>
        {switch result {
        | Ok(message) => <Banner textStatus=Success(message) onRequestClose />
        | Error(message) => <Banner textStatus=Danger(message) onRequestClose />
        }}
      </Box>
    | None => React.null
    }
  )
}

module StockActivityExportMenuItem = {
  let endpoint = Env.sheetUrl() ++ "/stock-movements-history"

  module Query = %graphql(`
    query StockActivityExportQuery($filterBy: InputStockActivitiesQueryFilter) {
      stockActivities(filterBy: $filterBy) {
        totalCount
      }
    }
  `)

  let encodeRequestBodyJson = (~shopIds, ~kind=?, ~reason=?, ~dateRange=?, ()) => {
    let body = Js.Dict.empty()

    body->Js.Dict.set(
      "shopIds",
      shopIds->Array.map(shopId => shopId->Json.encodeString)->Json.encodeArray,
    )
    switch kind {
    | Some(Filters.Kind(kind)) =>
      body->Js.Dict.set("stockActivityKind", kind->StockActivityKind.toString->Json.encodeString)
    | Some(ExcludingReset) =>
      body->Js.Dict.set(
        "stockActivityNotKind",
        #RESET->StockActivityKind.toString->Json.encodeString,
      )
    | _ => ()
    }
    switch reason {
    | Some(reason) =>
      body->Js.Dict.set(
        "stockActivityLossReason",
        reason->StockActivityReason.toString->Json.encodeString,
      )
    | _ => ()
    }
    switch dateRange {
    | Some((startDate, endDate)) =>
      body->Js.Dict.set("startDate", startDate->Js.Date.toISOString->Json.encodeString)
      body->Js.Dict.set("endDate", endDate->Js.Date.toISOString->Json.encodeString)
    | _ => ()
    }
    body->Js.Dict.set("timeZone", Intl.timeZone->Json.encodeString)

    body->Json.encodeDict
  }

  let makeVariablesFromFilters = (~shopIds, ~kind=?, ~reason=?, ~dateRange=?, ()) =>
    Query.makeVariables(
      ~filterBy=Query.makeInputObjectInputStockActivitiesQueryFilter(
        ~shopIds=Query.makeInputObjectInFilter(~_in=shopIds, ()),
        ~kind=?switch kind {
        | Some(Filters.Kind(kind)) =>
          Some(Query.makeInputObjectInOrNotInFilter(~_in=[kind->StockActivityKind.toString], ()))
        | Some(ExcludingReset) =>
          Some(
            Query.makeInputObjectInOrNotInFilter(~_notIn=[#RESET->StockActivityKind.toString], ()),
          )
        | None => None
        },
        ~reason=?switch reason {
        | Some(reason) =>
          Some(Query.makeInputObjectInFilter(~_in=[reason->StockActivityReason.toString], ()))
        | _ => None
        },
        ~date=?switch dateRange {
        | Some((startDate, endDate)) =>
          Some({
            _after: None,
            _before: None,
            _between: Some([
              startDate->Scalar.Datetime.serialize,
              endDate->Scalar.Datetime.serialize,
            ]),
          })
        | _ => None
        },
        (),
      ),
      (),
    )

  let totalCountLimit = 10000

  @react.component
  let make = (~text, ~shopIds, ~kind=?, ~reason=?, ~dateRange=?, ~onRequestErrorNotification) => {
    let {onRequestClose} = Popover.useState()
    let queryResults = Query.use(
      makeVariablesFromFilters(~shopIds, ~kind?, ~reason?, ~dateRange?, ()),
      ~fetchPolicy=CacheAndNetwork,
    )

    let request = () => {
      Request.make(
        endpoint,
        ~method=#POST,
        ~bodyJson=encodeRequestBodyJson(~shopIds, ~kind?, ~reason?, ~dateRange?, ()),
      )
    }

    let onSuccess = _ => onRequestClose()
    let onFailure = error => {
      let errorMessage =
        error->RequestOpenStorageUrlMenuItem.failureErrorToString(~exportName="stock activities")
      onRequestErrorNotification(errorMessage)
      onRequestClose()
    }

    let totalCount = switch queryResults {
    | {data: Some({stockActivities: {totalCount}})} => Some(totalCount)
    | _ => None
    }

    let operableRequest = switch (shopIds->Array.length === 1, totalCount) {
    | (false, _) => Error(t("Please select a shop beforehand with the filter."))
    | (_, None) => Error(t("Please wait."))
    | (_, Some(0)) => Error(t("No stock activity to export with these filters."))
    | (_, Some(totalCount)) if totalCount > totalCountLimit =>
      Error(
        template(
          t(
            "{{transactionNumber}} transactions are to be exported,\n" ++
            "the exported file may be too large.\n" ++
            "Please modify your filters to launch the export\n" ++ //
            "(maximum {{maxTransactionNumber}} transactions).",
          ),
          ~values={
            "transactionNumber": totalCount->Float.fromInt->Intl.decimalFormat,
            "maxTransactionNumber": totalCountLimit->Float.fromInt->Intl.decimalFormat,
          },
          (),
        ),
      )
    | _ => Ok(request)
    }

    <RequestOpenStorageUrlMenuItem text operableRequest onSuccess onFailure />
  }
}

module Scaffolded = Scaffold.Make({
  type filters = Filters.t
  let useFiltersJsonCodec = Filters.useJsonCodec

  module QueryInner = Query.Query_inner
  type queryVariableFilterBy = Query.t_variables_InputStockActivitiesQueryFilter
  let useQuery = Query.use

  let makeQueryVariables = (
    _defaultQueryVariables,
    ~connectionArguments,
    ~search=?,
    ~filterBy=?,
    (),
  ) => {
    QueryInner.first: connectionArguments.first,
    last: connectionArguments.last,
    before: connectionArguments.Scaffold.before,
    after: connectionArguments.after,
    search,
    filterBy,
  }

  let makeQueryVariablesFilterBy = ({Filters.shop: shop, dateRange, reason, kind}) =>
    Query.makeInputObjectInputStockActivitiesQueryFilter(
      ~shopIds=?{
        switch shop {
        | Some({id: shopId}) => Some(Query.makeInputObjectInFilter(~_in=[shopId], ()))
        | None => None
        }
      },
      ~date=?{
        switch dateRange {
        | Some((startDate, endDate)) =>
          Some({
            QueryInner._after: None,
            _before: None,
            _between: Some([
              startDate->Scalar.Datetime.serialize,
              endDate->Scalar.Datetime.serialize,
            ]),
          })
        | None => None
        }
      },
      ~reason=?{
        switch reason {
        | Some(reason) =>
          Some(Query.makeInputObjectInFilter(~_in=[reason->StockActivityReason.toString], ()))
        | None => None
        }
      },
      ~kind=?{
        switch kind {
        | Some(Kind(kind)) =>
          Some(Query.makeInputObjectInOrNotInFilter(~_in=[kind->StockActivityKind.toString], ()))
        | Some(ExcludingReset) =>
          Some(
            Query.makeInputObjectInOrNotInFilter(~_notIn=[#RESET->StockActivityKind.toString], ()),
          )
        | None => None
        }
      },
      (),
    )

  let totalCountFromQueryData = ({Query.stockActivities: stockActivities}) =>
    stockActivities.totalCount
  let cursorsFromQueryData = ({Query.stockActivities: stockActivities}) => (
    stockActivities.pageInfo.startCursor,
    stockActivities.pageInfo.endCursor,
  )

  type row = Row.t
  let rowsFromQueryDataAndState = ({Query.stockActivities: stockActivities}, _) =>
    stockActivities.edges->Array.keepMap(({node}) =>
      switch node.variant {
      | Some(variant) =>
        Some({
          Row.id: node.id,
          kind: node.kind,
          variantId: variant.id,
          variantCku: variant.cku,
          variantName: variant.formattedName,
          variantDescription: variant.formattedDescription,
          variantStockKeepingUnit: variant.stockKeepingUnit,
          variantCapacityUnit: switch (variant.bulk, variant.capacityUnit) {
          | (Some(true), Some(unit)) => Some(unit)
          | _ => None
          },
          variantCapacityPrecision: switch (variant.bulk, variant.capacityPrecision) {
          | (Some(true), Some(precision)) => Some(precision)
          | _ => None
          },
          color: node.variant
          ->Option.map(variant => variant.product)
          ->Option.flatMap(product => product.color),
          formattedKind: node.kind->StockActivityKind.toLabel,
          reason: node.reason,
          description: node.comment,
          shopName: node.shop.name,
          deviceName: DeviceName.decode(
            ~slug=node.device.slug,
            ~name=node.device.name,
          )->DeviceName.format,
          date: node.createdAt,
          quantity: node.quantity,
        })
      | _ => None
      }
    )

  let keyExtractor = ({Row.id: id}) => id
})

module SheetExportInput = {
  type t = {
    shopId: option<string>,
    kind: option<Filters.kind>,
    reason: option<StockActivityReason.t>,
    dateRange: option<(Js.Date.t, Js.Date.t)>,
  }

  let makeFromStateExn = ({Scaffold.filters: filters}) => {
    let {Filters.shop: shop, kind, reason, dateRange} = filters
    let shopId = shop->Option.map(({id}) => id)

    {shopId, kind, reason, dateRange}
  }
}

let use = (~initialState) => {
  let (state, dispatch) = Scaffolded.use(() => initialState)

  React.useEffect1(() => {
    switch state.filters {
    | {kind: Some(Kind(#LOSS))} => ()
    | {reason: Some(_)} => FiltersUpdated(prev => {...prev, reason: None})->dispatch
    | _ => ()
    }
    None
  }, [state.filters])

  (state, dispatch)
}

@react.component
let make = () => {
  let scope = Auth.useScope()
  let organisationAccount = switch scope {
  | Organisation(_) => true
  | Single(_) => false
  }

  let initialState = Scaffolded.makeInitialState(
    ~filters={
      shop: switch scope {
      | Organisation({activeShop}) => activeShop
      | Single(activeShop) => Some(activeShop)
      },
      kind: None,
      reason: None,
      dateRange: None,
    },
  )
  let (state, dispatch) = use(~initialState)
  let defaultQueryVariables = Query.makeVariables()

  let (notification, setNotification) = React.useState(() => None)

  let columns = [
    {
      Scaffold.name: t("Product name and description"),
      layout: {minWidth: 250.->#px, width: 30.->#pct, margin: #xxlarge, sticky: true},
      render: activity => {
        let pastilleColor =
          activity.Row.color->Option.map(color =>
            (color->CatalogProduct.Color.toColorSet(~variation=#pastille)).foregroundColor
          )
        let description =
          activity.variantStockKeepingUnit->Option.mapWithDefault("", sku =>
            `SKU${t(":")} ${sku}, `
          ) ++ activity.variantDescription

        <LegacyProductReferenceTableCell
          cku=activity.variantCku name=activity.variantName ?pastilleColor description
        />
      },
    },
    {
      name: t("Type"),
      layout: {minWidth: 120.->#px, width: 0.5->#fr},
      render: ({formattedKind, reason}) =>
        <StockActivityTypeTableCell value=formattedKind reason />,
    },
    {
      name: t("Quantity"),
      layout: {width: 0.5->#fr, alignX: #center, sticky: true},
      render: ({quantity, variantCapacityPrecision, variantCapacityUnit, kind}) =>
        <StockActivityQuantityTableCell
          value=quantity
          capacityPrecision=?variantCapacityPrecision
          capacityUnit=?variantCapacityUnit
          kind
        />,
    },
    {
      name: t("Comment"),
      layout: {minWidth: 100.->#px, margin: #normal},
      render: ({description}) =>
        <TextStyle> {description->Option.getWithDefault("—")->React.string} </TextStyle>,
    },
    {
      name: t("Datetime"),
      layout: {minWidth: 110.->#px},
      render: ({date}) => <TableDatetimeCell value=date />,
    },
    {
      name: t("Source"),
      layout: {minWidth: 120.->#px},
      render: ({deviceName, shopName}) => {
        let shopName = switch scope {
        | Organisation(_) => Some(shopName)
        | _ => None
        }
        <StockActivitySourceTableCell deviceName ?shopName />
      },
    },
  ]

  let filters =
    <Inline space=#small>
      {if organisationAccount {
        <Auth.SelectShopFilter
          value=?state.filters.shop
          onChange={shop => FiltersUpdated(prev => {...prev, shop})->dispatch}
        />
      } else {
        React.null
      }}
      {if organisationAccount {
        <Separator />
      } else {
        React.null
      }}
      <Select
        preset=#filter
        label={t("Kind")}
        sections={
          let defaultItem = {
            Select.key: "default",
            label: t("All"),
            value: None,
            sticky: true,
          }
          let excludingResetItem = {
            Select.key: "excluding-reset",
            label: t("All but reset"),
            value: Some(Filters.ExcludingReset),
            sticky: true,
          }
          let items = StockActivityKind.values->Array.map(value => {
            Select.label: value->StockActivityKind.toLabel,
            key: value->StockActivityKind.toString,
            value: Some(Filters.Kind(value)),
          })

          [{items: [defaultItem, excludingResetItem]}, {title: t("Kinds"), items}]
        }
        value=state.filters.kind
        onChange={kind => FiltersUpdated(prev => {...prev, kind})->dispatch}
      />
      {switch state.filters.kind {
      | Some(Kind(#LOSS)) =>
        <Select
          preset=#filter
          label={t("Reason")}
          sections={
            let defaultItem = {
              Select.key: "default",
              label: template(t("All{{feminine}}"), ()),
              value: None,
              sticky: true,
            }
            let items = StockActivityReason.values->Array.map(value => {
              Select.label: value->StockActivityReason.toLabel,
              key: value->StockActivityReason.toString,
              value: Some(value),
            })

            [{items: [defaultItem]}, {title: t("Reasons"), items}]
          }
          value=state.filters.reason
          onChange={reason => FiltersUpdated(prev => {...prev, reason})->dispatch}
        />
      | _ => React.null
      }}
      <SelectDateRangeFilter
        placeholder={t("Since the beginning")}
        value=?state.filters.dateRange
        onChange={dateRange => FiltersUpdated(prev => {...prev, dateRange})->dispatch}
        triggerLabelDisplay=#showPreset
      />
      {switch state.filters {
      | {dateRange: Some(_)} | {kind: Some(_)} =>
        <Scaffold.ResetFiltersButton
          onPress={() => FiltersUpdated(_ => initialState.filters)->dispatch}
        />
      | _ => React.null
      }}
    </Inline>

  let actions = {
    let shops = Auth.useShops()
    let {shopId, kind, reason, dateRange} = state->SheetExportInput.makeFromStateExn

    let shopIds = switch shopId {
    | Some(shopId) => [shopId]
    | None => shops->Array.map(shop => shop.id)
    }

    let onRequestErrorNotification = error => setNotification(_ => Some(Error(error)))

    <Menu overlayPriority=false>
      <StockActivityExportMenuItem
        text={t("Export")} shopIds ?kind ?reason ?dateRange onRequestErrorNotification
      />
    </Menu>
  }

  let banner = <NotificationBanner notification onRequestClose={() => setNotification(_ => None)} />

  let activeShop = switch scope {
  | Organisation({activeShop}) => activeShop
  | Single(activeShop) => Some(activeShop)
  }

  let searchBar =
    <SearchBar
      placeholder={t("Search a stock movement")}
      value=?state.searchQuery
      onChange={searchQuery => Searched(searchQuery)->dispatch}
    />

  let emptyState = switch state {
  | {currentPage: 1, searchQuery: None, filters: {shop, kind: None, reason: None, dateRange: None}}
    if shop === activeShop =>
    <EmptyState
      illustration=Illustration.create title={t("Welcome to the stock movements space.")}
    />
  | _ =>
    <EmptyState
      illustration=Illustration.notFound
      title={t("No result were found.")}
      text={t("Try again with another keyword/filter or:")}>
      <Button variation=#neutral onPress={_ => Reset(initialState)->dispatch}>
        {t("Clear search query and filters")->React.string}
      </Button>
    </EmptyState>
  }

  <Scaffolded
    title={t("Stock activities")}
    state
    dispatch
    filters
    columns
    actions
    banner
    searchBar
    emptyState
    defaultQueryVariables
  />
}

let make = React.memo(make)
