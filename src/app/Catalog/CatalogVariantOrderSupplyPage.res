open Intl

module ParentQuery = %graphql(`
  query CatalogVariantOrderSupplyPageParentQuery($cku: CKU!) {
    variantsByCku(cku: $cku, first: 1) {
      edges {
        node {
          id
          cku
          formattedName
        }
      }
    }
  }
`)

module Query = %graphql(`
  query CatalogVariantOrderSupplyPageQuery($cku: CKU!, $search: String, $filterBy: InputOrderProductsByVariantCkuQueryFilter, $before: String, $after: String, $first: Int, $last: Int) {
    orderProductsByVariantCku(cku: $cku, search: $search, filterBy: $filterBy, before: $before, after: $after, first: $first, last: $last) {
      pageInfo {
        startCursor
        endCursor
      }
      totalCount
      edges {
        node {
          quantity
          totalAmountExcludingTaxes
          totalAmountIncludingTaxes
          order {
            id
            name
            estimatedReceptionDate
            supplierCompanyName
            supplier {
              id
            }
          }
          shop {
            name
          }
        }
      }
    }
  }
`)

module Filters = {
  type t = {shop: option<Auth.shop>}

  let encoder = ({shop}) => shop->Option.map(shop => shop.id)

  let decoder = (~shops, shopId) => Ok({
    shop: shops->Array.getBy((shop: Auth.shop) => Some(shop.id) === shopId),
  })

  let useJsonCodec = () => {
    let shops = Auth.useShops()

    JsonCodec.object1(
      encoder,
      decoder(~shops),
      JsonCodec.field("shopId", JsonCodec.string)->JsonCodec.optional,
    )
  }
}

module Row = {
  type t = {
    id: string,
    name: string,
    shopName: string,
    receptionDate: Js.Date.t,
    quantity: string,
    totalAmountExcludingTaxes: float,
    totalAmountIncludingTaxes: float,
    supplierId: string,
    supplierCompanyName: string,
  }
}

module Scaffolded = Scaffold.Make({
  type filters = Filters.t
  let useFiltersJsonCodec = Filters.useJsonCodec

  module QueryInner = Query.Query_inner
  type queryVariableFilterBy = Query.t_variables_InputOrderProductsByVariantCkuQueryFilter
  let useQuery = Query.use

  let makeQueryVariables = (
    defaultQueryVariables,
    ~connectionArguments,
    ~search=?,
    ~filterBy=?,
    (),
  ) => {
    ...defaultQueryVariables,
    QueryInner.first: connectionArguments.first,
    last: connectionArguments.last,
    before: connectionArguments.Scaffold.before,
    after: connectionArguments.after,
    search,
    filterBy,
  }

  let makeQueryVariablesFilterBy = ({Filters.shop: shop}) => {
    Query.shopIds: switch shop {
    | Some({id: shopId}) => Some(Query.makeInputObjectInFilter(~_in=[shopId], ()))
    | None => None
    },
    archived: Some(#INCLUDED),
    receptionFinishedAt: Some({
      _before: Some(Js.Date.now()->Js.Date.fromFloat->Scalar.Datetime.serialize),
      _between: None,
      _after: None,
    }),
  }

  let totalCountFromQueryData = ({Query.orderProductsByVariantCku: orderProductsByVariantCku}) =>
    orderProductsByVariantCku.totalCount
  let cursorsFromQueryData = ({Query.orderProductsByVariantCku: orderProductsByVariantCku}) => (
    orderProductsByVariantCku.pageInfo.startCursor,
    orderProductsByVariantCku.pageInfo.endCursor,
  )

  type row = Row.t
  let rowsFromQueryDataAndState = (
    {Query.orderProductsByVariantCku: orderProductsByVariantCku},
    _,
  ) =>
    orderProductsByVariantCku.edges->Array.keepMap(({node: orderProduct}) =>
      switch (orderProduct.order, orderProduct.order->Option.flatMap(order => order.supplier)) {
      | (Some(order), Some(supplier)) =>
        Some({
          Row.id: order.id,
          name: order.name->Option.getWithDefault("?"),
          shopName: orderProduct.shop.name,
          receptionDate: order.estimatedReceptionDate,
          quantity: orderProduct.quantity->Int.toString,
          totalAmountExcludingTaxes: orderProduct.totalAmountExcludingTaxes,
          totalAmountIncludingTaxes: orderProduct.totalAmountIncludingTaxes,
          supplierId: supplier.id,
          supplierCompanyName: order.supplierCompanyName,
        })
      | _ => None
      }
    )

  let keyExtractor = ({Row.id: id}) => id
})

@react.component
let make = (~cku) => {
  let authScope = Auth.useScope()
  let activeShop = Auth.useActiveShop()
  let cku = cku->Scalar.CKU.serialize

  let pageSubtitle = switch ParentQuery.use(ParentQuery.makeVariables(~cku, ())) {
  | {data: Some({variantsByCku: {edges: [{node: variant}]}})} => Some(variant.formattedName)
  | {error: Some(_)} => None
  | _ => Some(t("Loading") ++ "...")
  }

  let initialState = Scaffolded.makeInitialState(~filters={Filters.shop: activeShop})
  let (state, dispatch) = Scaffolded.use(() => initialState)
  let defaultQueryVariables = Query.makeVariables(~cku, ())

  let columns = [
    {
      Scaffold.name: t("Number"),
      layout: {minWidth: 160.->#px, width: 30.->#pct, margin: #normal, sticky: true},
      render: ({Row.id: id, name, shopName}) => <OrderNameTableCell value=name shopName id />,
    },
    {
      name: t("Supplier"),
      layout: {minWidth: 180.->#px, width: 20.->#pct, margin: #small},
      render: ({supplierId, supplierCompanyName}) =>
        <TextLink text=supplierCompanyName to=Route(SupplierRoutes.showRoute(~id=supplierId)) />,
    },
    {
      name: t("Reception date"),
      layout: {minWidth: 150.->#px, width: 2.->#fr, margin: #huge},
      render: ({receptionDate}) => <TableDateCell value=Some(receptionDate) />,
    },
    {
      name: t("Amount VAT excl."),
      layout: {minWidth: 110.->#px},
      render: ({totalAmountExcludingTaxes, totalAmountIncludingTaxes}) =>
        <AmountTableCell
          value=totalAmountExcludingTaxes
          secondaryValue=totalAmountIncludingTaxes
          decimalPrecision=3
        />,
    },
    {
      name: t("Quantity"),
      layout: {width: 2.->#fr, alignX: #center},
      render: ({quantity}) => <TextStyle> {quantity->React.string} </TextStyle>,
    },
  ]

  let filters = switch authScope {
  | Organisation(_) =>
    <Auth.SelectShopFilter
      value=?state.filters.shop onChange={shop => FiltersUpdated(_ => {shop: shop})->dispatch}
    />
  | _ => React.null
  }

  let searchBar =
    <SearchBar
      value=?state.searchQuery
      placeholder={t("Search an order")}
      onChange={searchQuery => Searched(searchQuery)->dispatch}
    />

  let emptyState = switch (state, authScope) {
  | ({currentPage: 1, searchQuery: None}, Single(_) | Organisation({activeShop: None})) =>
    <EmptyState
      illustration=Illustration.create title={t("Welcome to the supplier orders space.")}
    />
  | ({currentPage: 1, searchQuery: None}, Organisation({activeShop: Some(_)})) =>
    <EmptyState
      illustration=Illustration.notFound
      title={t("Welcome to the supplier orders space.")}
      text={t("It seems no data exists yet for this shop.")}
    />
  | _ =>
    <EmptyState
      illustration=Illustration.notFound
      title={t("No result were found.")}
      text={t("Try again with another keyword/filter or:")}>
      <Button variation=#neutral onPress={_ => Reset(initialState)->dispatch}>
        {t("Clear search query and filters")->React.string}
      </Button>
    </EmptyState>
  }

  switch pageSubtitle {
  | Some(subtitle) =>
    <Scaffolded
      title={t("Received supplier orders")}
      subtitle
      defaultQueryVariables
      state
      dispatch
      columns
      filters
      searchBar
      emptyState
    />
  | _ => <Placeholder status=Error />
  }
}

let make = React.memo(make)
