module ProductsQuery = %graphql(`
    query productsByVariantCku($cku: CKU!) {
      productsByVariantCku(cku: $cku, first: 50) {
        edges {
          node {
            id
            name
            producer
            kind @ppxOmitFutureValue
            color @ppxOmitFutureValue
            shop { id }
            variants(first: 50) {
              edges {
                node {
                  cku
                }
              }
            }
          }
        }
      }
    }
  `)

module VariantsQuery = %graphql(`
    query variantsByCku($cku: CKU!) {
      variantsByCku(cku: $cku, first: 50) {
        edges {
          node {
            id
            formattedStatus @ppxOmitFutureValue
            formattedName
            shop { id }
            product {
              name
              producer
              kind @ppxOmitFutureValue
              color @ppxOmitFutureValue
              variants(first: 50) {
                edges {
                  node {
                    cku
                  }
                }
              }
            }
          }
        }
      }
    }
  `)
