open Intl

module Config = CatalogProductEdit__Config
module ProductsQuery = CatalogProductEdit__Queries.ProductsQuery
module ProductQuery = CatalogProductEdit__Queries.ProductQuery

type mode =
  | Create
  | Edit

@react.component
let make = (~cku: option<string>=?, ~mode: mode) => {
  let (executeProductsQuery, productsQueryResults) = ProductsQuery.useLazy()
  let (executeProductQuery, productQueryResults) = ProductQuery.useLazy()
  let onGoBack = Navigation.useGoBack()->snd
  let notifier = Notifier.use(~clearPolicy=KeepOnHistoryChanges, ())
  let shops = Auth.useShops()
  let activeShop = Auth.useActiveShop()
  let activeShopId = activeShop->Option.map(shop => shop.id)

  // Gets products eligible for edition
  React.useEffect0(() => {
    switch cku {
    | Some(cku) if shops->Array.length > 1 =>
      executeProductsQuery(
        ProductsQuery.makeVariables(
          ~filterBy=ProductsQuery.makeInputObjectInputProductsQueryFilter(
            ~variantCkus=ProductsQuery.makeInputObjectInFilter(~_in=[cku], ()),
            ~archived=#INCLUDED,
            (),
          ),
          (),
        ),
      )
    | _ => ()
    }
    None
  })

  // Gets shop IDs for contextual information in the shop selection
  let disabledShopIds = React.useMemo1(() =>
    switch productsQueryResults {
    | Executed({data: Some({products: {edges: products}})}) =>
      shops->Array.keepMap(({id}) =>
        switch products->Array.getBy(({node: product}) => product.shop.id === id) {
        | Some(_) => None
        | _ => Some(id)
        }
      )
    | _ => []
    }
  , [productsQueryResults])

  let renderShopSelectionItem = React.useCallback0((shop: Select.item<option<Auth.shop>>) => {
    let shopName = shop.value->Option.mapWithDefault("", shop => shop.name)

    switch (shop.value->Option.isSome, shop.disabled->Option.getWithDefault(false)) {
    | (true, true) =>
      <Inline grow=true align=#spaceBetween alignY=#top>
        <TextStyle variation=#placeholder> {shopName->React.string} </TextStyle>
        <Inline grow=true align=#end>
          <TextStyle variation=#subdued size=#xsmall lineHeight=#large>
            {t("Non existing product")->React.string}
          </TextStyle>
        </Inline>
      </Inline>
    | (true, false) => <TextStyle> {shopName->React.string} </TextStyle>
    | (false, _) => <TextStyle variation=#normal> {t("Select a shop")->React.string} </TextStyle>
    }
  })

  React.useEffect1(() => {
    switch (mode, activeShop, cku) {
    | (Edit, Some(_), Some(cku)) =>
      executeProductQuery(
        ProductQuery.makeVariables(
          ~cku=cku->Scalar.CKU.serialize,
          ~filterBy=Config.makeFilterBy(~activeShopId),
          (),
        ),
      )
    | _ => ()
    }
    None
  }, [activeShopId])

  // Redirects to the product/variant page if updating product succeeded
  let onSubmitSuccess = React.useCallback0(message => {
    notifier.reset(Success(message->Option.getWithDefault("")), ())
    onGoBack()
  })
  let onSubmitFailure = React.useCallback0(message => notifier.reset(Error(message), ()))

  // Gets results from query response
  let results = Config.use(~response=productQueryResults)

  <CatalogPlaceholderShopPicker
    disabledIds=disabledShopIds
    message={template(
      t("Please select the shop in which you would like to {{action}} the reference."),
      ~values={"action": t(mode === Edit ? "edit" : "create")},
      (),
    )}
    renderItemContent=renderShopSelectionItem>
    {switch results {
    | Error => <Placeholder status=Error />
    | Loading => <Placeholder status=Loading />
    | Data({initialValues, productKind, contextData, shopName}) =>
      // TODO - adds form persistance in case user wants/has to go back to step 1
      <CatalogProductEditForm.FormLegacyProvider
        id=?{contextData->Option.map(({id}) => id)}
        initialValues
        schema=CatalogProductEditForm.schema
        onSubmitSuccess
        onSubmitFailure>
        <CatalogProductEditPage editionMode={mode === Edit} productKind contextData shopName />
      </CatalogProductEditForm.FormLegacyProvider>
    }}
  </CatalogPlaceholderShopPicker>
}

let make = React.memo(make)
