open Intl

module Config = CatalogVariantEdit__Config

exception CatalogVariantEditPage__CouldNotMakeTitle

@react.component
let make = (
  ~editionMode,
  ~formattedName,
  ~shopName,
  ~productTaxRate,
  ~productKind: CatalogProduct.Kind.t,
  ~productValues: option<CatalogProductEditForm.Lenses.state>,
) => {
  let notifier = Notifier.use()

  let renderActions = () => <CatalogVariantEditFormPageActions editionMode productValues />

  let formattedTitle = switch (editionMode, productValues) {
  | (true, None) => t("Editing") ++ " " ++ formattedName
  | (false, None) => t("Create a new variant")
  | (false, Some(_)) =>
    template(
      t("Step {{step}} | Create {{fulfill}}"),
      ~values={"step": "2", "fulfill": t("a variant")},
      (),
    )
  | _ => raise(CatalogVariantEditPage__CouldNotMakeTitle)
  }

  let formattedSubtitle = if editionMode {
    shopName
  } else {
    shopName ++ " - " ++ formattedName
  }

  <Page title=formattedTitle subtitle=formattedSubtitle renderActions>
    <Notifier.Banner notifier />
    <Box spaceTop=#large>
      <Stack space=#large>
        <CatalogVariantEditFormInformationFieldsetPanel editionMode productKind />
        {if !editionMode {
          <Stack space=#large>
            <CatalogVariantEditFormPurchasePriceTablePanel />
            <CatalogVariantEditFormRetailPriceTablePanel
              taxRate={productTaxRate->Option.getWithDefault(20.)}
            />
            <CatalogVariantEditFormStockFieldsetPanel />
          </Stack>
        } else {
          React.null
        }}
      </Stack>
    </Box>
  </Page>
}

let make = React.memo(make)
