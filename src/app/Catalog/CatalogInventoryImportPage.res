open Intl
open StyleX

module CatalogInventoryAuditRequest = {
  let endpoint = Env.gatewayUrl() ++ "/inventory-import/audit"

  let encodeBodyJson = inventoryItems => {
    let dict = Js.Dict.empty()
    let encodeItem = item => {
      let dict = Js.Dict.empty()
      dict->Js.Dict.set("variantId", item.CatalogInventoryImportSheet.variantId->Json.encodeString)
      dict->Js.Dict.set("stock", item.stock->Json.encodeNumber)
      dict->Json.encodeDict
    }

    dict->Js.Dict.set(
      "inventoryItems",
      inventoryItems->Array.map(item => encodeItem(item))->Json.encodeArray,
    )

    dict->Js.Json.object_
  }

  type serverFailure =
    | NotFoundVariant(Uuid.t)
    | InvalidCapacityPrecision(Uuid.t)
    | NotBulkProduct(Uuid.t)
    | DuplicateVariantId(Uuid.t)
    | OutOfRangeStockValue(Uuid.t)
    | Unknown

  let decodeInvalidRequestFailureDataVariantId = json =>
    Option.orElse(
      json->Json.decodeDict->Json.flatDecodeDictFieldString("id"),
      json->Json.decodeDict->Json.flatDecodeDictFieldString("variantId"),
    )->Option.flatMap(Uuid.fromString)

  let decodeInvalidRequestFailure = serverFailure =>
    switch serverFailure.Request.data->Option.flatMap(decodeInvalidRequestFailureDataVariantId) {
    | Some(variantId) =>
      switch serverFailure.kind {
      | "NotFoundVariant" => NotFoundVariant(variantId)
      | "VariantBulkStockValue" => NotBulkProduct(variantId)
      | "InsufficientCapacityPrecision" => InvalidCapacityPrecision(variantId)
      | "DuplicateVariantIdFailure" => DuplicateVariantId(variantId)
      | "OutOfRangeStockValue" => OutOfRangeStockValue(variantId)
      | _ => Unknown
      }
    | None => Unknown
    }

  let decodeError = error =>
    switch error {
    | Request.InvalidRequestFailures([])
    | UnexpectedServerError
    | ServerError(_)
    | ClientError(_)
    | MalformedResponse
    | JwtAuthenticationRedirection => [Unknown]
    | InvalidRequestFailures(invalidRequestFailure) =>
      invalidRequestFailure->Array.map(decodeInvalidRequestFailure)
    }

  let make = inventoryItems =>
    Request.make(endpoint, ~bodyJson=encodeBodyJson(inventoryItems), ~method=#POST)
    ->Future.mapOk(_ => ())
    ->Future.mapError(decodeError)
}

module CatalogInventoryImportRequest = {
  let endpoint = Env.gatewayUrl() ++ "/inventory-import"

  let encodeBodyJson = (~inventoryItems, ~inventoryDatetime=?, ()) => {
    let dict = Js.Dict.empty()
    let encodeItem = item => {
      let dict = Js.Dict.empty()
      dict->Js.Dict.set("variantId", item.CatalogInventoryImportSheet.variantId->Json.encodeString)
      dict->Js.Dict.set("stock", item.stock->Json.encodeNumber)
      dict->Json.encodeDict
    }

    dict->Js.Dict.set(
      "inventoryItems",
      inventoryItems->Array.map(item => encodeItem(item))->Json.encodeArray,
    )
    switch inventoryDatetime {
    | Some(inventoryDatetime) =>
      let timestamp = inventoryDatetime->Js.Date.getTime
      dict->Js.Dict.set("inventoryDate", timestamp->Json.encodeNumber)
    | None => ()
    }

    dict->Js.Json.object_
  }

  let make = (~inventoryItems, ~inventoryDatetime=?, ()) =>
    Request.make(
      endpoint,
      ~skipMalformedOkResult=true,
      ~bodyJson=encodeBodyJson(~inventoryItems, ~inventoryDatetime?, ()),
      ~method=#POST,
    )
}

module CatalogInventoryImportPickFileView = {
  @react.component
  let make = (~loading, ~onPickSuccess, ~onPickError) =>
    <Stack space=#medium>
      <Stack>
        <InlineText align={#center}>
          <TextStyle variation=#normal>
            {t("Before upload your files below, make sure ")->React.string}
          </TextStyle>
          <TextAction
            highlighted=true
            text={t("your file is ready to be imported.")}
            onPress={() => HelpCenter.showArticle(HelpCenter.prepareImportInventory)}
          />
        </InlineText>
        <TextStyle variation=#normal align=#center>
          {t("Drag and drop or choose a file to upload the inventory.")->React.string}
        </TextStyle>
      </Stack>
      <Inline align=#center>
        <FilePicker
          types=CatalogInventoryImportSheet.fileTypes
          maxSizeMb=10.
          onChange=onPickSuccess
          onError=onPickError>
          <Button variation=#neutral loading> {t("Pick file")->React.string} </Button>
        </FilePicker>
      </Inline>
    </Stack>
}

module CatalogInventoryImportLaunchImportView = {
  @react.component
  let make = (~filename, ~rowsCount, ~onFileClear=?) => {
    <Stack>
      <Inline align=#center>
        <TextStyle> {(t("File is ready to be imported:") ++ " ")->React.string} </TextStyle>
        <TextStyle variation=#secondary weight=#medium>
          {template(
            t("{{stockCount}} {{stocksPlural}} quantity to be updated"),
            ~values={
              "stockCount": rowsCount,
              "stocksPlural": rowsCount > 1
                ? t("stocks") ++ " " ++ t("are")
                : t("stock") ++ " " ++ t("is"),
            },
            (),
          )->React.string}
        </TextStyle>
      </Inline>
      <Inline space=#xsmall align=#center>
        <TextStyle variation=#normal size=#small> {t("File to import:")->React.string} </TextStyle>
        <TextStyle size=#small weight=#medium> {(" " ++ filename)->React.string} </TextStyle>
        {switch onFileClear {
        | Some(onFileClear) => <IconButton name=#close_medium onPress={_ => onFileClear()} />
        | None => React.null
        }}
      </Inline>
    </Stack>
  }
}

module CatalogInventoryImportSuccessImportView = {
  @react.component
  let make = (~filename, ~rowsCount) => {
    let navigate = Navigation.useNavigate()
    let onPressViewButton = _ => navigate(StockActivityRouter.baseRoute)

    <Stack space=#medium>
      <Stack>
        <TextStyle variation=#success weight=#medium align=#center>
          {template(
            t(
              "{{stockCount}} {{stocksPlural}} quantity {{haveBeenPlural}} updated in your catalog",
            ),
            ~values={
              "stockCount": rowsCount,
              "stocksPlural": rowsCount > 1 ? t("stocks") : t("stock"),
              "haveBeenPlural": rowsCount > 1 ? t("have been") : t("has been"),
            },
            (),
          )->React.string}
        </TextStyle>
        <Inline space=#xsmall align=#center>
          <TextStyle variation=#normal size=#small> {t("Imported file:")->React.string} </TextStyle>
          <TextStyle size=#small weight=#medium> {(" " ++ filename)->React.string} </TextStyle>
        </Inline>
      </Stack>
      <Inline align=#center>
        <Button variation=#neutral onPress=onPressViewButton>
          {t("View stock activities")->React.string}
        </Button>
      </Inline>
    </Stack>
  }
}

module CatalogInventoryImportFileState = {
  type payload = {
    filename: string,
    data: array<CatalogInventoryImportSheet.inventoryItem>,
  }

  type serverError = Unexpected | ErrorMessages(array<string>)
  type auditError =
    | ParseDecodeError(string)
    | MultipleParseDecodeErrors(array<string>)
    | ServerError(serverError)

  type t =
    | Audit(AsyncResult.t<payload, auditError>)
    | Import(AsyncResult.t<payload, unit>)

  let initialState = Audit(NotAsked)
}

module CatalogInventoryImportDatetimeState = {
  type t = {
    enabled: bool,
    edited: bool,
    errorMessage: option<string>,
    value: option<Js.Date.t>,
  }

  let initialState = {
    enabled: false,
    edited: false,
    errorMessage: None,
    value: None,
  }
}

module CatalogInventoryImportReducer = {
  open ReactUpdateReducer

  type notification = {
    textStatus: Banner.textStatus,
    details?: array<string>,
  }
  type state = {
    submitted: bool,
    errored: bool,
    notification: option<notification>,
    sheetFile: option<CatalogInventoryImportFileState.payload>,
    fileState: CatalogInventoryImportFileState.t,
    datetimeState: CatalogInventoryImportDatetimeState.t,
  }

  type action =
    | SetNotificationRequested(option<notification>)
    | ImportSubmitted(CatalogInventoryImportFileState.payload)
    | SetFileProcessed(CatalogInventoryImportFileState.t)
    | BackdateToggled(bool)
    | DateUpdated(Js.Date.t)
    | DatetimeUpdated(Js.Date.t)
    | ResetRequested
    | DatetimeValidationRequested
    | ValidationRequested

  let validateDatetimeState = state =>
    switch state {
    | {CatalogInventoryImportDatetimeState.enabled: false} => Ok()
    | {enabled: true, value: Some(datetime), edited: true} =>
      let now = Js.Date.make()
      let validated = datetime->Js.Date.getTime <= now->Js.Date.getTime
      validated ? Ok() : Error(t("The time must be earlier"))
    | {enabled: true, edited: false} | {enabled: true, value: None} => Error(t("Required field"))
    }

  let initialState = {
    submitted: false,
    errored: false,
    notification: None,
    sheetFile: None,
    fileState: CatalogInventoryImportFileState.initialState,
    datetimeState: CatalogInventoryImportDatetimeState.initialState,
  }

  let reducer = (state, action) =>
    switch action {
    | SetNotificationRequested(notification) =>
      Update({
        ...state,
        notification,
      })
    | ImportSubmitted(payload) =>
      UpdateWithSideEffects(
        {
          ...state,
          sheetFile: Some(payload),
        },
        ({dispatch}) => {
          dispatch(ValidationRequested)
          None
        },
      )
    | SetFileProcessed(fileState) =>
      UpdateWithSideEffects(
        {
          ...state,
          fileState,
        },
        self => {
          let notification = switch self.state.fileState {
          | Audit(Done(Ok(_))) =>
            Some({
              textStatus: Info(
                t("The inventory file is ready. Click on \"Launch import\" to import your file."),
              ),
            })
          | Audit(Done(Error(error))) =>
            self.dispatch(ResetRequested)
            switch error {
            | MultipleParseDecodeErrors(errorMessages)
            | ServerError(ErrorMessages(errorMessages)) =>
              Some({
                textStatus: Danger(
                  t(
                    "The inventory file is incorrect and cannot be processed, several data items are incorrect.",
                  ),
                ),
                details: errorMessages,
              })
            | ParseDecodeError(errorMessage) => Some({textStatus: Danger(errorMessage)})
            | ServerError(Unexpected) =>
              Some({
                textStatus: Danger(
                  t("Wino could not proceed to the inventory import, please try again."),
                ),
              })
            }
          | Import(Done(Ok(_))) =>
            Some({
              textStatus: Success(
                t(
                  "The inventory file has been successfully imported! Please wait a few minutes before synchronizing your iPads.",
                ),
              ),
            })
          | Import(Done(Error())) =>
            self.dispatch(ResetRequested)
            Some({
              textStatus: Danger(
                t("Wino could not proceed to the inventory import, please try again."),
              ),
            })
          | _ => None
          }
          self.dispatch(SetNotificationRequested(notification))
          None
        },
      )
    | BackdateToggled(enabled) =>
      let defaultDatetime = enabled ? Some(Js.Date.make()) : None
      let datetimeState = {
        CatalogInventoryImportDatetimeState.enabled,
        edited: false,
        errorMessage: None,
        value: defaultDatetime,
      }

      Update({
        ...state,
        datetimeState,
      })
    | DateUpdated(nextDate) =>
      let date = state.datetimeState.value->Option.getWithDefault(Js.Date.make())
      let nextDate = Js.Date.makeWithYMDHM(
        ~year=nextDate->Js.Date.getFullYear,
        ~month=nextDate->Js.Date.getMonth,
        ~date=nextDate->Js.Date.getDate,
        ~hours=date->Js.Date.getHours,
        ~minutes=date->Js.Date.getMinutes,
        (),
      )
      let datetimeState = {
        ...state.datetimeState,
        value: Some(nextDate),
      }

      UpdateWithSideEffects(
        {
          ...state,
          datetimeState,
        },
        self => {
          if self.state.datetimeState.edited {
            self.dispatch(DatetimeValidationRequested)
          }
          None
        },
      )
    | DatetimeUpdated(nextDatetime) =>
      let date = state.datetimeState.value->Option.getWithDefault(Js.Date.make())
      let datetime = Js.Date.makeWithYMDHM(
        ~year=date->Js.Date.getFullYear,
        ~month=date->Js.Date.getMonth,
        ~date=date->Js.Date.getDate,
        ~hours=nextDatetime->Js.Date.getHours,
        ~minutes=nextDatetime->Js.Date.getMinutes,
        (),
      )
      let datetimeState = {
        ...state.datetimeState,
        edited: true,
        value: Some(datetime),
      }

      UpdateWithSideEffects(
        {
          ...state,
          datetimeState,
        },
        self => {
          self.dispatch(DatetimeValidationRequested)
          None
        },
      )
    | ResetRequested =>
      Update({
        submitted: false,
        errored: false,
        notification: None,
        sheetFile: None,
        fileState: CatalogInventoryImportFileState.initialState,
        datetimeState: CatalogInventoryImportDatetimeState.initialState,
      })
    | DatetimeValidationRequested =>
      let validated = validateDatetimeState(state.datetimeState)
      let errorMessage = switch validated {
      | Error(errorMessage) => Some(errorMessage)
      | Ok() => None
      }
      let datetimeState = {
        ...state.datetimeState,
        errorMessage,
      }

      Update({
        ...state,
        submitted: false,
        datetimeState,
      })
    | ValidationRequested =>
      let validated = validateDatetimeState(state.datetimeState)
      let errored = Result.isError(validated)

      UpdateWithSideEffects(
        {
          ...state,
          submitted: true,
          errored,
        },
        self => {
          let errorNotificationText = {
            textStatus: Danger(
              t(
                "There are some errors in the form, please correct them before trying to send it again.",
              ),
            ),
          }

          if errored {
            self.dispatch(DatetimeValidationRequested)
            self.dispatch(SetNotificationRequested(Some(errorNotificationText)))
          } else {
            self.dispatch(SetFileProcessed(Import(Loading)))
          }
          None
        },
      )
    }
}

module CatalogInventoryImportFileCard = {
  let styles = StyleX.create({
    "root": style(
      ~backgroundColor=Colors.neutralColor10,
      ~justifyContent=#center,
      ~alignItems=#center,
      ~padding="28px",
      ~border="1px solid " ++ Colors.neutralColor15,
      ~borderRadius="5px",
      (),
    ),
  })

  @react.component
  let make = (~state, ~sheetFile, ~onAcceptFile, ~onRejectFile, ~onResetPress) => {
    let dropFileDisabled = switch state {
    | CatalogInventoryImportFileState.Audit(NotAsked) | Import(Done(Ok(_))) => false
    | _ => true
    }

    <Card title={t("Inventory file")}>
      <DropZone
        types=CatalogInventoryImportSheet.fileTypes
        maxSizeMb=10.
        customSubtitle={t("to validate it then to import it")}
        disabled=dropFileDisabled
        onSuccess=onAcceptFile
        onError=onRejectFile>
        <DivX style={StyleX.props([styles["root"]])}>
          {switch state {
          | Audit((NotAsked | Loading) as asyncResult) =>
            let loading = asyncResult->AsyncResult.isBusy
            <CatalogInventoryImportPickFileView
              loading onPickSuccess=onAcceptFile onPickError=onRejectFile
            />
          | Audit(Done(Ok({filename, data: rows}))) =>
            <CatalogInventoryImportLaunchImportView
              filename rowsCount={rows->Array.length} onFileClear=onResetPress
            />
          | Import(Loading | Reloading(_)) =>
            let (filename, rows) =
              sheetFile->Option.mapWithDefault(("?", []), payload => (
                payload.CatalogInventoryImportFileState.filename,
                payload.data,
              ))
            <CatalogInventoryImportLaunchImportView filename rowsCount={rows->Array.length} />
          | Import(Done(Ok({filename, data: rows}))) =>
            <CatalogInventoryImportSuccessImportView filename rowsCount={rows->Array.length} />
          | Audit(Reloading(_) | Done(Error(_)))
          | Import(NotAsked | Done(Error(_))) => React.null
          }}
        </DivX>
      </DropZone>
    </Card>
  }
}

module CatalogInventoryImportDatetimeCard = {
  open CatalogInventoryImportDatetimeState
  @react.component
  let make = (~state, ~onToggle, ~onDateChange, ~onDatetimeChange) =>
    <Card grow=true title={t("Import option")}>
      <Stack space=#medium>
        <Tooltip
          content={<Tooltip.Span
            text={t(
              "To view inventory movements following a backdated inventory, filter the movement table by the previous date entered.",
            )}
          />}
          delay=300
          closeDelay=0
          placement=#end
          offset=3.>
          <InputToggleSwitchField
            label={t("Import at an earlier datetime")}
            required=false
            value=state.enabled
            onChange=onToggle
          />
        </Tooltip>
        {if state.enabled {
          <Stack space=#medium>
            <InputDateField
              label={t("Date")} disabledFutureDays=true value=state.value onChange=onDateChange
            />
            <InputTimeField
              label={t("Time")}
              errorMessage=?state.errorMessage
              required=true
              value=None
              onChange=onDatetimeChange
            />
          </Stack>
        } else {
          <InlineText>
            <TextStyle variation=#normal size=#small>
              {t("By default, inventory is imported at time")->React.string}
            </TextStyle>
            <TextStyle variation=#subdued size=#small> {" t."->React.string} </TextStyle>
          </InlineText>
        }}
      </Stack>
    </Card>
}

module CatalogInventoryImportPrecautionCard = {
  let text = `Please be aware that the action of importing inventory is irreversible. Before launching the import of the loaded file, make sure you have taken all the necessary precautions:

1. You have verified that you've entered accurate stock quantities in your import file.
2. You have checked that the inventory date for your file corresponds to the import options defined in the interface.
3. You have synchronized all of the store's iPads, and then you have not made any further stock movements on the devices until the inventory has been imported.`

  @react.component
  let make = () =>
    <Card grow=true title={t("Read precautions before importing")}>
      <TextStyle> {t(text)->React.string} </TextStyle>
      <TextAction
        text={t("Consult the tutorial dedicated to inventory import")}
        highlighted=true
        onPress={() => HelpCenter.showArticle(HelpCenter.prepareImportInventory)}
      />
    </Card>
}

let arrayChunks = (value, ~size) =>
  if value->Array.size > 0 {
    if size > 0 {
      Array.makeBy(Js.Math.ceil_int(value->Array.length->Int.toFloat /. size->Int.toFloat), index =>
        value->Array.slice(~len=size, ~offset=index * size)
      )
    } else {
      [value]
    }
  } else {
    []
  }

let catalogInventoryQueryChunkSize = 2500

let queryCatalogInventoryImport = (
  ~catalogInventoryImportRequest,
  ~catalogInventoryQueryChunkSize,
  ~inventoryItems,
  ~inventoryDatetime=?,
  (),
) =>
  arrayChunks(inventoryItems, ~size=catalogInventoryQueryChunkSize)
  ->Array.map(inventoryItems => catalogInventoryImportRequest(inventoryItems, inventoryDatetime))
  ->Future.all
  ->Future.map(results =>
    if results->Array.every(Result.isOk) {
      Ok()
    } else {
      Error()
    }
  )

let queryCatalogInventoryAudit = (
  ~catalogInventoryAuditRequest,
  ~catalogInventoryQueryChunkSize,
  ~inventoryItems,
) =>
  arrayChunks(inventoryItems, ~size=catalogInventoryQueryChunkSize)
  ->Array.map(catalogInventoryAuditRequest)
  ->Future.all
  ->Future.map(results =>
    if results->Array.every(Result.isOk) {
      Ok()
    } else {
      let errors = results->Array.reduce([], (acc, result) =>
        switch result {
        | Error(errors) => acc->Array.concat(errors)
        | Ok() => acc
        }
      )
      Error(errors)
    }
  )

let {dictVariantIdKey, dictStockKey} = module(CatalogInventoryImportSheet)

let mapQueryCatalogInventorySheetDecodeRowError = errors => {
  let traceFromRowError = (~json, ~index) => {
    let values = {"json": json->Json.stringify, "index": (index + 1)->Int.toString}
    let stringLiteral = t("{{json}} at line {{index}}")
    template(stringLiteral, ~values, ())
  }

  errors->Array.map(error =>
    switch error {
    | CatalogInventoryImportSheet.CannotDecodeHeaderRow({json, index})
    | CannotDecodeRow({json, index}) =>
      let trace = traceFromRowError(~json, ~index)
      let stringLiteral = t("Cannot decode {{trace}}")
      template(stringLiteral, ~values={"trace": trace}, ())
    | CannotDecodeCellVariantId({json, index}) =>
      let trace = traceFromRowError(~json, ~index)
      let stringLiteral = t("Incorrect reference ID {{trace}}")
      template(stringLiteral, ~values={"trace": trace}, ())
    | CannotDecodeCellStock({json, index}) =>
      let trace = traceFromRowError(~json, ~index)
      let stringLiteral = t("Incorrect inventory quantity {{trace}}")
      template(stringLiteral, ~values={"trace": trace}, ())
    | EmptyCellStock({index}) =>
      let stringLiteral = t("Inventory quantity missing at line {{line}}")
      template(stringLiteral, ~values={"line": index + 1}, ())
    | EmptyCellVariantId({index}) =>
      let stringLiteral = t("Reference ID missing at line {{line}}")
      template(stringLiteral, ~values={"line": index + 1}, ())
    | NegativeCellStock({json, index}) =>
      let trace = traceFromRowError(~json, ~index)
      let stringLiteral = t("Negative inventory quantity {{trace}}")
      template(stringLiteral, ~values={"trace": trace}, ())
    }
  )
}

let mapQueryCatalogInventorySheetParseAndDecodeError = error =>
  switch error {
  | CatalogInventoryImportSheet.NoRows =>
    t(
      "Please ensure that the inventory file is correct: " ++
      "it is currently empty, no rows have been found." ++ "",
    )
  | InvalidHeaderRow =>
    let message = t(
      "Please ensure that the inventory file is correct. " ++
      "Errors have been identified on the header row: " ++
      "the columns '{{A}}' and/or '{{B}}' cannot be found." ++ "",
    )
    template(t(message), ~values={"A": dictVariantIdKey, "B": dictStockKey}, ())
  | Unknown =>
    let message = "Please ensure that the inventory file is correct, as an error has been reported during its analysis."
    t(message)
  | EmptyColumnStock =>
    let message = "Please ensure that the inventory file is correct, the '{{column}}' column is empty."
    template(t(message), ~values={"column": dictStockKey}, ())
  }

let mapQueryCatalogInventoryAuditError = (error, ~headerOffset, ~inventoryItems) => {
  let formateErrorMessage = (variantId, ~errorMessageTemplate, ~errorMessageTemplateTrace) => {
    let variantIdStr = variantId->Uuid.toString
    let index =
      inventoryItems->Array.getIndexBy(({
        CatalogInventoryImportSheet.variantId: inventoryVariantItem,
      }) => inventoryVariantItem === variantIdStr)

    switch index {
    | Some(index) =>
      let traceValues = {
        "json": variantIdStr->Json.encodeString->Json.stringify,
        "index": (index + 1 + headerOffset)->Int.toString,
      }
      let trace = template(errorMessageTemplateTrace, ~values=traceValues, ())
      Some(template(errorMessageTemplate, ~values={"trace": trace}, ()))
    | None => None
    }
  }

  let mappedErrors = error->Array.keepMap(error =>
    switch error {
    | CatalogInventoryAuditRequest.NotFoundVariant(variantId) =>
      let errorMessageTemplateTrace = t("{{json}} at line {{index}}")
      let errorMessageTemplate = t("Incorrect reference ID {{trace}}")
      formateErrorMessage(variantId, ~errorMessageTemplate, ~errorMessageTemplateTrace)
    | NotBulkProduct(variantId) =>
      let errorMessageTemplateTrace = t("at line {{index}}")
      let errorMessageTemplate = t(
        "Invalid product inventory quantity {{trace}}, product is unit and its quantity must be an integer.",
      )
      formateErrorMessage(variantId, ~errorMessageTemplate, ~errorMessageTemplateTrace)
    | InvalidCapacityPrecision(variantId) =>
      let errorMessageTemplateTrace = t("at line {{index}}")
      let errorMessageTemplate = t(
        "Incorrect or corrupted inventory quantity at decimals {{trace}}, please re-enter the value.",
      )
      formateErrorMessage(variantId, ~errorMessageTemplate, ~errorMessageTemplateTrace)
    | DuplicateVariantId(variantId) =>
      let errorMessageTemplateTrace = t("{{json}} at line {{index}}")
      let errorMessageTemplate = t(
        "several occurrences of the ID {{trace}} are present in the file.",
      )
      formateErrorMessage(variantId, ~errorMessageTemplate, ~errorMessageTemplateTrace)
    | OutOfRangeStockValue(variantId) =>
      let errorMessageTemplateTrace = t("at line {{index}}")
      let errorMessageTemplate = t("Incorrect inventory quantity {{trace}}")
      formateErrorMessage(variantId, ~errorMessageTemplate, ~errorMessageTemplateTrace)
    | Unknown => None
    }
  )

  if mappedErrors->Array.size === 0 {
    None
  } else {
    Some(mappedErrors)
  }
}

let catalogInventoryImportRequest = (inventoryItems, inventoryDatetime) =>
  CatalogInventoryImportRequest.make(~inventoryItems, ~inventoryDatetime?, ())

let catalogInventoryAuditRequest = inventoryItems =>
  CatalogInventoryAuditRequest.make(inventoryItems)

// REVIEW — separating the effects of file processing in sub functions?
let catalogInventoryProcessAudit = (value, ~file, ~dispatch) =>
  value
  ->Future.flatMap(({CatalogInventoryImportSheet.headerOffset: headerOffset, payload}) =>
    switch payload {
    | Error(error) =>
      let parseDecodeError = mapQueryCatalogInventorySheetParseAndDecodeError(error)
      Error(CatalogInventoryImportFileState.ParseDecodeError(parseDecodeError))
    | Ok(rows) =>
      let okRows = rows->Array.keepMap(row =>
        switch row {
        | Ok(row: CatalogInventoryImportSheet.inventoryItem) =>
          let sanitizedStock = row.stock->Js.Float.toFixedWithPrecision(~digits=3)
          let stock = sanitizedStock->Float.fromString->Option.getWithDefault(row.stock)
          Some({...row, stock})
        | Error(_) => None
        }
      )
      let erroredRows = rows->Array.keepMap(row =>
        switch row {
        | Error(error) => Some(error)
        | Ok(_) => None
        }
      )
      if erroredRows->Array.size > 0 {
        Error(MultipleParseDecodeErrors(mapQueryCatalogInventorySheetDecodeRowError(erroredRows)))
      } else {
        Ok({
          CatalogInventoryImportSheet.headerOffset,
          payload: okRows,
        })
      }
    }->Future.value
  )
  ->Future.flatMapOk(({headerOffset, payload: inventoryItems}) => {
    queryCatalogInventoryAudit(
      ~catalogInventoryAuditRequest,
      ~catalogInventoryQueryChunkSize,
      ~inventoryItems,
    )
    ->Future.mapOk(() => {
      CatalogInventoryImportFileState.filename: file->File.name,
      data: inventoryItems,
    })
    ->Future.mapError(error => {
      let maybeErrorMessage = mapQueryCatalogInventoryAuditError(
        error,
        ~headerOffset,
        ~inventoryItems,
      )
      switch maybeErrorMessage {
      | Some(errorMessages) =>
        CatalogInventoryImportFileState.ServerError(ErrorMessages(errorMessages))
      | None => CatalogInventoryImportFileState.ServerError(Unexpected)
      }
    })
  })
  ->Future.map(data => dispatch(CatalogInventoryImportReducer.SetFileProcessed(Audit(Done(data)))))
  ->ignore

@react.component
let make = () => {
  let (state, dispatch) = ReactUpdateReducer.use(
    CatalogInventoryImportReducer.reducer,
    CatalogInventoryImportReducer.initialState,
  )

  React.useEffect1(() => {
    switch (state.submitted, state.errored, state.sheetFile) {
    | (true, false, Some({filename, data: inventoryItems})) =>
      queryCatalogInventoryImport(
        ~catalogInventoryImportRequest,
        ~catalogInventoryQueryChunkSize,
        ~inventoryItems,
        ~inventoryDatetime=?state.datetimeState.value,
        (),
      )->Future.get(result =>
        switch result {
        | Ok() => dispatch(SetFileProcessed(Import(Done(Ok({filename, data: inventoryItems})))))
        | Error() => dispatch(SetFileProcessed(Import(Done(Error()))))
        }
      )
    | _ => ()
    }
    None
  }, [state.submitted])

  let onAcceptFile = file => {
    dispatch(CatalogInventoryImportReducer.SetFileProcessed(Audit(Loading)))
    switch file->File.type_->FilePicker.typeFromMime {
    | Ok(#csv) =>
      CatalogInventoryImportSheet.parseAndDecodeCsvFile(file)->catalogInventoryProcessAudit(
        ~file,
        ~dispatch,
      )
    | Ok(#spreadsheetml) =>
      CatalogInventoryImportSheet.parseAndDecodeXlsxFile(
        ~headerIndexOffset=2,
        file,
      )->catalogInventoryProcessAudit(~file, ~dispatch)
    | _ => ()
    }
  }
  let onRejectFile = message =>
    dispatch(SetNotificationRequested(Some({textStatus: Danger(message)})))

  let onToggleBackdateView = enabled => dispatch(BackdateToggled(enabled))
  let onDateChange = date => dispatch(DateUpdated(date))
  let onDatetimeChange = datetime => dispatch(DatetimeUpdated(datetime))

  let onSubmitImport = (inventoryItems, filename) =>
    dispatch(ImportSubmitted({filename, data: inventoryItems}))

  let onReset = () => dispatch(ResetRequested)
  let onClearNotification = () => dispatch(SetNotificationRequested(None))

  let pageActions = switch state.fileState {
  | Audit(Done(Ok({filename, data}))) =>
    <Button variation=#primary onPress={_ => onSubmitImport(data, filename)}>
      {t("Launch import")->React.string}
    </Button>
  | Import(status) if status->AsyncResult.isBusy =>
    <Button variation=#primary loading=true> {t("Launch import")->React.string} </Button>
  | Import(Done(Ok(_))) =>
    <Button variation=#neutral onPress={_ => onReset()}>
      {t("Load new inventory")->React.string}
    </Button>
  | _ => React.null
  }

  let shouldBlockOnRouteChange = React.useCallback1(_nextRoute =>
    switch state.fileState {
    | Import(Loading) => true
    | _ => false
    }
  , [state.fileState])

  let formattedTitle = switch state.fileState {
  | Audit(NotAsked | Loading | Reloading(_)) => t("Step 1 | Validate the inventory file")
  | Audit(Done(_)) | Import(NotAsked | Loading | Reloading(_)) =>
    t("Step 2 | Import the inventory file")
  | Import(Done(_)) => t("Import finished")
  }

  <Page title=formattedTitle renderActions={() => pageActions}>
    <Navigation.Prompt
      message={t("Warning: the inventory import operation is still in progress.")}
      shouldBlockOnRouteChange
    />
    {switch state.notification {
    | Some({textStatus, ?details}) =>
      <Box spaceTop=#medium>
        <Banner textStatus ?details onRequestClose=onClearNotification />
      </Box>
    | None => React.null
    }}
    <Box spaceTop=#large>
      <Stack space=#medium>
        <CatalogInventoryImportFileCard
          state=state.fileState
          sheetFile=state.sheetFile
          onAcceptFile
          onRejectFile
          onResetPress=onReset
        />
        <Group wrap=false grid=["30%", "70%"] spaceX=#medium>
          <CatalogInventoryImportDatetimeCard
            state=state.datetimeState onToggle=onToggleBackdateView onDateChange onDatetimeChange
          />
          <CatalogInventoryImportPrecautionCard />
        </Group>
      </Stack>
    </Box>
  </Page>
}

let make = React.memo(make)
