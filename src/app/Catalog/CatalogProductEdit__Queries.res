module ProductsQuery = %graphql(`
  query products($filterBy: InputProductsQueryFilter) {
    products(filterBy: $filterBy) {
      edges {
        node {
          shop {
            id
          }
        }
      }
    }
  }
`)

module ProductQuery = %graphql(`
  query ProductQuery($cku: CKU!, $filterBy: InputProductsByVariantCkuQueryFilter) {
    productsByVariantCku(cku: $cku, first: 1, filterBy: $filterBy) {
      edges {
        node {
          id
          name
          kind @ppxOmitFutureValue
          color @ppxOmitFutureValue
          producer
          family
          designation
          country
          region
          beerType
          wineType @ppxOmitFutureValue
          whiteWineType @ppxOmitFutureValue
          tax {
            id
            value
          }
          category {
            id
            formattedName
          }
        }
      }
    }
  }
`)
