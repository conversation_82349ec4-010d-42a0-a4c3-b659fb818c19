open Intl
open CatalogDuplication

module Config = CatalogDuplication__Config

@react.component
let make = (
  ~cku: string,
  ~references: array<Config.reference>,
  ~duplicationMode: Mode.t,
  ~shopsData: array<Shop.t>,
) => {
  let state = CatalogDuplicationForm.useFormState()
  let notifier = Notifier.use()

  // Clears impossibility duplication notification when the source selection change
  ReactUpdateEffect.use1(() => {
    notifier.clear()
    None
  }, [state.values.source])

  let renderActions = () => <CatalogDuplicationFormPageActions duplicationMode />

  let title = switch duplicationMode {
  | Variant => t("Duplicate the variant")
  | Product => t("Duplicate the product and its variants")
  }
  let subtitle = React.useMemo2(() =>
    switch state.values.source {
    | Some(
        Product({shopId: Some(shopId)})
        | Variant({shopId: Some(shopId)}),
      ) =>
      references->Array.getBy(reference => reference.shopId === shopId)
    | _ => references[0]
    }->Option.mapWithDefault("...", ({formattedName}) => formattedName)
  , (references, state.values.source))

  // Gets the reference matching the selected source shopId
  let reference: option<Config.reference> = React.useMemo1(() =>
    switch state.values.source {
    | Some(
        Product({shopId: Some(shopId)})
        | Variant({shopId: Some(shopId)}),
      ) =>
      references->Array.getBy(reference => reference.shopId === shopId)
    | _ => None
    }
  , [state.values.source])

  <Page title subtitle renderActions>
    <Notifier.Banner notifier />
    <Box spaceBottom=#xxlarge />
    <Stack space=#xxlarge>
      <Stack space=#medium>
        <Group wrap=false grid=["25%", "75%"] spaceX=#xlarge>
          <Stack space=#small>
            <Title level=#3 weight=#strong> {t("Shop catalog to duplicate")->React.string} </Title>
            <TextStyle variation=#normal>
              {t(
                "Select the shop catalog from which the " ++
                (switch duplicationMode {
                | Product => "product and its variants"
                | Variant => "variant"
                } ++
                " will be duplicated."),
              )->React.string}
            </TextStyle>
          </Stack>
          <CatalogDuplicationFormSourceCard
            shopsData
            references={references->Array.map(({id, shopId}) => {
              open CatalogDuplicationFormSourceCard
              {id, shopId}
            })}
            duplicationMode
          />
        </Group>
        <Group wrap=false grid=["25%", "75%"] spaceX=#xlarge>
          <Stack space=#small>
            <Title level=#3 weight=#strong> {t("Shop catalog to duplicate")->React.string} </Title>
            <TextStyle variation=#normal>
              {t(
                "Select the shop catalog from which the " ++
                (switch duplicationMode {
                | Product => "product and its variants"
                | Variant => "variant"
                } ++
                " will be duplicated."),
              )->React.string}
            </TextStyle>
          </Stack>
          <CatalogDuplicationFormSelectionTableCard
            reference={switch reference {
            | Some(reference) =>
              Some({
                variantsCku: reference.variantsCku,
                productName: reference.productName,
                productKind: reference.productKind,
                productColor: reference.productColor,
                productProducer: reference.productProducer,
              })
            | _ => None
            }}
            duplicationMode
            onError={duplicationStatus =>
              notifier.reset(
                Warning(
                  t(
                    switch (duplicationMode, duplicationStatus) {
                    | (
                        Product,
                        _,
                      ) => "The product of the target catalog cannot be duplicated as its reference already exists inside the catalogs of all your shops."
                    | (Variant, MissingProduct) =>
                      template(
                        t(
                          "You cannot duplicate the product variant only. It is necessary to duplicate the whole product. [Duplicate product]({{link}})",
                        ),
                        ~values={
                          "link": `/catalog/product/duplication/${cku}`,
                        },
                        (),
                      )
                    | (
                        Variant,
                        _,
                      ) => "The variant of the target catalog cannot be duplicated because no reference matches the duplication criteria."
                    },
                  ),
                ),
                (),
              )}
          />
        </Group>
      </Stack>
    </Stack>
  </Page>
}

let make = React.memo(make)
