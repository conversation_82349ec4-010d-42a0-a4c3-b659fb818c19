// NOTE - this page contains 2 main different views with some shared parts:
// - Sheet: the user can generate and download a PDF sheet of the labels
// - Print: the user can request directly a print of the labels instead of downloading
// - Print: (settings only) the user can only see the options to setup the printing settings

open Intl

module CatalogLabelCreateProductsValue = {
  type productInformation = {
    name: string,
    description: string,
    internalCode: option<string>,
    stockKeepingUnit: option<string>,
  }
  type pickedItem = {
    productVariantId: string,
    printingQuantity: int,
    productInformation: productInformation,
  }
  type t = Picked(array<pickedItem>) | All

  let initial = Picked([])
}

module CatalogLabelCreateLabelSheetForm = {
  type submitResult = AsyncResult.t<CatalogLabel.RequestSheetLabelsType.t, string>

  type errorMessages = {
    printOffset?: string,
    productValues?: string,
  }

  type values = {
    priceId: Uuid.t,
    productBarcodeDisplayed: bool,
    missingProductBarcodeGenerated: bool,
    priceDisplayed: bool,
    producerDisplayed: bool,
    alcoholVolumeDisplayed: bool,
    productCode: CatalogLabel.ProductCode.t,
    labelFormat: CatalogLabel.Sheet.LabelFormat.t,
    printOffset: int,
    borderEnabled: bool,
    sort: CatalogLabel.Sheet.LabelSort.t,
    productValues: CatalogLabelCreateProductsValue.t,
  }

  type t = {
    submitResult: submitResult,
    values: values,
    errorMessages: errorMessages,
  }

  let initialValues = (
    ~priceId,
    ~productBarcodeDisplayed=false,
    ~missingProductBarcodeGenerated=false,
    ~priceDisplayed=true,
    ~producerDisplayed=false,
    ~alcoholVolumeDisplayed=false,
    ~productCode=CatalogLabel.ProductCode.Hidden,
    ~labelFormat=CatalogLabel.Sheet.LabelFormat.Grid21,
    ~printOffset=1,
    ~borderEnabled=false,
    ~sort=CatalogLabel.Sheet.LabelSort.Unsorted,
    (),
  ) => {
    priceId,
    productBarcodeDisplayed,
    missingProductBarcodeGenerated,
    priceDisplayed,
    producerDisplayed,
    alcoholVolumeDisplayed,
    productCode,
    labelFormat,
    printOffset,
    borderEnabled,
    sort,
    productValues: CatalogLabelCreateProductsValue.initial,
  }

  let errorMessagesFromValues = values => {
    let {printOffset, labelFormat, productValues} = values
    let maxOffset = CatalogLabel.Sheet.LabelFormat.toMaxOffset(labelFormat)

    {
      printOffset: ?switch labelFormat {
      | Grid64 if printOffset > maxOffset || printOffset < 1 =>
        Some(
          template(
            t("This number must range between {{min}} and {{max}} inclusive."),
            ~values={"min": 1, "max": maxOffset},
            (),
          ),
        )
      | Grid21 if printOffset > maxOffset || printOffset < 1 =>
        Some(
          template(
            t("This number must range between {{min}} and {{max}} inclusive."),
            ~values={"min": 1, "max": maxOffset},
            (),
          ),
        )
      | _ => None
      },
      productValues: ?switch productValues {
      | Picked([]) => Some(t("Please select at least one product."))
      | Picked(_) | All => None
      },
    }
  }
}

module CatalogLabelCreateLabelPrintForm = {
  type submitResult = AsyncResult.t<unit, string>

  type settingsOnly = {
    fromPathname: string,
    variantIdFromCatalogRedirection: string,
  }

  type errorMessages = {productValues?: string}

  type values = {
    priceId: Uuid.t,
    productBarcodeDisplayed: bool,
    missingProductBarcodeGenerated: bool,
    priceDisplayed: bool,
    producerDisplayed: bool,
    alcoholVolumeDisplayed: bool,
    productCode: CatalogLabel.ProductCode.t,
    labelFormat: CatalogLabel.Print.LabelFormat.t,
    productValues: CatalogLabelCreateProductsValue.t,
  }

  type t = {
    settingsOnly: option<settingsOnly>,
    submitResult: submitResult,
    values: values,
    errorMessages: errorMessages,
  }

  let initialValues = (
    ~priceId,
    ~productBarcodeDisplayed=false,
    ~missingProductBarcodeGenerated=false,
    ~priceDisplayed=true,
    ~producerDisplayed=false,
    ~alcoholVolumeDisplayed=false,
    ~productCode=CatalogLabel.ProductCode.Hidden,
    ~labelFormat=CatalogLabel.Print.LabelFormat.Label31x22,
    (),
  ) => {
    priceId,
    productBarcodeDisplayed,
    missingProductBarcodeGenerated,
    priceDisplayed,
    producerDisplayed,
    alcoholVolumeDisplayed,
    productCode,
    labelFormat,
    productValues: CatalogLabelCreateProductsValue.initial,
  }

  let errorMessagesFromValues = values => {
    productValues: ?switch values.productValues {
    | Picked([]) => Some(t("Please select at least one product."))
    | Picked(_) | All => None
    },
  }
}

type form =
  | LabelSheet(CatalogLabelCreateLabelSheetForm.t)
  | LabelPrint(CatalogLabelCreateLabelPrintForm.t)

type priceItem = {
  id: Uuid.t,
  name: string,
  default: bool,
}

type state = {
  priceList: array<priceItem>,
  form: form,
}

module CatalogLabelCreateReducer = {
  type state = state
  type action =
    | LabelSheetEdited(CatalogLabelCreateLabelSheetForm.values)
    | LabelSheetProductsUpdated(
        CatalogLabelCreateProductsValue.t => CatalogLabelCreateProductsValue.t,
      )
    | LabelPrintEdited(CatalogLabelCreateLabelPrintForm.values)
    | LabelPrintProductsUpdated(
        CatalogLabelCreateProductsValue.t => CatalogLabelCreateProductsValue.t,
      )
    | GenerateLabelsSubmitted(CatalogLabelCreateLabelSheetForm.submitResult)
    | PrintLabelsSubmitted(CatalogLabelCreateLabelPrintForm.submitResult)
    | SaveSettingsSubmitted

  let validationErrorText = t(
    "There are some errors in the form, please correct them before trying to send it again.",
  )

  let isErrored = state =>
    switch state.form {
    | LabelSheet({submitResult: Loading | Done(Error(_)), errorMessages}) =>
      Option.isSome(errorMessages.printOffset) || Option.isSome(errorMessages.productValues)
    | LabelPrint({submitResult: Loading | Done(Error(_)), errorMessages, settingsOnly: None}) =>
      Option.isSome(errorMessages.productValues)
    | _ => false
    }

  let isPrintMode = state =>
    switch state.form {
    | LabelSheet(_) => false
    | LabelPrint(_) => true
    }

  let isSettingsOnlyMode = state =>
    switch state.form {
    | LabelSheet(_) => false
    | LabelPrint({settingsOnly}) => settingsOnly->Option.isSome
    }

  let defaultPriceIdFromPriceList = priceList => {
    let defaultPriceItem = priceList->Array.getBy(priceItem => priceItem.default)
    let defaultPriceId = switch (defaultPriceItem, priceList[0]) {
    | (Some(priceItem), _) | (_, Some(priceItem)) => Some(priceItem.id)
    | (None, None) => None
    }
    defaultPriceId
  }

  let initializeStateLabelSheet = (
    ~priceList,
    ~priceId=?,
    ~productBarcodeDisplayed=?,
    ~missingProductBarcodeGenerated=?,
    ~priceDisplayed=?,
    ~producerDisplayed=?,
    ~alcoholVolumeDisplayed=?,
    ~productCode=?,
    ~labelFormat=?,
    ~printOffset=?,
    ~borderEnabled=?,
    ~sort=?,
    (),
  ) => {
    let priceId =
      Option.orElse(priceId, defaultPriceIdFromPriceList(priceList))->Option.getWithDefault(
        Uuid.make(),
      )
    {
      priceList,
      form: LabelSheet({
        submitResult: NotAsked,
        values: CatalogLabelCreateLabelSheetForm.initialValues(
          ~priceId,
          ~productBarcodeDisplayed?,
          ~missingProductBarcodeGenerated?,
          ~priceDisplayed?,
          ~producerDisplayed?,
          ~alcoholVolumeDisplayed?,
          ~productCode?,
          ~labelFormat?,
          ~printOffset?,
          ~borderEnabled?,
          ~sort?,
          (),
        ),
        errorMessages: {},
      }),
    }
  }

  let initializeStateLabelPrint = (
    ~settingsOnly=None,
    ~priceList,
    ~priceId=?,
    ~productBarcodeDisplayed=?,
    ~missingProductBarcodeGenerated=?,
    ~priceDisplayed=?,
    ~producerDisplayed=?,
    ~alcoholVolumeDisplayed=?,
    ~productCode=?,
    ~labelFormat=?,
    (),
  ) => {
    let priceId =
      Option.orElse(priceId, defaultPriceIdFromPriceList(priceList))->Option.getWithDefault(
        Uuid.make(),
      )
    {
      priceList,
      form: LabelPrint({
        settingsOnly,
        submitResult: NotAsked,
        values: CatalogLabelCreateLabelPrintForm.initialValues(
          ~priceId,
          ~productBarcodeDisplayed?,
          ~missingProductBarcodeGenerated?,
          ~priceDisplayed?,
          ~producerDisplayed?,
          ~alcoholVolumeDisplayed?,
          ~productCode?,
          ~labelFormat?,
          (),
        ),
        errorMessages: {},
      }),
    }
  }

  let toLabelSheetFormValues = values => {
    let {
      printOffset,
      borderEnabled,
      sort,
      labelFormat,
    } = CatalogLabelCreateLabelSheetForm.initialValues(
      ~priceId=values.CatalogLabelCreateLabelPrintForm.priceId,
      (),
    )
    {
      CatalogLabelCreateLabelSheetForm.priceId: values.priceId,
      productBarcodeDisplayed: values.productBarcodeDisplayed,
      missingProductBarcodeGenerated: values.missingProductBarcodeGenerated,
      priceDisplayed: values.priceDisplayed,
      producerDisplayed: values.producerDisplayed,
      alcoholVolumeDisplayed: values.alcoholVolumeDisplayed,
      productCode: values.productCode,
      printOffset,
      borderEnabled,
      sort,
      productValues: values.productValues,
      labelFormat,
    }
  }

  let toLabelPrintFormValues = values => {
    let {
      priceDisplayed,
      producerDisplayed,
      alcoholVolumeDisplayed,
      labelFormat,
    } = CatalogLabelCreateLabelPrintForm.initialValues(
      ~priceId=values.CatalogLabelCreateLabelSheetForm.priceId,
      (),
    )
    {
      CatalogLabelCreateLabelPrintForm.priceId: values.priceId,
      productBarcodeDisplayed: values.productBarcodeDisplayed,
      missingProductBarcodeGenerated: values.missingProductBarcodeGenerated,
      priceDisplayed,
      producerDisplayed,
      alcoholVolumeDisplayed,
      productCode: values.productCode,
      productValues: values.productValues,
      labelFormat,
    }
  }

  let reducer = (state, action) =>
    switch action {
    | LabelSheetEdited(values) =>
      switch state.form {
      | LabelSheet({values: prevValues}) =>
        let nextValues = {
          ...values,
          productBarcodeDisplayed: values.missingProductBarcodeGenerated &&
          !prevValues.productBarcodeDisplayed
            ? true
            : values.productBarcodeDisplayed,
          missingProductBarcodeGenerated: !values.productBarcodeDisplayed &&
          prevValues.missingProductBarcodeGenerated
            ? false
            : values.missingProductBarcodeGenerated,
        }

        {
          priceList: state.priceList,
          form: LabelSheet({
            submitResult: NotAsked,
            values: nextValues,
            errorMessages: CatalogLabelCreateLabelSheetForm.errorMessagesFromValues(nextValues),
          }),
        }
      | _ => initializeStateLabelSheet(~priceList=state.priceList, ())
      }
    | LabelSheetProductsUpdated(updater) =>
      switch state.form {
      | LabelSheet({values: prevValues, submitResult}) =>
        let nextValues = {...prevValues, productValues: updater(prevValues.productValues)}
        {
          priceList: state.priceList,
          form: LabelSheet({
            submitResult,
            values: nextValues,
            errorMessages: CatalogLabelCreateLabelSheetForm.errorMessagesFromValues(nextValues),
          }),
        }
      | _ => state
      }
    | LabelPrintEdited(values) =>
      switch state.form {
      | LabelPrint({values: prevValues, settingsOnly}) =>
        let nextValues = {
          ...values,
          productBarcodeDisplayed: values.missingProductBarcodeGenerated &&
          !prevValues.productBarcodeDisplayed
            ? true
            : values.productBarcodeDisplayed,
          missingProductBarcodeGenerated: !values.productBarcodeDisplayed &&
          prevValues.missingProductBarcodeGenerated
            ? false
            : values.missingProductBarcodeGenerated,
        }

        {
          priceList: state.priceList,
          form: LabelPrint({
            settingsOnly,
            submitResult: NotAsked,
            values: nextValues,
            errorMessages: CatalogLabelCreateLabelPrintForm.errorMessagesFromValues(nextValues),
          }),
        }
      | _ => initializeStateLabelPrint(~priceList=state.priceList, ())
      }
    | LabelPrintProductsUpdated(updater) =>
      switch state.form {
      | LabelPrint({values: prevValues, submitResult, settingsOnly}) =>
        let nextValues = {...prevValues, productValues: updater(prevValues.productValues)}
        {
          priceList: state.priceList,
          form: LabelPrint({
            settingsOnly,
            submitResult,
            values: nextValues,
            errorMessages: CatalogLabelCreateLabelPrintForm.errorMessagesFromValues(nextValues),
          }),
        }
      | _ => state
      }
    | GenerateLabelsSubmitted(request) =>
      switch state.form {
      | LabelSheet(form) =>
        let validatedState = {
          ...state,
          form: LabelSheet({
            ...form,
            submitResult: request,
            errorMessages: CatalogLabelCreateLabelSheetForm.errorMessagesFromValues(form.values),
          }),
        }
        let request = isErrored(validatedState)
          ? AsyncData.Done(Error(t(validationErrorText)))
          : request

        {
          priceList: state.priceList,
          form: LabelSheet({
            ...form,
            submitResult: request,
            errorMessages: CatalogLabelCreateLabelSheetForm.errorMessagesFromValues(form.values),
          }),
        }
      | _ => initializeStateLabelSheet(~priceList=state.priceList, ())
      }
    | PrintLabelsSubmitted(request) =>
      switch state.form {
      | LabelPrint(form) =>
        let validatedState = {
          ...state,
          form: LabelPrint({
            ...form,
            submitResult: request,
            errorMessages: CatalogLabelCreateLabelPrintForm.errorMessagesFromValues(form.values),
          }),
        }
        let request = isErrored(validatedState)
          ? AsyncData.Done(Error(t(validationErrorText)))
          : request

        {
          priceList: state.priceList,
          form: LabelPrint({
            ...form,
            submitResult: request,
            errorMessages: CatalogLabelCreateLabelPrintForm.errorMessagesFromValues(form.values),
          }),
        }
      | _ => initializeStateLabelPrint(~priceList=state.priceList, ())
      }
    | SaveSettingsSubmitted =>
      switch state.form {
      | LabelPrint(form) => {
          priceList: state.priceList,
          form: LabelPrint({
            ...form,
            submitResult: Loading,
          }),
        }
      | _ => initializeStateLabelPrint(~priceList=state.priceList, ())
      }
    }
}

module CatalogLabelCreateInformationCard = {
  module LabelSheet = {
    open CatalogLabelCreateLabelSheetForm

    @react.component
    let make = React.memo((~priceList, ~values, ~onChange) => {
      let {
        priceId,
        productBarcodeDisplayed,
        missingProductBarcodeGenerated,
        priceDisplayed,
        producerDisplayed,
        alcoholVolumeDisplayed,
        productCode,
        productValues,
      } = values

      let hasProductsPicked = switch productValues {
      | Picked([]) => false
      | _pickedProducts => true
      }

      <Card title={t("Label information")} grow=true>
        <Tooltip
          content={<Tooltip.Span
            text={t(
              "Modification of the price list is only allowed when there is no selected products.",
            )}
          />}
          delay=0
          placement=#end
          offset=4.
          crossOffset=11.
          disabled={!hasProductsPicked}>
          <Select
            label={t("Price list")}
            preset=#inputField({required: false})
            disabled=hasProductsPicked
            sections=[
              {
                Select.items: priceList->Array.map(item => {
                  Select.key: item.id->Uuid.toString,
                  label: item.name,
                  value: item.id,
                }),
              },
            ]
            value=priceId
            onChange={value =>
              onChange({
                ...values,
                priceId: value,
              })}
          />
        </Tooltip>
        <InfoBlock title={t("Options")}>
          <Stack space=#medium>
            <Stack space=#small>
              <InputToggleSwitchField
                label={t("Display barcode")}
                required=false
                value=productBarcodeDisplayed
                onChange={value =>
                  onChange({
                    ...values,
                    productBarcodeDisplayed: value,
                  })}
              />
              <InputToggleSwitchField
                label={t("Automatically generate missing barcodes")}
                required=false
                value=missingProductBarcodeGenerated
                onChange={value =>
                  onChange({
                    ...values,
                    missingProductBarcodeGenerated: value,
                  })}
              />
              <InputToggleSwitchField
                label={t("Display retail price")}
                required=false
                value=priceDisplayed
                onChange={value =>
                  onChange({
                    ...values,
                    priceDisplayed: value,
                  })}
              />
              <InputToggleSwitchField
                label={t("Display producer")}
                required=false
                value=producerDisplayed
                onChange={value =>
                  onChange({
                    ...values,
                    producerDisplayed: value,
                  })}
              />
              <InputToggleSwitchField
                label={t("Display alcohol volume")}
                required=false
                value=alcoholVolumeDisplayed
                onChange={value =>
                  onChange({
                    ...values,
                    alcoholVolumeDisplayed: value,
                  })}
              />
            </Stack>
            <InputRadioGroupField
              label={t("Additional product code")}
              required=false
              options=CatalogLabel.ProductCode.values
              optionToText=CatalogLabel.ProductCode.toLabel
              value=productCode
              onChange={value =>
                onChange({
                  ...values,
                  productCode: value,
                })}
            />
          </Stack>
        </InfoBlock>
      </Card>
    })
  }

  module LabelPrint = {
    open CatalogLabelCreateLabelPrintForm
    @react.component
    let make = React.memo((~priceList, ~values, ~onChange) => {
      let {
        priceId,
        productBarcodeDisplayed,
        missingProductBarcodeGenerated,
        priceDisplayed,
        producerDisplayed,
        alcoholVolumeDisplayed,
        productCode,
        productValues,
      } = values

      let hasProductsPicked = switch productValues {
      | Picked([]) | All => false
      | _pickedProducts => true
      }

      <Card title={t("Label information")} grow=true>
        <Tooltip
          content={<Tooltip.Span
            text={t(
              "Modification of the price list is only allowed when there is no selected products.",
            )}
          />}
          delay=0
          placement=#end
          offset=4.
          crossOffset=11.
          disabled={!hasProductsPicked}>
          <Select
            label={t("Price list")}
            preset=#inputField({required: false})
            disabled=hasProductsPicked
            sections=[
              {
                Select.items: priceList->Array.map(item => {
                  Select.key: item.id->Uuid.toString,
                  label: item.name,
                  value: item.id,
                }),
              },
            ]
            value=priceId
            onChange={value =>
              onChange({
                ...values,
                priceId: value,
              })}
          />
        </Tooltip>
        <InfoBlock title={t("Options")}>
          <Stack space=#medium>
            <Stack space=#small>
              <InputToggleSwitchField
                label={t("Display barcode")}
                required=false
                value=productBarcodeDisplayed
                onChange={value =>
                  onChange({
                    ...values,
                    productBarcodeDisplayed: value,
                  })}
              />
              <InputToggleSwitchField
                label={t("Automatically generate missing barcodes")}
                required=false
                value=missingProductBarcodeGenerated
                onChange={value =>
                  onChange({
                    ...values,
                    missingProductBarcodeGenerated: value,
                  })}
              />
              <InputToggleSwitchField
                label={t("Display retail price")}
                required=false
                value=priceDisplayed
                onChange={value =>
                  onChange({
                    ...values,
                    priceDisplayed: value,
                  })}
              />
              <InputToggleSwitchField
                label={t("Display producer")}
                required=false
                value=producerDisplayed
                onChange={value =>
                  onChange({
                    ...values,
                    producerDisplayed: value,
                  })}
              />
              <InputToggleSwitchField
                label={t("Display alcohol volume")}
                required=false
                value=alcoholVolumeDisplayed
                onChange={value =>
                  onChange({
                    ...values,
                    alcoholVolumeDisplayed: value,
                  })}
              />
            </Stack>
            <InputRadioGroupField
              label={t("Additional product code")}
              required=false
              options=CatalogLabel.ProductCode.values
              optionToText=CatalogLabel.ProductCode.toLabel
              value=productCode
              onChange={value =>
                onChange({
                  ...values,
                  productCode: value,
                })}
            />
          </Stack>
        </InfoBlock>
      </Card>
    })
  }
}

module CatalogLabelCreatePrintSettingsCard = {
  module GlobalLabelFormat = {
    type t = Grid21 | Grid64 | Label31x22 | Label57x19

    let values = [Label31x22 /* Label57x19, */, Grid21, Grid64]
    let isStarFormat = value =>
      switch value {
      | Label31x22 | Label57x19 => true
      | Grid21 | Grid64 => false
      }
    let toLabel = value =>
      switch value {
      | Grid21 => CatalogLabel.Sheet.LabelFormat.toLabel(Grid21)
      | Grid64 => CatalogLabel.Sheet.LabelFormat.toLabel(Grid64)
      | Label31x22 => CatalogLabel.Print.LabelFormat.toLabel(Label31x22)
      | Label57x19 => CatalogLabel.Print.LabelFormat.toLabel(Label57x19)
      }
  }

  module LabelSheet = {
    open CatalogLabelCreateLabelSheetForm
    @react.component
    let make = React.memo((
      ~printerStatus,
      ~values,
      ~printOffsetErrorMessage,
      ~onChange,
      ~onChangeLabelFormat,
    ) => {
      let {labelFormat, printOffset, borderEnabled, sort} = values

      let maxPrintOffset = CatalogLabel.Sheet.LabelFormat.toMaxOffset(labelFormat)
      let labelFormat = switch labelFormat {
      | Grid21 => GlobalLabelFormat.Grid21
      | Grid64 => GlobalLabelFormat.Grid64
      }

      let renderTemplateSelectionItemContent = React.useCallback1(
        (item: Select.item<GlobalLabelFormat.t>) => {
          let templateLabel = GlobalLabelFormat.toLabel(item.Select.value)
          let starFormat = GlobalLabelFormat.isStarFormat(item.value)

          <Inline space=#xsmall>
            {templateLabel->React.string}
            {switch (printerStatus, starFormat) {
            | (Error(), true) =>
              <TooltipIcon variation=#alert>
                <Tooltip.Span text={t("No StarPrinter printers registered in this shop.")} />
              </TooltipIcon>
            | (Ok(), true) =>
              <TooltipIcon variation=#info>
                <Tooltip.Span text={t("Format suitable for Star label printers.")} />
              </TooltipIcon>
            | _ => React.null
            }}
          </Inline>
        },
        [printerStatus],
      )

      <Card title={t("Print setup")} grow=true>
        <Stack space=#xlarge>
          <Stack space=#medium>
            <Select
              label={t("Label template")}
              preset=#inputField({required: false})
              renderItemContent=renderTemplateSelectionItemContent
              sections={
                let items = GlobalLabelFormat.values->Array.map(value => {
                  Select.key: value->GlobalLabelFormat.toLabel,
                  label: value->GlobalLabelFormat.toLabel,
                  disabled: GlobalLabelFormat.isStarFormat(value) && printerStatus->Result.isError,
                  value,
                })
                [{Select.items: items}]
              }
              value=labelFormat
              onChange=onChangeLabelFormat
            />
            <InputNumberField
              label={t("Offset of the first printed label")}
              errorMessage=?printOffsetErrorMessage
              precision=0
              minValue=1.
              maxValue={maxPrintOffset->Int.toFloat}
              required=false
              value={printOffset->Int.toFloat}
              onChange={value =>
                onChange({
                  ...values,
                  printOffset: value->Float.toInt,
                })}
            />
          </Stack>
          <InfoBlock title={t("Options")}>
            <Stack space=#medium>
              <InputToggleSwitchField
                label={t("Display border")}
                required=false
                value=borderEnabled
                onChange={value =>
                  onChange({
                    ...values,
                    borderEnabled: value,
                  })}
              />
              <InputRadioGroupField
                label={t("Sorting labels")}
                required=false
                options=CatalogLabel.Sheet.LabelSort.values
                optionToText=CatalogLabel.Sheet.LabelSort.toLabel
                value=sort
                onChange={value =>
                  onChange({
                    ...values,
                    sort: value,
                  })}
              />
              <Stack space=#normal>
                <TextStyle variation=#normal size=#small>
                  {(t("The printing is done on an A4 laser printer.") ++
                  "\n" ++
                  t("If required, you can order adhesive label sheets or rolls online."))
                    ->React.string}
                </TextStyle>
                <TextAction
                  highlighted=true
                  text={t("Order label holders online.")}
                  onPress={() => HelpCenter.showArticle(HelpCenter.orderLabelSheets)}
                />
              </Stack>
            </Stack>
          </InfoBlock>
        </Stack>
      </Card>
    })
  }

  module LabelPrint = {
    open CatalogLabelCreateLabelPrintForm
    @react.component
    let make = React.memo((~printerStatus, ~settingsOnly, ~values, ~onChangeLabelFormat) => {
      let {labelFormat} = values

      let labelFormat = switch labelFormat {
      | Label31x22 => GlobalLabelFormat.Label31x22
      | Label57x19 => GlobalLabelFormat.Label57x19
      }

      let renderTemplateSelectionItemContent = React.useCallback1(
        (item: Select.item<GlobalLabelFormat.t>) => {
          open GlobalLabelFormat
          let templateLabel = toLabel(item.Select.value)
          let starFormat = isStarFormat(item.value)

          <Inline space=#xsmall>
            {templateLabel->React.string}
            {switch (printerStatus, starFormat) {
            | (Error(), true) =>
              <TooltipIcon variation=#alert>
                <Tooltip.Span text={t("No StarPrinter printers registered in this shop.")} />
              </TooltipIcon>
            | (Ok(), true) =>
              <TooltipIcon variation=#info>
                <Tooltip.Span text={t("Format suitable for Star label printers.")} />
              </TooltipIcon>
            | _ => React.null
            }}
          </Inline>
        },
        [printerStatus],
      )

      <Card title={t("Print setup")} grow=true>
        <Stack space=#medium>
          <Select
            preset=#inputField({required: false})
            label={t("Label template")}
            renderItemContent=renderTemplateSelectionItemContent
            sections={
              let items =
                GlobalLabelFormat.values
                ->Array.keep(value =>
                  !settingsOnly || (settingsOnly && GlobalLabelFormat.isStarFormat(value))
                )
                ->Array.map(value => {
                  Select.key: value->GlobalLabelFormat.toLabel,
                  label: value->GlobalLabelFormat.toLabel,
                  disabled: GlobalLabelFormat.isStarFormat(value) && printerStatus->Result.isError,
                  value,
                })
              [{Select.items: items}]
            }
            value=labelFormat
            onChange=onChangeLabelFormat
          />
          <Box spaceTop=#small>
            <Stack space=#medium>
              <Stack space=#xsmall>
                <TextStyle variation=#normal size=#small>
                  {t(
                    "Printing is done on a Star mC-Label3 thermal connected printer.",
                  )->React.string}
                </TextStyle>
                <TextAction
                  highlighted=true
                  text={t("Find out more about the printer.")}
                  onPress={() => HelpCenter.showArticle(HelpCenter.getLabelPrintFeature)}
                />
              </Stack>
              <Stack space=#xsmall>
                <TextStyle variation=#normal size=#small>
                  {(t("If required, you can order adhesive label sheets or rolls online.") ++ "\n")
                    ->React.string}
                </TextStyle>
                <TextAction
                  highlighted=true
                  text={t("Order label holders online.")}
                  onPress={() => HelpCenter.showArticle(HelpCenter.orderLabelSheets)}
                />
              </Stack>
            </Stack>
          </Box>
        </Stack>
      </Card>
    })
  }
}

module CatalogLabelCreateProductsCard = {
  module TableEmptyState = {
    let style = StyleX.style(
      ~display=#flex,
      ~flexDirection=#column,
      ~alignItems=#center,
      ~backgroundColor=Colors.neutralColor05,
      ~padding="23px 0",
      ~borderBottom="1px solid " ++ Colors.neutralColor15,
      (),
    )

    @react.component
    let make = React.memo((~message, ~errorMessage=?) =>
      <DivX style={StyleX.props([style])}>
        <TextStyle size=#small variation=#normal> {message->React.string} </TextStyle>
        {switch errorMessage {
        | Some(error) =>
          <Box spaceTop=#xsmall>
            <TextStyle size=#xsmall variation=#negative> {error->React.string} </TextStyle>
          </Box>
        | None => React.null
        }}
      </DivX>
    )
  }

  type row = CatalogLabelCreateProductsValue.pickedItem
  type action =
    | Reset
    | RowAllProductsAdded
    | RowsProductsAdded(array<row>)
    | RowProductUpdated(string, int)
    | RowProductRemoved(string)

  let reducer = (state, action) =>
    switch action {
    | Reset => CatalogLabelCreateProductsValue.Picked([])
    | RowAllProductsAdded => All
    | RowsProductsAdded(addedProducts) =>
      switch state {
      | CatalogLabelCreateProductsValue.Picked(currentProducts) =>
        let filteredAddedProducts =
          addedProducts->Array.keep(addedProduct =>
            !(
              currentProducts->Array.some(currentProduct =>
                currentProduct.productVariantId === addedProduct.productVariantId
              )
            )
          )
        Picked(Array.concat(currentProducts, filteredAddedProducts))
      | All => All
      }
    | RowProductUpdated(updatedProductId, printingQuantity) =>
      switch state {
      | Picked(currentProducts) =>
        let updatedProducts = currentProducts->Array.map(currentProduct =>
          if currentProduct.productVariantId === updatedProductId {
            {...currentProduct, printingQuantity}
          } else {
            currentProduct
          }
        )
        Picked(updatedProducts)
      | All => All
      }
    | RowProductRemoved(removedProductId) =>
      switch state {
      | Picked(currentProducts) =>
        let updatedProducts =
          currentProducts->Array.keep(currentProduct =>
            currentProduct.productVariantId !== removedProductId
          )
        Picked(updatedProducts)
      | All => All
      }
    }

  let makeTableColumns = (~dispatch, ~hasProductsAllPicked) => [
    {
      Table.key: "line",
      name: t("Line"),
      layout: {minWidth: 65.->#px, width: 65.->#px},
      render: ({index}) => {
        let leadingZero = 3
        let value = index + 1
        let formattedLineNumber = {
          let string = value->Int.toString
          let length = leadingZero + 1 - string->String.length
          let leading = length->String.make('0')
          leading ++ string
        }
        <TextStyle size=#xsmall variation=#normal> {formattedLineNumber->React.string} </TextStyle>
      },
    },
    {
      key: "reference",
      name: t("Product"),
      layout: {minWidth: 250.->#px, width: 450.->#px},
      render: ({
        data: {
          CatalogLabelCreateProductsValue.productVariantId: id,
          productInformation: {name, description},
        },
      }) => <LegacyProductReferenceTableCell id name description openNewTab=true />,
    },
    {
      key: "printing-quantity",
      name: t("Copies"),
      layout: {width: 80.->#px, hidden: hasProductsAllPicked},
      render: ({data: {productVariantId, printingQuantity}}) =>
        <InputNumberField
          minValue=1.
          precision=0
          value={printingQuantity->Int.toFloat}
          onChange={value => dispatch(RowProductUpdated(productVariantId, value->Float.toInt))}
        />,
    },
    {
      key: "actions",
      layout: {alignX: #flexEnd},
      render: ({data: {productVariantId}}) =>
        <RoundButton
          icon=#delete_light
          onPress={_ =>
            dispatch(hasProductsAllPicked ? Reset : RowProductRemoved(productVariantId))}
        />,
    },
  ]

  module AddProductModalButton = {
    @react.component
    let make = (~onRequestModalOpen) => {
      let (ref, hovered) = Hover.use()

      <Touchable ref grow=true onPress={_ => onRequestModalOpen()}>
        <OverlayTriggerView preset=#inputField({required: false}) icon=#search hovered>
          <TextStyle variation=#normal> {t("Add product")->React.string} </TextStyle>
        </OverlayTriggerView>
      </Touchable>
    }
  }

  @react.component
  let make = React.memo((
    ~shopId,
    ~priceId,
    ~printMode,
    ~productsTotalCount,
    ~products,
    ~errorMessage,
    ~onChange,
  ) => {
    let (pickerOpened, setPickerOpened) = React.useState(() => false)

    let dispatch = action => onChange(prevProducts => reducer(prevProducts, action))

    React.useEffect1(() => {
      switch (printMode, products) {
      | (true, CatalogLabelCreateProductsValue.All) => dispatch(Reset)
      | _ => ()
      }
      None
    }, [printMode])

    let hasProductsAllPicked = switch products {
    | All => true
    | Picked(_) => false
    }
    let columns = makeTableColumns(~dispatch, ~hasProductsAllPicked)

    let onPickProducts = products => {
      let rows = products->Array.map(product => {
        CatalogLabelCreateProductsValue.productVariantId: product.CatalogLabelProductsPickerModalTable.id,
        printingQuantity: 1,
        productInformation: {
          name: product.formattedName,
          description: CatalogProduct.Information.formatDescription(
            ~productKind=product.productKind,
            ~information=product.information,
            (),
          ),
          stockKeepingUnit: product.information.sku,
          internalCode: product.information.internalCode,
        },
      })
      dispatch(RowsProductsAdded(rows))
    }

    let tooManyProductsPicked = productsTotalCount > 1000
    let pickAllProductsButtonDisabled = printMode || tooManyProductsPicked || hasProductsAllPicked

    let resetButtonDisabled = switch products {
    | Picked([]) => true
    | Picked(_) | All => false
    }

    let tableProductsRows = switch products {
    | Picked(products) => products
    | All => [
        {
          CatalogLabelCreateProductsValue.productVariantId: "all",
          printingQuantity: 0,
          productInformation: {
            name: productsTotalCount->Int.toString ++ " " ++ t("added products"),
            description: t("All non-archived catalog products"),
            stockKeepingUnit: None,
            internalCode: None,
          },
        },
      ]
    }
    let pickerModalProductsDisabledIds = switch products {
    | Picked(products) => products->Array.map(row => row.productVariantId)
    | All => []
    }

    <>
      <Card variation=#table>
        <Box spaceX=#large spaceBottom=#xlarge>
          <Inline space=#xlarge grow=true>
            <AddProductModalButton onRequestModalOpen={() => setPickerOpened(_ => true)} />
            <Inline space=#medium alignY=#center>
              <TextIconButton
                icon=#bordered_plus_light
                disabled=pickAllProductsButtonDisabled
                textTooltip=?{if printMode {
                  Some(t("This feature only works for PDF sheet of labels generation."))
                } else if tooManyProductsPicked {
                  Some(t("This feature only works for catalogs\nwith less than 1,000 products"))
                } else {
                  None
                }}
                onPress={_ => dispatch(RowAllProductsAdded)}>
                {t("Add all products")->React.string}
              </TextIconButton>
              <TextIconButton
                icon=#delete_light disabled=resetButtonDisabled onPress={_ => dispatch(Reset)}>
                {t("Reset")->React.string}
              </TextIconButton>
            </Inline>
          </Inline>
        </Box>
        <TableView
          columns
          data=AsyncData.Done(Ok(tableProductsRows))
          keyExtractor={row => row.productVariantId}
          hideCard=true
          placeholderEmptyState={<TableEmptyState
            message={printMode
              ? t("Add products to print labels")
              : t("Add products to generate a PDF sheet of labels")}
            ?errorMessage
          />}
        />
      </Card>
      <CatalogLabelProductsPickerModalTable
        opened=pickerOpened
        shopId=Some(shopId)
        priceId
        disabledIds=pickerModalProductsDisabledIds
        onCommit=onPickProducts
        onRequestClose={() => setPickerOpened(_ => false)}
      />
    </>
  })
}

module CatalogLabelCreatePageInvalidBarcodesBanner = {
  let formateDetails = (~productValues, ~invalidBarcodesVariants) =>
    switch productValues {
    | CatalogLabelCreateProductsValue.All =>
      // TODO - backend: should also return the SKU and CKU (for links later)
      invalidBarcodesVariants->Array.map(variant =>
        variant.CatalogLabel.RequestSheetLabelsType.productName ++ " - " ++ variant.name
      )
    | Picked(items) =>
      invalidBarcodesVariants->Array.keepMap(variant =>
        switch items->Array.getBy(item => item.productVariantId === variant.id) {
        | Some({productInformation: {name, stockKeepingUnit}}) =>
          switch stockKeepingUnit {
          | Some(sku) => Some(`${name} (${sku})`)
          | None => Some(name)
          }
        | None => None
        }
      )
    }

  @react.component
  let make = React.memo((~productValues, ~invalidBarcodesVariants, ~onRequestClose) =>
    switch formateDetails(~productValues, ~invalidBarcodesVariants) {
    | [] => React.null
    | details =>
      <Banner
        textStatus=Warning(
          t("The label sheet has been generated, some incorrect barcodes cannot be displayed."),
        )
        details
        onRequestClose
      />
    }
  )
}

module CatalogLabelCreatePageLoaded = {
  @react.component
  let make = React.memo((
    ~firstPersistence,
    ~shop,
    ~organisation,
    ~printerStatus,
    ~productsTotalCount,
    ~initialState,
  ) => {
    let (notification, setNotification) = React.useState(() => None)
    let (invalidBarcodes, setInvalidBarcodes) = React.useState(() => None)
    let (state, dispatch) = React.useReducer(CatalogLabelCreateReducer.reducer, initialState)
    let navigate = Navigation.useNavigate()
    let url = Navigation.useUrl()

    let {encoder, decoder} = module(CatalogLabel.StateSettingsUserPreferences)
    let {store, usePersistOnChange} = UserPreferences.make(
      ~key=#"catalog-labels-options",
      ~encoder,
      ~decoder,
    )

    let getPriceName = (~priceId) =>
      state.priceList
      ->Array.getBy(price => price.id === priceId)
      ->Option.mapWithDefault("", price => price.name)

    let persisted = {
      let values = switch state.form {
      | LabelSheet({values}) =>
        Some({
          CatalogLabel.StateSettingsUserPreferences.priceId: values.priceId,
          priceName: getPriceName(~priceId=values.priceId),
          productBarcodeDisplayed: values.productBarcodeDisplayed,
          missingProductBarcodeGenerated: values.missingProductBarcodeGenerated,
          priceDisplayed: values.priceDisplayed,
          producerDisplayed: values.producerDisplayed,
          alcoholVolumeDisplayed: values.alcoholVolumeDisplayed,
          productCode: values.productCode,
          labelFormat: LabelSheet(values.labelFormat),
          printOffset: values.printOffset,
          borderEnabled: values.borderEnabled,
          sort: values.sort,
        })
      | LabelPrint({values, settingsOnly: None}) =>
        Some({
          priceId: values.priceId,
          priceName: getPriceName(~priceId=values.priceId),
          productBarcodeDisplayed: values.productBarcodeDisplayed,
          missingProductBarcodeGenerated: values.missingProductBarcodeGenerated,
          priceDisplayed: values.priceDisplayed,
          producerDisplayed: values.producerDisplayed,
          alcoholVolumeDisplayed: values.alcoholVolumeDisplayed,
          productCode: values.productCode,
          labelFormat: LabelPrint(values.labelFormat),
          printOffset: 1,
          borderEnabled: false,
          sort: Unsorted,
        })
      | LabelPrint({settingsOnly: Some(_)}) => None
      }

      usePersistOnChange(
        values->Option.getUnsafe,
        ~disabled=values->Option.isNone || CatalogLabelCreateReducer.isErrored(state),
        ~debounceDelay=1250,
        (),
      )
    }

    let initialStateSettingsBanner = if (
      firstPersistence && !CatalogLabelCreateReducer.isSettingsOnlyMode(state)
    ) {
      let text = "Don't worry! Print settings are automatically saved on the browser from which you configure them."
      <Banner textStatus=Info(t(text)) />
    } else {
      React.null
    }
    let persistedSettingsIconTooltip = {
      let text = persisted
        ? t("Settings saved on your web browser, a few moments ago.")
        : t("Settings unsaved.")
      let color = persisted ? Colors.successColor50 : Colors.neutralColor30

      <Offset top={-21.} left={-7.}>
        <Tooltip content={<Tooltip.Span text />} arrowed=false placement=#"top start" closeDelay=0>
          <Icon name=#cloud_save fill=color />
        </Tooltip>
      </Offset>
    }

    React.useEffect1(() => {
      let requestBarcodeCompletion = CatalogLabel.BarcodeCompletionRequest.make

      if !CatalogLabelCreateReducer.isErrored(state) {
        switch state.form {
        | LabelSheet({values, submitResult}) if !AsyncResult.isIdle(submitResult) =>
          let {make: requestLabelsGenerating} = module(CatalogLabel.Sheet.LabelsRequest)
          let {make: combineSheetRequests} = module(CatalogLabel.Sheet.CombineRequests)

          let future = combineSheetRequests(
            ~requestBarcodeCompletion,
            ~requestLabelsGenerating,
            ~priceId=values.priceId,
            ~productBarcodeDisplayed=values.productBarcodeDisplayed,
            ~missingProductBarcodeGenerated=values.missingProductBarcodeGenerated,
            ~priceDisplayed=values.priceDisplayed,
            ~producerDisplayed=values.producerDisplayed,
            ~alcoholVolumeDisplayed=values.alcoholVolumeDisplayed,
            ~productCode=values.productCode,
            ~labelFormat=values.labelFormat,
            ~borderEnabled=values.borderEnabled,
            ~printOffset=values.printOffset,
            ~sort=values.sort,
            ~productsValues=switch values.productValues {
            | Picked(products) =>
              Picked(
                products->Array.map(
                  product => {
                    CatalogLabel.RequestProductType.variantId: product.productVariantId,
                    repetitions: product.printingQuantity,
                  },
                ),
              )
            | All => All
            },
            ~shopId=shop.Auth__Types.id,
          )

          future
          ->Future.map(
            result =>
              AsyncResult.done(
                switch result {
                | Ok(Some(url)) => Ok(url)
                | Ok(None) => Error(Request.serverErrorMessage)
                | Error(errorMessage) => Error(errorMessage)
                },
              ),
          )
          ->Future.get(requestResult => dispatch(GenerateLabelsSubmitted(requestResult)))

          Some(() => future->Future.cancel)
        | LabelPrint({values, submitResult, settingsOnly: None})
          if !AsyncResult.isIdle(submitResult) =>
          let {make: requestLabelsPrinting} = module(CatalogLabel.Print.LabelsRequest)
          let {make: combinePrintRequests} = module(CatalogLabel.Print.CombineRequests)

          let future = combinePrintRequests(
            ~requestBarcodeCompletion,
            ~requestLabelsPrinting,
            ~priceId=values.priceId,
            ~priceName=getPriceName(~priceId=values.priceId),
            ~productBarcodeDisplayed=values.productBarcodeDisplayed,
            ~missingProductBarcodeGenerated=values.missingProductBarcodeGenerated,
            ~priceDisplayed=values.priceDisplayed,
            ~producerDisplayed=values.producerDisplayed,
            ~alcoholVolumeDisplayed=values.alcoholVolumeDisplayed,
            ~productCode=values.productCode,
            ~labelFormat=values.labelFormat,
            ~productsValues=switch values.productValues {
            | Picked(products) =>
              Picked(
                products->Array.map(
                  product => {
                    CatalogLabel.RequestProductType.variantId: product.productVariantId,
                    repetitions: product.printingQuantity,
                  },
                ),
              )
            | All => All
            },
            ~shopId=shop.id,
          )

          future
          ->Future.map(
            result =>
              AsyncResult.done(
                switch result {
                | Ok() => Ok()
                | Error(errorMessage) => Error(errorMessage)
                },
              ),
          )
          ->Future.get(requestResult => dispatch(PrintLabelsSubmitted(requestResult)))

          Some(() => future->Future.cancel)
        | LabelPrint({
            values,
            settingsOnly: Some({fromPathname, variantIdFromCatalogRedirection}),
            submitResult: Loading,
          }) =>
          let editRedirectionQueryString = CatalogRoutes.CatalogLabelQueryString.encode({
            variantIdfromLabelEditSettings: Some(variantIdFromCatalogRedirection),
            fromPathname: Some(url.pathname),
          })

          store({
            priceId: values.priceId,
            priceName: getPriceName(~priceId=values.priceId),
            productBarcodeDisplayed: values.productBarcodeDisplayed,
            missingProductBarcodeGenerated: values.missingProductBarcodeGenerated,
            priceDisplayed: values.priceDisplayed,
            producerDisplayed: values.producerDisplayed,
            alcoholVolumeDisplayed: values.alcoholVolumeDisplayed,
            productCode: values.productCode,
            labelFormat: LabelPrint(values.labelFormat),
            printOffset: 1,
            borderEnabled: false,
            sort: Unsorted,
          })

          if fromPathname !== url.pathname {
            navigate(fromPathname ++ "?" ++ editRedirectionQueryString->QueryString.toString)
          } else {
            navigate(CatalogRoutes.baseRoute)
          }
          None
        | _ => None
        }
      } else {
        None
      }
    }, [state.form])

    React.useEffect1(() => {
      switch state.form {
      | LabelSheet({submitResult: Done(Ok({url, invalidBarcodesVariants}))}) =>
        if invalidBarcodesVariants->Array.length > 0 {
          setInvalidBarcodes(_ => Some(invalidBarcodesVariants))
        } else {
          setInvalidBarcodes(_ => None)
        }
        url
        ->TriggerDownload.fromUrl
        ->Future.mapOk(_ => t("The label sheet has been successfully generated."))
        ->Future.mapError(_ => t("An issue when attempting downloading the file occurred."))
        ->Future.get(
          message =>
            if invalidBarcodesVariants->Array.length === 0 {
              setNotification(_ => Some(message))
            },
        )
      | LabelPrint({submitResult: Done(Ok())}) =>
        setNotification(
          _ => Some(
            Ok(
              t("Label printing is in progress.") ++
              template(
                t("[Monitor printing from your StarPrinter Online account]({{link}})"),
                ~values={"link": "https://portal.starprinter.online/Dashboard"},
                (),
              ),
            ),
          ),
        )
        setInvalidBarcodes(_ => None)
      | LabelSheet({submitResult: Done(Error(errorMessage))})
      | LabelPrint({submitResult: Done(Error(errorMessage))}) =>
        setNotification(_ => Some(Error(errorMessage)))
        setInvalidBarcodes(_ => None)
      | _ => ()
      }
      None
    }, [state.form])

    React.useEffect1(() => {
      switch (state.form, printerStatus) {
      | (LabelPrint(_), Error()) =>
        setNotification(_ => Some(Error(t("No StarPrinter printers registered in this shop."))))
      | _ => ()
      }
      None
    }, [printerStatus])

    let submissionLoading = switch state.form {
    | LabelSheet({submitResult}) => AsyncResult.isBusy(submitResult)
    | LabelPrint({submitResult}) => AsyncResult.isBusy(submitResult)
    }

    let pageTitle = CatalogLabelCreateReducer.isSettingsOnlyMode(state)
      ? t("Set labels settings")
      : t("Print the labels")
    let pageSubtitle = organisation ? Some(shop.name) : None
    let actionsBar = {
      open CatalogLabelCreateReducer
      let printMode = CatalogLabelCreateReducer.isPrintMode(state)
      let settingsOnlyMode = CatalogLabelCreateReducer.isSettingsOnlyMode(state)
      let (action, text, variation) = switch (printMode, settingsOnlyMode) {
      | (false, _) => (GenerateLabelsSubmitted(Loading), t("Generate labels"), #primary)
      | (true, false) => (PrintLabelsSubmitted(Loading), t("Print labels"), #primary)
      | (true, true) => (SaveSettingsSubmitted, t("Commit settings"), #success)
      }

      <Button variation loading=submissionLoading onPress={_ => dispatch(action)}>
        {text->React.string}
      </Button>
    }

    let notificationBanner = switch notification {
    | Some(value) =>
      let onRequestClose = () => setNotification(_ => None)
      <ResourceDetailsPage.NotificationBanner value onRequestClose />
    | None => React.null
    }

    let invalidBarcodesBanner = switch (state.form, invalidBarcodes) {
    | (LabelSheet({values: {productValues}}), Some(invalidBarcodesVariants)) =>
      let onRequestClose = () => setInvalidBarcodes(_ => None)
      <CatalogLabelCreatePageInvalidBarcodesBanner
        productValues invalidBarcodesVariants onRequestClose
      />
    | _ => React.null
    }

    <ResourceDetailsPage
      title=pageTitle
      variation={CatalogLabelCreateReducer.isSettingsOnlyMode(state) ? #compact : #standard}
      subtitle=?pageSubtitle
      statusBadge=persistedSettingsIconTooltip
      actionsBar
      notificationBanner>
      <Stack space=#medium>
        {invalidBarcodesBanner}
        {initialStateSettingsBanner}
        <Stack space=#medium>
          <Group wrap=false grid=["50%", "50%"] spaceX=#medium>
            {switch state.form {
            | LabelSheet({values}) =>
              <CatalogLabelCreateInformationCard.LabelSheet
                priceList=state.priceList
                values
                onChange={values => dispatch(LabelSheetEdited(values))}
              />
            | LabelPrint({values}) =>
              <CatalogLabelCreateInformationCard.LabelPrint
                priceList=state.priceList
                values
                onChange={values => dispatch(LabelPrintEdited(values))}
              />
            }}
            {switch state.form {
            | LabelSheet({values, errorMessages}) =>
              <CatalogLabelCreatePrintSettingsCard.LabelSheet
                printerStatus
                values
                printOffsetErrorMessage=errorMessages.printOffset
                onChange={values => dispatch(LabelSheetEdited(values))}
                onChangeLabelFormat={labelFormat =>
                  dispatch(
                    switch labelFormat {
                    | CatalogLabelCreatePrintSettingsCard.GlobalLabelFormat.Grid21 =>
                      LabelSheetEdited({
                        ...values,
                        labelFormat: Grid21,
                      })
                    | Grid64 =>
                      LabelSheetEdited({
                        ...values,
                        labelFormat: Grid64,
                      })
                    | Label31x22 =>
                      LabelPrintEdited({
                        ...CatalogLabelCreateReducer.toLabelPrintFormValues(values),
                        labelFormat: Label31x22,
                      })
                    | Label57x19 =>
                      LabelPrintEdited({
                        ...CatalogLabelCreateReducer.toLabelPrintFormValues(values),
                        labelFormat: Label57x19,
                      })
                    },
                  )}
              />
            | LabelPrint({values, settingsOnly}) =>
              <CatalogLabelCreatePrintSettingsCard.LabelPrint
                settingsOnly={settingsOnly->Option.isSome}
                printerStatus
                values
                onChangeLabelFormat={labelFormat =>
                  switch labelFormat {
                  | CatalogLabelCreatePrintSettingsCard.GlobalLabelFormat.Grid21 =>
                    dispatch(
                      LabelSheetEdited({
                        ...CatalogLabelCreateReducer.toLabelSheetFormValues(values),
                        labelFormat: Grid21,
                      }),
                    )
                  | Grid64 =>
                    dispatch(
                      LabelSheetEdited({
                        ...CatalogLabelCreateReducer.toLabelSheetFormValues(values),
                        labelFormat: Grid64,
                      }),
                    )
                  | Label31x22 =>
                    dispatch(
                      LabelPrintEdited({
                        ...values,
                        labelFormat: Label31x22,
                      }),
                    )
                  | Label57x19 =>
                    dispatch(
                      LabelPrintEdited({
                        ...values,
                        labelFormat: Label57x19,
                      }),
                    )
                  }}
              />
            }}
          </Group>
          {switch state.form {
          | LabelSheet({values, errorMessages}) =>
            <CatalogLabelCreateProductsCard
              shopId=shop.id
              priceId=values.priceId
              productsTotalCount
              printMode=false
              errorMessage={CatalogLabelCreateReducer.isErrored(state)
                ? errorMessages.productValues
                : None}
              products=values.productValues
              onChange={updater => dispatch(LabelSheetProductsUpdated(updater))}
            />
          | LabelPrint({values, errorMessages, settingsOnly: None}) =>
            <CatalogLabelCreateProductsCard
              shopId=shop.id
              priceId=values.priceId
              productsTotalCount
              printMode=true
              errorMessage={CatalogLabelCreateReducer.isErrored(state)
                ? errorMessages.productValues
                : None}
              products=values.productValues
              onChange={updater => dispatch(LabelPrintProductsUpdated(updater))}
            />
          | LabelPrint({settingsOnly: Some(_)}) => React.null
          }}
        </Stack>
      </Stack>
    </ResourceDetailsPage>
  })
}

module PricesQuery = %graphql(`
  query PricesQuery($filterBy: InputPricesQueryFilter) {
    prices(first: 50, filterBy: $filterBy) {
      pageInfo {
        endCursor
        hasNextPage
      }
      edges {
        node {
          id
          name
          enableByDefault
        }
      }
    }
  }
`)

// FIXME - backend: the filter should take a variantPrice ID
module VariantsCountQuery = %graphql(`
  query QueryVariantsTotalCount($filterBy: InputVariantsQueryFilter) {
    variants(filterBy: $filterBy) {
      totalCount
    }
  } 
`)

@react.component
let make = (~settingsOnly=None) => {
  let (shop, organisation) = switch Auth.useScope() {
  | Organisation({activeShop: shop}) => (shop, true)
  | Single(shop) => (Some(shop), false)
  }

  let (printerStatusResult, setPrinterStatusResult) = React.useState(_ => None)

  let priceListResult =
    PricesQuery.use(
      ~skip=Option.isNone(shop),
      PricesQuery.makeVariables(
        ~filterBy=PricesQuery.makeInputObjectInputPricesQueryFilter(
          ~shopIds=PricesQuery.makeInputObjectInFilter(
            ~_in=[shop->Option.mapWithDefault("", shop => shop.id)],
            (),
          ),
          (),
        ),
        (),
      ),
    )
    ->ApolloHelpers.queryResultToAsyncResult
    ->AsyncResult.mapOk(({prices}) =>
      prices.edges->Array.map(edge => {
        id: Uuid.fromString(edge.node.id)->Option.getUnsafe,
        name: edge.node.name,
        default: edge.node.enableByDefault,
      })
    )

  let productsTotalCountResult =
    VariantsCountQuery.use(
      ~skip=Option.isNone(shop),
      VariantsCountQuery.makeVariables(
        ~filterBy=VariantsCountQuery.makeInputObjectInputVariantsQueryFilter(
          ~shopIds={_in: [shop->Option.mapWithDefault("", shop => shop.id)]},
          ~archived=#EXCLUDED,
          ~active=VariantsCountQuery.makeInputObjectBooleanEqualsFilter(~_equals=true, ()),
          (),
        ),
        (),
      ),
    )
    ->ApolloHelpers.queryResultToAsyncResult
    ->AsyncResult.mapOk(({variants}) => variants.totalCount)

  React.useEffect1(() => {
    switch shop {
    | Some({id: shopId}) =>
      let future = CatalogLabel.Print.DefaultPrinterRequest.make(~shopId)
      future->Future.map(result => setPrinterStatusResult(_ => Some(result)))->ignore
      Some(() => future->Future.cancel)
    | None => None
    }
  }, [shop])

  let persistedStateSettings = CatalogLabel.StateSettingsUserPreferences.useRead()
  let firstPersistence = Option.isNone(persistedStateSettings)
  let initialState = priceList =>
    switch persistedStateSettings {
    | Some({labelFormat: LabelPrint(labelFormat)} as persistedState) =>
      CatalogLabelCreateReducer.initializeStateLabelPrint(
        ~settingsOnly,
        ~priceList,
        ~priceId=persistedState.priceId,
        ~productBarcodeDisplayed=persistedState.productBarcodeDisplayed,
        ~missingProductBarcodeGenerated=persistedState.missingProductBarcodeGenerated,
        ~priceDisplayed=persistedState.priceDisplayed,
        ~producerDisplayed=persistedState.producerDisplayed,
        ~alcoholVolumeDisplayed=persistedState.alcoholVolumeDisplayed,
        ~productCode=persistedState.productCode,
        ~labelFormat,
        (),
      )
    | Some({labelFormat: LabelSheet(_)} as persistedState) if settingsOnly->Option.isSome =>
      CatalogLabelCreateReducer.initializeStateLabelPrint(
        ~settingsOnly,
        ~priceList,
        ~priceId=persistedState.priceId,
        ~productBarcodeDisplayed=persistedState.productBarcodeDisplayed,
        ~missingProductBarcodeGenerated=persistedState.missingProductBarcodeGenerated,
        ~priceDisplayed=persistedState.priceDisplayed,
        ~producerDisplayed=persistedState.producerDisplayed,
        ~alcoholVolumeDisplayed=persistedState.alcoholVolumeDisplayed,
        ~productCode=persistedState.productCode,
        (),
      )
    | Some({labelFormat: LabelSheet(labelFormat)} as persistedState) =>
      CatalogLabelCreateReducer.initializeStateLabelSheet(
        ~priceList,
        ~priceId=persistedState.priceId,
        ~productBarcodeDisplayed=persistedState.productBarcodeDisplayed,
        ~missingProductBarcodeGenerated=persistedState.missingProductBarcodeGenerated,
        ~priceDisplayed=persistedState.priceDisplayed,
        ~producerDisplayed=persistedState.producerDisplayed,
        ~alcoholVolumeDisplayed=persistedState.alcoholVolumeDisplayed,
        ~productCode=persistedState.productCode,
        ~labelFormat,
        ~printOffset=persistedState.printOffset,
        ~borderEnabled=persistedState.borderEnabled,
        ~sort=persistedState.sort,
        (),
      )
    | None if settingsOnly->Option.isSome =>
      CatalogLabelCreateReducer.initializeStateLabelPrint(~settingsOnly, ~priceList, ())
    | None => CatalogLabelCreateReducer.initializeStateLabelSheet(~priceList, ())
    }

  <CatalogPlaceholderShopPicker
    message={t("Please select the shop with which you would like to generate labels.")}>
    {switch (shop, printerStatusResult, priceListResult, productsTotalCountResult) {
    | (_, Some(Error(UnexpectedFailure)), _, _)
    | (_, _, Done(Error(_)), _)
    | (_, _, _, Done(Error(_))) =>
      <Placeholder status=Error />
    | (Some(shop), Some(printerStatus), Done(Ok(priceList)), Done(Ok(productsTotalCount))) =>
      let printerStatus = switch printerStatus {
      | Ok() => Ok()
      | Error(_) => Error()
      }
      <CatalogLabelCreatePageLoaded
        firstPersistence
        shop
        organisation
        printerStatus
        productsTotalCount
        initialState={initialState(priceList)}
      />
    | _ => <Placeholder status=Loading />
    }}
  </CatalogPlaceholderShopPicker>
}
