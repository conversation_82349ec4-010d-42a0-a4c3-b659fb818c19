module ProductQuery = %graphql(`
    query ProductQuery($cku: CKU!, $filterBy: InputProductsByVariantCkuQueryFilter, $productVariantsFilterBy: InputProductVariantsQueryFilter) {
      productsByVariantCku(cku: $cku, first: 50, filterBy: $filterBy) {
        edges {
          node {
            id
            formattedStatus @ppxOmitFutureValue
            name
            formattedDescription
            formattedOrigin
            category {
              id
              formattedName
            }
            tax {
              value
            }
            shop {
              id
              name
            }
            internalNote
            variants(first: 50, filterBy: $productVariantsFilterBy) {
              edges {
                node {
                  cku
                  id
                  name
                  shop {
                    name
                  }
                  formattedStatus @ppxOmitFutureValue
                  formattedPurchasedPrice
                  purchasedPrice
                  capacityUnit
                  bulk
                  maxStockThreshold
                  minStockThreshold
                  stockOrderTriggerThreshold
                  variantPrices {
                    edges {
                      node {
                        valueIncludingTax
                        valueExcludingTax
                        price {
                          name
                          enableByDefault
                          taxIncluded
                        }
                      }
                    }
                  }
                  stock {
                    formattedQuantity
                    state @ppxOmitFutureValue
                  }
                }
              }
            }
          }
        }
      }
    }
  `)

module ProductShopsQuery = %graphql(`
    query ProductQuery($cku: CKU!) {
      productsByVariantCku(cku: $cku, first: 50, filterBy: { archived: INCLUDED }) {
        edges {
          node {
            id
            shop {
              id
            }
          }
        }
      }
    }
  `)
