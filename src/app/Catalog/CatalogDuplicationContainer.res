open Intl

module Config = CatalogDuplication__Config

module ProductsQuery = CatalogDuplication__Queries.ProductsQuery
module VariantsQuery = CatalogDuplication__Queries.VariantsQuery

let schema = [
  CatalogDuplicationForm.Schema.Custom(
    Source,
    ({source}) =>
      if source->Option.isSome {
        Ok()
      } else {
        Error(t("Please select a catalog to duplicate."))
      },
  ),
  Custom(
    Destinations,
    ({destinations}) =>
      destinations->Array.length > 0
        ? Ok()
        : Error(t("Please select at least one target shop to duplicate to.")),
  ),
]

@react.component
let make = (~cku, ~mode as duplicationMode) => {
  let (executeProductsQuery, productsQueryResults) = ProductsQuery.useLazy()
  let (executeVariantsQuery, variantsQueryResults) = VariantsQuery.useLazy()
  let (_, onGoBack) = Navigation.useGoBack()
  let notifier = Notifier.use(~clearPolicy=KeepOnHistoryChanges, ())
  let dispatch = Auth.useDispatch()

  // Executes Products or Variants query according to props duplicationMode
  React.useEffect1(() => {
    notifier.clear()
    switch duplicationMode {
    | CatalogDuplication.Mode.Product =>
      executeProductsQuery(ProductsQuery.makeVariables(~cku=cku->Scalar.CKU.serialize, ()))
    | Variant =>
      executeVariantsQuery(VariantsQuery.makeVariables(~cku=cku->Scalar.CKU.serialize, ()))
    }
    None
  }, [])

  // Redirects to the product/variant page if duplication succeeded
  let onSubmitSuccess = React.useCallback0(message => {
    ActiveShopSet(None)->dispatch
    notifier.reset(Success(message->Option.getWithDefault("")), ())
    onGoBack()
  })

  // Sends error notification on submit failure
  let onSubmitFailure = React.useCallback0(message => notifier.reset(Error(message), ()))

  // Gets results from queries responses
  let results = Config.use(
    ~productResponse=productsQueryResults,
    ~variantResponse=variantsQueryResults,
    ~duplicationMode,
  )

  switch results {
  | Error => <Placeholder status=Error />
  | Loading => <Placeholder status=Loading />
  | Data({initialValues, references, shopsData}) =>
    <CatalogDuplicationForm.FormLegacyProvider
      id=cku initialValues schema onSubmitSuccess onSubmitFailure>
      <CatalogDuplicationPage cku duplicationMode shopsData references />
    </CatalogDuplicationForm.FormLegacyProvider>
  }
}

let make = React.memo(make)
