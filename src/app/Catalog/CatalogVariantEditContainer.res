open Intl

module Config = CatalogVariantEdit__Config
module ProductsQuery = CatalogVariantEdit__Queries.ProductsQuery
module VariantQuery = CatalogVariantEdit__Queries.VariantQuery
module ProductQuery = CatalogVariantEdit__Queries.ProductQuery

type mode =
  | Create
  | Edit

@react.component
let make = (~cku: option<string>=?, ~mode: mode) => {
  let (executeProductsQuery, productsQueryResults) = ProductsQuery.useLazy()
  let (executeVariantQuery, variantQueryResults) = VariantQuery.useLazy()
  let (executeProductQuery, productQueryResults) = ProductQuery.useLazy()
  let onGoBack = Navigation.useGoBack()->snd
  let navigate = Navigation.useNavigate()
  let notifier = Notifier.use(~clearPolicy=KeepOnHistoryChanges, ())
  let shops = Auth.useShops()
  let activeShop = Auth.useActiveShop()
  let activeShopId = activeShop->Option.map(shop => shop.id)

  // Gets products eligible for variant creation/edition
  React.useEffect0(() => {
    switch cku {
    | Some(cku) if shops->Array.length > 1 =>
      executeProductsQuery(
        ProductsQuery.makeVariables(
          ~filterBy=ProductsQuery.makeInputObjectInputProductsQueryFilter(
            ~variantCkus=ProductsQuery.makeInputObjectInFilter(~_in=[cku], ()),
            ~archived=#INCLUDED,
            (),
          ),
          (),
        ),
      )
    | _ => ()
    }
    None
  })

  // Gets shop IDs for contextual information in the shop selection
  let disabledShopIds = React.useMemo1(() =>
    switch productsQueryResults {
    | Executed({data: Some({products: {edges: products}})}) =>
      shops->Array.keepMap(({id}) =>
        switch products->Array.getBy(({node: product}) => product.shop.id === id) {
        | Some(_) => None
        | _ => Some(id)
        }
      )
    | _ => []
    }
  , [productsQueryResults])

  let renderShopSelectionItem = React.useCallback0((shop: Select.item<option<Auth.shop>>) => {
    let shopName = shop.value->Option.mapWithDefault("", shop => shop.name)

    switch (shop.value->Option.isSome, shop.disabled->Option.getWithDefault(false)) {
    | (true, true) =>
      <Inline grow=true align=#spaceBetween alignY=#top>
        <TextStyle variation=#placeholder> {shopName->React.string} </TextStyle>
        <Inline grow=true align=#end>
          <TextStyle variation=#subdued size=#xsmall lineHeight=#large>
            {t("Non existing product")->React.string}
          </TextStyle>
        </Inline>
      </Inline>
    | (true, false) => <TextStyle> {shopName->React.string} </TextStyle>
    | (false, _) => <TextStyle variation=#normal> {t("Select a shop")->React.string} </TextStyle>
    }
  })

  // Retrieves the variant information for edition
  // or the product information for variant creation.
  React.useEffect1(() => {
    switch (mode, activeShop, cku) {
    | (Edit, Some(_), Some(cku)) =>
      executeVariantQuery(
        VariantQuery.makeVariables(
          ~cku=cku->Scalar.CKU.serialize,
          ~filterBy=Config.makeVariantFilterBy(~activeShopId),
          (),
        ),
      )
    | (Create, Some(_), Some(cku)) =>
      executeProductQuery(
        ProductQuery.makeVariables(
          ~cku=cku->Scalar.CKU.serialize,
          ~filterBy=Config.makeProductFilterBy(~activeShopId),
          (),
        ),
      )
    | _ => ()
    }
    None
  }, [activeShopId])

  // Redirects to the variant page if updating succeeded
  let onSubmitSuccess = React.useCallback0(newVariantCku =>
    switch (mode, cku) {
    | (Edit, _) =>
      notifier.reset(Success(t("The variant information have been correctly updated.")), ())
      onGoBack()
    | (Create, Some(cku)) =>
      navigate(Catalog->LegacyRouter.routeToPathname ++ "/product/" ++ cku, ~replace=true)
    | (Create, _) =>
      navigate(
        Catalog->LegacyRouter.routeToPathname ++
        "/product/" ++
        newVariantCku->Option.getWithDefault(""),
      )
    }
  )
  let onSubmitFailure = React.useCallback0(message => notifier.reset(Error(message), ()))

  let results = Config.use(
    ~variantResponse=variantQueryResults,
    ~productResponse=productQueryResults,
  )

  <CatalogPlaceholderShopPicker
    disabledIds=disabledShopIds
    message={template(
      t("Please select the shop in which you would like to {{action}} the reference."),
      ~values={"action": t(mode === Edit ? "edit" : "create")},
      (),
    )}
    renderItemContent=renderShopSelectionItem>
    {switch results {
    | Error => <Placeholder status=Error />
    | Loading => <Placeholder status=Loading />
    | Data({
        id,
        productKind,
        productTaxRate,
        productValues,
        initialValues,
        formattedName,
        shopName,
      }) =>
      <CatalogVariantEditForm.FormLegacyProvider
        id initialValues schema=CatalogVariantEditForm.schema onSubmitSuccess onSubmitFailure>
        <CatalogVariantEditPage
          editionMode={mode === Edit}
          productKind
          productTaxRate
          productValues
          shopName
          formattedName
        />
      </CatalogVariantEditForm.FormLegacyProvider>
    }}
  </CatalogPlaceholderShopPicker>
}

let make = React.memo(make)
