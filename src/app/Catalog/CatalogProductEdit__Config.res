module Query = CatalogProductEdit__Queries.ProductQuery

exception CatalogProductEdit_ProductNotFound

let makeFilterBy = (~activeShopId) =>
  Query.makeInputObjectInputProductsByVariantCkuQueryFilter(
    ~shopIds=?switch activeShopId {
    | Some(shopId) => Some(Query.makeInputObjectInFilter(~_in=[shopId], ()))
    | _ => None
    },
    ~archived=#INCLUDED,
    (),
  )

let initialValuesFromProductKind = (productKind: CatalogProduct.Kind.t) => {
  let initialValues: CatalogProductEditForm.Lenses.state = {
    kind: productKind,
    color: None,
    wineType: None,
    whiteWineType: None,
    name: "",
    tax: None,
    producer: "",
    family: "",
    designation: "",
    country: "France",
    region: "",
    beerType: "",
    categoryId: None,
  }

  switch productKind {
  | #WINE => {...initialValues, color: Some(#RED), wineType: Some(#STILL)}
  | #BEER => {...initialValues, color: Some(#BLOND)}
  | #SIMPLE | #SPIRITUOUS => initialValues
  }
}

type productData = Query.ProductQuery_inner.t

type productContextData = {
  id: string,
  name: string,
}

type data = {
  initialValues: CatalogProductEditForm.Lenses.state,
  productKind: CatalogProduct.Kind.t,
  contextData: option<productContextData>,
  shopName: string,
}

type t =
  | Error
  | Loading
  | Data(data)

type productResponse = ApolloClient__React_Types.LazyQueryResult.t<
  Query.ProductQuery_inner.t,
  Query.ProductQuery_inner.Raw.t,
  Query.ProductQuery_inner.t_variables,
  Query.ProductQuery_inner.Raw.t_variables,
>

let productFromData: productData => Query.ProductQuery_inner.t_productsByVariantCku_edges_node = data =>
  switch data.productsByVariantCku.edges[0] {
  | Some({node: product}) => product
  | _ => raise(CatalogProductEdit_ProductNotFound)
  }

let productContextFromData: productData => productContextData = data =>
  switch data.productsByVariantCku.edges[0] {
  | Some({node: product}) => {id: product.id, name: product.name}
  | _ => raise(CatalogProductEdit_ProductNotFound)
  }

let use = (~response: productResponse) => {
  let shops = Auth.useShops()
  let activeShop = Auth.useActiveShop()
  let productKind = CatalogProductEditUrlQueryString.CreateProduct.decode(Navigation.useUrl().query)

  switch shops {
  | [] => Loading
  | _ =>
    switch (response, productKind, activeShop) {
    // Loading
    | (Executed({loading: true}), _, _) => Loading
    // Product editing
    | (Executed({data: Some(data)}), _, Some({name: shopName})) =>
      let product = productFromData(data)
      let productContext = productContextFromData(data)

      Data({
        initialValues: {
          name: product.name,
          tax: switch product.tax.id->Uuid.fromString {
          | Some(uuid) => Some({CatalogProductEditForm.Tax.id: uuid, value: product.tax.value})
          | _ => None
          },
          categoryId: product.category->Option.flatMap(({id}) =>
            switch id->Uuid.fromString {
            | Some(uuid) => Some(uuid)
            | _ => None
            }
          ),
          kind: product.kind,
          color: product.color,
          producer: product.producer->Option.getWithDefault(""),
          family: product.family->Option.getWithDefault(""),
          designation: product.designation->Option.getWithDefault(""),
          country: product.country->Option.getWithDefault(""),
          region: product.region->Option.getWithDefault(""),
          beerType: product.beerType->Option.getWithDefault(""),
          wineType: product.wineType,
          whiteWineType: product.whiteWineType,
        },
        productKind: product.kind,
        contextData: Some(productContext),
        shopName,
      })
    // Reference creation (first step)
    | (Unexecuted(_), Ok({productKind}), Some({name: shopName})) =>
      Data({
        initialValues: productKind->initialValuesFromProductKind,
        productKind,
        contextData: None,
        shopName,
      })
    | _ => Error
    }
  }
}
