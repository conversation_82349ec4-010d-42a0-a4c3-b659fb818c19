module ProductsQuery = %graphql(`
  query products($filterBy: InputProductsQueryFilter) {
    products(filterBy: $filterBy) {
      totalCount
      edges {
        node {
          variants(first: 50, filterBy: { archived: INCLUDED }) {
            edges {
              node {
                cku
              }
            }
          }
        }
      }
    }
  }
`)

module ProductsByVariantCku = %graphql(`
  query productsByVariantCku($cku: CKU!) {
    productsByVariantCku(cku: $cku, filterBy: { archived: INCLUDED }) {
      totalCount
    }
  }
`)

type status =
  | Loading
  | Success(string)
  | Error

type relevantVariantCkuResult = {
  cku: string,
  productsCount: int,
}

type queryResult = Js.Promise.t<
  Result.t<
    ApolloClient__React_Hooks_UseApolloClient.ApolloClient.ApolloQueryResult.t__ok<
      ProductsByVariantCku.t,
    >,
    ApolloClient__React_Hooks_UseApolloClient.ApolloClient.ApolloError.t,
  >,
>

let rec runFindingRelevantVariantCku = (
  ~productsByVariantCkuFetch: string => queryResult,
  ~variantsCku: array<string>,
  ~variantsCkuActiveIndex: int=0,
  ~relevantVariantCku: option<relevantVariantCkuResult>=?,
  ~onDone: status => unit,
  (),
) =>
  switch (variantsCku[variantsCkuActiveIndex], relevantVariantCku) {
  | (Some(cku), _) =>
    productsByVariantCkuFetch(cku)
    ->FuturePromise.fromPromise
    ->Future.get(response =>
      switch response {
      | Ok(Ok({data: {productsByVariantCku: {totalCount}}, error: None})) =>
        let relevantVariantCku = switch relevantVariantCku {
        | Some({productsCount}) if productsCount >= totalCount => relevantVariantCku
        | _ => Some({cku, productsCount: totalCount})
        }
        runFindingRelevantVariantCku(
          ~productsByVariantCkuFetch,
          ~variantsCku,
          ~variantsCkuActiveIndex=variantsCkuActiveIndex + 1,
          ~relevantVariantCku?,
          ~onDone,
          (),
        )
      | _ => onDone(Error)
      }
    )
  | (None, Some(relevantCku)) => onDone(Success(relevantCku.cku))
  | _ => onDone(Error)
  }

// NOTE - This solution avoids centralizing products with their own CKU
// it first looks for products containing variants with the passed CKU
// then when there is multiple candidates it looks individually for each
// the number of bound products to select the highest one and determine the cku.
@react.component
let make = (~cku, ~redirectToCreateVariant=false) => {
  let shops = Auth.useShops()
  let authDispatch = Auth.useDispatch()
  let navigate = Navigation.useNavigate()
  let apolloClient = ApolloClient.React.useApolloClient()
  let (executeQuery, queryResults) = ProductsQuery.useLazy()
  let (status, setStatus) = React.useState(() => Loading)

  // Executes the first query once shops are fetched to retrieve
  // the relevant CKU for multishop accounts only.
  React.useEffect1(() => {
    switch shops->Array.length {
    | 0 => ()
    | 1 => setStatus(_ => Success(cku))
    | _ =>
      executeQuery(
        ProductsQuery.makeVariables(
          ~filterBy=ProductsQuery.makeInputObjectInputProductsQueryFilter(
            ~variantCkus=ProductsQuery.makeInputObjectInFilter(~_in=[cku], ()),
            ~archived=#INCLUDED,
            (),
          ),
          (),
        ),
      )
    }
    None
  }, [shops])

  // Second query callback to find the relevant CKU
  let productsByVariantCkuFetch = (cku: string) =>
    apolloClient.query(
      ~query=module(ProductsByVariantCku),
      ~fetchPolicy=NetworkOnly,
      ProductsByVariantCku.makeVariables(~cku=cku->Scalar.CKU.serialize, ()),
    )

  // Looks for the relevant CKU from the first query result:
  // - OK if all the shop products contain the original CKU in their variants.
  // - OK if there is only one variant containing the original CKU.
  // - FIND the relevant CKU if there is multiple variants CKU of
  //   the product containing the original CKU.
  React.useEffect1(() => {
    switch queryResults {
    | Executed({data: Some({products: {totalCount, edges: products}})}) =>
      switch products[0]->Option.map(({node: {variants: {edges: variants}}}) =>
        variants->Array.map(({node: {cku}}) => cku)
      ) {
      | Some(_) if totalCount >= shops->Array.length => setStatus(_ => Success(cku))
      | Some([cku]) => setStatus(_ => Success(cku))
      | Some(variantsCku) =>
        runFindingRelevantVariantCku(
          ~productsByVariantCkuFetch,
          ~variantsCku,
          ~onDone=result => {
            ActiveShopSet(None)->authDispatch
            setStatus(_ => result)
          },
          (),
        )
      | _ => setStatus(_ => Error)
      }
    | Executed({error: Some(_)}) => setStatus(_ => Error)
    | _ => ()
    }
    None
  }, [queryResults])

  // Redirects with the relevant CKU found in case of centralization issues.
  switch status {
  | Success(cku) =>
    if !redirectToCreateVariant {
      navigate(Catalog->LegacyRouter.routeToPathname ++ `/product/${cku}`, ~replace=true)
    } else {
      navigate(Catalog->LegacyRouter.routeToPathname ++ `/${cku}/create`, ~replace=true)
    }
    React.null
  | Loading => <Placeholder status=Loading />
  | Error => <Placeholder status=Error />
  }
}

let make = React.memo(make)
