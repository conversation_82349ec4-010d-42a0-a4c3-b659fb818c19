module ProductsQuery = %graphql(`
  query products($filterBy: InputProductsQueryFilter) {
    products(filterBy: $filterBy) {
      edges {
        node {
          shop {
            id
          }
        }
      }
    }
  }
`)

module VariantQuery = %graphql(`
  query variantsByCku($cku: CKU!, $filterBy: InputVariantsByCkuQueryFilter) {
    variantsByCku(cku: $cku, first: 1, filterBy: $filterBy) {
      edges {
        node {
          id
          formattedName
          name
          capacityUnit
          capacityValue
          capacityPrecision
          ean13
          stockKeepingUnit
          priceLookUpCode
          internalCode
          alcoholVolume
          year
          bulk
          purchasedPrice
          maxStockThreshold
          minStockThreshold
          stockOrderTriggerThreshold
          product {
            kind @ppxOmitFutureValue
          }
          variantPrices(first: 50) {
            edges {
              node {
                id
                valueIncludingTax
                valueExcludingTax
                price {
                  name
                  enableByDefault
                  taxIncluded
                }
              }
            }
          }
        }
      }
    }
  }
`)

module ProductQuery = %graphql(`
  query productsByVariantCku($cku: CKU!, $filterBy: InputProductsByVariantCkuQueryFilter) {
    productsByVariantCku(cku: $cku, first: 1, filterBy: $filterBy) {
      edges {
        node {
          id
          name
          kind @ppxOmitFutureValue
          tax {
            value
          }
        }
      }
    }
  }
`)
