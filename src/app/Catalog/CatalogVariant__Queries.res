// NOTE - stock field has no ID, therefore `formattedShopsNames` must be passed to not invalidate cache
module VariantQuery = %graphql(`
    query variantsByCku($cku: CKU!, $filterBy: InputVariantsByCkuQueryFilter) {
      variantsByCku(cku: $cku, first: 50, filterBy: $filterBy) {
        edges {
          node {
            id
            active
            formattedStatus @ppxOmitFutureValue
            name
            updatedAt
            formattedName
            capacityValue
            capacityUnit
            capacityPrecision
            bulk
            year
            alcoholVolume
            ean13
            stockKeepingUnit
            priceLookUpCode
            internalCode
            tastingNote
            internalNote
            maxStockThreshold
            minStockThreshold
            stockOrderTriggerThreshold
            shop {
              id
              name
            }
            stock {
              formattedShopsNames
              rawQuantity
              formattedQuantity
              state @ppxOmitFutureValue
            }
            supplier {
              id
              companyName
            }
            formattedCategory
            product {
              id
              name
              formattedDescription
              formattedOrigin
              tax {
                value
              }
            }
            purchasedPrice
            formattedPurchasedPrice
            variantPrices(first: 50) {
              edges {
                node {
                  id
                  archivedAt
                  valueIncludingTax
                  valueExcludingTax
                  toQuantity
                  fromQuantity
                  price {
                    id
                    name
                    enableByDefault
                    taxIncluded
                    archivedAt
                  }
                }
              }
              pageInfo {
                hasNextPage
              }
            }
          }
        }
      }
    }
  `)

module VariantShopsQuery = %graphql(`
    query variantsByCku_shops($cku: CKU!) {
      variantsByCku(cku: $cku, first: 50, filterBy: { archived: INCLUDED }) {
        edges {
          node {
            id
            shop {
              id
            }
          }
        }
      }
    }
  `)

module VariantCkuQuery = %graphql(`
    query variant_cku($id: ID!) {
      variant(id: $id) {
        cku
      }
    }
  `)

module PricesQuery = %graphql(`
  query prices {
    prices(first: 50) {
      edges {
        node {
          id
          name
          taxIncluded
          shop {
            id
          }
        }
      }
    }
  }
`)
