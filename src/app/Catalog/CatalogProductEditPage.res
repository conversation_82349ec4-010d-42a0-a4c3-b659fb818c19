open Intl

module Config = CatalogProductEdit__Config

@react.component
let make = (
  ~shopName,
  ~editionMode,
  ~productKind: CatalogProduct.Kind.t,
  ~contextData: option<Config.productContextData>,
) => {
  let notifier = Notifier.use()

  let renderActions = () => <CatalogProductEditFormPageActions editionMode />

  let formattedTitle = switch (editionMode, contextData) {
  | (true, Some({name})) => t("Editing") ++ " " ++ name
  | _ =>
    let productLabel = productKind->CatalogProduct.Kind.toLabel(~translate=false)
    template(
      t("Step {{step}} | Create {{fulfill}}"),
      ~values={
        "step": "1",
        "fulfill": t("a new " ++ productLabel),
      },
      (),
    )
  }

  <Page title=formattedTitle subtitle=shopName renderActions>
    <Notifier.Banner notifier />
    <Box spaceTop=#large>
      <Stack space=#xlarge>
        <CatalogProductEditFormInformationFieldsetPanel />
        {if editionMode {
          React.null
        } else {
          <CatalogProductEditFormOrganisationFieldsetPanel />
        }}
      </Stack>
    </Box>
  </Page>
}

let make = React.memo(make)
