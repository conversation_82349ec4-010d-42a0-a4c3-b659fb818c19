module UpdateProductMutation = %graphql(`
  mutation updateProduct_internalNote($id: ID!, $input: InputUpdateProduct!) {
    updateProduct(id: $id, input:	$input) {
      id
      internalNote
    }
  }
`)

module ArchiveProductMutation = %graphql(`
  mutation ArchiveProductMutation($id: ID!) {
    archiveProduct(id: $id) {
      id
      formattedStatus
    }
  }
`)

module UnarchiveProductMutation = %graphql(`
  mutation UnarchiveProductMutation($id: ID!) {
    unarchiveProduct(id: $id) {
      id
      formattedStatus
    }
  }
`)

let useUpdate = (~id) => {
  let (mutate, _) = UpdateProductMutation.use()

  React.useCallback1((_, state: CatalogProductForm.Lenses.state) => {
    let input = UpdateProductMutation.makeInputObjectInputUpdateProduct(
      ~internalNote=state.internalNote->Scalar.Text.serialize,
      ~country=Intl.t("France"),
      (),
    )

    mutate(UpdateProductMutation.makeVariables(~id, ~input, ()))
    ->ApolloHelpers.mutationPromiseToFutureResult
    ->Future.mapOk(({UpdateProductMutation.updateProduct: {id}}) => Some(id))
  }, [id])
}

let useArchive = (~id) => {
  let (mutate, _) = ArchiveProductMutation.use()

  React.useCallback1(
    () =>
      mutate(ArchiveProductMutation.makeVariables(~id, ()))
      ->ApolloHelpers.mutationPromiseToFutureResult
      ->Future.mapOk(_ => Some(id)),
    [id],
  )
}

let useUnarchive = (~id) => {
  let (mutate, _) = UnarchiveProductMutation.use()

  React.useCallback1(
    () =>
      mutate(UnarchiveProductMutation.makeVariables(~id, ()))
      ->ApolloHelpers.mutationPromiseToFutureResult
      ->Future.mapOk(_ => Some(id)),
    [id],
  )
}
