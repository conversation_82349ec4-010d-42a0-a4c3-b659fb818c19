let baseRoute = "/catalog"

let productRoute = (~cku) => baseRoute ++ "/product" ++ cku
let productEditRoute = (~cku) => productRoute(~cku) ++ "/edit"
let productRedirectRoute = (~cku) => baseRoute ++ "/product/redirect" ++ cku
let productDuplicationRoute = (~cku) => baseRoute ++ "/product/duplication" ++ cku

let variantRoute = (~cku) => baseRoute ++ "/" ++ cku
let variantEditRoute = (~cku) => variantRoute(~cku) ++ "/edit"
let variantCreateRoute = (~cku) => variantRoute(~cku) ++ "/create"
let variantDuplicationRoute = (~cku) => baseRoute ++ "/duplication" ++ cku
let variantRedirectByIdRoute = (~id) => baseRoute ++ "/redirect" ++ id
let variantStockActivitiesRoute = (~cku) => variantRoute(~cku) ++ "/stockactivities"
let variantOrderSuppliesRoute = (~cku) => variantRoute(~cku) ++ "/ordersupplies"

let labelsCreateRoute = baseRoute ++ "/labels/create"
let labelsCreateSettingsRoute = labelsCreateRoute ++ "/settings"

let inventoryImportRoute = baseRoute ++ "/inventory/import"

module CatalogLabelQueryString = {
  type t = {
    variantIdfromLabelEditSettings: option<string>,
    fromPathname: option<string>,
  }

  module Codecs = {
    let encoder = ({variantIdfromLabelEditSettings, fromPathname}) => (
      variantIdfromLabelEditSettings,
      fromPathname,
    )
    let decoder = ((variantIdfromLabelEditSettings, fromPathname)) => Ok({
      variantIdfromLabelEditSettings,
      fromPathname,
    })

    let value = JsonCodec.object2(
      encoder,
      decoder,
      JsonCodec.field("variantIdfromLabelEditSettings", JsonCodec.string)->JsonCodec.optional,
      JsonCodec.field("fromPathname", JsonCodec.string)->JsonCodec.optional,
    )
  }

  let encode = state => state->JsonCodec.encodeWith(Codecs.value)->QueryString.stringify

  let decode = query =>
    switch query->QueryString.parse->JsonCodec.decodeWith(Codecs.value) {
    | Ok(state) => state
    | Error(_) => {
        variantIdfromLabelEditSettings: None,
        fromPathname: None,
      }
    }
}
