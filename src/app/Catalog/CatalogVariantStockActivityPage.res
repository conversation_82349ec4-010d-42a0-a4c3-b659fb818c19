open Intl

module ParentQuery = %graphql(`
  query CatalogVariantStockActivityPageParentQuery($cku: CKU!) {
    variantsByCku(cku: $cku, first: 1) {
      edges {
        node {
          id
          cku
          formattedName
        }
      }
    }
  }
`)

module Query = %graphql(`
  query CatalogVariantStockActivityPageQuery($cku: CKU!, $filterBy: InputStockActivitiesQueryFilter, $search: String, $before: String, $after: String, $first: Int, $last: Int) {
    stockActivitiesByVariantCku(cku: $cku, search: $search, filterBy: $filterBy, before: $before, after: $after, first: $first, last: $last) {
      pageInfo {
        startCursor
        endCursor
      }
      totalCount
      edges {
        node {
          id
          kind @ppxOmitFutureValue
          reason @ppxOmitFutureValue
          comment
          shop { name }
          device { name slug}
          createdAt
          quantity
          variant {
            capacityPrecision
            capacityUnit
            bulk
          }
        }
      }
    }
  }
`)

module Filters = {
  type t = {
    shop: option<Auth.shop>,
    dateRange: option<(Js.Date.t, Js.Date.t)>,
    kind: option<StockActivityKind.t>,
    reason: option<StockActivityReason.t>,
  }

  let encoder = ({shop, dateRange, kind, reason}) => (
    shop->Option.map(shop => shop.id),
    dateRange->Option.map(((start, end)) => [start->Js.Date.valueOf, end->Js.Date.valueOf]),
    kind->Option.map(StockActivityKind.toString),
    reason->Option.map(StockActivityReason.toString),
  )

  let decoder = (~shops, (shopId, dateRange, kind, reason)) => Ok({
    shop: shops->Array.getBy((shop: Auth.shop) => Some(shop.id) === shopId),
    dateRange: dateRange->Option.flatMap(range =>
      switch range {
      | [start, end] => Some((start->Js.Date.fromFloat, end->Js.Date.fromFloat))
      | _ => None
      }
    ),
    kind: kind->Option.map(StockActivityKind.fromStringExn),
    reason: reason->Option.map(StockActivityReason.fromStringExn),
  })

  let useJsonCodec = () => {
    let shops = Auth.useShops()

    JsonCodec.object4(
      encoder,
      decoder(~shops),
      JsonCodec.field("shopId", JsonCodec.string)->JsonCodec.optional,
      JsonCodec.field("dateRange", JsonCodec.array(JsonCodec.float))->JsonCodec.optional,
      JsonCodec.field("kind", JsonCodec.string)->JsonCodec.optional,
      JsonCodec.field("reason", JsonCodec.string)->JsonCodec.optional,
    )
  }
}

module Row = {
  type t = {
    id: string,
    formattedKind: string,
    kind: StockActivityKind.t,
    reason: option<StockActivityReason.t>,
    quantity: int,
    variantCapacityPrecision: option<int>,
    variantCapacityUnit: option<string>,
    description: option<string>,
    date: Js.Date.t,
    shopName: string,
    deviceName: string,
  }
}

module Scaffolded = Scaffold.Make({
  type filters = Filters.t
  let useFiltersJsonCodec = Filters.useJsonCodec

  module QueryInner = Query.Query_inner
  type queryVariableFilterBy = Query.t_variables_InputStockActivitiesQueryFilter
  let useQuery = Query.use

  let makeQueryVariables = (
    defaultQueryVariables,
    ~connectionArguments,
    ~search=?,
    ~filterBy=?,
    (),
  ) => {
    ...defaultQueryVariables,
    QueryInner.first: connectionArguments.first,
    last: connectionArguments.last,
    before: connectionArguments.Scaffold.before,
    after: connectionArguments.after,
    search,
    filterBy,
  }

  let makeQueryVariablesFilterBy = ({Filters.shop: shop, dateRange, reason, kind}) =>
    Query.makeInputObjectInputStockActivitiesQueryFilter(
      ~shopIds=?{
        switch shop {
        | Some({id: shopId}) => Some(Query.makeInputObjectInFilter(~_in=[shopId], ()))
        | None => None
        }
      },
      ~date=?{
        switch dateRange {
        | Some((startDate, endDate)) =>
          Some({
            QueryInner._after: None,
            _before: None,
            _between: Some([
              startDate->Scalar.Datetime.serialize,
              endDate->Scalar.Datetime.serialize,
            ]),
          })
        | None => None
        }
      },
      ~reason=?{
        switch reason {
        | Some(reason) =>
          Some(Query.makeInputObjectInFilter(~_in=[reason->StockActivityReason.toString], ()))
        | None => None
        }
      },
      ~kind=?{
        switch kind {
        | Some(kind) =>
          Some(Query.makeInputObjectInOrNotInFilter(~_in=[kind->StockActivityKind.toString], ()))
        | None => None
        }
      },
      (),
    )

  let totalCountFromQueryData = ({
    Query.stockActivitiesByVariantCku: stockActivitiesByVariantCku,
  }) => stockActivitiesByVariantCku.totalCount
  let cursorsFromQueryData = ({Query.stockActivitiesByVariantCku: stockActivitiesByVariantCku}) => (
    stockActivitiesByVariantCku.pageInfo.startCursor,
    stockActivitiesByVariantCku.pageInfo.endCursor,
  )

  type row = Row.t
  let rowsFromQueryDataAndState = (
    {Query.stockActivitiesByVariantCku: stockActivitiesByVariantCku},
    _,
  ) =>
    stockActivitiesByVariantCku.edges->Array.keepMap(({node}) =>
      switch node.variant {
      | Some(variant) =>
        Some({
          Row.id: node.id,
          kind: node.kind,
          formattedKind: node.kind->StockActivityKind.toLabel,
          reason: node.reason,
          description: node.comment,
          shopName: node.shop.name,
          deviceName: DeviceName.decode(
            ~slug=node.device.slug,
            ~name=node.device.name,
          )->DeviceName.format,
          date: node.createdAt,
          quantity: node.quantity,
          variantCapacityUnit: switch (variant.bulk, variant.capacityUnit) {
          | (Some(true), Some(unit)) => Some(unit)
          | _ => None
          },
          variantCapacityPrecision: switch (variant.bulk, variant.capacityPrecision) {
          | (Some(true), Some(precision)) => Some(precision)
          | _ => None
          },
        })
      | _ => None
      }
    )

  let keyExtractor = ({Row.id: id}) => id
})

let use = (~initialState) => {
  let (state, dispatch) = Scaffolded.use(() => initialState)

  React.useEffect1(() => {
    switch state.filters {
    | {kind: Some(#LOSS)} => ()
    | {reason: Some(_)} => FiltersUpdated(prev => {...prev, reason: None})->dispatch
    | _ => ()
    }
    None
  }, [state.filters])

  (state, dispatch)
}

@react.component
let make = (~cku) => {
  let scope = Auth.useScope()
  let activeShop = Auth.useActiveShop()
  let hasOrganisationScope = switch scope {
  | Organisation(_) => true
  | Single(_) => false
  }
  let cku = cku->Scalar.CKU.serialize

  let pageSubtitle = switch ParentQuery.use(ParentQuery.makeVariables(~cku, ())) {
  | {data: Some({variantsByCku: {edges: [{node: variant}]}})} => Some(variant.formattedName)
  | {error: Some(_)} => None
  | _ => Some(t("Loading") ++ "...")
  }

  let initialState = Scaffolded.makeInitialState(
    ~filters={
      Filters.shop: activeShop,
      kind: None,
      reason: None,
      dateRange: None,
    },
  )
  let (state, dispatch) = use(~initialState)

  let defaultQueryVariables = Query.makeVariables(~cku, ())

  let columns = [
    {
      Scaffold.name: t("Type"),
      layout: {minWidth: 100.->#px, width: 0.5->#fr},
      render: ({Row.formattedKind: formattedKind, reason}) =>
        <StockActivityTypeTableCell value=formattedKind reason />,
    },
    {
      name: t("Quantity"),
      layout: {width: 0.5->#fr, alignX: #center, sticky: true},
      render: ({quantity, variantCapacityPrecision, variantCapacityUnit, kind}) =>
        <StockActivityQuantityTableCell
          value=quantity
          capacityPrecision=?variantCapacityPrecision
          capacityUnit=?variantCapacityUnit
          kind
        />,
    },
    {
      name: t("Comment"),
      layout: {minWidth: 110.->#px, margin: #normal},
      render: ({description}) =>
        <TextStyle> {description->Option.getWithDefault("—")->React.string} </TextStyle>,
    },
    {
      name: t("Datetime"),
      layout: {minWidth: 110.->#px},
      render: ({date}) => <TableDatetimeCell value=date />,
    },
    {
      name: t("Source"),
      layout: {minWidth: 120.->#px},
      render: ({deviceName, shopName}) => {
        let shopName = switch scope {
        | Organisation({activeShop: None}) => Some(shopName)
        | _ => None
        }
        <StockActivitySourceTableCell deviceName ?shopName />
      },
    },
  ]

  let filters =
    <Inline space=#small>
      {if hasOrganisationScope {
        <Auth.SelectShopFilter
          value=?state.filters.shop
          onChange={shop => FiltersUpdated(prev => {...prev, shop})->dispatch}
        />
      } else {
        React.null
      }}
      {if hasOrganisationScope {
        <Separator />
      } else {
        React.null
      }}
      <Select
        preset=#filter
        label={t("Kind")}
        sections={
          let defaultItem = {
            Select.key: "default",
            label: t("All"),
            value: None,
            sticky: true,
          }
          let items = StockActivityKind.values->Array.map(value => {
            Select.label: value->StockActivityKind.toLabel,
            key: value->StockActivityKind.toString,
            value: Some(value),
          })

          [{items: [defaultItem]}, {title: t("Kinds"), items}]
        }
        value=state.filters.kind
        onChange={kind => FiltersUpdated(prev => {...prev, kind})->dispatch}
      />
      {switch state.filters.kind {
      | Some(#LOSS) =>
        <Select
          preset=#filter
          label={t("Reason")}
          sections={
            let defaultItem = {
              Select.key: "default",
              label: template(t("All{{feminine}}"), ()),
              value: None,
              sticky: true,
            }
            let items = StockActivityReason.values->Array.map(value => {
              Select.label: value->StockActivityReason.toLabel,
              key: value->StockActivityReason.toString,
              value: Some(value),
            })

            [{Select.items: [defaultItem]}, {title: t("Reasons"), items}]
          }
          value=state.filters.reason
          onChange={reason => FiltersUpdated(prev => {...prev, reason})->dispatch}
        />
      | _ => React.null
      }}
      <SelectDateRangeFilter
        placeholder={t("Since the beginning")}
        value=?state.filters.dateRange
        onChange={dateRange => FiltersUpdated(prev => {...prev, dateRange})->dispatch}
        triggerLabelDisplay=#showPreset
      />
      {switch state.filters {
      | {dateRange: Some(_)} | {kind: Some(_)} =>
        <Scaffold.ResetFiltersButton
          onPress={() => FiltersUpdated(_ => initialState.filters)->dispatch}
        />
      | _ => React.null
      }}
    </Inline>

  let searchBar =
    <SearchBar
      placeholder={t("Search a stock movement")}
      value=?state.searchQuery
      onChange={searchQuery => Searched(searchQuery)->dispatch}
    />

  let emptyState = switch (state, scope) {
  | (
      {currentPage: 1, searchQuery: None, filters: {kind: None, reason: None, dateRange: None}},
      Single(_) | Organisation({activeShop: None}),
    ) =>
    <EmptyState
      illustration=Illustration.create title={t("Welcome to the stock movements space.")}
    />
  | (
      {currentPage: 1, searchQuery: None, filters: {kind: None, reason: None, dateRange: None}},
      Organisation({activeShop: Some(_)}),
    ) =>
    <EmptyState
      illustration=Illustration.notFound
      title={t("Welcome to the stock movements space.")}
      text={t("It seems no data exists yet for this shop.")}
    />
  | _ =>
    <EmptyState
      illustration=Illustration.notFound
      title={t("No result were found.")}
      text={t("Try again with another keyword/filter or:")}>
      <Button variation=#neutral onPress={_ => Reset(initialState)->dispatch}>
        {t("Clear search query and filters")->React.string}
      </Button>
    </EmptyState>
  }

  switch pageSubtitle {
  | Some(subtitle) =>
    <Scaffolded
      title={t("Stock activities")}
      subtitle
      defaultQueryVariables
      state
      dispatch
      columns
      filters
      searchBar
      emptyState
    />
  | _ => <Placeholder status=Error />
  }
}

let make = React.memo(make)
