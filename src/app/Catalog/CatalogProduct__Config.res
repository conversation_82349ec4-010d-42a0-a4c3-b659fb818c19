open Product
open Intl

module Query = CatalogProduct__Queries.ProductQuery

let makeFilterBy = (~activeShopId) =>
  Query.makeInputObjectInputProductsByVariantCkuQueryFilter(
    ~shopIds=?switch activeShopId {
    | Some(shopId) => Some(Query.makeInputObjectInFilter(~_in=[shopId], ()))
    | _ => None
    },
    ~archived=#INCLUDED,
    (),
  )

type data = Query.ProductQuery_inner.t
type dataVariantPrice = Query.ProductQuery_inner.t_productsByVariantCku_edges_node_variants_edges_node_variantPrices_edges

type productInformation = {
  id: string,
  status: option<CatalogProduct.Status.t>,
  name: string,
  description: option<string>,
  formattedOrigin: string,
  taxValue: float,
  categoryId: option<string>,
  formattedCategoryName: string,
  shopId: string,
  shopName: string,
  internalNote: string,
}

type variantPrice = {
  name: string,
  formattedValue: string,
  taxIncluded: bool,
  default: bool,
}

type stock = {
  quantity: option<string>,
  state: option<Stock.t>,
}

type variant = {
  cku: string,
  id: string,
  shopName: string,
  status: option<CatalogProduct.Status.t>,
  name: string,
  formattedPurchasePrice: option<string>,
  purchasePrice: float,
  formattedRetailPrice: string,
  retailPrices: array<variantPrice>,
  bulkUnit: option<string>,
  stock: stock,
  maxStockThreshold: float,
  minStockThreshold: float,
  stockOrderTriggerThreshold: float,
  bulk: bool,
}

let formatValueFromVariantPrice: dataVariantPrice => string = ({node: variantPrice}) => {
  let price = switch variantPrice.price->Option.map(price => price.taxIncluded) {
  | Some(true) => Some(variantPrice.valueIncludingTax)
  | Some(false) => Some(variantPrice.valueExcludingTax)
  | _ => None
  }

  price->Option.mapWithDefault("—", price => price->Intl.currencyFormat(~currency=#EUR))
}

let retailPriceFromVariantPrices: array<dataVariantPrice> => string = prices =>
  prices
  ->Array.getBy(({node}) =>
    node.price->Option.mapWithDefault(false, price => price.enableByDefault)
  )
  ->Option.mapWithDefault("—", formatValueFromVariantPrice)

let productsInformationFromData: data => array<productInformation> = data =>
  data.productsByVariantCku.edges->Array.map(({node: data}) => {
    id: data.id,
    name: data.name,
    status: data.formattedStatus->Option.map(status =>
      // NOTE - ignoring #UNARCHIVED
      switch status {
      | #ACTIVE => CatalogProduct.Status.Active
      | #INACTIVE => Inactive
      | #ARCHIVED => Archived
      }
    ),
    description: data.formattedDescription,
    formattedOrigin: data.formattedOrigin,
    taxValue: data.tax.value,
    categoryId: data.category->Option.map(({id}) => id),
    formattedCategoryName: data.category->Option.mapWithDefault(t("Not classified"), ({
      formattedName,
    }) => formattedName),
    shopId: data.shop.id,
    shopName: data.shop.name,
    internalNote: data.internalNote->Option.getWithDefault(""),
  })

let productsVariantsFromData: data => array<array<variant>> = data =>
  data.productsByVariantCku.edges->Array.map(({node: {variants}}) =>
    variants.edges
    ->SortArray.stableSortBy(({node: current}, {node: next}) =>
      Js.String.localeCompare(next.name, current.name)->Float.toInt
    )
    ->Array.map(({node: variant}) => {
      cku: variant.cku,
      id: variant.id,
      shopName: variant.shop.name,
      status: switch variant.formattedStatus {
      | #ACTIVE => Some(CatalogProduct.Status.Active)
      | #INACTIVE => Some(Inactive)
      | #ARCHIVED => Some(Archived)
      | #VARIABLE => None
      },
      name: variant.name,
      bulkUnit: switch (variant.capacityUnit, variant.bulk->Option.getWithDefault(false)) {
      | (Some(unit), true) => Some(unit)
      | _ => None
      },
      purchasePrice: variant.purchasedPrice->Option.getWithDefault(0.),
      formattedPurchasePrice: variant.formattedPurchasedPrice,
      formattedRetailPrice: variant.variantPrices.edges->retailPriceFromVariantPrices,
      retailPrices: variant.variantPrices.edges->Array.keepMap(
        variantPrice =>
          switch variantPrice.node.price {
          | Some(price) =>
            Some({
              name: price.name,
              formattedValue: variantPrice->formatValueFromVariantPrice,
              taxIncluded: price.taxIncluded,
              default: price.enableByDefault,
            })
          | _ => None
          },
      ),
      stock: {
        quantity: variant.stock.formattedQuantity,
        state: variant.stock.state,
      },
      maxStockThreshold: variant.maxStockThreshold->Option.getWithDefault(0)->Float.fromInt,
      minStockThreshold: variant.minStockThreshold->Option.getWithDefault(0)->Float.fromInt,
      stockOrderTriggerThreshold: variant.stockOrderTriggerThreshold
      ->Option.getWithDefault(0)
      ->Float.fromInt,
      bulk: variant.bulk->Option.getWithDefault(false),
    })
  )
