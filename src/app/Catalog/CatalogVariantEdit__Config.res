module VariantQuery = CatalogVariantEdit__Queries.VariantQuery
module ProductQuery = CatalogVariantEdit__Queries.ProductQuery

exception CatalogVariantEdit_VariantNotFound
exception CatalogVariantEdit_VariantProductNotFound
exception CatalogVariantEdit_ProductNotFound

let makeVariantFilterBy = (~activeShopId) =>
  VariantQuery.makeInputObjectInputVariantsByCkuQueryFilter(
    ~shopIds=?switch activeShopId {
    | Some(shopId) => Some(VariantQuery.makeInputObjectInFilter(~_in=[shopId], ()))
    | _ => None
    },
    ~archived=#INCLUDED,
    (),
  )

let makeProductFilterBy = (~activeShopId) =>
  ProductQuery.makeInputObjectInputProductsByVariantCkuQueryFilter(
    ~shopIds=?switch activeShopId {
    | Some(shopId) => Some(ProductQuery.makeInputObjectInFilter(~_in=[shopId], ()))
    | _ => None
    },
    ~archived=#INCLUDED,
    (),
  )

type variantData = VariantQuery.VariantQuery_inner.t
type productData = ProductQuery.ProductQuery_inner.t

type data = {
  id: string,
  productKind: CatalogProduct.Kind.t,
  productTaxRate: option<float>,
  productValues: option<CatalogProductEditForm.Lenses.state>,
  initialValues: CatalogVariantEditForm.Lenses.state,
  formattedName: string,
  shopName: string,
}

type t =
  | Error
  | Loading
  | Data(data)

type variantResponse = ApolloClient__React_Types.LazyQueryResult.t<
  VariantQuery.VariantQuery_inner.t,
  VariantQuery.VariantQuery_inner.Raw.t,
  VariantQuery.VariantQuery_inner.t_variables,
  VariantQuery.VariantQuery_inner.Raw.t_variables,
>

type productResponse = ApolloClient__React_Types.LazyQueryResult.t<
  ProductQuery.ProductQuery_inner.t,
  ProductQuery.ProductQuery_inner.Raw.t,
  ProductQuery.ProductQuery_inner.t_variables,
  ProductQuery.ProductQuery_inner.Raw.t_variables,
>

let variantFromData: variantData => VariantQuery.VariantQuery_inner.t_variantsByCku_edges_node = data =>
  switch data.variantsByCku.edges[0] {
  | Some({node: variant}) => variant
  | _ => raise(CatalogVariantEdit_VariantNotFound)
  }

let variantProductFromData: variantData => VariantQuery.VariantQuery_inner.t_variantsByCku_edges_node_product = data =>
  switch data.variantsByCku.edges[0]->Option.map(({node}) => node.product) {
  | Some(product) => product
  | _ => raise(CatalogVariantEdit_VariantProductNotFound)
  }

let productFromData: productData => ProductQuery.ProductQuery_inner.t_productsByVariantCku_edges_node = data =>
  switch data.productsByVariantCku.edges[0] {
  | Some({node: product}) => product
  | _ => raise(CatalogVariantEdit_ProductNotFound)
  }

let use = (~variantResponse: variantResponse, ~productResponse: productResponse) => {
  let shops = Auth.useShops()
  let activeShop = Auth.useActiveShop()
  let productValues = CatalogVariantEditUrlState.CreateVariantFromProduct.decode(
    Navigation.useUrl().state,
  )

  switch shops {
  | [] => Loading
  | _ =>
    switch (variantResponse, productResponse, productValues, activeShop) {
    // Loading
    | (Executed({loading: true}), _, _, _)
    | (_, Executed({loading: true}), _, _) =>
      Loading
    // Variant editing
    | (Executed({data: Some(data)}), _, _, Some({name: shopName})) =>
      let variant = variantFromData(data)
      let product = variantProductFromData(data)

      Data({
        id: variant.id,
        productKind: product.kind,
        productTaxRate: None,
        productValues: None,
        initialValues: {
          name: variant.name,
          capacityValue: variant.capacityValue,
          capacityUnit: variant.capacityUnit,
          capacityPrecision: variant.capacityPrecision,
          ean13: variant.ean13->Option.getWithDefault(""),
          stockKeepingUnit: variant.stockKeepingUnit->Option.getWithDefault(""),
          priceLookUpCode: {
            invalidValues: [],
            value: variant.priceLookUpCode,
          },
          internalCode: variant.internalCode->Option.getWithDefault(""),
          alcoholVolume: variant.alcoholVolume,
          year: variant.year->Option.map(Int.toFloat),
          bulk: variant.bulk->Option.getWithDefault(false),
          purchasePrice: variant.purchasedPrice->Option.getWithDefault(0.),
          supplierId: "",
          variantPrices: None,
          initialStockQuantity: 0.,
          initialStockComment: "",
          maxStockThreshold: variant.maxStockThreshold->Option.getWithDefault(0)->Float.fromInt,
          minStockThreshold: variant.minStockThreshold->Option.getWithDefault(0)->Float.fromInt,
          stockOrderTriggerThreshold: variant.stockOrderTriggerThreshold
          ->Option.getWithDefault(0)
          ->Float.fromInt,
        },
        formattedName: variant.formattedName,
        shopName,
      })
    // Variant creation
    | (_, Executed({data: Some(data)}), _, Some({name: shopName})) =>
      let product = productFromData(data)

      Data({
        id: product.id,
        productKind: product.kind,
        productTaxRate: Some(product.tax.value),
        productValues: None,
        initialValues: {
          name: "",
          capacityValue: None,
          capacityPrecision: None,
          capacityUnit: product.kind->CatalogVariant.CapacityUnit.getDefaultFromProductKind,
          ean13: "",
          stockKeepingUnit: "",
          priceLookUpCode: {
            invalidValues: [],
            value: None,
          },
          internalCode: "",
          alcoholVolume: None,
          year: None,
          bulk: false,
          purchasePrice: 0.,
          supplierId: "",
          variantPrices: Some([]),
          initialStockQuantity: 0.,
          initialStockComment: "",
          maxStockThreshold: 0.,
          minStockThreshold: 0.,
          stockOrderTriggerThreshold: 0.,
        },
        formattedName: product.name,
        shopName,
      })
    // Reference creation (second step)
    | (Unexecuted(_), Unexecuted(_), Some(Ok(product)), Some({name: shopName})) =>
      Data({
        id: "",
        productKind: product.kind,
        productTaxRate: product.tax->Option.map(({value}) => value),
        productValues: Some(product),
        initialValues: {
          name: "",
          capacityValue: None,
          capacityPrecision: None,
          capacityUnit: product.kind->CatalogVariant.CapacityUnit.getDefaultFromProductKind,
          ean13: "",
          stockKeepingUnit: "",
          priceLookUpCode: {
            invalidValues: [],
            value: None,
          },
          internalCode: "",
          alcoholVolume: None,
          year: None,
          bulk: false,
          purchasePrice: 0.,
          supplierId: "",
          variantPrices: Some([]),
          initialStockQuantity: 0.,
          initialStockComment: "",
          maxStockThreshold: 0.,
          minStockThreshold: 0.,
          stockOrderTriggerThreshold: 0.,
        },
        formattedName: product.name,
        shopName,
      })
    | _ => Error
    }
  }
}
