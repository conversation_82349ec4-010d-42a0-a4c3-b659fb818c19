let isIndependentKindShops = (~authScope) =>
  switch (authScope: Auth.scope) {
  | Single({kind: #INDEPENDENT})
  | Organisation({activeShop: Some({kind: #INDEPENDENT})}) => true
  | Organisation({activeShop: None, shops}) =>
    shops->Array.every(shop => shop.kind === #INDEPENDENT)
  | Organisation(_) | Single(_) => false
  }

@react.component
let make = () => {
  let {query, path, pathname} = Navigation.useUrl()
  let shopsIndependentKind = isIndependentKindShops(~authScope=Auth.useScope())

  // NOTE - useRef -> the URL search query may be modified from children components
  let catalogLabelQueryStringRef = React.useRef(CatalogRoutes.CatalogLabelQueryString.decode(query))
  let {
    variantIdfromLabelEditSettings,
    fromPathname: fromPathnameLabelEditSettings,
  } = catalogLabelQueryStringRef.current

  React.useEffect1(() => {
    catalogLabelQueryStringRef.current = CatalogRoutes.CatalogLabelQueryString.decode(query)
    None
  }, [pathname])

  <Notifier.Provider value={Notifier.createContext()}>
    {switch path->List.fromArray {
    | list{"catalog", "product", "create"} => <CatalogProductEditContainer mode=Create />
    | list{"catalog", "product", cku} => <CatalogProductContainer cku />
    | list{"catalog", "product", cku, "edit"} => <CatalogProductEditContainer cku mode=Edit />
    | list{"catalog", "labels", "create"} => <CatalogLabelCreatePage />
    | list{"catalog", "labels", "create", "settings"} =>
      <CatalogLabelCreatePage
        settingsOnly={switch (fromPathnameLabelEditSettings, variantIdfromLabelEditSettings) {
        | (Some(fromPathname), Some(variantIdFromCatalogRedirection)) =>
          Some({
            CatalogLabelCreatePage.CatalogLabelCreateLabelPrintForm.fromPathname,
            variantIdFromCatalogRedirection,
          })
        | _ => None
        }}
      />
    | list{"catalog", "inventory", "import"} => <CatalogInventoryImportPage />
    | list{"catalog", "create"} => <CatalogVariantEditContainer mode=Create />
    | list{"catalog", cku, "create"} => <CatalogVariantEditContainer cku mode=Create />
    | list{"catalog", cku, "edit"} => <CatalogVariantEditContainer cku mode=Edit />
    | list{"catalog", cku} =>
      <CatalogVariantContainer
        cku fromLabelEditSettingsRedirected={Option.isSome(fromPathnameLabelEditSettings)}
      />
    | list{"catalog", "redirect", id} => <CatalogVariantByIdPage id />
    | list{"catalog", "product", "redirect", cku} => <CatalogProductByCkuPage cku />
    | list{"catalog", "create", "redirect", cku} =>
      <CatalogProductByCkuPage cku redirectToCreateVariant=true />
    | list{"catalog", cku, "stockactivities"} => <CatalogVariantStockActivityPage cku />
    | list{"catalog", cku, "ordersupplies"} => <CatalogVariantOrderSupplyPage cku />
    | list{"catalog", "duplication", cku} =>
      <CatalogDuplicationContainer cku mode=CatalogDuplication.Mode.Variant />
    | list{"catalog", "product", "duplication", cku} =>
      <CatalogDuplicationContainer cku mode=CatalogDuplication.Mode.Product />
    | list{_} => <CatalogListPage shopsIndependentKind variantIdfromLabelEditSettings />
    | _ => <Navigation.Redirect route=CatalogRoutes.baseRoute />
    }}
  </Notifier.Provider>
}
