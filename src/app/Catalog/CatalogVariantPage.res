open Intl

module Config = CatalogVariant__Config

let mapToVariantPrices = (
  ~shopsVariantRetailPrices: array<array<Config.variantRetailPrice>>,
  ~shopsVariantPurchasePrice: array<Config.variantPurchasePrice>,
) =>
  Array.concatMany(shopsVariantRetailPrices)->Array.map(retailPrice => {
    CatalogVariantRetailPrice.id: retailPrice.id,
    priceId: retailPrice.priceId,
    variantId: retailPrice.variantId,
    shopName: retailPrice.shopName,
    name: retailPrice.name,
    valueIncludingTax: retailPrice.valueIncludingTax,
    valueExcludingTax: retailPrice.valueExcludingTax,
    taxIncluded: retailPrice.taxIncluded,
    taxRate: retailPrice.taxRate,
    purchasePrice: shopsVariantPurchasePrice
    ->Array.getBy(current => current.shopId === retailPrice.shopId)
    ->Option.flatMap(({purchasePrice}) => purchasePrice)
    ->Option.getWithDefault(0.),
    toQuantity: retailPrice.toQuantity,
    fromQuantity: retailPrice.fromQuantity,
    capacityUnit: retailPrice.capacityUnit,
    edited: false,
  })

module AuthSelectShop = {
  @react.component
  let make = (~variantShopIds) => {
    let shops = Auth.useShops()

    let disabledIds = shops->Array.keepMap(({id}) =>
      switch variantShopIds->Array.getBy(current => current === id) {
      | Some(_) => None
      | _ => Some(id)
      }
    )

    <Auth.SelectShopFilter disabledIds />
  }
}

@react.component
let make = (
  ~fromLabelEditSettingsRedirected,
  ~cku,
  ~variantShopIds,
  ~shopsProduct: array<Config.variantProductInformation>,
  ~shopsVariant: array<Config.variantInformation>,
  ~shopsVariantStock: array<Config.variantStockInformation>,
  ~shopsVariantPurchasePrice: array<Config.variantPurchasePrice>,
  ~shopsVariantRetailPrices: array<array<Config.variantRetailPrice>>,
) => {
  let notifier = Notifier.use()
  let scope = Auth.useScope()

  let someVariant = shopsVariant->Array.length > 0
  let onRequestNotification = value =>
    switch value {
    | Ok(message) => notifier.reset(Success(message), ())
    | Error(message) => notifier.reset(Error(message), ())
    }

  let renderActions = () =>
    switch (scope, shopsVariant[0]) {
    | (Single(_), Some(variant)) =>
      <CatalogVariantPageActions
        cku variant fromLabelEditSettingsRedirected onRequestNotification
      />
    | _ => React.null
    }

  let renderHeaderActions = switch scope {
  | Organisation({shops}) =>
    Some(
      () =>
        <Inline align=#spaceBetween grow=true>
          <Inline space=#normal>
            {if shops->Array.length < 6 {
              <AuthSelectShop variantShopIds />
            } else {
              React.null
            }}
            {switch someVariant {
            | false =>
              <TextStyle variation=#negative>
                {t("No variant association to this shop has been recorded.")->React.string}
              </TextStyle>
            | _ => React.null
            }}
          </Inline>
          {switch shopsVariant[0] {
          | Some(variant) =>
            <CatalogVariantPageActions
              cku variant fromLabelEditSettingsRedirected onRequestNotification
            />
          | _ => React.null
          }}
        </Inline>,
    )
  | Single(_) => None
  }

  let pageTitle = shopsVariant[0]->Option.map(({formattedName}) => formattedName)
  let renderTitleEnd = () =>
    switch (shopsVariant[0], scope) {
    | (Some(variant), Organisation({activeShop: Some(_)}))
    | (Some(variant), Single(_)) =>
      <div style={ReactDOM.Style.make(~alignSelf="center", ())}>
        <CatalogProductStatusBadge value=variant.status />
      </div>
    | _ => React.null
    }

  <Page title=?pageTitle renderTitleEnd renderActions ?renderHeaderActions>
    {if someVariant {
      <>
        <Notifier.Banner notifier />
        <Box spaceTop=#medium>
          <Stack space=#medium>
            <Group wrap=false grid=["30%", "70%"] spaceX=#medium>
              <Stack space=#medium>
                <CatalogVariantInformationCard shopsVariant />
                <CatalogVariantStockTableCard cku shopsVariantStock />
                <CatalogVariantProductInformationCard cku shopsProduct />
                {switch scope {
                | Organisation({activeShop: None}) => <CatalogVariantStatusTableCard shopsVariant />
                | _ => React.null
                }}
                <CatalogVariantNotesCard
                  shopsNotes={shopsVariant->Array.map(variant => {
                    CatalogVariantNotesCard.variantId: variant.id,
                    shopId: variant.shopId,
                    internalNote: variant.internalNote,
                    tastingNote: variant.tastingNote,
                  })}
                />
              </Stack>
              <Stack space=#medium>
                <CatalogVariantRetailPriceTableCard
                  cku
                  variantPrices={mapToVariantPrices(
                    ~shopsVariantRetailPrices,
                    ~shopsVariantPurchasePrice,
                  )}
                />
                <CatalogVariantPurchasePriceTableCard
                  shopsVariantPrices={Array.zip(
                    shopsVariantPurchasePrice,
                    shopsVariantRetailPrices,
                  )->Array.map(((purchasePriceData, retailPricesData)) => {
                    CatalogVariantPurchasePriceTableCard.purchasePriceData,
                    retailPricesData,
                  })}
                />
                <CatalogVariantStockActivityTableCard cku />
                <CatalogVariantOrderSupplyTableCard cku />
                {switch (shopsVariantStock[0], scope, shopsVariant[0]) {
                | (
                    Some(shopsVariantStock),
                    Single(_) | Organisation({activeShop: Some(_)}),
                    Some({bulk: false}),
                  ) =>
                  <CatalogVariantStockThresholdFormCard
                    variantId=shopsVariantStock.variantId
                    minStockThreshold=shopsVariantStock.minStockThreshold
                    maxStockThreshold=shopsVariantStock.maxStockThreshold
                    stockOrderTriggerThreshold=shopsVariantStock.stockOrderTriggerThreshold
                  />
                | _ => React.null
                }}
              </Stack>
            </Group>
          </Stack>
        </Box>
      </>
    } else {
      React.null
    }}
  </Page>
}

let make = React.memo(make)
