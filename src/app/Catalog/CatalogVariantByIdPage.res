module Query = %graphql(`
  query variant($id: ID!) {
    variant(id: $id) {
      cku
    }
  }
`)

// NOTE - It is a temporary solution to access the variants
// via their #id, passing by an intermediate request retrieving
// their #cku and redirect to the page that displays the variant
// according to the #cku
@react.component
let make = (~id: string) => {
  let navigate = Navigation.useNavigate()
  let activeShop = Auth.useActiveShop()
  let activeShopId = activeShop->Option.map(shop => shop.id)
  let (executeQuery, queryResults) = Query.useLazy()

  React.useEffect1(() => {
    executeQuery(Query.makeVariables(~id, ()))
    None
  }, [activeShopId])

  switch queryResults {
  | Executed({data: Some({variant: Some(variant)})}) =>
    navigate(Catalog->LegacyRouter.routeToPathname ++ ("/" ++ variant.cku), ~replace=true)
    React.null
  | Unexecuted(_)
  | Executed({loading: true}) =>
    <Placeholder status=Loading />
  | _ => <Placeholder status=Error />
  }
}

let make = React.memo(make)
