module Config = CatalogProduct__Config
module ProductQuery = CatalogProduct__Queries.ProductQuery
module ProductShopsQuery = CatalogProduct__Queries.ProductShopsQuery

@react.component
let make = (~cku: string) => {
  let (executeQuery, queryResults) = ProductQuery.useLazy(~fetchPolicy=CacheAndNetwork, ())
  let (executeShopsQuery, queryShopsResults) = ProductShopsQuery.useLazy(
    ~fetchPolicy=NetworkOnly,
    (),
  )
  let activeShop = Auth.useActiveShop()
  let activeShopId = activeShop->Option.map(shop => shop.id)

  // FIXME - double query because of previous render activeShop value
  React.useEffect1(() => {
    executeQuery(
      ProductQuery.makeVariables(
        ~cku=cku->Scalar.CKU.serialize,
        ~filterBy=Config.makeFilterBy(~activeShopId),
        ~productVariantsFilterBy=ProductQuery.makeInputObjectInputProductVariantsQueryFilter(
          ~archived=#INCLUDED,
          (),
        ),
        (),
      ),
    )
    None
  }, [activeShopId])

  React.useEffect0(() => {
    executeShopsQuery(ProductShopsQuery.makeVariables(~cku=cku->Scalar.CKU.serialize, ()))
    None
  })

  // Get all existing shop IDs from products (limit 50 first nodes)
  let productShopIds = switch queryShopsResults {
  | Executed({data: Some(data)}) =>
    data.productsByVariantCku.edges->Array.map(({node: data}) => data.shop.id)
  | _ => []
  }

  switch queryResults {
  | Executed({data: Some(data)}) =>
    <CatalogProductPage
      cku
      productShopIds
      shopsProduct={data->Config.productsInformationFromData}
      shopsProductVariants={data->Config.productsVariantsFromData}
    />
  | Unexecuted(_)
  | Executed({loading: true}) =>
    <Placeholder status=Loading />
  | _ => <Placeholder status=Error />
  }
}

let make = React.memo(make)
