open CatalogDuplication

module ProductsQuery = CatalogDuplication__Queries.ProductsQuery
module VariantsQuery = CatalogDuplication__Queries.VariantsQuery

type productData = ProductsQuery.ProductsQuery_inner.t
type variantData = VariantsQuery.VariantsQuery_inner.t

type reference = {
  id: string,
  shopId: string,
  formattedName: string,
  variantsCku: array<string>,
  productName: string,
  productKind: CatalogProduct.Kind.t,
  productColor: option<CatalogProduct.Color.t>,
  productProducer: option<string>,
}

type data = {
  references: array<reference>,
  shopsData: array<Shop.t>,
  initialValues: CatalogDuplicationForm.Lenses.state,
}

type t =
  | Error
  | Loading
  | Data(data)

type productResponse = ApolloClient__React_Types.LazyQueryResult.t<
  ProductsQuery.ProductsQuery_inner.t,
  ProductsQuery.ProductsQuery_inner.Raw.t,
  ProductsQuery.ProductsQuery_inner.t_variables,
  ProductsQuery.ProductsQuery_inner.Raw.t_variables,
>

type variantResponse = ApolloClient__React_Types.LazyQueryResult.t<
  VariantsQuery.VariantsQuery_inner.t,
  VariantsQuery.VariantsQuery_inner.Raw.t,
  VariantsQuery.VariantsQuery_inner.t_variables,
  VariantsQuery.VariantsQuery_inner.Raw.t_variables,
>

let referencesFromProductData: (productData, ~shops: array<Auth.shop>) => array<reference> = (
  data,
  ~shops,
) =>
  data.productsByVariantCku.edges
  ->Array.map(({node: product}) => {
    id: product.id,
    shopId: product.shop.id,
    formattedName: product.name,
    variantsCku: product.variants.edges->Array.map(({node: variant}) => variant.cku),
    productName: product.name,
    productKind: product.kind,
    productColor: product.color,
    productProducer: product.producer,
  })
  ->Array.keep(product => shops->Array.getBy(shop => shop.id === product.shopId)->Option.isSome)

let referencesFromVariantData: (variantData, ~shops: array<Auth.shop>) => array<reference> = (
  data,
  ~shops,
) =>
  data.variantsByCku.edges
  ->Array.keepMap(({node: variant}) => Some({
    id: variant.id,
    shopId: variant.shop.id,
    formattedName: variant.formattedName,
    variantsCku: variant.product.variants.edges->Array.map(({node: variant}) => variant.cku),
    productName: variant.product.name,
    productKind: variant.product.kind,
    productColor: variant.product.color,
    productProducer: variant.product.producer,
  }))
  ->Array.keep(variant => shops->Array.getBy(shop => shop.id === variant.shopId)->Option.isSome)

let shopsDataFromReferences: (array<reference>, ~shops: array<Auth.shop>) => array<Shop.t> = (
  references,
  ~shops,
) =>
  shops->Array.map(shop => {
    open Shop
    {
      dataId: shop.id,
      dataName: shop.name,
      duplicability: switch references->Array.getBy(reference => reference.shopId === shop.id) {
      | Some(_) => ExistsAlready
      | _ => Duplicable
      },
    }
  })

let use = (
  ~productResponse: productResponse,
  ~variantResponse: variantResponse,
  ~duplicationMode: Mode.t,
) => {
  let scope = Auth.useScope()
  let shops = Auth.useShops()
  let activeShop = Auth.useActiveShop()

  switch shops {
  | [] => Loading
  | _ =>
    switch (duplicationMode, productResponse, variantResponse, scope) {
    // Loading
    | (Product, Unexecuted(_), _, _)
    | (Variant, _, Unexecuted(_), _)
    | (Product, Executed({loading: true}), _, _)
    | (Variant, _, Executed({loading: true}), _) =>
      Loading
    // Product with its variants duplication
    | (Product, Executed({data: Some(productData)}), _, Organisation(_)) =>
      let references = productData->referencesFromProductData(~shops)
      let shopsData = references->shopsDataFromReferences(~shops)
      // Picks active shop by default
      let source = switch (
        activeShop,
        references->Array.getBy(({shopId}) =>
          Some(shopId) === activeShop->Option.map(({id}) => id)
        ),
      ) {
      | (Some({id: shopId}), Some({id})) => Some(Source.Product({id, shopId: Some(shopId)}))
      | _ => None
      }
      // Picks only available shop by default
      let source = switch shopsData->Array.keep(shop => shop.duplicability === ExistsAlready) {
      | availableShops if availableShops->Array.length === 1 =>
        references
        ->Array.getBy(({shopId}) => shopId === (availableShops->Array.getExn(0)).dataId)
        ->Option.mapWithDefault(source, ({id, shopId}) => Some(
          Source.Product({id, shopId: Some(shopId)}),
        ))
      | _ => source
      }

      Data({
        references,
        shopsData,
        initialValues: {
          source,
          destinations: [],
        },
      })
    // Variant duplication
    | (Variant, _, Executed({data: Some(variantData)}), Organisation(_)) =>
      let references = variantData->referencesFromVariantData(~shops)
      let shopsData = references->shopsDataFromReferences(~shops)
      // Picks active shop by default
      let source = switch (
        activeShop,
        references->Array.getBy(({shopId}) =>
          Some(shopId) === activeShop->Option.map(({id}) => id)
        ),
      ) {
      | (Some({id: shopId}), Some({id})) => Some(Source.Variant({id, shopId: Some(shopId)}))
      | _ => None
      }
      // Picks only available shop by default
      let source = switch shopsData->Array.keep(shop => shop.duplicability === ExistsAlready) {
      | availableShops if availableShops->Array.length === 1 =>
        references
        ->Array.getBy(({shopId}) => shopId === (availableShops->Array.getExn(0)).dataId)
        ->Option.mapWithDefault(source, ({id, shopId}) => Some(
          Source.Variant({id, shopId: Some(shopId)}),
        ))
      | _ => source
      }

      Data({
        references,
        shopsData,
        initialValues: {
          source,
          destinations: [],
        },
      })
    // Failure
    | _ => Error
    }
  }
}
