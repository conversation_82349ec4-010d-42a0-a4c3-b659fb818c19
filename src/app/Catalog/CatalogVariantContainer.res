module Config = CatalogVariant__Config

module VariantQuery = CatalogVariant__Queries.VariantQuery
module VariantShopsQuery = CatalogVariant__Queries.VariantShopsQuery
module PricesQuery = CatalogVariant__Queries.PricesQuery

@react.component
let make = (~cku: string, ~fromLabelEditSettingsRedirected) => {
  let (executeVariantQuery, variantQueryResults) = VariantQuery.useLazy()
  let (executeShopsQuery, variantShopsQueryResults) = VariantShopsQuery.useLazy()
  let (executePricesQuery, pricesQueryResults) = PricesQuery.useLazy()
  let activeShop = Auth.useActiveShop()

  // Executes variants, variantShops and prices queries
  React.useEffect1(() => {
    executeVariantQuery(
      VariantQuery.makeVariables(
        ~cku=cku->Scalar.CKU.serialize,
        ~filterBy=Config.makeVariantFilterBy(~activeShop?, ()),
        (),
      ),
    )
    executeShopsQuery(VariantShopsQuery.makeVariables(~cku=cku->Scalar.CKU.serialize, ()))
    executePricesQuery(PricesQuery.makeVariables())
    None
  }, [activeShop->Option.map(({id}) => id)])

  // Gets results from queries responses
  let results = Config.use(
    ~variantResponse=variantQueryResults,
    ~variantShopsResponse=variantShopsQueryResults,
    ~pricesResponse=pricesQueryResults,
  )

  switch results {
  | Error => <Placeholder status=Error />
  | Loading => <Placeholder status=Loading />
  | Data({
      variantShopIds,
      shopsVariant,
      shopsProduct,
      shopsVariantStock,
      shopsVariantPurchasePrice,
      shopsVariantRetailPrices,
    }) =>
    <CatalogVariantPage
      fromLabelEditSettingsRedirected
      cku
      variantShopIds
      shopsVariant
      shopsProduct
      shopsVariantStock
      shopsVariantPurchasePrice
      shopsVariantRetailPrices
    />
  }
}

let make = React.memo(make)
