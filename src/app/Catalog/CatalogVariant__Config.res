open Product
open Intl

module VariantQuery = CatalogVariant__Queries.VariantQuery
module VariantShopsQuery = CatalogVariant__Queries.VariantShopsQuery
module PricesQuery = CatalogVariant__Queries.PricesQuery

module ProductConfig = CatalogProduct__Config

exception CatalogVariant__Config_TaxRateNotFound

let makeVariantFilterBy = (~activeShop: option<Auth.shop>=?, ()) =>
  VariantQuery.makeInputObjectInputVariantsByCkuQueryFilter(
    ~shopIds=?switch activeShop {
    | Some({id}) => Some(VariantQuery.makeInputObjectInFilter(~_in=[id], ()))
    | _ => None
    },
    ~archived=#INCLUDED,
    (),
  )

type variantData = VariantQuery.VariantQuery_inner.t
type variantShopsData = VariantShopsQuery.VariantShopsQuery_inner.t
type pricesData = PricesQuery.PricesQuery_inner.t

type variantResponse = ApolloClient__React_Types.LazyQueryResult.t<
  VariantQuery.VariantQuery_inner.t,
  VariantQuery.VariantQuery_inner.Raw.t,
  VariantQuery.VariantQuery_inner.t_variables,
  VariantQuery.VariantQuery_inner.Raw.t_variables,
>

type variantShopsResponse = ApolloClient__React_Types.LazyQueryResult.t<
  VariantShopsQuery.VariantShopsQuery_inner.t,
  VariantShopsQuery.VariantShopsQuery_inner.Raw.t,
  VariantShopsQuery.VariantShopsQuery_inner.t_variables,
  VariantShopsQuery.VariantShopsQuery_inner.Raw.t_variables,
>

type pricesResponse = ApolloClient__React_Types.LazyQueryResult.t<
  PricesQuery.PricesQuery_inner.t,
  PricesQuery.PricesQuery_inner.Raw.t,
  PricesQuery.PricesQuery_inner.t_variables,
  PricesQuery.PricesQuery_inner.Raw.t_variables,
>

type variantNotes = {
  tastingNote: string,
  internalNote: string,
}

type variantInformation = {
  id: string,
  shopId: string,
  shopName: string,
  updatedAt: Js.Date.t,
  status: option<CatalogProduct.Status.t>,
  active: bool,
  name: string,
  formattedName: string,
  formattedCapacity: option<string>,
  year: option<string>,
  formattedAlcoholVolume: option<string>,
  ean13: option<string>,
  stockKeepingUnit: option<string>,
  priceLookUpCode: option<int>,
  internalCode: option<string>,
  purchasePrice: option<float>,
  taxRate: float,
  internalNote: string,
  tastingNote: string,
  capacityUnit: option<string>,
  bulk: bool,
}

type variantRetailPrice = {
  id: option<string>,
  priceId: string,
  variantId: string,
  shopId: string,
  shopName: string,
  name: string,
  valueExcludingTax: float,
  valueIncludingTax: float,
  taxIncluded: bool,
  taxRate: float,
  toQuantity: option<float>,
  fromQuantity: option<float>,
  capacityUnit: option<string>,
}

type variantPurchasePrice = {
  id: string,
  shopId: string,
  shopName: string,
  supplierId: string,
  supplierName: string,
  purchasePrice: option<float>,
  formattedPurchasedPrice: option<string>,
  capacityUnit: option<string>,
}

type variantStockInformation = {
  variantId: string,
  shopId: string,
  shopName: string,
  rawQuantity: int,
  formattedQuantity: option<string>,
  state: option<Stock.t>,
  capacityUnit: option<string>,
  capacityPrecision: option<int>,
  maxStockThreshold: float,
  minStockThreshold: float,
  stockOrderTriggerThreshold: float,
}

type variantProductInformation = ProductConfig.productInformation

type data = {
  variantShopIds: array<string>,
  shopsProduct: array<variantProductInformation>,
  shopsVariant: array<variantInformation>,
  shopsVariantStock: array<variantStockInformation>,
  shopsVariantPurchasePrice: array<variantPurchasePrice>,
  shopsVariantRetailPrices: array<array<variantRetailPrice>>,
}

type t =
  | Error
  | Loading
  | Data(data)

let variantsInformationFromData: variantData => array<variantInformation> = data =>
  data.variantsByCku.edges->Array.map(({node: data}) => {
    id: data.id,
    shopId: data.shop.id,
    shopName: data.shop.name,
    updatedAt: data.updatedAt,
    status: switch data.formattedStatus {
    | #ACTIVE => Some(Active)
    | #INACTIVE => Some(Inactive)
    | #ARCHIVED => Some(Archived)
    | #VARIABLE => None
    },
    active: data.active->Option.getWithDefault(false),
    name: data.name,
    formattedName: data.formattedName,
    formattedCapacity: data.capacityValue->Option.map(capacityValue =>
      switch data.capacityUnit {
      | Some(capacityUnit) =>
        capacityValue->decimalFormat(~maximumFractionDigits=2) ++ " " ++ capacityUnit
      | None => capacityValue->decimalFormat(~maximumFractionDigits=2)
      }
    ),
    year: data.year->Option.map(year => year->Int.toString),
    formattedAlcoholVolume: data.alcoholVolume->Option.map(alcoholVolume =>
      alcoholVolume->decimalFormat(~maximumFractionDigits=1) ++ "°"
    ),
    ean13: data.ean13,
    stockKeepingUnit: data.stockKeepingUnit,
    priceLookUpCode: data.priceLookUpCode,
    internalCode: data.internalCode,
    purchasePrice: data.purchasedPrice,
    taxRate: data.product.tax.value,
    internalNote: data.internalNote->Option.getWithDefault(""),
    tastingNote: data.tastingNote->Option.getWithDefault(""),
    capacityUnit: switch (data.bulk, data.capacityUnit) {
    | (Some(true), Some(unit)) => Some(unit)
    | _ => None
    },
    bulk: data.bulk->Option.getWithDefault(false),
  })

let variantsRetailPricesFromData: variantData => array<array<variantRetailPrice>> = data =>
  data.variantsByCku.edges->Array.map(({node: data}) =>
    data.variantPrices.edges
    ->SortArray.stableSortBy(({node: variantPrice}, _) =>
      variantPrice.price->Option.map(price => price.enableByDefault)->Option.getWithDefault(false)
        ? 0
        : 1
    )
    ->Array.keepMap(({node: variantPrice}) => {
      let taxIncluded =
        variantPrice.price->Option.map(price => price.taxIncluded)->Option.getWithDefault(false)

      switch (variantPrice.price, variantPrice.archivedAt) {
      | (Some({id: priceId, name: priceName, archivedAt: None}), None) =>
        Some({
          id: Some(variantPrice.id),
          priceId,
          variantId: data.id,
          shopId: data.shop.id,
          shopName: data.shop.name,
          name: priceName,
          valueExcludingTax: variantPrice.valueExcludingTax,
          valueIncludingTax: variantPrice.valueIncludingTax,
          taxIncluded,
          taxRate: data.product.tax.value,
          toQuantity: variantPrice.toQuantity,
          fromQuantity: variantPrice.fromQuantity,
          capacityUnit: switch (data.bulk, data.capacityUnit) {
          | (Some(true), Some(unit)) => Some(unit)
          | _ => None
          },
        })
      | _ => None
      }
    })
  )

let variantsPurchasePriceFromData: variantData => array<variantPurchasePrice> = data =>
  data.variantsByCku.edges->Array.map(({node: data}) => {
    id: data.id,
    shopId: data.shop.id,
    shopName: data.shop.name,
    supplierId: data.supplier->Option.mapWithDefault("", ({id}) => id),
    supplierName: data.supplier->Option.mapWithDefault("", ({companyName}) => companyName),
    purchasePrice: data.purchasedPrice,
    formattedPurchasedPrice: data.formattedPurchasedPrice,
    capacityUnit: switch (data.bulk, data.capacityUnit) {
    | (Some(true), Some(unit)) => Some(unit)
    | _ => None
    },
  })

let variantsStockInformationFromData: variantData => array<variantStockInformation> = data =>
  data.variantsByCku.edges->Array.map(({node: data}) => {
    variantId: data.id,
    shopId: data.shop.id,
    shopName: data.shop.name,
    rawQuantity: data.stock.rawQuantity->Option.getWithDefault(0),
    formattedQuantity: data.stock.formattedQuantity,
    state: data.stock.state,
    capacityUnit: switch (data.bulk, data.capacityUnit) {
    | (Some(true), Some(unit)) => Some(unit)
    | _ => None
    },
    capacityPrecision: switch (data.bulk, data.capacityPrecision) {
    | (Some(true), Some(precision)) => Some(precision)
    | _ => None
    },
    maxStockThreshold: data.maxStockThreshold->Option.mapWithDefault(0., Float.fromInt),
    minStockThreshold: data.minStockThreshold->Option.mapWithDefault(0., Float.fromInt),
    stockOrderTriggerThreshold: data.stockOrderTriggerThreshold->Option.mapWithDefault(
      0.,
      Float.fromInt,
    ),
  })

// TODO - has to be a dedicated record "Information" for multi-usages
// in order to get rid of status and shopName.
let variantProductsInformationFromData: variantData => array<variantProductInformation> = data =>
  data.variantsByCku.edges->Array.map(({node: data}): variantProductInformation => {
    id: data.product.id,
    name: data.product.name,
    status: None,
    description: data.product.formattedDescription,
    formattedOrigin: data.product.formattedOrigin,
    taxValue: data.product.tax.value,
    categoryId: None,
    formattedCategoryName: data.formattedCategory->Option.getWithDefault(t("Not classified")),
    shopId: data.shop.id,
    shopName: data.shop.name,
    internalNote: "",
  })

let makeRetailPricesList = (
  shopsVariant: array<variantInformation>,
  ~variantPrices: array<array<variantRetailPrice>>,
  ~pricesData: pricesData,
) =>
  shopsVariant->Array.reduceWithIndex([], (acc, variant, index) => {
    acc->Array.concat([
      Array.concat(
        variantPrices[index]->Option.getWithDefault([]),
        pricesData.prices.edges->Array.keepMap(({node: price}) => {
          switch variantPrices[index]
          ->Option.getWithDefault([])
          ->Array.some(variantPrice => variantPrice.priceId === price.id) {
          | false if price.shop.id === variant.shopId =>
            Some({
              id: None,
              priceId: price.id,
              variantId: variant.id,
              shopId: variant.shopId,
              shopName: variant.shopName,
              name: price.name,
              valueExcludingTax: 0.,
              valueIncludingTax: 0.,
              taxIncluded: price.taxIncluded,
              taxRate: variant.taxRate,
              fromQuantity: None,
              toQuantity: None,
              capacityUnit: variant.capacityUnit,
            })
          | _ => None
          }
        }),
      ),
    ])
  })

let use = (
  ~variantResponse: variantResponse,
  ~variantShopsResponse: variantShopsResponse,
  ~pricesResponse: pricesResponse,
) =>
  switch (variantResponse, variantShopsResponse, pricesResponse) {
  | (Executed({loading: true}), _, _)
  | (_, Executed({loading: true}), _)
  | (_, _, Executed({loading: true}))
  | (Unexecuted(_), _, _)
  | (_, Unexecuted(_), _)
  | (_, _, Unexecuted(_)) =>
    Loading
  | (
      Executed({data: Some(variantData)}),
      Executed({data: Some(variantShopsData)}),
      Executed({data: Some(pricesData)}),
    ) =>
    let shopsVariantRetailPrices =
      variantData
      ->variantsInformationFromData
      ->makeRetailPricesList(~pricesData, ~variantPrices=variantData->variantsRetailPricesFromData)
    let shopsVariantRetailPricesSorted =
      shopsVariantRetailPrices->Array.map(variantRetailPrices =>
        variantRetailPrices->SortArray.stableSortBy((current, next) =>
          Js.String.localeCompare(next.name, current.name) >= 0. &&
            current.fromQuantity->Option.getWithDefault(0.) >=
              next.fromQuantity->Option.getWithDefault(0.)
            ? 1
            : 0
        )
      )

    Data({
      variantShopIds: variantShopsData.variantsByCku.edges->Array.map(({node}) => node.shop.id),
      shopsVariant: variantData->variantsInformationFromData,
      shopsProduct: variantData->variantProductsInformationFromData,
      shopsVariantStock: variantData->variantsStockInformationFromData,
      shopsVariantPurchasePrice: variantData->variantsPurchasePriceFromData,
      shopsVariantRetailPrices: shopsVariantRetailPricesSorted,
    })
  | _ => Error
  }
