open Intl

module SupplierListQuery = %graphql(`
  query SupplierListPageQuery($after: String, $before: String, $filterBy: InputSuppliersQueryFilter, $first: Int, $last: Int, $search: String) {
    suppliers(after: $after, before: $before, filterBy: $filterBy, first: $first, last: $last, search: $search) {
      pageInfo {
        startCursor
        endCursor
      }
      totalCount
      edges {
        node {
          id
          archivedAt
          companyName
          contacts {
            edges {
              node {
                firstName
                lastName
                civility @ppxOmitFutureValue
              }
            }
          }
          email
          phoneNumber
          formattedOrdersTotalAmountIncludingTaxes
          shop {
            name
          }
        }
      }
    }
  }
`)

module UnarchiveSupplierMutation = %graphql(`
  mutation unarchiveSupplier($id: ID!) {
    unarchiveSupplier(id: $id) {
      id
      archivedAt
    }
  }
`)

module SupplierListFilters = {
  type t = {
    shop: option<Auth.shop>,
    status: SupplierStatus.t,
  }

  let jsonCodec = (~shops) =>
    JsonCodec.object2(
      ({shop, status}) => (shop->Option.map(shop => shop.id), status->SupplierStatus.toString),
      ((shopId, status)) => Ok({
        shop: shops->Array.getBy((shop: Auth.shop) => Some(shop.id) === shopId),
        status: status->SupplierStatus.fromString,
      }),
      JsonCodec.field("shopId", JsonCodec.string)->JsonCodec.optional,
      JsonCodec.field("status", JsonCodec.string),
    )
}

module SupplierTableRow = {
  type t = {
    id: string,
    archivedAt: option<Js.Date.t>,
    status: SupplierStatus.t,
    companyName: string,
    shopName: string,
    phoneNumber: option<string>,
    email: option<string>,
    firstName: option<string>,
    lastName: string,
    civility: option<SupplierCivility.t>,
    formattedOrdersTotalAmountIncludingTaxes: string,
  }

  let keyExtractor = value => value.id
}

module SupplierActionsCell = {
  @react.component
  let make = (~id, ~status, ~supplierShowRoute, ~supplierBaseRoute) => {
    let (unarchiveSupplier, _) = UnarchiveSupplierMutation.use()
    let unarchiveAction = MenuItem.Callback(() => unarchiveSupplier({id: id})->ignore)

    let (archiveModalOpened, setArchiveModalOpened) = React.useState(_ => false)
    let archiveAction = MenuItem.Callback(() => setArchiveModalOpened(_ => true))

    let seeAction = MenuItem.OpenLink(Route(supplierShowRoute(~id)))

    <>
      <Inline align=#end>
        <Menu variation=#more_round>
          <MenuItem content=Text(t("See")) action=seeAction />
          {switch status {
          | #ARCHIVED => <MenuItem content=Text(t("Unarchive")) action=unarchiveAction />
          | #UNARCHIVED => <MenuItem content=Text(t("Archive")) action=archiveAction />
          }}
        </Menu>
      </Inline>
      <SupplierArchiveModal
        id
        opened=archiveModalOpened
        onRequestClose={() => setArchiveModalOpened(_ => false)}
        supplierBaseRoute
      />
    </>
  }
}

let supplierTableRowsFromQueryResult = queryResult =>
  queryResult.SupplierListQuery.suppliers.edges->Array.map(edge => {
    SupplierTableRow.id: edge.node.id,
    companyName: edge.node.companyName,
    shopName: edge.node.shop.name,
    archivedAt: edge.node.archivedAt,
    status: switch edge.node.archivedAt {
    | Some(_) => #ARCHIVED
    | None => #UNARCHIVED
    },
    phoneNumber: edge.node.phoneNumber,
    email: edge.node.email,
    formattedOrdersTotalAmountIncludingTaxes: edge.node.formattedOrdersTotalAmountIncludingTaxes,
    civility: switch edge.node.contacts.edges {
    | [edge] => edge.node.civility
    | _ => None
    },
    firstName: switch edge.node.contacts.edges {
    | [edge] => edge.node.firstName
    | _ => None
    },
    lastName: switch edge.node.contacts.edges {
    | [edge] => edge.node.lastName
    | _ => "—"
    },
  })

let suppliersQueryVariablesFilterBy = ({SupplierListFilters.shop: shop, status}) => {
  SupplierListQuery.shopIds: switch shop {
  | Some({id: shopId}) => Some(SupplierListQuery.makeInputObjectInFilter(~_in=[shopId], ()))
  | _ => None
  },
  archived: switch status {
  | #UNARCHIVED => Some(#EXCLUDED)
  | #ARCHIVED => Some(#ONLY)
  },
}

let suppliersQueryVariables = ({
  LegacyResourceList.connectionArguments: connectionArguments,
  ?searchQuery,
  filters,
}) => {
  SupplierListQuery.first: connectionArguments.first,
  last: connectionArguments.last,
  before: connectionArguments.before,
  after: connectionArguments.after,
  search: searchQuery,
  filterBy: Some(suppliersQueryVariablesFilterBy(filters)),
}

@react.component
let make = (~supplierBaseRoute, ~supplierNewRoute, ~supplierShowRoute) => {
  let shops = Auth.useShops()
  let activeShop = Auth.useActiveShop()

  let initialFilters = {SupplierListFilters.shop: activeShop, status: #UNARCHIVED}
  let initialState = LegacyResourceList.initialState(~filters=initialFilters)
  let filtersJsonCodec = SupplierListFilters.jsonCodec(~shops)
  let resourceListPropState = LegacyResourceList.use(~initialState, ~filtersJsonCodec)
  let (state, dispatch) = resourceListPropState

  let queryAsyncResult =
    SupplierListQuery.use(suppliersQueryVariables(state))->ApolloHelpers.queryResultToAsyncResult

  let onRequestPaginate = React.useCallback2(action =>
    switch queryAsyncResult {
    | Reloading(Ok({suppliers: {totalCount, pageInfo: {startCursor, endCursor}}}))
    | Done(Ok({suppliers: {totalCount, pageInfo: {startCursor, endCursor}}})) =>
      let totalPages = LegacyResourceList.totalPages(totalCount, LegacyResourceList.edgesPerPage)
      let nextPage = LegacyResourceList.nextPage(~state, ~action, ~totalPages)
      let cursors = (startCursor, endCursor)
      switch nextPage {
      | Some(nextPage) => dispatch(LegacyResourceList.Navigated({nextPage, totalCount, cursors}))
      | None => ()
      }
    | NotAsked | Loading | Reloading(Error(_)) | Done(Error(_)) => ()
    }
  , (state.currentPage, queryAsyncResult))

  let tableColumns = [
    {
      Table.key: "company-name",
      name: t("Company name"),
      layout: {minWidth: 200.->#px, width: 25.->#pct, sticky: true},
      render: ({data: {SupplierTableRow.id: id, companyName, shopName}}) => {
        let shopName = shops->Array.length > 1 ? Some(shopName) : None
        <Box spaceY=#xxsmall>
          <Stack space={shopName->Option.isSome ? #xxsmall : #none}>
            <TextLink text=companyName to=Route(supplierShowRoute(~id)) />
            {switch shopName {
            | Some(shopName) =>
              <TextStyle size=#xxsmall variation=#normal> {shopName->React.string} </TextStyle>
            | _ => React.null
            }}
          </Stack>
        </Box>
      },
    },
    {
      key: "contact",
      name: t("Contact"),
      layout: {minWidth: 115.->#px, width: 1.2->#fr},
      render: ({data: {civility, firstName, lastName}}) => {
        let text =
          [civility->Option.map(SupplierCivility.toShortLabel), firstName, Some(lastName)]
          ->Array.keepMap(maybeString => maybeString)
          ->Array.joinWith(" ", a => a)
        <TextStyle> {text->React.string} </TextStyle>
      },
    },
    {
      key: "email",
      name: t("Email"),
      layout: {minWidth: 160.->#px, width: 1.5->#fr},
      render: ({data: {email}}) =>
        <TextStyle> {email->Option.getWithDefault("—")->React.string} </TextStyle>,
    },
    {
      key: "phone",
      name: t("Phone"),
      layout: {minWidth: 135.->#px},
      render: ({data: {phoneNumber}}) =>
        <TextStyle> {phoneNumber->Option.getWithDefault("—")->React.string} </TextStyle>,
    },
    {
      key: "total-purchases-incl",
      name: t("Total purchases incl."),
      layout: {minWidth: 135.->#px},
      render: ({data: {formattedOrdersTotalAmountIncludingTaxes}}) =>
        <TextStyle>
          {switch formattedOrdersTotalAmountIncludingTaxes->Float.fromString {
          | Some(amount) =>
            amount->currencyFormat(
              ~currency=#EUR,
              ~minimumFractionDigits=3,
              ~maximumFractionDigits=3,
            )
          | None => "0"
          }->React.string}
        </TextStyle>,
    },
    {
      key: "actions",
      layout: {minWidth: 70.->#px, width: 70.->#px},
      render: ({data: {id, status}}) =>
        <SupplierActionsCell id status supplierBaseRoute supplierShowRoute />,
    },
  ]

  let filters =
    <Inline space=#small>
      {if shops->Array.length > 1 {
        <Auth.SelectShopFilter
          value=?state.filters.shop
          onChange={shop => dispatch(FiltersUpdated(prev => {...prev, shop}))}
        />
      } else {
        React.null
      }}
      {if shops->Array.length > 1 {
        <Separator />
      } else {
        React.null
      }}
      <Select
        preset=#filter
        label={t("Status")}
        sections={
          let items = SupplierStatus.values->Array.map(value => {
            Select.label: value->SupplierStatus.toLabel,
            key: value->SupplierStatus.toString,
            value: Some(value),
          })

          [{title: t("Statuses"), items}]
        }
        value=Some(state.filters.status)
        onChange={status =>
          dispatch(FiltersUpdated(prev => {...prev, status: status->Option.getExn}))}
      />
      {switch state.filters.status {
      | #ARCHIVED =>
        <LegacyResourceListPage.ResetFiltersButton
          onPress={() => dispatch(FiltersUpdated(_ => initialState.filters))}
        />
      | _ => React.null
      }}
    </Inline>

  let actions =
    <ButtonLink variation=#primary to=Route(supplierNewRoute)>
      {t("Create supplier")->React.string}
    </ButtonLink>

  let searchBar =
    <SearchBar
      value=?state.searchQuery
      placeholder={t("Search a supplier")}
      onChange={searchQuery => dispatch(Searched(searchQuery))}
    />

  let tableEmptyState = switch state {
  | {currentPage: 1, filters: {shop, status: #UNARCHIVED}, searchQuery: ""}
    if shop === activeShop =>
    <EmptyState illustration=Illustration.create title={t("Welcome to the suppliers space.")} />
  | _ =>
    <EmptyState
      illustration=Illustration.notFound
      title={t("No result were found.")}
      text={t("Try again with another keyword/filter or:")}>
      <Button variation=#neutral onPress={_ => Reset(initialState)->dispatch}>
        {t("Clear search query and filters")->React.string}
      </Button>
    </EmptyState>
  }

  let tableKeyExtractor = SupplierTableRow.keyExtractor

  let paginationCurrentPage = state.currentPage
  let paginationTotalPages = switch queryAsyncResult {
  | Done(Ok({suppliers: {totalCount}})) =>
    LegacyResourceList.totalPages(totalCount, LegacyResourceList.edgesPerPage)
  | _ => 1
  }

  let tableData =
    queryAsyncResult
    ->AsyncResult.mapOk(supplierTableRowsFromQueryResult)
    ->AsyncResult.mapError(_ => ())

  <LegacyResourceListPage
    title={t("Suppliers")}
    filters
    actions
    searchBar
    tableColumns
    tableData
    tableKeyExtractor
    tableEmptyState
    paginationCurrentPage
    paginationTotalPages
    onRequestPaginate
  />
}
