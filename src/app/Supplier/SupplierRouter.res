@react.component
let make = (~subUrlPath) => {
  let url = Navigation.useUrl()
  let userRole = Auth.useRole()

  if Auth.isAuthorizedAccess(~role=userRole, ~targetPathname=SupplierRoutes.baseRoute) {
    switch subUrlPath {
    | list{} =>
      <SupplierListPage
        supplierBaseRoute=SupplierRoutes.baseRoute
        supplierShowRoute=SupplierRoutes.showRoute
        supplierNewRoute={SupplierRoutes.newRoute()}
      />
    | list{"new"} =>
      let shopId = SupplierRoutes.decodeCreateSupplierQueryString(url.query)
      <SupplierNewPage supplierShowRoute=SupplierRoutes.showRoute ?shopId />
    | list{id} =>
      <SupplierShowPage
        id
        supplierBaseRoute=SupplierRoutes.baseRoute
        supplierEditRoute=SupplierRoutes.editRoute
        supplierEditContactRoute=SupplierRoutes.editContactRoute
        supplierEditLocationRoute=SupplierRoutes.editLocationRoute
      />
    | list{id, "edit"} => <SupplierEditPage id supplierShowRoute=SupplierRoutes.showRoute />
    | list{id, "edit", "location"} =>
      <SupplierEditLocationPage id supplierShowRoute=SupplierRoutes.showRoute />
    | list{id, "edit", "contact"} =>
      <SupplierEditContactPage id supplierShowRoute=SupplierRoutes.showRoute />
    | list{id, "orders"} => <SupplierOrderListPage id />
    | _ => <Navigation.Redirect route=SupplierRoutes.baseRoute />
    }
  } else {
    <AccessDeniedPage />
  }
}
