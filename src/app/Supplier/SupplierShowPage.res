open Intl

module SupplierQuery = %graphql(`
  query SupplierShowPageQuery($id: ID!, $ordersFirst: Int) {
    supplier(id: $id) {
      id
      companyName
      updatedAt
      archivedAt
      intraCommunityVat
      internalCode
      phoneNumber
      mobileNumber
      email
      note
      siretNumber
      formattedOrdersTotalAmountIncludingTaxes
      shop {
        id
        name
      }
      contacts {
        edges {
          node {
            position
            civility @ppxOmitFutureValue
            firstName
            lastName
            formattedName
            mobileNumber
            phoneNumber
            email
          }
        }
      }
      locations {
        edges {
          node {
            label
            recipient
            address
            postalCode
            city
            country
          }
        }
      }
      orders (first: $ordersFirst) {
        edges {
          node {
            id
            formattedStatus @ppxOmitFutureValue
            formattedName
            estimatedReceptionDate
            receptionFinishedAt
            totalAmountExcludingTaxes
            totalAmountIncludingTaxes
            totalProductsQuantity
            shop {
              name
            }
          }
        }
        pageInfo {
          hasNextPage
        }
      }
    }
  }
`)

module SupplierEditNoteMutation = %graphql(`
  mutation SupplierEditNoteMutation($id: ID!, $supplierInput: InputUpdateSupplier!) {
    updateSupplier(id: $id, supplierInput: $supplierInput) {
      id
      note
      updatedAt
    }
  }
`)

module SupplierUnarchiveMutation = %graphql(`
   mutation SupplierUnarchiveMutation($id: ID!) {
    unarchiveSupplier(id: $id) {
      id
      archivedAt
    }
  }
`)

module SupplierStatusBadge = {
  @react.component
  let make = (~status) => {
    switch status {
    | #ARCHIVED => <Badge variation=#neutral> {t("Archived")->React.string} </Badge>
    | #UNARCHIVED => React.null
    }
  }
}

module SupplierNoteEditFormLenses = %lenses(type state = {note: string})
module SupplierNoteEditForm = Form.Make(SupplierNoteEditFormLenses)

module SupplierMenuEditingActions = {
  @react.component
  let make = (~id) => {
    let {makeVariables, makeInputObjectInputUpdateSupplier} = module(SupplierEditNoteMutation)
    let (updateSupplierNote, _) = SupplierEditNoteMutation.use()
    let updateSupplierNoteVariables = ({SupplierNoteEditFormLenses.note: note}) =>
      makeVariables(~id, ~supplierInput=makeInputObjectInputUpdateSupplier(~note, ()), ())
    let updateSupplierNote = values =>
      updateSupplierNote(updateSupplierNoteVariables(values))
      ->ApolloHelpers.mutationPromiseToFutureResult
      ->Future.mapOk(({SupplierEditNoteMutation.updateSupplier: {id}}) => Some(id))

    <Inline space=#small>
      <SupplierNoteEditForm.CancelButton text={t("Discard")} size=#medium />
      <SupplierNoteEditForm.SubmitButton
        text={t("Save")}
        variation=#success
        size=#medium
        onSubmit={(_, values) => updateSupplierNote(values)}
      />
    </Inline>
  }
}

module SupplierMenuReadingActions = {
  @react.component
  let make = (~id, ~shopId, ~status, ~onRequestArchive) => {
    let {makeVariables} = module(SupplierUnarchiveMutation)
    let (unarchiveSupplier, _) = SupplierUnarchiveMutation.use()
    let unarchiveSupplier = _ => unarchiveSupplier(makeVariables(~id, ()))->ignore

    let createOrderMenuAction = MenuItem.OpenLink(
      RouteWithQueryString(
        Order->LegacyRouter.routeToPathname ++ "/create",
        OrderUrlQueryString.CreateOrder.encode({supplierId: id, shopId}),
      ),
    )

    switch status {
    | #ARCHIVED =>
      <Button variation=#primary onPress={unarchiveSupplier}>
        {t("Unarchive")->React.string}
      </Button>
    | #UNARCHIVED =>
      <Menu>
        <MenuItem content=Text(t("Create order")) action=createOrderMenuAction />
        <MenuItem content=Text(t("Archive")) action=Callback(onRequestArchive) />
      </Menu>
    }
  }
}

module SupplierMenuActions = {
  @react.component
  let make = (~id, ~shopId, ~status, ~supplierBaseRoute) => {
    let formState = SupplierNoteEditForm.useFormState()
    let isFormPristine = formState.status->Form.Status.isPristine

    let (archiveModalOpened, setArchiveModalOpened) = React.useState(_ => false)

    <>
      {if isFormPristine {
        let onRequestArchive = () => setArchiveModalOpened(_ => true)
        <SupplierMenuReadingActions status id shopId onRequestArchive />
      } else {
        <SupplierMenuEditingActions id />
      }}
      <SupplierArchiveModal
        id
        opened=archiveModalOpened
        onRequestClose={() => setArchiveModalOpened(_ => false)}
        supplierBaseRoute
      />
    </>
  }
}

module SupplierInformationsCard = {
  @react.component
  let make = (
    ~id,
    ~companyName,
    ~email=?,
    ~phoneNumber=?,
    ~mobileNumber=?,
    ~intraCommunityVat=?,
    ~siretNumber=?,
    ~internalCode=?,
    ~supplierEditRoute,
  ) =>
    <Card
      title={t("Company information")}
      action={{
        icon: #edit_light,
        title: t("Edit information"),
        handler: OpenLink(Route(supplierEditRoute(~id))),
      }}>
      <Stack space=#medium>
        <Stack space=#xxsmall>
          <TextStyle weight=#strong> {companyName->React.string} </TextStyle>
          {switch email {
          | Some(email) => <TextStyle> {email->React.string} </TextStyle>
          | None => React.null
          }}
          {switch phoneNumber {
          | Some(phoneNumber) => <TextStyle> {phoneNumber->React.string} </TextStyle>
          | None => React.null
          }}
          {switch mobileNumber {
          | Some(mobileNumber) => <TextStyle> {mobileNumber->React.string} </TextStyle>
          | None => React.null
          }}
        </Stack>
        {switch (intraCommunityVat, siretNumber, internalCode) {
        | (Some("") | None, Some("") | None, Some("") | None) => React.null
        | _ =>
          <Stack space=#xxsmall>
            {switch intraCommunityVat {
            | None | Some("") => React.null
            | Some(intraCommunityVat) =>
              <InlineText>
                <TextStyle variation=#normal> {t("VAT n.")->React.string} </TextStyle>
                <TextStyle> {intraCommunityVat->React.string} </TextStyle>
              </InlineText>
            }}
            {switch siretNumber {
            | None | Some("") => React.null
            | Some(siretNumber) =>
              <InlineText>
                <TextStyle variation=#normal> {(t("Siret") ++ " ")->React.string} </TextStyle>
                <TextStyle> {siretNumber->React.string} </TextStyle>
              </InlineText>
            }}
            {switch internalCode {
            | None | Some("") => React.null
            | Some(internalCode) =>
              <TextStyle variation=#subdued> {internalCode->React.string} </TextStyle>
            }}
          </Stack>
        }}
      </Stack>
    </Card>
}

module SupplierLocationCard = {
  type item = {
    label: string,
    recipient?: string,
    address?: string,
    postalCode?: string,
    country?: string,
    city?: string,
  }

  let postalCodeCityCountry = (~postalCode=?, ~city=?, ~country=?, ()) => {
    let cityCountry =
      [city, country]
      ->Array.keepMap(maybeString =>
        switch maybeString {
        | Some("") | None => None
        | someString => someString
        }
      )
      ->Array.joinWith(", ", a => a)
    switch (postalCode, cityCountry) {
    | (None | Some(""), "") => None
    | (Some(postalCode), "") => Some(postalCode)
    | (None | Some(""), cityCountry) => Some(cityCountry)
    | (Some(postalCode), cityCountry) => Some(postalCode ++ " " ++ cityCountry)
    }
  }

  let cartTitle = t("Location")

  @react.component
  let make = (~id, ~supplierEditLocationRoute, ~items) => {
    // NOTE — the location id must be provided in the route path, not only the supplier id
    // TODO — handle multiple contacts per supplier.
    let handler = Card.Action.OpenLink(Route(supplierEditLocationRoute(~id)))
    switch items[0] {
    | Some({label, ?recipient, ?address, ?postalCode, ?country, ?city}) =>
      <Card title=cartTitle action={{icon: #edit_light, title: t("Edit location"), handler}}>
        <Stack space=#xxsmall>
          <TextStyle variation=#normal> {label->React.string} </TextStyle>
          {switch recipient {
          | None | Some("") => React.null
          | Some(recipient) => <TextStyle> {recipient->React.string} </TextStyle>
          }}
          {switch address {
          | None | Some("") => React.null
          | Some(address) => <TextStyle> {address->React.string} </TextStyle>
          }}
          {switch postalCodeCityCountry(~postalCode?, ~city?, ~country?, ()) {
          | Some(postalCodeCityCountry) =>
            <TextStyle> {postalCodeCityCountry->React.string} </TextStyle>
          | None => React.null
          }}
        </Stack>
      </Card>
    | None =>
      <Card title=cartTitle action={{icon: #edit_light, title: t("New location"), handler}}>
        <TextStyle variation=#subdued>
          {t("Location information not specified")->React.string}
        </TextStyle>
      </Card>
    }
  }
}

module SupplierContactCard = {
  let title = (~lastName, ~civility=?, ~firstName=?, ()) =>
    [civility->Option.map(SupplierCivility.toShortLabel), firstName, Some(lastName)]
    ->Array.keepMap(maybeString => maybeString)
    ->Array.joinWith(" ", a => a)

  type item = {
    civility?: [#MR | #MRS | #NEUTRAL],
    firstName?: string,
    lastName: string,
    email?: string,
    position?: string,
    phoneNumber?: string,
    mobileNumber?: string,
  }

  let cardTitle = t("Contact")

  @react.component
  let make = (~id, ~supplierEditContactRoute, ~items=[]) => {
    // NOTE — the contact id must be provided in the route path, not only the supplier id
    // TODO — Handle multiple contacts per supplier.
    let handler = Card.Action.OpenLink(Route(supplierEditContactRoute(~id)))
    switch items[0] {
    | Some({?civility, ?firstName, lastName, ?position, ?email, ?phoneNumber, ?mobileNumber}) =>
      <Card title=cardTitle action={{icon: #edit_light, title: t("Edit contact"), handler}}>
        <Stack space=#xxsmall>
          <TextStyle weight=#strong>
            {title(~civility?, ~firstName?, ~lastName, ())->React.string}
          </TextStyle>
          {switch position {
          | Some(position) => <TextStyle> {position->React.string} </TextStyle>
          | None => React.null
          }}
          {switch email {
          | Some(email) => <TextStyle> {email->React.string} </TextStyle>
          | None => React.null
          }}
          {switch phoneNumber {
          | Some(phoneNumber) => <TextStyle> {phoneNumber->React.string} </TextStyle>
          | None => React.null
          }}
          {switch mobileNumber {
          | Some(mobileNumber) => <TextStyle> {mobileNumber->React.string} </TextStyle>
          | None => React.null
          }}
        </Stack>
      </Card>
    | None =>
      <Card title=cardTitle action={{icon: #edit_light, title: t("New contact"), handler}}>
        <TextStyle variation=#subdued>
          {t("Contact information not specified")->React.string}
        </TextStyle>
      </Card>
    }
  }
}

module SupplierTotalPurchasedCard = {
  @react.component
  let make = (~value, ~currency=#EUR) => {
    // TODO - the GraphQL API should not returned pre formatted value
    let value =
      value
      ->Js.String2.slice(~from=0, ~to_=-1)
      ->Js.String2.replace(",", ".")
      ->Float.fromString
      ->Option.getWithDefault(0.)

    <Card title={t("Amount of orders received including VAT")} grow=true>
      <ValueIndicator value=#currency(value, currency) />
    </Card>
  }
}

module SupplierOrderTableCard = {
  type row = {
    id: string,
    formattedName: string,
    shopName: string,
    estimatedReceptionDate: Js.Date.t,
    receptionFinishedAt: option<Js.Date.t>,
    totalAmountExcludingTaxes: float,
    totalAmountIncludingTaxes: float,
    totalProductsQuantity: int,
    formattedStatus: array<OrderStatus.t>,
  }

  @react.component
  let make = (~id, ~shopId, ~rows, ~hasNextPage, ~status) => {
    let authScope = Auth.useScope()
    let {pathname} = Navigation.useUrl()
    let createOrderPathname = Order->LegacyRouter.routeToPathname ++ "/create"

    let placeholderEmptyState =
      <Placeholder status=NoDataAvailable customText={t("No order has been recorded yet")} />

    let columns = [
      {
        Table.key: "order-formatted-name",
        name: t("Number"),
        layout: {minWidth: 140.->#px, width: 30.->#pct, margin: #normal},
        render: ({data: {id, formattedName, shopName}}) => {
          let allShopsFiltered = switch authScope {
          | Organisation({activeShop: None}) => true
          | _ => false
          }
          let shopName = allShopsFiltered ? Some(shopName) : None
          <OrderNameTableCell value=formattedName ?shopName id />
        },
      },
      {
        key: "issue-date",
        name: t("Reception date"),
        layout: {minWidth: 110.->#px, width: 1.7->#fr},
        render: ({data: {receptionFinishedAt, estimatedReceptionDate}}) =>
          switch (receptionFinishedAt, estimatedReceptionDate) {
          | (None, estimatedReceptionDate) =>
            <TableDateCell value=Some(estimatedReceptionDate) label={t("estimated")} />
          | (receptionFinishedAt, _) => <TableDateCell value=receptionFinishedAt />
          },
      },
      {
        key: "total-excl-vat",
        name: t("Amount VAT excl."),
        layout: {minWidth: 110.->#px},
        render: ({data: order}) =>
          <AmountTableCell
            value=order.totalAmountExcludingTaxes
            secondaryValue=order.totalAmountIncludingTaxes
            decimalPrecision=3
          />,
      },
      {
        key: "quantity",
        name: t("Quantity"),
        layout: {alignX: #center},
        render: ({data: order}) =>
          <TextStyle> {order.totalProductsQuantity->Int.toString->React.string} </TextStyle>,
      },
      {
        key: "status",
        name: t("Status"),
        layout: {minWidth: 100.->#px, width: 2.->#fr},
        render: ({data: order}) => <OrderStatusBadges statuses=order.formattedStatus />,
      },
    ]

    <Card
      grow=true
      variation=#table
      title={t("Orders history")}
      action=?{switch status {
      | #ARCHIVED => None
      | #UNARCHIVED =>
        Some({
          icon: #plus_light,
          title: t("Create order"),
          handler: OpenLink(
            RouteWithQueryString(
              createOrderPathname,
              OrderUrlQueryString.CreateOrder.encode({supplierId: id, shopId}),
            ),
          ),
        })
      }}>
      <TableView
        columns data=AsyncData.Done(Ok(rows)) keyExtractor={row => row.id} placeholderEmptyState
      />
      {if hasNextPage {
        <ShowAllDataLink
          to=Route(pathname ++ Order->LegacyRouter.routeToPathname) text={t("Show all data")}
        />
      } else {
        React.null
      }}
    </Card>
  }
}

@react.component
let make = (
  ~id,
  ~supplierEditRoute,
  ~supplierEditLocationRoute,
  ~supplierEditContactRoute,
  ~supplierBaseRoute,
) => {
  let scope = Auth.useScope()

  let asyncResultSupplier =
    SupplierQuery.use(SupplierQuery.makeVariables(~id, ~ordersFirst=5, ()))
    ->ApolloHelpers.queryResultToAsyncResult
    ->AsyncResult.mapOk(({supplier}) => supplier)
    ->AsyncResult.mapError(_ => ())

  let formId = switch asyncResultSupplier {
  | Done(Ok(Some(supplier))) | Reloading(Ok(Some(supplier))) => Some(supplier.id)
  | _ => None
  }

  let formInitialValues = switch asyncResultSupplier {
  | Done(Ok(Some(supplier))) | Reloading(Ok(Some(supplier))) =>
    let note = supplier.note->Option.getWithDefault("")
    {SupplierNoteEditFormLenses.note: note}
  | _ => {SupplierNoteEditFormLenses.note: ""}
  }

  let formPropState = SupplierNoteEditForm.useFormPropState({
    id: ?formId,
    schema: [],
    initialValues: formInitialValues,
    resetValuesAfterSubmission: true,
  })

  switch asyncResultSupplier {
  | Done(Ok(Some(supplier))) | Reloading(Ok(Some(supplier))) =>
    let {id, shop: {id: shopId, name: shopName}} = supplier

    let status = switch supplier.archivedAt {
    | Some(_) => #ARCHIVED
    | None => #UNARCHIVED
    }

    let subtitle = switch scope {
    | Organisation(_) => Some(shopName)
    | Single(_) => None
    }

    let actionsBar = <SupplierMenuActions id shopId status supplierBaseRoute />
    let statusBadge = <SupplierStatusBadge status />

    <SupplierNoteEditForm.FormProvider propState=formPropState>
      <ResourceDetailsPage title=supplier.companyName ?subtitle statusBadge actionsBar>
        <Group wrap=false grow=false grid=["30%", "70%"] spaceX=#large>
          <Stack space=#large>
            <SupplierTotalPurchasedCard value=supplier.formattedOrdersTotalAmountIncludingTaxes />
            <SupplierInformationsCard
              id
              supplierEditRoute
              // TODO - Try new record type coercion feature + spread prop record with Rescript v11.
              // One may coerce a mandatory field of an option type to an optional field.
              companyName=supplier.companyName
              email=?supplier.email
              phoneNumber=?supplier.phoneNumber
              mobileNumber=?supplier.mobileNumber
              intraCommunityVat=?supplier.intraCommunityVat
              siretNumber=?supplier.siretNumber
              internalCode=?supplier.internalCode
            />
            <SupplierLocationCard
              id
              supplierEditLocationRoute
              // TODO - Try new record type coercion feature + spread prop record with Rescript v11.
              // One may coerce a mandatory field of an option type to an optional field.
              items={supplier.locations.edges->Array.map(({node: location}) => {
                SupplierLocationCard.label: location.label,
                recipient: ?location.recipient,
                address: ?location.address,
                postalCode: ?location.postalCode,
                country: ?location.country,
                city: ?location.city,
              })}
            />
            <SupplierContactCard
              id
              supplierEditContactRoute
              // TODO - Try new record type coercion feature + spread prop record with Rescript v11.
              // One may coerce a mandatory field of an option type to an optional field.
              items={supplier.contacts.edges->Array.map(({node: contact}) => {
                SupplierContactCard.civility: ?contact.civility,
                firstName: ?contact.firstName,
                lastName: contact.lastName,
                position: ?contact.position,
                email: ?contact.email,
                phoneNumber: ?contact.phoneNumber,
                mobileNumber: ?contact.mobileNumber,
              })}
            />
            <Card title={t("Note")}>
              <SupplierNoteEditForm.InputTextArea field=Note label={t("Note")} />
            </Card>
          </Stack>
          <SupplierOrderTableCard
            id
            shopId
            status
            hasNextPage={supplier.orders.pageInfo.hasNextPage->Option.getWithDefault(false)}
            rows={supplier.orders.edges->Array.map(({node: order}) => {
              SupplierOrderTableCard.id: order.id,
              totalProductsQuantity: order.totalProductsQuantity,
              formattedName: order.formattedName,
              shopName: order.shop.name,
              estimatedReceptionDate: order.estimatedReceptionDate,
              receptionFinishedAt: order.receptionFinishedAt,
              totalAmountExcludingTaxes: order.totalAmountExcludingTaxes,
              totalAmountIncludingTaxes: order.totalAmountIncludingTaxes,
              formattedStatus: order.formattedStatus,
            })}
          />
        </Group>
      </ResourceDetailsPage>
    </SupplierNoteEditForm.FormProvider>
  | Done(Ok(None)) => <Placeholder status=NoDataAvailable />
  | Loading | Reloading(Ok(None) | Error(_)) => <Placeholder status=Loading />
  | NotAsked | Done(Error()) => <Placeholder status=Error />
  }
}
