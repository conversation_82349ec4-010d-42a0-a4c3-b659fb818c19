open Intl

let stripEmptyString = string =>
  switch string {
  | "" | " " => None
  | string => Some(string)
  }

let orEmptyString = value => value->Option.getWithDefault("")

module SupplierFragment = %graphql(`
  fragment SupplierFragment on Supplier {
    id
    updatedAt
    archivedAt
    companyName
    intraCommunityVat
    phoneNumber
    mobileNumber
    siretNumber
    internalCode
    email
    note
  }
`)

module SupplierQuery = %graphql(`
  query SupplierQuery($id: ID!) {
    supplier(id: $id) {
      ...SupplierFragment
    }
  }
`)

module SupplierUpdateMutation = %graphql(`
  mutation updateSupplier($id: ID!, $supplierInput: InputUpdateSupplier!) {
    updateSupplier(id: $id, supplierInput: $supplierInput) {
      ...SupplierFragment
    }
  }
`)

module SupplierEditFormLenses = %lenses(
  type state = {
    companyName: string,
    email: string,
    phoneNumber: string,
    mobileNumber: string,
    intraCommunityVat: string,
    siretNumber: string,
    internalCode: string,
    note: string,
  }
)

module SupplierEditForm = Form.Make(SupplierEditFormLenses)

let supplierEditFormInitialValues = {
  SupplierEditFormLenses.companyName: "",
  email: "",
  phoneNumber: "",
  mobileNumber: "",
  intraCommunityVat: "",
  siretNumber: "",
  internalCode: "",
  note: "",
}

let supplierEditFormSchema = [SupplierEditForm.Schema.StringNotEmpty(CompanyName)]

let supplierEditFormInitialValuesFromQueryResult = queryResult => {
  SupplierEditFormLenses.companyName: queryResult.SupplierFragment.companyName,
  email: queryResult.email->orEmptyString,
  phoneNumber: queryResult.phoneNumber->orEmptyString,
  mobileNumber: queryResult.mobileNumber->orEmptyString,
  intraCommunityVat: queryResult.intraCommunityVat->orEmptyString,
  siretNumber: queryResult.siretNumber->orEmptyString,
  internalCode: queryResult.internalCode->orEmptyString,
  note: queryResult.note->orEmptyString,
}

let mutationVariablesFromIdAndFormValues = (id, formValues) =>
  SupplierUpdateMutation.makeVariables(
    ~id,
    ~supplierInput=SupplierUpdateMutation.makeInputObjectInputUpdateSupplier(
      ~companyName=formValues.SupplierEditFormLenses.companyName,
      ~intraCommunityVat=?formValues.intraCommunityVat->stripEmptyString,
      ~siretNumber=?formValues.siretNumber->stripEmptyString,
      ~phoneNumber=?formValues.phoneNumber->stripEmptyString,
      ~mobileNumber=?formValues.mobileNumber->stripEmptyString,
      ~email=?formValues.email->stripEmptyString,
      ~note=?formValues.note->stripEmptyString,
      ~internalCode=?formValues.internalCode->stripEmptyString,
      (),
    ),
    (),
  )

module SupplierEditFormInformationFieldset = {
  open SupplierEditForm

  @react.component
  let make = () =>
    <FieldsetLayoutPanel
      title={t("General information")}
      description={t("Complete the essential information of your new supplier.")}>
      <InputText field=CompanyName label={t("Company name")} placeholder={t("Company name")} />
      <InputText field=Email label={t("Email")} placeholder={t("<EMAIL>")} />
      <Group>
        <InputPhone field=PhoneNumber label={t("Home phone")} placeholder="02 11 22 33 44" />
        <InputPhone field=MobileNumber label={t("Mobile phone")} placeholder="06 11 22 33 44" />
      </Group>
      <Group>
        <InputText field=IntraCommunityVat label={t("TVA number")} placeholder="FR 40 *********" />
        <InputText field=SiretNumber label={t("Siret")} placeholder="***********" />
      </Group>
      <InputText field=InternalCode label={t("Internal code")} placeholder={t("Internal code")} />
    </FieldsetLayoutPanel>
}

module SuppplierEditFormActionsBar = {
  @react.component
  let make = (~id) => {
    let (mutate, _) = SupplierUpdateMutation.use()
    let formState = SupplierEditForm.useFormState()
    let (canGoBack, onGoBack) = Navigation.useGoBack()

    <ResourceDetailsPage.ActionsBar
      items=[
        if formState.status->Form.Status.isPristine && canGoBack {
          <Button variation=#neutral onPress={_ => onGoBack()}>
            {t("Discard")->React.string}
          </Button>
        } else {
          <SupplierEditForm.CancelButton size=#medium text={t("Discard")} />
        },
        <SupplierEditForm.SubmitButton
          text={t("Save")}
          variation=#success
          size=#medium
          onSubmit={(_, values) =>
            mutate(mutationVariablesFromIdAndFormValues(id, values))
            ->ApolloHelpers.mutationPromiseToFutureResult
            ->Future.mapOk(({SupplierUpdateMutation.updateSupplier: {id}}) => Some(id))}
        />,
      ]
    />
  }
}

@react.component
let make = (~id, ~supplierShowRoute) => {
  let navigate = Navigation.useNavigate()
  let asyncQueryResult =
    SupplierQuery.use({id: id})
    ->ApolloHelpers.queryResultToAsyncResult
    ->AsyncResult.map(result =>
      switch result {
      | Ok({supplier: Some(supplier)}) => Ok(supplier)
      | Ok({supplier: None}) | Error(_) => Error()
      }
    )

  let (notificationError, setNotificationError) = React.useState(() => None)

  let onSubmitFailure = error => setNotificationError(_ => Some(error))
  let onSubmitSuccess = response =>
    switch response {
    | Some(id) => navigate(supplierShowRoute(~id), ~replace=true)
    | None => setNotificationError(_ => Some(Request.serverErrorMessage))
    }

  let formId = switch asyncQueryResult {
  | Done(Ok(queryResult)) | Reloading(Ok(queryResult)) => Some(queryResult.id)
  | _ => None
  }

  let formInitialValues = switch asyncQueryResult {
  | Done(Ok(queryResult)) | Reloading(Ok(queryResult)) =>
    supplierEditFormInitialValuesFromQueryResult(queryResult)
  | _ => supplierEditFormInitialValues
  }

  let formPropState = SupplierEditForm.useFormPropState({
    id: ?formId,
    initialValues: formInitialValues,
    schema: supplierEditFormSchema,
    onSubmitFailure,
    onSubmitSuccess,
  })

  let (formState, _) = formPropState

  let notificationBanner = switch notificationError {
  | Some(error) =>
    let onRequestClose = () => setNotificationError(_ => None)
    <ResourceDetailsPage.NotificationBanner value=Error(error) onRequestClose />
  | None => React.null
  }

  switch asyncQueryResult {
  | AsyncData.NotAsked | Loading | Reloading(_) => <Placeholder status=Loading />
  | Done(Error(_)) => <Placeholder status=Error />
  | Done(Ok(queryResult)) =>
    let actionsBar = <SuppplierEditFormActionsBar id=queryResult.id />
    <SupplierEditForm.FormProvider propState=formPropState>
      <ResourceDetailsPage
        title={t("Edition supplier")}
        subtitle=formState.values.companyName
        actionsBar
        notificationBanner>
        <SupplierEditFormInformationFieldset />
      </ResourceDetailsPage>
    </SupplierEditForm.FormProvider>
  }
}
