open Intl

let stripEmptyString = string =>
  switch string {
  | "" | " " => None
  | string => Some(string)
  }

let flatMapOrEmptyString = (value, map) => value->Option.flatMap(map)->Option.getWithDefault("")

let emptyStringToNull = string => stripEmptyString(string)->Option.getWithDefault(%raw(`null`))

module SupplierFragment = %graphql(`
  fragment SupplierFragment on Supplier {
    id
    updatedAt
    archivedAt
    companyName
    intraCommunityVat
    phoneNumber
    mobileNumber
    siretNumber
    internalCode
    email
    note
    shop { id }
    locations {
      edges {
        node {
          id
          label
          recipient
          address
          postalCode
          city
          country
        }
      }
    }
  }
`)

module SupplierQuery = %graphql(`
  query SupplierQuery($id: ID!) {
    supplier(id: $id) {
      ...SupplierFragment
    }
  }
`)

module SupplierUpdateMutation = %graphql(`
  mutation updateSupplier($id: ID!, $supplierInput: InputUpdateSupplier!, $locationsInput: [InputUpdateOrCreateSupplierLocation!]) {
    updateSupplier(id: $id, supplierInput: $supplierInput, locationsInput: $locationsInput) {
      ...SupplierFragment
    }
  }
`)

module SupplierEditFormLenses = %lenses(
  type state = {
    locationId: string,
    locationLabel: string,
    locationRecipient: string,
    locationAddress: string,
    locationPostalCode: string,
    locationCity: string,
    locationCountry: string,
  }
)

module SupplierEditForm = Form.Make(SupplierEditFormLenses)

let supplierEditFormInitialValues = {
  SupplierEditFormLenses.locationLabel: "",
  locationId: "",
  locationRecipient: "",
  locationAddress: "",
  locationPostalCode: "",
  locationCity: "",
  locationCountry: "",
}

let supplierEditFormSchema = [SupplierEditForm.Schema.StringNotEmpty(LocationLabel)]

let supplierEditFormInitialValuesFromQueryResult = queryResult => {
  let firstLocation = queryResult.SupplierFragment.locations.edges[0]->Option.map(({node}) => node)
  {
    SupplierEditFormLenses.locationId: firstLocation->Option.mapWithDefault("", ({id}) => id),
    locationLabel: firstLocation->Option.mapWithDefault("", ({label}) => label),
    locationRecipient: firstLocation->flatMapOrEmptyString(({recipient}) => recipient),
    locationPostalCode: firstLocation->flatMapOrEmptyString(({postalCode}) => postalCode),
    locationCity: firstLocation->flatMapOrEmptyString(({city}) => city),
    locationCountry: firstLocation->flatMapOrEmptyString(({country}) => country),
    locationAddress: firstLocation->flatMapOrEmptyString(({address}) => address),
  }
}

let supplierUpdateMutationVariablesFromFormValuesAndQueryResult = (formValues, queryResult) =>
  SupplierUpdateMutation.makeVariables(
    ~id=queryResult.SupplierFragment.id,
    // NOTE — in all cases, the backend waits for the supplierInput arg
    // so the frontend has added complexity to deal with it.
    ~supplierInput=SupplierUpdateMutation.makeInputObjectInputUpdateSupplier(
      ~companyName=queryResult.companyName,
      ~intraCommunityVat=?queryResult.intraCommunityVat,
      ~siretNumber=?queryResult.siretNumber,
      ~phoneNumber=?queryResult.phoneNumber,
      ~mobileNumber=?queryResult.mobileNumber,
      ~email=?queryResult.email,
      ~note=?queryResult.note,
      ~internalCode=?queryResult.internalCode,
      (),
    ),
    ~locationsInput=[
      SupplierUpdateMutation.makeInputObjectInputUpdateOrCreateSupplierLocation(
        ~id=?formValues.SupplierEditFormLenses.locationId->stripEmptyString,
        ~label=formValues.SupplierEditFormLenses.locationLabel->emptyStringToNull,
        ~recipient=formValues.locationRecipient->emptyStringToNull,
        ~postalCode=formValues.locationPostalCode->emptyStringToNull,
        ~city=formValues.locationCity->emptyStringToNull,
        ~country=formValues.locationCountry->emptyStringToNull,
        ~address=formValues.locationAddress->emptyStringToNull,
        ~defaults=[#DELIVERY, #BILLING],
        (),
      ),
    ],
    (),
  )

module SupplierEditFormLocationFieldset = {
  open SupplierEditForm

  @react.component
  let make = (~queryResult) => {
    let recipientFieldFilledRef = React.useRef(false)
    let dispatch = useFormDispatch()
    let {values} = useFormState()

    let onChangeLocationAddress = value => dispatch(FieldValueChanged(LocationAddress, _ => value))
    let onRequestLocationAutoComplete = (address: AddressComboBoxField.address) => {
      dispatch(FieldValueChanged(LocationAddress, _ => address.name))
      dispatch(FieldValueChanged(LocationPostalCode, _ => address.postcode))
      dispatch(FieldValueChanged(LocationCity, _ => address.city))
      dispatch(FieldValueChanged(LocationCountry, _ => address.country))
    }

    let defaultlocationRecipientFieldPlaceholder = t("Company name")
    let locationRecipientFieldPlaceholder = if (
      !recipientFieldFilledRef.current && queryResult.SupplierFragment.companyName !== ""
    ) {
      queryResult.companyName
    } else {
      defaultlocationRecipientFieldPlaceholder
    }

    let onFocusLocationRecipientField = () =>
      if (
        !recipientFieldFilledRef.current &&
        locationRecipientFieldPlaceholder !== defaultlocationRecipientFieldPlaceholder
      ) {
        dispatch(FieldValueChanged(LocationRecipient, _ => queryResult.companyName))
        recipientFieldFilledRef.current = true
      }

    <FieldsetLayoutPanel
      title={t("Company address")}
      description={t("Indicate the supplier's address. It will appear on commercial documents.")}>
      <InputText field=LocationLabel label={t("Title")} placeholder={t("Invoicing")} />
      <InputText
        field=LocationRecipient
        label={t("Recipient")}
        placeholder=locationRecipientFieldPlaceholder
        hideRequired=true
        onFocus=onFocusLocationRecipientField
      />
      <AddressComboBoxField
        addressName=values.locationAddress
        onInputChange=onChangeLocationAddress
        onRequestAutoComplete=onRequestLocationAutoComplete
      />
      <InputText field=LocationPostalCode label={t("Postal code")} placeholder={t("Postal code")} />
      <InputText field=LocationCity label={t("City")} placeholder={t("City")} />
      <InputCountrySelect field=LocationCountry />
    </FieldsetLayoutPanel>
  }
}

module SuppplierEditFormActionsBar = {
  @react.component
  let make = (~queryResult) => {
    let (mutate, _) = SupplierUpdateMutation.use()
    let formState = SupplierEditForm.useFormState()
    let (canGoBack, onGoBack) = Navigation.useGoBack()

    <Inline space=#small>
      {if formState.status->Form.Status.isPristine && canGoBack {
        <Button variation=#neutral onPress={_ => onGoBack()}> {t("Discard")->React.string} </Button>
      } else {
        <SupplierEditForm.CancelButton size=#medium text={t("Discard")} />
      }}
      <SupplierEditForm.SubmitButton
        text={t("Save")}
        variation=#success
        size=#medium
        onSubmit={(_, values) =>
          mutate(supplierUpdateMutationVariablesFromFormValuesAndQueryResult(values, queryResult))
          ->ApolloHelpers.mutationPromiseToFutureResult
          ->Future.mapOk(({SupplierUpdateMutation.updateSupplier: {id}}) => Some(id))}
      />
    </Inline>
  }
}

@react.component
let make = (~id, ~supplierShowRoute) => {
  let navigate = Navigation.useNavigate()
  let asyncQueryResult =
    SupplierQuery.use({id: id})
    ->ApolloHelpers.queryResultToAsyncResult
    ->AsyncResult.map(result =>
      switch result {
      | Ok({supplier: Some(supplier)}) => Ok(supplier)
      | Ok({supplier: None}) | Error(_) => Error()
      }
    )

  let (notificationError, setNotificationError) = React.useState(() => None)

  let onSubmitFailure = error => setNotificationError(_ => Some(error))
  let onSubmitSuccess = response =>
    switch response {
    | Some(id) => navigate(supplierShowRoute(~id), ~replace=true)
    | None => setNotificationError(_ => Some(Request.serverErrorMessage))
    }

  let formId = switch asyncQueryResult {
  | Done(Ok(queryResult)) | Reloading(Ok(queryResult)) => Some(queryResult.id)
  | _ => None
  }

  let formInitialValues = switch asyncQueryResult {
  | Done(Ok(queryResult)) | Reloading(Ok(queryResult)) =>
    supplierEditFormInitialValuesFromQueryResult(queryResult)
  | _ => supplierEditFormInitialValues
  }

  let formPropState = SupplierEditForm.useFormPropState({
    SupplierEditForm.Core.id: ?formId,
    initialValues: formInitialValues,
    schema: supplierEditFormSchema,
    onSubmitFailure,
    onSubmitSuccess,
  })

  let notificationBanner = switch notificationError {
  | Some(error) =>
    let onRequestClose = () => setNotificationError(_ => None)
    <ResourceDetailsPage.NotificationBanner value=Error(error) onRequestClose />
  | None => React.null
  }

  switch asyncQueryResult {
  | AsyncData.NotAsked | Loading | Reloading(_) => <Placeholder status=Loading />
  | Done(Error(_)) => <Placeholder status=Error />
  | Done(Ok(queryResult)) =>
    let actionsBar = <SuppplierEditFormActionsBar queryResult />
    <SupplierEditForm.FormProvider propState=formPropState>
      <ResourceDetailsPage
        title={t("Edition supplier location")}
        subtitle=queryResult.companyName
        actionsBar
        notificationBanner>
        <SupplierEditFormLocationFieldset queryResult />
      </ResourceDetailsPage>
    </SupplierEditForm.FormProvider>
  }
}
