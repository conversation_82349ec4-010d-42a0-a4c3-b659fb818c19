open Intl

module CreateSupplierMutation = %graphql(`
  mutation createSupplier($supplierInput: InputCreateSupplier!, $contactsInput: [InputCreateSupplierContact!], $locationsInput: [InputCreateSupplierLocation!]) {
    createSupplier(supplierInput: $supplierInput, contactsInput: $contactsInput, locationsInput: $locationsInput) {
      id
      companyName
      intraCommunityVat
      siretNumber
      phoneNumber
      mobileNumber
      email
      note
      internalCode
      contacts {
        edges {
          node {
            lastName
            firstName
            position
            email
            phoneNumber
            mobileNumber
            isDefault
            civility
          }
        }
      }
      locations {
        edges {
          node {
            label
            postalCode
            city
            country
            address
          }
        }
      }
    }
  }
`)

module SupplierNewFormLenses = %lenses(
  type state = {
    shopId: string, // TODO - should be Auth.ShopID
    companyName: string,
    email: string,
    phoneNumber: string,
    mobileNumber: string,
    intraCommunityVat: string,
    siretNumber: string,
    internalCode: string,
    note: string,
    contactFirstName: string,
    contactLastName: string,
    contactCivility: SupplierCivility.t,
    contactPosition: string,
    contactEmail: string,
    contactPhoneNumber: string,
    contactMobileNumber: string,
    locationLabel: string,
    locationRecipient: string,
    locationAddress: string,
    locationPostalCode: string,
    locationCity: string,
    locationCountry: string,
  }
)

let supplierNewFormInitialValues = (~shopId) => {
  SupplierNewFormLenses.shopId,
  companyName: "",
  email: "",
  phoneNumber: "",
  mobileNumber: "",
  intraCommunityVat: "",
  siretNumber: "",
  internalCode: "",
  note: "",
  contactFirstName: "",
  contactLastName: "",
  contactCivility: #NEUTRAL,
  contactPosition: "",
  contactEmail: "",
  contactPhoneNumber: "",
  contactMobileNumber: "",
  locationLabel: "",
  locationRecipient: "",
  locationAddress: "",
  locationPostalCode: "",
  locationCity: "",
  locationCountry: "France",
}

module SupplierNewForm = Form.Make(SupplierNewFormLenses)

let supplierNewFormSchema = [
  SupplierNewForm.Schema.StringNotEmpty(CompanyName),
  Custom(
    ContactLastName,
    values => {
      let contactFieldsFulfilled =
        values.contactEmail !== "" ||
        values.contactFirstName !== "" ||
        values.contactMobileNumber !== "" ||
        values.contactPhoneNumber !== "" ||
        values.contactPosition !== ""
      if values.contactLastName === "" && contactFieldsFulfilled {
        Error(t("This value must not be empty."))
      } else {
        Ok()
      }
    },
  ),
  SupplierNewForm.Schema.Custom(
    LocationLabel,
    values => {
      let locationFieldsFulfilled =
        values.locationRecipient !== "" ||
        values.locationAddress !== "" ||
        values.locationPostalCode !== "" ||
        values.locationCity !== "" ||
        values.locationCountry !== ""

      if values.locationLabel === "" && locationFieldsFulfilled {
        Error(t("This value must not be empty."))
      } else {
        Ok()
      }
    },
  ),
]

let stripEmptyString = string =>
  switch string {
  | "" | " " => None
  | string => Some(string)
  }

let createSupplierMutationVariablesFromFormValues = values =>
  CreateSupplierMutation.makeVariables(
    ~supplierInput=CreateSupplierMutation.makeInputObjectInputCreateSupplier(
      ~companyName=values.SupplierNewFormLenses.companyName,
      ~shopId=values.shopId,
      ~intraCommunityVat=?values.intraCommunityVat->stripEmptyString,
      ~siretNumber=?values.siretNumber->stripEmptyString,
      ~phoneNumber=?values.phoneNumber->stripEmptyString,
      ~mobileNumber=?values.mobileNumber->stripEmptyString,
      ~email=?values.email->stripEmptyString,
      ~note=?values.note->stripEmptyString,
      ~internalCode=?values.internalCode->stripEmptyString,
      (),
    ),
    ~contactsInput=?values.contactLastName !== ""
      ? Some([
          CreateSupplierMutation.makeInputObjectInputCreateSupplierContact(
            ~lastName=values.contactLastName,
            ~firstName=?values.contactFirstName->stripEmptyString,
            ~position=?values.contactPosition->stripEmptyString,
            ~email=?values.contactEmail->stripEmptyString,
            ~phoneNumber=?values.contactPhoneNumber->stripEmptyString,
            ~mobileNumber=?values.contactMobileNumber->stripEmptyString,
            ~isDefault=true,
            ~civility=values.contactCivility,
            (),
          ),
        ])
      : None,
    ~locationsInput=?values.locationLabel !== ""
      ? Some([
          CreateSupplierMutation.makeInputObjectInputCreateSupplierLocation(
            ~label=values.locationLabel,
            ~recipient=?values.locationRecipient->stripEmptyString,
            ~address=?values.locationAddress->stripEmptyString,
            ~postalCode=?values.locationPostalCode->stripEmptyString,
            ~city=?values.locationCity->stripEmptyString,
            ~country=?values.locationCountry->stripEmptyString,
            ~defaults=[#DELIVERY, #BILLING],
            (),
          ),
        ])
      : None,
    (),
  )

module SupplierNewFormShopFieldset = {
  @react.component
  let make = () => {
    let shops = Auth.useShops()

    let shopsItems = shops->Array.map(shop => {
      Select.key: shop.id,
      label: shop.name,
      value: shop.id,
    })
    let shopsSections = [{Select.items: shopsItems}]

    <FieldsetLayoutPanel
      title={t("Shop")} description={t("Define the shop for which the supplier will be created.")}>
      <SupplierNewForm.InputSelect
        field=ShopId label={t("Shop")} placeholder={t("Select a shop")} sections=shopsSections
      />
    </FieldsetLayoutPanel>
  }
}

module SupplierNewFormInformationFieldset = {
  open SupplierNewForm

  @react.component
  let make = () =>
    <FieldsetLayoutPanel
      title={t("General information")}
      description={t("Complete the essential information of your new supplier.")}>
      <InputText field=CompanyName label={t("Company name")} placeholder={t("Company name")} />
      <InputText field=Email label={t("Email")} placeholder={t("<EMAIL>")} />
      <Group>
        <InputPhone field=PhoneNumber label={t("Home phone")} placeholder="02 11 22 33 44" />
        <InputPhone field=MobileNumber label={t("Mobile phone")} placeholder="06 11 22 33 44" />
      </Group>
      <Group>
        <InputText field=IntraCommunityVat label={t("TVA number")} placeholder="FR 40 *********" />
        <InputText field=SiretNumber label={t("Siret")} placeholder="***********" />
      </Group>
      <InputText field=InternalCode label={t("Internal code")} placeholder={t("Internal code")} />
    </FieldsetLayoutPanel>
}

module SupplierNewFormLocationFieldset = {
  open SupplierNewForm

  @react.component
  let make = () => {
    let recipientFieldFilledRef = React.useRef(false)
    let dispatch = useFormDispatch()
    let {values} = useFormState()

    let onChangeLocationAddress = value => dispatch(FieldValueChanged(LocationAddress, _ => value))
    let onRequestLocationAutoComplete = (address: AddressComboBoxField.address) => {
      dispatch(FieldValueChanged(LocationAddress, _ => address.name))
      dispatch(FieldValueChanged(LocationPostalCode, _ => address.postcode))
      dispatch(FieldValueChanged(LocationCity, _ => address.city))
      dispatch(FieldValueChanged(LocationCountry, _ => address.country))
    }

    let defaultlocationRecipientFieldPlaceholder = t("Company name")
    let locationRecipientFieldPlaceholder = if (
      !recipientFieldFilledRef.current && values.companyName !== ""
    ) {
      values.companyName
    } else {
      defaultlocationRecipientFieldPlaceholder
    }

    let onFocusLocationRecipientField = () =>
      if (
        !recipientFieldFilledRef.current &&
        locationRecipientFieldPlaceholder !== defaultlocationRecipientFieldPlaceholder
      ) {
        dispatch(FieldValueChanged(LocationRecipient, _ => values.companyName))
        recipientFieldFilledRef.current = true
      }

    <FieldsetLayoutPanel
      title={t("Company address")}
      description={t("Indicate the supplier's address. It will appear on commercial documents.")}>
      <InputText
        field=LocationLabel label={t("Title")} placeholder={t("Invoicing")} hideRequired=true
      />
      <InputText
        field=LocationRecipient
        label={t("Recipient")}
        placeholder=locationRecipientFieldPlaceholder
        hideRequired=true
        onFocus=onFocusLocationRecipientField
      />
      <AddressComboBoxField
        addressName=values.locationAddress
        onInputChange=onChangeLocationAddress
        onRequestAutoComplete=onRequestLocationAutoComplete
      />
      <InputText field=LocationPostalCode label={t("Postal code")} placeholder={t("Postal code")} />
      <InputText field=LocationCity label={t("City")} placeholder={t("City")} />
      <InputCountrySelect field=LocationCountry />
    </FieldsetLayoutPanel>
  }
}

module SupplierNewFormContactFieldset = {
  @react.component
  let make = () => {
    let civilityFieldItems = SupplierCivility.options->Array.map(civility => {
      Select.key: civility->SupplierCivility.toLongLabel,
      label: civility->SupplierCivility.toLongLabel,
      value: civility,
    })
    let civilityFieldSections = [{Select.items: civilityFieldItems}]

    <FieldsetLayoutPanel
      title={t("Company contact")}
      description={t(
        "Specify the coordinates of your point of contact within the company to communicate effectively.",
      )}>
      <Group>
        <SupplierNewForm.InputText
          field=ContactLastName label={t("Last name")} placeholder="Dubois" hideRequired=true
        />
        <SupplierNewForm.InputText
          field=ContactFirstName label={t("First name")} placeholder="Jean"
        />
      </Group>
      <Group>
        <SupplierNewForm.InputSelect
          field=ContactCivility label={t("Civility")} sections=civilityFieldSections
        />
        <SupplierNewForm.InputText
          field=ContactPosition label={t("Employment")} placeholder="Commercial"
        />
      </Group>
      <SupplierNewForm.InputText
        field=ContactEmail label={t("Email")} placeholder={t("<EMAIL>")}
      />
      <Group>
        <SupplierNewForm.InputPhone
          field=ContactPhoneNumber label={t("Home phone")} placeholder="02 11 22 33 44"
        />
        <SupplierNewForm.InputPhone
          field=ContactMobileNumber label={t("Mobile phone")} placeholder="06 11 22 33 44"
        />
      </Group>
    </FieldsetLayoutPanel>
  }
}

module SupplierNewFormNoteFieldset = {
  open SupplierNewForm

  @react.component
  let make = () =>
    <FieldsetLayoutPanel title={t("Note")} description={t("Add a note.")}>
      <InputTextArea field=Note label={t("Note")} />
    </FieldsetLayoutPanel>
}

module SupplierNewFormDiscardButton = {
  @react.component
  let make = () => {
    let (canGoBack, onGoBack) = Navigation.useGoBack()

    if canGoBack {
      <Button variation=#neutral onPress={_ => onGoBack()}> {t("Discard")->React.string} </Button>
    } else {
      <SupplierNewForm.CancelButton text={t("Discard")} />
    }
  }
}

@react.component
let make = (~supplierShowRoute, ~shopId=?) => {
  let navigate = Navigation.useNavigate()

  let scope = Auth.useScope()
  let shopId = switch (shopId, scope) {
  | (Some(shopId), _) => shopId
  | (None, Single(shop)) | (None, Organisation({activeShop: Some(shop)})) => shop.id
  | (None, Organisation({shops})) => (shops->Array.getUnsafe(0)).id
  }

  let (notificationError, setNotificationError) = React.useState(() => None)

  let onSubmitFailure = error => setNotificationError(_ => Some(error))
  let onSubmitSuccess = success =>
    switch success {
    | Some(id) => navigate(supplierShowRoute(~id), ~replace=true)
    | _ => setNotificationError(_ => Some(Request.serverErrorMessage))
    }

  let (formState, formDispatch) = SupplierNewForm.useFormPropState({
    initialValues: supplierNewFormInitialValues(~shopId),
    schema: supplierNewFormSchema,
    onSubmitFailure,
    onSubmitSuccess,
  })

  let (createSupplierMutation, _) = CreateSupplierMutation.use()
  let onSubmit = (_, values) =>
    createSupplierMutation(createSupplierMutationVariablesFromFormValues(values))
    ->ApolloHelpers.mutationPromiseToFutureResult
    ->Future.mapOk(({CreateSupplierMutation.createSupplier: {id}}) => Some(id))

  let actionsBar =
    <ResourceDetailsPage.ActionsBar
      items={[
        <SupplierNewFormDiscardButton />,
        <SupplierNewForm.SubmitButton text={t("Save")} variation=#success size=#medium onSubmit />,
      ]}
    />

  let notificationBanner = switch notificationError {
  | Some(error) =>
    let onRequestClose = () => setNotificationError(_ => None)
    <ResourceDetailsPage.NotificationBanner value=Error(error) onRequestClose />
  | None => React.null
  }

  <SupplierNewForm.FormProvider propState=(formState, formDispatch)>
    <ResourceDetailsPage title={t("New supplier")} actionsBar notificationBanner>
      {switch scope {
      | Organisation(_) => <SupplierNewFormShopFieldset />
      | Single(_) => React.null
      }}
      <Stack space=#xlarge>
        <SupplierNewFormInformationFieldset />
        <SupplierNewFormLocationFieldset />
        <SupplierNewFormContactFieldset />
        <SupplierNewFormNoteFieldset />
      </Stack>
    </ResourceDetailsPage>
  </SupplierNewForm.FormProvider>
}
