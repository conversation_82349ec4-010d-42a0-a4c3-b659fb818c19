let baseRoute = "/suppliers"

module CreateSupplierQueryStringCodecs = {
  type t = {shopId: string}

  let encoder = ({shopId}) => shopId
  let decoder = shopId => Ok({shopId: shopId})

  let value = JsonCodec.object1(encoder, decoder, JsonCodec.field("shopId", JsonCodec.string))
}

let encodeCreateSupplierQueryString = state =>
  state->JsonCodec.encodeWith(CreateSupplierQueryStringCodecs.value)->QueryString.stringify

let decodeCreateSupplierQueryString = query =>
  switch query->QueryString.parse->JsonCodec.decodeWith(CreateSupplierQueryStringCodecs.value) {
  | Ok({shopId}) => Some(shopId)
  | Error(_) => None
  }

let showRoute = (~id) => baseRoute ++ "/" ++ id
let newRoute = (~shopId=?, ()) =>
  baseRoute ++
  "/new" ++
  shopId->Option.mapWithDefault("", shopId =>
    "?" ++ encodeCreateSupplierQueryString({shopId: shopId})->QueryString.toString
  )
let editRoute = (~id) => showRoute(~id) ++ "/edit"
let editLocationRoute = (~id) => showRoute(~id) ++ "/edit/location"
let editContactRoute = (~id) => showRoute(~id) ++ "/edit/contact"
