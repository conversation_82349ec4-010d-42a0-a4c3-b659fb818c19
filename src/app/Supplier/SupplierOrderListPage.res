open Intl

module ParentQuery = %graphql(`
  query SupplierOrderListPageParentQuery($id: ID!) {
    supplier(id: $id) {
      id
      companyName
      shop {
        id
      }
    }
  }
`)

module Query = %graphql(`
  query SupplierOrderListPageQuery($supplierId: ID!, $after: String, $before: String, $filterBy: InputSupplierOrdersFilter, $first: Int, $last: Int, $search: String) {
    supplier(id: $supplierId) {
      id
      orders(after: $after, before: $before, filterBy: $filterBy, first: $first, last: $last, search: $search) {
        pageInfo {
          startCursor
          endCursor
        }
        totalCount
        edges {
          node {
            id
            formattedName
            shopName
            supplierCompanyName
            issueDate
            receptionFinishedAt
            formattedStatus @ppxOmitFutureValue
            totalAmountExcludingTaxes
            totalAmountIncludingTaxes
            totalProductsQuantity
            totalProductsExpectedQuantity
          }
        }
      }
    }
  }
`)

module Filters = {
  type t = {
    status: option<OrderStatus.t>,
    issueDateRange: option<(Js.Date.t, Js.Date.t)>,
    receptionDateRange: option<(Js.Date.t, Js.Date.t)>,
  }

  let statuses = [#DRAFT, #FINALIZED, #ACCEPTED, #RECEIVING, #RECEIVED, #TO_PAY, #PAID, #ARCHIVED]

  let encoder = ({status, issueDateRange, receptionDateRange}) => (
    status->Option.map(OrderStatus.toString),
    issueDateRange->Option.map(((start, end)) => [start->Js.Date.valueOf, end->Js.Date.valueOf]),
    receptionDateRange->Option.map(((start, end)) => [
      start->Js.Date.valueOf,
      end->Js.Date.valueOf,
    ]),
  )

  let decoder = ((status, issueDateRange, receptionDateRange)) => Ok({
    status: status->Option.map(OrderStatus.fromStringExn),
    issueDateRange: issueDateRange->Option.flatMap(range =>
      switch range {
      | [start, end] => Some((start->Js.Date.fromFloat, end->Js.Date.fromFloat))
      | _ => None
      }
    ),
    receptionDateRange: receptionDateRange->Option.flatMap(range =>
      switch range {
      | [start, end] => Some((start->Js.Date.fromFloat, end->Js.Date.fromFloat))
      | _ => None
      }
    ),
  })

  let useJsonCodec = () =>
    JsonCodec.object3(
      encoder,
      decoder,
      JsonCodec.field("status", JsonCodec.string)->JsonCodec.optional,
      JsonCodec.field("issueDateRange", JsonCodec.array(JsonCodec.float))->JsonCodec.optional,
      JsonCodec.field("receptionDateRange", JsonCodec.array(JsonCodec.float))->JsonCodec.optional,
    )
}

module Row = {
  type t = {
    id: string,
    supplierId: string,
    formattedName: string,
    shopName: string,
    issueDate: Js.Date.t,
    receptionDate: option<Js.Date.t>,
    statuses: array<OrderStatus.t>,
    totalAmountExcludingTaxes: float,
    totalAmountIncludingTaxes: float,
    totalProductsQuantity: int,
    totalProductsExpectedQuantity: int,
  }
}

module SupplierOrderReceptionQuantityCell = {
  @react.component
  let make = (~statuses, ~totalProductsQuantity, ~totalProductsExpectedQuantity) =>
    <Inline>
      <TextStyle weight=#semibold>
        {if (
          statuses->OrderStatus.has(#RECEIVING) ||
          statuses->OrderStatus.has(#RECEIVED) ||
          statuses->OrderStatus.has(#ARCHIVED)
        ) {
          totalProductsQuantity->Int.toString->React.string
        } else {
          "—"->React.string
        }}
      </TextStyle>
      <TextStyle>
        {(" / " ++ totalProductsExpectedQuantity->Int.toString)->React.string}
      </TextStyle>
    </Inline>
}

module Scaffolded = Scaffold.Make({
  type filters = Filters.t
  let useFiltersJsonCodec = Filters.useJsonCodec

  module QueryInner = Query.Query_inner
  type queryVariableFilterBy = Query.t_variables_InputSupplierOrdersFilter
  let useQuery = Query.use

  let makeQueryVariables = (
    defaultQueryVariables,
    ~connectionArguments,
    ~search=?,
    ~filterBy=?,
    (),
  ) => {
    ...defaultQueryVariables,
    QueryInner.first: connectionArguments.first,
    last: connectionArguments.last,
    before: connectionArguments.before,
    after: connectionArguments.Scaffold.after,
    search,
    filterBy,
  }

  let makeQueryVariablesFilterBy = ({
    Filters.status: status,
    issueDateRange,
    receptionDateRange,
  }) => {
    Query.shopIds: None,
    status: switch status->Option.map(OrderStatus.toString) {
    | Some(status) => Some(Query.makeInputObjectInFilter(~_in=[status], ()))
    | _ => None
    },
    createdAt: None,
    issueDate: switch issueDateRange {
    | Some((start, end)) =>
      Some({
        _after: None,
        _before: None,
        _between: Some([start->Scalar.Datetime.serialize, end->Scalar.Datetime.serialize]),
      })
    | _ => None
    },
    receptionFinishedAt: switch receptionDateRange {
    | Some((start, end)) =>
      Some({
        _after: None,
        _before: None,
        _between: Some([start->Scalar.Datetime.serialize, end->Scalar.Datetime.serialize]),
      })
    | _ => None
    },
  }

  let totalCountFromQueryData = ({Query.supplier: supplier}) =>
    supplier->Option.map(({orders: {totalCount}}) => totalCount)->Option.getWithDefault(0)
  let cursorsFromQueryData = ({Query.supplier: supplier}) => (
    supplier->Option.flatMap(({orders: {pageInfo}}) => pageInfo.startCursor),
    supplier->Option.flatMap(({orders: {pageInfo}}) => pageInfo.endCursor),
  )

  type row = Row.t
  let rowsFromQueryDataAndState = ({Query.supplier: supplier}, _) =>
    switch supplier {
    | Some(supplier) =>
      supplier.orders.edges->Array.map(edge => {
        Row.id: edge.node.id,
        supplierId: supplier.id,
        formattedName: edge.node.formattedName,
        shopName: edge.node.shopName,
        issueDate: edge.node.issueDate,
        receptionDate: edge.node.receptionFinishedAt,
        statuses: edge.node.formattedStatus,
        totalAmountExcludingTaxes: edge.node.totalAmountExcludingTaxes,
        totalAmountIncludingTaxes: edge.node.totalAmountIncludingTaxes,
        totalProductsQuantity: edge.node.totalProductsQuantity,
        totalProductsExpectedQuantity: edge.node.totalProductsExpectedQuantity,
      })
    | _ => []
    }

  let keyExtractor = ({Row.id: id}) => id
})

@react.component
let make = (~id) => {
  let (pageSubtitle, supplierShopId) = switch ParentQuery.use(ParentQuery.makeVariables(~id, ())) {
  | {data: Some({supplier: Some(supplier)})} => (Some(supplier.companyName), Some(supplier.shop.id))
  | {error: Some(_)} => (None, None)
  | _ => (Some(t("Loading") ++ "..."), None)
  }

  let initialState = Scaffolded.makeInitialState(
    ~filters={
      Filters.status: None,
      issueDateRange: None,
      receptionDateRange: None,
    },
  )
  let (state, dispatch) = Scaffolded.use(() => initialState)
  let defaultQueryVariables = Query.makeVariables(~supplierId=id, ())

  let filters =
    <Inline space=#small>
      <Select
        preset=#filter
        label={t("Status")}
        sections={
          let defaultItem = {
            Select.key: "default",
            label: t("Not archived"),
            value: None,
          }
          let items = Filters.statuses->Array.map(status => {
            Select.key: status->OrderStatus.toString,
            label: status->OrderStatus.toLabel,
            value: Some(status),
          })

          [{title: t("Statuses"), items: Array.concat([defaultItem], items)}]
        }
        value=state.filters.status
        onChange={status => FiltersUpdated(prev => {...prev, status})->dispatch}
      />
      <SelectDateRangeFilter
        label={t("Issue date")}
        placeholder={t("Since the beginning")}
        value=?state.filters.issueDateRange
        onChange={issueDateRange => FiltersUpdated(prev => {...prev, issueDateRange})->dispatch}
        triggerLabelDisplay=#showPreset
      />
      <SelectDateRangeFilter
        label={t("Reception date")}
        placeholder={t("Since the beginning")}
        value=?state.filters.receptionDateRange
        onChange={receptionDateRange =>
          FiltersUpdated(prev => {...prev, receptionDateRange})->dispatch}
        triggerLabelDisplay=#showPreset
      />
      {switch state.filters {
      | {status: Some(_)} | {issueDateRange: Some(_)} | {receptionDateRange: Some(_)} =>
        <Scaffold.ResetFiltersButton
          onPress={() => FiltersUpdated(_ => initialState.filters)->dispatch}
        />
      | _ => React.null
      }}
    </Inline>

  let createOrderPathname = Order->LegacyRouter.routeToPathname ++ "/create"
  let createOrderStateJson = switch supplierShopId {
  | Some(supplierShopId) =>
    Some(
      OrderUrlQueryString.CreateOrder.encode({
        supplierId: id,
        shopId: supplierShopId,
      }),
    )
  | _ => None
  }

  let actions = switch createOrderStateJson {
  | Some(createOrderStateJson) =>
    <ButtonLink
      variation=#primary to=RouteWithQueryString(createOrderPathname, createOrderStateJson)>
      {t("Create order")->React.string}
    </ButtonLink>
  | _ => React.null
  }

  let searchBar =
    <SearchBar
      value=?state.searchQuery
      placeholder={t("Search an order")}
      onChange={searchQuery => Searched(searchQuery)->dispatch}
    />

  let columns = [
    {
      Scaffold.name: t("Number"),
      layout: {minWidth: 160.->#px, width: 20.->#pct, margin: #normal, sticky: true},
      render: ({Row.id: id, formattedName, shopName}) =>
        <OrderNameTableCell value=formattedName shopName id />,
    },
    {
      name: t("Amount VAT excl."),
      layout: {minWidth: 110.->#px, sticky: true},
      render: ({totalAmountExcludingTaxes, totalAmountIncludingTaxes}) =>
        <AmountTableCell
          value=totalAmountExcludingTaxes
          secondaryValue=totalAmountIncludingTaxes
          decimalPrecision=3
        />,
    },
    {
      name: t("Issue date"),
      layout: {minWidth: 110.->#px},
      render: ({issueDate}) => <TableDateCell value=Some(issueDate) />,
    },
    {
      name: t("Reception date"),
      layout: {minWidth: 110.->#px},
      render: ({receptionDate}) => <TableDateCell value=receptionDate />,
    },
    {
      name: t("Rec. qt"),
      layout: {minWidth: 100.->#px},
      render: ({statuses, totalProductsQuantity, totalProductsExpectedQuantity}) =>
        <SupplierOrderReceptionQuantityCell
          statuses totalProductsExpectedQuantity totalProductsQuantity
        />,
    },
    {
      name: t("Status"),
      layout: {minWidth: 100.->#px, width: 1.3->#fr},
      render: ({statuses}) => <OrderStatusBadges statuses />,
    },
    {
      layout: {minWidth: 70.->#px, width: 70.->#px},
      render: ({id, statuses}) => <OrderTableActions id statuses />,
    },
  ]

  let emptyState = switch state {
  | {
      currentPage: 1,
      searchQuery: None,
      filters: {status: None, issueDateRange: None, receptionDateRange: None},
    } =>
    <EmptyState
      illustration=Illustration.create title={t("Welcome to the supplier orders space.")}
    />
  | _ =>
    <EmptyState
      illustration=Illustration.notFound
      title={t("No result were found.")}
      text={t("Try again with another keyword/filter or:")}>
      <Button variation=#neutral onPress={_ => Reset(initialState)->dispatch}>
        {t("Clear search query and filters")->React.string}
      </Button>
    </EmptyState>
  }

  switch pageSubtitle {
  | Some(subtitle) =>
    <Scaffolded
      title={t("Supplier orders")}
      subtitle
      defaultQueryVariables
      state
      dispatch
      columns
      filters
      actions
      searchBar
      emptyState
    />
  | _ => <Placeholder status=Error />
  }
}

let make = React.memo(make)
