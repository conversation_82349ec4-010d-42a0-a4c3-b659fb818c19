open Intl

let stripEmptyString = string =>
  switch string {
  | "" | " " => None
  | string => Some(string)
  }

let flatMapWithDefault = (value, default, map) =>
  value->Option.flatMap(map)->Option.getWithDefault(default)

let flatMapOrEmptyString = (value, map) => value->flatMapWithDefault("", map)

module SupplierFragment = %graphql(`
  fragment SupplierFragment on Supplier {
    id
    updatedAt
    archivedAt
    companyName
    intraCommunityVat
    phoneNumber
    mobileNumber
    siretNumber
    internalCode
    email
    note
    shop { id }
    contacts {
      edges {
        node {
          id
          firstName
          lastName
          civility @ppxOmitFutureValue
          position
          email
          phoneNumber
          mobileNumber
        }
      }
    }
  }
`)

module SupplierQuery = %graphql(`
  query SupplierQuery($id: ID!) {
    supplier(id: $id) {
      ...SupplierFragment
    }
  }
`)

module SupplierUpdateMutation = %graphql(`
  mutation updateSupplier($id: ID!, $supplierInput: InputUpdateSupplier!, $contactsInput: [InputUpdateOrCreateSupplierContact!]) {
    updateSupplier(id: $id, supplierInput: $supplierInput, contactsInput: $contactsInput) {
      ...SupplierFragment
    }
  }
`)

module SupplierEditFormLenses = %lenses(
  type state = {
    contactId: string,
    contactFirstName: string,
    contactLastName: string,
    contactCivility: SupplierCivility.t,
    contactPosition: string,
    contactEmail: string,
    contactPhoneNumber: string,
    contactMobileNumber: string,
  }
)

module SupplierEditForm = Form.Make(SupplierEditFormLenses)

let supplierEditFormInitialValues = {
  SupplierEditFormLenses.contactId: "",
  contactFirstName: "",
  contactLastName: "",
  contactCivility: #NEUTRAL,
  contactPosition: "",
  contactEmail: "",
  contactPhoneNumber: "",
  contactMobileNumber: "",
}

let supplierEditFormSchema = [SupplierEditForm.Schema.StringNotEmpty(ContactLastName)]

let supplierEditFormInitialValuesFromQueryResult = queryResult => {
  let firstContact = queryResult.SupplierFragment.contacts.edges[0]->Option.map(({node}) => node)
  {
    SupplierEditFormLenses.contactId: firstContact->Option.mapWithDefault("", ({id}) => id),
    contactLastName: firstContact->Option.mapWithDefault("", ({lastName}) => lastName),
    contactFirstName: firstContact->flatMapOrEmptyString(({firstName}) => firstName),
    contactCivility: firstContact->flatMapWithDefault(#NEUTRAL, ({civility}) => civility),
    contactPosition: firstContact->flatMapOrEmptyString(({position}) => position),
    contactEmail: firstContact->flatMapOrEmptyString(({email}) => email),
    contactPhoneNumber: firstContact->flatMapOrEmptyString(({phoneNumber}) => phoneNumber),
    contactMobileNumber: firstContact->flatMapOrEmptyString(({mobileNumber}) => mobileNumber),
  }
}

let supplierUpdateMutationVariablesFromFormValuesAndQueryResult = (formValues, queryResult) =>
  SupplierUpdateMutation.makeVariables(
    ~id=queryResult.SupplierFragment.id,
    // NOTE — in all cases, the backend waits for the supplierInput arg
    // so the frontend has added complexity to deal with it.
    ~supplierInput=SupplierUpdateMutation.makeInputObjectInputUpdateSupplier(
      ~companyName=queryResult.companyName,
      ~intraCommunityVat=?queryResult.intraCommunityVat,
      ~siretNumber=?queryResult.siretNumber,
      ~phoneNumber=?queryResult.phoneNumber,
      ~mobileNumber=?queryResult.mobileNumber,
      ~email=?queryResult.email,
      ~note=?queryResult.note,
      ~internalCode=?queryResult.internalCode,
      (),
    ),
    ~contactsInput=[
      SupplierUpdateMutation.makeInputObjectInputUpdateOrCreateSupplierContact(
        ~id=?formValues.SupplierEditFormLenses.contactId->stripEmptyString,
        ~lastName=formValues.contactLastName,
        ~firstName=?formValues.contactFirstName->stripEmptyString,
        ~position=?formValues.contactPosition->stripEmptyString,
        ~email=?formValues.contactEmail->stripEmptyString,
        ~phoneNumber=?formValues.contactPhoneNumber->stripEmptyString,
        ~mobileNumber=?formValues.contactMobileNumber->stripEmptyString,
        ~isDefault=true,
        ~civility=formValues.contactCivility,
        (),
      ),
    ],
    (),
  )

module SupplierEditFormContactFieldset = {
  @react.component
  let make = () => {
    let civilityFieldItems = SupplierCivility.options->Array.map(civility => {
      Select.key: civility->SupplierCivility.toLongLabel,
      label: civility->SupplierCivility.toLongLabel,
      value: civility,
    })
    let civilityFieldSections = [{Select.items: civilityFieldItems}]

    <FieldsetLayoutPanel
      title={t("Company contact")}
      description={t(
        "Specify the coordinates of your point of contact within the company to communicate effectively.",
      )}>
      <Group>
        <SupplierEditForm.InputText
          field=ContactLastName label={t("Last name")} placeholder="Dubois" hideRequired=true
        />
        <SupplierEditForm.InputText
          field=ContactFirstName label={t("First name")} placeholder="Jean"
        />
      </Group>
      <Group>
        <SupplierEditForm.InputSelect
          field=ContactCivility label={t("Civility")} sections=civilityFieldSections
        />
        <SupplierEditForm.InputText
          field=ContactPosition label={t("Employment")} placeholder="Commercial"
        />
      </Group>
      <SupplierEditForm.InputText
        field=ContactEmail label={t("Email")} placeholder={t("<EMAIL>")}
      />
      <Group>
        <SupplierEditForm.InputPhone
          field=ContactPhoneNumber label={t("Home phone")} placeholder="02 11 22 33 44"
        />
        <SupplierEditForm.InputPhone
          field=ContactMobileNumber label={t("Mobile phone")} placeholder="06 11 22 33 44"
        />
      </Group>
    </FieldsetLayoutPanel>
  }
}

module SuppplierEditFormActionsBar = {
  @react.component
  let make = (~queryResult) => {
    let (mutate, _) = SupplierUpdateMutation.use()
    let formState = SupplierEditForm.useFormState()
    let (canGoBack, onGoBack) = Navigation.useGoBack()

    <ResourceDetailsPage.ActionsBar
      items=[
        if formState.status->Form.Status.isPristine && canGoBack {
          <Button variation=#neutral onPress={_ => onGoBack()}>
            {t("Discard")->React.string}
          </Button>
        } else {
          <SupplierEditForm.CancelButton size=#medium text={t("Discard")} />
        },
        <SupplierEditForm.SubmitButton
          text={t("Save")}
          variation=#success
          size=#medium
          onSubmit={(_, values) =>
            mutate(supplierUpdateMutationVariablesFromFormValuesAndQueryResult(values, queryResult))
            ->ApolloHelpers.mutationPromiseToFutureResult
            ->Future.mapOk(({SupplierUpdateMutation.updateSupplier: {id}}) => Some(id))}
        />,
      ]
    />
  }
}

@react.component
let make = (~id, ~supplierShowRoute) => {
  let navigate = Navigation.useNavigate()
  let asyncQueryResult =
    SupplierQuery.use({id: id})
    ->ApolloHelpers.queryResultToAsyncResult
    ->AsyncResult.map(result =>
      switch result {
      | Ok({supplier: Some(supplier)}) => Ok(supplier)
      | Ok({supplier: None}) | Error(_) => Error()
      }
    )

  let (notificationError, setNotificationError) = React.useState(() => None)

  let onSubmitFailure = error => setNotificationError(_ => Some(error))
  let onSubmitSuccess = response =>
    switch response {
    | Some(id) => navigate(supplierShowRoute(~id), ~replace=true)
    | None => setNotificationError(_ => Some(Request.serverErrorMessage))
    }

  let formId = switch asyncQueryResult {
  | Done(Ok(queryResult)) | Reloading(Ok(queryResult)) => Some(queryResult.id)
  | _ => None
  }

  let formInitialValues = switch asyncQueryResult {
  | Done(Ok(queryResult)) | Reloading(Ok(queryResult)) =>
    supplierEditFormInitialValuesFromQueryResult(queryResult)
  | _ => supplierEditFormInitialValues
  }

  let formPropState = SupplierEditForm.useFormPropState({
    SupplierEditForm.Core.id: ?formId,
    initialValues: formInitialValues,
    schema: supplierEditFormSchema,
    onSubmitFailure,
    onSubmitSuccess,
  })

  let notificationBanner = switch notificationError {
  | Some(error) =>
    let onRequestClose = () => setNotificationError(_ => None)
    <ResourceDetailsPage.NotificationBanner value={Error(error)} onRequestClose />
  | None => React.null
  }

  switch asyncQueryResult {
  | AsyncData.NotAsked | Loading | Reloading(_) => <Placeholder status=Loading />
  | Done(Error(_)) => <Placeholder status=Error />
  | Done(Ok(queryResult)) =>
    let actionsBar = <SuppplierEditFormActionsBar queryResult />
    <SupplierEditForm.FormProvider propState=formPropState>
      <ResourceDetailsPage
        title={t("Edition supplier contact")}
        subtitle=queryResult.companyName
        actionsBar
        notificationBanner>
        <SupplierEditFormContactFieldset />
      </ResourceDetailsPage>
    </SupplierEditForm.FormProvider>
  }
}
