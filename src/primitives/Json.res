type t = Js.<PERSON>son.t

let {
  decodeObject: decodeDict,
  decodeString,
  decodeArray,
  decodeBoolean,
  decodeNumber,
  decodeNull,
  parseExn,
  stringify,
  stringifyAny,
} = module(<PERSON><PERSON><PERSON><PERSON><PERSON>)

let decodeDictOptionalField = (dict, name, decode) =>
  dict->Js.Dict.get(name)->Option.flatMap(decode)
let decodeDictFieldExn = (dict, name, decode) =>
  decodeDictOptionalField(dict, name, decode)->Option.getExn

let flatDecodeDictField = (dict, name, decode) =>
  dict->Option.flatMap(dict => dict->Js.Dict.get(name))->Option.flatMap(decode)
let flatDecodeDictFieldBoolean = (dict, name) => dict->flatDecodeDictField(name, decodeBoolean)
let flatDecodeDictFieldString = (dict, name) => dict->flatDecodeDictField(name, decodeString)
let flatDecodeDictFieldFloat = (dict, name) => dict->flatDecodeDictField(name, decodeNumber)
let flatDecodeDictFieldDict = (dict, name) => dict->flatDecodeDictField(name, decodeDict)
let flatDecodeDictFieldArray = (dict, name) => dict->flatDecodeDictField(name, decodeArray)

let decodeDictField = (dict, name, decode) => dict->Js.Dict.get(name)->Option.flatMap(decode)
let decodeDictFieldBoolean = (dict, name) => dict->decodeDictField(name, decodeBoolean)
let decodeDictFieldString = (dict, name) => dict->decodeDictField(name, decodeString)
let decodeDictFieldFloat = (dict, name) => dict->decodeDictField(name, decodeNumber)
let decodeDictFieldArray = (dict, name) => dict->decodeDictField(name, decodeArray)

let encodeArray = Js.Json.array
let encodeString = Js.Json.string
let encodeDict = Js.Json.object_
let encodeBoolean = Js.Json.boolean
let encodedNull = Js.Json.null
let encodeNumber = Js.Json.number

let fromObjExn = obj => obj->stringifyAny->Option.getExn->parseExn
