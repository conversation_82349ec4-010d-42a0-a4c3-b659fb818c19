external toReactRef: ReactDOM.domRef => React.ref<Js.Nullable.t<'a>> = "%identity"

let defaultStyle = ReactDOM.Style.make(
  ~border="none",
  ~backgroundColor="inherit",
  ~fontFamily="inherit",
  ~fontSize="inherit",
  ~color="inherit",
  ~cursor="inherit",
  (),
)

@react.component
let make = (
  ~inputRef=?,
  ~ariaProps=?,
  ~placeholder=?,
  ~readOnly=false,
  ~autoTrim=true,
  ~secureTextEntry=false,
  ~autoFocus=false,
  ~style=?,
  ~value=?,
  ~onChange=?,
  ~onFocus=?,
  ~onBlur=?,
) => {
  let (focused, setFocused) = React.useState(() => false)
  let domRef = inputRef->Option.getWithDefault(React.useRef(Js.Nullable.null)->ReactDOM.Ref.domRef)

  // NOTE - autoFocus is delayed to work with animated renders
  React.useEffect1(() => {
    let ref = domRef->toReactRef
    switch ref.current->Js.Nullable.toOption {
    | Some(input) if autoFocus =>
      let timeoutId = Js.Global.setTimeout(() => ReactDomElement.focus(input), 75)
      Some(() => Js.Global.clearTimeout(timeoutId))
    | _ => None
    }
  }, [autoFocus])

  ReactUpdateEffect.use1(() => {
    switch (value, onChange) {
    | (Some(value), Some(onChange)) if !focused && autoTrim =>
      let trimmedValue = Js.String.trim(value)->Js.String2.replaceByRe(%re("/\s+/g"), " ")
      if trimmedValue !== value {
        onChange(trimmedValue)
      }
      None
    | _ => None
    }
  }, [focused])

  let (defaultValue, value) = value->Option.isSome ? (None, value) : (value, None)
  let onFocusChange = React.useCallback0(value => setFocused(_ => value))
  let props = {
    ReactAria.TextField.inputElementType: #input,
    \"type": !secureTextEntry ? #text : #password,
    \"aria-label": "Input",
    ?placeholder,
    readOnly,
    autoFocus,
    ?defaultValue,
    ?value,
    ?onChange,
    ?onFocus,
    ?onBlur,
    onFocusChange,
  }
  let {inputProps} = ReactAria.TextField.use(~props, ~ref=domRef)

  let props = ReactAria.mergeProps2(inputProps, ariaProps)
  let combinedStyle = switch style {
  | Some(style) => ReactDOM.Style.combine(defaultStyle, style)
  | None => defaultStyle
  }

  <ReactAria.Spread props>
    <input style=combinedStyle ref=domRef />
  </ReactAria.Spread>
}

// let make = React.memo(make)
