open Intl

type fileType = [#csv | #excel | #spreadsheetml | #spreadsheetopen]

type error =
  | FileNotFound
  | FileTooHeavy
  | FileBadExtension(array<fileType>)

let extensionFromType = _type =>
  switch _type {
  | #csv => ".csv"
  | #excel => ".xls"
  | #spreadsheetml => ".xlsx"
  | #spreadsheetopen => ".ods"
  }

// NOTE - https://developer.mozilla.org/en/docs/Web/HTTP/Basics_of_HTTP/MIME_types/Common_types
// NOTE - MIME are OS configuration dependent and may not be all defined
// TODO - move to Sheet prim
let mimeFromType = \"type" =>
  switch \"type" {
  | #csv => "text/csv"
  | #excel => "application/vnd.ms-excel"
  | #spreadsheetopen => "application/vnd.oasis.opendocument.spreadsheet"
  | #spreadsheetml => "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
  }

let typeFromMime = mime =>
  switch mime {
  | "text/csv" => Ok(#csv)
  | "application/vnd.ms-excel" => Ok(#excel)
  | "application/vnd.oasis.opendocument.spreadsheet" => Ok(#spreadsheetopen)
  | "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" => Ok(#spreadsheetml)
  | _ => Error()
  }

let messageFromError = \"type" =>
  switch \"type" {
  | FileNotFound => t("Couldn't import file.")
  | FileTooHeavy => t("The imported file is too heavy.")
  | FileBadExtension(fileTypes) =>
    switch fileTypes {
    | types if types->Array.length > 1 =>
      template(
        t("The imported file must be of the following types: {{types}}."),
        ~values={
          "types": fileTypes->Array.reduce("", (acc, fileType) =>
            acc ++ (acc->Js.String2.length > 0 ? ", " : "") ++ fileType->extensionFromType
          ),
        },
        (),
      )
    | _ =>
      template(
        t("The imported file must be of type {{type}}."),
        ~values={"type": fileTypes->Array.getExn(0)->extensionFromType},
        (),
      )
    }
  }

let extensionFromFilename = %raw(`filename => filename.split('.').pop()`)

let isFileBadType = (file, ~types) =>
  types->Array.every(fileType => fileType->mimeFromType !== file->File.type_)

let isFileBadExtension = (file, ~types) =>
  if file->File.type_ === "" {
    let extension = file->File.name->extensionFromFilename
    types->Array.every(fileType => fileType->extensionFromType !== "." ++ extension)
  } else {
    true
  }

@react.component
let make = (~children, ~types=?, ~maxSizeMb=1., ~disabled=false, ~onChange, ~onError) => {
  let ref = React.useRef(Js.Nullable.null)

  let handlePicking = React.useCallback1(event => {
    let files: array<File.t> = (event->ReactEvent.Form.currentTarget)["files"]

    switch (files[0], types) {
    | (Some(file), Some(types))
      if file->isFileBadType(~types) && file->isFileBadExtension(~types) =>
      onError(FileBadExtension(types)->messageFromError)
    | (Some(file), _) if file->File.size > maxSizeMb *. 1048576. =>
      onError(FileTooHeavy->messageFromError)
    | (Some(file), _) => onChange(file)
    | _ => onError(FileNotFound->messageFromError)
    }
  }, [types])

  let onPress = React.useCallback1(
    _ =>
      ref
      ->ReactDomElement.fromRef
      ->Option.flatMap(WebAPI.HtmlElement.ofDomElement)
      ->Option.forEach(WebAPI.HtmlElement.click),
    [ref],
  )

  let accept = React.useMemo1(() =>
    switch types {
    | Some(fileType) =>
      Some(
        fileType->Array.reduce("", (acc, fileType) =>
          acc ++ fileType->mimeFromType ++ ", " ++ fileType->extensionFromType ++ ", "
        ),
      )
    | _ => None
    }
  , [types])

  <>
    <input
      ref={ref->ReactDOM.Ref.domRef}
      style={ReactDOM.Style.make(~display="none", ())}
      type_="file"
      ?accept
      onChange=handlePicking
    />
    <Touchable disabled onPress> children </Touchable>
  </>
}

let make = React.memo(make)
