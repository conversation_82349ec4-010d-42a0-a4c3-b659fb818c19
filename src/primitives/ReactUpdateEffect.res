// NOTE — With next version of Rescript React, only one use
// and not many useN functions
// > See https://github.com/rescript-lang/rescript-react/blob/2f84dbc9fc735de7c3174134b9d72df372108c4b/CHANGELOG.md#boom-breaking-change

let use1 = (effect, deps) => {
  let mounted = React.useRef(false)

  React.useEffect1(() =>
    if mounted.current {
      effect()
    } else {
      mounted.current = true
      None
    }
  , deps)
}

let use2 = (effect, deps) => {
  let mounted = React.useRef(false)

  React.useEffect2(() =>
    if mounted.current {
      effect()
    } else {
      mounted.current = true
      None
    }
  , deps)
}

let use3 = (effect, deps) => {
  let mounted = React.useRef(false)

  React.useEffect3(() =>
    if mounted.current {
      effect()
    } else {
      mounted.current = true
      None
    }
  , deps)
}

let use4 = (effect, deps) => {
  let mounted = React.useRef(false)

  React.useEffect4(() =>
    if mounted.current {
      effect()
    } else {
      mounted.current = true
      None
    }
  , deps)
}

let use5 = (effect, deps) => {
  let mounted = React.useRef(false)

  React.useEffect5(() =>
    if mounted.current {
      effect()
    } else {
      mounted.current = true
      None
    }
  , deps)
}

let use6 = (effect, deps) => {
  let mounted = React.useRef(false)

  React.useEffect6(() =>
    if mounted.current {
      effect()
    } else {
      mounted.current = true
      None
    }
  , deps)
}

let use7 = (effect, deps) => {
  let mounted = React.useRef(false)

  React.useEffect7(() =>
    if mounted.current {
      effect()
    } else {
      mounted.current = true
      None
    }
  , deps)
}

let useLayout1 = (effect, deps) => {
  let mounted = React.useRef(false)

  React.useLayoutEffect1(() =>
    if mounted.current {
      effect()
    } else {
      mounted.current = true
      None
    }
  , deps)
}
