@genType.opaque type t = Js.Json.t

let decodeDict: t => option<Js.Dict.t<t>>
let decodeString: t => option<string>
let decodeArray: t => option<array<t>>
let decodeBoolean: t => option<bool>
let decodeNumber: t => option<float>
let decodeNull: t => option<Js.Null.t<'a>>

let decodeDictOptionalField: (Js.Dict.t<'a>, Js.Dict.key, 'a => option<'b>) => option<'b>
let decodeDictFieldExn: (Js.Dict.t<'a>, Js.Dict.key, 'a => option<'b>) => 'b

let flatDecodeDictField: (option<Js.Dict.t<'a>>, Js.Dict.key, 'a => option<'b>) => option<'b>
let flatDecodeDictFieldBoolean: (option<Js.Dict.t<t>>, Js.Dict.key) => option<bool>
let flatDecodeDictFieldString: (option<Js.Dict.t<t>>, Js.Dict.key) => option<string>
let flatDecodeDictFieldFloat: (option<Js.Dict.t<t>>, Js.Dict.key) => option<float>
let flatDecodeDictFieldDict: (option<Js.Dict.t<t>>, Js.Dict.key) => option<Js.Dict.t<t>>
let flatDecodeDictFieldArray: (option<Js.Dict.t<t>>, Js.Dict.key) => option<array<t>>

let decodeDictField: (Js.Dict.t<'a>, Js.Dict.key, 'a => option<'b>) => option<'b>
let decodeDictFieldBoolean: (Js.Dict.t<t>, Js.Dict.key) => option<bool>
let decodeDictFieldString: (Js.Dict.t<t>, Js.Dict.key) => option<string>
let decodeDictFieldFloat: (Js.Dict.t<t>, Js.Dict.key) => option<float>
let decodeDictFieldArray: (Js.Dict.t<t>, Js.Dict.key) => option<array<t>>

let encodeArray: array<t> => t
let encodeString: string => t
let encodeDict: Js.Dict.t<t> => t
let encodeBoolean: bool => t
let encodedNull: t
let encodeNumber: float => t

let parseExn: string => t
let stringify: t => string
let stringifyAny: 'a => option<string>

let fromObjExn: 'a => Js.Json.t
