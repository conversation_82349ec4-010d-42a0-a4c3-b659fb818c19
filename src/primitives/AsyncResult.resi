/**
 * AsyncResult is a module containing a type t('ok, 'error),
 * which is basically an alias and specialization of AsyncData.t(result('a, 'e)).
 * This variant type can be used to represent the different states in which
 * a data value can exist while being loaded asynchronously, with the possibility
 * of either success ('a) or failure ('e).
 *
 * Inspiration from https://reazen.github.io/relude/api/Relude_AsyncResult/
 */
@genType
type t<'ok, 'error> = AsyncData.t<Result.t<'ok, 'error>>

@genType let notAsked: unit => t<'ok, 'error>
@genType let loading: unit => t<'ok, 'error>
@genType let reloading: Result.t<'ok, 'error> => t<'ok, 'error>
@genType let done: Result.t<'ok, 'error> => t<'ok, 'error>
@genType let doneOk: 'ok => t<'ok, 'error>
@genType let reloadingOk: 'ok => t<'ok, 'error>

let isReloading: t<'ok, 'error> => bool
let isBusy: t<'ok, 'error> => bool
let isIdle: t<'ok, 'error> => bool

let toBusy: t<'ok, 'error> => t<'ok, 'error>

let map: (t<'a, 'b>, Result.t<'a, 'b> => Result.t<'c, 'd>) => t<'c, 'd>
let mapOk: (t<'a, 'b>, 'a => 'c) => t<'c, 'b>
let mapError: (t<'a, 'b>, 'b => 'c) => t<'a, 'c>
