type url = {
  pathname: string,
  path: array<string>,
  query: QueryString.t,
  state: option<Json.t>,
}

let urlToRoute: url => string

let useUrl: unit => url
let useNavigate: (unit, ~replace: bool=?, ~state: Json.t=?, string) => unit
let useGoBack: unit => (bool, unit => unit)

let openRoute: string => unit
let openNewTabRoute: string => unit
let closeBrowserTab: unit => unit

type to =
  | Route(string)
  | RouteWithQueryString(string, QueryString.t)
  | Url(Url.t)

module Link: {
  @genType let toRoute: string => to
  @genType let toUrl: Url.t => to

  @react.component
  let make: (
    ~children: React.element,
    ~to: to,
    ~openNewTab: bool=?,
    ~disabled: bool=?,
    ~className: string=?,
    ~ariaProps: ReactAria.Link.props=?,
    ~onPress: ReactAria.Button.pressEvent => unit=?,
  ) => React.element
}

module Provider: {
  @react.component
  let make: (~children: React.element, ~history: History.t=?) => React.element
}

module Prompt: {
  @react.component
  let make: (~message: string, ~shouldBlockOnRouteChange: string => bool) => React.element
}

module Redirect: {
  @react.component
  let make: (~route: string) => React.element
}
