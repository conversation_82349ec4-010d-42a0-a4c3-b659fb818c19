// TODO - to be removed in favor of native <svg>

module Circle = {
  @react.component
  let make = (~cx=?, ~cy=?, ~r=?, ~fill=?) => <circle ?cx ?cy ?r ?fill />
}

module Path = {
  @react.component
  let make = (
    ~d=?,
    ~fill=?,
    ~transform=?,
    ~fillRule=?,
    ~filter=?,
    ~stroke=?,
    ~strokeWidth=?,
    ~strokeDasharray=?,
  ) => <path ?d ?fill ?transform ?fillRule ?filter ?stroke ?strokeWidth ?strokeDasharray />
}

@react.component
let make = (
  ~children,
  ~role=?,
  ~width=?,
  ~height=?,
  ~viewBox=?,
  ~fill=?,
  ~stroke=?,
  ~style=?,
  ~className=?,
) => <svg ?role ?width ?height ?viewBox ?fill ?stroke ?style ?className> {children} </svg>
