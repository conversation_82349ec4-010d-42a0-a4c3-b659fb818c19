@deprecated("This API will be removed in a future major version of React. See the alternatives.
https://react.dev/reference/react-dom/findDOMNode")
let fromRef: React.ref<Js.Nullable.t<'a>> => option<Dom.element>
let toOptionalDomElement: ReactDOM.domRef => option<Dom.element>

let focus: Dom.element => unit
let blur: Dom.element => unit

let setDomElementStyleProp: (
  ~domElement: WebAPI.DomElement.t,
  ~styleProp: string,
  ~value: string,
) => unit
