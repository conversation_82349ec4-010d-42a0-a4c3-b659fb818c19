open WebAPI

let use = query => {
  let (matched, setMatched) = React.useState(() => {
    let mediaQueryList = window->Window.matchMedia(query)
    mediaQueryList->MediaQueryList.matches
  })

  React.useEffect1(() => {
    let mediaQueryList = window->Window.matchMedia(query)
    setMatched(_ => mediaQueryList->MediaQueryList.matches)

    let handler = () => setMatched(_ => mediaQueryList->MediaQueryList.matches)

    mediaQueryList->MediaQueryList.addListener(handler)
    Some(() => mediaQueryList->MediaQueryList.removeListener(handler))
  }, [query])

  matched
}
