open WebAPI

// NOTE - hovering can be achieved natively with `StyleX.style(~\":hover"=...)`,
// however you might find this primitive to be more comfortable in case of heavy
// conditionning (i.e: combination with states such as focused/active && opened).
// NOTE - this primitive is mainly used for programmatical scenarios.
// TODO - `hovered` should always be returned false on touchscreen devices

let use = (~ref as forwardedRef=?, ()) => {
  let (state, setState) = React.useState(() => false)
  let ref = switch forwardedRef {
  | Some(ref) => ref
  | _ => React.useRef(Js.Nullable.null)
  }

  React.useEffect1(() => {
    let onMouseEnter = _evt => setState(_ => true)
    let onMouseLeave = _evt => setState(_ => false)

    ref
    ->ReactDomElement.fromRef
    ->Option.map(domElement => {
      domElement->DomElement.addEventListener("mouseenter", onMouseEnter)
      domElement->DomElement.addEventListener("mouseleave", onMouseLeave)
    })
    ->ignore

    Some(
      () =>
        ref
        ->ReactDomElement.fromRef
        ->Option.map(domElement => {
          domElement->DomElement.removeEventListener("mouseenter", onMouseEnter)
          domElement->DomElement.removeEventListener("mouseleave", onMouseLeave)
        })
        ->ignore,
    )
  }, [ref.current])

  (ref, state)
}
