// NOTE — Inspired from https://github.com/bloodyowl/rescript-react-update

type dispatch<'action> = 'action => unit

type rec update<'state, 'action> =
  | NoUpdate
  | Update('state)
  | UpdateWithSideEffects('state, self<'state, 'action> => option<unit => unit>)
  | SideEffects(self<'state, 'action> => option<unit => unit>)
and self<'state, 'action> = {dispatch: dispatch<'action>, state: 'state}
and fullState<'state, 'action> = {
  state: 'state,
  sideEffects: ref<array<self<'state, 'action> => option<unit => unit>>>,
}

type reducer<'state, 'action> = ('state, 'action) => update<'state, 'action>

let use = (reducer, initialState) => {
  let cleanupEffects = React.useRef([])

  let ({state, sideEffects}, dispatch) = React.useReducer(
    ({state, sideEffects} as fullState, action) =>
      switch reducer(state, action) {
      | NoUpdate => fullState
      | Update(state) => {...fullState, state}
      | UpdateWithSideEffects(state, sideEffect) => {
          state,
          sideEffects: ref(Array.concat(sideEffects.contents, [sideEffect])),
        }
      | SideEffects(sideEffect) => {
          ...fullState,
          sideEffects: ref(Array.concat(fullState.sideEffects.contents, [sideEffect])),
        }
      },
    {state: initialState, sideEffects: ref([])},
  )

  React.useEffect1(() => {
    if Array.length(sideEffects.contents) > 0 {
      let sideEffectsToRun = Js.Array.sliceFrom(0, sideEffects.contents)
      sideEffects := []
      let cancelFuncs = Array.keepMap(sideEffectsToRun, func => func({state, dispatch}))
      let _ = cleanupEffects.current->Js.Array2.pushMany(cancelFuncs)
    }
    None
  }, [sideEffects])

  React.useEffect0(() => {
    Some(() => cleanupEffects.current->Js.Array2.forEach(cb => cb()))
  })

  (state, dispatch)
}

let useWithMapState = (reducer, getInitialState) => {
  let cleanupEffects = React.useRef([])

  let ({state, sideEffects}, dispatch) = React.useReducerWithMapState(
    ({state, sideEffects} as fullState, action) =>
      switch reducer(state, action) {
      | NoUpdate => fullState
      | Update(state) => {...fullState, state}
      | UpdateWithSideEffects(state, sideEffect) => {
          state,
          sideEffects: ref(Array.concat(sideEffects.contents, [sideEffect])),
        }
      | SideEffects(sideEffect) => {
          ...fullState,
          sideEffects: ref(Array.concat(fullState.sideEffects.contents, [sideEffect])),
        }
      },
    (),
    () => {state: getInitialState(), sideEffects: ref([])},
  )
  React.useEffect1(() => {
    if Array.length(sideEffects.contents) > 0 {
      let sideEffectsToRun = Js.Array.sliceFrom(0, sideEffects.contents)
      sideEffects := []
      let cancelFuncs = Array.keepMap(sideEffectsToRun, func => func({state, dispatch}))
      let _ = cleanupEffects.current->Js.Array2.pushMany(cancelFuncs)
    }
    None
  }, [sideEffects])

  React.useEffect0(() => {
    Some(() => cleanupEffects.current->Js.Array2.forEach(cb => cb()))
  })

  (state, dispatch)
}
