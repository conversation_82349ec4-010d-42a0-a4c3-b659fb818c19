type t<'ok, 'error> = AsyncData.t<Result.t<'ok, 'error>>

let {notAsked, loading, reloading, done} = module(AsyncData)
let {isReloading, isBusy, isIdle} = module(AsyncData)
let {toBusy} = module(AsyncData)

let doneOk = ok => AsyncData.Done(Ok(ok))
let reloadingOk = ok => AsyncData.Reloading(Ok(ok))

let map = (asyncResult, transform) => asyncResult->AsyncData.map(transform)

let mapOk = (asyncResult, transform) =>
  asyncResult->AsyncData.map(result =>
    switch result {
    | Ok(ok) => Ok(transform(ok))
    | Error(_) as error => error
    }
  )

let mapError = (asyncResult, transform) =>
  asyncResult->AsyncData.map(result =>
    switch result {
    | Ok(_) as ok => ok
    | Error(error) => Error(transform(error))
    }
  )
