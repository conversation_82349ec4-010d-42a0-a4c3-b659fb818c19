let style =
  ReactDOM.Style.make(
    ~fontSize=`${FontSizes.xsmall->Float.toString}px`,
    ~fontFamily=#LibreFranklin->FontFaces.fontFamilyFromFontName,
    ~fontWeight="500",
    ~marginBottom="7px",
    ~cursor="inherit",
    (),
  )->ReactDOM.Style.unsafeAddStyle({"textWrap": "nowrap"})

@react.component
let make = (~text="", ~color=Colors.neutralColor50, ~ariaProps=?) => {
  let colorStyle = ReactDOM.Style.make(~color, ())
  let style = ReactDOM.Style.combine(style, colorStyle)

  <ReactAria.Spread props=ariaProps>
    <label style> {text->React.string} </label>
  </ReactAria.Spread>
}

let make = React.memo(make)
