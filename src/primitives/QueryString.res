type t = Qs.t

external fromString: string => t = "%identity"
external toString: t => string = "%identity"

// NOTE - String values quoting layer before encoding
let stringifyEncoder = primitive =>
  Json.decodeString(primitive)
  ->Option.map(string => `"` ++ string->Js.String2.replaceByRe(%re(`/"/g`), `\\"`) ++ `"`)
  ->Option.orElse(Json.decodeBoolean(primitive)->Option.map(string_of_bool))
  ->Option.orElse(Json.decodeNumber(primitive)->Option.map(Float.toString))
  ->Option.orElse(Json.decodeNull(primitive)->Option.map(_ => "null"))
  ->Option.getWithDefault("")
  ->Js.Global.encodeURIComponent

let stringify = json =>
  json->Qs.stringifyWithOptions(
    ~options={
      encodeValuesOnly: true,
      encoder: stringifyEncoder,
    },
  )

// NOTE - By default primitives values are not converted back, see:
// https://github.com/ljharb/qs#parsing-primitivescalar-values-numbers-booleans-null-etc
let parseDecoder = (string, _, _, ~type_) =>
  switch type_ {
  | #value =>
    switch string->Js.Global.decodeURIComponent {
    | "undefined" | "" => Obj.magic(None)
    | "null" => Json.encodedNull
    | "true" => true->Json.encodeBoolean
    | "false" => false->Json.encodeBoolean
    | decodedString =>
      if decodedString->Js.String2.startsWith(`"`) && decodedString->Js.String2.endsWith(`"`) {
        decodedString
        ->Js.String2.slice(~from=1, ~to_=-1)
        ->Js.String2.replaceByRe(%re(`/\\"/g`), `"`)
        ->Json.encodeString
      } else if Js.Re.test_(%re(`/^-?\d+(.\d+)?$/`), string) {
        string->Js.Float.fromString->Json.encodeNumber
      } else {
        string->Json.encodeString
      }
    }
  | #key => string->Json.encodeString
  }

let parse = query =>
  query->Qs.parseWithOptions(
    ~options={
      ignoreQueryPrefix: true,
      decoder: parseDecoder,
    },
  )
