// ISO 3166-2 country codes
type t =
  | FR
  | FR_20R
  | FR_974
  | BE
  | LU

let values = [FR, FR_20R, FR_974, BE, LU]

let toIso2String = value =>
  switch value {
  | FR => "FR"
  | FR_20R => "FR-20R"
  | FR_974 => "FR-974"
  | BE => "BE"
  | LU => "LU"
  }

let toIsoString = value =>
  switch value {
  | FR => "FR"
  | FR_20R => "FR"
  | FR_974 => "FR"
  | BE => "BE"
  | LU => "LU"
  }

let toLongCountryString = value =>
  switch value {
  | FR => "France - Mainland"
  | FR_20R => "France - Corsica"
  | FR_974 => "France - Reunion Island"
  | BE => "Belgium"
  | LU => "Luxembourg"
  }

let toMediumCountryString = value =>
  switch value {
  | FR => "France"
  | FR_20R => "France"
  | FR_974 => "France"
  | BE => "Belgium"
  | LU => "Luxembourg"
  }

let fromString = value =>
  switch value {
  | "FR" => Ok(FR)
  | "FR_20R" => Ok(FR_20R)
  | "FR_974" => Ok(FR_974)
  | "BE" => Ok(BE)
  | "LU" => Ok(LU)
  | _ => Error("Unknown country code")
  }
