// ReactUpdateEffect.use is React effect hook that ignores the first
// invocation (e.g. on mount). The signature is exactly the same
// as the `React.useEffect` hook.
// While they may seem convenient, their behavior can be misleading
// or can encourage bad practices (component lifecycle mindset).
// Instead, opt for React.useEffect for synchronization, as React 18 emphasizes
// its importance by rendering components twice in strict mode
// to highlight improper usage. Prefer using React's API directly.
// > https://react.dev/blog/2022/03/29/react-v18#new-strict-mode-behaviors

let use1: (unit => option<unit => unit>, array<'a>) => unit
let use2: (unit => option<unit => unit>, ('a, 'b)) => unit
let use3: (unit => option<unit => unit>, ('a, 'b, 'c)) => unit
let use4: (unit => option<unit => unit>, ('a, 'b, 'c, 'd)) => unit
let use5: (unit => option<unit => unit>, ('a, 'b, 'c, 'd, 'e)) => unit
let use6: (unit => option<unit => unit>, ('a, 'b, 'c, 'd, 'e, 'f)) => unit
let use7: (unit => option<unit => unit>, ('a, 'b, 'c, 'd, 'e, 'f, 'g)) => unit

let useLayout1: (unit => option<unit => unit>, array<'a>) => unit
