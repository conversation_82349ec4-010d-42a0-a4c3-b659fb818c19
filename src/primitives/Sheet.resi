type csvFile = File.t
let csvFileExtension: string
let makeCsvPlainText: (~delimiter: string=?, array<array<string>>) => Result.t<string, unit>
let makeCsvBlobFromPlainText: string => Blob.t

let excelFileExtension: string
let makeExcelBlob: (array<array<string>>, ~worksheetName: string) => Future.t<result<Blob.t, unit>>

let parseExceljsXlsxFile: (
  ~headerIndexOffset: int=?,
  File.t,
) => Future.t<result<array<Json.t>, unit>>
let parseCsvFile: File.t => Future.t<result<array<Json.t>, unit>>

@deprecated("Use parseCsvFile")
module CsvParserAndDecoder: (
  Config: {
    type cell
    type row
    let cellFromEntryIndex: (int, string) => cell
    let validateCellFromLabel: cell => Result.t<unit, string>
    let rowFromEntry: array<string> => row
  },
) =>
{
  type parsingError = InvalidCell({entryIndex: int, rowIndex: int, message: string, value: string})
  let make: csvFile => Future.t<Result.t<(array<Config.row>, array<parsingError>), unit>>
}
