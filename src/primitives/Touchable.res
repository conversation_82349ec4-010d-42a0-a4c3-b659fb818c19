// TODO - To rename <Pressable />
open StyleX

let styles = StyleX.create({
  "root": style(~display=#block, ~position=#relative, ~userSelect=#text, ~cursor=#default, ()),
  "inline": style(~display=#"inline-flex", ()),
  "grow": style(~flex="1", ()),
  "cursorPointer": style(~cursor=#pointer, ()),
})

let styleProps = (~wrap, ~grow, ~disabled, ~additionalStyle) =>
  StyleX.props([
    styles["root"],
    !wrap ? styles["inline"] : style(),
    grow ? styles["grow"] : style(),
    !disabled ? styles["cursorPointer"] : style(),
    additionalStyle,
  ])

@react.component
let make = React.forwardRef((
  ~children,
  ~ariaProps=?,
  ~disabled=?,
  ~excludeFromTabOrder=?,
  ~wrap=true,
  ~grow=false,
  ~style=StyleX.style(),
  ~onPress=?,
  ref,
) => {
  let ref = ref->Js.Nullable.toOption->Option.map(ReactDOM.Ref.domRef)

  let disabled =
    disabled
    ->Option.orElse(ariaProps->Option.flatMap(props => props.ReactAria.Button.disabled))
    ->Option.getWithDefault(false)

  let defaultProps = {
    ReactAria.Button.elementType: #div,
    disabled,
    ?excludeFromTabOrder,
    ?onPress,
  }
  let props =
    ariaProps
    ->Option.map(props => ReactAria.mergeProps2(props, defaultProps))
    ->Option.getWithDefault(defaultProps)

  let {buttonProps} = ReactAria.Button.use(~props, ())

  let {?style, ?className} = styleProps(~wrap, ~grow, ~disabled, ~additionalStyle=style)

  <ReactAria.Spread props=buttonProps>
    <div ?style ?className ?ref> children </div>
  </ReactAria.Spread>
})

let make = React.memo(make)

React.setDisplayName(make, "Touchable")
