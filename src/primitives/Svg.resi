module Circle: {
  @react.component
  let make: (~cx: string=?, ~cy: string=?, ~r: string=?, ~fill: string=?) => React.element
}

module Path: {
  @react.component
  let make: (
    ~d: string=?,
    ~fill: string=?,
    ~transform: string=?,
    ~fillRule: string=?,
    ~filter: string=?,
    ~stroke: string=?,
    ~strokeWidth: string=?,
    ~strokeDasharray: string=?,
  ) => React.element
}

@react.component
let make: (
  ~children: React.element,
  ~role: string=?,
  ~width: string=?,
  ~height: string=?,
  ~viewBox: string=?,
  ~fill: string=?,
  ~stroke: string=?,
  ~style: ReactDOM.style=?,
  ~className: string=?,
) => React.element
