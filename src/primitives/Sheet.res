type csvFile = File.t

let csvFileExtension = "csv"
let excelFileExtension = "xlsx"

let makeCsvPlainText = (~delimiter=";", rows) =>
  switch PapaParse.unparse(rows, ~config={PapaParse.newline: "\n", delimiter}) {
  | rows =>
    let lines = rows->Js.String2.split("\n")
    let maxCols = lines->Array.reduce(0, (acc, line) =>
      switch line->Js.String2.split(delimiter)->Array.length {
      | count if count > acc => count
      | _ => acc
      }
    )
    let entries =
      lines
      ->Array.map(line => {
        let count = line->Js.String2.split(delimiter)->Array.length
        line ++ (maxCols - count)->String.make(delimiter->String.get(0))
      })
      ->Array.reduce("", (acc, entry) => acc ++ (entry->Js.String2.trim ++ "\n"))

    Ok(entries)
  | exception _ => Error()
  }

let makeCsvBlobFromPlainText = plainText => {
  Blob.makeWithOptions(
    [
      Blob.uint8ArrayToBlobPart(Js.TypedArray2.Uint8Array.make([0xEF, 0xBB, 0xBF])),
      Blob.stringToBlobPart(plainText),
    ],
    Blob.makeBlobPropertyBag(~_type="text/csv;charset=utf-8", ()),
  )
}

let makeExcelBlob = (rows, ~worksheetName) =>
  switch {
    let workbook = Exceljs.Workbook.make()
    let worksheet = workbook->Exceljs.Workbook.addWorksheet(worksheetName)

    rows->Array.forEach(row => worksheet->Exceljs.Worksheet.addRow(row))

    workbook
    ->Exceljs.Xlsx.fromWorkbook
    ->Exceljs.Xlsx.writeBuffer
    ->Promise.then(data => {
      Promise.resolve(
        Blob.makeWithOptions(
          [Blob.arrayBufferToBlobPart(data)],
          Blob.makeBlobPropertyBag(
            ~_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            (),
          ),
        ),
      )
    })
    ->FuturePromise.fromPromise
    ->Future.mapError(_ => ())
  } {
  | futureResultExcelBlob => futureResultExcelBlob
  | exception _ => Future.value(Error())
  }

let parseExceljsXlsxFile = (~headerIndexOffset=1, file) => {
  let workbook = Exceljs.Workbook.make()
  let xlsx = Exceljs.Xlsx.fromWorkbook(workbook)

  let rec findWorksheet = (~index=1, workbook) =>
    if index > 10 {
      exception NotFoundWorksheet
      Promise.reject(NotFoundWorksheet)
    } else {
      switch Exceljs.Workbook.getWorksheet(workbook, index) {
      | Some(worksheet) => Promise.resolve(worksheet)
      | None => findWorksheet(workbook, ~index=index + 1)
      }
    }

  let processWorkbook = async data => {
    let workbook = await xlsx->Exceljs.Xlsx.load(data)
    let worksheet = await findWorksheet(workbook)

    let rows = ref([])
    let workingHeaders =
      (worksheet->Exceljs.Worksheet.getRow(headerIndexOffset)).values->Array.sliceToEnd(1)

    worksheet->Exceljs.Worksheet.eachRow((row, rowNumber) =>
      if rowNumber > headerIndexOffset {
        let dict = Js.Dict.empty()
        let values = row.values->Array.sliceToEnd(1)

        workingHeaders->Array.forEachWithIndex((index, header) => {
          let header = header->Option.flatMap(Js.Json.decodeString)
          let cell = switch values[index] {
          | Some(Some(json)) =>
            let computedResultJson = switch Json.decodeDict(json)->Option.flatMap(
              dict => dict->Js.Dict.get("result"),
            ) {
            | Some(resultFromFormulaJson) => resultFromFormulaJson
            | None => json
            }

            computedResultJson
          | _ => Json.encodedNull
          }

          switch header {
          | Some("") | None => ()
          | Some(header) => dict->Js.Dict.set(header, cell)
          }
        })
        rows.contents->Array.push(dict)
      }
    )
    rows.contents->Array.map(Js.Json.object_)
  }

  file
  ->File.toBlob
  ->Blob.arrayBuffer
  ->Promise.then(processWorkbook)
  ->FuturePromise.fromPromise
  ->Future.mapError(_ => ())
}

let parseCsvFile = file =>
  Future.make(resolve => {
    let data = []

    let step = ({PapaParse.data: item}) => data->Array.push(item)
    let complete = _ => resolve(Ok(data))
    let error = _ => resolve(Error())

    try {
      let config = {
        PapaParse.step,
        complete,
        error,
        skipEmptyLines: Json.encodeString("greedy"),
        delimiter: ";",
        worker: true,
      }
      PapaParse.parse(file, config)
    } catch {
    | _ => resolve(Error())
    }

    None
  })

module CsvParserAndDecoder = (
  Config: {
    type cell
    type row
    let cellFromEntryIndex: (int, string) => cell
    let validateCellFromLabel: cell => Result.t<unit, string>
    let rowFromEntry: array<string> => row
  },
) => {
  type parsingError = InvalidCell({entryIndex: int, rowIndex: int, message: string, value: string})

  let make = file =>
    Future.make(resolve => {
      let data: Pervasives.ref<array<Config.row>> = ref([])
      let errors = ref([])
      let index = ref(0)
      let step = ({PapaParse.data: entry}) => {
        let entryIndex = ref(0)

        let valid = entry->Array.every(cellValue => {
          let cell = entryIndex.contents->Config.cellFromEntryIndex(cellValue)
          let error = cell->Config.validateCellFromLabel

          entryIndex := entryIndex.contents + 1
          switch error {
          | Error(message) =>
            errors :=
              errors.contents->Array.concat([
                InvalidCell({
                  entryIndex: entryIndex.contents,
                  rowIndex: index.contents + 1,
                  message,
                  value: cellValue,
                }),
              ])
            false
          | _ => true
          }
        })

        if valid {
          data := data.contents->Array.concat([entry->Config.rowFromEntry])
        }
        index := index.contents + 1
      }
      let complete = _ => resolve(Ok((data.contents, errors.contents)))
      let error = _ => resolve(Error())
      let config = {
        PapaParse.step,
        complete,
        error,
        skipEmptyLines: Json.encodeBoolean(true),
        delimiter: ";",
        worker: true,
      }

      try {
        PapaParse.unsafeParse(file, config)
      } catch {
      | _ => resolve(Error())
      }

      None
    })
}
