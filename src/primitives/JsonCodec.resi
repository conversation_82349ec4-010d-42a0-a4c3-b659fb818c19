// NOTE - This module is a minimalist version of https://github.com/nkrkv/jzon
// with slight modifications or improvements.

type t<'value>

type field<'value>

module DecodingError: {
  type locationComponent = Field(string) | Index(int)

  type location = array<locationComponent>

  type t = [
    | #SyntaxError(string)
    | #MissingField(location, string)
    | #UnexpectedJsonType(location, string, Json.t)
    | #UnexpectedJsonValue(location, string)
  ]
}

let encodeWith: ('value, t<'value>) => Json.t
let decodeWith: (Json.t, t<'value>) => result<'value, DecodingError.t>

let string: t<string>
let float: t<float>
let int: t<int>
let bool: t<bool>

let array: t<'value> => t<array<'value>>

let field: (string, t<'value>) => field<'value>
let optional: field<'value> => field<option<'value>>

let object1: ('r => 'f1, 'f1 => result<'r, DecodingError.t>, field<'f1>) => t<'r>

let object2: (
  'r => ('f1, 'f2),
  (('f1, 'f2)) => result<'r, DecodingError.t>,
  field<'f1>,
  field<'f2>,
) => t<'r>

let object3: (
  'r => ('f1, 'f2, 'f3),
  (('f1, 'f2, 'f3)) => result<'r, DecodingError.t>,
  field<'f1>,
  field<'f2>,
  field<'f3>,
) => t<'r>

let object4: (
  'r => ('f1, 'f2, 'f3, 'f4),
  (('f1, 'f2, 'f3, 'f4)) => result<'r, DecodingError.t>,
  field<'f1>,
  field<'f2>,
  field<'f3>,
  field<'f4>,
) => t<'r>

let object5: (
  'r => ('f1, 'f2, 'f3, 'f4, 'f5),
  (('f1, 'f2, 'f3, 'f4, 'f5)) => result<'r, DecodingError.t>,
  field<'f1>,
  field<'f2>,
  field<'f3>,
  field<'f4>,
  field<'f5>,
) => t<'r>

let object6: (
  'r => ('f1, 'f2, 'f3, 'f4, 'f5, 'f6),
  (('f1, 'f2, 'f3, 'f4, 'f5, 'f6)) => result<'r, DecodingError.t>,
  field<'f1>,
  field<'f2>,
  field<'f3>,
  field<'f4>,
  field<'f5>,
  field<'f6>,
) => t<'r>

let object7: (
  'r => ('f1, 'f2, 'f3, 'f4, 'f5, 'f6, 'f7),
  (('f1, 'f2, 'f3, 'f4, 'f5, 'f6, 'f7)) => result<'r, DecodingError.t>,
  field<'f1>,
  field<'f2>,
  field<'f3>,
  field<'f4>,
  field<'f5>,
  field<'f6>,
  field<'f7>,
) => t<'r>

let object8: (
  'r => ('f1, 'f2, 'f3, 'f4, 'f5, 'f6, 'f7, 'f8),
  (('f1, 'f2, 'f3, 'f4, 'f5, 'f6, 'f7, 'f8)) => result<'r, DecodingError.t>,
  field<'f1>,
  field<'f2>,
  field<'f3>,
  field<'f4>,
  field<'f5>,
  field<'f6>,
  field<'f7>,
  field<'f8>,
) => t<'r>

let object9: (
  'r => ('f1, 'f2, 'f3, 'f4, 'f5, 'f6, 'f7, 'f8, 'f9),
  (('f1, 'f2, 'f3, 'f4, 'f5, 'f6, 'f7, 'f8, 'f9)) => result<'r, DecodingError.t>,
  field<'f1>,
  field<'f2>,
  field<'f3>,
  field<'f4>,
  field<'f5>,
  field<'f6>,
  field<'f7>,
  field<'f8>,
  field<'f9>,
) => t<'r>

let object10: (
  'r => ('f1, 'f2, 'f3, 'f4, 'f5, 'f6, 'f7, 'f8, 'f9, 'f10),
  (('f1, 'f2, 'f3, 'f4, 'f5, 'f6, 'f7, 'f8, 'f9, 'f10)) => result<'r, DecodingError.t>,
  field<'f1>,
  field<'f2>,
  field<'f3>,
  field<'f4>,
  field<'f5>,
  field<'f6>,
  field<'f7>,
  field<'f8>,
  field<'f9>,
  field<'f10>,
) => t<'r>

let object11: (
  'r => ('f1, 'f2, 'f3, 'f4, 'f5, 'f6, 'f7, 'f8, 'f9, 'f10, 'f11),
  (('f1, 'f2, 'f3, 'f4, 'f5, 'f6, 'f7, 'f8, 'f9, 'f10, 'f11)) => result<'r, DecodingError.t>,
  field<'f1>,
  field<'f2>,
  field<'f3>,
  field<'f4>,
  field<'f5>,
  field<'f6>,
  field<'f7>,
  field<'f8>,
  field<'f9>,
  field<'f10>,
  field<'f11>,
) => t<'r>

let object12: (
  'r => ('f1, 'f2, 'f3, 'f4, 'f5, 'f6, 'f7, 'f8, 'f9, 'f10, 'f11, 'f12),
  (('f1, 'f2, 'f3, 'f4, 'f5, 'f6, 'f7, 'f8, 'f9, 'f10, 'f11, 'f12)) => result<'r, DecodingError.t>,
  field<'f1>,
  field<'f2>,
  field<'f3>,
  field<'f4>,
  field<'f5>,
  field<'f6>,
  field<'f7>,
  field<'f8>,
  field<'f9>,
  field<'f10>,
  field<'f11>,
  field<'f12>,
) => t<'r>

let object13: (
  'r => ('f1, 'f2, 'f3, 'f4, 'f5, 'f6, 'f7, 'f8, 'f9, 'f10, 'f11, 'f12, 'f13),
  (('f1, 'f2, 'f3, 'f4, 'f5, 'f6, 'f7, 'f8, 'f9, 'f10, 'f11, 'f12, 'f13)) => result<
    'r,
    DecodingError.t,
  >,
  field<'f1>,
  field<'f2>,
  field<'f3>,
  field<'f4>,
  field<'f5>,
  field<'f6>,
  field<'f7>,
  field<'f8>,
  field<'f9>,
  field<'f10>,
  field<'f11>,
  field<'f12>,
  field<'f13>,
) => t<'r>

let object14: (
  'r => ('f1, 'f2, 'f3, 'f4, 'f5, 'f6, 'f7, 'f8, 'f9, 'f10, 'f11, 'f12, 'f13, 'f14),
  (('f1, 'f2, 'f3, 'f4, 'f5, 'f6, 'f7, 'f8, 'f9, 'f10, 'f11, 'f12, 'f13, 'f14)) => result<
    'r,
    DecodingError.t,
  >,
  field<'f1>,
  field<'f2>,
  field<'f3>,
  field<'f4>,
  field<'f5>,
  field<'f6>,
  field<'f7>,
  field<'f8>,
  field<'f9>,
  field<'f10>,
  field<'f11>,
  field<'f12>,
  field<'f13>,
  field<'f14>,
) => t<'r>

let object15: (
  'r => ('f1, 'f2, 'f3, 'f4, 'f5, 'f6, 'f7, 'f8, 'f9, 'f10, 'f11, 'f12, 'f13, 'f14, 'f15),
  (('f1, 'f2, 'f3, 'f4, 'f5, 'f6, 'f7, 'f8, 'f9, 'f10, 'f11, 'f12, 'f13, 'f14, 'f15)) => result<
    'r,
    DecodingError.t,
  >,
  field<'f1>,
  field<'f2>,
  field<'f3>,
  field<'f4>,
  field<'f5>,
  field<'f6>,
  field<'f7>,
  field<'f8>,
  field<'f9>,
  field<'f10>,
  field<'f11>,
  field<'f12>,
  field<'f13>,
  field<'f14>,
  field<'f15>,
) => t<'r>

let object16: (
  'r => ('f1, 'f2, 'f3, 'f4, 'f5, 'f6, 'f7, 'f8, 'f9, 'f10, 'f11, 'f12, 'f13, 'f14, 'f15, 'f16),
  (
    ('f1, 'f2, 'f3, 'f4, 'f5, 'f6, 'f7, 'f8, 'f9, 'f10, 'f11, 'f12, 'f13, 'f14, 'f15, 'f16)
  ) => result<'r, DecodingError.t>,
  field<'f1>,
  field<'f2>,
  field<'f3>,
  field<'f4>,
  field<'f5>,
  field<'f6>,
  field<'f7>,
  field<'f8>,
  field<'f9>,
  field<'f10>,
  field<'f11>,
  field<'f12>,
  field<'f13>,
  field<'f14>,
  field<'f15>,
  field<'f16>,
) => t<'r>

let object17: (
  'r => (
    'f1,
    'f2,
    'f3,
    'f4,
    'f5,
    'f6,
    'f7,
    'f8,
    'f9,
    'f10,
    'f11,
    'f12,
    'f13,
    'f14,
    'f15,
    'f16,
    'f17,
  ),
  (
    ('f1, 'f2, 'f3, 'f4, 'f5, 'f6, 'f7, 'f8, 'f9, 'f10, 'f11, 'f12, 'f13, 'f14, 'f15, 'f16, 'f17)
  ) => result<'r, DecodingError.t>,
  field<'f1>,
  field<'f2>,
  field<'f3>,
  field<'f4>,
  field<'f5>,
  field<'f6>,
  field<'f7>,
  field<'f8>,
  field<'f9>,
  field<'f10>,
  field<'f11>,
  field<'f12>,
  field<'f13>,
  field<'f14>,
  field<'f15>,
  field<'f16>,
  field<'f17>,
) => t<'r>

let object18: (
  'r => (
    'f1,
    'f2,
    'f3,
    'f4,
    'f5,
    'f6,
    'f7,
    'f8,
    'f9,
    'f10,
    'f11,
    'f12,
    'f13,
    'f14,
    'f15,
    'f16,
    'f17,
    'f18,
  ),
  (
    (
      'f1,
      'f2,
      'f3,
      'f4,
      'f5,
      'f6,
      'f7,
      'f8,
      'f9,
      'f10,
      'f11,
      'f12,
      'f13,
      'f14,
      'f15,
      'f16,
      'f17,
      'f18,
    )
  ) => result<'r, DecodingError.t>,
  field<'f1>,
  field<'f2>,
  field<'f3>,
  field<'f4>,
  field<'f5>,
  field<'f6>,
  field<'f7>,
  field<'f8>,
  field<'f9>,
  field<'f10>,
  field<'f11>,
  field<'f12>,
  field<'f13>,
  field<'f14>,
  field<'f15>,
  field<'f16>,
  field<'f17>,
  field<'f18>,
) => t<'r>

let object19: (
  'r => (
    'f1,
    'f2,
    'f3,
    'f4,
    'f5,
    'f6,
    'f7,
    'f8,
    'f9,
    'f10,
    'f11,
    'f12,
    'f13,
    'f14,
    'f15,
    'f16,
    'f17,
    'f18,
    'f19,
  ),
  (
    (
      'f1,
      'f2,
      'f3,
      'f4,
      'f5,
      'f6,
      'f7,
      'f8,
      'f9,
      'f10,
      'f11,
      'f12,
      'f13,
      'f14,
      'f15,
      'f16,
      'f17,
      'f18,
      'f19,
    )
  ) => result<'r, DecodingError.t>,
  field<'f1>,
  field<'f2>,
  field<'f3>,
  field<'f4>,
  field<'f5>,
  field<'f6>,
  field<'f7>,
  field<'f8>,
  field<'f9>,
  field<'f10>,
  field<'f11>,
  field<'f12>,
  field<'f13>,
  field<'f14>,
  field<'f15>,
  field<'f16>,
  field<'f17>,
  field<'f18>,
  field<'f19>,
) => t<'r>

let object20: (
  'r => (
    'f1,
    'f2,
    'f3,
    'f4,
    'f5,
    'f6,
    'f7,
    'f8,
    'f9,
    'f10,
    'f11,
    'f12,
    'f13,
    'f14,
    'f15,
    'f16,
    'f17,
    'f18,
    'f19,
    'f20,
  ),
  (
    (
      'f1,
      'f2,
      'f3,
      'f4,
      'f5,
      'f6,
      'f7,
      'f8,
      'f9,
      'f10,
      'f11,
      'f12,
      'f13,
      'f14,
      'f15,
      'f16,
      'f17,
      'f18,
      'f19,
      'f20,
    )
  ) => result<'r, DecodingError.t>,
  field<'f1>,
  field<'f2>,
  field<'f3>,
  field<'f4>,
  field<'f5>,
  field<'f6>,
  field<'f7>,
  field<'f8>,
  field<'f9>,
  field<'f10>,
  field<'f11>,
  field<'f12>,
  field<'f13>,
  field<'f14>,
  field<'f15>,
  field<'f16>,
  field<'f17>,
  field<'f18>,
  field<'f19>,
  field<'f20>,
) => t<'r>

let object21: (
  'r => (
    'f1,
    'f2,
    'f3,
    'f4,
    'f5,
    'f6,
    'f7,
    'f8,
    'f9,
    'f10,
    'f11,
    'f12,
    'f13,
    'f14,
    'f15,
    'f16,
    'f17,
    'f18,
    'f19,
    'f20,
    'f21,
  ),
  (
    (
      'f1,
      'f2,
      'f3,
      'f4,
      'f5,
      'f6,
      'f7,
      'f8,
      'f9,
      'f10,
      'f11,
      'f12,
      'f13,
      'f14,
      'f15,
      'f16,
      'f17,
      'f18,
      'f19,
      'f20,
      'f21,
    )
  ) => result<'r, DecodingError.t>,
  field<'f1>,
  field<'f2>,
  field<'f3>,
  field<'f4>,
  field<'f5>,
  field<'f6>,
  field<'f7>,
  field<'f8>,
  field<'f9>,
  field<'f10>,
  field<'f11>,
  field<'f12>,
  field<'f13>,
  field<'f14>,
  field<'f15>,
  field<'f16>,
  field<'f17>,
  field<'f18>,
  field<'f19>,
  field<'f20>,
  field<'f21>,
) => t<'r>

let object22: (
  'r => (
    'f1,
    'f2,
    'f3,
    'f4,
    'f5,
    'f6,
    'f7,
    'f8,
    'f9,
    'f10,
    'f11,
    'f12,
    'f13,
    'f14,
    'f15,
    'f16,
    'f17,
    'f18,
    'f19,
    'f20,
    'f21,
    'f22,
  ),
  (
    (
      'f1,
      'f2,
      'f3,
      'f4,
      'f5,
      'f6,
      'f7,
      'f8,
      'f9,
      'f10,
      'f11,
      'f12,
      'f13,
      'f14,
      'f15,
      'f16,
      'f17,
      'f18,
      'f19,
      'f20,
      'f21,
      'f22,
    )
  ) => result<'r, DecodingError.t>,
  field<'f1>,
  field<'f2>,
  field<'f3>,
  field<'f4>,
  field<'f5>,
  field<'f6>,
  field<'f7>,
  field<'f8>,
  field<'f9>,
  field<'f10>,
  field<'f11>,
  field<'f12>,
  field<'f13>,
  field<'f14>,
  field<'f15>,
  field<'f16>,
  field<'f17>,
  field<'f18>,
  field<'f19>,
  field<'f20>,
  field<'f21>,
  field<'f22>,
) => t<'r>

let object23: (
  'r => (
    'f1,
    'f2,
    'f3,
    'f4,
    'f5,
    'f6,
    'f7,
    'f8,
    'f9,
    'f10,
    'f11,
    'f12,
    'f13,
    'f14,
    'f15,
    'f16,
    'f17,
    'f18,
    'f19,
    'f20,
    'f21,
    'f22,
    'f23,
  ),
  (
    (
      'f1,
      'f2,
      'f3,
      'f4,
      'f5,
      'f6,
      'f7,
      'f8,
      'f9,
      'f10,
      'f11,
      'f12,
      'f13,
      'f14,
      'f15,
      'f16,
      'f17,
      'f18,
      'f19,
      'f20,
      'f21,
      'f22,
      'f23,
    )
  ) => result<'r, DecodingError.t>,
  field<'f1>,
  field<'f2>,
  field<'f3>,
  field<'f4>,
  field<'f5>,
  field<'f6>,
  field<'f7>,
  field<'f8>,
  field<'f9>,
  field<'f10>,
  field<'f11>,
  field<'f12>,
  field<'f13>,
  field<'f14>,
  field<'f15>,
  field<'f16>,
  field<'f17>,
  field<'f18>,
  field<'f19>,
  field<'f20>,
  field<'f21>,
  field<'f22>,
  field<'f23>,
) => t<'r>

let object24: (
  'r => (
    'f1,
    'f2,
    'f3,
    'f4,
    'f5,
    'f6,
    'f7,
    'f8,
    'f9,
    'f10,
    'f11,
    'f12,
    'f13,
    'f14,
    'f15,
    'f16,
    'f17,
    'f18,
    'f19,
    'f20,
    'f21,
    'f22,
    'f23,
    'f24,
  ),
  (
    (
      'f1,
      'f2,
      'f3,
      'f4,
      'f5,
      'f6,
      'f7,
      'f8,
      'f9,
      'f10,
      'f11,
      'f12,
      'f13,
      'f14,
      'f15,
      'f16,
      'f17,
      'f18,
      'f19,
      'f20,
      'f21,
      'f22,
      'f23,
      'f24,
    )
  ) => result<'r, DecodingError.t>,
  field<'f1>,
  field<'f2>,
  field<'f3>,
  field<'f4>,
  field<'f5>,
  field<'f6>,
  field<'f7>,
  field<'f8>,
  field<'f9>,
  field<'f10>,
  field<'f11>,
  field<'f12>,
  field<'f13>,
  field<'f14>,
  field<'f15>,
  field<'f16>,
  field<'f17>,
  field<'f18>,
  field<'f19>,
  field<'f20>,
  field<'f21>,
  field<'f22>,
  field<'f23>,
  field<'f24>,
) => t<'r>

let object25: (
  'r => (
    'f1,
    'f2,
    'f3,
    'f4,
    'f5,
    'f6,
    'f7,
    'f8,
    'f9,
    'f10,
    'f11,
    'f12,
    'f13,
    'f14,
    'f15,
    'f16,
    'f17,
    'f18,
    'f19,
    'f20,
    'f21,
    'f22,
    'f23,
    'f24,
    'f25,
  ),
  (
    (
      'f1,
      'f2,
      'f3,
      'f4,
      'f5,
      'f6,
      'f7,
      'f8,
      'f9,
      'f10,
      'f11,
      'f12,
      'f13,
      'f14,
      'f15,
      'f16,
      'f17,
      'f18,
      'f19,
      'f20,
      'f21,
      'f22,
      'f23,
      'f24,
      'f25,
    )
  ) => result<'r, DecodingError.t>,
  field<'f1>,
  field<'f2>,
  field<'f3>,
  field<'f4>,
  field<'f5>,
  field<'f6>,
  field<'f7>,
  field<'f8>,
  field<'f9>,
  field<'f10>,
  field<'f11>,
  field<'f12>,
  field<'f13>,
  field<'f14>,
  field<'f15>,
  field<'f16>,
  field<'f17>,
  field<'f18>,
  field<'f19>,
  field<'f20>,
  field<'f21>,
  field<'f22>,
  field<'f23>,
  field<'f24>,
  field<'f25>,
) => t<'r>
