type locale = [#"en-EN" | #"fr-FR"]

@genType let locale: locale

let t: string => string

let isPlural: int => bool

let template: (string, ~values: 'a=?, unit) => string

let phoneNumberFormat: string => string

type currency = [#EUR | #USD | #CNY]
@genType let currencyEur: currency
@genType let currencyUsd: currency
@genType let currencyCny: currency

let toCurrencySymbol: currency => string

let currencyFormat: (
  ~locale: locale=?,
  ~currency: currency,
  ~minimumFractionDigits: int=?,
  ~maximumFractionDigits: int=?,
  float,
) => string

let decimalFormat: (
  ~locale: locale=?,
  ~minimumFractionDigits: int=?,
  ~maximumFractionDigits: int=?,
  float,
) => string

let percentFormat: (
  ~locale: locale=?,
  ~minimumFractionDigits: int=?,
  ~maximumFractionDigits: int=?,
  float,
) => string

// > https://tc39.es/proposal-unified-intl-numberformat/section6/locales-currencies-tz_proposed_out.html#sec-issanctionedsimpleunitidentifier
// NOTE - native Intl `unit` optional field is not supported on some iOs browser versions
// hence only currently used units are handled for formatting.
type numberUnit = [
  | #gram
  | #kilogram
  | #liter
]

let numberUnitToString: numberUnit => string

let unitFormat: (
  ~unit: numberUnit,
  ~minimumFractionDigits: int=?,
  ~maximumFractionDigits: int=?,
  float,
) => string

type timeStyle = [
  | #full
  | #long
  | #medium
  | #short
]
type dateStyle = [
  | #full
  | #long
  | #medium
  | #short
]

type year = [#numeric | #"2-digit"]
type month = [
  | #numeric
  | #"2-digit"
  | #long
  | #short
  | #narrow
]
type day = [#numeric | #"2-digit"]
type hour = [#numeric | #"2-digit"]
type minute = [#numeric | #"2-digit"]
type second = [#numeric | #"2-digit"]

type dateTimeConstructor
type dateTimeFormatOptions = {
  dateStyle?: dateStyle,
  timeStyle?: timeStyle,
  year?: year,
  month?: month,
  day?: day,
  hour?: hour,
  minute?: minute,
  second?: second,
}
let dateTimeFormatConstructor: (
  ~locale: locale=?,
  ~dateTimeFormatOptions: dateTimeFormatOptions=?,
  unit,
) => dateTimeConstructor
let dateTimeFormat: (
  ~dateStyle: dateStyle=?,
  ~timeStyle: timeStyle=?,
  ~year: year=?,
  ~month: month=?,
  ~day: day=?,
  ~hour: hour=?,
  ~minute: minute=?,
  ~second: second=?,
  Js.Date.t,
) => string

let timeZone: string

let listFormat: (
  ~style: [#long | #short | #narrow],
  ~type_: [#conjunction | #disjunction | #unit],
  array<string>,
) => string

type collatorOptions = {
  usage?: [#search | #sort],
  sensitivity?: [#accent | #base | #case | #variant],
  caseFirst?: [#"false" | #lower | #upper],
  ignorePunctuation?: bool,
  numeric?: bool,
  localeMatcher?: [#"best fit" | #lookup],
}
