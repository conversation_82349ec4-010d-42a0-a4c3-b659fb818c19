let decodeResult = json => json->Json.decodeDict->Json.flatDecodeDictFieldString("ip")
let endpoint = "https://api64.ipify.org/?format=json"

let request = () =>
  Fetch.make(endpoint, {method: #GET})
  ->Promise.then(Fetch.Response.json)
  ->Promise.then(json => Promise.resolve(decodeResult(json)))
  ->FuturePromise.fromPromise
  ->Future.map(result =>
    switch result {
    | Ok(Some(ip)) => Ok(ip)
    | Ok(None) | Error(_) => Error()
    }
  )
