@module("react-dom")
external findDomNode: 'a => Dom.node = "findDOMNode"

let fromRef = ({React.current: current}) =>
  current->Js.Nullable.toOption->Option.map(findDomNode)->Option.map(WebAPI.DomElement.ofDomNode)

@return(nullable) @get
external toOptionalDomElement: ReactDOM.domRef => option<Dom.element> = "current"

@send external focus: Dom.element => unit = "focus"
@send external blur: Dom.element => unit = "blur"

let setDomElementStyleProp = %raw(` (domElement, styleProp, value) =>  domElement.style[styleProp] = value `)
