// NOTE - this bundle is already quite old ... functions using
// @wino/translator should be revamped at some point.

type locale = [#"fr-FR" | #"en-EN"]

let locale = switch Env.context() {
| #test => #"en-EN"
| _ => #"fr-FR"
}

@module("../languages/en-EN.json") external languageEnEN: Json.t = "default"
@module("../languages/fr-FR.json") external languageFrFR: Json.t = "default"

let languages = {
  "en-EN": languageEnEN,
  "fr-FR": languageFrFR,
}

// NOTE - the @wino/translator dep should be removed at some point
@module("@wino/translator")
external translation: (
  string,
  ~options: {"plural": bool, "locale": locale, "languages": 'languages},
) => string = "t"

// NOTE - values arg is no type safe
@module("@wino/translator")
external template: (string, ~values: 'a=?, unit) => string = "template"

let t = str =>
  translation(
    str,
    ~options={
      "plural": false,
      "languages": languages,
      "locale": locale,
    },
  )

@module("@wino/translator")
external isPluralTranslation: (int, ~options: {"locale": locale}) => int = "isPlural"

let isPlural = int => isPluralTranslation(int, ~options={"locale": locale}) === 1

// NOTE - Phone number format, very heavy dep for little use ?
// > https://gitlab.com/catamphetamine/libphonenumber-js
let phoneNumberFormat = string => {
  let phoneNumber = LibPhoneNumberJs.parsePhoneNumberFromString(string)
  switch phoneNumber {
  | Some(phoneNumber) => phoneNumber["format"](. #INTERNATIONAL)->Js.String2.trim
  | None =>
    switch string {
    | string if string->Js.String2.startsWith("+") === false =>
      switch string->Js.String2.replaceByRe(%re("/\s/g"), "")->Js.String2.match_(%re("/.{1,2}/g")) {
      | None => ""
      | Some(arr) if arr->Array.length === 0 => ""
      | Some(arr) => arr->Js.Array2.joinWith(" ")
      }
    | _ => string
    }
  }
}

// NOTE - RescriptCore, the new drop-in standard library for ReScript
// includes built-in Intl primitives.

// It use ISO 4217 format
// > https://www.six-group.com/en/products-services/financial-information/data-standards.html#scrollTo=maintenance-agency
type currency = [#EUR | #USD | #CNY]
let currencyEur = #EUR
let currencyUsd = #USD
let currencyCny = #CNY

let toCurrencySymbol = currency =>
  switch currency {
  | #EUR => "€"
  | #USD => "$"
  | #CNY => "¥"
  }

// > https://tc39.es/proposal-unified-intl-numberformat/section6/locales-currencies-tz_proposed_out.html#sec-issanctionedsimpleunitidentifier
// NOTE - native Intl `unit` optional field is not supported on some iOs browser versions
// hence only currently used units are handled for formatting.
type numberUnit = [
  | #gram
  | #kilogram
  | #liter
]
// | #acre
// | #bit
// | #byte
// | #celsius
// | #centimeter
// | #day
// | #degree
// | #fahrenheit
// | #"fluid-ounce"
// | #foot
// | #gallon
// | #gigabit
// | #gigabyte
// | #hectare
// | #hour
// | #inch
// | #kilobit
// | #kilobyte
// | #kilometer
// | #megabit
// | #megabyte
// | #meter
// | #mile
// | #"mile-scandinavian"
// | #milliliter
// | #millimeter
// | #millisecond
// | #minute
// | #month
// | #ounce
// | #percent
// | #petabyte
// | #pound
// | #second
// | #stone
// | #terabit
// | #terabyte
// | #week
// | #yard
// | #year

type numberStyle = [#currency | #decimal | #unit | #percent]

type numberFormatConstructorOptions = {
  minimumFractionDigits?: int,
  maximumFractionDigits?: int,
  style?: numberStyle,
  unit?: numberUnit,
  currency?: currency,
}

// Intl.NumberFormat constructor
// > https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/NumberFormat
type numberFormatConstructor
@new @scope("Intl")
external numberFormatConstructor: (
  locale,
  numberFormatConstructorOptions,
) => numberFormatConstructor = "NumberFormat"

// Intl.NumberFormat.prototype.format()
@send external format: (numberFormatConstructor, float) => string = "format"

let numberFormat = (locale, value, numberFormatConstructorOptions) =>
  numberFormatConstructor(locale, numberFormatConstructorOptions)->format(value)

let currencyFormat = (
  ~locale=locale,
  ~currency,
  ~minimumFractionDigits=2,
  ~maximumFractionDigits=2,
  value,
) =>
  numberFormat(
    locale,
    value,
    {
      style: #currency,
      currency,
      minimumFractionDigits,
      maximumFractionDigits,
    },
  )

let decimalFormat = (~locale=locale, ~minimumFractionDigits=?, ~maximumFractionDigits=?, value) =>
  numberFormat(locale, value, {style: #decimal, ?minimumFractionDigits, ?maximumFractionDigits})

let percentFormat = (~locale=locale, ~minimumFractionDigits=?, ~maximumFractionDigits=?, value) =>
  numberFormat(locale, value, {style: #percent, ?minimumFractionDigits, ?maximumFractionDigits})

let numberUnitToString = unit =>
  switch unit {
  | #kilogram => "kg"
  | #gram => "g"
  | #liter => "L"
  }

// NOTE - native Intl `unit` optional field is not supported on some iOs browser versions
let unitFormat = (~unit, ~minimumFractionDigits=?, ~maximumFractionDigits=?, value) =>
  decimalFormat(value, ~minimumFractionDigits?, ~maximumFractionDigits?) ++
  " " ++
  numberUnitToString(unit)

type timeStyle = [
  | #full
  | #long
  | #medium
  | #short
]
type dateStyle = [
  | #full
  | #long
  | #medium
  | #short
]

type year = [#numeric | #"2-digit"]
type month = [
  | #numeric
  | #"2-digit"
  | #long
  | #short
  | #narrow
]
type day = [#numeric | #"2-digit"]
type hour = [#numeric | #"2-digit"]
type minute = [#numeric | #"2-digit"]
type second = [#numeric | #"2-digit"]

type dateTimeFormatOptions = {
  dateStyle?: dateStyle,
  timeStyle?: timeStyle,
  year?: year,
  month?: month,
  day?: day,
  hour?: hour,
  minute?: minute,
  second?: second,
}

// Intl.DateTimeFormat constructor
// > https://developer.mozilla.org/en/docs/Web/JavaScript/Reference/Global_Objects/Intl/DateTimeFormat
type dateTimeConstructor
@new @scope("Intl")
external dateTimeFormatConstructor: (
  ~locale: locale=?,
  ~dateTimeFormatOptions: dateTimeFormatOptions=?,
  unit,
) => dateTimeConstructor = "DateTimeFormat"

// Intl.DateTimeFormat.prototype.format()
@send external format: (dateTimeConstructor, Js.Date.t) => string = "format"

let dateTimeFormat = (
  ~dateStyle=?,
  ~timeStyle=?,
  ~year=?,
  ~month=?,
  ~day=?,
  ~hour=?,
  ~minute=?,
  ~second=?,
  value,
) => {
  let dateTimeFormatOptions = if dateStyle->Option.isSome || timeStyle->Option.isSome {
    {?timeStyle, ?dateStyle}
  } else {
    {?year, ?month, ?day, ?hour, ?minute, ?second}
  }
  dateTimeFormatConstructor(~locale, ~dateTimeFormatOptions, ())->format(value)
}

type resolvedOptions = {timeZone: string}
@send external resolvedOptions: dateTimeConstructor => resolvedOptions = "resolvedOptions"
let timeZone = resolvedOptions(dateTimeFormatConstructor()).timeZone

// Intl.ListFormat constructor
// > https://developer.mozilla.org/en/docs/Web/JavaScript/Reference/Global_Objects/Intl/ListFormat
type listFormatConstructor
type listFormatStyle = [#long | #short | #narrow]
type listFormatType = [#conjunction | #disjunction | #unit]
type listFormatOptions = {style: listFormatStyle, @as("type") type_: listFormatType}
@new @scope("Intl")
external listFormatConstructor: (locale, listFormatOptions) => listFormatConstructor = "ListFormat"

// Intl.ListTimeFormat.prototype.format()
@send external format: (listFormatConstructor, array<string>) => string = "format"

let listFormat = (~style, ~type_, list) =>
  listFormatConstructor(locale, {style, type_})->format(list)

// Intl.Collator options
// > https://developer.mozilla.org/en/docs/Web/JavaScript/Reference/Global_Objects/Intl/Collator/Collator#options
type collatorOptions = {
  usage?: [#sort | #search],
  sensitivity?: [#base | #accent | #case | #variant],
  caseFirst?: [#"false" | #upper | #lower],
  ignorePunctuation?: bool,
  numeric?: bool,
  localeMatcher?: [#"best fit" | #lookup],
}
