type animation = [#fadeTranslation | #fadeBubbleTranslation | #fadePopinTranslation | #fade]
type timingFunction = [#cubicBezier]
type transition = {start: string, end: string, duration: string, timing: string}

// NOTE - update accordingly: public/animations.css
let makeTransition = (~animation, ~duration, ~timingFunction) => {
  let duration = (duration->Int.toFloat /. 1000.)->Float.toString ++ "s"
  let timing = switch timingFunction {
  | #cubicBezier => "cubic-bezier(.4, 0, .2, 1)"
  }
  {
    start: switch animation {
    | #fadeTranslation => "fadeTranslation"
    | #fadeBubbleTranslation => "fadeBubbleTranslation"
    | #fadePopinTranslation => "fadePopinTranslation"
    | #fade => "fadeIn"
    },
    end: "fadeOut",
    duration,
    timing,
  }
}

@react.component
let make = (
  ~children,
  ~displayed,
  ~animation=#fadeBubbleTranslation,
  ~duration=85, // NOTE - should be a multiple of ~16.7 for 60fps screen
  ~timingFunction=#cubicBezier,
) => {
  let (shouldRender, setShouldRender) = React.useState(() => displayed)

  ReactUpdateEffect.use1(() => {
    if displayed {
      setShouldRender(_ => true)
    }
    None
  }, [displayed])

  let onAnimationEnd = _ =>
    if !displayed {
      setShouldRender(_ => false)
    }

  let style = switch (displayed, makeTransition(~animation, ~duration, ~timingFunction)) {
  | (true, {start, duration, timing}) =>
    ReactDOM.Style.make(
      ~animationName=start,
      ~animationDuration=duration,
      ~animationTimingFunction=timing,
      (),
    )
  | (false, {end, duration, timing}) =>
    ReactDOM.Style.make(
      ~animationName=end,
      ~animationDuration=duration,
      ~animationTimingFunction=timing,
      (),
    )
  }

  if shouldRender {
    <div style onAnimationEnd> {children} </div>
  } else {
    React.null
  }
}

let make = React.memo(make)
