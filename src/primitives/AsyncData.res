type t<'a> =
  | NotAsked
  | Loading
  | Reloading('a)
  | Done('a)

let notAsked = () => NotAsked
let loading = () => Loading
let reloading = data => Reloading(data)
let done = data => Done(data)

let isReloading = asyncData =>
  switch asyncData {
  | Reloading(_) => true
  | NotAsked | Loading | Done(_) => false
  }

let isNotAsked = asyncData =>
  switch asyncData {
  | NotAsked => true
  | Reloading(_) | Loading | Done(_) => false
  }

let isBusy = asyncData =>
  switch asyncData {
  | Loading | Reloading(_) => true
  | NotAsked | Done(_) => false
  }

let isIdle = asyncData =>
  switch asyncData {
  | NotAsked | Done(_) => true
  | Loading | Reloading(_) => false
  }

let toBusy = asyncData =>
  switch asyncData {
  | Loading | Reloading(_) => asyncData
  | NotAsked => Loading
  | Done(done) => Reloading(done)
  }

let map = (asyncResult, transform) =>
  switch asyncResult {
  | NotAsked => NotAsked
  | Loading => Loading
  | Reloading(result) => Reloading(transform(result))
  | Done(result) => Done(transform(result))
  }

let flatMap = (asyncResult, transform) =>
  switch asyncResult {
  | NotAsked => NotAsked
  | Loading => Loading
  | Reloading(result) => transform(result)
  | Done(result) => transform(result)
  }

let mapWithDefault = (asyncResult, defaultValue, transform) =>
  switch asyncResult {
  | Reloading(result) | Done(result) => transform(result)
  | Loading | NotAsked => defaultValue
  }

let getWithDefault = (asyncResult, defaultValue) =>
  switch asyncResult {
  | Reloading(result) | Done(result) => result
  | Loading | NotAsked => defaultValue
  }
