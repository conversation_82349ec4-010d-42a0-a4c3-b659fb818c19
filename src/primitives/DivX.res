// NOTE — For compatibility with some popovers in non-modal mode,
// a tabIndex value is required to make the element focusable so the popover
// can close when clicked. tabIndex = -1 keeps it out of keyboard navigation.
@react.component
let make = React.forwardRef((~children=?, ~style=StyleX.props([]), ref) => {
  let ref = ref->Js.Nullable.toOption->Option.map(ReactDOM.Ref.domRef)
  let {?style, ?className} = style
  switch children {
  | Some(children) => <div tabIndex={-1} ?ref ?style ?className> children </div>
  | None => <div tabIndex={-1} ?ref ?style ?className />
  }
})
