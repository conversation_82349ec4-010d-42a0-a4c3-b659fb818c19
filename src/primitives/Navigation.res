// NOTE - There are a lot of different ideas around routing from back
// and front end frameworks. Sometimes a word in one context might have
// different meaning than another. Here we have a whole set of words
// to define similar things: pathname, route, url, push, navigate …
// This should be reviewed in favor of a simple definition of routing.

type url = {
  pathname: string,
  path: array<string>,
  query: QueryString.t,
  state: option<Json.t>,
}

let urlToRoute = url =>
  url.pathname ++
  switch url.query->QueryString.toString {
  | "" => ""
  | queryString => "?" ++ queryString
  }

let pathFromPathname = pathname =>
  switch pathname {
  | "" | "/" => []
  | raw =>
    let raw = Js.String.sliceToEnd(~from=1, raw)
    let raw = switch Js.String.get(raw, Js.String.length(raw) - 1) {
    | "/" => Js.String.slice(~from=0, ~to_=-1, raw)
    | _ => raw
    }
    raw->Js.String2.split("/")
  }

let history = switch Env.context() {
| #test => History.createMemoryHistory()
| _ => History.createBrowserHistory()
}

let useRouter = () => React.useContext(ReactRouter.contextRouter)

let useUrl = () => {
  let {location} = useRouter()

  {
    pathname: location.pathname,
    path: location.pathname->pathFromPathname,
    query: location.search->Js.String2.substr(~from=1)->QueryString.fromString,
    state: location.state,
  }
}

let useNavigate = () => {
  let {history} = useRouter()

  (~replace=false, ~state=?, pathname) => {
    let pushAction = replace ? history.replace : history.push

    pushAction(pathname, state)
  }
}

let useGoBack = () => {
  let {history} = useRouter()

  let canGoBack = switch history.index {
  | Some(historyIndex) => historyIndex > 0
  | None => history.length > 1
  }
  let onGoBack = () => history.goBack()

  (canGoBack, onGoBack)
}

let openRoute = route => history.replace(route, None)
let openNewTabRoute = route => WebAPI.window->WebAPI.Window.openUrl(route)->ignore
let closeBrowserTab = () => WebAPI.window->WebAPI.Window.close

type to =
  | Route(string)
  | RouteWithQueryString(string, QueryString.t)
  | Url(Url.t)

module Link = {
  let toRoute = route => Route(route)
  let toUrl = href => Url(href)

  @react.component
  let make = (
    ~children,
    ~to,
    ~openNewTab=false,
    ~disabled=false,
    ~className=?,
    ~ariaProps=?,
    ~onPress=?,
  ) => {
    let defaultProps = {
      ReactAria.Link.href: ?switch to {
      | Url(url) => Some(url->Url.href)
      | _ => None
      },
      target: openNewTab ? #_blank : #_self,
      ?onPress,
      disabled,
    }
    let props = ariaProps->Option.getWithDefault(defaultProps)
    let {linkProps} = ReactAria.Link.use(~props, ())

    <ReactAria.Spread props=linkProps>
      {if !disabled {
        switch to {
        | Route(pathname) =>
          let replace = pathname === ""
          <ReactRouter.Link to={pathname: pathname} replace ?className> children </ReactRouter.Link>
        | RouteWithQueryString(pathname, query) =>
          let replace = pathname === ""
          <ReactRouter.Link to={pathname, search: query->QueryString.toString} replace ?className>
            children
          </ReactRouter.Link>
        | Url(_) => <a ?className> children </a>
        }
      } else {
        <span role="link" ?className> children </span>
      }}
    </ReactAria.Spread>
  }
}

module Provider = {
  @react.component
  let make = (~children, ~history as customHistory=?) => {
    <ReactRouter.Router history={customHistory->Option.getWithDefault(history)}>
      children
    </ReactRouter.Router>
  }
}

// TODO - handling a custom UI when blocking is possible
module Prompt = {
  @react.component
  let make = React.memo((~message, ~shouldBlockOnRouteChange) => {
    let {history, location} = useRouter()
    let unblock = React.useRef(None)

    let handleUnload = React.useCallback2(domEvent =>
      if shouldBlockOnRouteChange(location.pathname) {
        domEvent->WebAPI.DomEvent.setReturnValue(message)
      }
    , (location.pathname, shouldBlockOnRouteChange))

    React.useEffect1(() => {
      unblock.current = Some(
        history.block(
          (location, _action) =>
            if shouldBlockOnRouteChange(location.pathname) {
              Some(message)
            } else {
              None
            },
        ),
      )

      Some(
        () =>
          switch unblock.current {
          | Some(unblock) => unblock()
          | _ => ()
          },
      )
    }, [shouldBlockOnRouteChange])

    React.useEffect1(() => {
      WebAPI.window->WebAPI.Window.addEventListener("beforeunload", handleUnload)

      Some(_ => WebAPI.window->WebAPI.Window.removeEventListener("beforeunload", handleUnload))
    }, [handleUnload])

    React.null
  })
}

module Redirect = {
  @react.component
  let make = (~route as pathname) => <ReactRouter.Redirect to={pathname: pathname} />
}
