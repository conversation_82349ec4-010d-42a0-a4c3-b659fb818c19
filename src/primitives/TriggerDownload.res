let fromUrl = (~filename=?, url) =>
  Future.make(resolve => {
    open WebAPI

    let body = document->Document.querySelector("body")
    let tempLink = document->Document.createElement("a")

    tempLink->DomElement.setAttribute("style", "display: none;")
    tempLink->DomElement.setAttribute("href", url->Url.href)
    tempLink->DomElement.setAttribute("download", filename->Option.getWithDefault(url->Url.href))

    switch tempLink->DomElement.getAttribute("download") {
    | None => tempLink->DomElement.setAttribute("target", "_blank")
    | _ => ()
    }

    switch (body, tempLink->HtmlElement.ofDomElement) {
    | (Some(body), Some(tempLinkHtmlElement)) => {
        body->DomElement.asNode->DomNode.appendChild(~child=tempLink)
        tempLinkHtmlElement->HtmlElement.click

        let timeoutId = Js.Global.setTimeout(() => {
          body->DomElement.asNode->DomNode.removeChild(~child=tempLink)->ignore

          try {
            Url.revokeObjectURL(url->Url.href)

            resolve(Ok())
          } catch {
          | _ => resolve(Error())
          }
        }, 200)

        Some(() => Js.Global.clearTimeout(timeoutId))
      }

    | _ =>
      resolve(Error())
      None
    }
  })

let fromBlob = (blob, ~filename) =>
  Url.createObjectURL(File.make([blob->Blob.blobToBlobPart], filename))
  ->Url.make
  ->fromUrl(~filename)
