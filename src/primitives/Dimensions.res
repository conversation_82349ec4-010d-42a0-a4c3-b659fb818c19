open WebAPI

type t = {
  width: float,
  height: float,
}

let use = (~ref as forwardedRef=?, ()) => {
  let (dimensions, setDimensions) = React.useState(_ => {width: 0., height: 0.})
  let ref = switch forwardedRef {
  | Some(ref) => ref
  | _ => React.useRef(Js.Nullable.null)
  }

  let resizeTimeoutIdRef = React.useRef(Js.Nullable.null)

  let onResizeChanged = React.useCallback1((maybeDomElement, _) => {
    switch maybeDomElement {
    | Some(domElement) =>
      let dimensions = {
        width: Js.Math.floor_float(domElement->DomElement.offsetWidth),
        height: Js.Math.floor_float(domElement->DomElement.offsetHeight),
      }
      let timeoutId = Js.Global.setTimeout(_ => setDimensions(_ => dimensions), 75)
      resizeTimeoutIdRef.current = timeoutId->Js.Nullable.return
    | None => ()
    }
    switch resizeTimeoutIdRef.current->Js.Nullable.toOption {
    | Some(id) => Js.Global.clearTimeout(id)
    | _ => ()
    }
  }, [ref.current])

  // Get the dimensions at mounting then on window resizing
  // FIXME - won't resize when sidebar toggles (see ResizeObserver API)
  React.useEffect1(() => {
    switch ref->ReactDomElement.fromRef {
    | Some(domElement) =>
      window->Window.addEventListener("resize", onResizeChanged(Some(domElement)))
      setDimensions(_ => {
        width: Js.Math.floor_float(domElement->DomElement.offsetWidth),
        height: Js.Math.floor_float(domElement->DomElement.offsetHeight),
      })
    | _ => ()
    }

    Some(_ => window->Window.removeEventListener("resize", onResizeChanged(None)))
  }, [ref.current])

  (ref, dimensions)
}
