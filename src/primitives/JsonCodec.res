// NOTE - This module is a minimalist version of https://github.com/nkrkv/jzon
// with slight modifications or improvements.

module JsonResult = {
  let mapError = (result, fn) =>
    switch result {
    | Ok(_) as ok => ok
    | Error(err) => Error(fn(err))
    }

  let sequence = results =>
    results->Array.reduce(Ok([]), (maybeAcc, res) =>
      maybeAcc->Result.flatMap(acc => res->Result.map(x => acc->Array.concat([x])))
    )
}

module DecodingError = {
  type locationComponent = Field(string) | Index(int)
  type location = array<locationComponent>

  type t = [
    | #SyntaxError(string)
    | #MissingField(location, string)
    | #UnexpectedJsonType(location, string, Json.t)
    | #UnexpectedJsonValue(location, string)
  ]

  let prependLocation = (err, loc) =>
    switch err {
    | #SyntaxError(_) as err => err
    | #MissingField(location, key) =>
      let location' = [loc]->Array.concat(location)

      #MissingField(location', key)
    | #UnexpectedJsonType(location, expectation, actualJson) =>
      let location' = [loc]->Array.concat(location)

      #UnexpectedJsonType(location', expectation, actualJson)
    | #UnexpectedJsonValue(location, found) =>
      let location' = [loc]->Array.concat(location)

      #UnexpectedJsonValue(location', found)
    }
}

module Codec = {
  type encode<'value> = 'value => Json.t
  type decode<'value> = Json.t => result<'value, DecodingError.t>

  type t<'value> = {
    encode: encode<'value>,
    decode: decode<'value>,
  }

  let encode = codec => codec.encode
  let decode = codec => codec.decode

  let make = (encode, decode) => {encode, decode}
}

type t<'value> = Codec.t<'value>

let encode = Codec.encode
let encodeWith = (data, codec) => codec->encode(data)

let decode = Codec.decode
let decodeWith = (json, codec) => codec->decode(json)

let string = Codec.make(Json.encodeString, json =>
  switch json->Json.decodeString {
  | Some(x) => Ok(x)
  | None => Error(#UnexpectedJsonType([], "string", json))
  }
)

let float = Codec.make(Json.encodeNumber, json =>
  switch json->Json.decodeNumber {
  | Some(x) => Ok(x)
  | None => Error(#UnexpectedJsonType([], "number", json))
  }
)

let int = Codec.make(
  x => float->encode(x->Int.toFloat),
  json =>
    float
    ->decode(json)
    ->Result.flatMap(x =>
      x == x->Js.Math.trunc && x >= -2147483648. && x <= 2147483647.
        ? Ok(x->Js.Math.unsafe_trunc)
        : Error(#UnexpectedJsonValue([], x->Float.toString))
    ),
)

let bool = Codec.make(Json.encodeBoolean, json =>
  switch json->Json.decodeBoolean {
  | Some(x) => Ok(x)
  | None => Error(#UnexpectedJsonType([], "bool", json))
  }
)

let nullable = payloadCodec =>
  Codec.make(
    maybeValue =>
      switch maybeValue {
      | Some(value) => payloadCodec->encode(value)
      | None => Json.encodedNull
      },
    json =>
      json == Json.encodedNull ? Ok(None) : payloadCodec->decode(json)->Result.map(v => Some(v)),
  )

let array = elementCodec =>
  Codec.make(
    x => x->Array.map(elementCodec->encode(_))->Json.encodeArray,
    json =>
      switch json->Js.Json.classify {
      | JSONArray(elementJsons) =>
        elementJsons
        ->Array.mapWithIndex((i, elemJson) =>
          elementCodec
          ->decode(elemJson)
          ->JsonResult.mapError(DecodingError.prependLocation(_, Index(i)))
        )
        ->JsonResult.sequence
      | _ => Error(#UnexpectedJsonType([], "array", json))
      },
  )

let asObject = json =>
  switch json->Js.Json.classify {
  | JSONObject(fieldset) => Ok(fieldset)
  | _ => Error(#UnexpectedJsonType([], "object", json))
  }

module Field = {
  type path = Key(string)

  type claim<'value> =
    | Required
    | Optional

  type t<'value> = {
    path: path,
    codec: Codec.t<'value>,
    claim: claim<'value>,
  }

  let make = (path, codec) => {path, codec, claim: Required}
  let makeOptional = ({path, codec}) => {path, codec: codec->nullable, claim: Optional}

  let path = ({path}) => path
  let codec = ({codec}) => codec
  let claim = ({claim}) => claim

  let encode = (field, val) =>
    switch field->path {
    | Key(key) =>
      let json = field->codec->Codec.encode(val)

      json == Json.encodedNull && field->claim == Optional ? [] : [(key, json)]
    }

  let decode = (field, fieldset) =>
    switch field->path {
    | Key(key) =>
      let decodeChild = childJson =>
        field
        ->codec
        ->Codec.decode(childJson)
        ->JsonResult.mapError(DecodingError.prependLocation(_, Field(key)))

      switch (fieldset->Js.Dict.get(key), field->claim) {
      | (Some(childJson), _) => decodeChild(childJson)
      | (None, Optional) => decodeChild(Json.encodedNull)
      | (None, Required) => Error(#MissingField([], key))
      }
    }
}

type field<'value> = Field.t<'value>

let field = (key, codec) => Field.make(Key(key), codec)
let optional = Field.makeOptional

let jsonObject = keyVals => Json.encodeDict(Js.Dict.fromArray(keyVals->Array.concatMany))

let object1 = (destruct, construct, field1) =>
  Codec.make(
    value => {
      let val1 = destruct(value)
      jsonObject([Field.encode(field1, val1)])
    },
    json =>
      json
      ->asObject
      ->Result.flatMap(fieldset =>
        switch field1->Field.decode(fieldset) {
        | Ok(val1) => construct(val1)
        | Error(_) as err => err
        }
      ),
  )

let object2 = (destruct, construct, field1, field2) =>
  Codec.make(
    value => {
      let (val1, val2) = destruct(value)
      jsonObject([Field.encode(field1, val1), Field.encode(field2, val2)])
    },
    json =>
      json
      ->asObject
      ->Result.flatMap(fieldset =>
        switch field1->Field.decode(fieldset) {
        | Ok(val1) =>
          switch field2->Field.decode(fieldset) {
          | Ok(val2) => construct((val1, val2))
          | Error(_) as err => err
          }
        | Error(_) as err => err
        }
      ),
  )

let object3 = (destruct, construct, field1, field2, field3) =>
  Codec.make(
    value => {
      let (val1, val2, val3) = destruct(value)
      jsonObject([
        Field.encode(field1, val1),
        Field.encode(field2, val2),
        Field.encode(field3, val3),
      ])
    },
    json =>
      json
      ->asObject
      ->Result.flatMap(fieldset =>
        switch field1->Field.decode(fieldset) {
        | Ok(val1) =>
          switch field2->Field.decode(fieldset) {
          | Ok(val2) =>
            switch field3->Field.decode(fieldset) {
            | Ok(val3) => construct((val1, val2, val3))
            | Error(_) as err => err
            }
          | Error(_) as err => err
          }
        | Error(_) as err => err
        }
      ),
  )

let object4 = (destruct, construct, field1, field2, field3, field4) =>
  Codec.make(
    value => {
      let (val1, val2, val3, val4) = destruct(value)
      jsonObject([
        Field.encode(field1, val1),
        Field.encode(field2, val2),
        Field.encode(field3, val3),
        Field.encode(field4, val4),
      ])
    },
    json =>
      json
      ->asObject
      ->Result.flatMap(fieldset =>
        switch field1->Field.decode(fieldset) {
        | Ok(val1) =>
          switch field2->Field.decode(fieldset) {
          | Ok(val2) =>
            switch field3->Field.decode(fieldset) {
            | Ok(val3) =>
              switch field4->Field.decode(fieldset) {
              | Ok(val4) => construct((val1, val2, val3, val4))
              | Error(_) as err => err
              }
            | Error(_) as err => err
            }
          | Error(_) as err => err
          }
        | Error(_) as err => err
        }
      ),
  )

let object5 = (destruct, construct, field1, field2, field3, field4, field5) =>
  Codec.make(
    value => {
      let (val1, val2, val3, val4, val5) = destruct(value)
      jsonObject([
        Field.encode(field1, val1),
        Field.encode(field2, val2),
        Field.encode(field3, val3),
        Field.encode(field4, val4),
        Field.encode(field5, val5),
      ])
    },
    json =>
      json
      ->asObject
      ->Result.flatMap(fieldset =>
        switch field1->Field.decode(fieldset) {
        | Ok(val1) =>
          switch field2->Field.decode(fieldset) {
          | Ok(val2) =>
            switch field3->Field.decode(fieldset) {
            | Ok(val3) =>
              switch field4->Field.decode(fieldset) {
              | Ok(val4) =>
                switch field5->Field.decode(fieldset) {
                | Ok(val5) => construct((val1, val2, val3, val4, val5))
                | Error(_) as err => err
                }
              | Error(_) as err => err
              }
            | Error(_) as err => err
            }
          | Error(_) as err => err
          }
        | Error(_) as err => err
        }
      ),
  )

let object6 = (destruct, construct, field1, field2, field3, field4, field5, field6) =>
  Codec.make(
    value => {
      let (val1, val2, val3, val4, val5, val6) = destruct(value)
      jsonObject([
        Field.encode(field1, val1),
        Field.encode(field2, val2),
        Field.encode(field3, val3),
        Field.encode(field4, val4),
        Field.encode(field5, val5),
        Field.encode(field6, val6),
      ])
    },
    json =>
      json
      ->asObject
      ->Result.flatMap(fieldset =>
        switch field1->Field.decode(fieldset) {
        | Ok(val1) =>
          switch field2->Field.decode(fieldset) {
          | Ok(val2) =>
            switch field3->Field.decode(fieldset) {
            | Ok(val3) =>
              switch field4->Field.decode(fieldset) {
              | Ok(val4) =>
                switch field5->Field.decode(fieldset) {
                | Ok(val5) =>
                  switch field6->Field.decode(fieldset) {
                  | Ok(val6) => construct((val1, val2, val3, val4, val5, val6))
                  | Error(_) as err => err
                  }
                | Error(_) as err => err
                }
              | Error(_) as err => err
              }
            | Error(_) as err => err
            }
          | Error(_) as err => err
          }
        | Error(_) as err => err
        }
      ),
  )

let object7 = (destruct, construct, field1, field2, field3, field4, field5, field6, field7) =>
  Codec.make(
    value => {
      let (val1, val2, val3, val4, val5, val6, val7) = destruct(value)
      jsonObject([
        Field.encode(field1, val1),
        Field.encode(field2, val2),
        Field.encode(field3, val3),
        Field.encode(field4, val4),
        Field.encode(field5, val5),
        Field.encode(field6, val6),
        Field.encode(field7, val7),
      ])
    },
    json =>
      json
      ->asObject
      ->Result.flatMap(fieldset =>
        switch field1->Field.decode(fieldset) {
        | Ok(val1) =>
          switch field2->Field.decode(fieldset) {
          | Ok(val2) =>
            switch field3->Field.decode(fieldset) {
            | Ok(val3) =>
              switch field4->Field.decode(fieldset) {
              | Ok(val4) =>
                switch field5->Field.decode(fieldset) {
                | Ok(val5) =>
                  switch field6->Field.decode(fieldset) {
                  | Ok(val6) =>
                    switch field7->Field.decode(fieldset) {
                    | Ok(val7) => construct((val1, val2, val3, val4, val5, val6, val7))
                    | Error(_) as err => err
                    }
                  | Error(_) as err => err
                  }
                | Error(_) as err => err
                }
              | Error(_) as err => err
              }
            | Error(_) as err => err
            }
          | Error(_) as err => err
          }
        | Error(_) as err => err
        }
      ),
  )

let object8 = (
  destruct,
  construct,
  field1,
  field2,
  field3,
  field4,
  field5,
  field6,
  field7,
  field8,
) =>
  Codec.make(
    value => {
      let (val1, val2, val3, val4, val5, val6, val7, val8) = destruct(value)
      jsonObject([
        Field.encode(field1, val1),
        Field.encode(field2, val2),
        Field.encode(field3, val3),
        Field.encode(field4, val4),
        Field.encode(field5, val5),
        Field.encode(field6, val6),
        Field.encode(field7, val7),
        Field.encode(field8, val8),
      ])
    },
    json =>
      json
      ->asObject
      ->Result.flatMap(fieldset =>
        switch field1->Field.decode(fieldset) {
        | Ok(val1) =>
          switch field2->Field.decode(fieldset) {
          | Ok(val2) =>
            switch field3->Field.decode(fieldset) {
            | Ok(val3) =>
              switch field4->Field.decode(fieldset) {
              | Ok(val4) =>
                switch field5->Field.decode(fieldset) {
                | Ok(val5) =>
                  switch field6->Field.decode(fieldset) {
                  | Ok(val6) =>
                    switch field7->Field.decode(fieldset) {
                    | Ok(val7) =>
                      switch field8->Field.decode(fieldset) {
                      | Ok(val8) => construct((val1, val2, val3, val4, val5, val6, val7, val8))
                      | Error(_) as err => err
                      }
                    | Error(_) as err => err
                    }
                  | Error(_) as err => err
                  }
                | Error(_) as err => err
                }
              | Error(_) as err => err
              }
            | Error(_) as err => err
            }
          | Error(_) as err => err
          }
        | Error(_) as err => err
        }
      ),
  )

let object9 = (
  destruct,
  construct,
  field1,
  field2,
  field3,
  field4,
  field5,
  field6,
  field7,
  field8,
  field9,
) =>
  Codec.make(
    value => {
      let (val1, val2, val3, val4, val5, val6, val7, val8, val9) = destruct(value)
      jsonObject([
        Field.encode(field1, val1),
        Field.encode(field2, val2),
        Field.encode(field3, val3),
        Field.encode(field4, val4),
        Field.encode(field5, val5),
        Field.encode(field6, val6),
        Field.encode(field7, val7),
        Field.encode(field8, val8),
        Field.encode(field9, val9),
      ])
    },
    json =>
      json
      ->asObject
      ->Result.flatMap(fieldset =>
        switch field1->Field.decode(fieldset) {
        | Ok(val1) =>
          switch field2->Field.decode(fieldset) {
          | Ok(val2) =>
            switch field3->Field.decode(fieldset) {
            | Ok(val3) =>
              switch field4->Field.decode(fieldset) {
              | Ok(val4) =>
                switch field5->Field.decode(fieldset) {
                | Ok(val5) =>
                  switch field6->Field.decode(fieldset) {
                  | Ok(val6) =>
                    switch field7->Field.decode(fieldset) {
                    | Ok(val7) =>
                      switch field8->Field.decode(fieldset) {
                      | Ok(val8) =>
                        switch field9->Field.decode(fieldset) {
                        | Ok(val9) =>
                          construct((val1, val2, val3, val4, val5, val6, val7, val8, val9))
                        | Error(_) as err => err
                        }
                      | Error(_) as err => err
                      }
                    | Error(_) as err => err
                    }
                  | Error(_) as err => err
                  }
                | Error(_) as err => err
                }
              | Error(_) as err => err
              }
            | Error(_) as err => err
            }
          | Error(_) as err => err
          }
        | Error(_) as err => err
        }
      ),
  )

let object10 = (
  destruct,
  construct,
  field1,
  field2,
  field3,
  field4,
  field5,
  field6,
  field7,
  field8,
  field9,
  field10,
) =>
  Codec.make(
    value => {
      let (val1, val2, val3, val4, val5, val6, val7, val8, val9, val10) = destruct(value)
      jsonObject([
        Field.encode(field1, val1),
        Field.encode(field2, val2),
        Field.encode(field3, val3),
        Field.encode(field4, val4),
        Field.encode(field5, val5),
        Field.encode(field6, val6),
        Field.encode(field7, val7),
        Field.encode(field8, val8),
        Field.encode(field9, val9),
        Field.encode(field10, val10),
      ])
    },
    json =>
      json
      ->asObject
      ->Result.flatMap(fieldset =>
        switch field1->Field.decode(fieldset) {
        | Ok(val1) =>
          switch field2->Field.decode(fieldset) {
          | Ok(val2) =>
            switch field3->Field.decode(fieldset) {
            | Ok(val3) =>
              switch field4->Field.decode(fieldset) {
              | Ok(val4) =>
                switch field5->Field.decode(fieldset) {
                | Ok(val5) =>
                  switch field6->Field.decode(fieldset) {
                  | Ok(val6) =>
                    switch field7->Field.decode(fieldset) {
                    | Ok(val7) =>
                      switch field8->Field.decode(fieldset) {
                      | Ok(val8) =>
                        switch field9->Field.decode(fieldset) {
                        | Ok(val9) =>
                          switch field10->Field.decode(fieldset) {
                          | Ok(val10) =>
                            construct((val1, val2, val3, val4, val5, val6, val7, val8, val9, val10))
                          | Error(_) as err => err
                          }
                        | Error(_) as err => err
                        }
                      | Error(_) as err => err
                      }
                    | Error(_) as err => err
                    }
                  | Error(_) as err => err
                  }
                | Error(_) as err => err
                }
              | Error(_) as err => err
              }
            | Error(_) as err => err
            }
          | Error(_) as err => err
          }
        | Error(_) as err => err
        }
      ),
  )

let object11 = (
  destruct,
  construct,
  field1,
  field2,
  field3,
  field4,
  field5,
  field6,
  field7,
  field8,
  field9,
  field10,
  field11,
) =>
  Codec.make(
    value => {
      let (val1, val2, val3, val4, val5, val6, val7, val8, val9, val10, val11) = destruct(value)
      jsonObject([
        Field.encode(field1, val1),
        Field.encode(field2, val2),
        Field.encode(field3, val3),
        Field.encode(field4, val4),
        Field.encode(field5, val5),
        Field.encode(field6, val6),
        Field.encode(field7, val7),
        Field.encode(field8, val8),
        Field.encode(field9, val9),
        Field.encode(field10, val10),
        Field.encode(field11, val11),
      ])
    },
    json =>
      json
      ->asObject
      ->Result.flatMap(fieldset =>
        switch field1->Field.decode(fieldset) {
        | Ok(val1) =>
          switch field2->Field.decode(fieldset) {
          | Ok(val2) =>
            switch field3->Field.decode(fieldset) {
            | Ok(val3) =>
              switch field4->Field.decode(fieldset) {
              | Ok(val4) =>
                switch field5->Field.decode(fieldset) {
                | Ok(val5) =>
                  switch field6->Field.decode(fieldset) {
                  | Ok(val6) =>
                    switch field7->Field.decode(fieldset) {
                    | Ok(val7) =>
                      switch field8->Field.decode(fieldset) {
                      | Ok(val8) =>
                        switch field9->Field.decode(fieldset) {
                        | Ok(val9) =>
                          switch field10->Field.decode(fieldset) {
                          | Ok(val10) =>
                            switch field11->Field.decode(fieldset) {
                            | Ok(val11) =>
                              construct((
                                val1,
                                val2,
                                val3,
                                val4,
                                val5,
                                val6,
                                val7,
                                val8,
                                val9,
                                val10,
                                val11,
                              ))
                            | Error(_) as err => err
                            }
                          | Error(_) as err => err
                          }
                        | Error(_) as err => err
                        }
                      | Error(_) as err => err
                      }
                    | Error(_) as err => err
                    }
                  | Error(_) as err => err
                  }
                | Error(_) as err => err
                }
              | Error(_) as err => err
              }
            | Error(_) as err => err
            }
          | Error(_) as err => err
          }
        | Error(_) as err => err
        }
      ),
  )

let object12 = (
  destruct,
  construct,
  field1,
  field2,
  field3,
  field4,
  field5,
  field6,
  field7,
  field8,
  field9,
  field10,
  field11,
  field12,
) =>
  Codec.make(
    value => {
      let (val1, val2, val3, val4, val5, val6, val7, val8, val9, val10, val11, val12) = destruct(
        value,
      )
      jsonObject([
        Field.encode(field1, val1),
        Field.encode(field2, val2),
        Field.encode(field3, val3),
        Field.encode(field4, val4),
        Field.encode(field5, val5),
        Field.encode(field6, val6),
        Field.encode(field7, val7),
        Field.encode(field8, val8),
        Field.encode(field9, val9),
        Field.encode(field10, val10),
        Field.encode(field11, val11),
        Field.encode(field12, val12),
      ])
    },
    json =>
      json
      ->asObject
      ->Result.flatMap(fieldset =>
        switch field1->Field.decode(fieldset) {
        | Ok(val1) =>
          switch field2->Field.decode(fieldset) {
          | Ok(val2) =>
            switch field3->Field.decode(fieldset) {
            | Ok(val3) =>
              switch field4->Field.decode(fieldset) {
              | Ok(val4) =>
                switch field5->Field.decode(fieldset) {
                | Ok(val5) =>
                  switch field6->Field.decode(fieldset) {
                  | Ok(val6) =>
                    switch field7->Field.decode(fieldset) {
                    | Ok(val7) =>
                      switch field8->Field.decode(fieldset) {
                      | Ok(val8) =>
                        switch field9->Field.decode(fieldset) {
                        | Ok(val9) =>
                          switch field10->Field.decode(fieldset) {
                          | Ok(val10) =>
                            switch field11->Field.decode(fieldset) {
                            | Ok(val11) =>
                              switch field12->Field.decode(fieldset) {
                              | Ok(val12) =>
                                construct((
                                  val1,
                                  val2,
                                  val3,
                                  val4,
                                  val5,
                                  val6,
                                  val7,
                                  val8,
                                  val9,
                                  val10,
                                  val11,
                                  val12,
                                ))
                              | Error(_) as err => err
                              }
                            | Error(_) as err => err
                            }
                          | Error(_) as err => err
                          }
                        | Error(_) as err => err
                        }
                      | Error(_) as err => err
                      }
                    | Error(_) as err => err
                    }
                  | Error(_) as err => err
                  }
                | Error(_) as err => err
                }
              | Error(_) as err => err
              }
            | Error(_) as err => err
            }
          | Error(_) as err => err
          }
        | Error(_) as err => err
        }
      ),
  )

let object13 = (
  destruct,
  construct,
  field1,
  field2,
  field3,
  field4,
  field5,
  field6,
  field7,
  field8,
  field9,
  field10,
  field11,
  field12,
  field13,
) =>
  Codec.make(
    value => {
      let (
        val1,
        val2,
        val3,
        val4,
        val5,
        val6,
        val7,
        val8,
        val9,
        val10,
        val11,
        val12,
        val13,
      ) = destruct(value)
      jsonObject([
        Field.encode(field1, val1),
        Field.encode(field2, val2),
        Field.encode(field3, val3),
        Field.encode(field4, val4),
        Field.encode(field5, val5),
        Field.encode(field6, val6),
        Field.encode(field7, val7),
        Field.encode(field8, val8),
        Field.encode(field9, val9),
        Field.encode(field10, val10),
        Field.encode(field11, val11),
        Field.encode(field12, val12),
        Field.encode(field13, val13),
      ])
    },
    json =>
      json
      ->asObject
      ->Result.flatMap(fieldset =>
        switch field1->Field.decode(fieldset) {
        | Ok(val1) =>
          switch field2->Field.decode(fieldset) {
          | Ok(val2) =>
            switch field3->Field.decode(fieldset) {
            | Ok(val3) =>
              switch field4->Field.decode(fieldset) {
              | Ok(val4) =>
                switch field5->Field.decode(fieldset) {
                | Ok(val5) =>
                  switch field6->Field.decode(fieldset) {
                  | Ok(val6) =>
                    switch field7->Field.decode(fieldset) {
                    | Ok(val7) =>
                      switch field8->Field.decode(fieldset) {
                      | Ok(val8) =>
                        switch field9->Field.decode(fieldset) {
                        | Ok(val9) =>
                          switch field10->Field.decode(fieldset) {
                          | Ok(val10) =>
                            switch field11->Field.decode(fieldset) {
                            | Ok(val11) =>
                              switch field12->Field.decode(fieldset) {
                              | Ok(val12) =>
                                switch field13->Field.decode(fieldset) {
                                | Ok(val13) =>
                                  construct((
                                    val1,
                                    val2,
                                    val3,
                                    val4,
                                    val5,
                                    val6,
                                    val7,
                                    val8,
                                    val9,
                                    val10,
                                    val11,
                                    val12,
                                    val13,
                                  ))
                                | Error(_) as err => err
                                }
                              | Error(_) as err => err
                              }
                            | Error(_) as err => err
                            }
                          | Error(_) as err => err
                          }
                        | Error(_) as err => err
                        }
                      | Error(_) as err => err
                      }
                    | Error(_) as err => err
                    }
                  | Error(_) as err => err
                  }
                | Error(_) as err => err
                }
              | Error(_) as err => err
              }
            | Error(_) as err => err
            }
          | Error(_) as err => err
          }
        | Error(_) as err => err
        }
      ),
  )

let object14 = (
  destruct,
  construct,
  field1,
  field2,
  field3,
  field4,
  field5,
  field6,
  field7,
  field8,
  field9,
  field10,
  field11,
  field12,
  field13,
  field14,
) =>
  Codec.make(
    value => {
      let (
        val1,
        val2,
        val3,
        val4,
        val5,
        val6,
        val7,
        val8,
        val9,
        val10,
        val11,
        val12,
        val13,
        val14,
      ) = destruct(value)
      jsonObject([
        Field.encode(field1, val1),
        Field.encode(field2, val2),
        Field.encode(field3, val3),
        Field.encode(field4, val4),
        Field.encode(field5, val5),
        Field.encode(field6, val6),
        Field.encode(field7, val7),
        Field.encode(field8, val8),
        Field.encode(field9, val9),
        Field.encode(field10, val10),
        Field.encode(field11, val11),
        Field.encode(field12, val12),
        Field.encode(field13, val13),
        Field.encode(field14, val14),
      ])
    },
    json =>
      json
      ->asObject
      ->Result.flatMap(fieldset =>
        switch field1->Field.decode(fieldset) {
        | Ok(val1) =>
          switch field2->Field.decode(fieldset) {
          | Ok(val2) =>
            switch field3->Field.decode(fieldset) {
            | Ok(val3) =>
              switch field4->Field.decode(fieldset) {
              | Ok(val4) =>
                switch field5->Field.decode(fieldset) {
                | Ok(val5) =>
                  switch field6->Field.decode(fieldset) {
                  | Ok(val6) =>
                    switch field7->Field.decode(fieldset) {
                    | Ok(val7) =>
                      switch field8->Field.decode(fieldset) {
                      | Ok(val8) =>
                        switch field9->Field.decode(fieldset) {
                        | Ok(val9) =>
                          switch field10->Field.decode(fieldset) {
                          | Ok(val10) =>
                            switch field11->Field.decode(fieldset) {
                            | Ok(val11) =>
                              switch field12->Field.decode(fieldset) {
                              | Ok(val12) =>
                                switch field13->Field.decode(fieldset) {
                                | Ok(val13) =>
                                  switch field14->Field.decode(fieldset) {
                                  | Ok(val14) =>
                                    construct((
                                      val1,
                                      val2,
                                      val3,
                                      val4,
                                      val5,
                                      val6,
                                      val7,
                                      val8,
                                      val9,
                                      val10,
                                      val11,
                                      val12,
                                      val13,
                                      val14,
                                    ))
                                  | Error(_) as err => err
                                  }
                                | Error(_) as err => err
                                }
                              | Error(_) as err => err
                              }
                            | Error(_) as err => err
                            }
                          | Error(_) as err => err
                          }
                        | Error(_) as err => err
                        }
                      | Error(_) as err => err
                      }
                    | Error(_) as err => err
                    }
                  | Error(_) as err => err
                  }
                | Error(_) as err => err
                }
              | Error(_) as err => err
              }
            | Error(_) as err => err
            }
          | Error(_) as err => err
          }
        | Error(_) as err => err
        }
      ),
  )

let object15 = (
  destruct,
  construct,
  field1,
  field2,
  field3,
  field4,
  field5,
  field6,
  field7,
  field8,
  field9,
  field10,
  field11,
  field12,
  field13,
  field14,
  field15,
) =>
  Codec.make(
    value => {
      let (
        val1,
        val2,
        val3,
        val4,
        val5,
        val6,
        val7,
        val8,
        val9,
        val10,
        val11,
        val12,
        val13,
        val14,
        val15,
      ) = destruct(value)
      jsonObject([
        Field.encode(field1, val1),
        Field.encode(field2, val2),
        Field.encode(field3, val3),
        Field.encode(field4, val4),
        Field.encode(field5, val5),
        Field.encode(field6, val6),
        Field.encode(field7, val7),
        Field.encode(field8, val8),
        Field.encode(field9, val9),
        Field.encode(field10, val10),
        Field.encode(field11, val11),
        Field.encode(field12, val12),
        Field.encode(field13, val13),
        Field.encode(field14, val14),
        Field.encode(field15, val15),
      ])
    },
    json =>
      json
      ->asObject
      ->Result.flatMap(fieldset =>
        switch field1->Field.decode(fieldset) {
        | Ok(val1) =>
          switch field2->Field.decode(fieldset) {
          | Ok(val2) =>
            switch field3->Field.decode(fieldset) {
            | Ok(val3) =>
              switch field4->Field.decode(fieldset) {
              | Ok(val4) =>
                switch field5->Field.decode(fieldset) {
                | Ok(val5) =>
                  switch field6->Field.decode(fieldset) {
                  | Ok(val6) =>
                    switch field7->Field.decode(fieldset) {
                    | Ok(val7) =>
                      switch field8->Field.decode(fieldset) {
                      | Ok(val8) =>
                        switch field9->Field.decode(fieldset) {
                        | Ok(val9) =>
                          switch field10->Field.decode(fieldset) {
                          | Ok(val10) =>
                            switch field11->Field.decode(fieldset) {
                            | Ok(val11) =>
                              switch field12->Field.decode(fieldset) {
                              | Ok(val12) =>
                                switch field13->Field.decode(fieldset) {
                                | Ok(val13) =>
                                  switch field14->Field.decode(fieldset) {
                                  | Ok(val14) =>
                                    switch field15->Field.decode(fieldset) {
                                    | Ok(val15) =>
                                      construct((
                                        val1,
                                        val2,
                                        val3,
                                        val4,
                                        val5,
                                        val6,
                                        val7,
                                        val8,
                                        val9,
                                        val10,
                                        val11,
                                        val12,
                                        val13,
                                        val14,
                                        val15,
                                      ))
                                    | Error(_) as err => err
                                    }
                                  | Error(_) as err => err
                                  }
                                | Error(_) as err => err
                                }
                              | Error(_) as err => err
                              }
                            | Error(_) as err => err
                            }
                          | Error(_) as err => err
                          }
                        | Error(_) as err => err
                        }
                      | Error(_) as err => err
                      }
                    | Error(_) as err => err
                    }
                  | Error(_) as err => err
                  }
                | Error(_) as err => err
                }
              | Error(_) as err => err
              }
            | Error(_) as err => err
            }
          | Error(_) as err => err
          }
        | Error(_) as err => err
        }
      ),
  )

let object16 = (
  destruct,
  construct,
  field1,
  field2,
  field3,
  field4,
  field5,
  field6,
  field7,
  field8,
  field9,
  field10,
  field11,
  field12,
  field13,
  field14,
  field15,
  field16,
) =>
  Codec.make(
    value => {
      let (
        val1,
        val2,
        val3,
        val4,
        val5,
        val6,
        val7,
        val8,
        val9,
        val10,
        val11,
        val12,
        val13,
        val14,
        val15,
        val16,
      ) = destruct(value)
      jsonObject([
        Field.encode(field1, val1),
        Field.encode(field2, val2),
        Field.encode(field3, val3),
        Field.encode(field4, val4),
        Field.encode(field5, val5),
        Field.encode(field6, val6),
        Field.encode(field7, val7),
        Field.encode(field8, val8),
        Field.encode(field9, val9),
        Field.encode(field10, val10),
        Field.encode(field11, val11),
        Field.encode(field12, val12),
        Field.encode(field13, val13),
        Field.encode(field14, val14),
        Field.encode(field15, val15),
        Field.encode(field16, val16),
      ])
    },
    json =>
      json
      ->asObject
      ->Result.flatMap(fieldset =>
        switch field1->Field.decode(fieldset) {
        | Ok(val1) =>
          switch field2->Field.decode(fieldset) {
          | Ok(val2) =>
            switch field3->Field.decode(fieldset) {
            | Ok(val3) =>
              switch field4->Field.decode(fieldset) {
              | Ok(val4) =>
                switch field5->Field.decode(fieldset) {
                | Ok(val5) =>
                  switch field6->Field.decode(fieldset) {
                  | Ok(val6) =>
                    switch field7->Field.decode(fieldset) {
                    | Ok(val7) =>
                      switch field8->Field.decode(fieldset) {
                      | Ok(val8) =>
                        switch field9->Field.decode(fieldset) {
                        | Ok(val9) =>
                          switch field10->Field.decode(fieldset) {
                          | Ok(val10) =>
                            switch field11->Field.decode(fieldset) {
                            | Ok(val11) =>
                              switch field12->Field.decode(fieldset) {
                              | Ok(val12) =>
                                switch field13->Field.decode(fieldset) {
                                | Ok(val13) =>
                                  switch field14->Field.decode(fieldset) {
                                  | Ok(val14) =>
                                    switch field15->Field.decode(fieldset) {
                                    | Ok(val15) =>
                                      switch field16->Field.decode(fieldset) {
                                      | Ok(val16) =>
                                        construct((
                                          val1,
                                          val2,
                                          val3,
                                          val4,
                                          val5,
                                          val6,
                                          val7,
                                          val8,
                                          val9,
                                          val10,
                                          val11,
                                          val12,
                                          val13,
                                          val14,
                                          val15,
                                          val16,
                                        ))
                                      | Error(_) as err => err
                                      }
                                    | Error(_) as err => err
                                    }
                                  | Error(_) as err => err
                                  }
                                | Error(_) as err => err
                                }
                              | Error(_) as err => err
                              }
                            | Error(_) as err => err
                            }
                          | Error(_) as err => err
                          }
                        | Error(_) as err => err
                        }
                      | Error(_) as err => err
                      }
                    | Error(_) as err => err
                    }
                  | Error(_) as err => err
                  }
                | Error(_) as err => err
                }
              | Error(_) as err => err
              }
            | Error(_) as err => err
            }
          | Error(_) as err => err
          }
        | Error(_) as err => err
        }
      ),
  )

let object17 = (
  destruct,
  construct,
  field1,
  field2,
  field3,
  field4,
  field5,
  field6,
  field7,
  field8,
  field9,
  field10,
  field11,
  field12,
  field13,
  field14,
  field15,
  field16,
  field17,
) =>
  Codec.make(
    value => {
      let (
        val1,
        val2,
        val3,
        val4,
        val5,
        val6,
        val7,
        val8,
        val9,
        val10,
        val11,
        val12,
        val13,
        val14,
        val15,
        val16,
        val17,
      ) = destruct(value)
      jsonObject([
        Field.encode(field1, val1),
        Field.encode(field2, val2),
        Field.encode(field3, val3),
        Field.encode(field4, val4),
        Field.encode(field5, val5),
        Field.encode(field6, val6),
        Field.encode(field7, val7),
        Field.encode(field8, val8),
        Field.encode(field9, val9),
        Field.encode(field10, val10),
        Field.encode(field11, val11),
        Field.encode(field12, val12),
        Field.encode(field13, val13),
        Field.encode(field14, val14),
        Field.encode(field15, val15),
        Field.encode(field16, val16),
        Field.encode(field17, val17),
      ])
    },
    json =>
      json
      ->asObject
      ->Result.flatMap(fieldset =>
        switch field1->Field.decode(fieldset) {
        | Ok(val1) =>
          switch field2->Field.decode(fieldset) {
          | Ok(val2) =>
            switch field3->Field.decode(fieldset) {
            | Ok(val3) =>
              switch field4->Field.decode(fieldset) {
              | Ok(val4) =>
                switch field5->Field.decode(fieldset) {
                | Ok(val5) =>
                  switch field6->Field.decode(fieldset) {
                  | Ok(val6) =>
                    switch field7->Field.decode(fieldset) {
                    | Ok(val7) =>
                      switch field8->Field.decode(fieldset) {
                      | Ok(val8) =>
                        switch field9->Field.decode(fieldset) {
                        | Ok(val9) =>
                          switch field10->Field.decode(fieldset) {
                          | Ok(val10) =>
                            switch field11->Field.decode(fieldset) {
                            | Ok(val11) =>
                              switch field12->Field.decode(fieldset) {
                              | Ok(val12) =>
                                switch field13->Field.decode(fieldset) {
                                | Ok(val13) =>
                                  switch field14->Field.decode(fieldset) {
                                  | Ok(val14) =>
                                    switch field15->Field.decode(fieldset) {
                                    | Ok(val15) =>
                                      switch field16->Field.decode(fieldset) {
                                      | Ok(val16) =>
                                        switch field17->Field.decode(fieldset) {
                                        | Ok(val17) =>
                                          construct((
                                            val1,
                                            val2,
                                            val3,
                                            val4,
                                            val5,
                                            val6,
                                            val7,
                                            val8,
                                            val9,
                                            val10,
                                            val11,
                                            val12,
                                            val13,
                                            val14,
                                            val15,
                                            val16,
                                            val17,
                                          ))
                                        | Error(_) as err => err
                                        }
                                      | Error(_) as err => err
                                      }
                                    | Error(_) as err => err
                                    }
                                  | Error(_) as err => err
                                  }
                                | Error(_) as err => err
                                }
                              | Error(_) as err => err
                              }
                            | Error(_) as err => err
                            }
                          | Error(_) as err => err
                          }
                        | Error(_) as err => err
                        }
                      | Error(_) as err => err
                      }
                    | Error(_) as err => err
                    }
                  | Error(_) as err => err
                  }
                | Error(_) as err => err
                }
              | Error(_) as err => err
              }
            | Error(_) as err => err
            }
          | Error(_) as err => err
          }
        | Error(_) as err => err
        }
      ),
  )

let object18 = (
  destruct,
  construct,
  field1,
  field2,
  field3,
  field4,
  field5,
  field6,
  field7,
  field8,
  field9,
  field10,
  field11,
  field12,
  field13,
  field14,
  field15,
  field16,
  field17,
  field18,
) =>
  Codec.make(
    value => {
      let (
        val1,
        val2,
        val3,
        val4,
        val5,
        val6,
        val7,
        val8,
        val9,
        val10,
        val11,
        val12,
        val13,
        val14,
        val15,
        val16,
        val17,
        val18,
      ) = destruct(value)
      jsonObject([
        Field.encode(field1, val1),
        Field.encode(field2, val2),
        Field.encode(field3, val3),
        Field.encode(field4, val4),
        Field.encode(field5, val5),
        Field.encode(field6, val6),
        Field.encode(field7, val7),
        Field.encode(field8, val8),
        Field.encode(field9, val9),
        Field.encode(field10, val10),
        Field.encode(field11, val11),
        Field.encode(field12, val12),
        Field.encode(field13, val13),
        Field.encode(field14, val14),
        Field.encode(field15, val15),
        Field.encode(field16, val16),
        Field.encode(field17, val17),
        Field.encode(field18, val18),
      ])
    },
    json =>
      json
      ->asObject
      ->Result.flatMap(fieldset =>
        switch field1->Field.decode(fieldset) {
        | Ok(val1) =>
          switch field2->Field.decode(fieldset) {
          | Ok(val2) =>
            switch field3->Field.decode(fieldset) {
            | Ok(val3) =>
              switch field4->Field.decode(fieldset) {
              | Ok(val4) =>
                switch field5->Field.decode(fieldset) {
                | Ok(val5) =>
                  switch field6->Field.decode(fieldset) {
                  | Ok(val6) =>
                    switch field7->Field.decode(fieldset) {
                    | Ok(val7) =>
                      switch field8->Field.decode(fieldset) {
                      | Ok(val8) =>
                        switch field9->Field.decode(fieldset) {
                        | Ok(val9) =>
                          switch field10->Field.decode(fieldset) {
                          | Ok(val10) =>
                            switch field11->Field.decode(fieldset) {
                            | Ok(val11) =>
                              switch field12->Field.decode(fieldset) {
                              | Ok(val12) =>
                                switch field13->Field.decode(fieldset) {
                                | Ok(val13) =>
                                  switch field14->Field.decode(fieldset) {
                                  | Ok(val14) =>
                                    switch field15->Field.decode(fieldset) {
                                    | Ok(val15) =>
                                      switch field16->Field.decode(fieldset) {
                                      | Ok(val16) =>
                                        switch field17->Field.decode(fieldset) {
                                        | Ok(val17) =>
                                          switch field18->Field.decode(fieldset) {
                                          | Ok(val18) =>
                                            construct((
                                              val1,
                                              val2,
                                              val3,
                                              val4,
                                              val5,
                                              val6,
                                              val7,
                                              val8,
                                              val9,
                                              val10,
                                              val11,
                                              val12,
                                              val13,
                                              val14,
                                              val15,
                                              val16,
                                              val17,
                                              val18,
                                            ))
                                          | Error(_) as err => err
                                          }
                                        | Error(_) as err => err
                                        }
                                      | Error(_) as err => err
                                      }
                                    | Error(_) as err => err
                                    }
                                  | Error(_) as err => err
                                  }
                                | Error(_) as err => err
                                }
                              | Error(_) as err => err
                              }
                            | Error(_) as err => err
                            }
                          | Error(_) as err => err
                          }
                        | Error(_) as err => err
                        }
                      | Error(_) as err => err
                      }
                    | Error(_) as err => err
                    }
                  | Error(_) as err => err
                  }
                | Error(_) as err => err
                }
              | Error(_) as err => err
              }
            | Error(_) as err => err
            }
          | Error(_) as err => err
          }
        | Error(_) as err => err
        }
      ),
  )

let object19 = (
  destruct,
  construct,
  field1,
  field2,
  field3,
  field4,
  field5,
  field6,
  field7,
  field8,
  field9,
  field10,
  field11,
  field12,
  field13,
  field14,
  field15,
  field16,
  field17,
  field18,
  field19,
) =>
  Codec.make(
    value => {
      let (
        val1,
        val2,
        val3,
        val4,
        val5,
        val6,
        val7,
        val8,
        val9,
        val10,
        val11,
        val12,
        val13,
        val14,
        val15,
        val16,
        val17,
        val18,
        val19,
      ) = destruct(value)
      jsonObject([
        Field.encode(field1, val1),
        Field.encode(field2, val2),
        Field.encode(field3, val3),
        Field.encode(field4, val4),
        Field.encode(field5, val5),
        Field.encode(field6, val6),
        Field.encode(field7, val7),
        Field.encode(field8, val8),
        Field.encode(field9, val9),
        Field.encode(field10, val10),
        Field.encode(field11, val11),
        Field.encode(field12, val12),
        Field.encode(field13, val13),
        Field.encode(field14, val14),
        Field.encode(field15, val15),
        Field.encode(field16, val16),
        Field.encode(field17, val17),
        Field.encode(field18, val18),
        Field.encode(field19, val19),
      ])
    },
    json =>
      json
      ->asObject
      ->Result.flatMap(fieldset =>
        switch field1->Field.decode(fieldset) {
        | Ok(val1) =>
          switch field2->Field.decode(fieldset) {
          | Ok(val2) =>
            switch field3->Field.decode(fieldset) {
            | Ok(val3) =>
              switch field4->Field.decode(fieldset) {
              | Ok(val4) =>
                switch field5->Field.decode(fieldset) {
                | Ok(val5) =>
                  switch field6->Field.decode(fieldset) {
                  | Ok(val6) =>
                    switch field7->Field.decode(fieldset) {
                    | Ok(val7) =>
                      switch field8->Field.decode(fieldset) {
                      | Ok(val8) =>
                        switch field9->Field.decode(fieldset) {
                        | Ok(val9) =>
                          switch field10->Field.decode(fieldset) {
                          | Ok(val10) =>
                            switch field11->Field.decode(fieldset) {
                            | Ok(val11) =>
                              switch field12->Field.decode(fieldset) {
                              | Ok(val12) =>
                                switch field13->Field.decode(fieldset) {
                                | Ok(val13) =>
                                  switch field14->Field.decode(fieldset) {
                                  | Ok(val14) =>
                                    switch field15->Field.decode(fieldset) {
                                    | Ok(val15) =>
                                      switch field16->Field.decode(fieldset) {
                                      | Ok(val16) =>
                                        switch field17->Field.decode(fieldset) {
                                        | Ok(val17) =>
                                          switch field18->Field.decode(fieldset) {
                                          | Ok(val18) =>
                                            switch field19->Field.decode(fieldset) {
                                            | Ok(val19) =>
                                              construct((
                                                val1,
                                                val2,
                                                val3,
                                                val4,
                                                val5,
                                                val6,
                                                val7,
                                                val8,
                                                val9,
                                                val10,
                                                val11,
                                                val12,
                                                val13,
                                                val14,
                                                val15,
                                                val16,
                                                val17,
                                                val18,
                                                val19,
                                              ))
                                            | Error(_) as err => err
                                            }
                                          | Error(_) as err => err
                                          }
                                        | Error(_) as err => err
                                        }
                                      | Error(_) as err => err
                                      }
                                    | Error(_) as err => err
                                    }
                                  | Error(_) as err => err
                                  }
                                | Error(_) as err => err
                                }
                              | Error(_) as err => err
                              }
                            | Error(_) as err => err
                            }
                          | Error(_) as err => err
                          }
                        | Error(_) as err => err
                        }
                      | Error(_) as err => err
                      }
                    | Error(_) as err => err
                    }
                  | Error(_) as err => err
                  }
                | Error(_) as err => err
                }
              | Error(_) as err => err
              }
            | Error(_) as err => err
            }
          | Error(_) as err => err
          }
        | Error(_) as err => err
        }
      ),
  )

let object20 = (
  destruct,
  construct,
  field1,
  field2,
  field3,
  field4,
  field5,
  field6,
  field7,
  field8,
  field9,
  field10,
  field11,
  field12,
  field13,
  field14,
  field15,
  field16,
  field17,
  field18,
  field19,
  field20,
) =>
  Codec.make(
    value => {
      let (
        val1,
        val2,
        val3,
        val4,
        val5,
        val6,
        val7,
        val8,
        val9,
        val10,
        val11,
        val12,
        val13,
        val14,
        val15,
        val16,
        val17,
        val18,
        val19,
        val20,
      ) = destruct(value)
      jsonObject([
        Field.encode(field1, val1),
        Field.encode(field2, val2),
        Field.encode(field3, val3),
        Field.encode(field4, val4),
        Field.encode(field5, val5),
        Field.encode(field6, val6),
        Field.encode(field7, val7),
        Field.encode(field8, val8),
        Field.encode(field9, val9),
        Field.encode(field10, val10),
        Field.encode(field11, val11),
        Field.encode(field12, val12),
        Field.encode(field13, val13),
        Field.encode(field14, val14),
        Field.encode(field15, val15),
        Field.encode(field16, val16),
        Field.encode(field17, val17),
        Field.encode(field18, val18),
        Field.encode(field19, val19),
        Field.encode(field20, val20),
      ])
    },
    json =>
      json
      ->asObject
      ->Result.flatMap(fieldset =>
        switch field1->Field.decode(fieldset) {
        | Ok(val1) =>
          switch field2->Field.decode(fieldset) {
          | Ok(val2) =>
            switch field3->Field.decode(fieldset) {
            | Ok(val3) =>
              switch field4->Field.decode(fieldset) {
              | Ok(val4) =>
                switch field5->Field.decode(fieldset) {
                | Ok(val5) =>
                  switch field6->Field.decode(fieldset) {
                  | Ok(val6) =>
                    switch field7->Field.decode(fieldset) {
                    | Ok(val7) =>
                      switch field8->Field.decode(fieldset) {
                      | Ok(val8) =>
                        switch field9->Field.decode(fieldset) {
                        | Ok(val9) =>
                          switch field10->Field.decode(fieldset) {
                          | Ok(val10) =>
                            switch field11->Field.decode(fieldset) {
                            | Ok(val11) =>
                              switch field12->Field.decode(fieldset) {
                              | Ok(val12) =>
                                switch field13->Field.decode(fieldset) {
                                | Ok(val13) =>
                                  switch field14->Field.decode(fieldset) {
                                  | Ok(val14) =>
                                    switch field15->Field.decode(fieldset) {
                                    | Ok(val15) =>
                                      switch field16->Field.decode(fieldset) {
                                      | Ok(val16) =>
                                        switch field17->Field.decode(fieldset) {
                                        | Ok(val17) =>
                                          switch field18->Field.decode(fieldset) {
                                          | Ok(val18) =>
                                            switch field19->Field.decode(fieldset) {
                                            | Ok(val19) =>
                                              switch field20->Field.decode(fieldset) {
                                              | Ok(val20) =>
                                                construct((
                                                  val1,
                                                  val2,
                                                  val3,
                                                  val4,
                                                  val5,
                                                  val6,
                                                  val7,
                                                  val8,
                                                  val9,
                                                  val10,
                                                  val11,
                                                  val12,
                                                  val13,
                                                  val14,
                                                  val15,
                                                  val16,
                                                  val17,
                                                  val18,
                                                  val19,
                                                  val20,
                                                ))
                                              | Error(_) as err => err
                                              }
                                            | Error(_) as err => err
                                            }
                                          | Error(_) as err => err
                                          }
                                        | Error(_) as err => err
                                        }
                                      | Error(_) as err => err
                                      }
                                    | Error(_) as err => err
                                    }
                                  | Error(_) as err => err
                                  }
                                | Error(_) as err => err
                                }
                              | Error(_) as err => err
                              }
                            | Error(_) as err => err
                            }
                          | Error(_) as err => err
                          }
                        | Error(_) as err => err
                        }
                      | Error(_) as err => err
                      }
                    | Error(_) as err => err
                    }
                  | Error(_) as err => err
                  }
                | Error(_) as err => err
                }
              | Error(_) as err => err
              }
            | Error(_) as err => err
            }
          | Error(_) as err => err
          }
        | Error(_) as err => err
        }
      ),
  )

let object21 = (
  destruct,
  construct,
  field1,
  field2,
  field3,
  field4,
  field5,
  field6,
  field7,
  field8,
  field9,
  field10,
  field11,
  field12,
  field13,
  field14,
  field15,
  field16,
  field17,
  field18,
  field19,
  field20,
  field21,
) =>
  Codec.make(
    value => {
      let (
        val1,
        val2,
        val3,
        val4,
        val5,
        val6,
        val7,
        val8,
        val9,
        val10,
        val11,
        val12,
        val13,
        val14,
        val15,
        val16,
        val17,
        val18,
        val19,
        val20,
        val21,
      ) = destruct(value)
      jsonObject([
        Field.encode(field1, val1),
        Field.encode(field2, val2),
        Field.encode(field3, val3),
        Field.encode(field4, val4),
        Field.encode(field5, val5),
        Field.encode(field6, val6),
        Field.encode(field7, val7),
        Field.encode(field8, val8),
        Field.encode(field9, val9),
        Field.encode(field10, val10),
        Field.encode(field11, val11),
        Field.encode(field12, val12),
        Field.encode(field13, val13),
        Field.encode(field14, val14),
        Field.encode(field15, val15),
        Field.encode(field16, val16),
        Field.encode(field17, val17),
        Field.encode(field18, val18),
        Field.encode(field19, val19),
        Field.encode(field20, val20),
        Field.encode(field21, val21),
      ])
    },
    json =>
      json
      ->asObject
      ->Result.flatMap(fieldset =>
        switch field1->Field.decode(fieldset) {
        | Ok(val1) =>
          switch field2->Field.decode(fieldset) {
          | Ok(val2) =>
            switch field3->Field.decode(fieldset) {
            | Ok(val3) =>
              switch field4->Field.decode(fieldset) {
              | Ok(val4) =>
                switch field5->Field.decode(fieldset) {
                | Ok(val5) =>
                  switch field6->Field.decode(fieldset) {
                  | Ok(val6) =>
                    switch field7->Field.decode(fieldset) {
                    | Ok(val7) =>
                      switch field8->Field.decode(fieldset) {
                      | Ok(val8) =>
                        switch field9->Field.decode(fieldset) {
                        | Ok(val9) =>
                          switch field10->Field.decode(fieldset) {
                          | Ok(val10) =>
                            switch field11->Field.decode(fieldset) {
                            | Ok(val11) =>
                              switch field12->Field.decode(fieldset) {
                              | Ok(val12) =>
                                switch field13->Field.decode(fieldset) {
                                | Ok(val13) =>
                                  switch field14->Field.decode(fieldset) {
                                  | Ok(val14) =>
                                    switch field15->Field.decode(fieldset) {
                                    | Ok(val15) =>
                                      switch field16->Field.decode(fieldset) {
                                      | Ok(val16) =>
                                        switch field17->Field.decode(fieldset) {
                                        | Ok(val17) =>
                                          switch field18->Field.decode(fieldset) {
                                          | Ok(val18) =>
                                            switch field19->Field.decode(fieldset) {
                                            | Ok(val19) =>
                                              switch field20->Field.decode(fieldset) {
                                              | Ok(val20) =>
                                                switch field21->Field.decode(fieldset) {
                                                | Ok(val21) =>
                                                  construct((
                                                    val1,
                                                    val2,
                                                    val3,
                                                    val4,
                                                    val5,
                                                    val6,
                                                    val7,
                                                    val8,
                                                    val9,
                                                    val10,
                                                    val11,
                                                    val12,
                                                    val13,
                                                    val14,
                                                    val15,
                                                    val16,
                                                    val17,
                                                    val18,
                                                    val19,
                                                    val20,
                                                    val21,
                                                  ))
                                                | Error(_) as err => err
                                                }
                                              | Error(_) as err => err
                                              }
                                            | Error(_) as err => err
                                            }
                                          | Error(_) as err => err
                                          }
                                        | Error(_) as err => err
                                        }
                                      | Error(_) as err => err
                                      }
                                    | Error(_) as err => err
                                    }
                                  | Error(_) as err => err
                                  }
                                | Error(_) as err => err
                                }
                              | Error(_) as err => err
                              }
                            | Error(_) as err => err
                            }
                          | Error(_) as err => err
                          }
                        | Error(_) as err => err
                        }
                      | Error(_) as err => err
                      }
                    | Error(_) as err => err
                    }
                  | Error(_) as err => err
                  }
                | Error(_) as err => err
                }
              | Error(_) as err => err
              }
            | Error(_) as err => err
            }
          | Error(_) as err => err
          }
        | Error(_) as err => err
        }
      ),
  )

let object22 = (
  destruct,
  construct,
  field1,
  field2,
  field3,
  field4,
  field5,
  field6,
  field7,
  field8,
  field9,
  field10,
  field11,
  field12,
  field13,
  field14,
  field15,
  field16,
  field17,
  field18,
  field19,
  field20,
  field21,
  field22,
) =>
  Codec.make(
    value => {
      let (
        val1,
        val2,
        val3,
        val4,
        val5,
        val6,
        val7,
        val8,
        val9,
        val10,
        val11,
        val12,
        val13,
        val14,
        val15,
        val16,
        val17,
        val18,
        val19,
        val20,
        val21,
        val22,
      ) = destruct(value)
      jsonObject([
        Field.encode(field1, val1),
        Field.encode(field2, val2),
        Field.encode(field3, val3),
        Field.encode(field4, val4),
        Field.encode(field5, val5),
        Field.encode(field6, val6),
        Field.encode(field7, val7),
        Field.encode(field8, val8),
        Field.encode(field9, val9),
        Field.encode(field10, val10),
        Field.encode(field11, val11),
        Field.encode(field12, val12),
        Field.encode(field13, val13),
        Field.encode(field14, val14),
        Field.encode(field15, val15),
        Field.encode(field16, val16),
        Field.encode(field17, val17),
        Field.encode(field18, val18),
        Field.encode(field19, val19),
        Field.encode(field20, val20),
        Field.encode(field21, val21),
        Field.encode(field22, val22),
      ])
    },
    json =>
      json
      ->asObject
      ->Result.flatMap(fieldset =>
        switch field1->Field.decode(fieldset) {
        | Ok(val1) =>
          switch field2->Field.decode(fieldset) {
          | Ok(val2) =>
            switch field3->Field.decode(fieldset) {
            | Ok(val3) =>
              switch field4->Field.decode(fieldset) {
              | Ok(val4) =>
                switch field5->Field.decode(fieldset) {
                | Ok(val5) =>
                  switch field6->Field.decode(fieldset) {
                  | Ok(val6) =>
                    switch field7->Field.decode(fieldset) {
                    | Ok(val7) =>
                      switch field8->Field.decode(fieldset) {
                      | Ok(val8) =>
                        switch field9->Field.decode(fieldset) {
                        | Ok(val9) =>
                          switch field10->Field.decode(fieldset) {
                          | Ok(val10) =>
                            switch field11->Field.decode(fieldset) {
                            | Ok(val11) =>
                              switch field12->Field.decode(fieldset) {
                              | Ok(val12) =>
                                switch field13->Field.decode(fieldset) {
                                | Ok(val13) =>
                                  switch field14->Field.decode(fieldset) {
                                  | Ok(val14) =>
                                    switch field15->Field.decode(fieldset) {
                                    | Ok(val15) =>
                                      switch field16->Field.decode(fieldset) {
                                      | Ok(val16) =>
                                        switch field17->Field.decode(fieldset) {
                                        | Ok(val17) =>
                                          switch field18->Field.decode(fieldset) {
                                          | Ok(val18) =>
                                            switch field19->Field.decode(fieldset) {
                                            | Ok(val19) =>
                                              switch field20->Field.decode(fieldset) {
                                              | Ok(val20) =>
                                                switch field21->Field.decode(fieldset) {
                                                | Ok(val21) =>
                                                  switch field22->Field.decode(fieldset) {
                                                  | Ok(val22) =>
                                                    construct((
                                                      val1,
                                                      val2,
                                                      val3,
                                                      val4,
                                                      val5,
                                                      val6,
                                                      val7,
                                                      val8,
                                                      val9,
                                                      val10,
                                                      val11,
                                                      val12,
                                                      val13,
                                                      val14,
                                                      val15,
                                                      val16,
                                                      val17,
                                                      val18,
                                                      val19,
                                                      val20,
                                                      val21,
                                                      val22,
                                                    ))
                                                  | Error(_) as err => err
                                                  }
                                                | Error(_) as err => err
                                                }
                                              | Error(_) as err => err
                                              }
                                            | Error(_) as err => err
                                            }
                                          | Error(_) as err => err
                                          }
                                        | Error(_) as err => err
                                        }
                                      | Error(_) as err => err
                                      }
                                    | Error(_) as err => err
                                    }
                                  | Error(_) as err => err
                                  }
                                | Error(_) as err => err
                                }
                              | Error(_) as err => err
                              }
                            | Error(_) as err => err
                            }
                          | Error(_) as err => err
                          }
                        | Error(_) as err => err
                        }
                      | Error(_) as err => err
                      }
                    | Error(_) as err => err
                    }
                  | Error(_) as err => err
                  }
                | Error(_) as err => err
                }
              | Error(_) as err => err
              }
            | Error(_) as err => err
            }
          | Error(_) as err => err
          }
        | Error(_) as err => err
        }
      ),
  )

let object23 = (
  destruct,
  construct,
  field1,
  field2,
  field3,
  field4,
  field5,
  field6,
  field7,
  field8,
  field9,
  field10,
  field11,
  field12,
  field13,
  field14,
  field15,
  field16,
  field17,
  field18,
  field19,
  field20,
  field21,
  field22,
  field23,
) =>
  Codec.make(
    value => {
      let (
        val1,
        val2,
        val3,
        val4,
        val5,
        val6,
        val7,
        val8,
        val9,
        val10,
        val11,
        val12,
        val13,
        val14,
        val15,
        val16,
        val17,
        val18,
        val19,
        val20,
        val21,
        val22,
        val23,
      ) = destruct(value)
      jsonObject([
        Field.encode(field1, val1),
        Field.encode(field2, val2),
        Field.encode(field3, val3),
        Field.encode(field4, val4),
        Field.encode(field5, val5),
        Field.encode(field6, val6),
        Field.encode(field7, val7),
        Field.encode(field8, val8),
        Field.encode(field9, val9),
        Field.encode(field10, val10),
        Field.encode(field11, val11),
        Field.encode(field12, val12),
        Field.encode(field13, val13),
        Field.encode(field14, val14),
        Field.encode(field15, val15),
        Field.encode(field16, val16),
        Field.encode(field17, val17),
        Field.encode(field18, val18),
        Field.encode(field19, val19),
        Field.encode(field20, val20),
        Field.encode(field21, val21),
        Field.encode(field22, val22),
        Field.encode(field23, val23),
      ])
    },
    json =>
      json
      ->asObject
      ->Result.flatMap(fieldset =>
        switch field1->Field.decode(fieldset) {
        | Ok(val1) =>
          switch field2->Field.decode(fieldset) {
          | Ok(val2) =>
            switch field3->Field.decode(fieldset) {
            | Ok(val3) =>
              switch field4->Field.decode(fieldset) {
              | Ok(val4) =>
                switch field5->Field.decode(fieldset) {
                | Ok(val5) =>
                  switch field6->Field.decode(fieldset) {
                  | Ok(val6) =>
                    switch field7->Field.decode(fieldset) {
                    | Ok(val7) =>
                      switch field8->Field.decode(fieldset) {
                      | Ok(val8) =>
                        switch field9->Field.decode(fieldset) {
                        | Ok(val9) =>
                          switch field10->Field.decode(fieldset) {
                          | Ok(val10) =>
                            switch field11->Field.decode(fieldset) {
                            | Ok(val11) =>
                              switch field12->Field.decode(fieldset) {
                              | Ok(val12) =>
                                switch field13->Field.decode(fieldset) {
                                | Ok(val13) =>
                                  switch field14->Field.decode(fieldset) {
                                  | Ok(val14) =>
                                    switch field15->Field.decode(fieldset) {
                                    | Ok(val15) =>
                                      switch field16->Field.decode(fieldset) {
                                      | Ok(val16) =>
                                        switch field17->Field.decode(fieldset) {
                                        | Ok(val17) =>
                                          switch field18->Field.decode(fieldset) {
                                          | Ok(val18) =>
                                            switch field19->Field.decode(fieldset) {
                                            | Ok(val19) =>
                                              switch field20->Field.decode(fieldset) {
                                              | Ok(val20) =>
                                                switch field21->Field.decode(fieldset) {
                                                | Ok(val21) =>
                                                  switch field22->Field.decode(fieldset) {
                                                  | Ok(val22) =>
                                                    switch field23->Field.decode(fieldset) {
                                                    | Ok(val23) =>
                                                      construct((
                                                        val1,
                                                        val2,
                                                        val3,
                                                        val4,
                                                        val5,
                                                        val6,
                                                        val7,
                                                        val8,
                                                        val9,
                                                        val10,
                                                        val11,
                                                        val12,
                                                        val13,
                                                        val14,
                                                        val15,
                                                        val16,
                                                        val17,
                                                        val18,
                                                        val19,
                                                        val20,
                                                        val21,
                                                        val22,
                                                        val23,
                                                      ))
                                                    | Error(_) as err => err
                                                    }
                                                  | Error(_) as err => err
                                                  }
                                                | Error(_) as err => err
                                                }
                                              | Error(_) as err => err
                                              }
                                            | Error(_) as err => err
                                            }
                                          | Error(_) as err => err
                                          }
                                        | Error(_) as err => err
                                        }
                                      | Error(_) as err => err
                                      }
                                    | Error(_) as err => err
                                    }
                                  | Error(_) as err => err
                                  }
                                | Error(_) as err => err
                                }
                              | Error(_) as err => err
                              }
                            | Error(_) as err => err
                            }
                          | Error(_) as err => err
                          }
                        | Error(_) as err => err
                        }
                      | Error(_) as err => err
                      }
                    | Error(_) as err => err
                    }
                  | Error(_) as err => err
                  }
                | Error(_) as err => err
                }
              | Error(_) as err => err
              }
            | Error(_) as err => err
            }
          | Error(_) as err => err
          }
        | Error(_) as err => err
        }
      ),
  )

let object24 = (
  destruct,
  construct,
  field1,
  field2,
  field3,
  field4,
  field5,
  field6,
  field7,
  field8,
  field9,
  field10,
  field11,
  field12,
  field13,
  field14,
  field15,
  field16,
  field17,
  field18,
  field19,
  field20,
  field21,
  field22,
  field23,
  field24,
) =>
  Codec.make(
    value => {
      let (
        val1,
        val2,
        val3,
        val4,
        val5,
        val6,
        val7,
        val8,
        val9,
        val10,
        val11,
        val12,
        val13,
        val14,
        val15,
        val16,
        val17,
        val18,
        val19,
        val20,
        val21,
        val22,
        val23,
        val24,
      ) = destruct(value)
      jsonObject([
        Field.encode(field1, val1),
        Field.encode(field2, val2),
        Field.encode(field3, val3),
        Field.encode(field4, val4),
        Field.encode(field5, val5),
        Field.encode(field6, val6),
        Field.encode(field7, val7),
        Field.encode(field8, val8),
        Field.encode(field9, val9),
        Field.encode(field10, val10),
        Field.encode(field11, val11),
        Field.encode(field12, val12),
        Field.encode(field13, val13),
        Field.encode(field14, val14),
        Field.encode(field15, val15),
        Field.encode(field16, val16),
        Field.encode(field17, val17),
        Field.encode(field18, val18),
        Field.encode(field19, val19),
        Field.encode(field20, val20),
        Field.encode(field21, val21),
        Field.encode(field22, val22),
        Field.encode(field23, val23),
        Field.encode(field24, val24),
      ])
    },
    json =>
      json
      ->asObject
      ->Result.flatMap(fieldset =>
        switch field1->Field.decode(fieldset) {
        | Ok(val1) =>
          switch field2->Field.decode(fieldset) {
          | Ok(val2) =>
            switch field3->Field.decode(fieldset) {
            | Ok(val3) =>
              switch field4->Field.decode(fieldset) {
              | Ok(val4) =>
                switch field5->Field.decode(fieldset) {
                | Ok(val5) =>
                  switch field6->Field.decode(fieldset) {
                  | Ok(val6) =>
                    switch field7->Field.decode(fieldset) {
                    | Ok(val7) =>
                      switch field8->Field.decode(fieldset) {
                      | Ok(val8) =>
                        switch field9->Field.decode(fieldset) {
                        | Ok(val9) =>
                          switch field10->Field.decode(fieldset) {
                          | Ok(val10) =>
                            switch field11->Field.decode(fieldset) {
                            | Ok(val11) =>
                              switch field12->Field.decode(fieldset) {
                              | Ok(val12) =>
                                switch field13->Field.decode(fieldset) {
                                | Ok(val13) =>
                                  switch field14->Field.decode(fieldset) {
                                  | Ok(val14) =>
                                    switch field15->Field.decode(fieldset) {
                                    | Ok(val15) =>
                                      switch field16->Field.decode(fieldset) {
                                      | Ok(val16) =>
                                        switch field17->Field.decode(fieldset) {
                                        | Ok(val17) =>
                                          switch field18->Field.decode(fieldset) {
                                          | Ok(val18) =>
                                            switch field19->Field.decode(fieldset) {
                                            | Ok(val19) =>
                                              switch field20->Field.decode(fieldset) {
                                              | Ok(val20) =>
                                                switch field21->Field.decode(fieldset) {
                                                | Ok(val21) =>
                                                  switch field22->Field.decode(fieldset) {
                                                  | Ok(val22) =>
                                                    switch field23->Field.decode(fieldset) {
                                                    | Ok(val23) =>
                                                      switch field24->Field.decode(fieldset) {
                                                      | Ok(val24) =>
                                                        construct((
                                                          val1,
                                                          val2,
                                                          val3,
                                                          val4,
                                                          val5,
                                                          val6,
                                                          val7,
                                                          val8,
                                                          val9,
                                                          val10,
                                                          val11,
                                                          val12,
                                                          val13,
                                                          val14,
                                                          val15,
                                                          val16,
                                                          val17,
                                                          val18,
                                                          val19,
                                                          val20,
                                                          val21,
                                                          val22,
                                                          val23,
                                                          val24,
                                                        ))
                                                      | Error(_) as err => err
                                                      }
                                                    | Error(_) as err => err
                                                    }
                                                  | Error(_) as err => err
                                                  }
                                                | Error(_) as err => err
                                                }
                                              | Error(_) as err => err
                                              }
                                            | Error(_) as err => err
                                            }
                                          | Error(_) as err => err
                                          }
                                        | Error(_) as err => err
                                        }
                                      | Error(_) as err => err
                                      }
                                    | Error(_) as err => err
                                    }
                                  | Error(_) as err => err
                                  }
                                | Error(_) as err => err
                                }
                              | Error(_) as err => err
                              }
                            | Error(_) as err => err
                            }
                          | Error(_) as err => err
                          }
                        | Error(_) as err => err
                        }
                      | Error(_) as err => err
                      }
                    | Error(_) as err => err
                    }
                  | Error(_) as err => err
                  }
                | Error(_) as err => err
                }
              | Error(_) as err => err
              }
            | Error(_) as err => err
            }
          | Error(_) as err => err
          }
        | Error(_) as err => err
        }
      ),
  )

let object25 = (
  destruct,
  construct,
  field1,
  field2,
  field3,
  field4,
  field5,
  field6,
  field7,
  field8,
  field9,
  field10,
  field11,
  field12,
  field13,
  field14,
  field15,
  field16,
  field17,
  field18,
  field19,
  field20,
  field21,
  field22,
  field23,
  field24,
  field25,
) =>
  Codec.make(
    value => {
      let (
        val1,
        val2,
        val3,
        val4,
        val5,
        val6,
        val7,
        val8,
        val9,
        val10,
        val11,
        val12,
        val13,
        val14,
        val15,
        val16,
        val17,
        val18,
        val19,
        val20,
        val21,
        val22,
        val23,
        val24,
        val25,
      ) = destruct(value)
      jsonObject([
        Field.encode(field1, val1),
        Field.encode(field2, val2),
        Field.encode(field3, val3),
        Field.encode(field4, val4),
        Field.encode(field5, val5),
        Field.encode(field6, val6),
        Field.encode(field7, val7),
        Field.encode(field8, val8),
        Field.encode(field9, val9),
        Field.encode(field10, val10),
        Field.encode(field11, val11),
        Field.encode(field12, val12),
        Field.encode(field13, val13),
        Field.encode(field14, val14),
        Field.encode(field15, val15),
        Field.encode(field16, val16),
        Field.encode(field17, val17),
        Field.encode(field18, val18),
        Field.encode(field19, val19),
        Field.encode(field20, val20),
        Field.encode(field21, val21),
        Field.encode(field22, val22),
        Field.encode(field23, val23),
        Field.encode(field24, val24),
        Field.encode(field25, val25),
      ])
    },
    json =>
      json
      ->asObject
      ->Result.flatMap(fieldset =>
        switch field1->Field.decode(fieldset) {
        | Ok(val1) =>
          switch field2->Field.decode(fieldset) {
          | Ok(val2) =>
            switch field3->Field.decode(fieldset) {
            | Ok(val3) =>
              switch field4->Field.decode(fieldset) {
              | Ok(val4) =>
                switch field5->Field.decode(fieldset) {
                | Ok(val5) =>
                  switch field6->Field.decode(fieldset) {
                  | Ok(val6) =>
                    switch field7->Field.decode(fieldset) {
                    | Ok(val7) =>
                      switch field8->Field.decode(fieldset) {
                      | Ok(val8) =>
                        switch field9->Field.decode(fieldset) {
                        | Ok(val9) =>
                          switch field10->Field.decode(fieldset) {
                          | Ok(val10) =>
                            switch field11->Field.decode(fieldset) {
                            | Ok(val11) =>
                              switch field12->Field.decode(fieldset) {
                              | Ok(val12) =>
                                switch field13->Field.decode(fieldset) {
                                | Ok(val13) =>
                                  switch field14->Field.decode(fieldset) {
                                  | Ok(val14) =>
                                    switch field15->Field.decode(fieldset) {
                                    | Ok(val15) =>
                                      switch field16->Field.decode(fieldset) {
                                      | Ok(val16) =>
                                        switch field17->Field.decode(fieldset) {
                                        | Ok(val17) =>
                                          switch field18->Field.decode(fieldset) {
                                          | Ok(val18) =>
                                            switch field19->Field.decode(fieldset) {
                                            | Ok(val19) =>
                                              switch field20->Field.decode(fieldset) {
                                              | Ok(val20) =>
                                                switch field21->Field.decode(fieldset) {
                                                | Ok(val21) =>
                                                  switch field22->Field.decode(fieldset) {
                                                  | Ok(val22) =>
                                                    switch field23->Field.decode(fieldset) {
                                                    | Ok(val23) =>
                                                      switch field24->Field.decode(fieldset) {
                                                      | Ok(val24) =>
                                                        switch field25->Field.decode(fieldset) {
                                                        | Ok(val25) =>
                                                          construct((
                                                            val1,
                                                            val2,
                                                            val3,
                                                            val4,
                                                            val5,
                                                            val6,
                                                            val7,
                                                            val8,
                                                            val9,
                                                            val10,
                                                            val11,
                                                            val12,
                                                            val13,
                                                            val14,
                                                            val15,
                                                            val16,
                                                            val17,
                                                            val18,
                                                            val19,
                                                            val20,
                                                            val21,
                                                            val22,
                                                            val23,
                                                            val24,
                                                            val25,
                                                          ))
                                                        | Error(_) as err => err
                                                        }
                                                      | Error(_) as err => err
                                                      }
                                                    | Error(_) as err => err
                                                    }
                                                  | Error(_) as err => err
                                                  }
                                                | Error(_) as err => err
                                                }
                                              | Error(_) as err => err
                                              }
                                            | Error(_) as err => err
                                            }
                                          | Error(_) as err => err
                                          }
                                        | Error(_) as err => err
                                        }
                                      | Error(_) as err => err
                                      }
                                    | Error(_) as err => err
                                    }
                                  | Error(_) as err => err
                                  }
                                | Error(_) as err => err
                                }
                              | Error(_) as err => err
                              }
                            | Error(_) as err => err
                            }
                          | Error(_) as err => err
                          }
                        | Error(_) as err => err
                        }
                      | Error(_) as err => err
                      }
                    | Error(_) as err => err
                    }
                  | Error(_) as err => err
                  }
                | Error(_) as err => err
                }
              | Error(_) as err => err
              }
            | Error(_) as err => err
            }
          | Error(_) as err => err
          }
        | Error(_) as err => err
        }
      ),
  )
