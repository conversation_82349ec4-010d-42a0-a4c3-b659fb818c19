@genType.as("FileType")
type fileType = [#csv | #excel | #spreadsheetml | #spreadsheetopen]

type error =
  | FileNotFound
  | FileTooHeavy
  | FileBadExtension(array<fileType>)

let isFileBadType: (File.t, ~types: array<fileType>) => bool
let isFileBadExtension: (File.t, ~types: array<fileType>) => bool
let messageFromError: error => string
let typeFromMime: string => result<fileType, unit>
let extensionFromType: fileType => string

@react.component
let make: (
  ~children: React.element,
  ~types: array<fileType>=?,
  ~maxSizeMb: float=?,
  ~disabled: bool=?,
  ~onChange: File.t => unit,
  ~onError: string => unit,
) => React.element
