type dispatch<'action> = 'action => unit
type rec update<'state, 'action> =
  | NoUpdate
  | Update('state)
  | UpdateWithSideEffects('state, self<'state, 'action> => option<unit => unit>)
  | SideEffects(self<'state, 'action> => option<unit => unit>)
and self<'state, 'action> = {dispatch: dispatch<'action>, state: 'state}

type reducer<'state, 'action> = ('state, 'action) => update<'state, 'action>

/**
 The ReactUpdateReducer.use hook was inspired by the original/pre-hooks
 ReasonReact record API, and the hooks-based rescript-react-update library
 by <PERSON> (bloodyowl).
 ReactUpdateReducer.use is similar to the React useReducer hook with the key difference
 that the React useReducer only allows you to change the state,
 whereas the ReactUpdateReducer.use allows you to both change the state, and
 to safely emit side effects which can result in the emission of further actions.

 ```rescript
 type state = int
 type action =
   | Increment
   | Decrement
 
 @react.component
 let make = () => {
   let (state, send) =
     ReactUpdateReducer.use((state, action) =>
       switch (action) {
       | Increment => Update(state + 1)
       | Decrement => Update(state - 1)
       },
       0
     )
   <div>
     {state->React.int}
     <button onClick={_ => send(Decrement)}> {"—"->React.string} </button>
     <button onClick={_ => send(Increment)}> {"+"->React.string} </button>
   </div>
 }
 ```
 */
let use: (('state, 'action) => update<'state, 'action>, 'state) => ('state, dispatch<'action>)

let useWithMapState: (
  ('state, 'action) => update<'state, 'action>,
  unit => 'state,
) => ('state, dispatch<'action>)
