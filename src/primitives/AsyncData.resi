/** 
  * AsyncData contains an variant type t('a) for representing
  * the different states in which a value can be during
  * an asynchronous data load. Because the type only has
  * a single type parameter 'a, it cannot represent failures
  * by default, but you can choose to use any type for 'a,
  * including option('b) (for data that may or may not load),
  * result('b, 'e) (for data that can fail to load), etc.
  * If you need to represent an error condition along
  * with the success condition, see AsyncResult.
  *
  * Inspiration from https://reazen.github.io/relude/api/Relude_AsyncData/
  */
@genType
type t<'a> = NotAsked | Loading | Reloading('a) | Done('a)

@genType let notAsked: unit => t<'a>
@genType let loading: unit => t<'a>
@genType let reloading: 'a => t<'a>
@genType let done: 'a => t<'a>

let isReloading: t<'a> => bool
let isNotAsked: t<'a> => bool
let isBusy: t<'a> => bool
let isIdle: t<'a> => bool

let toBusy: t<'a> => t<'a>

let map: (t<'a>, 'a => 'b) => t<'b>
let flatMap: (t<'a>, 'a => t<'b>) => t<'b>
let mapWithDefault: (t<'a>, 'b, 'a => 'b) => 'b
let getWithDefault: (t<'a>, 'a) => 'a
