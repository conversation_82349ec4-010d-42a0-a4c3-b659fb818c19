import '../public/reset.css'
import '../public/fonts.css'
import '../public/animations.css'
import '../public/style.css'
import '../public/react-dates.css'

import 'react-dates/initialize'
import 'react-dates/lib/css/_datepicker.css'

import React from 'react'

// FIXME - cannot resolve: import { Providers } from 'src/core/Providers'
import { BrowserRouter } from 'react-router-dom'

const Canvas = ({ children }) => (
  <div style={{ display: 'flex' }}>
    {children} 
    <div id="portals-modal" />
  </div>
)

export const decorators = [
  (Story) => (
    <BrowserRouter>
      <Canvas>
        <Story />
      </Canvas>
    </BrowserRouter>
  ),
]
