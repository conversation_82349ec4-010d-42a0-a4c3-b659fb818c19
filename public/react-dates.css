/* react-dates assumes that box-sizing: border-box is set globally in your page's CSS. At least do this, to make it display properly. */
.RangeDatePicker *,
.SingleDatePicker *,
.DayPickerRangeController * {
  box-sizing: border-box;
}

/* react-dates needs a custom theme */
.CalendarMonth_table.CalendarMonth_table_1 {
  margin-top: 12px;
}

.CalendarDay__selected,
.CalendarDay__selected:hover,
.CalendarDay__selected:active {
  background: #a01e72 !important;
  border: 1px double #a01e72 !important;
  color: #fff !important;
}

.CalendarDay__selected_span {
  background: #8a0262 !important;
  border: 1px double #a01e72 !important;
}

.CalendarDay__selected_span:hover {
  background: #a01e72 !important;
}

.CalendarDay__hovered_span {
  background: #f4e4ee !important;
  border: 1px double #c04695 !important;
  color: #c04695 !important;
}

.CalendarDay__selected_span.CalendarDay__blocked_out_of_range {
  color: #dcadcb !important;
}

.CalendarDay__blocked_out_of_range.CalendarDay__selected_end {
  color: #dcadcb !important;
}

.CalendarDay__selected_span.CalendarDay__blocked_out_of_range:hover {
  background: #8a0262 !important;
}

.DayPickerKeyboardShortcuts_show__bottomRight {
  display: none;
}

.DayPickerKeyboardShortcuts_show__bottomRight::before {
  border-right: 33px solid #a01e72 !important;
}
