@layer base {
  span {
    word-break: break-word;
  }
  
  div {
    box-sizing: border-box;
  }
}

.focus-visible-ring::after,
div[role='radio'][data-focusvisible-polyfill='true']::after,
div[role='tab'][data-focusvisible-polyfill='true']::after,
*[role='button'][data-focusvisible-polyfill='true']::after,
button[data-focusvisible-polyfill='true']::after,
tr[data-focusvisible-polyfill='true']::after {
  content: "";
  position: absolute;
  top: 3px;
  left: 3px;
  right: 3px;
  bottom: 3px;
  border: 1px solid #c04695; /* Colors.brandColor40 */
  border-radius: 4px;
}

td[tabindex='0'] {
  z-index: 1 !important;
}

thead {
  scrollbar-width: none;
}
thead::-webkit-scrollbar {
  display: none;
  overflow-x: scroll;
}

div[aria-label='track']::-webkit-scrollbar {
  display: none;
}

::placeholder {
  color: #bdbdca; /* Colors.neutralColor25 */
  opacity: 1;
}

body {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
  font-family: 'Libre Franklin';
  background-color: #f3f3f7; /* Colors.neutralColor10 */
}

.enabled-scroll {
  overflow: auto;
}

.disabled-scroll {
  overflow: hidden;
}

/* Force cursor on popover/modal backdrop to default */
#portals-root > [data-focusable='true'] {
  cursor: default !important;
}

[role='listbox'] > div {
  scrollbar-width: thin;
}

img {
  object-fit: cover;
}

div:has(> table) {
  min-width: 0px;
}
