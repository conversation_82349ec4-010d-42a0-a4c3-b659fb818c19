@font-face {
  font-family: 'Libre Franklin';
  font-weight: 400;
  src: url(fonts/LibreFranklin-Regular.ttf) format('truetype');
}

@font-face {
  font-family: 'Libre Franklin';
  font-weight: 500;
  src: url(fonts/LibreFranklin-Medium.ttf) format('truetype');
}

@font-face {
  font-family: 'Libre Franklin';
  font-weight: 600;
  src: url(fonts/LibreFranklin-SemiBold.ttf) format('truetype');
}

@font-face {
  font-family: 'Libre Franklin';
  font-weight: 700;
  src: url(fonts/LibreFranklin-Bold.ttf) format('truetype');
}

@font-face {
  font-family: 'Archivo';
  font-weight: 400;
  src: url(fonts/Archivo-Regular.ttf) format('truetype');
}

@font-face {
  font-family: 'Archivo';
  font-weight: 500;
  src: url(fonts/Archivo-Medium.ttf) format('truetype');
}

@font-face {
  font-family: 'Archivo';
  font-weight: 600;
  src: url(fonts/Archivo-SemiBold.ttf) format('truetype');
}

@font-face {
  font-family: 'Archivo';
  font-weight: 700;
  src: url(fonts/Archivo-Bold.ttf) format('truetype');
}
