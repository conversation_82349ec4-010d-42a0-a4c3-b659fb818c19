@layer base {
  * {
    outline: none;
    margin: 0;
    padding: 0;
  }

  button {
    -webkit-appearance: none;
    border-radius: 0;
    border: 0;
    text-align: inherit;
    background: none;
    box-shadow: none;
    padding: 0;
    cursor: pointer;
    color: inherit;
    font: inherit;
  }

  a:link,
  a:link:hover {
    text-decoration: none;
  }
}

:active,
:focus,
:visited {
  color: none;
}

html {
  font-size: 1rem;
}



ul, li {
  list-style-type: none;
}

td, th, tr {
  padding: 0
}

textarea:focus {
  outline: none;
}

input {
  padding: 0;
}

input:focus {
  outline: none;
}

/* Chrome, Safari, Edge, Opera */
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

/* Firefox */
input[type='number'] {
  -moz-appearance: textfield;
  appearance: textfield;
}

input:-webkit-autofill,
input:-webkit-autofill:hover,
input:-webkit-autofill:focus textarea:-webkit-autofill,
textarea:-webkit-autofill:hover textarea:-webkit-autofill:focus,
select:-webkit-autofill,
select:-webkit-autofill:hover,
select:-webkit-autofill:focus {
  box-shadow: 0 0 0px 1000px #ffffff inset !important;
}

input:-webkit-autofill::first-line {
  font-size: 16px;
}

h1, h2, h3, h4, h5, h6 {
  display: inline;
}
