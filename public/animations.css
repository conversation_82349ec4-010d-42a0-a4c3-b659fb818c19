/* NOTE - update accordingly: src/primitives/AnimatedRender.res */

@keyframes fadeTranslation {
  0% {
    pointer-events: none;
    overflow: hidden;
    opacity: 0;
    transform:  translate3d(0, -.75rem, 0);
  }
  100% {
    opacity: 1;
  }
}

@keyframes fadeBubbleTranslation {
  0% {
    pointer-events: none;
    overflow: hidden;
    opacity: 0;
    transform:  translate3d(0, -.75rem, 0) scale3d(0.95, 0.95, 0.95);
  }
  70% {
    transform: scale3d(1.007, 1.01, 1.0);
  }
  100% {
    opacity: 1;
    transform: scale3d(1, 1, 1);
  }
}

@keyframes fadePopinTranslation {
  0% {
    pointer-events: none;
    overflow: hidden;
    opacity: 0;
    transform:  translate3d(0, -.75rem, 0) scale3d(0.95, 0.95, 0.95);
  }
  100% {
    opacity: 1;
    transform: scale3d(1, 1, 1);
  }
}

@keyframes fadeIn {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

@keyframes fadeOut {
  0% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to   { transform: rotate(360deg); }
}
