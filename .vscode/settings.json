{"files.exclude": {"**/*.bs.js": true, "**/*.gen.tsx": true}, "files.insertFinalNewline": true, "editor.formatOnSave": true, "editor.insertSpaces": true, "editor.tabSize": 2, "emmet.triggerExpansionOnTab": true, "emmet.includeLanguages": {"javascript": "javascriptreact", "rescript": "javascriptreact", "json": "json"}, "[rescript]": {"editor.defaultFormatter": "chenglou92.rescript-vscode"}, "explorer.fileNesting.enabled": true, "explorer.fileNesting.expand": false, "explorer.fileNesting.patterns": {"*.resi": "${capture}.res, ${capture}.tsx, ${capture}.ts,, ${capture}*.png"}, "typescript.tsdk": "node_modules/typescript/lib"}