open Vitest
open TestingLibraryReact

it("should not change displayName", () => {
  expect(Touchable.make->React.displayName)->toMatchSnapshot
})

it("should render a visible element", () => {
  render(
    <Touchable onPress={_ => ()}>
      <span> {"my button"->React.string} </span>
    </Touchable>,
  )->ignore

  let touchableElement = screen->getByRoleWithOptionsExn(#button, {name: "my button"})

  expect(touchableElement)->toBeVisible
})

it("should match snapshot with disable prop set to false", () => {
  let {baseElement} = render(
    <Touchable onPress={_ => ()} disabled={false}>
      <span> {"my button"->React.string} </span>
    </Touchable>,
  )

  expect(baseElement)->toMatchSnapshot
})

it("should match snapshot with disable prop set to true", () => {
  let {baseElement} = render(
    <Touchable onPress={_ => ()} disabled={true}>
      <span> {"my button"->React.string} </span>
    </Touchable>,
  )

  expect(baseElement)->toMatchSnapshot
})

itPromise("should call onPress prop", async () => {
  let userEvent = TestingLibraryEvent.setup()
  let onPress = fn1(ignore)

  let {rerender} = render(
    <Touchable onPress={onPress->fn}>
      <span> {"my button"->React.string} </span>
    </Touchable>,
  )

  let element = screen->getByRoleWithOptionsExn(#button, {name: "my button"})

  expect(element)->toBeVisible
  expect(onPress)->toHaveBeenCalledTimes(0)

  await userEvent->TestingLibraryEvent.click(element)

  expect(element)->toBeVisible
  expect(onPress)->toHaveBeenCalledTimes(1)

  rerender(
    <Touchable onPress={onPress->fn} disabled={true}>
      <span> {"my button"->React.string} </span>
    </Touchable>,
  )->ignore

  let element = screen->getByRoleWithOptionsExn(#button, {name: "my button"})

  await userEvent->TestingLibraryEvent.click(element)

  expect(element)->toBeVisible
  expect(onPress)->toHaveBeenCalledTimes(1)
})
