open Vitest

// NOTE - this spec covers only basic tests from the ljharb/qs lib bindings
// see: https://github.com/ljharb/qs/tree/main/test

let {fromString, toString, stringifyEncoder, parse, stringify, parseDecoder} = module(QueryString)

test("fromString/toString", () => {
  let inputString = "some query string"
  let inputQueryString = "some query string"->fromString
  expect(inputString->fromString)->toBe(inputString->fromString)
  expect(inputQueryString->toString)->toBe(inputString)
})

describe("stringifyEncoder", () => {
  it("should return the stringified primitive value from input", () => {
    expect(stringifyEncoder(true->Json.encodeBoolean))->toBe("true")
    expect(stringifyEncoder(false->Json.encodeBoolean))->toBe("false")

    expect(stringifyEncoder("my encoded str"->Json.encodeString))->toBe("%22my%20encoded%20str%22")
    expect(stringifyEncoder(""->Json.encodeString))->toBe("%22%22")
    expect(stringifyEncoder("\"\""->Json.encodeString))->toBe("%22%5C%22%5C%22%22")
    expect(stringifyEncoder("123"->Json.encodeString))->toBe("%22123%22")
    expect(stringifyEncoder("false"->Json.encodeString))->toBe("%22false%22")
    expect(stringifyEncoder("null"->Json.encodeString))->toBe("%22null%22")
    expect(stringifyEncoder("undefined"->Json.encodeString))->toBe("%22undefined%22")

    expect(stringifyEncoder(123.->Json.encodeNumber))->toBe("123")
    expect(stringifyEncoder(-123.->Json.encodeNumber))->toBe("-123")
    expect(stringifyEncoder(123.456->Json.encodeNumber))->toBe("123.456")
    expect(stringifyEncoder(-123.456->Json.encodeNumber))->toBe("-123.456")
    expect(stringifyEncoder(0.->Json.encodeNumber))->toBe("0")

    // NOTE - inputs virtually never passed by `stringify` to this encoder
    expect(stringifyEncoder(Obj.magic(None)))->toBe("")
    expect(stringifyEncoder(Json.encodedNull))->toBe("null")
  })
})

describe("stringify", () => {
  it("should turn into a query string a simple object", () => {
    let json = Obj.magic({"data": "test"})->Json.encodeDict
    expect(stringify(json))->toUnsafeStrictEqual("data=%22test%22")

    let json = Obj.magic({"data": 2})->Json.encodeDict
    expect(stringify(json))->toUnsafeStrictEqual("data=2")

    let json = Obj.magic({"data": Js.Nullable.null})->Json.encodeDict
    expect(stringify(json))->toUnsafeStrictEqual("data=%22%22")
  })

  it("should turn into a query string an object with multiple properties", () => {
    let json = Obj.magic({"color": "purple", "fruit": "pear"})->Json.encodeDict
    expect(stringify(json))->toUnsafeStrictEqual("color=%22purple%22&fruit=%22pear%22")
  })

  it("should turn into a query string an object with nested values", () => {
    let json =
      Obj.magic({"data": {"fruits": ["pineapple", "blueberry"]}, "count": 2})->Json.encodeDict
    expect(stringify(json))->toUnsafeStrictEqual(
      "data[fruits][0]=%22pineapple%22&data[fruits][1]=%22blueberry%22&count=2",
    )
  })

  it("should turn into a query string an object with undefined/nested values", () => {
    let json = Obj.magic({"data": {"a": None, "b": None}})->Json.encodeDict
    expect(stringify(json))->toUnsafeStrictEqual("")
  })
})

describe("parseDecoder", () => {
  let parseDecoder = value => parseDecoder(value, (), (), ~type_=#value)

  it("should return a primitive value for the input string", () => {
    expect(parseDecoder("true"))->toBe(true->Json.encodeBoolean)
    expect(parseDecoder("false"))->toBe(false->Json.encodeBoolean)

    expect(parseDecoder("null"))->toBe(Json.encodedNull)
    expect(parseDecoder(" undefined null "))->toBe(" undefined null "->Json.encodeString)

    expect(parseDecoder("123"))->toBe(123.->Json.encodeNumber)
    expect(parseDecoder("-123"))->toBe(-123.->Json.encodeNumber)
    expect(parseDecoder("123.456"))->toBe(123.456->Json.encodeNumber)
    expect(parseDecoder("-123.456"))->toBe(-123.456->Json.encodeNumber)
    expect(parseDecoder("00"))->toBe(0.->Json.encodeNumber)

    expect(parseDecoder(" 0"))->toBe(" 0"->Json.encodeString)
    expect(parseDecoder("+123"))->toBe("+123"->Json.encodeString)
    expect(parseDecoder("--123"))->toBe("--123"->Json.encodeString)

    expect(parseDecoder("%22string%20with%20spaces%22"))->toBe(
      "string with spaces"->Json.encodeString,
    )
    expect(parseDecoder("%22string%20%5C%22with%5C%22%20spaces%22"))->toBe(
      "string \"with\" spaces"->Json.encodeString,
    )

    // NOTE - inputs virtually never passed by `parse` to this decoder
    expect(parseDecoder(""))->toUnsafeStrictEqual(None)
    expect(parseDecoder("undefined"))->toUnsafeStrictEqual(None)
  })
})

describe("parse", () => {
  it("should parse a query string into its simple object", () => {
    let queryString = "data=test"->fromString
    expect(parse(queryString))->toUnsafeStrictEqual({"data": "test"})

    let queryString = "?data=test"->fromString
    expect(parse(queryString))->toUnsafeStrictEqual({"data": "test"})

    let queryString = "?data=%22test%22"->fromString
    expect(parse(queryString))->toUnsafeStrictEqual({"data": "test"})

    let queryString = "?data="->fromString
    expect(parse(queryString))->toUnsafeStrictEqual({"data": None})

    let queryString = "?data=%22%22"->fromString
    expect(parse(queryString))->toUnsafeStrictEqual({"data": ""})

    let queryString = "?data=null"->fromString
    expect(parse(queryString))->toUnsafeStrictEqual({"data": Js.Nullable.null})

    let queryString = "?data=undefined"->fromString
    expect(parse(queryString))->toUnsafeStrictEqual({"data": None})

    let queryString = "?data=1"->fromString
    expect(parse(queryString))->toUnsafeStrictEqual({"data": 1})
  })

  it("should parse a query string into its multiple properties object", () => {
    let queryString = "color=purple&fruit=pear"->fromString
    expect(parse(queryString))->toUnsafeStrictEqual({"color": "purple", "fruit": "pear"})
  })

  it("should parse a query string into its object with nested values", () => {
    let queryString =
      "data[fruits][0]=%22pineapple%22&data[fruits][1]=%22blueberry%22&count=2"->fromString
    expect(parse(queryString))->toUnsafeStrictEqual({
      "data": {"fruits": ["pineapple", "blueberry"]},
      "count": 2,
    })
  })

  it("should parse a query string into its object with nested/undefined values", () => {
    let queryString = "data[a]=&data[b]="->fromString
    expect(parse(queryString))->toUnsafeStrictEqual({"data": {"a": None, "b": None}})
  })
})
