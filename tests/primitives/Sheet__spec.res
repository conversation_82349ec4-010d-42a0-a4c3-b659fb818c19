open Vitest

let isBlobInstance: 'a => bool = %raw(`function(x) { return x instanceof Blob }`)

describe("makeCsvPlainText", () => {
  it("should return csv plain text with basic data", () => {
    let data = [["A_1", "B_1", "C_1"], ["A_2", "B_2", "C_2"], ["A_3", "B_3", "C_3"]]
    expect(data->Sheet.makeCsvPlainText)->toStrictEqual(
      Ok(`A_1;B_1;C_1
A_2;B_2;C_2
A_3;B_3;C_3
`),
    )
  })

  it("should return csv plain text with some blank values", () => {
    let data = [["A_1", "B_1"], ["A_2", "B_2", ""], ["", "B_3", "C_3"]]
    expect(data->Sheet.makeCsvPlainText)->toStrictEqual(
      Ok(`A_1;B_1;
A_2;B_2;
;B_3;C_3
`),
    )
  })

  it("should return csv plain text without consistent rows and with line spaces", _ => {
    let data = [["A_1"], [], ["A_3", "B_3", "C_3"], ["A_4", "B_4", "C_4"], [], ["A_6", "B_6"]]
    expect(data->Sheet.makeCsvPlainText)->toStrictEqual(
      Ok(`A_1;;
;;
A_3;B_3;C_3
A_4;B_4;C_4
;;
A_6;B_6;
`),
    )
  })

  it("should return csv plain text with comma delimiter", _ => {
    let data = [["A_1", "B_1", "C_1"], ["A_2", "B_2", "C_2"], ["A_3", "B_3", "C_3"]]
    expect(data->Sheet.makeCsvPlainText(~delimiter=","))->toStrictEqual(
      Ok(`A_1,B_1,C_1
A_2,B_2,C_2
A_3,B_3,C_3
`),
    )
  })

  it("should return csv plain text with empty data", _ => {
    let data = []
    expect(data->Sheet.makeCsvPlainText(~delimiter=","))->toStrictEqual(
      Ok(`
`),
    )
  })

  it("should return error with wtf data", _ => {
    let data = Obj.magic(-1)
    expect(data->Sheet.makeCsvPlainText(~delimiter=","))->toStrictEqual(Error())
  })
})

describe("csvFileExtension", () => {
  it("should match snapshot", () => {
    expect(Sheet.csvFileExtension)->toMatchSnapshot
  })
})

describe("excelFileExtension", () => {
  it("should match snapshot", () => {
    expect(Sheet.excelFileExtension)->toMatchSnapshot
  })
})

describe("makeCsvBlobFromPlainText", () => {
  let blob = "ABC"->Sheet.makeCsvBlobFromPlainText

  it("should return Blob instance", () => {
    expect(isBlobInstance(blob))->toBe(true)
  })

  it("should return csv mime type", () => {
    expect(Blob.type_(blob))->toBe("text/csv;charset=utf-8")
  })
})

describe("makeExcelBlob", () => {
  let blob = [["", ""], []]->Sheet.makeExcelBlob(~worksheetName="")

  it("should return a pending future", () => expect(blob->Future.isPending)->toBe(true))

  itFuture("should resolve Blob instance with excel mime type", () =>
    blob->Future.tap(
      result => {
        let blob = result->Result.getExn
        expect(Blob.type_(blob))->toBe(
          "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        )
        expect(isBlobInstance(blob))->toBe(true)
      },
    )
  )

  itFuture("should resolve Blob instance with empty row", () =>
    []
    ->Sheet.makeExcelBlob(~worksheetName="")
    ->Future.tap(
      result => {
        let blob = result->Result.getExn
        expect(isBlobInstance(blob))->toBe(true)
      },
    )
  )

  itFuture("should resolve Blob instance with wtf rows", () =>
    Obj.magic(() => ())
    ->Sheet.makeExcelBlob(~worksheetName="")
    ->Future.tap(
      result => {
        let blob = result->Result.getExn
        expect(isBlobInstance(blob))->toBe(true)
      },
    )
  )
})

describe("parseExceljsXlsxFile", () => {
  let excelXlsxFileFromUnsafeObj = unsafeObj =>
    Obj.magic(unsafeObj)->Sheet.makeExcelBlob(~worksheetName="")->Future.mapOk(File.fromBlob)

  itFuture("should parse", () => {
    let excelXlsxFile = excelXlsxFileFromUnsafeObj(
      %raw(`[
        ["id", "value"],
        ["id-0", 3.5],
        ["id-1", "z"],
        ["id-2", undefined],
      ]`),
    )

    excelXlsxFile
    ->Future.flatMapOk(xlsxFile => Sheet.parseExceljsXlsxFile(xlsxFile))
    ->Future.tap(
      result => {
        expect(result)->toStrictEqual(
          Ok([
            Js.Dict.fromArray([
              ("id", Json.encodeString("id-0")),
              ("value", Json.encodeNumber(3.5)),
            ])->Json.encodeDict,
            Js.Dict.fromArray([
              ("id", Json.encodeString("id-1")),
              ("value", Json.encodeString("z")),
            ])->Json.encodeDict,
            Js.Dict.fromArray([
              ("id", Json.encodeString("id-2")),
              ("value", Json.encodedNull),
            ])->Json.encodeDict,
          ]),
        )
      },
    )
  })

  itFuture("should parse with headers row index", () => {
    let excelXlsxFile = excelXlsxFileFromUnsafeObj(
      %raw(`[
        ["my headers:"],
        ["id", "value"],
        ["id-0", "3.5"],
      ]`),
    )

    excelXlsxFile
    ->Future.flatMapOk(xlsx => Sheet.parseExceljsXlsxFile(xlsx, ~headerIndexOffset=2))
    ->Future.tap(
      result =>
        expect(result)->toUnsafeStrictEqual(
          Ok([
            {
              "id": "id-0",
              "value": "3.5",
            },
          ]),
        ),
    )
  })

  itFuture("should parse multiple rows with headers row index and ignore extra columns", () => {
    let excelXlsxFile = excelXlsxFileFromUnsafeObj(
      %raw(`[
        ["my headers:"],
        ["id", "value"],
        ["id-0", 3.5],
        ["id-1", 7, "misc"],
        ["id-2", "6,980"],
      ]`),
    )

    excelXlsxFile
    ->Future.flatMapOk(xlsx => Sheet.parseExceljsXlsxFile(xlsx, ~headerIndexOffset=2))
    ->Future.tap(
      result =>
        expect(result)->toUnsafeStrictEqual(
          Ok([
            {
              "id": "id-0",
              "value": 3.5->Json.encodeNumber,
            },
            {
              "id": "id-1",
              "value": 7.->Json.encodeNumber,
            },
            {
              "id": "id-2",
              "value": "6,980"->Json.encodeString,
            },
          ]),
        ),
    )
  })

  itFuture("should parse with result from formula", () => {
    let excelXlsxFile = excelXlsxFileFromUnsafeObj(
      %raw(`[
        ["id", "value"],
        ["id-0", { result: 4., formula: "=MIN(6;4;9)" }],
        ["id-1", { result: 3., formula: "a" }],
        ["id-2", { result: 2., formula: "" }],
        ["id-3", { result: 1. }],
      ]`),
    )

    excelXlsxFile
    ->Future.flatMapOk(xlsxFile => Sheet.parseExceljsXlsxFile(xlsxFile))
    ->Future.tap(
      result => {
        expect(result)->toStrictEqual(
          Ok([
            Js.Dict.fromArray([
              ("id", Json.encodeString("id-0")),
              ("value", Json.encodeNumber(4.)),
            ])->Json.encodeDict,
            Js.Dict.fromArray([
              ("id", Json.encodeString("id-1")),
              ("value", Json.encodeNumber(3.)),
            ])->Json.encodeDict,
            Js.Dict.fromArray([
              ("id", Json.encodeString("id-2")),
              ("value", Json.encodeString("{\"result\":2,\"formula\":\"\"}")),
            ])->Json.encodeDict,
            Js.Dict.fromArray([
              ("id", Json.encodeString("id-3")),
              ("value", Json.encodeString("{\"result\":1}")),
            ])->Json.encodeDict,
          ]),
        )
      },
    )
  })

  itFuture("should parse rows with empty cells", () => {
    let excelXlsxFile = excelXlsxFileFromUnsafeObj(
      %raw(`[
        ["id", "value"],
        ["id-0"],
        ["", "a"],
        ["", ""],
        ["id-1", "0011000111111100110010110000110001111111001100101100"],
      ]`),
    )

    excelXlsxFile
    ->Future.flatMapOk(xlsx => Sheet.parseExceljsXlsxFile(xlsx))
    ->Future.tap(
      result =>
        expect(result)->toUnsafeStrictEqual(
          Ok([
            {
              "id": "id-0",
              "value": Js.Nullable.null,
            },
            {
              "id": "",
              "value": "a"->Js.Nullable.return,
            },
            {
              "id": "",
              "value": ""->Js.Nullable.return,
            },
            {
              "id": "id-1",
              "value": "0011000111111100110010110000110001111111001100101100"->Js.Nullable.return,
            },
          ]),
        ),
    )
  })

  itFuture("should ignore numeric or empty headers", () => {
    let excelXlsxFile = excelXlsxFileFromUnsafeObj(
      %raw(`[
        ["", 84.1, "value"],
        ["id-0", 42, "ok"],
      ]`),
    )

    excelXlsxFile
    ->Future.flatMapOk(xlsx => Sheet.parseExceljsXlsxFile(xlsx))
    ->Future.tap(result => expect(result)->toUnsafeStrictEqual(Ok([{"value": "ok"}])))
  })

  itFuture("should override previous identical headers names", () => {
    let excelXlsxFile = excelXlsxFileFromUnsafeObj(
      %raw(`[
        ["id", "id"],
        ["original-id-0", "overrided-id-0"],
      ]`),
    )

    excelXlsxFile
    ->Future.flatMapOk(xlsx => Sheet.parseExceljsXlsxFile(xlsx))
    ->Future.tap(
      result =>
        expect(result)->toUnsafeStrictEqual(
          Ok([
            {
              "id": "overrided-id-0",
            },
          ]),
        ),
    )
  })

  itFuture("should parse without any row values", () => {
    let excelXlsxFile = excelXlsxFileFromUnsafeObj(
      %raw(`[
        ["id", "value"]
      ]`),
    )

    excelXlsxFile
    ->Future.flatMapOk(xlsx => Sheet.parseExceljsXlsxFile(xlsx))
    ->Future.tap(result => expect(result)->toUnsafeStrictEqual(Ok([])))
  })

  itFuture("should parse empty file", () => {
    let excelXlsxFile = excelXlsxFileFromUnsafeObj([[]])

    excelXlsxFile
    ->Future.flatMapOk(xlsx => Sheet.parseExceljsXlsxFile(xlsx))
    ->Future.tap(result => expect(result)->toUnsafeStrictEqual(Ok([])))
  })
})

describe("parseCsv", () => {
  itFuture("should parse with one column", () => {
    let csvFile = Obj.magic(`
foo
bar
`)
    Sheet.parseCsvFile(csvFile)->Future.tap(
      value =>
        expect(value)->toStrictEqual(Ok([Json.fromObjExn(["foo"]), Json.fromObjExn(["bar"])])),
    )
  })

  itFuture("should parse with multiple columns", () => {
    let csvFile = Obj.magic(`
foo;2;
bar;3
`)
    Sheet.parseCsvFile(csvFile)->Future.tap(
      value =>
        expect(value)->toStrictEqual(
          Ok([Json.fromObjExn(["foo", "2", ""]), Json.fromObjExn(["bar", "3"])]),
        ),
    )
  })

  itFuture("should parse ignoring empty lines", () => {
    let csvFile = Obj.magic(`
foo;null
;

bar;

;;


`)
    Sheet.parseCsvFile(csvFile)->Future.tap(
      value =>
        expect(value)->toStrictEqual(
          Ok([Json.fromObjExn(["foo", "null"]), Json.fromObjExn(["bar", ""])]),
        ),
    )
  })

  itFuture("should not parse with incorrect data file", () => {
    let csvFile = Obj.magic(00110001111111001100101100.)
    Sheet.parseCsvFile(csvFile)->Future.tap(value => expect(value)->toStrictEqual(Error()))
  })

  itFuture("should parse with an empty file", () => {
    let csvFile = Obj.magic(``)
    Sheet.parseCsvFile(csvFile)->Future.tap(value => expect(value)->toStrictEqual(Ok([])))
  })
})

describe("CsvParserAndDecoder", () => {
  let entryValueFromOption = entry =>
    switch entry {
    | Some(entry) => entry
    | None => failwith("Unexpected error when parsing a CSV entry.")
    }

  module CsvParserAndDecoderConfig = {
    type row = {
      name: string,
      price: option<float>,
    }

    type cell =
      | Name(string)
      | Price(option<string>)
      | Unknown

    let cellFromEntryIndex = (index, value) =>
      switch index {
      | 0 => Name(value)
      | 1 => Price(Some(value))
      | _ => Unknown
      }

    let validateCellFromLabel = cell =>
      switch cell {
      | Name(value) =>
        switch value {
        | "" => Error("invalid name")
        | _ => Ok()
        }
      | Price(valueOptional) =>
        switch (valueOptional, valueOptional->Option.flatMap(Float.fromString)) {
        | (Some(_), Some(_)) | (None, _) => Ok()
        | _ => Error("invalid price")
        }
      | Unknown => Error("invalid column")
      }

    let rowFromEntry = entry => {
      let (name, price) = (entry[0]->entryValueFromOption, entry[1])

      {
        name,
        price: price->Option.flatMap(Float.fromString),
      }
    }
  }

  module Parser = Sheet.CsvParserAndDecoder(CsvParserAndDecoderConfig)

  itFuture("should parse", () => {
    let csvFile = Obj.magic(`
tortue
ninja
`)
    Parser.make(csvFile)->Future.tap(
      value =>
        expect(value)->toStrictEqual(
          Ok((
            [
              {
                name: "tortue",
                price: None,
              },
              {
                name: "ninja",
                price: None,
              },
            ],
            [],
          )),
        ),
    )
  })

  itFuture("should parse with optional column", () => {
    let csvFile = Obj.magic(`
tortue;12
ninja;0.75
`)

    Parser.make(csvFile)->Future.tap(
      value =>
        expect(value)->toStrictEqual(
          Ok((
            [
              {
                name: "tortue",
                price: Some(12.),
              },
              {
                name: "ninja",
                price: Some(0.75),
              },
            ],
            [],
          )),
        ),
    )
  })

  itFuture("should parse both valid and erronous entries", () => {
    let csvFile = Obj.magic(`
tortue;12
ninja;aaaa
;
`)

    Parser.make(csvFile)->Future.tap(
      value =>
        expect(value)->toStrictEqual(
          Ok((
            [
              {
                name: "tortue",
                price: Some(12.),
              },
            ],
            [
              InvalidCell({entryIndex: 2, rowIndex: 2, message: "invalid price", value: "aaaa"}),
              InvalidCell({entryIndex: 1, rowIndex: 3, message: "invalid name", value: ""}),
            ],
          )),
        ),
    )
  })
})
