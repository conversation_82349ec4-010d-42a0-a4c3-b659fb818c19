open Vitest
open TestingLibraryReact

describe("use1", () => {
  let {use1} = module(ReactUpdateEffect)

  it("should run effect on update", () => {
    let effect = fn1(_ => None)
    let effectFn = effect->fn
    let {rerender} = renderHookWithOptions(
      deps => use1(effectFn, [deps["0"]]),
      ~options={initialProps: {"0": "foo", "1": "bar"}},
    )
    expect(effect)->toHaveBeenCalledTimes(0)
    rerender({"0": "foo", "1": "magic"})
    expect(effect)->toHaveBeenCalledTimes(0)
    rerender({"0": "surprise", "1": "magic"})
    expect(effect)->toHaveBeenCalledTimes(1)
  })

  it("should run cleanup on unmount", () => {
    let cleanup = fn0()
    let effect = fn1(_ => Some(cleanup->fn))
    let effectFn = effect->fn
    let {unmount, rerender} = renderHookWithOptions(
      deps => use1(effectFn, deps),
      ~options={initialProps: [""]},
    )
    expect(effect)->toHaveBeenCalledTimes(0)
    expect(cleanup)->toHaveBeenCalledTimes(0)
    rerender(["foo"])
    expect(effect)->toHaveBeenCalledTimes(1)
    expect(cleanup)->toHaveBeenCalledTimes(0)
    unmount()
    expect(effect)->toHaveBeenCalledTimes(1)
    expect(cleanup)->toHaveBeenCalledTimes(1)
  })

  it("should not run cleanup provided func on unmount if no update effet is running", () => {
    let cleanup = fn0()
    let effect = fn1(_ => Some(cleanup->fn))
    let effectFn = effect->fn
    let {unmount} = renderHookWithOptions(dep => use1(effectFn, dep), ~options={initialProps: [""]})
    unmount()
    expect(effect)->toHaveBeenCalledTimes(0)
    expect(cleanup)->toHaveBeenCalledTimes(0)
  })
})

describe("use2", () => {
  let {use2} = module(ReactUpdateEffect)

  it("should run effect on update", () => {
    let effect = fn1(_ => None)
    let effectFn = effect->fn
    let {rerender} = renderHookWithOptions(
      dep => use2(effectFn, (dep["0"], dep["1"])),
      ~options={initialProps: {"0": "foo", "1": "bar", "2": "never"}},
    )
    expect(effect)->toHaveBeenCalledTimes(0)
    rerender({"0": "foo", "1": "bar", "2": "never never"})
    expect(effect)->toHaveBeenCalledTimes(0)
    rerender({"0": "foo", "1": "magic", "2": "never never"})
    expect(effect)->toHaveBeenCalledTimes(1)
    rerender({"0": "soo", "1": "much", "2": "fun"})
    expect(effect)->toHaveBeenCalledTimes(2)
  })

  it("should run cleanup on unmount", () => {
    let cleanup = fn0()
    let effect = fn1(_ => Some(cleanup->fn))
    let effectFn = effect->fn
    let {unmount, rerender} = renderHookWithOptions(
      deps => use2(effectFn, deps),
      ~options={initialProps: ("foo", "bar")},
    )
    expect(effect)->toHaveBeenCalledTimes(0)
    expect(cleanup)->toHaveBeenCalledTimes(0)
    rerender(("bar", "foo"))
    expect(effect)->toHaveBeenCalledTimes(1)
    expect(cleanup)->toHaveBeenCalledTimes(0)
    unmount()
    expect(effect)->toHaveBeenCalledTimes(1)
    expect(cleanup)->toHaveBeenCalledTimes(1)
  })

  it("should not run cleanup provided func on unmount if no update effet is running", () => {
    let cleanup = fn0()
    let effect = fn1(_ => Some(cleanup->fn))
    let effectFn = effect->fn
    let {unmount} = renderHookWithOptions(
      deps => use2(effectFn, deps),
      ~options={initialProps: ("foo", "bar")},
    )
    unmount()
    expect(effect)->toHaveBeenCalledTimes(0)
    expect(cleanup)->toHaveBeenCalledTimes(0)
  })
})

describe("use3", () => {
  let {use3} = module(ReactUpdateEffect)

  it("should run effect on update", () => {
    let effect = fn1(_ => None)
    let effectFn = effect->fn
    let {rerender} = renderHookWithOptions(
      dep => use3(effectFn, (dep["0"], dep["1"], dep["2"])),
      ~options={initialProps: {"0": "foo", "1": "bar", "2": "", "3": "never"}},
    )
    expect(effect)->toHaveBeenCalledTimes(0)
    rerender({"0": "foo", "1": "bar", "2": "", "3": "ping"})
    expect(effect)->toHaveBeenCalledTimes(0)
    rerender({"0": "foo", "1": "magic", "2": "", "3": "never"})
    expect(effect)->toHaveBeenCalledTimes(1)
    rerender({"0": "waou", "1": "so", "2": "much", "3": "fun"})
    expect(effect)->toHaveBeenCalledTimes(2)
  })

  it("should run cleanup on unmount", () => {
    let cleanup = fn0()
    let effect = fn1(_ => Some(cleanup->fn))
    let effectFn = effect->fn
    let {unmount, rerender} = renderHookWithOptions(
      deps => use3(effectFn, deps),
      ~options={initialProps: ("foo", "bar", "")},
    )
    expect(effect)->toHaveBeenCalledTimes(0)
    expect(cleanup)->toHaveBeenCalledTimes(0)
    rerender(("bar", "foo", "well"))
    expect(effect)->toHaveBeenCalledTimes(1)
    expect(cleanup)->toHaveBeenCalledTimes(0)
    unmount()
    expect(effect)->toHaveBeenCalledTimes(1)
    expect(cleanup)->toHaveBeenCalledTimes(1)
  })

  it("should not run cleanup provided func on unmount if no update effet is running", () => {
    let cleanup = fn0()
    let effect = fn1(_ => Some(cleanup->fn))
    let effectFn = effect->fn
    let {unmount} = renderHookWithOptions(
      deps => use3(effectFn, deps),
      ~options={initialProps: ("foo", "bar", "")},
    )
    unmount()
    expect(effect)->toHaveBeenCalledTimes(0)
    expect(cleanup)->toHaveBeenCalledTimes(0)
  })
})

todo("use4")
todo("use5")
todo("use6")
todo("use7")

describe("useLayout1", () => {
  let {useLayout1} = module(ReactUpdateEffect)

  it("should run effect on update", () => {
    let effect = fn1(_ => None)
    let effectFn = effect->fn
    let {rerender} = renderHookWithOptions(
      deps => useLayout1(effectFn, [deps["0"]]),
      ~options={initialProps: {"0": "foo", "1": "bar"}},
    )
    expect(effect)->toHaveBeenCalledTimes(0)
    rerender({"0": "foo", "1": "magic"})
    expect(effect)->toHaveBeenCalledTimes(0)
    rerender({"0": "surprise", "1": "magic"})
    expect(effect)->toHaveBeenCalledTimes(1)
  })

  it("should run cleanup on unmount", () => {
    let cleanup = fn0()
    let effect = fn1(_ => Some(cleanup->fn))
    let effectFn = effect->fn
    let {unmount, rerender} = renderHookWithOptions(
      deps => useLayout1(effectFn, deps),
      ~options={initialProps: [""]},
    )
    expect(effect)->toHaveBeenCalledTimes(0)
    expect(cleanup)->toHaveBeenCalledTimes(0)
    rerender(["foo"])
    expect(effect)->toHaveBeenCalledTimes(1)
    expect(cleanup)->toHaveBeenCalledTimes(0)
    unmount()
    expect(effect)->toHaveBeenCalledTimes(1)
    expect(cleanup)->toHaveBeenCalledTimes(1)
  })

  it("should not run cleanup provided func on unmount if no update effet is running", () => {
    let cleanup = fn0()
    let effect = fn1(_ => Some(cleanup->fn))
    let effectFn = effect->fn
    let {unmount} = renderHookWithOptions(
      dep => useLayout1(effectFn, dep),
      ~options={initialProps: [""]},
    )
    unmount()
    expect(effect)->toHaveBeenCalledTimes(0)
    expect(cleanup)->toHaveBeenCalledTimes(0)
  })
})
