open Vitest

test("string", () => {
  let input = {"content": "value"}
  let field = JsonCodec.field("content", JsonCodec.string)

  module Codecs = {
    let encoder = obj => obj["content"]
    let decoder = value => Ok({"content": value})

    let value = JsonCodec.object1(encoder, decoder, field)
  }

  let json = input->JsonCodec.encodeWith(Codecs.value)

  expect(json->JsonCodec.decodeWith(Codecs.value))->toStrictEqual(Ok(input))
})

test("int", () => {
  let input = {"content": 1}
  let field = JsonCodec.field("content", JsonCodec.int)

  module Codecs = {
    let encoder = obj => obj["content"]
    let decoder = value => Ok({"content": value})

    let value = JsonCodec.object1(encoder, decoder, field)
  }

  let json = input->JsonCodec.encodeWith(Codecs.value)

  expect(json->JsonCodec.decodeWith(Codecs.value))->toStrictEqual(Ok(input))
})

test("float", () => {
  let input = {"content": 3.141}
  let field = JsonCodec.field("content", JsonCodec.float)

  module Codecs = {
    let encoder = obj => obj["content"]
    let decoder = value => Ok({"content": value})

    let value = JsonCodec.object1(encoder, decoder, field)
  }

  let json = input->JsonCodec.encodeWith(Codecs.value)

  expect(json->JsonCodec.decodeWith(Codecs.value))->toStrictEqual(Ok(input))
})

test("bool", () => {
  let input = {"content": true}
  let field = JsonCodec.field("content", JsonCodec.bool)

  module Codecs = {
    let encoder = obj => obj["content"]
    let decoder = value => Ok({"content": value})

    let value = JsonCodec.object1(encoder, decoder, field)
  }

  let json = input->JsonCodec.encodeWith(Codecs.value)

  expect(json->JsonCodec.decodeWith(Codecs.value))->toStrictEqual(Ok(input))
})

test("array", () => {
  let input = {"content": [1, 2, 3]}
  let field = JsonCodec.field("content", JsonCodec.int->JsonCodec.array)

  module Codecs = {
    let encoder = obj => obj["content"]
    let decoder = value => Ok({"content": value})

    let value = JsonCodec.object1(encoder, decoder, field)
  }

  let json = input->JsonCodec.encodeWith(Codecs.value)

  expect(json->JsonCodec.decodeWith(Codecs.value))->toStrictEqual(Ok(input))
})

test("optional", () => {
  let input = {"content": None}
  let field = JsonCodec.field("content", JsonCodec.int)->JsonCodec.optional

  module Codecs = {
    let encoder = obj => obj["content"]
    let decoder = value => Ok({"content": value})

    let value = JsonCodec.object1(encoder, decoder, field)
  }

  let json = input->JsonCodec.encodeWith(Codecs.value)

  expect(json->JsonCodec.decodeWith(Codecs.value))->toStrictEqual(Ok(input))
})

describe("object", () => {
  module Codecs = {
    type style = {
      size: float,
      color: string,
    }
    type state = {
      x: float,
      y: float,
      style: option<style>,
    }

    let style = JsonCodec.object2(
      ({size, color}) => (size, color),
      ((size, color)) =>
        switch color {
        | "black" | "white" => Error(#SyntaxError("not a color"))
        | _ => Ok({size, color})
        },
      JsonCodec.field("size", JsonCodec.float),
      JsonCodec.field("color", JsonCodec.string),
    )

    let value = JsonCodec.object3(
      ({x, y, style}) => (x, y, style),
      ((x, y, style)) => Ok({x, y, style}),
      JsonCodec.field("x", JsonCodec.float),
      JsonCodec.field("y", JsonCodec.float),
      JsonCodec.field("style", style)->JsonCodec.optional,
    )
  }

  it("should encode and decode without error", () => {
    let input: Codecs.state = {
      x: 5.,
      y: 0.2,
      style: Some({size: 10., color: "red"}),
    }

    let json = input->JsonCodec.encodeWith(Codecs.value)

    expect(json)->toUnsafeStrictEqual({
      "x": 5,
      "y": 0.2,
      "style": {
        "size": 10,
        "color": "red",
      },
    })
    expect(json->JsonCodec.decodeWith(Codecs.value))->toStrictEqual(Ok(input))
  })

  it("should decode with a SyntaxError when a custom error is raised", () => {
    let input = {
      "x": 5.,
      "y": 0.2,
      "style": Some({"size": 10., "color": "black"}),
    }->Obj.magic

    expect(input->JsonCodec.decodeWith(Codecs.value))->toStrictEqual(
      Error(#SyntaxError("not a color")),
    )
  })

  it("should decode with a MissingField error", () => {
    let input = {
      "x": 5.,
      "style": Some({"size": 10., "color": "red"}),
    }->Obj.magic

    expect(input->JsonCodec.decodeWith(Codecs.value))->toStrictEqual(
      Error(#MissingField(([], "y"))),
    )
  })

  it("should decode with a UnexpectedJsonType error", () => {
    let input = {
      "x": "5",
      "y": "true",
      "style": Some({"size": 10., "color": "red"}),
    }->Obj.magic

    expect(input->JsonCodec.decodeWith(Codecs.value))->toStrictEqual(
      Error(#UnexpectedJsonType(([Field("x")], "number", "5"->Json.encodeString))),
    )
  })

  it("should decode with a UnexpectedJsonValue error when value has a size overflow", () => {
    module Codecs = {
      let encoder = obj => obj["content"]
      let decoder = value => Ok({"content": value})

      let value = JsonCodec.object1(encoder, decoder, JsonCodec.field("content", JsonCodec.int))
    }

    let input = {
      "content": 2147483649.00,
    }->Obj.magic

    expect(input->JsonCodec.decodeWith(Codecs.value))->toStrictEqual(
      Error(#UnexpectedJsonValue(([Field("content")], "2147483649"))),
    )
  })
})
