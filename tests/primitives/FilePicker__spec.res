open Vitest

open FilePicker

let makeFile: (~filename: string, ~mime: string) => File.t = %raw(` (filename, mime) =>
  new Object({ lastModifiedDate: "", name: filename, type: mime })
`)

describe("isFileBadType", () => {
  let types = [#csv]

  it("should return false", () => {
    expect(makeFile(~filename="foo.csv", ~mime="text/csv")->isFileBadType(~types))->toBe(false)
    expect(makeFile(~filename="windows.case", ~mime="text/csv")->isFileBadType(~types))->toBe(false)
  })

  it("should return true", () => {
    expect(makeFile(~filename="windows.csv", ~mime="")->isFileBadType(~types))->toBe(true)
    expect(
      makeFile(~filename="foo.csv", ~mime="application/vnd.ms-excel")->isFileBadType(~types),
    )->toBe(true)
    expect(
      makeFile(~filename="foo.xml", ~mime="application/vnd.ms-excel")->isFileBadType(~types),
    )->toBe(true)
    expect(makeFile(~filename="windows.xml", ~mime="")->isFileBadType(~types))->toBe(true)
  })
})

describe("isFileBadExtension", () => {
  let types = [#csv]

  it("should return false", () => {
    expect(makeFile(~filename="windows.csv", ~mime="")->isFileBadExtension(~types))->toBe(false)
  })

  it("should return true", () => {
    expect(makeFile(~filename="windows.case", ~mime="text/csv")->isFileBadExtension(~types))->toBe(
      true,
    )
    expect(makeFile(~filename="foo.csv", ~mime="text/csv")->isFileBadExtension(~types))->toBe(true)
    expect(
      makeFile(~filename="foo.csv", ~mime="application/vnd.ms-excel")->isFileBadExtension(~types),
    )->toBe(true)
    expect(
      makeFile(~filename="foo.xml", ~mime="application/vnd.ms-excel")->isFileBadExtension(~types),
    )->toBe(true)
    expect(makeFile(~filename="windows.xml", ~mime="")->isFileBadExtension(~types))->toBe(true)
  })
})

todo("component")
