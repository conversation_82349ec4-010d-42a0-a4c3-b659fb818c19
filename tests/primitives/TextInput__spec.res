open Vitest
open TestingLibraryReact

let noop = _ => ()

let userEvent = TestingLibraryEvent.setup()

itPromise("should interact with uncontrolled value", async () => {
  let onChange = fn1(ignore)

  render(<TextInput onChange={onChange->fn} />)->ignore

  let textInputElement = screen->getByRoleExn(#textbox)

  expect(textInputElement)->toBeVisible
  expect(textInputElement)->toHaveDisplayValue("")

  await userEvent->TestingLibraryEvent.type_(textInputElement, "new text")

  expect(textInputElement)->toHaveDisplayValue("new text")
  expect(onChange)->toHaveBeenLastCalledWith1("new text")
})

it("should render with ariaProps priority", () => {
  let ariaProps = {JsxDOM.value: "text 1", readOnly: true, disabled: true}

  render(<TextInput value="text 2" readOnly=false ariaProps onChange=noop />)->ignore

  let textInputElement = screen->getByRoleExn(#textbox)

  expect(textInputElement)->toBeVisible
  expect(textInputElement)->toHaveDisplayValue("text 1")
  expect(textInputElement)->toHaveAttribute("readonly")
  expect(textInputElement)->toHaveAttribute("disabled")
})

module TestableTextInput = {
  @react.component
  let make = (
    ~placeholder=?,
    ~readOnly=?,
    ~autoTrim=?,
    ~secureTextEntry=?,
    ~autoFocus=?,
    ~value,
    ~onFocus=?,
    ~onBlur=?,
    ~onChange,
  ) => {
    let (value, setValue) = React.useState(() => value)

    let onChange = value => {
      onChange(value)
      setValue(_ => value)
    }

    <TextInput
      ariaProps={ariaLabel: "TextInput"}
      ?placeholder
      ?readOnly
      ?autoTrim
      ?secureTextEntry
      ?autoFocus
      value
      onChange
      ?onFocus
      ?onBlur
    />
  }
}

itPromise("should interact", async () => {
  let onChange = fn1(ignore)

  render(<TestableTextInput value="" onChange={onChange->fn} />)->ignore

  let textInputElement = screen->getByRoleExn(#textbox)

  expect(textInputElement)->toBeVisible
  expect(textInputElement)->toHaveDisplayValue("")
  expect(onChange)->toHaveBeenCalledTimes(0)

  await userEvent->TestingLibraryEvent.type_(textInputElement, "new text")

  expect(textInputElement)->toBeVisible
  expect(textInputElement)->toHaveDisplayValue("new text")
  expect(onChange)->toHaveBeenLastCalledWith1("new text")
})

itPromise("should interact", async () => {
  let onChange = fn1(ignore)

  render(<TestableTextInput value="" onChange={onChange->fn} />)->ignore

  let textInputElement = screen->getByRoleExn(#textbox)

  expect(textInputElement)->toBeVisible
  expect(textInputElement)->toHaveDisplayValue("")
  expect(onChange)->toHaveBeenCalledTimes(0)

  await userEvent->TestingLibraryEvent.type_(textInputElement, "new text")

  expect(textInputElement)->toBeVisible
  expect(textInputElement)->toHaveDisplayValue("new text")
  expect(onChange)->toHaveBeenLastCalledWith1("new text")
})

itPromise("should trim the controlled value onBlur", async () => {
  let onChange = fn1(ignore)
  let onFocus = fn1(ignore)
  let onBlur = fn1(ignore)

  render(
    <TestableTextInput
      autoTrim=true value=" " onChange={onChange->fn} onFocus={onFocus->fn} onBlur={onBlur->fn}
    />,
  )->ignore

  let textInputElement = screen->getByRoleExn(#textbox)

  expect(textInputElement)->toHaveDisplayValue(" ")
  expect(onChange)->toHaveBeenCalledTimes(0)
  expect(onFocus)->toHaveBeenCalledTimes(0)
  expect(onBlur)->toHaveBeenCalledTimes(0)

  await userEvent->TestingLibraryEvent.type_(textInputElement, "new  text")

  expect(textInputElement)->toHaveDisplayValue(" new  text")
  expect(onChange)->toHaveBeenLastCalledWith1(" new  text")
  expect(onChange)->toHaveBeenCalledTimes(9)
  expect(onFocus)->toHaveBeenCalledTimes(1)
  expect(onBlur)->toHaveBeenCalledTimes(0)

  await userEvent->TestingLibraryEvent.tab

  await waitFor(() => expect(textInputElement)->toHaveDisplayValue("new text"))
  expect(onChange)->toHaveBeenLastCalledWith1("new text")
  expect(onChange)->toHaveBeenCalledTimes(10)
  expect(onFocus)->toHaveBeenCalledTimes(1)
  expect(onBlur)->toHaveBeenCalledTimes(1)
})

itPromise("should cover specificities of the remaining props", async () => {
  let {unmount} = render(
    <TestableTextInput placeholder="some placeholder" value="some text" onChange=noop />,
  )

  let textInputElement = screen->getByRoleExn(#textbox)

  expect(textInputElement)->toHaveDisplayValue("some text")
  expect(textInputElement)->toHaveAttributeValue("placeholder", "some placeholder")
  expect(textInputElement)->Vitest.not->toHaveAttribute("readonly")

  await userEvent->TestingLibraryEvent.type_(textInputElement, "!")

  expect(textInputElement)->toHaveDisplayValue("some text!")
  expect(textInputElement)->toHaveFocus

  unmount()

  let {unmount} = render(<TestableTextInput readOnly=true value="some text" onChange=noop />)

  let textInputElement = screen->getByRoleExn(#textbox)

  expect(textInputElement)->toHaveAttribute("readonly")
  expect(textInputElement)->Vitest.not->toHaveFocus

  await userEvent->TestingLibraryEvent.type_(textInputElement, "!")

  expect(textInputElement)->toHaveDisplayValue("some text")
  expect(textInputElement)->toHaveFocus

  unmount()

  let {unmount} = render(<TestableTextInput secureTextEntry=true value="some text" onChange=noop />)

  // NOTE - for some reason getByRoleExn(#textbox) doesn't work
  let textInputElement = screen->getByLabelTextExn("TextInput")

  expect(textInputElement)->toHaveValue("some text")
  expect(textInputElement)->toHaveAttributeValue("type", "password")

  expect(textInputElement)->Vitest.not->toHaveFocus

  unmount()

  render(<TestableTextInput autoFocus=true value="some text" onChange=noop />)->ignore

  let textInputElement = screen->getByRoleExn(#textbox)

  expect(textInputElement)->toHaveFocus
})
