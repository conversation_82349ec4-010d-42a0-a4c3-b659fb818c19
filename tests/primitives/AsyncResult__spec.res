open Vitest

test("notAsked", () => {
  expect(AsyncResult.notAsked())->toStrictEqual(NotAsked)
})

test("loading", () => {
  expect(AsyncResult.loading())->toStrictEqual(Loading)
})

test("reloading", () => {
  expect(AsyncResult.reloading(Ok()))->toStrictEqual(Reloading(Ok()))
  expect(AsyncResult.reloading(Error()))->toStrictEqual(Reloading(Error()))
})

test("reloadingOk", () => {
  expect(AsyncResult.reloadingOk())->toStrictEqual(Reloading(Ok()))
  expect(AsyncResult.reloadingOk("ok"))->toStrictEqual(Reloading(Ok("ok")))
})

test("done", () => {
  expect(AsyncResult.done(Ok()))->toStrictEqual(Done(Ok()))
  expect(AsyncResult.done(Error()))->toStrictEqual(Done(Error()))
})

test("doneOk", () => {
  expect(AsyncResult.doneOk())->toStrictEqual(Done(Ok()))
  expect(AsyncResult.doneOk("ok"))->toStrictEqual(Done(Ok("ok")))
})

test("isReloading", () => {
  expect(AsyncResult.isReloading(NotAsked))->toBe(false)
  expect(AsyncResult.isReloading(Loading))->toBe(false)
  expect(AsyncResult.isReloading(Reloading(Ok())))->toBe(true)
  expect(AsyncResult.isReloading(Done(Ok())))->toBe(false)
})

test("isBusy", () => {
  expect(AsyncResult.isBusy(NotAsked))->toBe(false)
  expect(AsyncResult.isBusy(Loading))->toBe(true)
  expect(AsyncResult.isBusy(Reloading(Ok())))->toBe(true)
  expect(AsyncResult.isBusy(Done(Ok())))->toBe(false)
})

test("isIdle", () => {
  expect(AsyncResult.isIdle(NotAsked))->toBe(true)
  expect(AsyncResult.isIdle(Loading))->toBe(false)
  expect(AsyncResult.isIdle(Reloading(Ok())))->toBe(false)
  expect(AsyncResult.isIdle(Done(Ok())))->toBe(true)
})

test("toBusy", () => {
  expect(AsyncResult.toBusy(NotAsked))->toStrictEqual(Loading)
  expect(AsyncResult.toBusy(Loading))->toStrictEqual(Loading)
  expect(AsyncResult.toBusy(Reloading(Ok(0))))->toStrictEqual(Reloading(Ok(0)))
  expect(AsyncResult.toBusy(Done(Ok(1))))->toStrictEqual(Reloading(Ok(1)))
})

let {notAsked, loading, reloading, done} = module(AsyncResult)

test("map", () => {
  expect(
    notAsked()->AsyncResult.map(result =>
      switch result {
      | Ok(ok) => Ok(ok)
      | Error(_) as error => error
      }
    ),
  )->toStrictEqual(NotAsked)

  expect(
    loading()->AsyncResult.map(result =>
      switch result {
      | Ok(ok) => Ok(ok)
      | Error(_) as error => error
      }
    ),
  )->toStrictEqual(Loading)

  expect(
    reloading(Ok(1))->AsyncResult.map(result =>
      switch result {
      | Ok(ok) => Ok(ok)
      | Error(_) as error => error
      }
    ),
  )->toStrictEqual(Reloading(Ok(1)))

  expect(
    reloading(Error(1))->AsyncResult.map(result =>
      switch result {
      | Ok(_) as ok => ok
      | Error(error) => Error(error + 1)
      }
    ),
  )->toStrictEqual(Reloading(Error(2)))

  expect(
    done(Ok(1))->AsyncResult.map(result =>
      switch result {
      | Ok(_) => Error()
      | Error(_) as error => error
      }
    ),
  )->toStrictEqual(Done(Error()))

  expect(
    done(Error(1))->AsyncResult.map(result =>
      switch result {
      | Ok(ok) => Ok(ok)
      | Error(_) => Ok(0)
      }
    ),
  )->toStrictEqual(Done(Ok(0)))
})

test("mapOk", () => {
  expect(notAsked()->AsyncResult.mapOk(a => a + 1))->toStrictEqual(NotAsked)
  expect(loading()->AsyncResult.mapOk(a => a + 1))->toStrictEqual(Loading)
  expect(reloading(Ok(1))->AsyncResult.mapOk(a => a + 1))->toStrictEqual(Reloading(Ok(2)))
  expect(reloading(Error(1))->AsyncResult.mapOk(a => a + 1))->toStrictEqual(Reloading(Error(1)))
  expect(done(Ok(1))->AsyncResult.mapOk(a => a + 1))->toStrictEqual(Done(Ok(2)))
  expect(done(Error(1))->AsyncResult.mapOk(a => a + 1))->toStrictEqual(Done(Error(1)))
})

test("mapError", () => {
  expect(notAsked()->AsyncResult.mapError(a => a + 1))->toStrictEqual(NotAsked)
  expect(loading()->AsyncResult.mapError(a => a + 1))->toStrictEqual(Loading)
  expect(reloading(Ok(1))->AsyncResult.mapError(a => a + 1))->toStrictEqual(Reloading(Ok(1)))
  expect(reloading(Error(1))->AsyncResult.mapError(a => a + 1))->toStrictEqual(Reloading(Error(2)))
  expect(done(Ok(1))->AsyncResult.mapError(a => a + 1))->toStrictEqual(Done(Ok(1)))
  expect(done(Error(1))->AsyncResult.mapError(a => a + 1))->toStrictEqual(Done(Error(2)))
})
