// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`currencyCny > should not change 1`] = `"CNY"`;

exports[`currencyEur > should not change 1`] = `"EUR"`;

exports[`currencyUsd > should not change 1`] = `"USD"`;

exports[`locale > should return a locale 1`] = `"en-EN"`;

exports[`locale > should return a locale 2`] = `"fr-FR"`;

exports[`toCurrencySymbol > #CNY 1`] = `"¥"`;

exports[`toCurrencySymbol > #EUR 1`] = `"€"`;

exports[`toCurrencySymbol > #USD 1`] = `"$"`;
