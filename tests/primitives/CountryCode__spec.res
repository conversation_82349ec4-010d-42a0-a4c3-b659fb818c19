open Vitest

describe("CountryCode", () => {
  test("toMediumCountryString", () =>
    expect(CountryCode.BE->CountryCode.toMediumCountryString)->toStrictEqual("Belgium")
  )

  test("toIso2String", () =>
    expect(CountryCode.FR_20R->CountryCode.toIso2String)->toStrictEqual("FR-20R")
  )

  test("toIsoString", () =>
    expect(CountryCode.FR_974->CountryCode.toIsoString)->toStrictEqual("FR")
  )

  test("toLongCountryString", () =>
    expect(CountryCode.FR_974->CountryCode.toLongCountryString)->toStrictEqual(
      "France - Reunion Island",
    )
  )

  test("fromString", () => {
    expect("FR_974"->CountryCode.fromString)->toStrictEqual(Ok(CountryCode.FR_974))
    expect("XX"->CountryCode.fromString)->toStrictEqual(Error("Unknown country code"))
  })
})
