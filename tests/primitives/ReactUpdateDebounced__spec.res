open Vitest
open TestingLibraryReact

testPromise("use", async () => {
  let testableHook = renderHookWithOptions(
    value => ReactUpdateDebounced.use(value, ~delay=500),
    ~options={initialProps: 0},
  )
  expect(testableHook.result.current)->toBe(0)

  testableHook.rerender(1)
  expect(testableHook.result.current)->toBe(0)

  testableHook.rerender(2)
  expect(testableHook.result.current)->toBe(0)

  let start = Js.Date.now()
  await waitFor(() => expect(testableHook.result.current)->toBe(2))
  let end = Js.Date.now()
  let delay = end -. start
  expect(delay)->toBeGreaterThanOrEqual(500.)
  expect(delay)->toBeLessThan(550.)
  expect(testableHook.result.all)->toStrictEqual([0, 0, 0, 2])

  testableHook.rerender(3)
  expect(testableHook.result.current)->toBe(2)

  let start = Js.Date.now()
  await waitFor(() => expect(testableHook.result.current)->toBe(3))
  let end = Js.Date.now()
  let delay = end -. start
  expect(delay)->toBeGreaterThanOrEqual(500.)
  expect(delay)->toBeLessThan(550.)
  expect(testableHook.result.all)->toStrictEqual([0, 0, 0, 2, 2, 3])
})
