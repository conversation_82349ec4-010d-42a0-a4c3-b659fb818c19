open Vitest

let fr = #"fr-FR"

describe("locale", () => {
  let {locale} = module(Intl)

  it("should return a locale", () => {
    expect(locale)->toBe(#"en-EN")
    expect(locale)->toMatchSnapshot
    expect(fr)->toMatchSnapshot
  })
})

describe("t", () => {
  let {t} = module(Intl)

  it("should return a string", () => {
    expect(t("test"))->toBe("Hello")
  })
})

describe("template", () => {
  let {template} = module(Intl)

  it("should return a raw string", () => {
    expect(
      template(
        "test {{foo}} {{bar}}",
        ~values={
          "foo": "1",
          "bar": "2",
        },
        (),
      ),
    )->toBe("test 1 2")
  })
})

describe("isPlural", () => {
  let {isPlural} = module(Intl)

  it("should return plural boolean", () => {
    expect(isPlural(0))->toBe(true)
    expect(isPlural(1))->toBe(false)
    expect(isPlural(2))->toBe(true)
    expect(isPlural(10))->toBe(true)
  })
})

describe("phoneNumberFormat", () => {
  let {phoneNumberFormat} = module(Intl)

  it("should format value", () => {
    expect(phoneNumberFormat(""))->toBe("")
    expect(phoneNumberFormat("0682082955"))->toBe("06 82 08 29 55")
    expect(phoneNumberFormat("068208295"))->toBe("06 82 08 29 5")
    expect(phoneNumberFormat("+33682082955"))->toBe("+33 6 82 08 29 55")
    expect(phoneNumberFormat("+3368208295"))->toBe("+33 68208295")
  })
})

test("timeZone", () => {
  let {timeZone} = module(Intl)

  expect(timeZone)->toBe("UTC")
})

// Intl uses non-breaking space and not a normal space.
// It also uses an actual minus sign (\u2212) and not a
// hyphen-minus (\x2D) which keyboards use.
// So we need to set those manually in our matchers

// > https://github.com/opendevtools/rescript-intl/blob/main/__tests__/NumberFormat_test.res#L5

describe("currencyEur", () => {
  let {currencyEur} = module(Intl)

  it("should not change", () => {
    expect(currencyEur)->toMatchSnapshot
  })
})

describe("currencyUsd", () => {
  let {currencyUsd} = module(Intl)

  it("should not change", () => {
    expect(currencyUsd)->toMatchSnapshot
  })
})

describe("currencyCny", () => {
  let {currencyCny} = module(Intl)

  it("should not change", () => {
    expect(currencyCny)->toMatchSnapshot
  })
})

describe("toCurrencySymbol", () => {
  let {toCurrencySymbol} = module(Intl)

  test("#EUR", () => {
    expect(#EUR->toCurrencySymbol)->toMatchSnapshot
  })

  test("#USD", () => {
    expect(#USD->toCurrencySymbol)->toMatchSnapshot
  })

  test("#CNY", () => {
    expect(#CNY->toCurrencySymbol)->toMatchSnapshot
  })
})

describe("currencyFormat", () => {
  let {currencyFormat, currencyEur, currencyUsd} = module(Intl)

  testEach2([
    (0., "€0.00"),
    (0.01, "€0.01"),
    (0.001, "€0.00"),
    (0.005, "€0.01"),
    (1., "€1.00"),
    (1.01, "€1.01"),
    (1.1, "€1.10"),
    (1.001, "€1.00"),
    (12.001, "€12.00"),
    (12.01, "€12.01"),
    (12.1, "€12.10"),
    (1200000.1, "€1,200,000.10"),
    (1200000.01, "€1,200,000.01"),
    (1200000.008, "€1,200,000.01"),
  ])(."should format %i with euro currency", (input, output) => {
    expect(currencyFormat(input, ~currency=currencyEur))->toBe(output)
  })

  testEach2([
    (0., "$0.00"),
    (0.01, "$0.01"),
    (0.001, "$0.00"),
    (0.005, "$0.01"),
    (1., "$1.00"),
    (1.01, "$1.01"),
    (1.1, "$1.10"),
    (1.001, "$1.00"),
    (12.001, "$12.00"),
    (12.01, "$12.01"),
    (12.1, "$12.10"),
    (1200000.1, "$1,200,000.10"),
    (1200000.01, "$1,200,000.01"),
    (1200000.008, "$1,200,000.01"),
  ])(."should format %i with dollar currency", (input, output) => {
    expect(currencyFormat(input, ~currency=currencyUsd))->toBe(output)
  })

  todo("fraction digits")
})

describe("decimalFormat", () => {
  let {decimalFormat} = module(Intl)

  testEach2([
    (0., `0`),
    (0.01, `0.01`),
    (0.001, `0.001`),
    (0.005, `0.005`),
    (1., `1`),
    (1.01, `1.01`),
    (1.1, `1.1`),
    (1.001, `1.001`),
    (12.001, `12.001`),
    (12.01, `12.01`),
    (12.1, `12.1`),
    (1200000.1, `1,200,000.1`),
    (1200000.01, `1,200,000.01`),
    (1200000.008, `1,200,000.008`),
  ])(."should format %i with decimal", (input, output) => {
    expect(decimalFormat(input))->toBe(output)
  })

  todo("fraction digits")
})

describe("unitFormat", () => {
  let {unitFormat} = module(Intl)

  testEach2([
    ((0., #kilogram), `0 kg`),
    ((0.01, #gram), `0.01 g`),
    ((0.001, #liter), `0.001 L`),
    ((0.005, #liter), `0.005 L`),
    ((1., #liter), `1 L`),
    ((1.01, #liter), `1.01 L`),
    ((1.1, #liter), `1.1 L`),
    ((1.001, #liter), `1.001 L`),
    ((12.001, #liter), `12.001 L`),
    ((12.01, #liter), `12.01 L`),
    ((12.1, #liter), `12.1 L`),
    ((1200000.1, #liter), `1,200,000.1 L`),
    ((1200000.01, #liter), `1,200,000.01 L`),
    ((1200000.008, #liter), `1,200,000.008 L`),
  ])(."should format %i with decimal appended with the unit", ((inputValue, inputUnit), output) => {
    expect(inputValue->unitFormat(~unit=inputUnit))->toBe(output)
  })

  testEach2([
    ((1., #kilogram), `1 kg`),
    ((1., #gram), `1 g`),
    ((1., #liter), `1 L`),
  ])(."should append the unit to formate %i", ((inputValue, inputUnit), output) =>
    expect(inputValue->unitFormat(~unit=inputUnit))->toBe(output)
  )
})

describe("percentFormat", () => {
  let {percentFormat} = module(Intl)

  testEach2([
    (0., `0%`),
    (0.01, `1%`),
    (0.001, `0%`),
    (0.005, `1%`),
    (0.0001, `0%`),
    (0.0005, `0%`),
    (0.00001, `0%`),
    (0.00005, `0%`),
    (0.000005, `0%`),
    (18., `1,800%`),
    (18.01, `1,801%`),
    (18.001, `1,800%`),
    (18.005, `1,801%`),
    (18.00005, `1,800%`),
    (18.000005, `1,800%`),
    (1200000.1, `120,000,010%`),
    (1200000.01, `120,000,001%`),
    (1200000.008, `120,000,001%`),
  ])(."should format %i with percent", (input, output) => {
    expect(percentFormat(input))->toBe(output)
  })

  todo("fraction digits")
})

describe("dateTimeFomat", () => {
  let {dateTimeFormat} = module(Intl)

  testEach3([
    (Js.Date.fromFloat(0.), #full, "12:00:00 AM Coordinated Universal Time"),
    (Js.Date.fromFloat(0.), #long, "12:00:00 AM UTC"),
    (Js.Date.fromFloat(0.), #medium, "12:00:00 AM"),
    (Js.Date.fromFloat(0.), #short, "12:00 AM"),
  ])(."should format %i with timeStyle arg %j", (input, timeStyle, output) => {
    expect(dateTimeFormat(input, ~timeStyle))->toBe(output)
  })

  testEach3([
    (Js.Date.fromFloat(0.), #full, "Thursday, January 1, 1970"),
    (Js.Date.fromFloat(0.), #long, "January 1, 1970"),
    (Js.Date.fromFloat(0.), #medium, "Jan 1, 1970"),
    (Js.Date.fromFloat(0.), #short, "1/1/70"),
  ])(."should format %i with dateStyle arg %j", (input, dateStyle, output) => {
    expect(dateTimeFormat(input, ~dateStyle))->toBe(output)
  })

  testEach4([
    (
      Js.Date.fromFloat(0.),
      #full,
      #full,
      "Thursday, January 1, 1970 at 12:00:00 AM Coordinated Universal Time",
    ),
    (Js.Date.fromFloat(0.), #long, #short, "January 1, 1970 at 12:00 AM"),
    (Js.Date.fromFloat(0.), #medium, #medium, "Jan 1, 1970, 12:00:00 AM"),
    (Js.Date.fromFloat(0.), #short, #long, "1/1/70, 12:00:00 AM UTC"),
  ])(."should format %i with dateStyle arg %j and timeStyle %k", (
    input,
    dateStyle,
    timeStyle,
    output,
  ) => {
    expect(dateTimeFormat(input, ~dateStyle, ~timeStyle))->toBe(output)
  })

  it("should formate the date with different custom options", () => {
    let date = Js.Date.fromFloat(0.)
    expect(dateTimeFormat(date, ~year=#numeric))->toBe("1970")
    expect(dateTimeFormat(date, ~year=#numeric, ~month=#short, ~day=#numeric))->toBe("Jan 1, 1970")
    expect(dateTimeFormat(date, ~year=#numeric, ~month=#"2-digit", ~day=#"2-digit"))->toBe(
      "01/01/1970",
    )
    expect(
      dateTimeFormat(
        date,
        ~year=#"2-digit",
        ~month=#"2-digit",
        ~day=#"2-digit",
        ~hour=#numeric,
        ~minute=#"2-digit",
        ~second=#numeric,
      ),
    )->toBe("01/01/70, 12:00:00 AM")
    expect(dateTimeFormat(date, ~hour=#numeric, ~minute=#numeric))->toBe("12:00 AM")
  })

  it("should ignore custom date options when a `timeStyle` or `dateStyle` is defined", () => {
    let date = Js.Date.fromFloat(0.)
    expect(dateTimeFormat(date, ~dateStyle=#short, ~hour=#"2-digit"))->toBe("1/1/70")
    expect(dateTimeFormat(date, ~timeStyle=#long, ~year=#"2-digit"))->toBe("12:00:00 AM UTC")
    expect(
      dateTimeFormat(
        date,
        ~dateStyle=#long,
        ~timeStyle=#short,
        ~year=#numeric,
        ~month=#numeric,
        ~day=#numeric,
        ~hour=#numeric,
        ~minute=#numeric,
        ~second=#numeric,
      ),
    )->toBe("January 1, 1970 at 12:00 AM")
  })
})

todo("listFormat")
