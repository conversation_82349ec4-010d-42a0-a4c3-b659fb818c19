open Vitest
open TestingLibraryReact

module TestableLink = {
  @react.component
  let make = (
    ~children,
    ~to,
    ~openNewTab=?,
    ~disabled=?,
    ~onPress=?,
    ~history=History.createMemoryHistory(),
  ) =>
    <Providers history>
      <Navigation.Link to ?openNewTab ?onPress ?disabled> {children} </Navigation.Link>
    </Providers>
}

let actHook = (~history=History.createMemoryHistory(), hook) => {
  let wrapper = props => <Providers history> {props["children"]} </Providers>
  renderHookWithOptions(hook, ~options={wrapper: wrapper}).result
}

test("useUrl", () => {
  let {current: url} = actHook(Navigation.useUrl)
  expect(url.path)->toStrictEqual([])
  expect(url.pathname)->toBe("/")
  expect(url.query)->toUnsafeStrictEqual("")
  expect(url.state)->toBe(None)

  let history = History.createMemoryHistory(~options={initialEntries: [""]}, ())
  let {current: url} = actHook(Navigation.useUrl, ~history)
  expect(url.path)->toStrictEqual([])
  expect(url.pathname)->toBe("/")
  expect(url.query)->toUnsafeStrictEqual("")
  expect(url.state)->toBe(None)

  let history = History.createMemoryHistory(
    ~options={initialEntries: ["/a", "/b"], initialIndex: 1},
    (),
  )
  let {current: url} = actHook(Navigation.useUrl, ~history)
  expect(url.path)->toStrictEqual(["b"])
  expect(url.pathname)->toBe("/b")
  expect(url.query)->toUnsafeStrictEqual("")
  expect(url.state)->toBe(None)

  let history = History.createMemoryHistory(
    ~options={
      initialEntries: ["/a", "/b/c"],
      initialIndex: 1,
    },
    (),
  )
  let {current: url} = actHook(Navigation.useUrl, ~history)
  expect(url.path)->toStrictEqual(["b", "c"])
  expect(url.pathname)->toBe("/b/c")
  expect(url.query)->toUnsafeStrictEqual("")
  expect(url.state)->toBe(None)

  let history = History.createMemoryHistory(
    ~options={
      initialEntries: ["/a", "/1/2-3"],
      initialIndex: 1,
    },
    (),
  )
  let {current: url} = actHook(Navigation.useUrl, ~history)
  expect(url.path)->toStrictEqual(["1", "2-3"])
  expect(url.pathname)->toBe("/1/2-3")
  expect(url.query)->toUnsafeStrictEqual("")
  expect(url.state)->toBe(None)
})

describe("useNavigate", () => {
  it("should navigate to the new location", () => {
    let history = History.createMemoryHistory()
    let {current: navigate} = actHook(Navigation.useNavigate, ~history)

    expect(history.location.pathname)->toBe("/")
    expect(history.length)->toBe(1)
    expect(history.index)->toBe(Some(0))

    navigate("/new-route")

    expect(history.location.pathname)->toBe("/new-route")
    expect(history.length)->toBe(2)
    expect(history.index)->toBe(Some(1))
  })

  it("should navigate with a custom history state", () => {
    let payload = Js.Dict.empty()
    payload->Js.Dict.set("payload", Json.encodedNull)

    let history = History.createMemoryHistory()
    let {current: navigate} = actHook(Navigation.useNavigate, ~history)

    expect(history.length)->toBe(1)
    expect(history.location.pathname)->toBe("/")
    expect(history.location.state)->toBe(None)
    expect(history.index)->toBe(Some(0))

    navigate("/", ~state=payload->Json.encodeDict)

    expect(history.length)->toBe(2)
    expect(history.location.pathname)->toBe("/")
    expect(history.index)->toBe(Some(1))
    expect(history.location.state)->toStrictEqual(Some(payload->Json.encodeDict))
  })

  it("should navigate by replacing the current history node", () => {
    let history = History.createMemoryHistory()
    let {current: navigate} = actHook(Navigation.useNavigate, ~history)

    expect(history.length)->toBe(1)
    expect(history.location.pathname)->toBe("/")
    expect(history.index)->toBe(Some(0))

    navigate("/new-route", ~replace=true)

    expect(history.location.pathname)->toBe("/new-route")
    expect(history.length)->toBe(1)
    expect(history.index)->toBe(Some(0))
  })
})

test("useGoBack", () => {
  let history = History.createMemoryHistory()
  let result = actHook(Navigation.useGoBack, ~history)

  let (canGoBack, goBack) = result.current
  expect(canGoBack)->toBe(false)
  expect(history.length)->toBe(1)
  expect(history.location.pathname)->toBe("/")
  expect(history.index)->toBe(Some(0))

  act(goBack)

  let (canGoBack, _) = result.current
  expect(canGoBack)->toBe(false)
  expect(history.length)->toBe(1)
  expect(history.location.pathname)->toBe("/")
  expect(history.index)->toBe(Some(0))

  act(() => history.push("/new-route", None))

  let (canGoBack, goBack) = result.current
  expect(canGoBack)->toBe(true)
  expect(history.length)->toBe(2)
  expect(history.location.pathname)->toBe("/new-route")
  expect(history.index)->toBe(Some(1))

  act(goBack)

  let (canGoBack, _) = result.current
  expect(canGoBack)->toBe(false)
  expect(history.length)->toBe(2)
  expect(history.location.pathname)->toBe("/")
  expect(history.index)->toBe(Some(0))
})

// REVIEW - hard to test because not based on history API
todo("openRoute")

describe("Link", () => {
  testEach1([
    Navigation.Route("/"),
    RouteWithQueryString("/", ""->QueryString.fromString),
    Url("https://wino.fr"->Url.make),
  ])(."should render a link", to => {
    render(<TestableLink to> {"my link"->React.string} </TestableLink>)->ignore

    let link = screen->getByRoleExn(#link)
    let href = switch to {
    | Route(pathname) | RouteWithQueryString(pathname, _) => pathname
    | Url(url) => url->Url.href
    }

    expect(link)->toBeVisible
    expect(link)->toHaveAttributeValue("href", href)
    expect(link)->toHaveAttributeValue("target", #_self)
    expect(link)->toHaveTextContent("my link")
  })

  testEach1([
    Navigation.Route("/"),
    RouteWithQueryString("/", ""->QueryString.fromString),
    Url("https://wino.fr"->Url.make),
  ])(."should render a link that open a new tab", to => {
    render(<TestableLink to openNewTab=true> {"my link"->React.string} </TestableLink>)->ignore

    let link = screen->getByRoleExn(#link)
    let href = switch to {
    | Route(pathname) | RouteWithQueryString(pathname, _) => pathname
    | Url(url) => url->Url.href
    }

    expect(link)->toBeVisible
    expect(link)->toHaveAttributeValue("href", href)
    expect(link)->toHaveAttributeValue("target", #_blank)
    expect(link)->toHaveTextContent("my link")
  })

  itPromise("should render a route link and navigate + trigger callback on click", async () => {
    let userEvent = TestingLibraryEvent.setup()
    let history = History.createMemoryHistory()
    let onPress = fn1(ignore)

    <TestableLink onPress={onPress->fn} history to=Navigation.Route("/route")>
      {"my link"->React.string}
    </TestableLink>
    ->render
    ->ignore

    let link = screen->getByRoleExn(#link)

    expect(link)->toHaveAttributeValue("href", "/route")
    expect(link)->toHaveTextContent("my link")
    expect(link)->toBeVisible
    expect(onPress)->toHaveBeenCalledTimes(0)
    expect(history.index)->toBe(Some(0))
    expect(history.length)->toBe(1)
    expect(history.location.pathname)->toBe("/")
    expect(history.location.state)->toBe(None)
    expect(history.location.search)->toBe("")

    await userEvent->TestingLibraryEvent.click(link)

    expect(onPress)->toHaveBeenCalledTimes(1)
    expect(history.index)->toBe(Some(1))
    expect(history.length)->toBe(2)
    expect(history.location.pathname)->toBe("/route")
    expect(history.location.state)->toBe(None)
    expect(history.location.search)->toBe("")
  })

  itPromise(
    "should render a route link with query string and navigate + trigger callback on click",
    async () => {
      let userEvent = TestingLibraryEvent.setup()
      let history = History.createMemoryHistory()
      let queryString = "?some=test"->QueryString.fromString
      let onPress = fn1(ignore)

      <TestableLink
        history to=Navigation.RouteWithQueryString("/route", queryString) onPress={onPress->fn}>
        {"my link"->React.string}
      </TestableLink>
      ->render
      ->ignore

      let link = screen->getByRoleExn(#link)

      expect(link)->toHaveAttributeValue("href", "/route?some=test")
      expect(link)->toHaveTextContent("my link")
      expect(link)->toBeVisible
      expect(onPress)->toHaveBeenCalledTimes(0)
      expect(history.index)->toBe(Some(0))
      expect(history.length)->toBe(1)
      expect(history.location.pathname)->toBe("/")
      expect(history.location.state)->toBe(None)
      expect(history.location.search)->toBe("")

      await userEvent->TestingLibraryEvent.click(link)

      expect(onPress)->toHaveBeenCalledTimes(1)
      expect(history.index)->toBe(Some(1))
      expect(history.length)->toBe(2)
      expect(history.location.pathname)->toBe("/route")
      expect(history.location.state)->toBe(None)
      expect(history.location.search)->toBe("?some=test")
    },
  )

  testEachPromise1([
    Navigation.Route("/"),
    RouteWithQueryString("/", ""->QueryString.fromString),
    Url("https://wino.fr"->Url.make),
  ])(."should render a disabled link", async to => {
    let userEvent = TestingLibraryEvent.setup()
    let history = History.createMemoryHistory()
    let onPress = fn1(ignore)

    <TestableLink onPress={onPress->fn} history to disabled=true>
      {"my link"->React.string}
    </TestableLink>
    ->render
    ->ignore

    let link = screen->getByRoleExn(#link)

    expect(screen->getByTextExn("my link"))->toBeVisible
    expect(link)->toHaveAttributeValue("aria-disabled", "true")
    expect(onPress)->toHaveBeenCalledTimes(0)
    expect(history.index)->toBe(Some(0))
    expect(history.length)->toBe(1)
    expect(history.location.pathname)->toBe("/")
    expect(history.location.state)->toBe(None)
    expect(history.location.search)->toBe("")

    await userEvent->TestingLibraryEvent.click(screen->getByTextExn("my link"))

    expect(screen->getByTextExn("my link"))->toBeVisible
    expect(link)->toHaveAttributeValue("aria-disabled", "true")
    expect(onPress)->toHaveBeenCalledTimes(0)
    expect(history.index)->toBe(Some(0))
    expect(history.length)->toBe(1)
    expect(history.location.pathname)->toBe("/")
    expect(history.location.state)->toBe(None)
    expect(history.location.search)->toBe("")
  })

  todo("should render a hypertext link and navigate on click and not trigger callback")
  todo("should render and navigate on a new tab on click")
})

// NOTE - Some integration tests to ensure basic interactions
// with the browser might be valuable at some point
// but how to implement them in a relevant/efficient way still
// need some investigations.
todo("Prompt")

todo("Redirect")
