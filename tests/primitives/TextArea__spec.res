open Vitest
open TestingLibraryReact

let noop = _ => ()

let userEvent = TestingLibraryEvent.setup()

itPromise("should interact with uncontrolled value", async () => {
  let onChange = fn1(ignore)

  render(<TextArea onChange={onChange->fn} />)->ignore

  let textAreaElement = screen->getByRoleExn(#textbox)

  expect(textAreaElement)->toBeVisible
  expect(textAreaElement)->toHaveDisplayValue("")

  await userEvent->TestingLibraryEvent.type_(textAreaElement, "new text")

  expect(textAreaElement)->toHaveDisplayValue("new text")
  expect(onChange)->toHaveBeenLastCalledWith1("new text")
})

it("should render with ariaProps priority", () => {
  let ariaProps = {JsxDOM.value: "text 1", readOnly: true, disabled: true}

  render(<TextArea value="text 2" readOnly=false ariaProps onChange=noop />)->ignore

  let textAreaElement = screen->getByRoleExn(#textbox)

  expect(textAreaElement)->toBeVisible
  expect(textAreaElement)->toHaveDisplayValue("text 1")
  expect(textAreaElement)->toHaveAttribute("readonly")
  expect(textAreaElement)->toHaveAttribute("disabled")

  screen->debugElement(textAreaElement)
})

module TestableTextArea = {
  @react.component
  let make = (~placeholder=?, ~readOnly=?, ~value, ~onFocus=?, ~onBlur=?, ~onChange) => {
    let (value, setValue) = React.useState(() => value)

    let onChange = value => {
      onChange(value)
      setValue(_ => value)
    }

    <TextArea
      ariaProps={ariaLabel: "TextArea"} ?placeholder ?readOnly value onChange ?onFocus ?onBlur
    />
  }
}

itPromise("should interact", async () => {
  let onChange = fn1(ignore)

  render(<TestableTextArea value="" onChange={onChange->fn} />)->ignore

  let textAreaElement = screen->getByRoleExn(#textbox)

  expect(textAreaElement)->toBeVisible
  expect(textAreaElement)->toHaveDisplayValue("")
  expect(onChange)->toHaveBeenCalledTimes(0)

  await userEvent->TestingLibraryEvent.type_(textAreaElement, "new text")

  expect(textAreaElement)->toBeVisible
  expect(textAreaElement)->toHaveDisplayValue("new text")
  expect(onChange)->toHaveBeenLastCalledWith1("new text")
})

itPromise("should cover specificities of the remaining props", async () => {
  let {unmount} = render(
    <TestableTextArea placeholder="some placeholder" value="some text" onChange=noop />,
  )

  let textAreaElement = screen->getByRoleExn(#textbox)

  expect(textAreaElement)->toHaveDisplayValue("some text")
  expect(textAreaElement)->toHaveAttributeValue("placeholder", "some placeholder")
  expect(textAreaElement)->Vitest.not->toHaveAttribute("readonly")

  await userEvent->TestingLibraryEvent.type_(textAreaElement, "!")

  expect(textAreaElement)->toHaveDisplayValue("some text!")
  expect(textAreaElement)->toHaveFocus

  unmount()

  render(<TestableTextArea readOnly=true value="some text" onChange=noop />)->ignore

  let textAreaElement = screen->getByRoleExn(#textbox)

  expect(textAreaElement)->toHaveAttribute("readonly")
  expect(textAreaElement)->Vitest.not->toHaveFocus

  await userEvent->TestingLibraryEvent.type_(textAreaElement, "!")

  expect(textAreaElement)->toHaveDisplayValue("some text")
  expect(textAreaElement)->toHaveFocus
})
