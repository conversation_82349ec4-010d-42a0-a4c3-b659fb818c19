open Vitest
open TestingLibraryReact

module ReducerWithBasicUpdates = {
  type state = int
  type action = Increment | Decrement | Reset

  let reducer = (state: state, action: action) => {
    open ReactUpdateReducer
    switch action {
    | Increment => Update(state + 1)
    | Decrement => Update(state - 1)
    | Reset => Update(0)
    }
  }

  let initialState = 0
}

test("use with basic updates", () => {
  let {reducer, initialState} = module(ReducerWithBasicUpdates)
  let {result} = renderHook(() => ReactUpdateReducer.use(reducer, initialState))
  let (state, dispatch) = result.current
  expect(state)->toBe(0)

  act(() => dispatch(Increment))

  let (state, dispatch) = result.current
  expect(state)->toBe(1)

  act(() => dispatch(Increment))

  let (state, dispatch) = result.current
  expect(state)->toBe(2)

  act(() => dispatch(Decrement))

  let (state, dispatch) = result.current
  expect(state)->toBe(1)

  act(() => dispatch(Reset))

  let (state, _) = result.current
  expect(state)->toBe(0)
})

let actDelay = 500

module ReducerWithAdvancedUpdates = {
  type state = AsyncResult.t<int, unit>
  type action =
    Noop | Fetch(result<int, unit>) | FetchDone(result<int, unit>) | FetchSuccess(int) | FetchError

  let reducer = (state: state, action: action) => {
    open ReactUpdateReducer
    switch action {
    | Noop => NoUpdate
    | Fetch(result) =>
      UpdateWithSideEffects(
        switch state {
        | Loading | NotAsked => AsyncResult.loading()
        | Reloading(_) as reloadingState => reloadingState
        | Done(result) => AsyncResult.reloading(result)
        },
        ({dispatch}) => {
          let timeoutId = Js.Global.setTimeout(() => dispatch(FetchDone(result)), actDelay)
          Some(() => Js.Global.clearTimeout(timeoutId))
        },
      )
    | FetchDone(result) =>
      SideEffects(
        ({dispatch}) => {
          switch result {
          | Ok(ok) => dispatch(FetchSuccess(ok))
          | Error() => dispatch(FetchError)
          }
          None
        },
      )
    | FetchSuccess(int) => Update(AsyncResult.done(Ok(int)))
    | FetchError => Update(AsyncResult.done(Error()))
    }
  }

  let initialState = AsyncResult.notAsked()
}

testPromise("use with advanced updates", async () => {
  let {reducer, initialState} = module(ReducerWithAdvancedUpdates)
  let testableHook = renderHook(() => ReactUpdateReducer.use(reducer, initialState))
  let (state, dispatch) = testableHook.result.current
  expect(state)->toStrictEqual(NotAsked)

  act(() => dispatch(Fetch(Ok(0))))

  let (state, _) = testableHook.result.current
  expect(state)->toStrictEqual(Loading)

  await waitFor(() => {
    let (state, _dispatch) = testableHook.result.current
    expect(state)->toStrictEqual(Done(Ok(0)))
  })

  act(() => dispatch(Fetch(Error())))

  let (state, _) = testableHook.result.current
  expect(state)->toStrictEqual(Reloading(Ok(0)))

  await waitFor(() => {
    let (state, _dispatch) = testableHook.result.current
    expect(state)->toStrictEqual(Done(Error()))
  })

  act(() => dispatch(Noop))

  let (state, _) = testableHook.result.current
  expect(state)->toStrictEqual(Done(Error()))

  act(() => dispatch(Fetch(Ok(1))))
  act(() => dispatch(Noop))

  let (state, _) = testableHook.result.current
  expect(state)->toStrictEqual(Reloading(Error()))

  await waitFor(() => {
    let (state, _dispatch) = testableHook.result.current
    expect(state)->toStrictEqual(Done(Ok(1)))
  })
})

todo("use with cancelling a side effect")

todo("useWithMapState")
