open Vitest

test("notAsked", () => {
  expect(AsyncData.notAsked())->toStrictEqual(NotAsked)
})

test("loading", () => {
  expect(AsyncData.loading())->toStrictEqual(Loading)
})

test("reloading", () => {
  expect(AsyncData.reloading())->toStrictEqual(Reloading())
})

test("reloading", () => {
  expect(AsyncData.done())->toStrictEqual(Done())
})

test("isReloading", () => {
  expect(AsyncData.isReloading(NotAsked))->toBe(false)
  expect(AsyncData.isReloading(Loading))->toBe(false)
  expect(AsyncData.isReloading(Reloading()))->toBe(true)
  expect(AsyncData.isReloading(Done()))->toBe(false)
})

test("isNotAsked", () => {
  expect(AsyncData.isNotAsked(NotAsked))->toBe(true)
  expect(AsyncData.isNotAsked(Loading))->toBe(false)
  expect(AsyncData.isNotAsked(Reloading()))->toBe(false)
  expect(AsyncData.isNotAsked(Done()))->toBe(false)
})

test("isBusy", () => {
  expect(AsyncData.isBusy(NotAsked))->toBe(false)
  expect(AsyncData.isBusy(Loading))->toBe(true)
  expect(AsyncData.isBusy(Reloading()))->toBe(true)
  expect(AsyncData.isBusy(Done()))->toBe(false)
})

test("isIdle", () => {
  expect(AsyncData.isIdle(NotAsked))->toBe(true)
  expect(AsyncData.isIdle(Loading))->toBe(false)
  expect(AsyncData.isIdle(Reloading()))->toBe(false)
  expect(AsyncData.isIdle(Done()))->toBe(true)
})

test("toBusy", () => {
  expect(AsyncData.toBusy(NotAsked))->toStrictEqual(Loading)
  expect(AsyncData.toBusy(Loading))->toStrictEqual(Loading)
  expect(AsyncData.toBusy(Reloading(0)))->toStrictEqual(Reloading(0))
  expect(AsyncData.toBusy(Done(1)))->toStrictEqual(Reloading(1))
})

test("map", () => {
  expect(AsyncData.NotAsked->AsyncData.map(a => a + 1))->toStrictEqual(NotAsked)
  expect(AsyncData.Loading->AsyncData.map(a => a + 1))->toStrictEqual(Loading)
  expect(AsyncData.Reloading(1)->AsyncData.map(a => a + 1))->toStrictEqual(Reloading(2))
  expect(AsyncData.Done(1)->AsyncData.map(a => a + 1))->toStrictEqual(Done(2))
})

test("flatMap", () => {
  expect(AsyncData.NotAsked->AsyncData.flatMap(a => Done(a + 1)))->toStrictEqual(NotAsked)
  expect(AsyncData.Loading->AsyncData.flatMap(a => Reloading(a + 1)))->toStrictEqual(Loading)
  expect(AsyncData.Reloading(1)->AsyncData.flatMap(_ => NotAsked))->toStrictEqual(NotAsked)
  expect(AsyncData.Done(1)->AsyncData.flatMap(a => Reloading(a + 1)))->toStrictEqual(Reloading(2))
  expect(AsyncData.Done(1)->AsyncData.flatMap(a => Done(a + 1)))->toStrictEqual(Done(2))
})
