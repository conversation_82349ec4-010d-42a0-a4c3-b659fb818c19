open Vitest

let {apolloQueryExn} = module(MSWHelpers)

module Query = %graphql(`
  query product($productId: ID!) {
    product(id: $productId) {
      name
    }
  }
`)

module QueryWithoutOperationName = %graphql(`
  query ($productId: ID!) {
    product(id: $productId) {
      name
    }
  }
`)

todo("apolloQueryWithDelayExn")

describe("apolloQueryExn", () => {
  let {setupNodeServer, use, listen, close} = module(MSW)
  let {ctxData, makeLink} = module(MSW.GraphQL)

  let server = setupNodeServer([])
  let gatewayLink = makeLink("http://localhost/graphql")

  beforeAll(() => server->listen({onUnhandledRequest: #warn}))
  afterAll(() => server->close)

  itPromise(
    "should find the request operationName and variables then resolve the query with null result",
    async () => {
      server->use([
        gatewayLink->MSWHelpers.apolloQueryExn(
          module(Query),
          (request, res, ctx) => {
            expect(request.body.operationName)->toBe(Some("product"))
            expect(request.variables)->toStrictEqual({productId: "mock-id"})

            res(ctx->ctxData({product: Js.Nullable.null}))
          },
        ),
      ])

      let queryResult = await ApolloConfig.makeClient().query(
        ~query=module(Query),
        Query.makeVariables(~productId="mock-id", ()),
      )
      let queryData = switch queryResult {
      | Ok({data}) => data
      | _ => {product: None}
      }

      expect(queryData)->toStrictEqual({product: None})
    },
  )

  itPromise(
    "should find the request operationName and variables then resolve the query with defined result",
    async () => {
      server->use([
        gatewayLink->MSWHelpers.apolloQueryExn(
          module(Query),
          (request, res, ctx) => {
            expect(request.body.operationName)->toBe(Some("product"))
            expect(request.variables)->toStrictEqual({productId: "mock-id"})

            res(
              ctx->ctxData({
                product: {
                  Query.Raw.name: "mock-name",
                  __typename: "mock-typename",
                }->Js.Nullable.return,
              }),
            )
          },
        ),
      ])

      let queryResult = await ApolloConfig.makeClient().query(
        ~query=module(Query),
        Query.makeVariables(~productId="mock-id", ()),
      )
      let queryData = switch queryResult {
      | Ok({data}) => data
      | _ => {product: None}
      }

      expect(queryData)->toStrictEqual({
        Query.product: Some({name: "mock-name", __typename: "mock-typename"}),
      })
    },
  )

  it("should raise an exception when the operationName is missing in the graphql query", () =>
    expect(
      () =>
        server->use([
          gatewayLink->MSWHelpers.apolloQueryExn(
            module(QueryWithoutOperationName),
            (_req, res, ctx) => res(ctx->ctxData({product: Js.Nullable.null})),
          ),
        ]),
    )->toRaise(MSWHelpers.OperationNameNotFound)
  )
})

describe("cursorPaginateExn", () => {
  let fixture = ["node-a", "node-b", "node-c"]

  it("should fetch all the nodes with by default the first 20 fetched", () => {
    let (first, after) = (None, None)
    let {edges, pageInfo} = fixture->MSWHelpers.cursorPaginateExn(~first?, ~after?, ())
    expect(pageInfo)->toStrictEqual({endCursor: Some("2"), hasNextPage: Some(false)})
    expect(edges)->toStrictEqual(fixture)
  })

  it("should fetch all the nodes one by one with a pagination", () => {
    let (first, after) = (Some(1), None)
    let {edges: edgesFirstPage, pageInfo} =
      fixture->MSWHelpers.cursorPaginateExn(~first?, ~after?, ())
    let {edges: edgesSecondPage, pageInfo} =
      fixture->MSWHelpers.cursorPaginateExn(~first?, ~after=?pageInfo.endCursor, ())
    let {edges: edgesThirdPage, pageInfo} =
      fixture->MSWHelpers.cursorPaginateExn(~first?, ~after=?pageInfo.endCursor, ())
    let edges = Array.concatMany([edgesFirstPage, edgesSecondPage, edgesThirdPage])
    expect(pageInfo)->toStrictEqual({endCursor: Some("2"), hasNextPage: Some(false)})
    expect(edges)->toStrictEqual(fixture)
  })

  it("should fetch a node after cursor", () => {
    let (first, after) = (Some(1), Some("0"))
    let {edges, pageInfo} = fixture->MSWHelpers.cursorPaginateExn(~first?, ~after?, ())
    expect(pageInfo)->toStrictEqual({endCursor: Some("1"), hasNextPage: Some(true)})
    expect(edges)->toStrictEqual(["node-b"])

    let (first, after) = (Some(1), Some("1"))
    let {edges, pageInfo} = fixture->MSWHelpers.cursorPaginateExn(~first?, ~after?, ())
    expect(pageInfo)->toStrictEqual({endCursor: Some("2"), hasNextPage: Some(false)})
    expect(edges)->toStrictEqual(["node-c"])
  })

  it("should fetch after cursor the last node", () => {
    let (first, after) = (Some(1), Some("0"))
    let {edges, pageInfo} = fixture->MSWHelpers.cursorPaginateExn(~first?, ~after?, ())
    expect(pageInfo)->toStrictEqual({endCursor: Some("1"), hasNextPage: Some(true)})
    expect(edges)->toStrictEqual(["node-b"])
  })

  it("should not fetch anything when the after cursor is not found", () => {
    let (first, after) = (Some(1), Some("200"))
    let {edges, pageInfo} = fixture->MSWHelpers.cursorPaginateExn(~first?, ~after?, ())
    expect(pageInfo)->toStrictEqual({endCursor: None, hasNextPage: None})
    expect(edges)->toStrictEqual([])
  })

  it("should raise an exception when the argument first is zero", () => {
    expect(() => fixture->MSWHelpers.cursorPaginateExn(~first=0, ()))->toRaise(
      MSWHelpers.InvalidArgumentFirst,
    )
    expect(() => fixture->MSWHelpers.cursorPaginateExn(~first=-1, ()))->toRaise(
      MSWHelpers.InvalidArgumentFirst,
    )
  })

  it("should raise an exception when the argument after isn't a numerical string", () => {
    let exec = () =>
      fixture->MSWHelpers.cursorPaginateExn(
        ~after="sTI1MmZlNWQxNDBlMTlkMzA4ZjIwMzc0MDRhMDUzNmEa",
        (),
      )
    expect(exec)->toRaise(MSWHelpers.InvalidArgumentAfter)
  })
})
