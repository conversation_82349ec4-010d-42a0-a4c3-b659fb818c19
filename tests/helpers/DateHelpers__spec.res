open Vitest

let {
  addMonths,
  subMonths,
  diffInDays,
  diffInHours,
  subDays,
  getPreviousPeriod,
  getPreviousYearPeriod,
  endOfDay,
} = module(DateHelpers)

test("addMonths", () => {
  let inputDate = Js.Date.makeWithYMD(~year=2020., ~month=3., ~date=1., ())
  let expectedDate = Js.Date.makeWithYMD(~year=2020., ~month=4., ~date=1., ())
  expect(addMonths(inputDate, 1.))->toStrictEqual(expectedDate)

  let inputDate = Js.Date.makeWithYMD(~year=2020., ~month=3., ~date=2., ())
  let expectedDate = Js.Date.makeWithYMD(~year=2020., ~month=4., ~date=2., ())
  expect(addMonths(inputDate, 1.))->toStrictEqual(expectedDate)

  let inputDate = Js.Date.makeWithYMD(~year=2020., ~month=1., ~date=28., ())
  let expectedDate = Js.Date.makeWithYMD(~year=2020., ~month=2., ~date=28., ())
  expect(addMonths(inputDate, 1.))->toStrictEqual(expectedDate)

  let inputDate = Js.Date.makeWithYMD(~year=2020., ~month=2., ~date=30., ())
  let expectedDate = Js.Date.makeWithYMD(~year=2020., ~month=3., ~date=30., ())
  expect(addMonths(inputDate, 1.))->toStrictEqual(expectedDate)

  let inputDate = Js.Date.makeWithYMD(~year=2020., ~month=3., ~date=30., ())
  let expectedDate = Js.Date.makeWithYMD(~year=2020., ~month=4., ~date=30., ())
  expect(addMonths(inputDate, 1.))->toStrictEqual(expectedDate)

  let inputDate = Js.Date.makeWithYMD(~year=2020., ~month=11., ~date=31., ())
  let expectedDate = Js.Date.makeWithYMD(~year=2021., ~month=1., ~date=28., ())
  expect(addMonths(inputDate, 2.))->toStrictEqual(expectedDate)

  let inputDate = Js.Date.makeWithYMD(~year=2019., ~month=11., ~date=31., ())
  let expectedDate = Js.Date.makeWithYMD(~year=2020., ~month=1., ~date=29., ())
  expect(addMonths(inputDate, 2.))->toStrictEqual(expectedDate)

  let hours = 16.
  let minutes = 12.
  let seconds = 53.
  let inputDate = Js.Date.makeWithYMDHMS(
    ~year=2020.,
    ~month=2.,
    ~date=31.,
    ~hours,
    ~minutes,
    ~seconds,
    (),
  )
  let expectedDate = Js.Date.makeWithYMDHMS(
    ~year=2020.,
    ~month=3.,
    ~date=30.,
    ~hours,
    ~minutes,
    ~seconds,
    (),
  )
  expect(addMonths(inputDate, 1.))->toStrictEqual(expectedDate)

  let inputDate = Js.Date.makeWithYMD(~year=2020., ~month=10., ~date=30., ())
  let expectedDate = Js.Date.makeWithYMD(~year=2020., ~month=7., ~date=30., ())
  expect(addMonths(inputDate, -3.))->toStrictEqual(expectedDate)

  let inputDate = Js.Date.makeWithYMD(~year=2020., ~month=2., ~date=30., ())
  let expectedDate = Js.Date.makeWithYMD(~year=2020., ~month=1., ~date=29., ())
  expect(addMonths(inputDate, -1.))->toStrictEqual(expectedDate)

  let inputDate = Js.Date.makeWithYMD(~year=2020., ~month=2., ~date=30., ())
  let expectedDate = Js.Date.makeWithYMD(~year=2019., ~month=10., ~date=30., ())
  expect(addMonths(inputDate, -4.))->toStrictEqual(expectedDate)

  let inputDate = Js.Date.makeWithYMD(~year=2020., ~month=2., ~date=1., ())
  let expectedDate = Js.Date.makeWithYMD(~year=2020., ~month=1., ~date=1., ())
  expect(addMonths(inputDate, -1.))->toStrictEqual(expectedDate)

  let inputDate = Js.Date.makeWithYMD(~year=2020., ~month=2., ~date=1., ())
  let expectedDate = Js.Date.makeWithYMD(~year=2020., ~month=0., ~date=1., ())
  expect(addMonths(inputDate, -2.))->toStrictEqual(expectedDate)
})

test("subMonths", () => {
  let inputDate = Js.Date.makeWithYMD(~year=2020., ~month=3., ~date=1., ())
  let expectedDate = Js.Date.makeWithYMD(~year=2020., ~month=4., ~date=1., ())
  expect(subMonths(inputDate, -1.))->toStrictEqual(expectedDate)

  let inputDate = Js.Date.makeWithYMD(~year=2020., ~month=3., ~date=2., ())
  let expectedDate = Js.Date.makeWithYMD(~year=2020., ~month=4., ~date=2., ())
  expect(subMonths(inputDate, -1.))->toStrictEqual(expectedDate)

  let inputDate = Js.Date.makeWithYMD(~year=2020., ~month=1., ~date=28., ())
  let expectedDate = Js.Date.makeWithYMD(~year=2020., ~month=2., ~date=28., ())
  expect(subMonths(inputDate, -1.))->toStrictEqual(expectedDate)

  let inputDate = Js.Date.makeWithYMD(~year=2020., ~month=2., ~date=30., ())
  let expectedDate = Js.Date.makeWithYMD(~year=2020., ~month=3., ~date=30., ())
  expect(subMonths(inputDate, -1.))->toStrictEqual(expectedDate)

  let inputDate = Js.Date.makeWithYMD(~year=2020., ~month=3., ~date=30., ())
  let expectedDate = Js.Date.makeWithYMD(~year=2020., ~month=4., ~date=30., ())
  expect(subMonths(inputDate, -1.))->toStrictEqual(expectedDate)

  let inputDate = Js.Date.makeWithYMD(~year=2020., ~month=11., ~date=31., ())
  let expectedDate = Js.Date.makeWithYMD(~year=2021., ~month=1., ~date=28., ())
  expect(subMonths(inputDate, -2.))->toStrictEqual(expectedDate)

  let inputDate = Js.Date.makeWithYMD(~year=2019., ~month=11., ~date=31., ())
  let expectedDate = Js.Date.makeWithYMD(~year=2020., ~month=1., ~date=29., ())
  expect(subMonths(inputDate, -2.))->toStrictEqual(expectedDate)

  let hours = 16.
  let minutes = 12.
  let seconds = 53.
  let inputDate = Js.Date.makeWithYMDHMS(
    ~year=2020.,
    ~month=2.,
    ~date=31.,
    ~hours,
    ~minutes,
    ~seconds,
    (),
  )
  let expectedDate = Js.Date.makeWithYMDHMS(
    ~year=2020.,
    ~month=3.,
    ~date=30.,
    ~hours,
    ~minutes,
    ~seconds,
    (),
  )
  expect(subMonths(inputDate, -1.))->toStrictEqual(expectedDate)

  let inputDate = Js.Date.makeWithYMD(~year=2020., ~month=10., ~date=30., ())
  let expectedDate = Js.Date.makeWithYMD(~year=2020., ~month=7., ~date=30., ())
  expect(subMonths(inputDate, 3.))->toStrictEqual(expectedDate)

  let inputDate = Js.Date.makeWithYMD(~year=2020., ~month=2., ~date=30., ())
  let expectedDate = Js.Date.makeWithYMD(~year=2020., ~month=1., ~date=29., ())
  expect(subMonths(inputDate, 1.))->toStrictEqual(expectedDate)

  let inputDate = Js.Date.makeWithYMD(~year=2020., ~month=2., ~date=30., ())
  let expectedDate = Js.Date.makeWithYMD(~year=2019., ~month=10., ~date=30., ())
  expect(subMonths(inputDate, 4.))->toStrictEqual(expectedDate)

  let inputDate = Js.Date.makeWithYMD(~year=2020., ~month=2., ~date=1., ())
  let expectedDate = Js.Date.makeWithYMD(~year=2020., ~month=1., ~date=1., ())
  expect(subMonths(inputDate, 1.))->toStrictEqual(expectedDate)

  let inputDate = Js.Date.makeWithYMD(~year=2020., ~month=2., ~date=1., ())
  let expectedDate = Js.Date.makeWithYMD(~year=2020., ~month=0., ~date=1., ())
  expect(subMonths(inputDate, 2.))->toStrictEqual(expectedDate)
})

test("diffInDays", () => {
  let firstDate = Js.Date.makeWithYMD(~year=2023., ~month=01., ~date=01., ())
  let secondDate = Js.Date.makeWithYMD(~year=2023., ~month=01., ~date=01., ())

  expect(diffInDays(firstDate, secondDate))->toStrictEqual(0)

  let firstDate = Js.Date.makeWithYMD(~year=2023., ~month=01., ~date=01., ())
  let secondDate = Js.Date.makeWithYMD(~year=2023., ~month=01., ~date=10., ())

  expect(diffInDays(firstDate, secondDate))->toStrictEqual(9)

  let firstDate = Js.Date.makeWithYMD(~year=2023., ~month=01., ~date=01., ())
  let secondDate = Js.Date.makeWithYMD(~year=2023., ~month=02., ~date=01., ())

  expect(diffInDays(firstDate, secondDate))->toStrictEqual(28)

  let firstDate = Js.Date.makeWithYMD(~year=2021., ~month=01., ~date=01., ())
  let secondDate = Js.Date.makeWithYMD(~year=2022., ~month=01., ~date=01., ())

  expect(diffInDays(firstDate, secondDate))->toStrictEqual(365)

  let firstDate = Js.Date.makeWithYMD(~year=2020., ~month=01., ~date=01., ())
  let secondDate = Js.Date.makeWithYMD(~year=2021., ~month=01., ~date=01., ())

  expect(diffInDays(firstDate, secondDate))->toStrictEqual(366)

  let firstDate = Js.Date.makeWithYMD(~year=2020., ~month=01., ~date=10., ())
  let secondDate = Js.Date.makeWithYMD(~year=2020., ~month=01., ~date=01., ())

  expect(diffInDays(firstDate, secondDate))->toStrictEqual(9)
})

test("diffInHours", () => {
  let firstDate = Js.Date.makeWithYMDHMS(
    ~year=2023.,
    ~month=01.,
    ~date=01.,
    ~hours=12.,
    ~minutes=00.,
    ~seconds=00.,
    (),
  )
  let lastDate = Js.Date.makeWithYMDHMS(
    ~year=2023.,
    ~month=01.,
    ~date=01.,
    ~hours=13.,
    ~minutes=00.,
    ~seconds=00.,
    (),
  )
  expect(diffInHours(firstDate, lastDate))->toStrictEqual(1)

  let firstDate = Js.Date.makeWithYMDHMS(
    ~year=2023.,
    ~month=01.,
    ~date=01.,
    ~hours=00.,
    ~minutes=00.,
    ~seconds=00.,
    (),
  )
  let lastDate = Js.Date.makeWithYMDHMS(
    ~year=2023.,
    ~month=01.,
    ~date=01.,
    ~hours=23.,
    ~minutes=59.,
    ~seconds=59.,
    (),
  )
  expect(diffInHours(firstDate, lastDate))->toStrictEqual(24)

  let firstDate = Js.Date.makeWithYMDHMS(
    ~year=2023.,
    ~month=01.,
    ~date=01.,
    ~hours=00.,
    ~minutes=00.,
    ~seconds=00.,
    (),
  )
  let lastDate = Js.Date.makeWithYMDHMS(
    ~year=2023.,
    ~month=01.,
    ~date=02.,
    ~hours=00.,
    ~minutes=00.,
    ~seconds=00.,
    (),
  )
  expect(diffInHours(firstDate, lastDate))->toStrictEqual(24)

  let firstDate = Js.Date.makeWithYMDHMS(
    ~year=2023.,
    ~month=01.,
    ~date=01.,
    ~hours=12.,
    ~minutes=30.,
    ~seconds=00.,
    (),
  )
  let lastDate = Js.Date.makeWithYMDHMS(
    ~year=2023.,
    ~month=01.,
    ~date=02.,
    ~hours=12.,
    ~minutes=30.,
    ~seconds=00.,
    (),
  )
  expect(diffInHours(firstDate, lastDate))->toStrictEqual(24)

  let firstDate = Js.Date.makeWithYMDHMS(
    ~year=2023.,
    ~month=01.,
    ~date=01.,
    ~hours=15.,
    ~minutes=00.,
    ~seconds=00.,
    (),
  )
  let lastDate = Js.Date.makeWithYMDHMS(
    ~year=2023.,
    ~month=01.,
    ~date=03.,
    ~hours=15.,
    ~minutes=00.,
    ~seconds=00.,
    (),
  )
  expect(diffInHours(firstDate, lastDate))->toStrictEqual(48)

  let firstDate = Js.Date.makeWithYMDHMS(
    ~year=2023.,
    ~month=01.,
    ~date=10.,
    ~hours=10.,
    ~minutes=00.,
    ~seconds=00.,
    (),
  )
  let lastDate = Js.Date.makeWithYMDHMS(
    ~year=2023.,
    ~month=01.,
    ~date=09.,
    ~hours=11.,
    ~minutes=00.,
    ~seconds=00.,
    (),
  )
  expect(diffInHours(firstDate, lastDate))->toStrictEqual(23)
})

test("endOfDay", () => {
  let date = Js.Date.makeWithYMD(~year=2020., ~month=3., ~date=1., ())
  let expectedEndOfDay = Js.Date.makeWithYMDHMS(
    ~year=2020.,
    ~month=3.,
    ~date=1.,
    ~hours=23.,
    ~minutes=59.,
    ~seconds=59.,
    (),
  )
  let expectedEndOfDay = Js.Date.setMilliseconds(expectedEndOfDay, 999.0)
  expect(date->endOfDay)->toStrictEqual(expectedEndOfDay->Js.Date.fromFloat)
})

describe("previousPeriod", () => {
  it("should return the day before when period is a single day", () => {
    let startDate = Js.Date.makeWithYMDH(~year=2022., ~month=09., ~date=27., ~hours=0., ())
    let endDate = Js.Date.makeWithYMDH(~year=2022., ~month=09., ~date=27., ~hours=24., ())

    let expectedStartDate = Js.Date.makeWithYMDH(~year=2022., ~month=09., ~date=26., ~hours=0., ())
    let expectedEndDate = Js.Date.makeWithYMDH(~year=2022., ~month=09., ~date=26., ~hours=24., ())

    let (startDatePreviousPeriod, endDatePreviousPeriod) = getPreviousPeriod(startDate, endDate)

    expect(startDatePreviousPeriod)->toStrictEqual(expectedStartDate)
    expect(endDatePreviousPeriod)->toStrictEqual(expectedEndDate)
  })

  describe("for week", () => {
    it(
      "should return the previous period",
      () => {
        let startDate = Js.Date.makeWithYMD(~year=2022., ~month=09., ~date=26., ())
        let endDate = Js.Date.makeWithYMD(~year=2022., ~month=09., ~date=28., ())

        let expectedStartDate = Js.Date.makeWithYMD(~year=2022., ~month=09., ~date=24., ())
        let expectedEndDate = Js.Date.makeWithYMD(~year=2022., ~month=09., ~date=26., ())

        let (startDatePreviousPeriod, endDatePreviousPeriod) = getPreviousPeriod(startDate, endDate)

        expect(startDatePreviousPeriod)->toStrictEqual(expectedStartDate)
        expect(endDatePreviousPeriod)->toStrictEqual(expectedEndDate)
      },
    )

    it(
      "should return the previous period",
      () => {
        let startDate = Js.Date.makeWithYMD(~year=2022., ~month=09., ~date=19., ())
        let endDate = Js.Date.makeWithYMD(~year=2022., ~month=09., ~date=25., ())

        let expectedStartDate = Js.Date.makeWithYMD(~year=2022., ~month=09., ~date=13., ())
        let expectedEndDate = Js.Date.makeWithYMD(~year=2022., ~month=09., ~date=19., ())

        let (startDatePreviousPeriod, endDatePreviousPeriod) = getPreviousPeriod(startDate, endDate)

        expect(startDatePreviousPeriod)->toStrictEqual(expectedStartDate)
        expect(endDatePreviousPeriod)->toStrictEqual(expectedEndDate)
      },
    )
  })

  describe("for month", () => {
    it(
      "should return the previous period",
      () => {
        let startDate = Js.Date.makeWithYMD(~year=2022., ~month=08., ~date=01., ())
        let endDate = Js.Date.makeWithYMD(~year=2022., ~month=08., ~date=25., ())

        let expectedStartDate = Js.Date.makeWithYMD(~year=2022., ~month=07., ~date=08., ())
        let expectedEndDate = Js.Date.makeWithYMD(~year=2022., ~month=08., ~date=01., ())

        let (startDatePreviousPeriod, endDatePreviousPeriod) = getPreviousPeriod(startDate, endDate)

        expect(startDatePreviousPeriod)->toStrictEqual(expectedStartDate)
        expect(endDatePreviousPeriod)->toStrictEqual(expectedEndDate)
      },
    )

    it(
      "should return the previous period",
      () => {
        let startDate = Js.Date.makeWithYMD(~year=2022., ~month=07., ~date=01., ())
        let endDate = Js.Date.makeWithYMD(~year=2022., ~month=07., ~date=31., ())

        let expectedStartDate = Js.Date.makeWithYMD(~year=2022., ~month=06., ~date=02., ())
        let expectedEndDate = Js.Date.makeWithYMD(~year=2022., ~month=07., ~date=01., ())

        let (startDatePreviousPeriod, endDatePreviousPeriod) = getPreviousPeriod(startDate, endDate)

        expect(startDatePreviousPeriod)->toStrictEqual(expectedStartDate)
        expect(endDatePreviousPeriod)->toStrictEqual(expectedEndDate)
      },
    )
  })
})

describe("for year", () => {
  it("should return the previous period", () => {
    let startDate = Js.Date.makeWithYMD(~year=2022., ~month=0., ~date=01., ())
    let endDate = Js.Date.makeWithYMD(~year=2022., ~month=08., ~date=28., ())

    let expectedStartDate = Js.Date.makeWithYMD(~year=2021., ~month=03., ~date=06., ())
    let expectedEndDate = Js.Date.makeWithYMD(~year=2021., ~month=12., ~date=01., ())

    let (startDatePreviousPeriod, endDatePreviousPeriod) = getPreviousPeriod(startDate, endDate)

    expect(startDatePreviousPeriod)->toStrictEqual(expectedStartDate)
    expect(endDatePreviousPeriod)->toStrictEqual(expectedEndDate)
  })

  it("should return the previous period", () => {
    let startDate = Js.Date.makeWithYMD(~year=2021., ~month=0., ~date=01., ())
    let endDate = Js.Date.makeWithYMD(~year=2021., ~month=11., ~date=31., ())

    let expectedStartDate = Js.Date.makeWithYMD(~year=2020., ~month=0., ~date=03., ())
    let expectedEndDate = Js.Date.makeWithYMD(~year=2020., ~month=12., ~date=01., ())

    let (startDatePreviousPeriod, endDatePreviousPeriod) = getPreviousPeriod(startDate, endDate)

    expect(startDatePreviousPeriod)->toStrictEqual(expectedStartDate)
    expect(endDatePreviousPeriod)->toStrictEqual(expectedEndDate)
  })
})

describe("custom period", () => {
  it("should return the previous period", () => {
    let startDate = Js.Date.makeWithYMD(~year=2022., ~month=06., ~date=01., ())
    let endDate = Js.Date.makeWithYMD(~year=2022., ~month=06., ~date=02., ())

    let expectedStartDate = Js.Date.makeWithYMD(~year=2022., ~month=05., ~date=30., ())
    let expectedEndDate = Js.Date.makeWithYMD(~year=2022., ~month=06., ~date=01., ())

    let (startDatePreviousPeriod, endDatePreviousPeriod) = getPreviousPeriod(startDate, endDate)

    expect(startDatePreviousPeriod)->toStrictEqual(expectedStartDate)
    expect(endDatePreviousPeriod)->toStrictEqual(expectedEndDate)
  })
})

describe("previousYearPeriod", () => {
  it("should return the same period last year", () => {
    let startDate = Js.Date.makeWithYMD(~year=2022., ~month=0., ~date=01., ())
    let endDate = Js.Date.makeWithYMD(~year=2022., ~month=08., ~date=28., ())

    let expectedStartDate = Js.Date.makeWithYMD(~year=2021., ~month=0., ~date=01., ())
    let expectedEndDate = Js.Date.makeWithYMD(~year=2021., ~month=08., ~date=28., ())

    let (startDatePreviousPeriod, endDatePreviousPeriod) = getPreviousYearPeriod(startDate, endDate)

    expect(startDatePreviousPeriod)->toStrictEqual(expectedStartDate)
    expect(endDatePreviousPeriod)->toStrictEqual(expectedEndDate)
  })
})

todo("copyDate")
todo("startOfDay")
todo("isSameDay")
todo("addDays")
todo("subDays")
todo("startOfWeek")
todo("endOfWeek")
todo("addWeeks")
todo("subWeeks")
todo("startOfMonth")
todo("lastDayOfMonthDate")
todo("getDaysInMonth")
todo("addMonths")
todo("endOfMonth")
todo("startOfYear")
todo("addYears")
todo("subYears")
todo("endOfYear")
