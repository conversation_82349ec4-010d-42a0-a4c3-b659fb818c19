open Vitest

test("sumManyCharges", () => {
  let {sumManyCharges} = module(PriceCalculator)

  expect(sumManyCharges([]))->toBe(0.)
  expect(sumManyCharges([1.5, 2.25, 3.75]))->toBe(7.5)
  expect(sumManyCharges([5., -2., 0.]))->toBe(3.)
  expect(sumManyCharges([0.1, 0.2, 0.387238]))->toBe(0.687238)
})

describe("Purchase", () => {
  let {toNetValue, fromNetValue} = module(PriceCalculator.Purchase)
  let {toValueWithCharges, fromValueWithCharges} = module(PriceCalculator.Purchase)

  test("toNetValue / fromNetValue", () => {
    expect(toNetValue(100., ~discount=0.))->toBe(100.)
    expect(fromNetValue(100., ~discount=0.))->toBe(100.)

    expect(toNetValue(100., ~discount=15.))->toBe(85.)
    expect(fromNetValue(85., ~discount=15.))->toBe(100.)

    expect(toNetValue(50., ~discount=75.))->toBe(0.)
    expect(fromNetValue(0., ~discount=75.))->toBe(75.)

    expect(toNetValue(0., ~discount=-1.))->toBe(0.)
    expect(fromNetValue(0., ~discount=-1.))->toBe(0.)
  })

  test("toValueWithCharges / fromValueWithCharges", () => {
    expect(toValueWithCharges(100., ~charges=0.))->toBe(100.)
    expect(fromValueWithCharges(100., ~charges=0.))->toBe(100.)

    expect(toValueWithCharges(100., ~charges=5.))->toBe(105.)
    expect(fromValueWithCharges(105., ~charges=5.))->toBe(100.)

    expect(toValueWithCharges(100., ~charges=-5.))->toBe(100.)
    expect(fromValueWithCharges(100., ~charges=-5.))->toBe(100.)
  })
})

describe("Retail", () => {
  describe("Charges", () => {
    let {remove, addBack} = module(PriceCalculator.Retail.Charges)

    test(
      "remove / addBack",
      () => {
        expect(remove(100., ~charges=0.))->toBe(100.)
        expect(addBack(100., ~charges=0.))->toBe(100.)

        expect(remove(100., ~charges=5.))->toBe(95.)
        expect(addBack(95., ~charges=5.))->toBe(100.)

        expect(remove(100., ~charges=-5.))->toBe(100.)
        expect(addBack(100., ~charges=-5.))->toBe(100.)
      },
    )
  })

  describe("Tax", () => {
    let {add, removeBack} = module(PriceCalculator.Retail.Tax)

    test(
      "add / removeBack",
      () => {
        expect(add(100., ~taxRate=0.))->toBe(100.)
        expect(removeBack(100., ~taxRate=0.))->toBe(100.)

        expect(add(100., ~taxRate=-10.))->toBe(100.)
        expect(removeBack(100., ~taxRate=-10.))->toBe(100.)

        expect(add(10., ~taxRate=20.))->toBe(12.)
        expect(removeBack(12., ~taxRate=20.))->toBe(10.)

        expect(add(29.17, ~taxRate=20.))->toBe(35.004)
        expect(removeBack(35.004, ~taxRate=20.))->toBe(29.17)

        expect(add(10., ~taxRate=5.5))->toBe(10.55)
        expect(removeBack(10.55, ~taxRate=5.5))->toBe(10.)

        expect(add(10., ~taxRate=200.))->toBe(30.)
        expect(removeBack(30., ~taxRate=200.))->toBe(10.)

        expect(add(10., ~taxRate=-50.))->toBe(10.)
        expect(removeBack(10., ~taxRate=-50.))->toBe(10.)
      },
    )
  })

  describe("Margin", () => {
    let {compute} = module(PriceCalculator.Retail.Margin)

    test(
      "compute",
      () => {
        expect(compute(100., ~purchasePrice=0.))->toBe(100.)
        expect(compute(150., ~purchasePrice=100.))->toBe(50.)
        expect(compute(80., ~purchasePrice=100.))->toBe(-20.)
        expect(compute(100., ~purchasePrice=-100.))->toBe(100.)
      },
    )
  })

  describe("Rate", () => {
    let {apply, undo} = module(PriceCalculator.Retail.Rate)

    test(
      "apply / undo with Coefficient",
      () => {
        let kind = PriceCalculator.Retail.Rate.Coefficient
        expect(apply(110., ~kind, ~purchasePrice=0., ~charges=10., ~taxRate=20.))->toBe(None)

        expect(apply(100., ~kind, ~purchasePrice=100., ~charges=0., ~taxRate=0.))->toBe(Some(1.))
        expect(undo(1., ~kind, ~purchasePrice=100., ~charges=0., ~taxRate=0.))->toBe(Some(100.))

        expect(apply(100., ~kind, ~purchasePrice=100., ~charges=0., ~taxRate=20.))->toBe(Some(1.2))
        expect(undo(1.2, ~kind, ~purchasePrice=100., ~charges=0., ~taxRate=20.))->toBe(Some(100.))

        expect(apply(110., ~kind, ~purchasePrice=100., ~charges=10., ~taxRate=20.))->toBe(Some(1.2))
        expect(undo(1.2, ~kind, ~purchasePrice=100., ~charges=10., ~taxRate=20.))->toBe(Some(110.))
      },
    )

    test(
      "apply / undo with MarkupRate",
      () => {
        let kind = PriceCalculator.Retail.Rate.MarkupRate
        expect(apply(150., ~kind, ~purchasePrice=0., ~charges=10., ~taxRate=20.))->toBe(None)

        expect(apply(150., ~kind, ~purchasePrice=100., ~charges=0., ~taxRate=0.))->toBe(Some(50.))
        expect(undo(50., ~kind, ~purchasePrice=100., ~charges=0., ~taxRate=0.))->toBe(Some(150.))

        expect(apply(150., ~kind, ~purchasePrice=100., ~charges=0., ~taxRate=20.))->toBe(Some(50.))
        expect(undo(50., ~kind, ~purchasePrice=100., ~charges=0., ~taxRate=20.))->toBe(Some(150.))

        expect(apply(150., ~kind, ~purchasePrice=100., ~charges=10., ~taxRate=20.))->toBe(Some(40.))
        expect(undo(40., ~kind, ~purchasePrice=100., ~charges=10., ~taxRate=20.))->toBe(Some(150.))
      },
    )

    test(
      "apply / undo with MarginRate",
      () => {
        let kind = PriceCalculator.Retail.Rate.MarginRate
        expect(apply(0., ~kind, ~purchasePrice=100., ~charges=0., ~taxRate=20.))->toBe(None)

        expect(apply(200., ~kind, ~purchasePrice=100., ~charges=0., ~taxRate=0.))->toBe(Some(50.))
        expect(undo(50., ~kind, ~purchasePrice=100., ~charges=0., ~taxRate=0.))->toBe(Some(200.))

        expect(apply(200., ~kind, ~purchasePrice=100., ~charges=0., ~taxRate=20.))->toBe(Some(50.))
        expect(undo(50., ~kind, ~purchasePrice=100., ~charges=0., ~taxRate=20.))->toBe(Some(200.))

        expect(apply(200., ~kind, ~purchasePrice=100., ~charges=10., ~taxRate=20.))->toBe(
          Some(47.36842105263158),
        )
        expect(
          undo(47.36842105263158, ~kind, ~purchasePrice=100., ~charges=10., ~taxRate=20.),
        )->toBe(Some(200.))

        expect(apply(200., ~kind, ~purchasePrice=0., ~charges=0., ~taxRate=0.))->toBe(Some(100.))
        expect(undo(100., ~kind, ~purchasePrice=0., ~charges=0., ~taxRate=0.))->toBe(None)
      },
    )
  })
})
