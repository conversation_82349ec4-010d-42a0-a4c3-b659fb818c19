open Vitest

test("Iban.validate", () => {
  expect(CorporateEntity.Iban.validate(""))->toStrictEqual(false)
  expect(CorporateEntity.Iban.validate("FR3093098098"))->toStrictEqual(false)
  expect(CorporateEntity.Iban.validate("**********************"))->toStrictEqual(true)
  expect(CorporateEntity.Iban.validate("***************************"))->toStrictEqual(false)
  expect(CorporateEntity.Iban.validate("***************************"))->toStrictEqual(true)
  expect(CorporateEntity.Iban.validate("***************************"))->toStrictEqual(true)
  expect(CorporateEntity.Iban.validate("FR 2014 5080 0050 2193 6636 36E70"))->toStrictEqual(true)
  expect(CorporateEntity.Iban.validate("FR 2014 5080 0050 2193 6636 36E70"))->toStrictEqual(true)
  expect(CorporateEntity.Iban.validate(" FR 2014 5080 0050 2193 6636 36E70 "))->toStrictEqual(true)
})

test("Iban.sanitize", () => {
  expect(CorporateEntity.Iban.sanitize(" FR 2014 5080 0050 2193 6636 36E70 "))->toStrictEqual(
    "***************************",
  )
})

describe("CompanyLegalForm", () => {
  test("toString", () => {
    expect(
      CorporateEntity.CompanyLegalForm.MICRO->CorporateEntity.CompanyLegalForm.toString,
    )->toStrictEqual("MICRO")
  })
  test("fromString", () => {
    expect("MICRO"->CorporateEntity.CompanyLegalForm.fromString)->toStrictEqual(
      Ok(CorporateEntity.CompanyLegalForm.MICRO),
    )
    expect("XX"->CorporateEntity.CompanyLegalForm.fromString)->toStrictEqual(
      Error("Unknown legal form"),
    )
  })
})

test("CorporateName.validate", () => {
  expect(CorporateEntity.CorporateName.validate(""))->toStrictEqual(false)

  // Too short
  expect(CorporateEntity.CorporateName.validate("ab"))->toStrictEqual(false)

  // Only corporate prefix
  expect(CorporateEntity.CorporateName.validate("SA"))->toStrictEqual(false)
  expect(CorporateEntity.CorporateName.validate("SAS"))->toStrictEqual(false)
  expect(CorporateEntity.CorporateName.validate("SARL"))->toStrictEqual(false)
  expect(CorporateEntity.CorporateName.validate("EI"))->toStrictEqual(false)
  expect(CorporateEntity.CorporateName.validate("EIRL"))->toStrictEqual(false)
  expect(CorporateEntity.CorporateName.validate("EURL"))->toStrictEqual(false)
  expect(CorporateEntity.CorporateName.validate("MICRO"))->toStrictEqual(false)
  expect(CorporateEntity.CorporateName.validate("SNC"))->toStrictEqual(false)
  expect(CorporateEntity.CorporateName.validate("SCS"))->toStrictEqual(false)
  expect(CorporateEntity.CorporateName.validate("SCA"))->toStrictEqual(false)

  // Too short without corporate prefix / suffix
  expect(CorporateEntity.CorporateName.validate("EI ab"))->toStrictEqual(false)
  expect(CorporateEntity.CorporateName.validate("SARL ab"))->toStrictEqual(false)

  expect(CorporateEntity.CorporateName.validate("SARL Mon entreprise"))->toStrictEqual(true)
})
