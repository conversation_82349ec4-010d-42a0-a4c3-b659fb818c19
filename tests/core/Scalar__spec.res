open Vitest

describe("Datetime", () => {
  it("should serialize", () => {
    expect(
      Js.Date.makeWithYMD(~year=2021., ~month=11., ~date=4., ())->Scalar.Datetime.serialize,
    )->toBe("2021-12-04T00:00:00.000Z"->Json.encodeString)
    expect(
      Js.Date.makeWithYMDHMS(
        ~year=2021.,
        ~month=11.,
        ~date=4.,
        ~hours=20.,
        ~minutes=48.,
        ~seconds=24.,
        (),
      )->Scalar.Datetime.serialize,
    )->toBe("2021-12-04T20:48:24.000Z"->Json.encodeString)
    expect(() => "a"->Obj.magic->Scalar.Datetime.serialize)->toRaise(
      Scalar.Datetime.CannotSerializeDatetime,
    )
  })

  it("should parse", () => {
    expect("2021-12-04T20:48:24.000Z"->Json.encodeString->Scalar.Datetime.parse)->toStrictEqual(
      Js.Date.makeWithYMDHMS(
        ~year=2021.,
        ~month=11.,
        ~date=4.,
        ~hours=20.,
        ~minutes=48.,
        ~seconds=24.,
        (),
      ),
    )
    expect(() => "a"->Json.encodeString->Scalar.Datetime.parse)->toRaise(
      Scalar.Datetime.CannotParseDatetime,
    )
  })
})

describe("Text", () => {
  it("should serialize", () => {
    expect("test"->Scalar.Text.serialize)->toBe("test"->Json.encodeString)
    expect(() => 1->Obj.magic->Scalar.Text.serialize)->toRaise(Scalar.Text.CannotSerializeText)
    expect(() => true->Obj.magic->Scalar.Text.serialize)->toRaise(Scalar.Text.CannotSerializeText)
  })

  it("should parse", () => {
    expect("test"->Json.encodeString->Scalar.Text.parse)->toBe("test")
    expect(() => 1.->Json.encodeNumber->Scalar.Text.parse)->toRaise(Scalar.Text.CannotParseText)
    expect(() => true->Json.encodeBoolean->Scalar.Text.parse)->toRaise(Scalar.Text.CannotParseText)
  })
})

describe("CKU", () => {
  it("should serialize", () => {
    expect("ecabfe10-63b4-491d-a132-5baa1afbb203"->Scalar.CKU.serialize)->toBe(
      "ecabfe10-63b4-491d-a132-5baa1afbb203"->Json.encodeString,
    )
    expect(() => "3dc7dbbc-5542-11ec-bf63-0242ac130002"->Scalar.CKU.serialize)->toRaise(
      Scalar.CKU.CannotSerializeCku,
    )
    expect(() => "test"->Scalar.CKU.serialize)->toRaise(Scalar.CKU.CannotSerializeCku)
    expect(() => 1->Obj.magic->Scalar.CKU.serialize)->toRaise(Scalar.CKU.CannotSerializeCku)
    expect(() => true->Obj.magic->Scalar.CKU.serialize)->toRaise(Scalar.CKU.CannotSerializeCku)
  })

  it("should parse", () => {
    expect("ecabfe10-63b4-491d-a132-5baa1afbb203"->Json.encodeString->Scalar.CKU.parse)->toBe(
      "ecabfe10-63b4-491d-a132-5baa1afbb203",
    )
    expect(
      () => "3dc7dbbc-5542-11ec-bf63-0242ac130002"->Json.encodeString->Scalar.CKU.parse,
    )->toRaise(Scalar.CKU.CannotParseCku)
    expect(() => "test"->Json.encodeString->Scalar.CKU.parse)->toRaise(Scalar.CKU.CannotParseCku)
    expect(() => 1.->Json.encodeNumber->Scalar.CKU.parse)->toRaise(Scalar.CKU.CannotParseCku)
    expect(() => true->Json.encodeBoolean->Scalar.CKU.parse)->toRaise(Scalar.CKU.CannotParseCku)
  })
})
