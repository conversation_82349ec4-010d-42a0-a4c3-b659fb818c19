open Vitest
open TestingLibraryReact

afterEach(() => %raw(`global.localStorage.clear()`))

describe("store", () => {
  it("should store correctly the state within localStorage", () => {
    let spySetItem = spyOn1(%raw(`global.localStorage`), "setItem")

    expect(spySetItem)->toHaveBeenCalledTimes(0)

    let state = "mock-state"
    let preferenceKey = #"catalog-page-settings"
    let encoder = state => Json.encodeString(state)

    UserPreferences.store(state, ~key=preferenceKey, ~encoder)

    expect(spySetItem)->toHaveBeenCalledTimes(1)
    expect(spySetItem)->toHaveBeenCalledWith2("catalog-page-settings", "\"mock-state\"")
  })
})

describe("read", () => {
  it("should read correctly the state within localStorage", () => {
    let spyGetItem = spyOn1(%raw(`global.localStorage`), "getItem")

    expect(spyGetItem)->toHaveBeenCalledTimes(0)

    let state = 999.
    let preferenceKeyA = #"catalog-page-settings"
    let encoderA = state => Json.encodeNumber(state)
    let decoderA = state => Json.decodeNumber(state)->Option.mapWithDefault(Error(), ok => Ok(ok))

    UserPreferences.store(state, ~key=preferenceKeyA, ~encoder=encoderA)
    let result = UserPreferences.read(~key=preferenceKeyA, ~decoder=decoderA)

    expect(result)->toBe(Some(999.))
    expect(spyGetItem)->toHaveBeenCalledTimes(1)
    expect(spyGetItem)->toHaveBeenCalledWith1(#"catalog-page-settings")

    let state = true
    let preferenceKeyB = #"catalog-labels-options"
    let encoderB = state => Json.encodeBoolean(state)
    let decoderB = state => Json.decodeBoolean(state)->Option.mapWithDefault(Error(), ok => Ok(ok))

    UserPreferences.store(state, ~key=preferenceKeyB, ~encoder=encoderB)
    let result = UserPreferences.read(~key=preferenceKeyB, ~decoder=decoderB)

    expect(result)->toBe(Some(true))
    expect(spyGetItem)->toHaveBeenCalledTimes(2)
    expect(spyGetItem)->toHaveBeenCalledWith1(#"catalog-labels-options")

    let result = UserPreferences.read(~key=preferenceKeyA, ~decoder=decoderA)

    expect(result)->toBe(Some(999.))
    expect(spyGetItem)->toHaveBeenCalledTimes(3)
    expect(spyGetItem)->toHaveBeenCalledWith1(#"catalog-page-settings")
  })

  it("should not return any value when reading the state fails", () => {
    let spySetItem = spyOn1(%raw(`global.localStorage`), "setItem")
    let spyGetItem = spyOn1(%raw(`global.localStorage`), "getItem")
    let spyRemoveItem = spyOn1(%raw(`global.localStorage`), "removeItem")

    let state = "mock-state"
    let preferenceKey = #"catalog-page-settings"
    let encoder = state => Json.encodeString(state)
    let decoder = state => Json.decodeString(state)->Option.mapWithDefault(Error(), ok => Ok(ok))

    expect(spyGetItem)->toHaveBeenCalledTimes(0)

    UserPreferences.store(state, ~key=preferenceKey, ~encoder)

    expect(spySetItem)->toHaveBeenCalledWith2(#"catalog-page-settings", "\"mock-state\"")
    expect(spyGetItem)->toHaveBeenCalledTimes(0)
    expect(spyRemoveItem)->toHaveBeenCalledTimes(0)

    let preferenceKey = Obj.magic("mock-notfound-key")

    let result = UserPreferences.read(~key=preferenceKey, ~decoder)

    expect(result)->toBe(None)
    expect(spyGetItem)->toHaveBeenCalledTimes(1)
    expect(spyGetItem)->toHaveBeenCalledWith1("mock-notfound-key")
    expect(spyRemoveItem)->toHaveBeenCalledTimes(0)

    mockClear(spySetItem)
    mockClear(spyGetItem)
    mockClear(spyRemoveItem)

    let state = "mock-state"
    let preferenceKey = #"catalog-page-settings"
    let encoder = state => Json.encodeString(state)
    let decoder = state => Json.decodeNumber(state)->Option.mapWithDefault(Error(), ok => Ok(ok))

    UserPreferences.store(state, ~key=preferenceKey, ~encoder)

    expect(spyGetItem)->toHaveBeenCalledTimes(0)
    expect(spyRemoveItem)->toHaveBeenCalledTimes(0)
    expect(spySetItem)->toHaveBeenCalledWith2(#"catalog-page-settings", "\"mock-state\"")

    let result = UserPreferences.read(~key=preferenceKey, ~decoder)

    expect(result)->toBe(None)
    expect(spyGetItem)->toHaveBeenCalledTimes(1)
    expect(spyGetItem)->toHaveBeenCalledWith1("catalog-page-settings")
    expect(spyRemoveItem)->toHaveBeenCalledTimes(1)
    expect(spyRemoveItem)->toHaveBeenCalledWith1("catalog-page-settings")
  })
})

describe("useRead", () => {
  it("should read the state only once at mounting", () => {
    let spyGetItem = spyOn1(%raw(`global.localStorage`), "getItem")

    let state = "mock-state"
    let preferenceKey = #"catalog-page-settings"
    let encoder = state => Json.encodeString(state)
    let decoder = state => Json.decodeString(state)->Option.mapWithDefault(Error(), ok => Ok(ok))

    UserPreferences.store(state, ~key=preferenceKey, ~encoder)

    expect(spyGetItem)->toHaveBeenCalledTimes(0)

    let {result, rerender} = renderHook(() => UserPreferences.useRead(~key=preferenceKey, ~decoder))

    expect(result.current)->toBe(Some("mock-state"))
    expect(spyGetItem)->toHaveBeenCalledTimes(1)

    let state = "mock-new-state"
    UserPreferences.store(state, ~key=preferenceKey, ~encoder)

    rerender()

    expect(result.current)->toBe(Some("mock-state"))
    expect(spyGetItem)->toHaveBeenCalledTimes(1)
  })
})

describe("usePersistOnChange", () => {
  let state = "mock-initial-state"
  let preferenceKey = #"catalog-page-settings"
  let encoder = state => Json.encodeString(state)
  let decoder = state => Json.decodeString(state)->Option.mapWithDefault(Error(), ok => Ok(ok))

  it("should read an initial value already stored", () => {
    let spySetItem = spyOn1(%raw(`global.localStorage`), "setItem")

    expect(spySetItem)->toHaveBeenCalledTimes(0)

    UserPreferences.store(state, ~key=preferenceKey, ~encoder)

    let {result} = renderHook(
      state =>
        UserPreferences.usePersistOnChange(state, ~key=preferenceKey, ~encoder, ~decoder, ()),
    )

    let persisted = result.current
    expect(persisted)->toBe(true)
    expect(spySetItem)->toHaveBeenCalledTimes(1)
    expect(spySetItem)->toHaveBeenCalledWith2(#"catalog-page-settings", "\"mock-initial-state\"")
  })

  itPromise("should store the state whenever its value changes", async () => {
    let spySetItem = spyOn1(%raw(`global.localStorage`), "setItem")

    expect(spySetItem)->toHaveBeenCalledTimes(0)

    let {result, rerender} = renderHookWithOptions(
      state =>
        UserPreferences.usePersistOnChange(
          state,
          ~key=preferenceKey,
          ~encoder,
          ~decoder,
          ~debounceDelay=500,
          (),
        ),
      ~options={initialProps: "mock-initial-state"},
    )

    let persisted = result.current
    expect(persisted)->toBe(false)
    expect(spySetItem)->toHaveBeenCalledTimes(0)

    let newState = "mock-new-state"
    rerender(newState)
    rerender(newState)
    rerender(newState)

    let start = Js.Date.now()

    let persisted = result.current
    expect(persisted)->toBe(false)
    expect(spySetItem)->toHaveBeenCalledTimes(0)

    await waitFor(() => expect(result.current)->toBe(true))

    let end = Js.Date.now()
    let delay = end -. start

    expect(spySetItem)->toHaveBeenCalledTimes(1)
    expect(delay)->toBeGreaterThanOrEqual(500.)
    expect(delay)->toBeLessThan(550.)
  })

  itPromise("should not store the state when it's disabled", async () => {
    let spySetItem = spyOn1(%raw(`global.localStorage`), "setItem")

    expect(spySetItem)->toHaveBeenCalledTimes(0)

    let {result, rerender} = renderHookWithOptions(
      ((state, disabled)) =>
        UserPreferences.usePersistOnChange(
          state,
          ~key=preferenceKey,
          ~encoder,
          ~decoder,
          ~disabled,
          (),
        ),
      ~options={initialProps: ("mock-initial-state", false)},
    )

    expect(spySetItem)->toHaveBeenCalledTimes(0)

    let newState = "mock-state"
    let disabled = false
    rerender((newState, disabled))

    let persisted = result.current
    expect(persisted)->toBe(false)
    expect(spySetItem)->toHaveBeenCalledTimes(0)

    await waitFor(() => expect(result.current)->toBe(true))

    expect(spySetItem)->toHaveBeenCalledTimes(1)

    let newState = "mock-new-state"
    let disabled = true
    rerender((newState, disabled))

    let persisted = result.current
    expect(persisted)->toBe(false)
    expect(spySetItem)->toHaveBeenCalledTimes(1)

    await waitFor(() => expect(result.current)->toBe(true))

    expect(spySetItem)->toHaveBeenCalledTimes(1)
  })
})
