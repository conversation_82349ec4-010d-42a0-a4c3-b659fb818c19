open Vitest

let {set: setJwt, remove: removeJwt} = module(Auth__Jwt)

let mockedEndpoint = "https://some.api/"
let mockedResponseJson = {"data": {"abc": 123.}}

let {setupNodeServer, use, listen, resetHandlers, close} = module(MSW)
let {ctxUnsafeObject, ctxStatus, ctxText, post, asyncPost, postWithStatus, get} = module(MSW.Rest)

let server = setupNodeServer([])

beforeAll(() => server->listen({onUnhandledRequest: #warn}))
beforeEach(() => setJwt("token", ()))
afterEach(() => server->resetHandlers)
afterAll(() => server->close)

test("decodeInvalidRequestFailures", () => {
  expect(
    Request.decodeInvalidRequestFailures([
      Json.fromObjExn({
        "message": "",
        "unknwoProp": true,
        "data": Js.Obj.empty(),
      }),
    ]),
  )->toStrictEqual(None)

  expect(
    Request.decodeInvalidRequestFailures([
      Json.fromObjExn({
        "message": "Error message mock",
        "kind": "SomeErrorMock",
        "data": {"url": "mock-url"},
      }),
    ]),
  )->toStrictEqual(
    Some([
      {
        Request.message: "Error message mock",
        kind: "SomeErrorMock",
        data: Some(Json.fromObjExn({"url": "mock-url"})),
      },
    ]),
  )
})

test("decodeResult", () => {
  let {decodeResult} = module(Request)

  expect(decodeResult(Json.fromObjExn({"data": Json.encodedNull})))->toStrictEqual(
    Ok(Some(Json.fromObjExn(Js.null))),
  )

  expect(decodeResult(Json.fromObjExn({"data": {"url": "https://wino.fr"}})))->toStrictEqual(
    Ok(
      Some(
        Json.fromObjExn({
          "url": "https://wino.fr",
        }),
      ),
    ),
  )

  expect(decodeResult(Json.fromObjExn({"data": []})))->toStrictEqual(Ok(Some(Json.fromObjExn([]))))

  expect(
    decodeResult(
      Json.fromObjExn({
        "errors": [
          {
            "message": "incomplete provided file data in url",
            "kind": "PartialProcessError",
            "data": Js.Obj.empty(),
          },
        ],
        "data": {"url": "mock-url"},
      }),
    ),
  )->toStrictEqual(Error(None))

  expect(
    decodeResult(
      Json.fromObjExn({
        "errors": [
          {
            "message": "error-a",
            "kind": "TestError",
            "data": None,
          },
          {
            "message": "error-b",
            "kind": "TestError",
            "data": None,
          },
        ],
      }),
    ),
  )->toStrictEqual(
    Error(
      Some([
        {
          Request.message: "error-a",
          kind: "TestError",
          data: None,
        },
        {
          message: "error-b",
          kind: "TestError",
          data: None,
        },
      ]),
    ),
  )

  expect(
    decodeResult(
      Json.fromObjExn({
        "errors": [
          {
            "message": "could not provide result",
            "kind": "FatalError",
            "data": None,
          },
        ],
      }),
    ),
  )->toStrictEqual(
    Error(
      Some([
        {
          Request.message: "could not provide result",
          kind: "FatalError",
          data: None,
        },
      ]),
    ),
  )

  expect(decodeResult(Json.fromObjExn(Js.Obj.empty())))->toStrictEqual(Error(None))
})

itFuture("should call Request.make(_) with a GET request and return Ok(_)", () => {
  let bodyJson = Json.fromObjExn({"a": "b"})

  server->use([
    asyncPost(mockedEndpoint, async (req, res, ctx) => {
      expect(await req->MSW.Rest.Request.json)->toStrictEqual(bodyJson)
      res(ctx->ctxUnsafeObject(mockedResponseJson))
    }),
  ])

  let request = Request.make(mockedEndpoint, ~method=#POST, ~bodyJson)

  request->Future.tap(result => expect(result)->toStrictEqual(Ok(Json.fromObjExn({"abc": 123.}))))
})

itFuture("should call Request.make(_) with a POST request and return Ok(_)", () => {
  server->use([get(mockedEndpoint, (_, res, ctx) => res(ctx->ctxUnsafeObject(mockedResponseJson)))])

  let request = Request.make(mockedEndpoint, ~method=#GET)

  request->Future.tap(result => expect(result)->toStrictEqual(Ok(Json.fromObjExn({"abc": 123.}))))
})

itFuture(
  "should call Request.make(_, ~authTokenRequired=true) with missing JWT and return Ok(_)",
  () => {
    removeJwt()

    server->use([
      post(mockedEndpoint, (_, res, ctx) => res(ctx->ctxUnsafeObject(mockedResponseJson))),
    ])

    let request = Request.make(mockedEndpoint, ~method=#POST, ~authTokenRequired=false)

    request->Future.tap(result =>
      expect(result)->toUnsafeStrictEqual(Ok(Json.fromObjExn({"abc": 123.})))
    )
  },
)

itFuture("should call Request.make(_) and return Error(ClientError(_))", () => {
  let bodyJson = Json.fromObjExn({"a": "b"})

  server->use([get(mockedEndpoint, (_, res, ctx) => res(ctx->ctxUnsafeObject(mockedResponseJson)))])

  let request = Request.make(mockedEndpoint, ~method=#GET, ~bodyJson)

  request->Future.tap(result =>
    expect(result)->toStrictEqual(
      Error(ClientError("Request with GET/HEAD method cannot have body.")),
    )
  )
})

itFuture("should call Request.make(_) and return Error(UnexpectedServerError)", () => {
  server->use([post(mockedEndpoint, (_req, res, ctx) => res(ctx->ctxStatus(500)))])
  let request = Request.make(mockedEndpoint, ~method=#POST)
  request->Future.tap(result => expect(result)->toStrictEqual(Error(UnexpectedServerError)))
})

itFuture(
  "should call Request.make(_) with status=401 and return Error(JwtAuthenticationRedirection)",
  () => {
    server->use([post(mockedEndpoint, (_req, res, ctx) => res(ctx->ctxStatus(401)))])
    let request = Request.make(mockedEndpoint, ~method=#POST)
    request->Future.tap(result =>
      expect(result)->toStrictEqual(Error(JwtAuthenticationRedirection))
    )
  },
)

itFuture(
  "should call Request.make(_) with missing jwt and return Error(JwtAuthenticationRedirection)",
  () => {
    removeJwt()
    let request = Request.make(mockedEndpoint, ~method=#POST)
    request->Future.tap(result =>
      expect(result)->toStrictEqual(Error(JwtAuthenticationRedirection))
    )
  },
)

itFuture(
  "should call Request.make(_) with status=500 and return Error(InvalidRequestFailures(_))",
  () => {
    let mockedResponseFailuresJson = {
      "errors": [
        {"message": "mock-failure-message-1", "kind": "MockFailureKind"},
        {"message": "mock-failure-message-2", "kind": "MockFailureKind"},
      ],
    }

    server->use([
      postWithStatus(mockedEndpoint, (_, res, ctx) =>
        res(. ctx->ctxStatus(500), ctx->ctxUnsafeObject(mockedResponseFailuresJson))
      ),
    ])

    let request = Request.make(
      mockedEndpoint,
      ~method=#POST,
      ~bodyJson=Json.encodeDict(Js.Dict.empty()),
    )

    request->Future.tap(result =>
      expect(result)->toStrictEqual(
        Error(
          InvalidRequestFailures([
            {
              message: "mock-failure-message-1",
              kind: "MockFailureKind",
              data: None,
            },
            {
              message: "mock-failure-message-2",
              kind: "MockFailureKind",
              data: None,
            },
          ]),
        ),
      )
    )
  },
)

itFuture(
  "should call Request.make(_) with status=400 and return Error(InvalidRequestFailures(_))",
  () => {
    let mockedResponseFailuresJson = {
      "errors": [{"message": "mock-failure-message", "kind": "MockFailureKind"}],
    }

    server->use([
      postWithStatus(mockedEndpoint, (_, res, ctx) =>
        res(. ctx->ctxStatus(400), ctx->ctxUnsafeObject(mockedResponseFailuresJson))
      ),
    ])

    let request = Request.make(
      mockedEndpoint,
      ~method=#POST,
      ~bodyJson=Json.encodeDict(Js.Dict.empty()),
    )

    request->Future.tap(result =>
      expect(result)->toStrictEqual(
        Error(
          InvalidRequestFailures([
            {
              message: "mock-failure-message",
              kind: "MockFailureKind",
              data: None,
            },
          ]),
        ),
      )
    )
  },
)

itFuture(
  "should call Request.make(_) with status=500&text='fatal' and return Error(ServerError(_))",
  () => {
    server->use([
      postWithStatus(mockedEndpoint, (_, res, ctx) =>
        res(. ctx->ctxStatus(500), ctx->ctxText("fatal"))
      ),
    ])

    let request = Request.make(
      mockedEndpoint,
      ~method=#POST,
      ~bodyJson=Json.encodeDict(Js.Dict.empty()),
    )

    request->Future.tap(result => expect(result)->toStrictEqual(Error(ServerError("fatal"))))
  },
)

itFuture(
  "should call Request.make(_) with status=400&text='fatal' and return Error(UnexpectedServerError)",
  () => {
    server->use([
      postWithStatus(mockedEndpoint, (_, res, ctx) =>
        res(. ctx->ctxStatus(400), ctx->ctxText("fatal"))
      ),
    ])

    let request = Request.make(
      mockedEndpoint,
      ~method=#POST,
      ~bodyJson=Json.encodeDict(Js.Dict.empty()),
    )

    request->Future.tap(result => expect(result)->toStrictEqual(Error(UnexpectedServerError)))
  },
)

itFuture(
  "should call Request.make(_) with text response and return Error(UnexpectedServerError)",
  () => {
    server->use([get(mockedEndpoint, (_, res, ctx) => res(ctx->ctxText("mockedResponseJson")))])

    let request = Request.make(mockedEndpoint, ~method=#GET)

    request->Future.tap(result => expect(result)->toStrictEqual(Error(UnexpectedServerError)))
  },
)

itFuture(
  "should call Request.make(_) with invalid data response and return Error(MalformedResponse)",
  () => {
    server->use([get(mockedEndpoint, (_, res, ctx) => res(ctx->ctxUnsafeObject({"abd": 123.})))])

    let request = Request.make(mockedEndpoint, ~method=#GET)

    request->Future.tap(result => expect(result)->toStrictEqual(Error(MalformedResponse)))
  },
)

itFuture(
  "should call Request.make(_, ~skipMalformedOkResult=true) with invalid data response and return Ok(_)",
  () => {
    server->use([get(mockedEndpoint, (_, res, ctx) => res(ctx->ctxUnsafeObject({"abd": 123.})))])

    let request = Request.make(mockedEndpoint, ~method=#GET, ~skipMalformedOkResult=true)

    request->Future.tap(result => expect(result)->toStrictEqual(Ok(Json.fromObjExn({"abd": 123.}))))
  },
)

let sleep = delay =>
  Js.Promise.make((~resolve, ~reject as _) =>
    Js.Global.setTimeout(() => resolve(. ()), delay)->ignore
  )

itPromise("should abort request", async () => {
  let spy = fn0()

  server->use([
    asyncPost(mockedEndpoint, async (_, res, ctx) => {
      await sleep(1000)
      spy->fn()
      res(ctx->ctxUnsafeObject({"abd": 123.}))
    }),
  ])

  let request = Request.make(mockedEndpoint, ~method=#POST, ~skipMalformedOkResult=true)
  expect(request->Future.isPending)->toBe(true)
  expect(request->Future.isCancelled)->toBe(false)
  expect(spy)->toHaveBeenCalledTimes(0)

  request->Future.cancel
  expect(request->Future.isPending)->toBe(false)
  expect(request->Future.isCancelled)->toBe(true)
  expect(spy)->toHaveBeenCalledTimes(0)

  await sleep(1500)

  request->Future.get(_ => assert false)
  expect(spy)->toHaveBeenCalledTimes(1)
  expect(request->Future.isPending)->toBe(false)
  expect(request->Future.isCancelled)->toBe(true)
})
