open Vitest
open TestingLibraryReact

test("defaultEdgesPerPage", () => {
  let {defaultEdgesPerPage} = module(ResourceList)

  expect(defaultEdgesPerPage)->toBe(50)
})

test("totalPages", () => {
  let {totalPages} = module(ResourceList)

  expect(totalPages(0, 8))->toBe(1)
  expect(totalPages(-1, 1))->toBe(1)
  expect(totalPages(1, -1))->toBe(1)
  expect(totalPages(-1, 0))->toBe(1)
  expect(totalPages(0, -1))->toBe(1)
  expect(totalPages(0, 0))->toBe(1)
  expect(totalPages(20, 8))->toBe(3)
  expect(totalPages(103, 7))->toBe(15)
  expect(totalPages(2, 1))->toBe(2)
  expect(totalPages(1, 1))->toBe(1)
  expect(totalPages(1, 2))->toBe(1)
  expect(totalPages(2, 2))->toBe(1)
  expect(totalPages(8, 20))->toBe(1)
})

module TestableFilters = {
  type t = {slug?: string, name?: string}
}
module TestableExtra = {
  type t = {custom?: string}
}

test("initialState", () => {
  let {initialState} = module(ResourceList)

  let filters: TestableFilters.t = {}
  let sorting = {ReactStately.Table.column: "", direction: #ascending}
  expect(initialState(~first=50, ~filters, ~sorting, ()))->toEqual({
    currentPage: 1,
    previousPage: -1,
    filters: {},
    sorting: {column: "", direction: #ascending},
    connectionArguments: {first: 50},
  })

  let filters = {TestableFilters.slug: "mock-slug"}
  let sorting = {ReactStately.Table.column: "mock-column", direction: #ascending}
  expect(initialState(~first=50, ~filters, ~sorting, ()))->toEqual({
    currentPage: 1,
    previousPage: -1,
    filters: {slug: "mock-slug"},
    sorting: {column: "mock-column", direction: #ascending},
    connectionArguments: {first: 50},
  })

  let filters = {TestableFilters.slug: "mock-slug", name: "mock-name"}
  let sorting = {ReactStately.Table.column: "mock-column", direction: #descending}
  let extra = {TestableExtra.custom: "mock-custom"}
  expect(initialState(~filters, ~sorting, ~extra, ()))->toEqual({
    currentPage: 1,
    previousPage: -1,
    filters: {slug: "mock-slug", name: "mock-name"},
    sorting: {column: "mock-column", direction: #descending},
    extra: {custom: "mock-custom"},
    connectionArguments: {first: 50},
  })
})

describe("reducer", () => {
  let {reducer, initialState} = module(ResourceList)

  let filters: TestableFilters.t = {}
  let sorting = {ReactStately.Table.column: "", direction: #ascending}
  let initialState = initialState(~filters, ~sorting, ())
  let fulfilledState = {
    ResourceList.currentPage: 2,
    searchQuery: "mock-search-query",
    previousPage: 1,
    filters: {TestableFilters.slug: "mock-slug", name: "mock-name"},
    sorting: {ReactStately.Table.column: "mock-column", direction: #descending},
    connectionArguments: {first: 50, before: "mock-cursor", after: "mock-cursor", last: 10},
  }

  test("Navigated", () => {
    expect(
      reducer(initialState, Navigated({nextPage: 2, totalCount: 4, cursors: (Some(""), Some(""))})),
    )->toEqual({
      currentPage: 2,
      previousPage: 1,
      connectionArguments: {first: 50, after: ""},
      filters: {},
      sorting: {column: "", direction: #ascending},
    })
    expect(
      reducer(initialState, Navigated({nextPage: 2, totalCount: 4, cursors: (None, None)})),
    )->toEqual({
      currentPage: 1,
      previousPage: -1,
      connectionArguments: {first: 50},
      filters: {},
      sorting: {column: "", direction: #ascending},
    })
    expect(
      reducer(initialState, Navigated({nextPage: 5, totalCount: 4, cursors: (Some(""), Some(""))})),
    )->toEqual({
      currentPage: 1,
      previousPage: -1,
      connectionArguments: {first: 50},
      filters: {},
      sorting: {column: "", direction: #ascending},
    })
    expect(
      reducer(
        fulfilledState,
        Navigated({nextPage: 2, totalCount: 4, cursors: (Some(""), Some(""))}),
      ),
    )->toEqual({
      searchQuery: "mock-search-query",
      currentPage: 2,
      previousPage: 1,
      connectionArguments: {first: 50, before: "mock-cursor", after: "mock-cursor", last: 10},
      filters: {name: "mock-name", slug: "mock-slug"},
      sorting: {column: "mock-column", direction: #descending},
    })
  })

  test("Searched", () => {
    expect(reducer(initialState, Searched("my search query")))->toEqual({
      searchQuery: "my search query",
      currentPage: 1,
      previousPage: -1,
      connectionArguments: {first: 50},
      filters: {},
      sorting: {column: "", direction: #ascending},
    })
    expect(reducer(fulfilledState, Searched("my search query")))->toEqual({
      searchQuery: "my search query",
      currentPage: 1,
      previousPage: -1,
      connectionArguments: {first: 50},
      filters: {TestableFilters.slug: "mock-slug", name: "mock-name"},
      sorting: {column: "mock-column", direction: #descending},
    })
  })

  todo("FiltersUpdated")
  todo("SortingUpdated")
  todo("ExtraParamsUpdated")
  todo("Reset")
})

todo("useQueryStringPersistReducer")

test("use", () => {
  let {use, initialState} = module(ResourceList)

  let history = History.createMemoryHistory()
  let wrapper = props => <Providers history> {props["children"]} </Providers>
  let testableHook = hookCallback =>
    renderHookWithOptions(hookCallback, ~options={wrapper: wrapper})

  let filters = {TestableFilters.slug: "mock-slug", name: "mock-name"}
  let filtersJsonCodec = JsonCodec.object2(
    ({TestableFilters.name: ?name, ?slug}) => (name, slug),
    ((name, slug)) => Ok({?name, ?slug}),
    JsonCodec.field("name", JsonCodec.string)->JsonCodec.optional,
    JsonCodec.field("slug", JsonCodec.string)->JsonCodec.optional,
  )

  let sorting = {ReactStately.Table.column: "mock-column", direction: #ascending}
  let sortingJsonCodec = JsonCodec.object2(
    (sort: ReactStately.Table.sortDescriptor) => (sort.column, (sort.direction :> string)),
    ((column, direction)) => {
      let direction = direction === "descending" ? #descending : #ascending
      Ok({ReactStately.Table.column, direction})
    },
    JsonCodec.field("column", JsonCodec.string),
    JsonCodec.field("direction", JsonCodec.string),
  )
  let initialState = initialState(~filters, ~sorting, ())

  let hookResult = testableHook(() => use(~initialState, ~filtersJsonCodec, ~sortingJsonCodec, ()))
  let (state, dispatch) = hookResult.result.current

  expect(state)->toEqual({
    currentPage: 1,
    previousPage: -1,
    filters: {slug: "mock-slug", name: "mock-name"},
    sorting: {column: "mock-column", direction: #ascending},
    connectionArguments: {first: 50},
  })
  expect(history.location.search)->toBe(
    "?page=1&previousPage=-1&filters[name]=%22mock-name%22&filters[slug]=%22mock-slug%22&sorting[column]=%22mock-column%22&sorting[direction]=%22ascending%22&first=50",
  )

  act(() =>
    dispatch(Navigated({nextPage: 2, totalCount: 99, cursors: (None, Some("mock-end-cursor"))}))
  )

  let (state, _) = hookResult.result.current

  expect(state)->toEqual({
    currentPage: 2,
    previousPage: 1,
    filters: {slug: "mock-slug", name: "mock-name"},
    sorting: {column: "mock-column", direction: #ascending},
    connectionArguments: {first: 50, after: "mock-end-cursor"},
  })
  expect(history.location.search)->toBe(
    "?page=2&previousPage=1&filters[name]=%22mock-name%22&filters[slug]=%22mock-slug%22&sorting[column]=%22mock-column%22&sorting[direction]=%22ascending%22&first=50&after=%22mock-end-cursor%22",
  )

  act(() => dispatch(Searched("my search query")))

  let (state, _) = hookResult.result.current

  expect(state)->toEqual({
    currentPage: 1,
    previousPage: -1,
    searchQuery: "my search query",
    filters: {slug: "mock-slug", name: "mock-name"},
    sorting: {column: "mock-column", direction: #ascending},
    connectionArguments: {first: 50},
  })
  expect(history.location.search)->toBe(
    "?page=1&previousPage=-1&search=%22my%20search%20query%22&filters[name]=%22mock-name%22&filters[slug]=%22mock-slug%22&sorting[column]=%22mock-column%22&sorting[direction]=%22ascending%22&first=50",
  )

  act(() => dispatch(FiltersUpdated(prevFilters => {...prevFilters, name: "new-name"})))

  let (state, _) = hookResult.result.current

  expect(state)->toEqual({
    currentPage: 1,
    previousPage: -1,
    searchQuery: "my search query",
    filters: {slug: "mock-slug", name: "new-name"},
    sorting: {column: "mock-column", direction: #ascending},
    connectionArguments: {first: 50},
  })
  expect(history.location.search)->toBe(
    "?page=1&previousPage=-1&search=%22my%20search%20query%22&filters[name]=%22new-name%22&filters[slug]=%22mock-slug%22&sorting[column]=%22mock-column%22&sorting[direction]=%22ascending%22&first=50",
  )

  act(() =>
    dispatch(Navigated({nextPage: 2, totalCount: 99, cursors: (None, Some("mock-end-cursor"))}))
  )

  let (state, _) = hookResult.result.current

  expect(state)->toEqual({
    currentPage: 2,
    previousPage: 1,
    searchQuery: "my search query",
    filters: {slug: "mock-slug", name: "new-name"},
    sorting: {column: "mock-column", direction: #ascending},
    connectionArguments: {first: 50, after: "mock-end-cursor"},
  })
  expect(history.location.search)->toBe(
    "?page=2&previousPage=1&search=%22my%20search%20query%22&filters[name]=%22new-name%22&filters[slug]=%22mock-slug%22&sorting[column]=%22mock-column%22&sorting[direction]=%22ascending%22&first=50&after=%22mock-end-cursor%22",
  )

  act(() => dispatch(FiltersUpdated(prevFilters => prevFilters)))

  let (state, _) = hookResult.result.current

  expect(state)->toEqual({
    currentPage: 1,
    previousPage: -1,
    searchQuery: "my search query",
    filters: {slug: "mock-slug", name: "new-name"},
    sorting: {column: "mock-column", direction: #ascending},
    connectionArguments: {first: 50},
  })
  expect(history.location.search)->toBe(
    "?page=1&previousPage=-1&search=%22my%20search%20query%22&filters[name]=%22new-name%22&filters[slug]=%22mock-slug%22&sorting[column]=%22mock-column%22&sorting[direction]=%22ascending%22&first=50",
  )

  act(() => dispatch(Searched("my new search query")))

  let (state, _) = hookResult.result.current

  expect(state)->toEqual({
    currentPage: 1,
    previousPage: -1,
    searchQuery: "my new search query",
    filters: {slug: "mock-slug", name: "new-name"},
    sorting: {column: "mock-column", direction: #ascending},
    connectionArguments: {first: 50},
  })
  expect(history.location.search)->toBe(
    "?page=1&previousPage=-1&search=%22my%20new%20search%20query%22&filters[name]=%22new-name%22&filters[slug]=%22mock-slug%22&sorting[column]=%22mock-column%22&sorting[direction]=%22ascending%22&first=50",
  )

  act(() =>
    dispatch(Navigated({nextPage: 8, totalCount: 99, cursors: (None, Some("mock-end-cursor"))}))
  )

  let (state, _) = hookResult.result.current

  expect(state)->toEqual({
    currentPage: 1,
    previousPage: -1,
    searchQuery: "my new search query",
    filters: {slug: "mock-slug", name: "new-name"},
    sorting: {column: "mock-column", direction: #ascending},
    connectionArguments: {first: 50},
  })
  expect(history.location.search)->toBe(
    "?page=1&previousPage=-1&search=%22my%20new%20search%20query%22&filters[name]=%22new-name%22&filters[slug]=%22mock-slug%22&sorting[column]=%22mock-column%22&sorting[direction]=%22ascending%22&first=50",
  )

  act(() =>
    dispatch(Navigated({nextPage: 1, totalCount: 99, cursors: (Some("mock-start-cursor"), None)}))
  )

  let (state, _) = hookResult.result.current

  expect(state)->toEqual({
    currentPage: 1,
    previousPage: 1,
    searchQuery: "my new search query",
    filters: {slug: "mock-slug", name: "new-name"},
    sorting: {column: "mock-column", direction: #ascending},
    connectionArguments: {first: 50},
  })
  expect(history.location.search)->toBe(
    "?page=1&previousPage=1&search=%22my%20new%20search%20query%22&filters[name]=%22new-name%22&filters[slug]=%22mock-slug%22&sorting[column]=%22mock-column%22&sorting[direction]=%22ascending%22&first=50",
  )

  act(() => dispatch(Searched("")))

  let (state, _) = hookResult.result.current

  let none = Option.isNone(None) ? None : Some("") // NOTE — workaround solution to fix "too" optimized js output
  expect(state)->toEqual({
    currentPage: 1,
    previousPage: -1,
    filters: {slug: "mock-slug", name: "new-name"},
    sorting: {column: "mock-column", direction: #ascending},
    connectionArguments: {first: 50},
    searchQuery: ?none,
  })
  expect(history.location.search)->toBe(
    "?page=1&previousPage=-1&filters[name]=%22new-name%22&filters[slug]=%22mock-slug%22&sorting[column]=%22mock-column%22&sorting[direction]=%22ascending%22&first=50",
  )

  act(() => dispatch(SortingUpdated({column: "new-column", direction: #descending})))

  let (state, _) = hookResult.result.current

  let none = Option.isNone(None) ? None : Some("") // NOTE — workaround solution to fix "too" optimized js output
  expect(state)->toEqual({
    currentPage: 1,
    previousPage: -1,
    filters: {slug: "mock-slug", name: "new-name"},
    sorting: {column: "new-column", direction: #descending},
    connectionArguments: {first: 50},
    searchQuery: ?none,
  })
  expect(history.location.search)->toBe(
    "?page=1&previousPage=-1&filters[name]=%22new-name%22&filters[slug]=%22mock-slug%22&sorting[column]=%22new-column%22&sorting[direction]=%22descending%22&first=50",
  )
})

type filters = {mock?: string}
let mockStateFilters = () => {mock: ""}
type sorting = {mock?: string}
let mockStateSorting = () => {mock: ""}
let mockState = (
  ~filters=mockStateFilters(),
  ~sorting=mockStateSorting(),
  ~extra=?,
  ~currentPage=1,
  ~previousPage=-1,
  (),
) => {
  ResourceList.currentPage,
  previousPage,
  filters,
  sorting,
  extra,
  connectionArguments: {first: 50},
}

describe("nextPage", () => {
  let {nextPage} = module(ResourceList)

  it("should go Next", () => {
    let action = ResourceListPagination.Next

    expect(nextPage(~totalPages=0, ~state=mockState(~currentPage=0, ()), ~action))->toBeNone
    expect(nextPage(~totalPages=0, ~state=mockState(~currentPage=1, ()), ~action))->toBeNone
    expect(nextPage(~totalPages=1, ~state=mockState(~currentPage=0, ()), ~action))->toBeNone
    expect(nextPage(~totalPages=0, ~state=mockState(~currentPage=1, ()), ~action))->toBeNone
    expect(nextPage(~totalPages=1, ~state=mockState(~currentPage=1, ()), ~action))->toBeNone
    expect(nextPage(~totalPages=2, ~state=mockState(~currentPage=1, ()), ~action))->toBe(Some(2))
    expect(nextPage(~totalPages=2, ~state=mockState(~currentPage=1, ()), ~action))->toBe(Some(2))
    expect(nextPage(~totalPages=10, ~state=mockState(~currentPage=1, ()), ~action))->toBe(Some(2))
    expect(nextPage(~totalPages=2, ~state=mockState(~currentPage=3, ()), ~action))->toBe(Some(2))
    expect(nextPage(~totalPages=10, ~state=mockState(~currentPage=10, ()), ~action))->toBeNone
  })

  it("should go Prev", () => {
    let action = ResourceListPagination.Prev

    expect(nextPage(~totalPages=0, ~state=mockState(~currentPage=0, ()), ~action))->toBeNone
    expect(nextPage(~totalPages=0, ~state=mockState(~currentPage=1, ()), ~action))->toBeNone
    expect(nextPage(~totalPages=1, ~state=mockState(~currentPage=0, ()), ~action))->toBeNone
    expect(nextPage(~totalPages=1, ~state=mockState(~currentPage=1, ()), ~action))->toBeNone
    expect(nextPage(~totalPages=1, ~state=mockState(~currentPage=2, ()), ~action))->toBe(Some(1))
    expect(nextPage(~totalPages=2, ~state=mockState(~currentPage=2, ()), ~action))->toBe(Some(1))
    expect(nextPage(~totalPages=90, ~state=mockState(~currentPage=2, ()), ~action))->toBe(Some(1))
    expect(nextPage(~totalPages=90, ~state=mockState(~currentPage=95, ()), ~action))->toBe(Some(90))
    expect(nextPage(~totalPages=90, ~state=mockState(~currentPage=92, ()), ~action))->toBe(Some(90))
    expect(nextPage(~totalPages=90, ~state=mockState(~currentPage=91, ()), ~action))->toBe(Some(90))
    expect(nextPage(~totalPages=90, ~state=mockState(~currentPage=90, ()), ~action))->toBe(Some(89))
  })

  it("should go First", () => {
    let action = ResourceListPagination.First

    expect(nextPage(~totalPages=1, ~state=mockState(~currentPage=1, ()), ~action))->toBeNone
    expect(nextPage(~totalPages=10, ~state=mockState(~currentPage=1, ()), ~action))->toBe(Some(1))
    expect(nextPage(~totalPages=8, ~state=mockState(~currentPage=4, ()), ~action))->toBe(Some(1))
    expect(nextPage(~totalPages=8, ~state=mockState(~currentPage=8, ()), ~action))->toBe(Some(1))
  })

  it("should go Last", () => {
    let action = ResourceListPagination.Last

    expect(nextPage(~totalPages=1, ~state=mockState(~currentPage=1, ()), ~action))->toBeNone
    expect(nextPage(~totalPages=10, ~state=mockState(~currentPage=1, ()), ~action))->toBe(Some(10))
  })
})

test("connectionArguments", () => {
  let {connectionArguments} = module(ResourceList)

  expect(
    connectionArguments(~currentPage=1, ~previousPage=-1, ~totalCount=0, ~cursors=(None, None), ()),
  )->toStrictEqual(Some({first: 50}))

  expect(
    connectionArguments(~currentPage=1, ~previousPage=-1, ~totalCount=1, ~cursors=(None, None), ()),
  )->toStrictEqual(Some({first: 50}))

  expect(
    connectionArguments(~currentPage=1, ~previousPage=-1, ~totalCount=9, ~cursors=(None, None), ()),
  )->toStrictEqual(Some({first: 50}))

  expect(
    connectionArguments(
      ~currentPage=1,
      ~previousPage=-1,
      ~totalCount=100,
      ~cursors=(None, None),
      (),
    ),
  )->toStrictEqual(Some({first: 50}))

  expect(
    connectionArguments(
      ~currentPage=2,
      ~previousPage=1,
      ~totalCount=100,
      ~cursors=(None, None),
      (),
    ),
  )->toStrictEqual(Some({last: 50}))

  expect(
    connectionArguments(
      ~currentPage=1,
      ~previousPage=-1,
      ~totalCount=0,
      ~cursors=(Some("mock-start-cursor"), Some("mock-end-cursor")),
      (),
    ),
  )->toStrictEqual(Some({first: 50}))

  expect(
    connectionArguments(~currentPage=2, ~previousPage=1, ~totalCount=1, ~cursors=(None, None), ()),
  )->toStrictEqual(None)

  expect(
    connectionArguments(
      ~currentPage=2,
      ~previousPage=1,
      ~totalCount=100,
      ~cursors=(Some("mock-start-cursor"), Some("mock-end-cursor")),
      (),
    ),
  )->toStrictEqual(Some({first: 50, after: "mock-end-cursor"}))

  expect(
    connectionArguments(
      ~currentPage=2,
      ~previousPage=1,
      ~totalCount=9,
      ~cursors=(Some("mock-start-cursor"), Some("mock-end-cursor")),
      (),
    ),
  )->toStrictEqual(Some({first: 50, after: "mock-end-cursor"}))

  expect(
    connectionArguments(
      ~currentPage=2,
      ~previousPage=3,
      ~totalCount=50,
      ~cursors=(Some("mock-start-cursor"), Some("mock-end-cursor")),
      (),
    ),
  )->toStrictEqual(Some({last: 50, before: "mock-start-cursor"}))

  expect(
    connectionArguments(
      ~currentPage=4,
      ~previousPage=5,
      ~totalCount=50,
      ~cursors=(Some("mock-start-cursor"), Some("mock-end-cursor")),
      (),
    ),
  )->toStrictEqual(Some({last: 50, before: "mock-start-cursor"}))

  expect(
    connectionArguments(
      ~currentPage=4,
      ~previousPage=1,
      ~totalCount=50,
      ~cursors=(Some("mock-start-cursor"), Some("mock-end-cursor")),
      (),
    ),
  )->toStrictEqual(None)

  expect(
    connectionArguments(
      ~currentPage=5,
      ~previousPage=1,
      ~totalCount=50,
      ~cursors=(Some("mock-start-cursor"), Some("mock-end-cursor")),
      (),
    ),
  )->toStrictEqual(None)

  expect(
    connectionArguments(
      ~currentPage=5,
      ~previousPage=1,
      ~totalCount=250,
      ~cursors=(Some("mock-start-cursor"), Some("mock-end-cursor")),
      (),
    ),
  )->toStrictEqual(Some({last: 50}))

  expect(
    connectionArguments(
      ~currentPage=1,
      ~previousPage=-1,
      ~totalCount=0,
      ~cursors=(None, None),
      ~edgesPerPage=30,
      (),
    ),
  )->toStrictEqual(Some({first: 30}))

  expect(
    connectionArguments(
      ~currentPage=2,
      ~previousPage=4,
      ~totalCount=200,
      ~cursors=(Some(""), Some("")),
      (),
    ),
  )->toStrictEqual(Some({before: "OTI1MmZlNWQxNDBlMTlkMzA4ZjIwMzc0MDRhMDUzNmE1MA==", last: 50}))

  expect(
    connectionArguments(
      ~currentPage=4,
      ~previousPage=2,
      ~totalCount=200,
      ~cursors=(Some(""), Some("")),
      (),
    ),
  )->toStrictEqual(Some({after: "OTI1MmZlNWQxNDBlMTlkMzA4ZjIwMzc0MDRhMDUzNmExNTA=", first: 50}))
})
