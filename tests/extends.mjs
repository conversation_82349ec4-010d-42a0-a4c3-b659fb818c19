// @ts-nocheck

import 'react-dates/initialize'
import '@testing-library/jest-dom'
import 'blob-polyfill'

import * as Belt_Option from 'rescript/lib/es6/belt_Option'
import * as Caml_js_exceptions from 'rescript/lib/es6/caml_js_exceptions'

expect.extend({
  toRaise(received, exn) {
    try {
      received()
      fail('Expected to raise an exception')
    } catch (err) {
      const exception = Caml_js_exceptions.internalToOCamlException(err)
      return exception.RE_EXN_ID === exn.RE_EXN_ID
        ? { pass: true }
        : {
          pass: false,
          message: () =>
            `Expected exception '${exn.RE_EXN_ID}' but receveid '${exception.RE_EXN_ID}'`,
        }
    }
  },
  toBeNone(received) {
    return Belt_Option.isNone(received)
      ? { pass: true }
      : {
        pass: false,
        message: () =>
          `Expected to receive None but received Some(${received})`,
      }
  },
})
