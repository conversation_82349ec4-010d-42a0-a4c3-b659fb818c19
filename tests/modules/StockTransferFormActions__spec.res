open Vitest

let mockProduct = (
  ~quantity=1,
  ~recipientVariantId="mock-recipientVariantId",
  ~senderVariantId="mock-senderVariantId",
  (),
) => {
  StockTransferForm.ProductLenses.quantity,
  recipientVariantId,
  senderVariantId,
}

let mockValues = (
  ~senderShopId="mock-sender-shop-id",
  ~recipientShopId="mock-recipient-shop-id",
  ~products=[],
  (),
) => {
  StockTransferForm.Lenses.products,
  senderShopId,
  recipientShopId,
}

let {makeMutationVariables} = module(StockTransferFormActions)

test("makeMutationVariables", () => {
  let values = mockValues(~products=[], ())
  expect(makeMutationVariables(values))->toStrictEqual({
    input: {senderShopId: "mock-sender-shop-id", recipientShopId: "mock-recipient-shop-id"},
    productsInput: [],
  })

  let values = mockValues(~products=[mockProduct()], ())
  expect(makeMutationVariables(values))->toStrictEqual({
    input: {senderShopId: "mock-sender-shop-id", recipientShopId: "mock-recipient-shop-id"},
    productsInput: [
      {
        quantity: 1,
        senderVariantId: "mock-senderVariantId",
        recipientVariantId: "mock-recipientVariantId",
      },
    ],
  })

  let values = mockValues(~products=[mockProduct(), mockProduct()], ())
  expect(makeMutationVariables(values))->toStrictEqual({
    input: {senderShopId: "mock-sender-shop-id", recipientShopId: "mock-recipient-shop-id"},
    productsInput: [
      {
        quantity: 1,
        senderVariantId: "mock-senderVariantId",
        recipientVariantId: "mock-recipientVariantId",
      },
      {
        quantity: 1,
        senderVariantId: "mock-senderVariantId",
        recipientVariantId: "mock-recipientVariantId",
      },
    ],
  })
})
