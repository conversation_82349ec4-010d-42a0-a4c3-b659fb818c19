open Vitest

describe("Status", () => {
  let {toString, fromString} = module(CatalogProduct.Status)

  test("toString", () => {
    expect(Active->toString)->toBe("ACTIVE")
    expect(Inactive->toString)->toBe("INACTIVE")
    expect(Archived->toString)->toBe("ARCHIVED")
    expect(Unarchived->toString)->toBe("UNARCHIVED")
  })

  test("fromString", () => {
    expect("ACTIVE"->fromString)->toStrictEqual(Ok(Active))
    expect("INACTIVE"->fromString)->toStrictEqual(Ok(Inactive))
    expect("ARCHIVED"->fromString)->toStrictEqual(Ok(Archived))
    expect("UNARCHIVED"->fromString)->toStrictEqual(Ok(Unarchived))
    expect("unknown"->fromString)->toStrictEqual(Error("Invalid product status"))
  })
})

describe("Kind", () => {
  let {toString, fromString, toLabel} = module(CatalogProduct.Kind)

  describe("toLabel", () => {
    it("when only the product kind is passed", () => expect(#WINE->toLabel)->toBe("wine"))
    it("when translate is true", () => expect(#WINE->toLabel(~translate=true))->toBe("wine"))
    it("when translate is false", () => expect(#WINE->toLabel(~translate=false))->toBe("wine"))
    it("when titleCase is true", () => expect(#WINE->toLabel(~titleCase=true))->toBe("Wine"))
    it("when titleCase is false", () => expect(#WINE->toLabel(~titleCase=false))->toBe("wine"))
  })

  test("toString", () => expect(#WINE->toString)->toBe("WINE"))

  test("fromString", () => {
    expect("WINE"->fromString)->toStrictEqual(Ok(#WINE))
    expect("wine"->fromString)->toStrictEqual(Error("Invalid kind"))
  })
})

describe("Color", () => {
  let {toString, fromString, toColorSet} = module(CatalogProduct.Color)

  test("toString", () => expect(#AMBER->toString)->toBe("AMBER"))

  test("fromString", () => {
    expect("AMBER"->fromString)->toStrictEqual(Ok(#AMBER))
    expect("dark"->fromString)->toStrictEqual(Error("Invalid color"))
  })

  describe("toColorSet", () => {
    it(
      "should make a record containing the background and foreground hexadecimal codes associated",
      () => {
        expect(#WHITE->toColorSet(~variation=#pastille))->toStrictEqual({
          foregroundColor: Colors.Product.whitePastilleColor,
          backgroundColor: Colors.Product.whitePastilleColor,
        })
        expect(#WHITE->toColorSet(~variation=#badge))->toStrictEqual({
          foregroundColor: Colors.Product.whiteTextColor,
          backgroundColor: Colors.Product.whiteBadgeColor,
        })
        expect(#BLOND->toColorSet(~variation=#badge))->toStrictEqual({
          foregroundColor: Colors.Product.blondTextColor,
          backgroundColor: Colors.Product.blondBadgeColor,
        })
        expect(#ORANGE->toColorSet(~variation=#badge))->toStrictEqual({
          foregroundColor: Colors.Product.blondTextColor,
          backgroundColor: Colors.Product.blondBadgeColor,
        })
        expect(#AMBER->toColorSet(~variation=#badge))->toStrictEqual({
          foregroundColor: Colors.Product.amberTextColor,
          backgroundColor: Colors.Product.amberBadgeColor,
        })
        expect(#DARK->toColorSet(~variation=#badge))->toStrictEqual({
          foregroundColor: Colors.Product.darkTextColor,
          backgroundColor: Colors.Product.darkBadgeColor,
        })
        expect(#BLACK->toColorSet(~variation=#badge))->toStrictEqual({
          foregroundColor: Colors.Product.blackTextColor,
          backgroundColor: Colors.Product.blackBadgeColor,
        })
        expect(#RED->toColorSet(~variation=#badge))->toStrictEqual({
          foregroundColor: Colors.Product.redTextColor,
          backgroundColor: Colors.Product.redBadgeColor,
        })
        expect(#ROSE->toColorSet(~variation=#badge))->toStrictEqual({
          foregroundColor: Colors.Product.roseTextColor,
          backgroundColor: Colors.Product.roseBadgeColor,
        })
      },
    )
  })
})

describe("WineType", () => {
  let {toString, fromString} = module(CatalogProduct.WineType)

  test("toString", () => expect(#STILL->toString)->toBe("STILL"))

  test("fromString", () => {
    expect("STILL"->fromString)->toStrictEqual(Ok(#STILL))
    expect("unknown"->fromString)->toStrictEqual(Error("Invalid wine type"))
  })
})

describe("WhiteWineType", () => {
  let {toString, fromString} = module(CatalogProduct.WhiteWineType)

  test("toString", () => expect(#DRY->toString)->toBe("DRY"))

  test("fromString", () => {
    expect("DRY"->fromString)->toStrictEqual(Ok(#DRY))
    expect("unknown"->fromString)->toStrictEqual(Error("Invalid white wine type"))
  })
})

describe("Information", () => {
  open CatalogProduct.Information

  describe("formatDescription", () => {
    it(
      "should formate with kind WINE",
      () => {
        let information = {
          productName: "Château Margaux",
          variantName: "2015",
          sku: "12345",
          internalCode: "abcde",
          supplierName: "Domaine Château Margaux",
          designation: "Margaux",
          region: "Bordeaux",
          country: "France",
          categoryName: "Vin",
        }
        let description = formatDescription(~productKind=#WINE, ~information, ())
        expect(description)->toBe(
          "SKU: 12345, CODE: abcde, Domaine Château Margaux, Margaux, Bordeaux, France",
        )

        let information = {
          productName: "Pouilly-Fumé",
          variantName: "2018",
          designation: "Val de Loire",
          country: "France",
          categoryName: "Vin",
        }
        let description = formatDescription(~productKind=#WINE, ~information, ())
        expect(description)->toBe("Val de Loire, France")
      },
    )

    it(
      "should not formate with the color if it exists",
      () => {
        let information = {
          productName: "Pouilly-Fumé",
          variantName: "2018",
          designation: "Val de Loire",
          country: "France",
          categoryName: "Vin",
          color: #RED,
        }
        let description = formatDescription(~productKind=#WINE, ~information, ())
        expect(description)->toBe("Red, Val de Loire, France")

        let description = formatDescription(~productKind=#WINE, ~information, ~hideColor=true, ())
        expect(description)->toBe("Val de Loire, France")

        let information = {
          productName: "Pouilly-Fumé",
          variantName: "2018",
          designation: "Val de Loire",
          country: "France",
          categoryName: "Vin",
        }
        let description = formatDescription(~productKind=#WINE, ~information, ())
        expect(description)->toBe("Val de Loire, France")
      },
    )

    it(
      "should formate when `sku` equals `internalCode`",
      () => {
        let information = {
          productName: "Moët & Chandon",
          variantName: "Brut Impérial",
          sku: "54321",
          internalCode: "54321",
          supplierName: "Moët & Chandon",
          designation: "Champagne",
          region: "CHAMPAGNE",
          country: "France",
          categoryName: "Vin",
        }
        let description = formatDescription(~productKind=#WINE, ~information, ())
        expect(description)->toBe("SKU/CODE: 54321, Moët & Chandon, CHAMPAGNE, France")
      },
    )

    it(
      "should formate when `producerName` equals `supplierName`",
      () => {
        let information = {
          productName: "Moët & Chandon",
          variantName: "Brut Impérial",
          sku: "54321",
          internalCode: "fghijk",
          producerName: "Epure",
          supplierName: "EPURE",
          designation: "?",
          region: "CHAMPAGNE",
          country: "France",
          categoryName: "Vin",
        }
        let description = formatDescription(~productKind=#WINE, ~information, ())
        expect(description)->toBe("SKU: 54321, CODE: fghijk, Epure, ?, CHAMPAGNE, France")

        let description = formatDescription(
          ~productKind=#WINE,
          ~information,
          ~hideProducer=true,
          ~hideSupplier=true,
          (),
        )
        expect(description)->toBe("SKU: 54321, CODE: fghijk, ?, CHAMPAGNE, France")

        let information = {
          ...information,
          producerName: "Epure",
          supplierName: "Monoprix",
        }
        let description = formatDescription(
          ~productKind=#WINE,
          ~information,
          ~hideProducer=true,
          (),
        )
        expect(description)->toBe("SKU: 54321, CODE: fghijk, Monoprix, ?, CHAMPAGNE, France")

        let description = formatDescription(
          ~productKind=#WINE,
          ~information,
          ~hideSupplier=true,
          (),
        )
        expect(description)->toBe("SKU: 54321, CODE: fghijk, Epure, ?, CHAMPAGNE, France")
      },
    )

    it(
      "should formate when `designation` equals `region`",
      () => {
        let information = {
          productName: "Moët & Chandon",
          variantName: "Brut Impérial",
          sku: "54321",
          internalCode: "fghijk",
          supplierName: "Moët & Chandon",
          designation: "Champagne",
          region: "CHAMPAGNE",
          country: "France",
          categoryName: "Vin",
        }
        let description = formatDescription(~productKind=#WINE, ~information, ())
        expect(description)->toBe("SKU: 54321, CODE: fghijk, Moët & Chandon, CHAMPAGNE, France")
      },
    )

    it(
      "should formate with kind BEER",
      () => {
        let information = {
          productName: "Kronenbourg 1664",
          variantName: "Blanc",
          sku: "67890",
          internalCode: "mnopq",
          supplierName: "Brasseries Kronenbourg",
          beerType: "Bière de blé",
          country: "France",
          categoryName: "Bière",
        }
        let description = formatDescription(~productKind=#BEER, ~information, ())
        expect(description)->toBe(
          "SKU: 67890, CODE: mnopq, Brasseries Kronenbourg, Bière de blé, France",
        )

        let information = {
          productName: "Kronenbourg 1664",
          variantName: "Blanc",
          country: "France",
          categoryName: "Bière",
        }
        let description = formatDescription(~productKind=#BEER, ~information, ())
        expect(description)->toBe("France")
      },
    )

    it(
      "should formate with kind SPIRITUOUS",
      () => {
        let information = {
          productName: "Cointreau",
          variantName: "Triple Sec",
          sku: "98765",
          internalCode: "stuvw",
          supplierName: "Cointreau SA",
          productFamily: "Liqueur",
          country: "France",
          categoryName: "Spiritueux",
        }
        let description = formatDescription(~productKind=#SPIRITUOUS, ~information, ())
        expect(description)->toBe("SKU: 98765, CODE: stuvw, Cointreau SA, Liqueur, France")
        let information = {
          productName: "Cointreau",
          variantName: "Triple Sec",
          country: "France",
          categoryName: "Spiritueux",
        }
        let description = formatDescription(~productKind=#SPIRITUOUS, ~information, ())
        expect(description)->toBe("France")
      },
    )

    it(
      "should formate with kind SIMPLE",
      () => {
        let information = {
          productName: "Orangina",
          variantName: "Original",
          sku: "11223",
          internalCode: "xyz",
          supplierName: "Orangina Suntory France",
          country: "France",
          categoryName: "Boisson",
        }
        let description = formatDescription(~productKind=#SIMPLE, ~information, ())
        expect(description)->toBe("SKU: 11223, CODE: xyz, Orangina Suntory France, France")

        let information = {
          productName: "Orangina",
          variantName: "Original",
          country: "France",
          categoryName: "Boisson",
        }
        let description = formatDescription(~productKind=#SIMPLE, ~information, ())
        expect(description)->toBe("France")
      },
    )
  })
})
