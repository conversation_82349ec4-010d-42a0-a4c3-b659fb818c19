open Vitest

test("values", () => {
  let {values} = module(AccountingVendor)
  expect(values)->toStrictEqual([Excel, IsaCompta])
})

test("toLabel", () => {
  let {toLabel} = module(AccountingVendor)
  expect(toLabel(Excel))->toBe("Excel")
  expect(toLabel(IsaCompta))->toBe("IsaCompta")
})

todo("toLowercaseString")

test("fromString", () => {
  let {fromString} = module(AccountingVendor)
  expect(fromString("Excel"))->toBe(Some(Excel))
  expect(fromString("IsaCompta"))->toBe(Some(IsaCompta))
  expect(fromString("excel"))->toBe(Some(Excel))
  expect(fromString("isacompta"))->toBe(Some(IsaCompta))
  expect(fromString("cegid"))->toBe(None)
  expect(fromString("bobbee"))->toBe(None)
})

test("toSelectItem", () => {
  let {toSelectItem} = module(AccountingVendor)
  expect(toSelectItem(Excel))->toStrictEqual({
    key: "excel",
    label: "Excel",
    value: Excel,
  })
  expect(toSelectItem(IsaCompta))->toStrictEqual({
    key: "isacompta",
    label: "IsaCompta",
    value: IsaCompta,
  })
})

test("isExcel", () => {
  let {isExcel} = module(AccountingVendor)
  expect(isExcel(Excel))->toBe(true)
  expect(isExcel(IsaCompta))->toBe(false)
})

test("isIsaCompta", () => {
  let {isIsaCompta} = module(AccountingVendor)
  expect(isIsaCompta(IsaCompta))->toBe(true)
  expect(isIsaCompta(Excel))->toBe(false)
})

test("equal", () => {
  let {equal} = module(AccountingVendor)
  expect(equal(IsaCompta, IsaCompta))->toBe(true)
  expect(equal(Excel, Excel))->toBe(true)
  expect(equal(Excel, IsaCompta))->toBe(false)
  expect(equal(IsaCompta, Excel))->toBe(false)
})
