open Vitest

test("getRetailPriceValue", () => {
  let {getRetailPriceValue} = module(PromotionEditDiscountedProductTable)
  let retailPrices = Some([
    {PromotionEditDiscountedProduct.ProductPrice.name: "Tarif Cave", value: 10.},
    {name: "Tarif CHR", value: 20.},
  ])
  let selectedPriceName = Some("Tarif CHR")

  expect(getRetailPriceValue(~retailPrices, ~selectedPriceName))->toBe(Some(20.))
  expect(getRetailPriceValue(~retailPrices, ~selectedPriceName=Some("Tarif Pro")))->toBe(None)
  expect(getRetailPriceValue(~retailPrices, ~selectedPriceName=None))->toBe(None)
})
