open Vitest

let {rowsFromEdgesData, setDefaultOrderedQuantityValue, makeVariables, runScanEdges} = module(
  OrderProductFormPickerModalTable
)

let mockQueryProductTaxData = (
  ~productTaxValue=5.5,
  ~productId="",
  ~productName="",
  ~productKind=#SIMPLE,
  ~productColor=None,
  ~productProducer=None,
  ~productDesignation=None,
  ~productWineType=None,
  ~productWhiteWineType=None,
  ~productFamily=None,
  ~productBeerType=None,
  ~productRegion=None,
  ~productCountry=None,
  (),
) => {
  __typename: "Product",
  id: productId,
  name: productName,
  kind: productKind,
  producer: productProducer,
  designation: productDesignation,
  family: productFamily,
  beerType: productBeerType,
  color: productColor,
  wineType: productWineType,
  whiteWineType: productWhiteWineType,
  region: productRegion,
  country: productCountry,
  OrderProductFormPickerModalTable.Query.tax: {
    __typename: "Tax",
    value: productTaxValue,
  },
}

let mockQueryData = (
  ~id="mock-id",
  ~name="",
  ~formattedName="",
  ~supplierCompanyName="",
  ~bulk=None,
  ~capacityPrecision=None,
  ~purchasedPrice=None,
  ~formattedCategory=None,
  ~stockKeepingUnit=None,
  ~priceLookUpCode=None,
  ~internalCode=None,
  ~product=mockQueryProductTaxData(),
  ~alcoholVolume=None,
  ~stockState=None,
  (),
) => {
  __typename: "VariantEdge",
  OrderProductFormPickerModalTable.Query.node: {
    __typename: "Variant",
    id,
    name,
    createdAt: Js.Date.makeWithYM(~year=2023., ~month=1., ()),
    formattedName,
    formattedCategory,
    stockKeepingUnit,
    priceLookUpCode,
    internalCode,
    alcoholVolume,
    packaging: None,
    bulk,
    capacityPrecision,
    capacityUnit: Some("mock-capacity-unit"),
    purchasedPrice,
    formattedPurchasedPrice: None,
    supplier: Some({
      companyName: supplierCompanyName,
      __typename: "Supplier",
    }),
    stock: {
      __typename: "VariantStock",
      rawQuantity: Some(0),
      formattedQuantity: Some("mock-stock-formatted-quantity"),
      state: stockState,
    },
    product,
  },
}

describe("rowsFromEdgesData", () => {
  it("should map with default values", () => {
    let result = rowsFromEdgesData([mockQueryData()])
    expect(result)->toEqual([
      {
        id: "mock-id",
        createdAt: Js.Date.makeWithYM(~year=2023., ~month=1., ()),
        formattedName: "",
        productKind: #SIMPLE,
        purchasedPrice: 0.,
        formattedPurchasedPrice: None,
        packaging: None,
        bulkPrecision: None,
        capacityUnit: Some("mock-capacity-unit"),
        tax: 5.5,
        stockQuantity: Some(0),
        stockFormattedQuantity: Some("mock-stock-formatted-quantity"),
        stockState: None,
        information: {
          productName: "",
          variantName: "",
          country: "—",
          categoryName: "—",
          supplierName: "",
        },
      },
    ])

    let result = rowsFromEdgesData([
      mockQueryData(
        ~purchasedPrice=Some(2.99),
        ~product=mockQueryProductTaxData(
          ~productTaxValue=5.5,
          ~productId="mock-product-id",
          ~productName="mock-product-name",
          ~productProducer=Some("mock-product-producer"),
          ~productKind=#SIMPLE,
          ~productColor=Some(#AMBER),
          ~productDesignation=Some("mock-product-designation"),
          ~productWineType=Some(#STILL),
          ~productWhiteWineType=Some(#SWEET),
          ~productFamily=Some("mock-product-family"),
          ~productBeerType=Some("mock-product-beertype"),
          ~productRegion=Some("mock-product-region"),
          ~productCountry=Some("mock-product-country"),
          (),
        ),
        ~stockKeepingUnit=Some("12345"),
        ~priceLookUpCode=Some(1),
        ~internalCode=Some("abcde"),
        ~formattedName="mock-formatted-name",
        ~name="mock-variant-name",
        ~supplierCompanyName="mock-supplier-name",
        ~formattedCategory=Some("mock-formatted-category"),
        ~alcoholVolume=Some(12.),
        ~stockState=Some(#ALERT),
        (),
      ),
    ])
    expect(result)->toStrictEqual([
      {
        id: "mock-id",
        createdAt: Js.Date.makeWithYM(~year=2023., ~month=1., ()),
        formattedName: "mock-formatted-name",
        productKind: #SIMPLE,
        purchasedPrice: 2.99,
        formattedPurchasedPrice: None,
        packaging: None,
        bulkPrecision: None,
        capacityUnit: Some("mock-capacity-unit"),
        tax: 5.5,
        stockQuantity: Some(0),
        stockFormattedQuantity: Some("mock-stock-formatted-quantity"),
        stockState: Some(#ALERT),
        information: {
          productName: "mock-product-name",
          variantName: "mock-variant-name",
          producerName: "mock-product-producer",
          color: #AMBER,
          designation: "mock-product-designation",
          wineType: #STILL,
          whiteWineType: #SWEET,
          productFamily: "mock-product-family",
          beerType: "mock-product-beertype",
          region: "mock-product-region",
          country: "mock-product-country",
          categoryName: "mock-formatted-category",
          supplierName: "mock-supplier-name",
          alcoholVolume: "12°",
          sku: "12345",
          plu: "1",
          internalCode: "abcde",
        },
      },
    ])
  })

  testEach2([
    (mockQueryData(~bulk=Some(true), ~capacityPrecision=Some(2), ()), Some(2)),
    (mockQueryData(~bulk=Some(false), ~capacityPrecision=Some(2), ()), None),
    (mockQueryData(~bulk=None, ~capacityPrecision=Some(2), ()), None),
    (mockQueryData(~bulk=Some(true), ~capacityPrecision=None, ()), None),
    (mockQueryData(~bulk=Some(false), ~capacityPrecision=None, ()), None),
    (mockQueryData(~bulk=None, ~capacityPrecision=None, ()), None),
  ])(."should map `bulkPrecision` from related properties", (input, output) => {
    let result = rowsFromEdgesData([input])->Array.getExn(0)
    expect(result.bulkPrecision)->toBe(output)
  })
})

describe("setDefaultOrderedQuantityValue", () => {
  let {makeProductInput} = module(CartProduct)

  it("should remap the productInput with a default quantity to 1", () => {
    let result = setDefaultOrderedQuantityValue(
      makeProductInput(
        ~id="mock-id",
        ~name="mock-name",
        ~description="mock-description",
        ~taxValue=5.5,
        ~rawQuantity=9999,
        (),
      ),
    )

    expect(
      switch result {
      | Unit({product}) => product.quantity->Float.fromInt
      | Bulk({product}) => product.quantity
      },
    )->toBe(1.)
  })
})

test("makeVariables", () => {
  let result = makeVariables(~search="", ~filters={shopId: None}, ())
  expect(result)->toStrictEqual({
    search: Some(""),
    after: None,
    first: Some(20),
    filterBy: Some({
      shopIds: None,
      supplierId: None,
      categoryId: None,
      producer: None,
      stock: None,
      active: None,
      ean13: None,
      stockKeepingUnit: None,
      archived: None,
      createdAt: None,
      updatedAt: None,
    }),
  })

  let result = makeVariables(
    ~after="mock-after",
    ~search="mock-search",
    ~filters={
      shopId: Some("mock-shop-id"),
      supplier: {id: "mock-supplier-id"->Js.Nullable.return, name: "mock-supplier-name"},
      category: {id: "mock-category-id"->Js.Nullable.return, name: "mock-category-name"},
      producer: "mock-producer",
      stock: {min: 1., max: 5.},
    },
    (),
  )
  expect(result)->toStrictEqual({
    search: Some("mock-search"),
    after: Some("mock-after"),
    first: Some(20),
    filterBy: Some({
      shopIds: Some({_in: ["mock-shop-id"]}),
      supplierId: Some({_equals: Some("mock-supplier-id")}),
      categoryId: Some({_equals: Some("mock-category-id")}),
      producer: Some({_equals: "mock-producer"}),
      stock: Some({
        _min: Some(1.),
        _max: Some(5.),
        _between: None,
      }),
      active: None,
      ean13: None,
      stockKeepingUnit: None,
      archived: None,
      createdAt: None,
      updatedAt: None,
    }),
  })

  let result = makeVariables(
    ~after="mock-after",
    ~search="mock-search",
    ~filters={
      shopId: Some("mock-shop-id"),
      supplier: {id: Js.Nullable.null, name: "not-classified"},
      category: {id: Js.Nullable.null, name: "not-classified"},
    },
    (),
  )
  expect(result)->toStrictEqual({
    search: Some("mock-search"),
    after: Some("mock-after"),
    first: Some(20),
    filterBy: Some({
      shopIds: Some({_in: ["mock-shop-id"]}),
      supplierId: Some({_equals: %raw(`null`)}),
      categoryId: Some({_equals: %raw(`null`)}),
      producer: None,
      stock: None,
      active: None,
      ean13: None,
      stockKeepingUnit: None,
      archived: None,
      createdAt: None,
      updatedAt: None,
    }),
  })
})

describe("Reducer", () => {
  open OrderProductFormPickerModalTable.Reducer

  test("make with Searched(_)", () => {
    let state = {
      selectedProducts: [],
      searchQuery: "dummy-search-query",
      filters: {shopId: None},
    }
    expect(make(state, Searched("any-search-query")))->toStrictEqual({
      selectedProducts: [],
      searchQuery: "any-search-query",
      filters: {shopId: None},
    })
  })

  test("make with FiltersUpdated(_)", () => {
    let state = {
      selectedProducts: [],
      searchQuery: "",
      filters: {shopId: None},
    }
    expect(
      make(
        state,
        FiltersUpdated(
          _prev => {
            shopId: Some("mock-shop-id"),
            supplier: {id: "mock-supplier-id"->Js.Nullable.return, name: "mock-supplier-name"},
            category: {id: "mock-category-id"->Js.Nullable.return, name: "mock-category-name"},
            producer: "mock-producer",
            stock: {min: 1., max: 5.},
          },
        ),
      ),
    )->toStrictEqual({
      selectedProducts: [],
      searchQuery: "",
      filters: {
        shopId: Some("mock-shop-id"),
        supplier: {id: "mock-supplier-id"->Js.Nullable.return, name: "mock-supplier-name"},
        category: {id: "mock-category-id"->Js.Nullable.return, name: "mock-category-name"},
        producer: "mock-producer",
        stock: {min: 1., max: 5.},
      },
    })
  })

  test("make with ProductSelectionUpdated(_)", () => {
    let allProducts = rowsFromEdgesData([mockQueryData(~id="mock-product-id-0", ())])
    let state = {
      selectedProducts: [],
      searchQuery: "",
      filters: {shopId: None},
    }
    expect(make(state, ProductSelectionUpdated(All, allProducts)))->toStrictEqual({
      selectedProducts: [],
      searchQuery: "",
      filters: {shopId: None},
    })

    let allProducts = rowsFromEdgesData([mockQueryData(~id="mock-product-id-0", ())])
    let state = {
      selectedProducts: rowsFromEdgesData([mockQueryData(~id="mock-product-id-0", ())]),
      searchQuery: "",
      filters: {shopId: None},
    }
    expect(
      make(state, ProductSelectionUpdated(Selected(["mock-product-id-999"]), allProducts)),
    )->toStrictEqual({
      selectedProducts: [],
      searchQuery: "",
      filters: {shopId: None},
    })

    let allProducts = rowsFromEdgesData([
      mockQueryData(~id="mock-product-id-0", ()),
      mockQueryData(~id="mock-product-id-1", ()),
    ])
    let state = {
      selectedProducts: rowsFromEdgesData([mockQueryData(~id="mock-product-id-0", ())]),
      searchQuery: "",
      filters: {shopId: None},
    }
    expect(
      make(
        state,
        ProductSelectionUpdated(Selected(["mock-product-id-0", "mock-product-id-1"]), allProducts),
      ),
    )->toEqual({
      selectedProducts: [
        {
          id: "mock-product-id-0",
          createdAt: Js.Date.makeWithYM(~year=2023., ~month=1., ()),
          formattedName: "",
          productKind: #SIMPLE,
          purchasedPrice: 0.,
          formattedPurchasedPrice: None,
          packaging: None,
          bulkPrecision: None,
          capacityUnit: Some("mock-capacity-unit"),
          tax: 5.5,
          stockQuantity: Some(0),
          stockFormattedQuantity: Some("mock-stock-formatted-quantity"),
          stockState: None,
          information: {
            productName: "",
            variantName: "",
            country: "—",
            categoryName: "—",
            supplierName: "",
          },
        },
        {
          id: "mock-product-id-1",
          createdAt: Js.Date.makeWithYM(~year=2023., ~month=1., ()),
          formattedName: "",
          productKind: #SIMPLE,
          purchasedPrice: 0.,
          formattedPurchasedPrice: None,
          packaging: None,
          bulkPrecision: None,
          capacityUnit: Some("mock-capacity-unit"),
          tax: 5.5,
          stockQuantity: Some(0),
          stockFormattedQuantity: Some("mock-stock-formatted-quantity"),
          stockState: None,
          information: {
            productName: "",
            variantName: "",
            country: "—",
            categoryName: "—",
            supplierName: "",
          },
        },
      ],
      searchQuery: "",
      filters: {shopId: None},
    })
  })
})

todo("runScanEdges")
