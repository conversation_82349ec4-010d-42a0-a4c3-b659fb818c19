open Vitest

open PromotionStatus

describe("globalFromStatuses", () => {
  it("should rank the different statuses from highest to lowest priority", () => {
    expect(statusesWeight)->toStrictEqual([
      (#DRAFT, 0),
      (#ONGOING, 1),
      (#PROGRAMMED, 2),
      (#STOPPED, 3),
      (#NOT_PROGRAMMED, 4),
      (#EXPIRED, 5),
      (#ARCHIVED, 6),
    ])
  })
})

describe("globalFromStatuses", () => {
  it("should not return any status from empty input", () => {
    expect([]->globalFromStatuses)->toBe(None)
  })

  it("should return the only status from input", () => {
    expect([#DRAFT]->globalFromStatuses)->toBe(Some(#DRAFT))
  })

  it("should return a status when all statuses are equal from input", () => {
    expect([#ONGOING, #ONGOING]->globalFromStatuses)->toBe(Some(#ONGOING))
  })

  it("should return the most important status from the input", () => {
    expect([#ONGOING, #DRAFT]->globalFromStatuses)->toBe(Some(#DRAFT))
    expect([#STOPPED, #ONGOING]->globalFromStatuses)->toBe(Some(#ONGOING))
    expect([#EXPIRED, #ARCHIVED]->globalFromStatuses)->toBe(Some(#EXPIRED))
    expect([#NOT_PROGRAMMED, #NOT_PROGRAMMED, #PROGRAMMED]->globalFromStatuses)->toBe(
      Some(#PROGRAMMED),
    )
  })
})
