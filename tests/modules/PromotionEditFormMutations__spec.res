open Vitest
open PromotionEditFormMutations

let mockedRootCampaignId = "mocked-rootcampaign-generated-id"
let mockedProductVariantCku = "mocked-product-generated-cku"

mockPackage("uuid", () =>
  {
    "v4": () => mockedRootCampaignId,
    "validate": () => true,
    "version": () => 4,
  }
)

let mockDiscountedProduct = (~discountAmountCurrency, ~retailPrices=?, ()) => {
  PromotionEditDiscountedProduct.id: "",
  cku: mockedProductVariantCku,
  name: "",
  description: "",
  stockKeepingUnit: None,
  purchasePrice: 0.,
  bulkUnit: None,
  retailPrices,
  discount: {
    id: "",
    amount: discountAmountCurrency,
    kind: #CURRENCY,
  },
}

let mockPeriod = () => (
  Js.Date.fromString("2022-02-11T11:00:00.000Z"),
  Js.Date.fromString("2022-02-18T22:59:59.999Z"),
)

let mockRootCampaign = (~status=#DRAFT, ~selected=true, ()) => {
  PromotionEditForm.RootCampaign.id: mockedRootCampaignId,
  creatorIdentifier: "",
  shopId: "",
  shopName: "",
  priceId: "",
  status: Some(status),
  selected,
}

let mockPromotionEditFormState = (
  ~name="",
  ~priceName=?,
  ~period=?,
  ~rootCampaigns=[],
  ~discountedProducts=[],
  (),
) => {
  PromotionEditForm.Lenses.discountedProducts,
  name,
  priceName,
  period,
  rootCampaigns,
}

describe("makeCreateCampaignsInputs", () => {
  let productRetailPrices: array<PromotionEditDiscountedProduct.ProductPrice.t> = [
    {name: "Tarif Cave", value: 10.},
  ]

  it("should return a set of root campaigns inputs and common discounts input", () => {
    let (rootCampaignsInputs, discountsInput) = makeCreateCampaignsInputs(
      ~creatorIdentifier="Michel",
      ~state=mockPromotionEditFormState(
        ~name="Test",
        ~priceName="Tarif Cave",
        ~period=mockPeriod(),
        ~rootCampaigns=[mockRootCampaign()],
        ~discountedProducts=[
          mockDiscountedProduct(~discountAmountCurrency=3., ~retailPrices=productRetailPrices, ()),
        ],
        (),
      ),
    )

    expect(rootCampaignsInputs)->toStrictEqual([
      (
        mockedRootCampaignId,
        {
          CreateCampaignMutation.creatorIdentifier: "Michel",
          name: "Test",
          startDate: Js.Date.fromString("2022-02-11T11:00:00.000Z")->Scalar.Datetime.serialize,
          endDate: Js.Date.fromString("2022-02-18T22:59:59.999Z")->Scalar.Datetime.serialize,
          priceId: "",
          shopId: "",
          recurringDays: None,
          recurringStartTime: None,
          recurringEndTime: None,
        },
      ),
    ])
    expect(discountsInput)->toStrictEqual([
      {
        CreateCampaignMutation.variantCku: mockedProductVariantCku->Scalar.CKU.serialize,
        value: 3.,
        kind: #CURRENCY,
      },
    ])
  })

  it("should raise an error when there is no campaign period defined in state", () => {
    let make = () =>
      makeCreateCampaignsInputs(
        ~creatorIdentifier="Michel",
        ~state=mockPromotionEditFormState(
          ~name="Test",
          ~priceName="Tarif Cave",
          ~rootCampaigns=[mockRootCampaign()],
          ~discountedProducts=[
            mockDiscountedProduct(
              ~discountAmountCurrency=3.,
              ~retailPrices=productRetailPrices,
              (),
            ),
          ],
          (),
        ),
      )

    expect(make)->toRaise(MakeCreateCampaignsInputs_PeriodNotFound)
  })

  it(
    "should raise an error when the price name, campaign name or creatorIdentifier is empty",
    () => {
      let partialState = mockPromotionEditFormState(
        ~period=mockPeriod(),
        ~rootCampaigns=[mockRootCampaign()],
        ~discountedProducts=[
          mockDiscountedProduct(~discountAmountCurrency=3., ~retailPrices=productRetailPrices, ()),
        ],
      )

      expect(
        () =>
          makeCreateCampaignsInputs(
            ~creatorIdentifier="",
            ~state=partialState(~name="Test", ~priceName="Tarif Cave", ()),
          ),
      )->toRaise(MakeCreateCampaignsInputs_CreatorIdentifierEmpty)

      expect(
        () =>
          makeCreateCampaignsInputs(
            ~creatorIdentifier="Michel",
            ~state=partialState(~name="", ~priceName="Tarif Cave", ()),
          ),
      )->toRaise(MakeCreateCampaignsInputs_NameEmpty)
    },
  )

  it("should raise an error when one of the discounted product is invalid", () => {
    let make = () =>
      makeCreateCampaignsInputs(
        ~creatorIdentifier="Michel",
        ~state=mockPromotionEditFormState(
          ~name="Test",
          ~priceName="Tarif Cave",
          ~period=mockPeriod(),
          ~rootCampaigns=[mockRootCampaign()],
          ~discountedProducts=[
            mockDiscountedProduct(
              ~discountAmountCurrency=11.,
              ~retailPrices=productRetailPrices,
              (),
            ),
          ],
          (),
        ),
      )

    expect(make)->toRaise(MakeCreateCampaignsInputs_InvalidDiscountedProduct)
  })
})

describe("makeUpdateCampaignsInputs", () => {
  let productRetailPrices: array<PromotionEditDiscountedProduct.ProductPrice.t> = [
    {name: "Tarif Cave", value: 10.},
  ]

  it("should return a set of root campaigns inputs and common discounts input", () => {
    let (rootCampaignsInputs, discountsInput) = makeUpdateCampaignsInputs(
      ~state=mockPromotionEditFormState(
        ~name="Test",
        ~priceName="Tarif Cave",
        ~period=mockPeriod(),
        ~rootCampaigns=[mockRootCampaign()],
        ~discountedProducts=[
          mockDiscountedProduct(~discountAmountCurrency=3., ~retailPrices=productRetailPrices, ()),
        ],
        (),
      ),
    )

    expect(rootCampaignsInputs)->toStrictEqual([
      (
        mockedRootCampaignId,
        {
          name: Some("Test"),
          startDate: Some(
            Js.Date.fromString("2022-02-11T11:00:00.000Z")->Scalar.Datetime.serialize,
          ),
          endDate: Some(Js.Date.fromString("2022-02-18T22:59:59.999Z")->Scalar.Datetime.serialize),
          recurringDays: None,
          recurringStartTime: None,
          recurringEndTime: None,
        },
      ),
    ])
    expect(discountsInput)->toStrictEqual([
      {
        UpdateCampaignMutation.variantCku: mockedProductVariantCku->Scalar.CKU.serialize,
        value: 3.,
        kind: #CURRENCY,
      },
    ])
  })

  it("should raise an error when there is no campaign period defined in state", () => {
    let make = () =>
      makeUpdateCampaignsInputs(
        ~state=mockPromotionEditFormState(
          ~name="Test",
          ~priceName="Tarif Cave",
          ~rootCampaigns=[mockRootCampaign()],
          ~discountedProducts=[
            mockDiscountedProduct(
              ~discountAmountCurrency=3.,
              ~retailPrices=productRetailPrices,
              (),
            ),
          ],
          (),
        ),
      )

    expect(make)->toRaise(MakeUpdateCampaignsInputs_PeriodNotFound)
  })

  it("should raise an error when the price name or campaign name is empty", () => {
    let partialState = mockPromotionEditFormState(
      ~period=mockPeriod(),
      ~rootCampaigns=[mockRootCampaign()],
      ~discountedProducts=[
        mockDiscountedProduct(~discountAmountCurrency=3., ~retailPrices=productRetailPrices, ()),
      ],
    )

    expect(
      () => makeUpdateCampaignsInputs(~state=partialState(~name="", ~priceName="Tarif Cave", ())),
    )->toRaise(MakeUpdateCampaignsInputs_NameEmpty)
  })

  it("should raise an error when one of the discounted product is invalid", () => {
    let make = () =>
      makeUpdateCampaignsInputs(
        ~state=mockPromotionEditFormState(
          ~name="Test",
          ~priceName="Tarif Cave",
          ~period=mockPeriod(),
          ~rootCampaigns=[mockRootCampaign()],
          ~discountedProducts=[
            mockDiscountedProduct(
              ~discountAmountCurrency=11.,
              ~retailPrices=productRetailPrices,
              (),
            ),
          ],
          (),
        ),
      )

    expect(make)->toRaise(MakeUpdateCampaignsInputs_InvalidDiscountedProduct)
  })
})
