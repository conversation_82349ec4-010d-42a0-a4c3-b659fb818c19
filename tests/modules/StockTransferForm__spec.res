open Vitest
open Intl

let mockProduct = (
  ~quantity,
  ~recipientVariantId="mock-recipientVariantId",
  ~senderVariantId="mock-senderVariantId",
  (),
) => {
  StockTransferForm.ProductLenses.quantity,
  recipientVariantId,
  senderVariantId,
}

let mockValues = (~senderShopId, ~recipientShopId, ~products=[], ()) => {
  StockTransferForm.Lenses.senderShopId,
  recipientShopId,
  products,
}

describe("StockTransferForm schema validation", () => {
  let {schema} = module(StockTransferForm)

  it("should return no error", () => {
    let values = mockValues(
      ~senderShopId="Sender",
      ~recipientShopId="Recipient",
      ~products=[mockProduct(~quantity=1, ())],
      (),
    )
    expect(StockTransferForm.validate(~schema, ~values))->toStrictEqual(Ok())
  })

  it("should return an error when sender shop is missing", () => {
    let values = mockValues(
      ~senderShopId="",
      ~recipientShopId="Recipient",
      ~products=[mockProduct(~quantity=1, ())],
      (),
    )
    expect(StockTransferForm.validate(~schema, ~values))->toStrictEqual(
      Error([(Field(SenderShopId), "Please fulfill this field.")]),
    )
  })

  it("should return an error when recipient shop is missing", () => {
    let values = mockValues(
      ~senderShopId="Sender",
      ~recipientShopId="",
      ~products=[mockProduct(~quantity=1, ())],
      (),
    )
    expect(StockTransferForm.validate(~schema, ~values))->toStrictEqual(
      Error([(Field(RecipientShopId), "Please fulfill this field.")]),
    )
  })

  it("should return an error when there is no product in cart", () => {
    let values = mockValues(~senderShopId="Sender", ~recipientShopId="Recipient", ~products=[], ())
    expect(StockTransferForm.validate(~schema, ~values))->toStrictEqual(
      Error([(Field(Products), "You should have at least one product in cart.")]),
    )
  })

  it("should return an error when there is an incorrect transferred quantity", () => {
    let values = mockValues(
      ~senderShopId="Sender",
      ~recipientShopId="Recipient",
      ~products=[mockProduct(~quantity=0, ())],
      (),
    )
    expect(StockTransferForm.validate(~schema, ~values))->toStrictEqual(
      Error([
        (Field(Products), "The cart contains products with an incorrect transferred quantity."),
      ]),
    )
  })
})

test("productsError", () => {
  let {productsError} = module(StockTransferForm)

  let productA = mockProduct(~quantity=0, ~senderVariantId="mock-variant-id-a", ())
  let productB = mockProduct(~quantity=2, ~senderVariantId="mock-variant-id-b", ())
  expect([productA, productB]->productsError)->toStrictEqual([
    {key: productA.senderVariantId, message: t("Transferred quantity must be greater than 0.")},
  ])

  let productA = mockProduct(~quantity=0, ~senderVariantId="mock-variant-id-a", ())
  let productB = mockProduct(~quantity=-1, ~senderVariantId="mock-variant-id-b", ())
  expect([productA, productB]->productsError)->toStrictEqual([
    {key: productA.senderVariantId, message: t("Transferred quantity must be greater than 0.")},
    {key: productB.senderVariantId, message: t("Transferred quantity must be greater than 0.")},
  ])

  let productA = mockProduct(~quantity=1, ~senderVariantId="mock-variant-id-a", ())
  let productB = mockProduct(~quantity=2, ~senderVariantId="mock-variant-id-b", ())
  expect([productA, productB]->productsError)->toStrictEqual([])

  expect([]->productsError)->toStrictEqual([])
})
