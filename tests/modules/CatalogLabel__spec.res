open Vitest

open CatalogLabel

describe("ProductCode", () => {
  let {toString, fromString, toLabel} = module(ProductCode)

  test("toString", () => {
    expect(toString(Hidden))->toBe("Hidden")
    expect(toString(SKU))->toBe("SKU")
    expect(toString(InternalCode))->toBe("InternalCode")
  })
  test("fromString", () => {
    expect(fromString("Hidden"))->toStrictEqual(Ok(Hidden))
    expect(fromString("SKU"))->toStrictEqual(Ok(SKU))
    expect(fromString("InternalCode"))->toStrictEqual(Ok(InternalCode))
    expect(fromString("OtherCode"))->toStrictEqual(Error())
    expect(fromString(""))->toStrictEqual(Error())
  })
  test("toLabel", () => {
    expect(toLabel(Hidden))->toBe("Do not display additional product codes")
    expect(toLabel(SKU))->toBe("Display SKU")
    expect(toLabel(InternalCode))->toBe("Display internal code")
  })
})

describe("BarcodeCompletionRequest", () => {
  let {encodeBodyJson} = module(BarcodeCompletionRequest)

  test("encodeBodyJson", () => {
    let result = encodeBodyJson(~productsValues=Picked([]), ~shopId="mock-shop-id")
    expect(result)->toStrictEqual(
      Error("Products list empty, please select at least one product before continuing"),
    )

    let result = encodeBodyJson(
      ~productsValues=Picked([
        {
          variantId: "mock-variant-id",
          repetitions: 1,
        },
      ]),
      ~shopId="mock-shop-id",
    )
    expect(result)->toUnsafeStrictEqual(
      Ok({
        "shopId": "mock-shop-id",
        "variantIds": ["mock-variant-id"],
      }),
    )

    let result = encodeBodyJson(~productsValues=All, ~shopId="mock-shop-id")
    expect(result)->toUnsafeStrictEqual(Ok({"shopId": "mock-shop-id"}))
  })
})

describe("Sheet", () => {
  open! CatalogLabel.Sheet

  describe("LabelFormat", () => {
    let {toString, fromString, toMaxOffset, toFormatLabel, toLabel} = module(LabelFormat)

    test(
      "toString",
      () => {
        expect(toString(Grid21))->toBe("Grid21")
        expect(toString(Grid64))->toBe("Grid64")
      },
    )
    test(
      "fromString",
      () => {
        expect(fromString("Grid21"))->toStrictEqual(Ok(Grid21))
        expect(fromString("Grid64"))->toStrictEqual(Ok(Grid64))
        expect(fromString("Grid999"))->toStrictEqual(Error())
        expect(fromString(""))->toStrictEqual(Error())
      },
    )
    test(
      "toMaxOffset",
      () => {
        expect(toMaxOffset(Grid21))->toBe(21)
        expect(toMaxOffset(Grid64))->toBe(64)
      },
    )
    test(
      "toFormatLabel",
      () => {
        expect(toFormatLabel(Grid21))->toBe("6,35 x 3,81 cm")
        expect(toFormatLabel(Grid64))->toBe("4,57 x 1,68 cm")
      },
    )
    test(
      "toLabel",
      () => {
        expect(toLabel(Grid21))->toBe("Sheet of 21 labels (format 6,35 x 3,81 cm)")
        expect(toLabel(Grid64))->toBe("Sheet of 64 labels (format 4,57 x 1,68 cm)")
      },
    )
  })

  describe("LabelSort", () => {
    let {toString, fromString, toLabel} = module(LabelSort)

    test(
      "toString",
      () => {
        expect(toString(Unsorted))->toBe("Unsorted")
        expect(toString(AscProductName))->toBe("AscProductName")
        expect(toString(AscProductCategory))->toBe("AscProductCategory")
      },
    )
    test(
      "fromString",
      () => {
        expect(fromString("Unsorted"))->toStrictEqual(Ok(Unsorted))
        expect(fromString("AscProductName"))->toStrictEqual(Ok(AscProductName))
        expect(fromString("AscProductCategory"))->toStrictEqual(Ok(AscProductCategory))
        expect(fromString("AscQuantity"))->toStrictEqual(Error())
        expect(fromString(""))->toStrictEqual(Error())
      },
    )
    test(
      "toLabel",
      () => {
        expect(toLabel(Unsorted))->toBe("Do not sort")
        expect(toLabel(AscProductName))->toBe("Sort alphabetically by product name")
        expect(toLabel(AscProductCategory))->toBe("Sort alphabetically by product category")
      },
    )
  })

  describe("LabelsRequest", () => {
    let {encodeBodyJson, decodeBodyJson} = module(LabelsRequest)
    let {make: combineSheetRequests} = module(CombineRequests)

    test(
      "encodeBodyJson",
      () => {
        let result = encodeBodyJson(
          ~priceId="mock-id"->Uuid.unsafeFromString,
          ~sort=Unsorted,
          ~productCode=Hidden,
          ~productsValues=All,
          ~borderEnabled=false,
          ~productBarcodeDisplayed=false,
          ~priceDisplayed=false,
          ~producerDisplayed=false,
          ~alcoholVolumeDisplayed=false,
          ~printOffset=10,
          ~labelFormat=Grid64,
        )

        expect(result)->toUnsafeStrictEqual(
          Ok({
            "priceId": "mock-id",
            "barcodeEnabled": false,
            "priceEnabled": false,
            "producerEnabled": false,
            "alcoholVolumeEnabled": false,
            "offsetLabel": 10.,
            "labelsPerPage": "A4_64",
            "borderEnabled": false,
          }),
        )

        let result = encodeBodyJson(
          ~priceId="mock-id"->Uuid.unsafeFromString,
          ~productCode=InternalCode,
          ~sort=AscProductName,
          ~productsValues=All,
          ~borderEnabled=false,
          ~productBarcodeDisplayed=false,
          ~priceDisplayed=true,
          ~producerDisplayed=true,
          ~alcoholVolumeDisplayed=true,
          ~printOffset=5,
          ~labelFormat=Grid64,
        )

        expect(result)->toUnsafeStrictEqual(
          Ok({
            "priceId": "mock-id",
            "barcodeEnabled": false,
            "priceEnabled": true,
            "producerEnabled": true,
            "alcoholVolumeEnabled": true,
            "offsetLabel": 5.,
            "labelCodeKind": "INTERNAL_CODE",
            "labelSorting": "ASC_PRODUCT_NAME",
            "labelsPerPage": "A4_64",
            "borderEnabled": false,
          }),
        )

        let result = encodeBodyJson(
          ~priceId="mock-id"->Uuid.unsafeFromString,
          ~productsValues=Picked([
            {
              variantId: "mock-product-0-id",
              repetitions: 1,
            },
            {
              variantId: "mock-product-1-id",
              repetitions: 1,
            },
          ]),
          ~sort=Unsorted,
          ~productCode=Hidden,
          ~borderEnabled=false,
          ~productBarcodeDisplayed=false,
          ~priceDisplayed=false,
          ~producerDisplayed=false,
          ~alcoholVolumeDisplayed=false,
          ~printOffset=10,
          ~labelFormat=Grid64,
        )

        expect(result)->toUnsafeStrictEqual(
          Ok({
            "priceId": "mock-id",
            "barcodeEnabled": false,
            "priceEnabled": false,
            "producerEnabled": false,
            "alcoholVolumeEnabled": false,
            "offsetLabel": 10.,
            "labelsPerPage": "A4_64",
            "items": [
              {
                "variantId": "mock-product-0-id",
                "repetitions": 1,
              },
              {
                "variantId": "mock-product-1-id",
                "repetitions": 1,
              },
            ],
            "borderEnabled": false,
          }),
        )

        let result = encodeBodyJson(
          ~priceId="mock-id"->Uuid.unsafeFromString,
          ~productsValues=Picked([
            {
              variantId: "mock-product-0-id",
              repetitions: 2,
            },
            {
              variantId: "mock-product-1-id",
              repetitions: 4,
            },
          ]),
          ~sort=Unsorted,
          ~productCode=Hidden,
          ~borderEnabled=true,
          ~productBarcodeDisplayed=false,
          ~priceDisplayed=false,
          ~producerDisplayed=false,
          ~alcoholVolumeDisplayed=false,
          ~printOffset=10,
          ~labelFormat=Grid64,
        )

        expect(result)->toUnsafeStrictEqual(
          Ok({
            "priceId": "mock-id",
            "barcodeEnabled": false,
            "priceEnabled": false,
            "producerEnabled": false,
            "alcoholVolumeEnabled": false,
            "offsetLabel": 10.,
            "labelsPerPage": "A4_64",
            "items": [
              {
                "variantId": "mock-product-0-id",
                "repetitions": 2,
              },
              {
                "variantId": "mock-product-1-id",
                "repetitions": 4,
              },
            ],
            "borderEnabled": true,
          }),
        )

        let result = encodeBodyJson(
          ~priceId="mock-id"->Uuid.unsafeFromString,
          ~productsValues=Picked([]),
          ~sort=Unsorted,
          ~productCode=Hidden,
          ~borderEnabled=false,
          ~productBarcodeDisplayed=false,
          ~priceDisplayed=false,
          ~producerDisplayed=false,
          ~alcoholVolumeDisplayed=false,
          ~printOffset=10,
          ~labelFormat=Grid64,
        )

        expect(result)->toStrictEqual(
          Error("Products list empty, please select at least one product before continuing"),
        )
      },
    )

    test(
      "decodeBodyJson",
      () => {
        let json = Json.fromObjExn({"url": "https://co.fr", "invalidBarcodes": []})
        expect(json->decodeBodyJson)->toStrictEqual(
          Ok({url: Url.make("https://co.fr"), invalidBarcodesVariants: []}),
        )

        let json = Json.fromObjExn({
          "url": "https://co.fr",
          "invalidBarcodes": [
            {
              "variantId": {"mock-variant-id"},
              "productName": {"mock-product-name"},
              "variantName": {"mock-variant-name"},
            },
          ],
        })
        expect(json->decodeBodyJson)->toStrictEqual(
          Ok({
            url: Url.make("https://co.fr"),
            invalidBarcodesVariants: [
              {
                id: "mock-variant-id",
                productName: "mock-product-name",
                name: "mock-variant-name",
              },
            ],
          }),
        )

        let json = Json.fromObjExn({"url": "http://co.fr", "invalidBarcodes": []})
        expect(json->decodeBodyJson)->toStrictEqual(Error())

        let json = Json.fromObjExn({"url": "co.fr/fr", "invalidBarcodes": []})
        expect(json->decodeBodyJson)->toStrictEqual(Error())

        let json = Json.fromObjExn({"url": "mock-url", "invalidBarcodes": []})
        expect(json->decodeBodyJson)->toStrictEqual(Error())

        let json = Json.fromObjExn({"a": "b", "invalidBarcodes": []})
        expect(json->decodeBodyJson)->toStrictEqual(Error())

        let json = Json.fromObjExn({"url": "", "invalidBarcodes": []})
        expect(json->decodeBodyJson)->toStrictEqual(Error())

        let json = Json.fromObjExn({"url": "https://co.fr"})
        expect(json->decodeBodyJson)->toStrictEqual(Error())

        let json = Json.fromObjExn({"invalidBarcodes": []})
        expect(json->decodeBodyJson)->toStrictEqual(Error())

        let json = Json.encodedNull
        expect(json->decodeBodyJson)->toStrictEqual(Error())
      },
    )

    describe(
      "combinePrintRequests (make)",
      () => {
        let mockedExpectedResultUrl = Url.make("https://some.result/")

        itFuture(
          "should fetch without barcode completion then return the pdf download url",
          () => {
            let requestBarcodeCompletion = fn1(_ => Future.value(Ok()))
            let requestLabelsGenerating = fn1(
              _ =>
                Future.value(
                  Ok(
                    Json.fromObjExn({
                      "url": mockedExpectedResultUrl,
                      "invalidBarcodes": [],
                    }),
                  ),
                ),
            )

            let request = combineSheetRequests(
              ~requestBarcodeCompletion=requestBarcodeCompletion->fn,
              ~requestLabelsGenerating=requestLabelsGenerating->fn,
              ~priceId="mock-id"->Uuid.unsafeFromString,
              ~productBarcodeDisplayed=false,
              ~missingProductBarcodeGenerated=false,
              ~priceDisplayed=true,
              ~producerDisplayed=true,
              ~alcoholVolumeDisplayed=true,
              ~sort=Unsorted,
              ~productCode=Hidden,
              ~productsValues=All,
              ~borderEnabled=false,
              ~printOffset=10,
              ~labelFormat=Grid64,
              ~shopId="mock-shop-id",
            )

            request->Future.tap(
              result => {
                expect(result)->toStrictEqual(
                  Ok(
                    Some({
                      url: mockedExpectedResultUrl,
                      invalidBarcodesVariants: [],
                    }),
                  ),
                )
                expect(requestBarcodeCompletion)->toHaveBeenCalledTimes(0)
                expect(requestLabelsGenerating)->toHaveBeenCalledWith1(
                  Json.fromObjExn({
                    "barcodeEnabled": false,
                    "priceEnabled": true,
                    "producerEnabled": true,
                    "alcoholVolumeEnabled": true,
                    "borderEnabled": false,
                    "labelsPerPage": "A4_64",
                    "offsetLabel": 10,
                    "priceId": "mock-id",
                  }),
                )
              },
            )
          },
        )

        itFuture(
          "should fetch with barcode completion then return the pdf download url",
          () => {
            let requestBarcodeCompletion = fn1(_ => Future.value(Ok()))
            let requestLabelsGenerating = fn1(
              _ =>
                Future.value(
                  Ok(
                    Json.fromObjExn({
                      "url": mockedExpectedResultUrl,
                      "invalidBarcodes": [
                        {
                          "variantId": {"mock-variant-id"},
                          "productName": {"mock-product-name"},
                          "variantName": {"mock-variant-name"},
                        },
                      ],
                    }),
                  ),
                ),
            )

            let request = combineSheetRequests(
              ~requestBarcodeCompletion=requestBarcodeCompletion->fn,
              ~requestLabelsGenerating=requestLabelsGenerating->fn,
              ~priceId="mock-id"->Uuid.unsafeFromString,
              ~productBarcodeDisplayed=false,
              ~missingProductBarcodeGenerated=true,
              ~priceDisplayed=false,
              ~producerDisplayed=false,
              ~alcoholVolumeDisplayed=false,
              ~sort=Unsorted,
              ~productCode=Hidden,
              ~productsValues=All,
              ~borderEnabled=false,
              ~printOffset=10,
              ~labelFormat=Grid64,
              ~shopId="mock-shop-id",
            )

            request->Future.tap(
              result => {
                expect(result)->toStrictEqual(
                  Ok(
                    Some({
                      url: mockedExpectedResultUrl,
                      invalidBarcodesVariants: [
                        {
                          id: "mock-variant-id",
                          productName: "mock-product-name",
                          name: "mock-variant-name",
                        },
                      ],
                    }),
                  ),
                )
                expect(requestBarcodeCompletion)->toHaveBeenCalledWith1(
                  Json.fromObjExn({
                    "shopId": "mock-shop-id",
                  }),
                )
                expect(requestLabelsGenerating)->toHaveBeenCalledWith1(
                  Json.fromObjExn({
                    "barcodeEnabled": false,
                    "priceEnabled": false,
                    "producerEnabled": false,
                    "alcoholVolumeEnabled": false,
                    "borderEnabled": false,
                    "labelsPerPage": "A4_64",
                    "offsetLabel": 10,
                    "priceId": "mock-id",
                  }),
                )
              },
            )
          },
        )

        itFuture(
          "should not fetch but return an error (1)",
          () => {
            let requestBarcodeCompletion = fn1(_ => Future.value(Ok()))
            let requestLabelsGenerating = fn1(
              _ =>
                Future.value(
                  Ok(
                    Json.fromObjExn({
                      "url": mockedExpectedResultUrl,
                      "invalidBarcodes": [],
                    }),
                  ),
                ),
            )

            let request = combineSheetRequests(
              ~requestBarcodeCompletion=requestBarcodeCompletion->fn,
              ~requestLabelsGenerating=requestLabelsGenerating->fn,
              ~priceId="mock-id"->Uuid.unsafeFromString,
              ~productBarcodeDisplayed=false,
              ~missingProductBarcodeGenerated=true,
              ~priceDisplayed=false,
              ~producerDisplayed=false,
              ~alcoholVolumeDisplayed=false,
              ~sort=Unsorted,
              ~productCode=Hidden,
              ~productsValues=Picked([]),
              ~borderEnabled=false,
              ~printOffset=10,
              ~labelFormat=Grid64,
              ~shopId="mock-shop-id",
            )

            request->Future.tap(
              result => {
                expect(result)->toStrictEqual(
                  Error(
                    "Products list empty, please select at least one product before continuing",
                  ),
                )
                expect(requestBarcodeCompletion)->toHaveBeenCalledTimes(0)
                expect(requestLabelsGenerating)->toHaveBeenCalledTimes(0)
              },
            )
          },
        )

        itFuture(
          "should not fetch but return an error (2)",
          () => {
            let requestBarcodeCompletion = fn1(_ => Future.value(Error()))
            let requestLabelsGenerating = fn1(
              _ =>
                Future.value(
                  Ok(
                    Json.fromObjExn({
                      "url": mockedExpectedResultUrl,
                      "invalidBarcodes": [],
                    }),
                  ),
                ),
            )

            let request = combineSheetRequests(
              ~requestBarcodeCompletion=requestBarcodeCompletion->fn,
              ~requestLabelsGenerating=requestLabelsGenerating->fn,
              ~priceId="mock-id"->Uuid.unsafeFromString,
              ~productBarcodeDisplayed=false,
              ~missingProductBarcodeGenerated=true,
              ~priceDisplayed=false,
              ~producerDisplayed=false,
              ~alcoholVolumeDisplayed=false,
              ~sort=Unsorted,
              ~productCode=Hidden,
              ~productsValues=All,
              ~borderEnabled=false,
              ~printOffset=10,
              ~labelFormat=Grid64,
              ~shopId="mock-shop-id",
            )

            request->Future.tap(
              result => {
                expect(result)->toStrictEqual(
                  Error("An unexpected error occured. Please try again or contact the support."),
                )
                expect(requestBarcodeCompletion)->toHaveBeenCalledTimes(1)
                expect(requestBarcodeCompletion)->toHaveBeenCalledWith1(
                  Json.fromObjExn({
                    "shopId": "mock-shop-id",
                  }),
                )
                expect(requestLabelsGenerating)->toHaveBeenCalledTimes(0)
              },
            )
          },
        )

        itFuture(
          "should not fetch but return an error (3)",
          () => {
            let requestBarcodeCompletion = fn1(_ => Future.value(Ok()))
            let requestLabelsGenerating = fn1(_ => Future.value(Error("some server error")))

            let request = combineSheetRequests(
              ~requestBarcodeCompletion=requestBarcodeCompletion->fn,
              ~requestLabelsGenerating=requestLabelsGenerating->fn,
              ~priceId="mock-id"->Uuid.unsafeFromString,
              ~productBarcodeDisplayed=false,
              ~missingProductBarcodeGenerated=false,
              ~priceDisplayed=false,
              ~producerDisplayed=false,
              ~alcoholVolumeDisplayed=false,
              ~sort=Unsorted,
              ~productCode=Hidden,
              ~productsValues=All,
              ~borderEnabled=false,
              ~printOffset=10,
              ~labelFormat=Grid64,
              ~shopId="mock-shop-id",
            )

            request->Future.tap(
              result => {
                expect(result)->toStrictEqual(
                  Error("An unexpected error occured. Please try again or contact the support."),
                )
                expect(requestBarcodeCompletion)->toHaveBeenCalledTimes(0)
                expect(requestLabelsGenerating)->toHaveBeenCalledTimes(1)
                expect(requestLabelsGenerating)->toHaveBeenCalledWith1(
                  Json.fromObjExn({
                    "priceId": "mock-id",
                    "barcodeEnabled": false,
                    "priceEnabled": false,
                    "producerEnabled": false,
                    "alcoholVolumeEnabled": false,
                    "borderEnabled": false,
                    "offsetLabel": 10,
                    "labelsPerPage": "A4_64",
                  }),
                )
              },
            )
          },
        )
      },
    )
  })
})

describe("Print", () => {
  open CatalogLabel.Print

  describe("LabelFormat", () => {
    let {toString, fromString, toFormatLabel, toLabel} = module(LabelFormat)

    test(
      "toString",
      () => {
        expect(toString(Label31x22))->toBe("Label31x22")
        expect(toString(Label57x19))->toBe("Label57x19")
      },
    )
    test(
      "fromString",
      () => {
        expect(fromString("Label31x22"))->toStrictEqual(Ok(Label31x22))
        expect(fromString("Label57x19"))->toStrictEqual(Ok(Label57x19))
        expect(fromString("Label999x999"))->toStrictEqual(Error())
        expect(fromString(""))->toStrictEqual(Error())
      },
    )
    test(
      "toFormatLabel",
      () => {
        expect(toFormatLabel(Label31x22))->toBe("3,10 x 2,20 cm")
        expect(toFormatLabel(Label57x19))->toBe("5,70 x 1,90 cm")
      },
    )
    test(
      "toLabel",
      () => {
        expect(toLabel(Label31x22))->toBe("Unit label (format 3,10 x 2,20 cm)")
        expect(toLabel(Label57x19))->toBe("Unit label (format 5,70 x 1,90 cm)")
      },
    )
  })

  describe("LabelsRequest", () => {
    let {encodeBodyJson} = module(LabelsRequest)
    let {make: combinePrintRequests} = module(CombineRequests)

    test(
      "encodeBodyJson",
      () => {
        let result = encodeBodyJson(
          ~priceId="mock-id"->Uuid.unsafeFromString,
          ~productBarcodeDisplayed=false,
          ~priceDisplayed=false,
          ~producerDisplayed=false,
          ~alcoholVolumeDisplayed=false,
          ~productCode=Hidden,
          ~labelFormat=Label31x22,
          ~productsValues=All,
        )

        expect(result)->toUnsafeStrictEqual(
          Ok({
            "priceId": "mock-id",
            "priceEnabled": false,
            "producerEnabled": false,
            "barcodeEnabled": false,
            "alcoholVolumeEnabled": false,
            "labelFormat": "W31_X_H22",
          }),
        )

        let result = encodeBodyJson(
          ~priceId="mock-id"->Uuid.unsafeFromString,
          ~productCode=InternalCode,
          ~productsValues=All,
          ~priceDisplayed=true,
          ~producerDisplayed=true,
          ~productBarcodeDisplayed=true,
          ~alcoholVolumeDisplayed=true,
          ~labelFormat=Label57x19,
        )

        expect(result)->toUnsafeStrictEqual(
          Ok({
            "priceId": "mock-id",
            "labelCodeKind": "INTERNAL_CODE",
            "priceEnabled": true,
            "producerEnabled": true,
            "barcodeEnabled": true,
            "alcoholVolumeEnabled": true,
          }),
        )

        let result = encodeBodyJson(
          ~priceId="mock-id"->Uuid.unsafeFromString,
          ~productCode=InternalCode,
          ~productsValues=Picked([
            {
              variantId: "mock-product-0-id",
              repetitions: 1,
            },
            {
              variantId: "mock-product-1-id",
              repetitions: 1,
            },
          ]),
          ~priceDisplayed=true,
          ~producerDisplayed=true,
          ~productBarcodeDisplayed=true,
          ~alcoholVolumeDisplayed=true,
          ~labelFormat=Label57x19,
        )

        expect(result)->toUnsafeStrictEqual(
          Ok({
            "priceId": "mock-id",
            "labelCodeKind": "INTERNAL_CODE",
            "items": [
              {
                "variantId": "mock-product-0-id",
                "repetitions": 1,
              },
              {
                "variantId": "mock-product-1-id",
                "repetitions": 1,
              },
            ],
            "priceEnabled": true,
            "producerEnabled": true,
            "barcodeEnabled": true,
            "alcoholVolumeEnabled": true,
          }),
        )

        let result = encodeBodyJson(
          ~priceId="mock-id"->Uuid.unsafeFromString,
          ~productCode=Hidden,
          ~productsValues=Picked([
            {
              variantId: "mock-product-0-id",
              repetitions: 2,
            },
            {
              variantId: "mock-product-1-id",
              repetitions: 4,
            },
          ]),
          ~priceDisplayed=true,
          ~producerDisplayed=false,
          ~productBarcodeDisplayed=true,
          ~alcoholVolumeDisplayed=false,
          ~labelFormat=Label31x22,
        )

        expect(result)->toUnsafeStrictEqual(
          Ok({
            "priceId": "mock-id",
            "items": [
              {
                "variantId": "mock-product-0-id",
                "repetitions": 2,
              },
              {
                "variantId": "mock-product-1-id",
                "repetitions": 4,
              },
            ],
            "priceEnabled": true,
            "producerEnabled": false,
            "barcodeEnabled": true,
            "alcoholVolumeEnabled": false,
            "labelFormat": "W31_X_H22",
          }),
        )

        let result = encodeBodyJson(
          ~priceId="mock-id"->Uuid.unsafeFromString,
          ~productsValues=Picked([]),
          ~productCode=SKU,
          ~priceDisplayed=false,
          ~producerDisplayed=true,
          ~productBarcodeDisplayed=false,
          ~alcoholVolumeDisplayed=true,
          ~labelFormat=Label31x22,
        )

        expect(result)->toStrictEqual(
          Error("Products list empty, please select at least one product before continuing"),
        )
      },
    )

    describe(
      "combinePrintRequests (make)",
      () => {
        itFuture(
          "should fetch without barcode completion then return success",
          () => {
            let requestBarcodeCompletion = fn1(_bodyJson => Future.value(Ok()))
            let requestLabelsPrinting = fn2((_bodyJson, _shopId) => Future.value(Ok()))

            combinePrintRequests(
              ~requestBarcodeCompletion=requestBarcodeCompletion->fn,
              ~requestLabelsPrinting=requestLabelsPrinting->fn,
              ~priceId="mock-id"->Uuid.unsafeFromString,
              ~priceName="mock-price-name",
              ~productCode=Hidden,
              ~productsValues=All,
              ~productBarcodeDisplayed=false,
              ~missingProductBarcodeGenerated=false,
              ~priceDisplayed=false,
              ~producerDisplayed=false,
              ~alcoholVolumeDisplayed=false,
              ~labelFormat=Label31x22,
              ~shopId="mock-shop-id",
            )->Future.tap(
              result => {
                expect(result)->toStrictEqual(Ok())
                expect(requestBarcodeCompletion)->toHaveBeenCalledTimes(0)
                expect(requestLabelsPrinting)->toHaveBeenCalledWith2(
                  Json.fromObjExn({
                    "priceId": "mock-id",
                    "priceEnabled": false,
                    "producerEnabled": false,
                    "alcoholVolumeEnabled": false,
                    "barcodeEnabled": false,
                    "labelFormat": "W31_X_H22",
                  }),
                  "mock-shop-id",
                )
              },
            )
          },
        )

        itFuture(
          "should fetch with barcode completion then return success",
          () => {
            let requestBarcodeCompletion = fn1(_bodyJson => Future.value(Ok()))
            let requestLabelsPrinting = fn2((_bodyJson, _shopId) => Future.value(Ok()))

            combinePrintRequests(
              ~requestBarcodeCompletion=requestBarcodeCompletion->fn,
              ~requestLabelsPrinting=requestLabelsPrinting->fn,
              ~priceId="mock-id"->Uuid.unsafeFromString,
              ~priceName="mock-price-name",
              ~productCode=InternalCode,
              ~productsValues=Picked([
                {
                  variantId: "mock-product-0-id",
                  repetitions: 1,
                },
                {
                  variantId: "mock-product-1-id",
                  repetitions: 1,
                },
              ]),
              ~priceDisplayed=true,
              ~producerDisplayed=true,
              ~productBarcodeDisplayed=true,
              ~alcoholVolumeDisplayed=true,
              ~labelFormat=Label57x19,
              ~shopId="mock-shop-id",
              ~missingProductBarcodeGenerated=true,
            )->Future.tap(
              result => {
                expect(result)->toStrictEqual(Ok())
                expect(requestBarcodeCompletion)->toHaveBeenCalledWith1(
                  Json.fromObjExn({
                    "shopId": "mock-shop-id",
                    "variantIds": ["mock-product-0-id", "mock-product-1-id"],
                  }),
                )
                expect(requestLabelsPrinting)->toHaveBeenCalledWith2(
                  Json.fromObjExn({
                    "priceId": "mock-id",
                    "labelCodeKind": "INTERNAL_CODE",
                    "items": [
                      {
                        "variantId": "mock-product-0-id",
                        "repetitions": 1,
                      },
                      {
                        "variantId": "mock-product-1-id",
                        "repetitions": 1,
                      },
                    ],
                    "priceEnabled": true,
                    "producerEnabled": true,
                    "barcodeEnabled": true,
                    "alcoholVolumeEnabled": true,
                  }),
                  "mock-shop-id",
                )
              },
            )
          },
        )

        itFuture(
          "should not fetch but return an error (1)",
          () => {
            let requestBarcodeCompletion = fn1(_ => Future.value(Ok()))
            let requestLabelsPrinting = fn2((_bodyJson, _shopId) => Future.value(Ok()))

            combinePrintRequests(
              ~requestBarcodeCompletion=requestBarcodeCompletion->fn,
              ~requestLabelsPrinting=requestLabelsPrinting->fn,
              ~priceId="mock-id"->Uuid.unsafeFromString,
              ~priceName="mock-price-name",
              ~productCode=Hidden,
              ~productsValues=Picked([]),
              ~priceDisplayed=false,
              ~producerDisplayed=false,
              ~productBarcodeDisplayed=false,
              ~alcoholVolumeDisplayed=false,
              ~labelFormat=Label31x22,
              ~missingProductBarcodeGenerated=false,
              ~shopId="mock-shop-id",
            )->Future.tap(
              result => {
                expect(result)->toStrictEqual(
                  Error(
                    "Products list empty, please select at least one product before continuing",
                  ),
                )
                expect(requestBarcodeCompletion)->toHaveBeenCalledTimes(0)
                expect(requestLabelsPrinting)->toHaveBeenCalledTimes(0)
              },
            )
          },
        )

        itFuture(
          "should not fetch but return an error (2)",
          () => {
            let requestBarcodeCompletion = fn1(_ => Future.value(Error()))
            let requestLabelsPrinting = fn2((_bodyJson, _shopId) => Future.value(Ok()))

            combinePrintRequests(
              ~requestBarcodeCompletion=requestBarcodeCompletion->fn,
              ~requestLabelsPrinting=requestLabelsPrinting->fn,
              ~priceId="mock-id"->Uuid.unsafeFromString,
              ~priceName="mock-price-name",
              ~productCode=Hidden,
              ~productsValues=All,
              ~priceDisplayed=false,
              ~producerDisplayed=false,
              ~productBarcodeDisplayed=false,
              ~alcoholVolumeDisplayed=false,
              ~labelFormat=Label31x22,
              ~shopId="mock-shop-id",
              ~missingProductBarcodeGenerated=true,
            )->Future.tap(
              result => {
                expect(result)->toStrictEqual(
                  Error("An unexpected error occured. Please try again or contact the support."),
                )
                expect(requestBarcodeCompletion)->toHaveBeenCalledTimes(1)
                expect(requestBarcodeCompletion)->toHaveBeenCalledWith1(
                  Json.fromObjExn({
                    "shopId": "mock-shop-id",
                  }),
                )
                expect(requestLabelsPrinting)->toHaveBeenCalledTimes(0)
              },
            )
          },
        )

        itFuture(
          "should not fetch but return an error (4)",
          () => {
            let requestBarcodeCompletion = fn1(_ => Future.value(Ok()))
            let requestLabelsPrinting = fn2(
              (_bodyJson, _shopId) =>
                Future.value(Error(CatalogLabel.Print.LabelsRequest.PriceUnknown)),
            )

            combinePrintRequests(
              ~requestBarcodeCompletion=requestBarcodeCompletion->fn,
              ~requestLabelsPrinting=requestLabelsPrinting->fn,
              ~priceId="mock-id"->Uuid.unsafeFromString,
              ~priceName="mock-price-name",
              ~productCode=Hidden,
              ~productsValues=All,
              ~priceDisplayed=false,
              ~producerDisplayed=false,
              ~productBarcodeDisplayed=false,
              ~alcoholVolumeDisplayed=false,
              ~labelFormat=Label31x22,
              ~shopId="mock-shop-id",
              ~missingProductBarcodeGenerated=false,
            )->Future.tap(
              result => {
                expect(result)->toStrictEqual(
                  Error(
                    "The label printing has failed: please enter the retail price for the price list set up for printing (mock-price-name).",
                  ),
                )
                expect(requestBarcodeCompletion)->toHaveBeenCalledTimes(0)
                expect(requestLabelsPrinting)->toHaveBeenCalledTimes(1)
                expect(requestLabelsPrinting)->toHaveBeenCalledWith2(
                  Json.fromObjExn({
                    "priceId": "mock-id",
                    "priceEnabled": false,
                    "producerEnabled": false,
                    "barcodeEnabled": false,
                    "alcoholVolumeEnabled": false,
                    "labelFormat": "W31_X_H22",
                  }),
                  "mock-shop-id",
                )
              },
            )
          },
        )
        itFuture(
          "should not fetch but return an error (3)",
          () => {
            let requestBarcodeCompletion = fn1(_ => Future.value(Ok()))
            let requestLabelsPrinting = fn2(
              (_bodyJson, _shopId) => Future.value(Error(CatalogLabel.Print.LabelsRequest.Unknown)),
            )

            combinePrintRequests(
              ~requestBarcodeCompletion=requestBarcodeCompletion->fn,
              ~requestLabelsPrinting=requestLabelsPrinting->fn,
              ~priceId="mock-id"->Uuid.unsafeFromString,
              ~priceName="mock-price-name",
              ~productCode=Hidden,
              ~productsValues=All,
              ~priceDisplayed=false,
              ~producerDisplayed=false,
              ~productBarcodeDisplayed=false,
              ~alcoholVolumeDisplayed=false,
              ~labelFormat=Label31x22,
              ~shopId="mock-shop-id",
              ~missingProductBarcodeGenerated=false,
            )->Future.tap(
              result => {
                expect(result)->toStrictEqual(
                  Error("An unexpected error occured. Please try again or contact the support."),
                )
                expect(requestBarcodeCompletion)->toHaveBeenCalledTimes(0)
                expect(requestLabelsPrinting)->toHaveBeenCalledTimes(1)
                expect(requestLabelsPrinting)->toHaveBeenCalledWith2(
                  Json.fromObjExn({
                    "priceId": "mock-id",
                    "priceEnabled": false,
                    "producerEnabled": false,
                    "barcodeEnabled": false,
                    "alcoholVolumeEnabled": false,
                    "labelFormat": "W31_X_H22",
                  }),
                  "mock-shop-id",
                )
              },
            )
          },
        )
      },
    )
  })
})

todo("StateSettingsPreferences")
