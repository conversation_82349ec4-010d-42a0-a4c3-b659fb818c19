open Vitest

open PromotionEditFormManagementTableCard
open PromotionEditForm

mockPackage("uuid", () =>
  {
    "v4": () => "",
    "validate": () => true,
    "version": () => 4,
  }
)

let mockShop: (~id: string, ~name: string) => Auth.shop = (~id, ~name) => {
  id,
  kind: #INTEGRATED,
  name,
  corporateName: "",
  address: "",
  postalCode: "",
  city: "",
  country: "",
  phoneNumber: "",
  email: "",
  activeWebDeviceId: "",
  legalRepresentative: None,
  logoUri: None,
  bankName: None,
  cityOfRegistryOffice: None,
  website: None,
  fiscalYearEndClosingMonth: None,
  legalForm: None,
  amountOfShareCapital: None,
  tvaNumber: None,
  siretNumber: None,
  rcsNumber: None,
  apeNafCode: None,
  bankCode: None,
  bankAccountHolder: None,
  bankAccountNumber: None,
  bicCode: None,
  ibanNumber: None,
}

describe("makeEligibleShopsFromPrices", () => {
  let mockPriceEdge = (~name: string, ~shopId: string) => {
    PricesQuery.__typename: "PriceEdge",
    node: {
      __typename: "Price",
      id: "",
      name,
      shop: {
        __typename: "Shop",
        id: shopId,
      },
    },
  }

  it("should return only the shops matching the selected price name from the prices list", () => {
    let prices = [
      mockPriceEdge(~name="Tarif E-commerce", ~shopId="A-fab4b89"),
      mockPriceEdge(~name="Tarif E-commerce", ~shopId="B-r8d13i7"),
      mockPriceEdge(~name="Tarif Pro", ~shopId="B-r8d13i7"),
    ]
    let shops = [mockShop(~id="A-fab4b89", ~name="ShopA"), mockShop(~id="B-r8d13i7", ~name="ShopB")]
    let selectedPriceName = "Tarif E-commerce"

    expect(makeEligibleShopsFromPrices(~prices, ~shops, ~selectedPriceName))->toStrictEqual([
      {
        RootCampaign.id: "",
        creatorIdentifier: "",
        shopId: "A-fab4b89",
        shopName: "ShopA",
        priceId: "",
        status: None,
        selected: false,
      },
      {
        RootCampaign.id: "",
        creatorIdentifier: "",
        shopId: "B-r8d13i7",
        shopName: "ShopB",
        priceId: "",
        status: None,
        selected: false,
      },
    ])

    let selectedPriceName = "Tarif Pro"

    expect(makeEligibleShopsFromPrices(~prices, ~shops, ~selectedPriceName))->toStrictEqual([
      {
        RootCampaign.id: "",
        creatorIdentifier: "",
        shopId: "B-r8d13i7",
        shopName: "ShopB",
        priceId: "",
        status: None,
        selected: false,
      },
    ])
  })
})
