open Vitest

let {fromRawValue} = module(PaymentMethod)
testEach2([
  (#AMEX, PaymentMethod.Amex),
  (#BANK_TRANSFER, BankTransfer),
  (#BARNABY, Barnaby),
  (#CASH, Cash),
  (#CHEQUE, Cheque),
  (#CONTACTLESS_AMEX, ContactlessAmex),
  (#CONTACTLESS_DEBIT_CARD, ContactlessDebitCard),
  (#CREDIT_MEMO, CreditNote),
  (#DEBIT_CARD, DebitCard),
  (#DISTANCE_SELLING, DistanceSelling),
  (#EUSKO_CASH, EuskoCash),
  (#EUSKO_DIGITAL, EuskoDigital),
  (#GIFT_VOUCHER, GiftVoucher),
  (#LYDIA, Lydia),
  (#LESHABITUES, LesHabitues),
  (#PAYPAL, Paypal),
  (#TICKET_RESTAURANT, TicketRestaurant),
  (#TOWN_HALL_GIFT_VOUCHER, TownHallGiftVoucher),
  (#WINO_PAY, WinoPay),
])(."fromRawValue", (input, output) => expect(fromRawValue(input))->toBe(output))

let {decodeFromJson} = module(PaymentMethod)
testEach2([
  ("AMEX", Some(PaymentMethod.Amex)),
  ("BANK_TRANSFER", Some(BankTransfer)),
  ("BARNABY", Some(Barnaby)),
  ("CASH", Some(Cash)),
  ("CHEQUE", Some(Cheque)),
  ("CONTACTLESS_AMEX", Some(ContactlessAmex)),
  ("CONTACTLESS_DEBIT_CARD", Some(ContactlessDebitCard)),
  ("CREDIT_MEMO", Some(CreditNote)),
  ("DEBIT_CARD", Some(DebitCard)),
  ("DISTANCE_SELLING", Some(DistanceSelling)),
  ("EUSKO_CASH", Some(EuskoCash)),
  ("EUSKO_DIGITAL", Some(EuskoDigital)),
  ("GIFT_VOUCHER", Some(GiftVoucher)),
  ("LYDIA", Some(Lydia)),
  ("LESHABITUES", Some(LesHabitues)),
  ("PAYPAL", Some(Paypal)),
  ("TICKET_RESTAURANT", Some(TicketRestaurant)),
  ("TOWN_HALL_GIFT_VOUCHER", Some(TownHallGiftVoucher)),
  ("WINO_PAY", Some(WinoPay)),
  ("abc", None),
  ("0", None),
])(."decodeFromJson", (input, output) => {
  let json = Json.encodeString(input)
  expect(decodeFromJson(json))->toBe(output)
})

let {encodeToJson} = module(PaymentMethod)
testEach2([
  (PaymentMethod.Amex, "AMEX"),
  (BankTransfer, "BANK_TRANSFER"),
  (Barnaby, "BARNABY"),
  (Cash, "CASH"),
  (Cheque, "CHEQUE"),
  (ContactlessAmex, "CONTACTLESS_AMEX"),
  (ContactlessDebitCard, "CONTACTLESS_DEBIT_CARD"),
  (CreditNote, "CREDIT_MEMO"),
  (DebitCard, "DEBIT_CARD"),
  (DistanceSelling, "DISTANCE_SELLING"),
  (EuskoCash, "EUSKO_CASH"),
  (EuskoDigital, "EUSKO_DIGITAL"),
  (GiftVoucher, "GIFT_VOUCHER"),
  (Lydia, "LYDIA"),
  (LesHabitues, "LESHABITUES"),
  (Paypal, "PAYPAL"),
  (TicketRestaurant, "TICKET_RESTAURANT"),
  (TownHallGiftVoucher, "TOWN_HALL_GIFT_VOUCHER"),
  (WinoPay, "WINO_PAY"),
])(."encodeToJson", (input, output) => {
  expect(encodeToJson(input))->toBe(Json.encodeString(output))
})

let {toLabel} = module(PaymentMethod)
testEach2([
  (PaymentMethod.Amex, "Amex"),
  (BankTransfer, "Bank Transfer"),
  (Barnaby, "Barnaby"),
  (Cash, "Cash"),
  (Cheque, "Cheque"),
  (ContactlessAmex, "Amex contactless"),
  (ContactlessDebitCard, "Debit Card contactless"),
  (CreditNote, "Credit note"),
  (DistanceSelling, "Distance Selling"),
  (DebitCard, "Debit Card"),
  (EuskoCash, "Eusko Cash"),
  (EuskoDigital, "Eusko Digital"),
  (GiftVoucher, "Gift voucher"),
  (Lydia, "Lydia"),
  (LesHabitues, "Les Habitués"),
  (Paypal, "Paypal"),
  (TicketRestaurant, "Ticket Restaurant"),
  (TownHallGiftVoucher, "Council voucher"),
  (WinoPay, "WinoPay"),
])(."toLabel", (input, output) => expect(toLabel(input))->toBe(output))

let {toShortLabel} = module(PaymentMethod)
testEach2([
  (PaymentMethod.Amex, "Amex"),
  (BankTransfer, "Bank transfer"),
  (Barnaby, "Barnaby"),
  (Cash, "Cash"),
  (Cheque, "Cheque"),
  (ContactlessAmex, "Amex - CTLS"),
  (ContactlessDebitCard, "DC - CTLS"),
  (CreditNote, "Credit note"),
  (DistanceSelling, "Dist. sell."),
  (DebitCard, "DC"),
  (EuskoCash, "Eusko cash"),
  (EuskoDigital, "Eusko digital"),
  (GiftVoucher, "Gift voucher"),
  (Lydia, "Lydia"),
  (LesHabitues, "Les Habitués"),
  (Paypal, "Paypal"),
  (TicketRestaurant, "Ticket resto"),
  (TownHallGiftVoucher, "Council voucher"),
  (WinoPay, "WinoPay"),
])(."toShortLabel", (input, output) => expect(toShortLabel(input))->toBe(output))

test("values", () => {
  let {values} = module(PaymentMethod)
  expect(values)->toMatchSnapshot
})

todo("equal")
todo("ColorSet")
todo("Svg")
