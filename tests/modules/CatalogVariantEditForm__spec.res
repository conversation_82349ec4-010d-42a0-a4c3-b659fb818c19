open Vitest

let mockValues = (
  ~name="Variant",
  ~minStockThreshold=1.,
  ~maxStockThreshold=3.,
  ~stockOrderTriggerThreshold=2.,
  ~priceLookUpCode={
    CatalogVariantEditForm.invalidValues: [],
    value: None,
  },
  (),
) => {
  CatalogVariantEditForm.Lenses.name,
  capacityUnit: Some("L"),
  capacityValue: None,
  capacityPrecision: None,
  ean13: "foo",
  stockKeepingUnit: "foo",
  priceLookUpCode,
  internalCode: "foo",
  alcoholVolume: Some(10.),
  year: Some(2021.),
  bulk: false,
  purchasePrice: 0.,
  variantPrices: Some([]),
  initialStockQuantity: 0.,
  initialStockComment: "foo",
  supplierId: "foo",
  minStockThreshold,
  maxStockThreshold,
  stockOrderTriggerThreshold,
}

let {schema} = module(CatalogVariantEditForm)

it("should return no error", () => {
  let values = mockValues()
  expect(CatalogVariantEditForm.validate(~schema, ~values))->toStrictEqual(Ok())
})

it("should return no error when all thresholds fields are 0", () => {
  let values = mockValues(
    ~minStockThreshold=0.,
    ~maxStockThreshold=0.,
    ~stockOrderTriggerThreshold=0.,
    (),
  )
  expect(CatalogVariantEditForm.validate(~schema, ~values))->toStrictEqual(Ok())
})

it("should return an error when name is empty", () => {
  let values = mockValues(~name="", ())
  expect(CatalogVariantEditForm.validate(~schema, ~values))->toStrictEqual(
    Error([(Field(Name), "Please fulfill this field.")]),
  )
})

it(
  "should return an error when the minimum stock is greater than the value triggering a new order",
  () => {
    let values = mockValues(~minStockThreshold=2., ~stockOrderTriggerThreshold=1., ())
    expect(CatalogVariantEditForm.validate(~schema, ~values))->toStrictEqual(
      Error([
        (
          Field(MinStockThreshold),
          "The minimum stock should be less or equal to the value triggering a new order.",
        ),
      ]),
    )
  },
)

it(
  "should return an error when the maximum stock is inferior or equal than the value triggering a new order",
  () => {
    let values = mockValues(~maxStockThreshold=2., ~stockOrderTriggerThreshold=2., ())
    expect(CatalogVariantEditForm.validate(~schema, ~values))->toStrictEqual(
      Error([
        (Field(StockOrderTriggerThreshold), "This value should be less than maximum stock value."),
      ]),
    )
  },
)

it(
  "should return an error when the minimum stock is inferior or equal than the maximum stock value",
  () => {
    let values = mockValues(
      ~minStockThreshold=3.,
      ~maxStockThreshold=3.,
      ~stockOrderTriggerThreshold=3.,
      (),
    )
    expect(CatalogVariantEditForm.validate(~schema, ~values))->toStrictEqual(
      Error([
        (Field(MinStockThreshold), "The minimum stock should be less than the maximum stock."),
        (Field(StockOrderTriggerThreshold), "This value should be less than maximum stock value."),
      ]),
    )
  },
)

it("should return an error when the PLU code is out of range", () => {
  let values = mockValues(
    ~priceLookUpCode={
      invalidValues: [],
      value: Some(9998),
    },
    (),
  )
  expect(CatalogVariantEditForm.validate(~schema, ~values))->toStrictEqual(
    Error([(Field(PriceLookUpCode), "The PLU code must be between 1 and 9997.")]),
  )
})

it("should return an error when the PLU code is already consummed.", () => {
  let values = mockValues(
    ~priceLookUpCode={
      invalidValues: [23],
      value: Some(23),
    },
    (),
  )
  expect(CatalogVariantEditForm.validate(~schema, ~values))->toStrictEqual(
    Error([(Field(PriceLookUpCode), "This PLU code is used by another product.")]),
  )
})

it("should return an error when the PLU code is already consummed.", () => {
  let values = mockValues(
    ~priceLookUpCode={
      invalidValues: Array.makeBy(9997, index => index + 1),
      value: Some(23),
    },
    (),
  )
  expect(CatalogVariantEditForm.validate(~schema, ~values))->toStrictEqual(
    Error([(Field(PriceLookUpCode), "There is no more PLU code available.")]),
  )
})
