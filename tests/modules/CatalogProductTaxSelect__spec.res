open Vitest
open TestingLibraryReact

module TestableQuery = %graphql(`
  query taxes($filterBy: InputTaxQueryFilter) {
    taxes(first: 50, filterBy: $filterBy) {
      edges {
        node {
          id
          value
          shop { id }
        }
      }
    }
  }
`)

let mockedIdTax0 = Uuid.make()
let mockedIdTax5f5 = Uuid.make()
let mockedIdTax20 = Uuid.make()

let {setupNodeServer, listen, resetHandlers, close, ctxDelayDuration} = module(MSW)
let {ctxData, makeLink} = module(MSW.GraphQL)

let gatewayLink = makeLink("http://localhost/graphql")

let mockTaxEdge = (~id, ~value) => {
  TestableQuery.Raw.__typename: "",
  node: {
    __typename: "",
    id: id->Uuid.toString,
    value,
    shop: {
      __typename: "",
      id: "",
    },
  },
}

let server = setupNodeServer([
  gatewayLink->MSWHelpers.apolloQueryWithDelayExn(module(TestableQuery), (_req, res, ctx) =>
    res(.
      ctx->ctxDelayDuration(100),
      ctx->ctxData({
        taxes: {
          __typename: "",
          edges: [
            mockTaxEdge(~id=mockedIdTax0, ~value=0.),
            mockTaxEdge(~id=mockedIdTax20, ~value=20.),
            mockTaxEdge(~id=mockedIdTax5f5, ~value=5.5),
          ],
        },
      }),
    )
  ),
])

beforeAll(() => server->listen({onUnhandledRequest: #warn}))
afterEach(() => server->resetHandlers)
afterAll(() => server->close)

let render = element =>
  element->renderWithOptions(
    ~options={wrapper: props => <Providers> {props["children"]} </Providers>},
  )

module TestableCatalogProductTaxSelect = {
  @react.component
  let make = (~defaultValue, ~defaultTaxValue, ~onChange=fn1(ignore)->fn) => {
    let (value, setValue) = React.useState(() => defaultValue)

    <CatalogProductTaxSelect
      defaultTaxValue
      value
      onChange={value => {
        setValue(_ => Some(value))
        onChange(value)
      }}
    />
  }
}

describe("component", () => {
  let userEvent = TestingLibraryEvent.setup()

  itPromise("should dispatch the fetched tax matching the default tax value", async () => {
    let value = None
    let defaultTaxValue = Some(5.5)
    let onChange = fn1(ignore)

    <TestableCatalogProductTaxSelect defaultValue=value defaultTaxValue onChange={onChange->fn} />
    ->render
    ->ignore

    let triggerElement = screen->getByRoleExn(#button)

    expect(triggerElement)->toBeVisible
    expect(triggerElement)->toHaveTextContent("Loading...")
    expect(triggerElement)->toBeDisabled
    expect(onChange)->toHaveBeenCalledTimes(0)

    await waitFor(() => expect(triggerElement)->Vitest.not->toBeDisabled)

    await userEvent->TestingLibraryEvent.click(triggerElement)

    let listbox = screen->getByRoleExn(#listbox)
    let (taxValue0, taxValue5f5, taxValue20) = within(listbox)->getAllByRoleExn3(#option)

    expect(taxValue0)->toHaveTextContent("0,00 %")
    expect(taxValue5f5)->toHaveTextContent("5,50 %")
    expect(taxValue20)->toHaveTextContent("20,00 %")

    await userEvent->TestingLibraryEvent.click(taxValue20)

    expect(onChange)->toHaveBeenCalledTimes(2)
    expect(onChange)->toHaveBeenLastCalledWith1({
      CatalogProductTaxSelect.id: mockedIdTax20,
      value: 20.,
    })
  })

  itPromise(
    "should dispatch the fetched tax with the highest tax value when not default tax value is passed",
    async () => {
      let value = None
      let defaultTaxValue = None
      let onChange = fn1(ignore)

      <TestableCatalogProductTaxSelect defaultValue=value defaultTaxValue onChange={onChange->fn} />
      ->render
      ->ignore

      let triggerElement = screen->getByRoleExn(#button)

      expect(triggerElement)->toBeVisible
      expect(triggerElement)->toHaveTextContent("Loading...")
      expect(triggerElement)->toBeDisabled

      expect(onChange)->toHaveBeenCalledTimes(0)

      await waitFor(() => expect(triggerElement)->Vitest.not->toBeDisabled)

      expect(onChange)->toHaveBeenCalledTimes(1)
      expect(onChange)->toHaveBeenLastCalledWith1({
        CatalogProductTaxSelect.id: mockedIdTax20,
        value: 20.,
      })
    },
  )

  itPromise("should dispatch first fetched tax when no matching default tax value", async () => {
    let value = None
    let defaultTaxValue = Some(12.)
    let onChange = fn1(ignore)

    <TestableCatalogProductTaxSelect defaultValue=value defaultTaxValue onChange={onChange->fn} />
    ->render
    ->ignore

    let triggerElement = screen->getByRoleExn(#button)

    expect(triggerElement)->toBeVisible
    expect(triggerElement)->toHaveTextContent("Loading...")
    expect(triggerElement)->toBeDisabled
    expect(onChange)->toHaveBeenCalledTimes(0)

    await waitFor(() => expect(triggerElement)->Vitest.not->toBeDisabled)

    expect(onChange)->toHaveBeenCalledTimes(1)
    expect(onChange)->toHaveBeenLastCalledWith1({
      CatalogProductTaxSelect.id: mockedIdTax0,
      value: 0.,
    })
  })

  itPromise(
    "should not dispatch any fetched tax when the already value exists and ignore default value",
    async () => {
      let value = Some({
        CatalogProductTaxSelect.id: mockedIdTax20,
        value: 20.,
      })
      let defaultTaxValue = Some(5.5)
      let onChange = fn1(ignore)

      <TestableCatalogProductTaxSelect defaultValue=value defaultTaxValue onChange={onChange->fn} />
      ->render
      ->ignore

      let triggerElement = screen->getByRoleExn(#button)

      expect(triggerElement)->toBeVisible
      expect(triggerElement)->toHaveTextContent("Loading...") // NOTE - rendering delay in Select
      expect(triggerElement)->Vitest.not->toBeDisabled
      expect(onChange)->toHaveBeenCalledTimes(0)

      await waitFor(() => expect(triggerElement)->toHaveTextContent("20,00 %"))

      expect(onChange)->toHaveBeenCalledTimes(0)

      await userEvent->TestingLibraryEvent.click(triggerElement)

      let listbox = screen->getByRoleExn(#listbox)
      let (taxValue0, taxValue5f5) = within(listbox)->getAllByRoleExn2(#option)

      expect(taxValue0)->toHaveTextContent("0,00 %")
      expect(taxValue5f5)->toHaveTextContent("5,50 %")
      // NOTE - taxValue20: already selected item doesn't appear

      await userEvent->TestingLibraryEvent.click(taxValue0)

      expect(onChange)->toHaveBeenCalledTimes(1)
      expect(onChange)->toHaveBeenLastCalledWith1({
        CatalogProductTaxSelect.id: mockedIdTax0,
        value: 0.,
      })
    },
  )
})
