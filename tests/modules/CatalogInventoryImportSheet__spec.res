open Vitest

describe("parseAndDecodeXlsxFile", () => {
  let excelXlsxFileFromUnsafeObjExn = unsafeObj =>
    Obj.magic(unsafeObj)
    ->Sheet.makeExcelBlob(~worksheetName="")
    ->Future.mapOk(File.fromBlob)
    ->Future.map(result => result->Result.getExn)

  itFuture("should parse and decode", () => {
    let excelXlsxFile = excelXlsxFileFromUnsafeObjExn(
      %raw(`[
        ["headers"],
        ["ID", "Inventory quantity", "misc-header"],
        ["e1e01f02-168f-4f9f-b499-5f39022acbef", 1., "misc-data"],
        ["8ebcf9af-1238-4163-af1d-17865ef74ef9", 9.21, "misc-data"],
        ["40cc3b99-d020-486d-b2ef-27aa97b30d2d", "17,330", "misc-data"],
      ]`),
    )

    excelXlsxFile
    ->Future.flatMap(
      xlsx => CatalogInventoryImportSheet.parseAndDecodeXlsxFile(xlsx, ~headerIndexOffset=2),
    )
    ->Future.tap(
      value =>
        expect(value)->toStrictEqual({
          headerOffset: 2,
          payload: Ok([
            Ok({
              variantId: "e1e01f02-168f-4f9f-b499-5f39022acbef",
              stock: 1.,
            }),
            Ok({
              variantId: "8ebcf9af-1238-4163-af1d-17865ef74ef9",
              stock: 9.21,
            }),
            Ok({
              variantId: "40cc3b99-d020-486d-b2ef-27aa97b30d2d",
              stock: 17.33,
            }),
          ]),
        }),
    )
  })

  itFuture("should parse and decode a xlsx with an empty column stock", () => {
    let excelXlsxFile = excelXlsxFileFromUnsafeObjExn(
      %raw(`[
        ["ID", "Inventory quantity", "misc-header"],
        ["e1e01f02-168f-4f9f-b499-5f39022acbef", "", "misc-data"],
        ["8ebcf9af-1238-4163-af1d-17865ef74ef9", "", "misc-data"],
        ["40cc3b99-d020-486d-b2ef-27aa97b30d2d", "", "misc-data"],
      ]`),
    )

    excelXlsxFile
    ->Future.flatMap(xlsx => CatalogInventoryImportSheet.parseAndDecodeXlsxFile(xlsx))
    ->Future.tap(
      value =>
        expect(value)->toStrictEqual({
          headerOffset: 1,
          payload: Error(EmptyColumnStock),
        }),
    )
  })

  itFuture("should parse and decode with some errors", () => {
    let excelXlsxFile = excelXlsxFileFromUnsafeObjExn(
      %raw(`[
        ["========================"],
        ["ID", "Inventory quantity"],
        ["not-an-uuid", "1"],
        ["00000000-0000-0000-0000-000000000000", "0"],
        ["dd05a0b4-b20f-4c53-9646-170823200be5", { result: 4., formula: "=MIN(6;4;9)" }],
        ["d7367e02-dfe7-42da-82a4-12803526bb8f", { result: 4. }],
        ["8ebcf9af-1238-4163-af1d-17865ef74ef9", "not-a-number"],
        ["cef97230-ad26-4dc0-aa7b-4ff21e4f3cde", "-2"],
      ]`),
    )

    excelXlsxFile
    ->Future.flatMap(
      xlsx => CatalogInventoryImportSheet.parseAndDecodeXlsxFile(xlsx, ~headerIndexOffset=2),
    )
    ->Future.tap(
      value =>
        expect(value)->toStrictEqual({
          headerOffset: 2,
          payload: Ok([
            Error(
              CannotDecodeCellVariantId({
                index: 2,
                json: Json.encodeString("not-an-uuid"),
              }),
            ),
            Ok({
              variantId: "00000000-0000-0000-0000-000000000000",
              stock: 0.,
            }),
            Ok({
              variantId: "dd05a0b4-b20f-4c53-9646-170823200be5",
              stock: 4.,
            }),
            Error(
              CannotDecodeCellStock({
                index: 5,
                json: Json.encodeString("{\"result\":4}"),
              }),
            ),
            Error(
              CannotDecodeCellStock({
                index: 6,
                json: Json.encodeString("not-a-number"),
              }),
            ),
            Error(
              NegativeCellStock({
                index: 7,
                json: Json.encodeString("-2"),
              }),
            ),
          ]),
        }),
    )
  })

  itFuture("should parse and decode with only errors", () => {
    let excelXlsxFile = excelXlsxFileFromUnsafeObjExn(
      %raw(`[
        ["ID", "Inventory quantity"],
        ["not-an-uuid", "1"],
        ["8ebcf9af-1238-4163-af1d-17865ef74ef9", ""],
        ["cef97230-ad26-4dc0-aa7b-4ff21e4f3cde", 1e10000],
      ]`),
    )

    excelXlsxFile
    ->Future.flatMap(xlsx => CatalogInventoryImportSheet.parseAndDecodeXlsxFile(xlsx))
    ->Future.tap(
      value =>
        expect(value)->toStrictEqual({
          headerOffset: 1,
          payload: Ok([
            Error(
              CannotDecodeCellVariantId({
                index: 1,
                json: Json.encodeString("not-an-uuid"),
              }),
            ),
            Error(
              EmptyCellStock({
                index: 2,
              }),
            ),
            Error(
              CannotDecodeCellStock({
                index: 3,
                json: Json.encodeNumber(1e10000),
              }),
            ),
          ]),
        }),
    )
  })

  itFuture("should parse with missing first header row", () => {
    let excelXlsxFile = excelXlsxFileFromUnsafeObjExn(
      %raw(`[
        ["ID", "Inventory quantity", "misc-header"],
        ["e1e01f02-168f-4f9f-b499-5f39022acbef", 1., "misc-data"],
        ["8ebcf9af-1238-4163-af1d-17865ef74ef9", 9.21, "misc-data"],
        ["40cc3b99-d020-486d-b2ef-27aa97b30d2d", "17.330", "misc-data"],
      ]`),
    )

    excelXlsxFile
    ->Future.flatMap(xlsx => CatalogInventoryImportSheet.parseAndDecodeXlsxFile(xlsx))
    ->Future.tap(
      value =>
        expect(value)->toStrictEqual({
          headerOffset: 1,
          payload: Ok([
            Ok({
              variantId: "e1e01f02-168f-4f9f-b499-5f39022acbef",
              stock: 1.,
            }),
            Ok({
              variantId: "8ebcf9af-1238-4163-af1d-17865ef74ef9",
              stock: 9.21,
            }),
            Ok({
              variantId: "40cc3b99-d020-486d-b2ef-27aa97b30d2d",
              stock: 17.33,
            }),
          ]),
        }),
    )
  })

  itFuture("should parse with header row but not body rows", () => {
    let excelXlsxFile = excelXlsxFileFromUnsafeObjExn(
      %raw(`[
        ["ID", "Inventory quantity"],
      ]`),
    )

    excelXlsxFile
    ->Future.flatMap(xlsx => CatalogInventoryImportSheet.parseAndDecodeXlsxFile(xlsx))
    ->Future.tap(
      value =>
        expect(value)->toStrictEqual({
          headerOffset: 1,
          payload: Error(NoRows),
        }),
    )
  })

  itFuture("should parse unknown file", () => {
    let excelXlsxFile = excelXlsxFileFromUnsafeObjExn(
      %raw(`[
        ["A", "B", "C"],
        ["id-0", 1., ""],
        ["id-1", 2., ""],
        ["id-2", 3., ""],
      ]`),
    )

    excelXlsxFile
    ->Future.flatMap(xlsx => CatalogInventoryImportSheet.parseAndDecodeXlsxFile(xlsx))
    ->Future.tap(
      value =>
        expect(value)->toStrictEqual({
          headerOffset: 1,
          payload: Error(InvalidHeaderRow),
        }),
    )
  })

  itFuture("should parse with incorrect data file", () => {
    let excelXlsxFile = excelXlsxFileFromUnsafeObjExn(00110001111111001100101100.)

    excelXlsxFile
    ->Future.flatMap(xlsx => CatalogInventoryImportSheet.parseAndDecodeXlsxFile(xlsx))
    ->Future.tap(
      value =>
        expect(value)->toStrictEqual({
          headerOffset: 1,
          payload: Error(NoRows),
        }),
    )
  })

  itFuture("should parse with an empty file", () => {
    let excelXlsxFile = excelXlsxFileFromUnsafeObjExn("")

    excelXlsxFile
    ->Future.flatMap(xlsx => CatalogInventoryImportSheet.parseAndDecodeXlsxFile(xlsx))
    ->Future.tap(
      value =>
        expect(value)->toStrictEqual({
          headerOffset: 1,
          payload: Error(NoRows),
        }),
    )
  })
})

describe("parseAndDecodeCsvFile", () => {
  itFuture("should parse and decode", () => {
    let csvFile = Obj.magic(`
e1e01f02-168f-4f9f-b499-5f39022acbef;1
8ebcf9af-1238-4163-af1d-17865ef74ef9;9.21
`)

    CatalogInventoryImportSheet.parseAndDecodeCsvFile(csvFile)->Future.tap(
      value =>
        expect(value)->toStrictEqual({
          headerOffset: 0,
          payload: Ok([
            Ok({
              variantId: "e1e01f02-168f-4f9f-b499-5f39022acbef",
              stock: 1.,
            }),
            Ok({
              variantId: "8ebcf9af-1238-4163-af1d-17865ef74ef9",
              stock: 9.21,
            }),
          ]),
        }),
    )
  })

  itFuture("should ignore empty columns/lines", () => {
    let csvFile = Obj.magic(`
e1e01f02-168f-4f9f-b499-5f39022acbef;0;
;
8ebcf9af-1238-4163-af1d-17865ef74ef9;2;
;
`)

    CatalogInventoryImportSheet.parseAndDecodeCsvFile(csvFile)->Future.tap(
      value =>
        expect(value)->toStrictEqual({
          headerOffset: 0,
          payload: Ok([
            Ok({
              variantId: "e1e01f02-168f-4f9f-b499-5f39022acbef",
              stock: 0.,
            }),
            Ok({
              variantId: "8ebcf9af-1238-4163-af1d-17865ef74ef9",
              stock: 2.,
            }),
          ]),
        }),
    )
  })

  itFuture("should not parse with incorrect data file", () => {
    let csvFile = Obj.magic(00110001111111001100101100.)

    CatalogInventoryImportSheet.parseAndDecodeCsvFile(csvFile)->Future.tap(
      value =>
        expect(value)->toStrictEqual({
          headerOffset: 0,
          payload: Error(Unknown),
        }),
    )
  })

  itFuture("should parse with an empty file", () => {
    let csvFile = Obj.magic(``)

    CatalogInventoryImportSheet.parseAndDecodeCsvFile(csvFile)->Future.tap(
      value =>
        expect(value)->toStrictEqual({
          headerOffset: 0,
          payload: Error(NoRows),
        }),
    )
  })

  itFuture("should parse and decode with errors", () => {
    let csvFile = Obj.magic(`
not-an-uuid;1
00000000-0000-0000-0000-000000000000;0
8ebcf9af-1238-4163-af1d-17865ef74ef9;not-a-number
cef97230-ad26-4dc0-aa7b-4ff21e4f3cde;-2
;-1
`)

    CatalogInventoryImportSheet.parseAndDecodeCsvFile(csvFile)->Future.tap(
      value =>
        expect(value)->toStrictEqual({
          headerOffset: 0,
          payload: Ok([
            Error(
              CannotDecodeCellVariantId({
                index: 0,
                json: Json.encodeString("not-an-uuid"),
              }),
            ),
            Ok({
              variantId: "00000000-0000-0000-0000-000000000000",
              stock: 0.,
            }),
            Error(
              CannotDecodeCellStock({
                index: 2,
                json: Json.encodeString("not-a-number"),
              }),
            ),
            Error(
              NegativeCellStock({
                index: 3,
                json: Json.encodeString("-2"),
              }),
            ),
            Error(
              EmptyCellVariantId({
                index: 4,
              }),
            ),
          ]),
        }),
    )
  })
})

test("decodeRowVariantId", () => {
  let {decodeRowVariantId} = module(CatalogInventoryImportSheet)

  let result = decodeRowVariantId(Json.encodeString("e1e01f02-168f-4f9f-b499-5f39022acbef"))
  expect(result)->toStrictEqual(Ok("e1e01f02-168f-4f9f-b499-5f39022acbef"))

  let result = decodeRowVariantId(Json.encodeString(""))
  expect(result)->toStrictEqual(Error(Empty))

  let result = decodeRowVariantId(Json.encodedNull)
  expect(result)->toStrictEqual(Error(Empty))

  let result = decodeRowVariantId(Json.encodeString("not-uuid"))
  expect(result)->toStrictEqual(Error(Unkown))

  let result = decodeRowVariantId(Json.fromObjExn(42))
  expect(result)->toStrictEqual(Error(Unkown))
})

test("decodeRowStock", () => {
  let {decodeRowStock} = module(CatalogInventoryImportSheet)

  let result = decodeRowStock(Json.encodeNumber(3.))
  expect(result)->toStrictEqual(Ok(3.))

  let result = decodeRowStock(Json.encodeString("3."))
  expect(result)->toStrictEqual(Ok(3.))

  let result = decodeRowStock(Json.encodeNumber(25.567))
  expect(result)->toStrictEqual(Ok(25.567))

  let result = decodeRowStock(Json.encodeString("25.567"))
  expect(result)->toStrictEqual(Ok(25.567))

  let result = decodeRowStock(Json.encodeString("1.23456789"))
  expect(result)->toStrictEqual(Ok(1.23456789))

  let result = decodeRowStock(Json.encodeNumber(1.23456789))
  expect(result)->toStrictEqual(Ok(1.23456789))

  let result = decodeRowStock(Json.encodeString("123456789"))
  expect(result)->toStrictEqual(Ok(123456789.))

  let result = decodeRowStock(Json.encodeNumber(123456789.))
  expect(result)->toStrictEqual(Ok(123456789.))

  let result = decodeRowStock(Json.encodeString("5,500"))
  expect(result)->toStrictEqual(Ok(5.5))

  let result = decodeRowStock(Json.encodeNumber(0.))
  expect(result)->toStrictEqual(Ok(0.))

  let result = decodeRowStock(Json.encodeString("0."))
  expect(result)->toStrictEqual(Ok(0.))

  let result = decodeRowStock(Json.encodeNumber(-2.))
  expect(result)->toStrictEqual(Error(Negative))

  let result = decodeRowStock(Json.encodeString("-2"))
  expect(result)->toStrictEqual(Error(Negative))

  let result = decodeRowStock(Json.encodeString("2cL"))
  expect(result)->toStrictEqual(Error(NotNumber))

  let result = decodeRowStock(Json.encodeString("1.25 L"))
  expect(result)->toStrictEqual(Error(NotNumber))

  let result = decodeRowStock(Json.encodeString(""))
  expect(result)->toStrictEqual(Error(Empty))

  let result = decodeRowStock(Json.encodedNull)
  expect(result)->toStrictEqual(Error(Empty))

  let result = decodeRowStock(Json.encodeNumber(1e10000))
  expect(result)->toStrictEqual(Error(NonFinite))

  let result = decodeRowStock(Json.encodeDict(Js.Dict.empty()))
  expect(result)->toStrictEqual(Error(Unknown))
})

test("decodeTupleRowWithIndex", () => {
  let {decodeTupleRowWithIndex} = module(CatalogInventoryImportSheet)

  let result = decodeTupleRowWithIndex(
    0,
    [Json.encodeString("e1e01f02-168f-4f9f-b499-5f39022acbef"), Json.encodeString("2")],
  )
  expect(result)->toStrictEqual(
    Ok({
      variantId: "e1e01f02-168f-4f9f-b499-5f39022acbef",
      stock: 2.,
    }),
  )

  let result = decodeTupleRowWithIndex(0, [Json.encodeString("not-uuid"), Json.encodeString("2")])
  expect(result)->toStrictEqual(
    Error(
      CannotDecodeCellVariantId({
        index: 0,
        json: Json.encodeString("not-uuid"),
      }),
    ),
  )

  let result = decodeTupleRowWithIndex(
    0,
    [Json.encodeString("e1e01f02-168f-4f9f-b499-5f39022acbef")],
  )
  expect(result)->toStrictEqual(
    Error(
      CannotDecodeRow({
        index: 0,
        json: Json.fromObjExn([Json.encodeString("e1e01f02-168f-4f9f-b499-5f39022acbef")]),
      }),
    ),
  )
})

test("decodeDictRowWithIndex", () => {
  let {decodeDictRowWithIndex} = module(CatalogInventoryImportSheet)

  expect(
    decodeDictRowWithIndex(
      0,
      Js.Dict.fromArray([
        ("ID", Json.encodeString("e1e01f02-168f-4f9f-b499-5f39022acbef")),
        ("Inventory quantity", Json.fromObjExn(2)),
      ]),
    ),
  )->toStrictEqual(
    Ok({
      variantId: "e1e01f02-168f-4f9f-b499-5f39022acbef",
      stock: 2.,
    }),
  )

  expect(
    decodeDictRowWithIndex(
      0,
      Js.Dict.fromArray([
        ("ID", Json.encodeString("not-uuid")),
        ("Inventory quantity", Json.fromObjExn(2)),
      ]),
    ),
  )->toStrictEqual(
    Error(
      CannotDecodeCellVariantId({
        index: 0,
        json: Json.encodeString("not-uuid"),
      }),
    ),
  )

  expect(
    decodeDictRowWithIndex(
      0,
      Js.Dict.fromArray([
        ("ID", Json.encodeString("e1e01f02-168f-4f9f-b499-5f39022acbef")),
        ("Inventory quantity", Json.encodeNumber(-999.)),
      ]),
    ),
  )->toStrictEqual(
    Error(
      NegativeCellStock({
        index: 0,
        json: Json.encodeNumber(-999.),
      }),
    ),
  )

  expect(
    decodeDictRowWithIndex(
      0,
      Js.Dict.fromArray([
        ("ID", Json.encodeString("e1e01f02-168f-4f9f-b499-5f39022acbef")),
        ("Inventory quantity", Json.encodeString("")),
      ]),
    ),
  )->toStrictEqual(
    Error(
      EmptyCellStock({
        index: 0,
      }),
    ),
  )

  expect(
    decodeDictRowWithIndex(
      0,
      Js.Dict.fromArray([
        ("bad header", Json.encodeString("e1e01f02-168f-4f9f-b499-5f39022acbef")),
        ("Inventory quantity", Json.encodeNumber(2.)),
      ]),
    ),
  )->toStrictEqual(
    Error(
      CannotDecodeHeaderRow({
        index: 0,
        json: Json.fromObjExn({
          "bad header": Json.encodeString("e1e01f02-168f-4f9f-b499-5f39022acbef"),
          "Inventory quantity": Json.encodeNumber(2.),
        }),
      }),
    ),
  )

  expect(
    decodeDictRowWithIndex(
      1,
      Js.Dict.fromArray([
        ("ID", Json.encodeString("e1e01f02-168f-4f9f-b499-5f39022acbef")),
        ("bad header", Json.encodeNumber(2.)),
      ]),
    ),
  )->toStrictEqual(
    Error(
      CannotDecodeHeaderRow({
        index: 1,
        json: Json.fromObjExn({
          "ID": Json.encodeString("e1e01f02-168f-4f9f-b499-5f39022acbef"),
          "bad header": Json.encodeNumber(2.),
        }),
      }),
    ),
  )
})

describe("decodeRows", () => {
  let {decodeRows} = module(CatalogInventoryImportSheet)

  it("should decode tuple rows", () => {
    let tupleRows = %raw(`[
      ["e1e01f02-168f-4f9f-b499-5f39022acbef", "1"],
      ["8ebcf9af-1238-4163-af1d-17865ef74ef9", 9.21],
    ]`)

    expect(decodeRows(tupleRows))->toStrictEqual(
      Ok([
        Ok({
          variantId: "e1e01f02-168f-4f9f-b499-5f39022acbef",
          stock: 1.,
        }),
        Ok({
          variantId: "8ebcf9af-1238-4163-af1d-17865ef74ef9",
          stock: 9.21,
        }),
      ]),
    )
  })

  it("should decode dict rows", () => {
    let dictRows = %raw(`[
      {
        "ID": "e1e01f02-168f-4f9f-b499-5f39022acbef",
        "Inventory quantity": "1",
      },
      {
        "ID": "8ebcf9af-1238-4163-af1d-17865ef74ef9",
        "Inventory quantity": 9.21,
      },
    ]`)

    expect(decodeRows(dictRows))->toStrictEqual(
      Ok([
        Ok({
          variantId: "e1e01f02-168f-4f9f-b499-5f39022acbef",
          stock: 1.,
        }),
        Ok({
          variantId: "8ebcf9af-1238-4163-af1d-17865ef74ef9",
          stock: 9.21,
        }),
      ]),
    )
  })

  it("should not decode rows", () => {
    let tupleRows = %raw(`["bad row", 9.21]`)
    expect(decodeRows(tupleRows))->toStrictEqual(
      Ok([
        Error(
          CannotDecodeRow({
            index: 0,
            json: "bad row"->Json.encodeString,
          }),
        ),
        Error(
          CannotDecodeRow({
            index: 1,
            json: 9.21->Json.encodeNumber,
          }),
        ),
      ]),
    )
  })
})

todo("decodeRowValues")
