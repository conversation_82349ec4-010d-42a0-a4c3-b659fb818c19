open Vitest

let {rowsFromEdgesData, makeVariables} = module(PromotionEditDiscountedProductPickerModalTable)

let mockTypename = () => ""

let mockQueryProductTaxData = (
  ~productTaxValue=5.5,
  ~productId="",
  ~productName="",
  ~productKind=#SIMPLE,
  ~productColor=None,
  ~productProducer=None,
  ~productDesignation=None,
  ~productWineType=None,
  ~productWhiteWineType=None,
  ~productFamily=None,
  ~productBeerType=None,
  ~productRegion=None,
  ~productCountry=None,
  (),
) => {
  __typename: mockTypename(),
  id: productId,
  name: productName,
  kind: productKind,
  producer: productProducer,
  designation: productDesignation,
  family: productFamily,
  beerType: productBeerType,
  color: productColor,
  wineType: productWineType,
  whiteWineType: productWhiteWineType,
  region: productRegion,
  country: productCountry,
  PromotionEditDiscountedProductPickerModalTable.Query.tax: {
    __typename: mockTypename(),
    value: productTaxValue,
  },
}

let mockQueryVariantPricesDataEdge = (
  ~valueIncludingTax=0.,
  ~priceName="",
  (),
): PromotionEditDiscountedProductPickerModalTable.Query.t_variantsDistinctOnCku_edges_node_variantPrices_edges => {
  node: {
    valueIncludingTax,
    price: Some({
      name: priceName,
      __typename: mockTypename(),
    }),
    __typename: mockTypename(),
  },
  __typename: mockTypename(),
}

let mockQueryVariantPricesData = (~edges=[], ()) => {
  PromotionEditDiscountedProductPickerModalTable.Query.edges,
  __typename: mockTypename(),
}

let mockQueryData = (
  ~id="mock-id",
  ~name="",
  ~formattedName="",
  ~supplierCompanyName="",
  ~bulk=None,
  ~capacityUnit=Some("mock-capacity-unit"),
  ~purchasedPrice=None,
  ~formattedCategory=None,
  ~stockKeepingUnit=None,
  ~priceLookUpCode=None,
  ~internalCode=None,
  ~alcoholVolume=None,
  ~product=mockQueryProductTaxData(),
  ~variantPrices=mockQueryVariantPricesData(),
  (),
) => {
  __typename: mockTypename(),
  PromotionEditDiscountedProductPickerModalTable.Query.node: {
    __typename: mockTypename(),
    id,
    cku: "mock-cku",
    name,
    createdAt: Js.Date.makeWithYM(~year=2023., ~month=1., ()),
    formattedName,
    formattedCategory,
    stockKeepingUnit,
    priceLookUpCode,
    internalCode,
    alcoholVolume,
    bulk,
    capacityUnit,
    purchasedPrice,
    supplier: Some({
      companyName: supplierCompanyName,
      __typename: mockTypename(),
    }),
    product,
    variantPrices,
  },
}

describe("rowsFromEdgesData", () => {
  it("should map with default values", () => {
    let result = rowsFromEdgesData([mockQueryData()])
    expect(result)->toEqual([
      {
        id: "mock-id",
        cku: "mock-cku",
        createdAt: Js.Date.makeWithYM(~year=2023., ~month=1., ()),
        formattedName: "",
        productKind: #SIMPLE,
        information: {
          productName: "",
          variantName: "",
          country: "—",
          categoryName: "—",
          supplierName: "",
        },
        bulkUnit: None,
        purchasePrice: 0.,
        retailPrices: [],
      },
    ])

    let result = rowsFromEdgesData([
      mockQueryData(
        ~purchasedPrice=Some(2.99),
        ~product=mockQueryProductTaxData(
          ~productTaxValue=5.5,
          ~productId="mock-product-id",
          ~productName="mock-product-name",
          ~productProducer=Some("mock-product-producer"),
          ~productKind=#SIMPLE,
          ~productColor=Some(#AMBER),
          ~productDesignation=Some("mock-product-designation"),
          ~productWineType=Some(#STILL),
          ~productWhiteWineType=Some(#SWEET),
          ~productFamily=Some("mock-product-family"),
          ~productBeerType=Some("mock-product-beertype"),
          ~productRegion=Some("mock-product-region"),
          ~productCountry=Some("mock-product-country"),
          (),
        ),
        ~variantPrices=mockQueryVariantPricesData(
          ~edges=[
            mockQueryVariantPricesDataEdge(
              ~priceName="mock-variant-price-name",
              ~valueIncludingTax=2.5,
              (),
            ),
          ],
          (),
        ),
        ~stockKeepingUnit=Some("12345"),
        ~priceLookUpCode=Some(1),
        ~internalCode=Some("abcde"),
        ~formattedName="mock-formatted-name",
        ~name="mock-variant-name",
        ~supplierCompanyName="mock-supplier-name",
        ~formattedCategory=Some("mock-formatted-category"),
        ~alcoholVolume=Some(12.),
        (),
      ),
    ])
    expect(result)->toStrictEqual([
      {
        id: "mock-id",
        cku: "mock-cku",
        createdAt: Js.Date.makeWithYM(~year=2023., ~month=1., ()),
        formattedName: "mock-formatted-name",
        productKind: #SIMPLE,
        information: {
          productName: "mock-product-name",
          variantName: "mock-variant-name",
          producerName: "mock-product-producer",
          color: #AMBER,
          designation: "mock-product-designation",
          wineType: #STILL,
          whiteWineType: #SWEET,
          productFamily: "mock-product-family",
          beerType: "mock-product-beertype",
          region: "mock-product-region",
          country: "mock-product-country",
          categoryName: "mock-formatted-category",
          supplierName: "mock-supplier-name",
          alcoholVolume: "12°",
          sku: "12345",
          plu: "1",
          internalCode: "abcde",
        },
        bulkUnit: None,
        purchasePrice: 2.99,
        retailPrices: [
          {
            priceName: "mock-variant-price-name",
            valueIncludingTax: 2.5,
          },
        ],
      },
    ])
  })

  testEach2([
    (mockQueryData(~bulk=Some(true), ~capacityUnit=Some("L"), ()), Some("L")),
    (mockQueryData(~bulk=Some(false), ~capacityUnit=Some("L"), ()), None),
    (mockQueryData(~bulk=None, ~capacityUnit=Some("L"), ()), None),
    (mockQueryData(~bulk=Some(true), ~capacityUnit=None, ()), None),
    (mockQueryData(~bulk=Some(false), ~capacityUnit=None, ()), None),
    (mockQueryData(~bulk=None, ~capacityUnit=None, ()), None),
  ])(."should map `bulkUnit` from related properties", (input, output) => {
    let result = rowsFromEdgesData([input])->Array.getExn(0)
    expect(result.bulkUnit)->toBe(output)
  })
})

test("makeVariables", () => {
  let result = makeVariables(~search="", ())
  expect(result)->toStrictEqual({
    search: Some(""),
    after: None,
    first: Some(20),
  })

  let result = makeVariables(~after="mock-after", ~search="mock-search", ())
  expect(result)->toStrictEqual({
    search: Some("mock-search"),
    after: Some("mock-after"),
    first: Some(20),
  })
})
