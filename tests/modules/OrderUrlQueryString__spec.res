open Vitest
open OrderUrlQueryString

describe("CreateOrder", () => {
  it("should encode and decode in a query string correctly", () => {
    let jsonQueryString = CreateOrder.encode({
      supplierId: "mocked-supplier-id",
      shopId: "mocked-shop-id",
    })

    expect(jsonQueryString)->toUnsafeStrictEqual(
      "supplierId=%22mocked-supplier-id%22&shopId=%22mocked-shop-id%22",
    )
    expect(CreateOrder.decode(jsonQueryString))->toStrictEqual((
      Some("mocked-supplier-id"),
      Some("mocked-shop-id"),
    ))
  })
})
