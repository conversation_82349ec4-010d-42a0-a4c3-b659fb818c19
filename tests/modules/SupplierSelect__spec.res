open Vitest
open TestingLibraryReact

let mockTypename = () => ""

let mockQuerySuppliersDataEdge = (~id, ~companyName) => {
  SupplierSelect.Query.node: {
    id,
    companyName,
    __typename: mockTypename(),
  },
  __typename: mockTypename(),
}

describe("component", () => {
  let userEvent = TestingLibraryEvent.setup()

  itPromise("should be disabled when there is no shopId", async () => {
    let onChange = fn1(ignore)
    let useSuppliersQuery = (~shopId as _, ~skip as _) => AsyncData.Done([])

    render(
      <SupplierSelect
        preset=#filter shopId=None value=None useSuppliersQuery onChange={onChange->fn}
      />,
    )->ignore

    let triggerElement = screen->getByRoleExn(#button)

    expect(triggerElement)->toBeVisible
    expect(triggerElement)->toHaveTextContent("Select a supplier")
    expect(triggerElement)->toBeDisabled
    expect(onChange)->toHaveBeenCalledTimes(0)
  })

  itPromise("should be a default item with 'All' selected", async () => {
    let onChange = fn1(ignore)
    let useSuppliersQuery = (~shopId as _, ~skip as _) => AsyncData.Done([])

    render(
      <SupplierSelect
        preset=#filter
        showDefaultItem=true
        shopId=None
        value=None
        useSuppliersQuery
        onChange={onChange->fn}
      />,
    )->ignore

    let triggerElement = screen->getByRoleExn(#button)

    expect(triggerElement)->toBeVisible
    expect(triggerElement)->toHaveTextContent("All")
    expect(triggerElement)->toBeDisabled
    expect(onChange)->toHaveBeenCalledTimes(0)
  })

  itPromise("should render with the value passed selected", async () => {
    let onChange = fn1(ignore)
    let useSuppliersQuery = (~shopId as _, ~skip as _) => AsyncData.Done([
      mockQuerySuppliersDataEdge(~id="mock-supplier-id-1", ~companyName="mock-supplier-name-1"),
      mockQuerySuppliersDataEdge(~id="mock-supplier-id-0", ~companyName="mock-supplier-name-0"),
    ])

    render(
      <Providers>
        <SupplierSelect
          preset=#filter
          shopId=Some("mock-shop-id")
          value=Some({
            SupplierSelect.id: "mock-supplier-id-1"->Js.Nullable.return,
            name: "mock-supplier-name-1",
          })
          useSuppliersQuery
          onChange={onChange->fn}
        />
      </Providers>,
    )->ignore

    let triggerElement = screen->getByRoleExn(#button)

    expect(triggerElement)->toBeVisible
    expect(triggerElement)->toHaveTextContent("mock-supplier-name-1")
    expect(triggerElement)->Vitest.not->toBeDisabled
    expect(onChange)->toHaveBeenCalledTimes(0)

    await userEvent->TestingLibraryEvent.click(triggerElement)

    let listbox = screen->getByRoleExn(#listbox)
    let (option1, option2) = within(listbox)->getAllByRoleExn2(#option)

    expect(option1)->toHaveTextContent("mock-supplier-name-1")
    expect(option2)->toHaveTextContent("mock-supplier-name-0")

    await userEvent->TestingLibraryEvent.click(option2)

    expect(onChange)->toHaveBeenCalledTimes(1)
    expect(onChange)->toHaveBeenLastCalledWith1(
      Some({id: "mock-supplier-id-0"->Js.Nullable.return, name: "mock-supplier-name-0"}),
    )
  })

  itPromise("should render with a null supplier value passed and set it", async () => {
    let onChange = fn1(ignore)
    let useSuppliersQuery = (~shopId as _, ~skip as _) => AsyncData.Done([])

    render(
      <Providers>
        <SupplierSelect
          preset=#filter
          showDefaultItem=true
          shopId=Some("mock-shop-id")
          value=None
          useSuppliersQuery
          onChange={onChange->fn}
        />
      </Providers>,
    )->ignore

    let triggerElement = screen->getByRoleExn(#button)

    expect(triggerElement)->toBeVisible
    expect(triggerElement)->toHaveTextContent("All")
    expect(triggerElement)->Vitest.not->toBeDisabled
    expect(onChange)->toHaveBeenCalledTimes(0)

    await userEvent->TestingLibraryEvent.click(triggerElement)

    let listbox = screen->getByRoleExn(#listbox)
    let (option1, option2) = within(listbox)->getAllByRoleExn2(#option)

    expect(option1)->toHaveTextContent("All")
    expect(option2)->toHaveTextContent("Not specified")

    await userEvent->TestingLibraryEvent.click(option2)

    expect(onChange)->toHaveBeenCalledTimes(1)
    expect(onChange)->toHaveBeenLastCalledWith1(Some({id: Js.Nullable.null, name: "Not specified"}))
  })

  itPromise("should render with the placeholder when the passed value is not found", async () => {
    let onChange = fn1(ignore)
    let useSuppliersQuery = (~shopId as _, ~skip as _) => AsyncData.Done([])

    render(
      <Providers>
        <SupplierSelect
          preset=#filter
          shopId=Some("mock-shop-id")
          value=None
          useSuppliersQuery
          onChange={onChange->fn}
        />
      </Providers>,
    )->ignore

    let triggerElement = screen->getByRoleExn(#button)

    expect(triggerElement)->toBeVisible
    expect(triggerElement)->toHaveTextContent("Select a supplier")
    expect(triggerElement)->Vitest.not->toBeDisabled
    expect(onChange)->toHaveBeenCalledTimes(0)
  })

  itPromise(
    "should render with a link in overlayFooter and the overlay opened by default",
    async () => {
      let onChange = fn1(ignore)
      let useSuppliersQuery = (~shopId as _, ~skip as _) => AsyncData.Done([])

      let {rerender} = render(
        <Providers>
          <SupplierSelect
            preset=#filter
            shopId=Some("mock-shop-id")
            value=None
            defaultOpen=true
            useSuppliersQuery
            onChange={onChange->fn}
          />
        </Providers>,
      )

      let link = screen->getByRoleExn(#link)

      expect(link)->toHaveAttributeValue("href", "/suppliers/new?shopId=%22mock-shop-id%22")
      expect(link)->toHaveTextContent("Create supplier")
      expect(link)->toBeVisible

      rerender(
        <Providers>
          <SupplierSelect
            preset=#filter
            shopId=Some("mock-shop-id")
            value=None
            defaultOpen=true
            hideOverlayFooter=true
            useSuppliersQuery
            onChange={onChange->fn}
          />
        </Providers>,
      )->ignore

      let link = screen->queryByRole(#link)

      expect(link)->Vitest.not->toBeDefined
    },
  )

  itPromise(
    "should render reset value to `None` when the passed value is not found in the fetched suppliers list",
    async () => {
      let onChange = fn1(ignore)
      let useSuppliersQuery = (~shopId as _, ~skip as _) => AsyncData.Done([
        mockQuerySuppliersDataEdge(~id="mock-supplier-id-0", ~companyName="mock-supplier-name-0"),
      ])

      let {rerender} = render(
        <Providers>
          <SupplierSelect
            preset=#filter
            shopId=Some("mock-shop-id")
            value=Some({
              SupplierSelect.id: "mock-supplier-id-0"->Js.Nullable.return,
              name: "mock-supplier-name-0",
            })
            useSuppliersQuery
            onChange={onChange->fn}
          />
        </Providers>,
      )

      let triggerElement = screen->getByRoleExn(#button)

      expect(triggerElement)->toBeVisible
      expect(triggerElement)->toHaveTextContent("mock-supplier-name-0")
      expect(triggerElement)->Vitest.not->toBeDisabled
      expect(onChange)->toHaveBeenCalledTimes(0)

      let useSuppliersQuery = (~shopId as _, ~skip as _) => AsyncData.Done([
        mockQuerySuppliersDataEdge(~id="mock-supplier-id-2", ~companyName="mock-supplier-name-2"),
      ])

      rerender(
        <Providers>
          <SupplierSelect
            preset=#filter
            shopId=Some("mock-shop-id")
            value=Some({
              SupplierSelect.id: "mock-supplier-id-1"->Js.Nullable.return,
              name: "mock-supplier-name-1",
            })
            useSuppliersQuery
            onChange={onChange->fn}
          />
        </Providers>,
      )->ignore

      let triggerElement = screen->getByRoleExn(#button)

      expect(triggerElement)->toBeVisible
      expect(triggerElement)->toHaveTextContent("Select a supplier")
      expect(triggerElement)->Vitest.not->toBeDisabled
      expect(onChange)->toHaveBeenCalledTimes(1)
      expect(onChange)->toHaveBeenLastCalledWith1(None)
    },
  )
})
