open Vitest

test("toString", () => {
  let {toString} = module(FiscalYearOpeningMonth)
  expect(toString(January))->toBe("January")
  expect(toString(May))->toBe("May")
  expect(toString(June))->toBe("June")
  expect(toString(December))->toBe("December")
})

test("isEqual", () => {
  let {isEqual} = module(FiscalYearOpeningMonth)
  expect(isEqual(January, January))->toBe(true)
  expect(isEqual(January, February))->toBe(false)
  expect(isEqual(March, February))->toBe(false)
  expect(isEqual(December, December))->toBe(true)
})

test("toDateMonthJsIndex", () => {
  let {toDateMonthJsIndex} = module(FiscalYearOpeningMonth)
  expect(toDateMonthJsIndex(January))->toBe(0)
  expect(toDateMonthJsIndex(May))->toBe(4)
  expect(toDateMonthJsIndex(June))->toBe(5)
  expect(toDateMonthJsIndex(December))->toBe(11)
})

test("toSelectItem", () => {
  let {toSelectItem} = module(FiscalYearOpeningMonth)
  expect(toSelectItem(January))->toStrictEqual({
    key: "January",
    label: "January",
    value: January,
  })
  expect(toSelectItem(May))->toStrictEqual({
    key: "May",
    label: "May",
    value: May,
  })
  expect(toSelectItem(June))->toStrictEqual({
    key: "June",
    label: "June",
    value: June,
  })
  expect(toSelectItem(December))->toStrictEqual({
    key: "December",
    label: "December",
    value: December,
  })
})

test("decodeFromJson", () => {
  let {decodeFromJson} = module(FiscalYearOpeningMonth)
  expect(decodeFromJson(Json.encodeNumber(10.)))->toStrictEqual(Some(November))
})

todo("unsafeEncodeToJson")

todo("request")
