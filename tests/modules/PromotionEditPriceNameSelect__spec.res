open Vitest

describe("PromotionEditPriceNameSelect", () => {
  open PromotionEditPriceNameSelect

  describe("makePriceNamesDataset", () => {
    let mockPriceEdge = (~name: string, ~enableByDefault=false, ()) => {
      PricesQuery.__typename: "PriceEdge",
      node: {
        __typename: "Price",
        id: "",
        name,
        enableByDefault,
      },
    }

    it(
      "should return an unique list of priceNames and a defaultPriceName",
      () => {
        let prices = [
          mockPriceEdge(~name="Tarif E-commerce", ()),
          mockPriceEdge(~name="Tarif Pro", ~enableByDefault=true, ()),
          mockPriceEdge(~name="Tarif E-commerce", ()),
        ]

        expect(makePriceNamesDataset(~prices))->toStrictEqual({
          defaultPriceName: Some("Tarif Pro"),
          priceNames: ["Tarif E-commerce", "Tarif Pro"],
        })
      },
    )

    it(
      "should return no defaultPriceName",
      () => {
        let prices = [
          mockPriceEdge(~name="Tarif Pro", ()),
          mockPriceEdge(~name="Tarif E-commerce", ()),
        ]

        expect(makePriceNamesDataset(~prices))->toStrictEqual({
          defaultPriceName: None,
          priceNames: ["Tarif Pro", "Tarif E-commerce"],
        })
      },
    )

    it(
      "should return the first defaultPriceName found in case of equality",
      () => {
        let prices = [
          mockPriceEdge(~name="Tarif Cave", ()),
          mockPriceEdge(~name="Tarif E-commerce", ~enableByDefault=true, ()),
          mockPriceEdge(~name="Tarif Pro", ~enableByDefault=true, ()),
          mockPriceEdge(~name="Tarif E-commerce", ()),
        ]

        expect(makePriceNamesDataset(~prices))->toStrictEqual({
          defaultPriceName: Some("Tarif E-commerce"),
          priceNames: ["Tarif Cave", "Tarif E-commerce", "Tarif Pro"],
        })
      },
    )

    it(
      "should return the defaultPriceName found with the most occurences",
      () => {
        let prices = [
          mockPriceEdge(~name="Tarif Cave", ()),
          mockPriceEdge(~name="Tarif E-commerce", ~enableByDefault=true, ()),
          mockPriceEdge(~name="Tarif Pro", ~enableByDefault=true, ()),
          mockPriceEdge(~name="Tarif Pro", ~enableByDefault=true, ()),
          mockPriceEdge(~name="Tarif E-commerce", ()),
        ]

        expect(makePriceNamesDataset(~prices))->toStrictEqual({
          defaultPriceName: Some("Tarif Pro"),
          priceNames: ["Tarif Cave", "Tarif E-commerce", "Tarif Pro"],
        })
      },
    )
  })
})
