open Vitest

describe("BillingPlanKind", () => {
  test("toString", () => {
    open BillingAccount
    expect(BillingPlanKind.Standard->BillingPlanKind.toString)->toStrictEqual("Standard")
    expect(BillingPlanKind.Company->BillingPlanKind.toString)->toStrictEqual("Company")
    expect(BillingPlanKind.Boost->BillingPlanKind.toString)->toStrictEqual("Boost")
    expect(BillingPlanKind.Warehouse->BillingPlanKind.toString)->toStrictEqual("Warehouse")
  })
  test("fromString", () => {
    open BillingAccount
    expect("standard"->BillingPlanKind.fromString)->toStrictEqual(Ok(Standard))
    expect("company"->BillingPlanKind.fromString)->toStrictEqual(Ok(Company))
    expect("boost"->BillingPlanKind.fromString)->toStrictEqual(Ok(Boost))
    expect("warehouse"->BillingPlanKind.fromString)->toStrictEqual(Ok(Warehouse))
  })
})

test("InvoiceStatus", () => {
  expect("open"->BillingAccount.InvoiceStatus.fromString)->toStrictEqual(Some(Open))
  expect("paid"->BillingAccount.InvoiceStatus.fromString)->toStrictEqual(Some(Paid))
  expect("paid_via_credit_note"->BillingAccount.InvoiceStatus.fromString)->toStrictEqual(
    Some(PaidViaCreditNote),
  )
  expect("uncollectible"->BillingAccount.InvoiceStatus.fromString)->toStrictEqual(
    Some(Uncollectible),
  )
  expect("void"->BillingAccount.InvoiceStatus.fromString)->toStrictEqual(Some(Void))
})
