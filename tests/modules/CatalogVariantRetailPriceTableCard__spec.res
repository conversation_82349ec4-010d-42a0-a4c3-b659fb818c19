open Vitest
open CatalogVariantRetailPriceTableCard

type testPrice = {
  id: option<string>,
  value: float,
}

let mockVariantPrice = (
  ~id=?,
  ~priceId,
  ~variantId="variantId",
  ~valueExcludingTax,
  ~valueIncludingTax,
  ~taxIncluded=true,
  ~taxRate=20.,
  ~edited=false,
  ~fromQuantity=?,
  (),
) => {
  CatalogVariantRetailPrice.id,
  priceId,
  variantId,
  shopName: "shopName",
  name: "name",
  valueExcludingTax,
  valueIncludingTax,
  taxRate,
  taxIncluded,
  purchasePrice: 42.,
  toQuantity: fromQuantity,
  fromQuantity,
  capacityUnit: None,
  edited,
}

let (variantIdA, variantIdB) = ("variantID-A", "variantID-B")
let (idA, idB, idC) = ("id-A", "id-B", "id-C")
let (priceIdA, priceIdB, priceIdC) = ("priceID-A", "priceID-B", "priceID-C")

describe("makeVariantPricesMutationInput", () => {
  it("should make the variantPricesInput with target price updated among the pricelist", () => {
    let variantPrices = [
      mockVariantPrice(
        ~id=idA,
        ~priceId=priceIdA,
        ~valueExcludingTax=15.83,
        ~valueIncludingTax=19.,
        (),
      ),
      mockVariantPrice(
        ~id=idB,
        ~priceId=priceIdB,
        ~valueExcludingTax=200.,
        ~valueIncludingTax=200.,
        ~taxIncluded=false,
        ~taxRate=0.,
        (),
      ),
    ]

    expect(variantPrices->makeVariantPricesMutationInput)->toStrictEqual([
      {
        id: Some("id-A"),
        priceId: "priceID-A",
        valueExcludingTax: 15.83,
        valueIncludingTax: 19.,
        fromQuantity: None,
        toQuantity: None,
      },
      {
        id: Some("id-B"),
        priceId: "priceID-B",
        valueExcludingTax: 200.,
        valueIncludingTax: 200.,
        fromQuantity: None,
        toQuantity: None,
      },
    ])
  })

  it("should make the input with scaling prices", () => {
    let variantPrices = [
      mockVariantPrice(
        ~id=idA,
        ~priceId=priceIdA,
        ~valueExcludingTax=8.33,
        ~valueIncludingTax=10.,
        ~taxIncluded=true,
        ~taxRate=20.,
        (),
      ),
      mockVariantPrice(
        ~id=idB,
        ~priceId=priceIdA,
        ~valueExcludingTax=6.67,
        ~valueIncludingTax=8.,
        ~taxIncluded=true,
        ~taxRate=20.,
        ~fromQuantity=10.,
        (),
      ),
    ]

    expect(variantPrices->makeVariantPricesMutationInput)->toStrictEqual([
      {
        id: Some("id-A"),
        priceId: "priceID-A",
        valueExcludingTax: 8.33,
        valueIncludingTax: 10.,
        fromQuantity: None,
        toQuantity: None,
      },
      {
        id: Some("id-B"),
        priceId: "priceID-A",
        valueExcludingTax: 6.67,
        valueIncludingTax: 8.,
        fromQuantity: Some(10.),
        toQuantity: Some(10.),
      },
    ])
  })

  it("should only make the input with positive price values or non negative if edited", () => {
    let variantPrices = [
      mockVariantPrice(~priceId=priceIdA, ~valueExcludingTax=-1., ~valueIncludingTax=-1., ()),
    ]

    expect(variantPrices->makeVariantPricesMutationInput)->toStrictEqual([])

    let variantPrices = [
      mockVariantPrice(~priceId=priceIdA, ~valueExcludingTax=-1., ~valueIncludingTax=-1., ()),
    ]

    expect(variantPrices->makeVariantPricesMutationInput)->toStrictEqual([])

    let variantPrices = [
      mockVariantPrice(~priceId=priceIdA, ~valueExcludingTax=-1., ~valueIncludingTax=20., ()),
    ]

    expect(variantPrices->makeVariantPricesMutationInput)->toStrictEqual([])

    let variantPrices = [
      mockVariantPrice(
        ~id=idA,
        ~priceId=priceIdA,
        ~valueExcludingTax=-1.,
        ~valueIncludingTax=-1.,
        (),
      ),
      mockVariantPrice(
        ~id=idB,
        ~priceId=priceIdA,
        ~valueExcludingTax=0.,
        ~valueIncludingTax=0.,
        (),
      ),
      mockVariantPrice(
        ~id=idC,
        ~priceId=priceIdB,
        ~valueExcludingTax=0.,
        ~valueIncludingTax=0.,
        ~edited=true,
        (),
      ),
    ]

    expect(variantPrices->makeVariantPricesMutationInput)->toStrictEqual([
      {
        id: Some("id-C"),
        priceId: "priceID-B",
        valueIncludingTax: 0.,
        valueExcludingTax: 0.,
        fromQuantity: None,
        toQuantity: None,
      },
    ])
  })
})

describe("makeVariantPricesMutationsVariables", () => {
  let mockedUntouchedVariantInput = {
    Mutation.purchasedPrice: None,
    stockKeepingUnit: None,
    name: None,
    year: None,
    capacityValue: None,
    capacityUnit: None,
    capacityPrecision: None,
    alcoholVolume: None,
    ean13: None,
    internalNote: None,
    metadata: None,
    tastingNote: None,
    packaging: None,
    internalCode: None,
    supplierId: None,
    minStockThreshold: None,
    maxStockThreshold: None,
    stockOrderTriggerThreshold: None,
    priceLookUpCode: None,
  }

  it(
    "should make a mutation variable from all prices with a common variantId as long as one is edited",
    () => {
      let variantPrices = [
        mockVariantPrice(
          ~variantId=variantIdA,
          ~id=idA,
          ~priceId=priceIdA,
          ~valueExcludingTax=9.17,
          ~valueIncludingTax=11.,
          ~edited=true,
          (),
        ),
        mockVariantPrice(
          ~variantId=variantIdA,
          ~id=idB,
          ~priceId=priceIdB,
          ~valueExcludingTax=20.,
          ~valueIncludingTax=24.,
          (),
        ),
      ]

      expect(makeVariantPricesMutationsVariables(~variantPrices))->toStrictEqual([
        {
          id: variantIdA,
          input: mockedUntouchedVariantInput,
          variantPricesInput: Some([
            {
              id: Some(idA),
              priceId: priceIdA,
              valueExcludingTax: 9.17,
              valueIncludingTax: 11.,
              fromQuantity: None,
              toQuantity: None,
            },
            {
              id: Some(idB),
              priceId: priceIdB,
              valueExcludingTax: 20.,
              valueIncludingTax: 24.,
              fromQuantity: None,
              toQuantity: None,
            },
          ]),
        },
      ])
    },
  )

  it(
    "should make mutations variables for each different variantId with one of its edited price",
    () => {
      let variantPrices = [
        mockVariantPrice(
          ~variantId=variantIdA,
          ~id=idA,
          ~priceId=priceIdA,
          ~valueExcludingTax=9.17,
          ~valueIncludingTax=11.,
          ~edited=true,
          (),
        ),
        mockVariantPrice(
          ~variantId=variantIdA,
          ~id=idB,
          ~priceId=priceIdB,
          ~valueExcludingTax=20.,
          ~valueIncludingTax=24.,
          (),
        ),
        mockVariantPrice(
          ~variantId=variantIdB,
          ~id=idC,
          ~priceId=priceIdA,
          ~valueExcludingTax=15.,
          ~valueIncludingTax=18.,
          ~edited=true,
          (),
        ),
      ]

      expect(makeVariantPricesMutationsVariables(~variantPrices))->toStrictEqual([
        {
          id: variantIdA,
          input: mockedUntouchedVariantInput,
          variantPricesInput: Some([
            {
              id: Some(idA),
              priceId: priceIdA,
              valueIncludingTax: 11.,
              valueExcludingTax: 9.17,
              fromQuantity: None,
              toQuantity: None,
            },
            {
              id: Some(idB),
              valueIncludingTax: 24.,
              valueExcludingTax: 20.,
              priceId: priceIdB,
              fromQuantity: None,
              toQuantity: None,
            },
          ]),
        },
        {
          id: variantIdB,
          input: mockedUntouchedVariantInput,
          variantPricesInput: Some([
            {
              id: Some(idC),
              priceId: priceIdA,
              valueIncludingTax: 18.,
              valueExcludingTax: 15.,
              fromQuantity: None,
              toQuantity: None,
            },
          ]),
        },
      ])
    },
  )

  it("should make a mutation variable with a new price (without priceId)", () => {
    let variantPrices = [
      mockVariantPrice(
        ~variantId=variantIdA,
        ~priceId=priceIdA,
        ~valueExcludingTax=9.17,
        ~valueIncludingTax=11.,
        ~edited=true,
        (),
      ),
    ]

    expect(makeVariantPricesMutationsVariables(~variantPrices))->toStrictEqual([
      {
        id: variantIdA,
        input: mockedUntouchedVariantInput,
        variantPricesInput: Some([
          {
            id: None,
            priceId: priceIdA,
            valueExcludingTax: 9.17,
            valueIncludingTax: 11.,
            fromQuantity: None,
            toQuantity: None,
          },
        ]),
      },
    ])

    let variantPrices = [
      mockVariantPrice(
        ~variantId=variantIdA,
        ~id=idA,
        ~priceId=priceIdA,
        ~valueExcludingTax=16.67,
        ~valueIncludingTax=20.,
        ~edited=false,
        (),
      ),
      mockVariantPrice(
        ~variantId=variantIdA,
        ~priceId=priceIdB,
        ~valueExcludingTax=9.17,
        ~valueIncludingTax=11.,
        ~edited=true,
        (),
      ),
      mockVariantPrice(
        ~variantId=variantIdA,
        ~id=idB,
        ~priceId=priceIdB,
        ~valueExcludingTax=8.33,
        ~valueIncludingTax=10.,
        ~edited=true,
        (),
      ),
    ]

    expect(makeVariantPricesMutationsVariables(~variantPrices))->toStrictEqual([
      {
        id: variantIdA,
        input: mockedUntouchedVariantInput,
        variantPricesInput: Some([
          {
            id: Some("id-A"),
            priceId: priceIdA,
            valueIncludingTax: 20.,
            valueExcludingTax: 16.67,
            fromQuantity: None,
            toQuantity: None,
          },
          {
            id: None,
            priceId: priceIdB,
            valueIncludingTax: 11.,
            valueExcludingTax: 9.17,
            fromQuantity: None,
            toQuantity: None,
          },
          {
            id: Some("id-B"),
            priceId: priceIdB,
            valueIncludingTax: 10.,
            valueExcludingTax: 8.33,
            fromQuantity: None,
            toQuantity: None,
          },
        ]),
      },
    ])
  })

  it(
    "should make only one mutation variable per variantId whatever the other parameters are",
    () => {
      let variantPrices = [
        mockVariantPrice(
          ~variantId=variantIdA,
          ~priceId=priceIdA,
          ~valueExcludingTax=8.33,
          ~valueIncludingTax=10.,
          ~edited=true,
          (),
        ),
        mockVariantPrice(
          ~variantId=variantIdA,
          ~priceId=priceIdB,
          ~valueExcludingTax=16.67,
          ~valueIncludingTax=20.,
          ~edited=true,
          (),
        ),
      ]

      expect(makeVariantPricesMutationsVariables(~variantPrices))->toStrictEqual([
        {
          id: variantIdA,
          input: mockedUntouchedVariantInput,
          variantPricesInput: Some([
            {
              id: None,
              priceId: priceIdA,
              valueIncludingTax: 10.,
              valueExcludingTax: 8.33,
              fromQuantity: None,
              toQuantity: None,
            },
            {
              id: None,
              priceId: priceIdB,
              valueIncludingTax: 20.,
              valueExcludingTax: 16.67,
              fromQuantity: None,
              toQuantity: None,
            },
          ]),
        },
      ])

      let variantPrices = [
        mockVariantPrice(
          ~id=idA,
          ~variantId=variantIdA,
          ~priceId=priceIdA,
          ~valueExcludingTax=8.33,
          ~valueIncludingTax=10.,
          ~edited=true,
          (),
        ),
        mockVariantPrice(
          ~id=idA,
          ~variantId=variantIdA,
          ~priceId=priceIdA,
          ~valueExcludingTax=16.67,
          ~valueIncludingTax=20.,
          ~edited=true,
          (),
        ),
        mockVariantPrice(
          ~id=idA,
          ~variantId=variantIdB,
          ~priceId=priceIdB,
          ~valueExcludingTax=25.,
          ~valueIncludingTax=30.,
          ~edited=false,
          (),
        ),
      ]

      expect(makeVariantPricesMutationsVariables(~variantPrices))->toStrictEqual([
        {
          id: variantIdA,
          input: mockedUntouchedVariantInput,
          variantPricesInput: Some([
            {
              id: Some(idA),
              priceId: priceIdA,
              valueIncludingTax: 10.,
              valueExcludingTax: 8.33,
              fromQuantity: None,
              toQuantity: None,
            },
            {
              id: Some(idA),
              priceId: priceIdA,
              valueIncludingTax: 20.,
              valueExcludingTax: 16.67,
              fromQuantity: None,
              toQuantity: None,
            },
          ]),
        },
      ])
    },
  )

  it("should not make any mutations variables if no price is signed as edited", () => {
    let variantPrices = [
      mockVariantPrice(
        ~id=idA,
        ~variantId=variantIdA,
        ~priceId=priceIdA,
        ~valueExcludingTax=9.17,
        ~valueIncludingTax=11.,
        ~edited=false,
        (),
      ),
    ]

    expect(makeVariantPricesMutationsVariables(~variantPrices))->toStrictEqual([])

    let variantPrices = [
      mockVariantPrice(
        ~variantId=variantIdA,
        ~priceId=priceIdA,
        ~valueExcludingTax=9.17,
        ~valueIncludingTax=11.,
        ~edited=false,
        (),
      ),
    ]

    expect(makeVariantPricesMutationsVariables(~variantPrices))->toStrictEqual([])
  })

  it("should not make any mutations variables by default when there is no variantPrices", () => {
    let variantPrices = []

    expect(makeVariantPricesMutationsVariables(~variantPrices))->toStrictEqual([])
  })
})
