open Vitest

open CatalogProductEditUrlQueryString

it("should encode and decode in a query string correctly", () => {
  let jsonQueryString = CreateProduct.encode({CreateProduct.productKind: #SIMPLE})

  expect(jsonQueryString)->toUnsafeStrictEqual("productKind=%22SIMPLE%22")
  expect(CreateProduct.decode(jsonQueryString))->toStrictEqual(Ok({productKind: #SIMPLE}))
})

it("should encode then decode with a SyntaxError", () => {
  let jsonQueryString = CreateProduct.encode({CreateProduct.productKind: #INVALID->Obj.magic})

  expect(jsonQueryString)->toUnsafeStrictEqual("productKind=%22INVALID%22")
  expect(CreateProduct.decode(jsonQueryString))->toStrictEqual(Error(#SyntaxError("Invalid kind")))
})
