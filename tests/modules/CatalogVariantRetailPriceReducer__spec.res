open Vitest
open CatalogVariantRetailPriceReducer

let mockVariantPrice = (
  ~priceId,
  ~valueExcludingTax,
  ~valueIncludingTax,
  ~purchasePrice=10.,
  ~taxIncluded=true,
  ~taxRate=20.,
  (),
) => {
  CatalogVariantRetailPrice.id: None,
  priceId,
  variantId: "foo",
  shopName: "foo",
  name: "foo",
  valueExcludingTax,
  valueIncludingTax,
  taxIncluded,
  taxRate,
  purchasePrice,
  toQuantity: None,
  fromQuantity: None,
  capacityUnit: None,
  edited: false,
}

let reducer = CatalogVariantRetailPriceReducer.make

describe("RetailPricesPurchaseValueUpdated", () => {
  let initialState = [
    mockVariantPrice(
      ~priceId="price-A",
      ~purchasePrice=50.,
      ~valueExcludingTax=100.,
      ~valueIncludingTax=120.,
      (),
    ),
    mockVariantPrice(
      ~priceId="price-B",
      ~purchasePrice=50.,
      ~valueExcludingTax=100.,
      ~valueIncludingTax=100.,
      ~taxIncluded=false,
      ~taxRate=0.,
      (),
    ),
  ]

  it("should update the purchase price value of every retail prices", () => {
    let newPurchasePrice = 99.
    let newState =
      initialState->Array.map(retailPrice => {...retailPrice, purchasePrice: newPurchasePrice})
    let state = initialState->reducer(RetailPricesPurchaseValueUpdated(newState))

    state->Array.forEach(
      retailPrice => expect(retailPrice)->toHaveProperty("purchasePrice", newPurchasePrice),
    )

    expect(state)->toStrictEqual([
      {
        id: None,
        priceId: "price-A",
        variantId: "foo",
        shopName: "foo",
        name: "foo",
        valueExcludingTax: 100.,
        valueIncludingTax: 120.,
        taxIncluded: true,
        taxRate: 20.,
        purchasePrice: newPurchasePrice,
        toQuantity: None,
        fromQuantity: None,
        capacityUnit: None,
        edited: false,
      },
      {
        id: None,
        priceId: "price-B",
        variantId: "foo",
        shopName: "foo",
        name: "foo",
        valueExcludingTax: 100.,
        valueIncludingTax: 100.,
        taxIncluded: false,
        taxRate: 0.,
        purchasePrice: newPurchasePrice,
        toQuantity: None,
        fromQuantity: None,
        capacityUnit: None,
        edited: false,
      },
    ])
  })
})

describe("RetailPriceUpdated", () => {
  it("should update the value excluding tax and including tax computed with 20% tax rate", () => {
    let initialState = [
      mockVariantPrice(~priceId="price-A", ~valueExcludingTax=100., ~valueIncludingTax=120., ()),
    ]

    let state =
      initialState->reducer(
        RetailPriceUpdated({id: None, variantId: "foo", priceId: "price-A"}, 125.),
      )

    expect(state)->toStrictEqual([
      {
        id: None,
        priceId: "price-A",
        variantId: "foo",
        shopName: "foo",
        name: "foo",
        valueExcludingTax: 125.,
        valueIncludingTax: 150.,
        taxIncluded: true,
        taxRate: 20.,
        purchasePrice: 10.,
        toQuantity: None,
        fromQuantity: None,
        capacityUnit: None,
        edited: true,
      },
    ])
  })

  it(
    "should update both value excluding tax and including tax at the same value when the tax rate is 0",
    () => {
      let initialState = [
        mockVariantPrice(
          ~priceId="price-A",
          ~valueExcludingTax=100.,
          ~valueIncludingTax=100.,
          ~taxIncluded=false,
          ~taxRate=0.,
          (),
        ),
      ]

      let state =
        initialState->reducer(
          RetailPriceUpdated({id: None, variantId: "foo", priceId: "price-A"}, 125.),
        )

      expect(state)->toStrictEqual([
        {
          id: None,
          priceId: "price-A",
          variantId: "foo",
          shopName: "foo",
          name: "foo",
          valueExcludingTax: 125.,
          valueIncludingTax: 125.,
          taxIncluded: false,
          taxRate: 0.,
          purchasePrice: 10.,
          toQuantity: None,
          fromQuantity: None,
          capacityUnit: None,
          edited: true,
        },
      ])
    },
  )

  let initialState = [
    mockVariantPrice(
      ~priceId="price-A",
      ~valueExcludingTax=100.,
      ~valueIncludingTax=100.,
      ~taxIncluded=false,
      (),
    ),
  ]

  it("should not update the state when id is not found", () => {
    let state =
      initialState->reducer(
        RetailPriceUpdated({id: None, variantId: "foo", priceId: "price-unknown"}, 125.),
      )

    expect(state)->toStrictEqual(initialState)
  })
})
