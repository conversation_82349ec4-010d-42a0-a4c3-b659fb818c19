open Vitest

let {rowsFromEdgesData, makeVariables} = module(CatalogLabelProductsPickerModalTable)

let mockTypename = () => ""

let mockQueryProductTaxData = (
  ~productTaxValue=5.5,
  ~productId="",
  ~productName="",
  ~productKind=#SIMPLE,
  ~productColor=None,
  ~productProducer=None,
  ~productDesignation=None,
  ~productWineType=None,
  ~productWhiteWineType=None,
  ~productFamily=None,
  ~productBeerType=None,
  ~productRegion=None,
  ~productCountry=None,
  (),
) => {
  id: productId,
  name: productName,
  kind: productKind,
  producer: productProducer,
  designation: productDesignation,
  family: productFamily,
  beerType: productBeerType,
  color: productColor,
  wineType: productWineType,
  whiteWineType: productWhiteWineType,
  region: productRegion,
  country: productCountry,
  CatalogLabelProductsPickerModalTable.Query.tax: {
    value: productTaxValue,
    __typename: mockTypename(),
  },
  __typename: mockTypename(),
}

let mockQueryVariantPricesDataEdge = (
  ~valueIncludingTax=0.,
  ~fromQuantity=None,
  ~priceId="",
  (),
): CatalogLabelProductsPickerModalTable.Query.t_variants_edges_node_variantPrices_edges => {
  node: {
    valueIncludingTax,
    fromQuantity,
    price: Some({
      id: priceId,
      __typename: mockTypename(),
    }),
    __typename: mockTypename(),
  },
  __typename: mockTypename(),
}

let mockQueryVariantPricesData = (~edges=[], ()) => {
  CatalogLabelProductsPickerModalTable.Query.edges,
  __typename: mockTypename(),
}

let mockQueryData = (
  ~id="mock-id",
  ~name="",
  ~formattedName="",
  ~supplierCompanyName="",
  ~formattedCategory=None,
  ~stockKeepingUnit=None,
  ~priceLookUpCode=None,
  ~internalCode=None,
  ~alcoholVolume=None,
  ~product=mockQueryProductTaxData(),
  ~variantPrices=mockQueryVariantPricesData(),
  (),
) => {
  CatalogLabelProductsPickerModalTable.Query.node: {
    id,
    name,
    createdAt: Js.Date.makeWithYM(~year=2023., ~month=1., ()),
    formattedName,
    formattedCategory,
    stockKeepingUnit,
    priceLookUpCode,
    internalCode,
    alcoholVolume,
    supplier: Some({
      companyName: supplierCompanyName,
      __typename: mockTypename(),
    }),
    product,
    variantPrices,
    __typename: mockTypename(),
  },
  __typename: mockTypename(),
}

describe("rowsFromEdgesData", () => {
  it("should map with default values", () => {
    let priceId = Uuid.make()
    let result = rowsFromEdgesData([mockQueryData()], ~priceId)
    expect(result)->toEqual([
      {
        id: "mock-id",
        createdAt: Js.Date.makeWithYM(~year=2023., ~month=1., ()),
        formattedName: "",
        productKind: #SIMPLE,
        information: {
          productName: "",
          variantName: "",
          country: "—",
          categoryName: "—",
          supplierName: "",
        },
        hasValidVariantPrice: false,
      },
    ])

    let priceId = Uuid.make()
    let result = rowsFromEdgesData(
      [
        mockQueryData(
          ~product=mockQueryProductTaxData(
            ~productTaxValue=5.5,
            ~productId="mock-product-id",
            ~productName="mock-product-name",
            ~productProducer=Some("mock-product-producer"),
            ~productKind=#SIMPLE,
            ~productColor=Some(#AMBER),
            ~productDesignation=Some("mock-product-designation"),
            ~productWineType=Some(#STILL),
            ~productWhiteWineType=Some(#SWEET),
            ~productFamily=Some("mock-product-family"),
            ~productBeerType=Some("mock-product-beertype"),
            ~productRegion=Some("mock-product-region"),
            ~productCountry=Some("mock-product-country"),
            (),
          ),
          ~stockKeepingUnit=Some("12345"),
          ~priceLookUpCode=Some(1),
          ~internalCode=Some("abcde"),
          ~formattedName="mock-formatted-name",
          ~name="mock-variant-name",
          ~supplierCompanyName="mock-supplier-name",
          ~formattedCategory=Some("mock-formatted-category"),
          ~alcoholVolume=Some(12.),
          ~variantPrices=mockQueryVariantPricesData(
            ~edges=[
              mockQueryVariantPricesDataEdge(
                ~priceId=priceId->Uuid.toString,
                ~valueIncludingTax=2.5,
                (),
              ),
            ],
            (),
          ),
          (),
        ),
      ],
      ~priceId,
    )
    expect(result)->toStrictEqual([
      {
        id: "mock-id",
        createdAt: Js.Date.makeWithYM(~year=2023., ~month=1., ()),
        formattedName: "mock-formatted-name",
        productKind: #SIMPLE,
        information: {
          productName: "mock-product-name",
          variantName: "mock-variant-name",
          producerName: "mock-product-producer",
          color: #AMBER,
          designation: "mock-product-designation",
          wineType: #STILL,
          whiteWineType: #SWEET,
          productFamily: "mock-product-family",
          beerType: "mock-product-beertype",
          region: "mock-product-region",
          country: "mock-product-country",
          categoryName: "mock-formatted-category",
          supplierName: "mock-supplier-name",
          alcoholVolume: "12°",
          sku: "12345",
          plu: "1",
          internalCode: "abcde",
        },
        hasValidVariantPrice: true,
      },
    ])
  })
})

test("makeVariables", () => {
  let result = makeVariables(~search="", ~filters={shopId: None}, ())
  expect(result)->toStrictEqual({
    search: Some(""),
    after: None,
    first: Some(20),
    filterBy: Some({
      shopIds: None,
      supplierId: None,
      categoryId: None,
      producer: None,
      stock: None,
      active: Some({_equals: true}),
      ean13: None,
      stockKeepingUnit: None,
      archived: None,
      createdAt: None,
      updatedAt: None,
    }),
  })

  let result = makeVariables(
    ~after="mock-after",
    ~search="mock-search",
    ~filters={
      shopId: Some("mock-shop-id"),
      supplier: {id: "mock-supplier-id"->Js.Nullable.return, name: "mock-supplier-name"},
      category: {id: "mock-category-id"->Js.Nullable.return, name: "mock-category-name"},
      producer: "mock-producer",
    },
    (),
  )
  expect(result)->toStrictEqual({
    search: Some("mock-search"),
    after: Some("mock-after"),
    first: Some(20),
    filterBy: Some({
      shopIds: Some({_in: ["mock-shop-id"]}),
      supplierId: Some({_equals: Some("mock-supplier-id")}),
      categoryId: Some({_equals: Some("mock-category-id")}),
      producer: Some({_equals: "mock-producer"}),
      stock: None,
      active: Some({_equals: true}),
      ean13: None,
      stockKeepingUnit: None,
      archived: None,
      createdAt: None,
      updatedAt: None,
    }),
  })

  let result = makeVariables(
    ~after="mock-after",
    ~search="mock-search",
    ~filters={
      shopId: Some("mock-shop-id"),
      supplier: {id: Js.Nullable.null, name: "not-classified"},
      category: {id: Js.Nullable.null, name: "not-classified"},
    },
    (),
  )
  expect(result)->toStrictEqual({
    search: Some("mock-search"),
    after: Some("mock-after"),
    first: Some(20),
    filterBy: Some({
      shopIds: Some({_in: ["mock-shop-id"]}),
      supplierId: Some({_equals: %raw(`null`)}),
      categoryId: Some({_equals: %raw(`null`)}),
      producer: None,
      stock: None,
      active: Some({_equals: true}),
      ean13: None,
      stockKeepingUnit: None,
      archived: None,
      createdAt: None,
      updatedAt: None,
    }),
  })
})

describe("Reducer", () => {
  open CatalogLabelProductsPickerModalTable.Reducer

  test("make with Searched(_)", () => {
    let state = {
      selectedProducts: [],
      searchQuery: "dummy-search-query",
      filters: {shopId: None},
    }
    expect(make(state, Searched("any-search-query")))->toStrictEqual({
      selectedProducts: [],
      searchQuery: "any-search-query",
      filters: {shopId: None},
    })
  })

  test("make with FiltersUpdated(_)", () => {
    let state = {
      selectedProducts: [],
      searchQuery: "",
      filters: {shopId: None},
    }
    expect(
      make(
        state,
        FiltersUpdated(
          _prev => {
            shopId: Some("mock-shop-id"),
            supplier: {id: "mock-supplier-id"->Js.Nullable.return, name: "mock-supplier-name"},
            category: {id: "mock-category-id"->Js.Nullable.return, name: "mock-category-name"},
            producer: "mock-producer",
          },
        ),
      ),
    )->toStrictEqual({
      selectedProducts: [],
      searchQuery: "",
      filters: {
        shopId: Some("mock-shop-id"),
        supplier: {id: "mock-supplier-id"->Js.Nullable.return, name: "mock-supplier-name"},
        category: {id: "mock-category-id"->Js.Nullable.return, name: "mock-category-name"},
        producer: "mock-producer",
      },
    })
  })

  test("make with ProductSelectionUpdated(_)", () => {
    let priceId = Uuid.make()
    let allProducts = rowsFromEdgesData([mockQueryData(~id="mock-product-id-0", ())], ~priceId)
    let state = {
      selectedProducts: [],
      searchQuery: "",
      filters: {shopId: None},
    }
    expect(make(state, ProductSelectionUpdated(All, allProducts)))->toStrictEqual({
      selectedProducts: [],
      searchQuery: "",
      filters: {shopId: None},
    })

    let allProducts = rowsFromEdgesData([mockQueryData(~id="mock-product-id-0", ())], ~priceId)
    let state = {
      selectedProducts: rowsFromEdgesData([mockQueryData(~id="mock-product-id-0", ())], ~priceId),
      searchQuery: "",
      filters: {shopId: None},
    }
    expect(
      make(state, ProductSelectionUpdated(Selected(["mock-product-id-999"]), allProducts)),
    )->toStrictEqual({
      selectedProducts: [],
      searchQuery: "",
      filters: {shopId: None},
    })

    let allProducts = rowsFromEdgesData(
      [mockQueryData(~id="mock-product-id-0", ()), mockQueryData(~id="mock-product-id-1", ())],
      ~priceId,
    )
    let state = {
      selectedProducts: rowsFromEdgesData([mockQueryData(~id="mock-product-id-0", ())], ~priceId),
      searchQuery: "",
      filters: {shopId: None},
    }
    expect(
      make(
        state,
        ProductSelectionUpdated(Selected(["mock-product-id-0", "mock-product-id-1"]), allProducts),
      ),
    )->toEqual({
      selectedProducts: [
        {
          id: "mock-product-id-0",
          createdAt: Js.Date.makeWithYM(~year=2023., ~month=1., ()),
          formattedName: "",
          productKind: #SIMPLE,
          information: {
            productName: "",
            variantName: "",
            country: "—",
            categoryName: "—",
            supplierName: "",
          },
          hasValidVariantPrice: false,
        },
        {
          id: "mock-product-id-1",
          createdAt: Js.Date.makeWithYM(~year=2023., ~month=1., ()),
          formattedName: "",
          productKind: #SIMPLE,
          information: {
            productName: "",
            variantName: "",
            country: "—",
            categoryName: "—",
            supplierName: "",
          },
          hasValidVariantPrice: false,
        },
      ],
      searchQuery: "",
      filters: {shopId: None},
    })
  })
})
