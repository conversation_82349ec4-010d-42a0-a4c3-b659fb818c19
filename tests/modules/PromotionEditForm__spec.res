open Vitest

let mockDiscountedProduct = (~discountAmountCurrency=5., ()) => {
  PromotionEditDiscountedProduct.id: "",
  cku: "",
  name: "",
  description: "",
  stockKeepingUnit: None,
  purchasePrice: 0.,
  bulkUnit: None,
  retailPrices: Some([{name: "Tarif", value: 10.}]),
  discount: {
    id: "",
    amount: discountAmountCurrency,
    kind: #PERCENT,
  },
}

let mockRootCampaign = () => {
  PromotionEditForm.RootCampaign.id: "",
  creatorIdentifier: "",
  shopId: "",
  shopName: "",
  priceId: "",
  status: None,
  selected: true,
}

let mockValues = (
  ~name,
  ~priceName=?,
  ~period=?,
  ~discountedProducts=[],
  ~rootCampaigns=[],
  (),
) => {
  PromotionEditForm.Lenses.discountedProducts,
  name,
  priceName,
  period,
  rootCampaigns,
}

let {schema} = module(PromotionEditForm)

it("should return no error", () => {
  let values = mockValues(
    ~name="Campagne",
    ~priceName="Tarif",
    ~period={(Js.Date.make(), Js.Date.make())},
    ~discountedProducts=[mockDiscountedProduct()],
    ~rootCampaigns=[mockRootCampaign()],
    (),
  )

  expect(PromotionEditForm.validate(~schema, ~values))->toStrictEqual(Ok())
})

it("should return an error when name is empty", () => {
  let values = mockValues(
    ~name="",
    ~priceName="Tarif",
    ~period={(Js.Date.make(), Js.Date.make())},
    ~discountedProducts=[mockDiscountedProduct()],
    ~rootCampaigns=[mockRootCampaign()],
    (),
  )

  expect(PromotionEditForm.validate(~schema, ~values))->toStrictEqual(
    Error([(Field(Name), "Please fulfill this field.")]),
  )
})

it("should return an error when priceName is empty", () => {
  let values = mockValues(
    ~name="Campagne",
    ~priceName="",
    ~period={(Js.Date.make(), Js.Date.make())},
    ~discountedProducts=[mockDiscountedProduct()],
    ~rootCampaigns=[mockRootCampaign()],
    (),
  )

  expect(PromotionEditForm.validate(~schema, ~values))->toStrictEqual(
    Error([
      (Field(PriceName), "Please select a price name."),
      (Field(DiscountedProducts), "At least one product has an invalid discount."),
    ]),
  )
})

it("should return an error when period is missing", () => {
  let values = mockValues(
    ~name="Campagne",
    ~priceName="Tarif",
    ~discountedProducts=[mockDiscountedProduct()],
    ~rootCampaigns=[mockRootCampaign()],
    (),
  )

  expect(PromotionEditForm.validate(~schema, ~values))->toStrictEqual(
    Error([(Field(Period), "Please select a date range.")]),
  )
})

it("should return an error when there is no discounted product", () => {
  let values = mockValues(
    ~name="Campagne",
    ~priceName="Tarif",
    ~period={(Js.Date.make(), Js.Date.make())},
    ~discountedProducts=[],
    ~rootCampaigns=[mockRootCampaign()],
    (),
  )

  expect(PromotionEditForm.validate(~schema, ~values))->toStrictEqual(
    Error([(Field(DiscountedProducts), "Please add at least one product.")]),
  )
})

it("should return an error when the discounted product price is null or negative", () => {
  let values = mockValues(
    ~name="Campagne",
    ~priceName="Tarif",
    ~period={(Js.Date.make(), Js.Date.make())},
    ~discountedProducts=[mockDiscountedProduct(~discountAmountCurrency=0., ())],
    ~rootCampaigns=[mockRootCampaign()],
    (),
  )

  expect(PromotionEditForm.validate(~schema, ~values))->toStrictEqual(
    Error([(Field(DiscountedProducts), "At least one product has an invalid discount.")]),
  )
})

it("should return an error when there is no root campaign", () => {
  let values = mockValues(
    ~name="Campagne",
    ~priceName="Tarif",
    ~period={(Js.Date.make(), Js.Date.make())},
    ~discountedProducts=[mockDiscountedProduct()],
    ~rootCampaigns=[],
    (),
  )

  expect(PromotionEditForm.validate(~schema, ~values))->toStrictEqual(
    Error([(Field(RootCampaigns), "Please select at least one shop.")]),
  )
})
