open Vitest

let mockValues = (
  ~productKind=#SIMPLE,
  ~color=?,
  ~beerType="",
  ~region="",
  ~designation="",
  ~family="",
  ~wineType=?,
  ~tax=Some({CatalogProductEditForm.Tax.id: Uuid.make(), value: 20.}),
  (),
) => {
  CatalogProductEditForm.Lenses.kind: productKind,
  color,
  wineType,
  whiteWineType: None,
  name: "MyProduct",
  tax,
  categoryId: None,
  producer: "MyProducer",
  family,
  designation,
  country: "MyCountry",
  region,
  beerType,
}

let {schema} = module(CatalogProductEditForm)

it("should return no error when making a new wine", () => {
  let values = mockValues(
    ~productKind=#WINE,
    ~color=#RED,
    ~wineType=#STILL,
    ~designation="foo",
    ~region="foo",
    (),
  )
  expect(CatalogProductEditForm.validate(~schema, ~values))->toStrictEqual(Ok())
})

it("should return no error when making a new beer", () => {
  let values = mockValues(
    ~productKind=#BEER,
    ~color=#AMBER,
    ~beerType="foo",
    ~designation="foo",
    ~region="foo",
    (),
  )
  expect(CatalogProductEditForm.validate(~schema, ~values))->toStrictEqual(Ok())
})

it("should return no error when making a new spirituous", () => {
  let values = mockValues(~productKind=#SPIRITUOUS, ~family="foo", ())
  expect(CatalogProductEditForm.validate(~schema, ~values))->toStrictEqual(Ok())
})

it("should return no error when making a new simple product", () => {
  let values = mockValues(~productKind=#SIMPLE, ())
  expect(CatalogProductEditForm.validate(~schema, ~values))->toStrictEqual(Ok())
})

it("should return an error when the color is missing for wine or beer", () => {
  let values = mockValues(
    ~productKind=#WINE,
    ~wineType=#STILL,
    ~designation="foo",
    ~region="foo",
    (),
  )
  expect(CatalogProductEditForm.validate(~schema, ~values))->toStrictEqual(
    Error([(Field(Color), "Please pick a color.")]),
  )

  let values = mockValues(~productKind=#BEER, ~beerType="foo", ())
  expect(CatalogProductEditForm.validate(~schema, ~values))->toStrictEqual(
    Error([(Field(Color), "Please pick a color.")]),
  )
})

it("should return an error when the designation, wineType or region is missing for wine", () => {
  let values = mockValues(~productKind=#WINE, ~color=#RED, ~wineType=#STILL, ~region="foo", ())
  expect(CatalogProductEditForm.validate(~schema, ~values))->toStrictEqual(
    Error([(Field(Designation), "Please fulfill this field.")]),
  )

  let values = mockValues(~productKind=#WINE, ~color=#RED, ~designation="foo", ~region="foo", ())
  expect(CatalogProductEditForm.validate(~schema, ~values))->toStrictEqual(
    Error([(Field(WineType), "Please pick a wine type.")]),
  )

  let values = mockValues(~productKind=#WINE, ~color=#RED, ~wineType=#STILL, ~designation="foo", ())
  expect(CatalogProductEditForm.validate(~schema, ~values))->toStrictEqual(
    Error([(Field(Region), "Please fulfill this field.")]),
  )
})

it("should return an error when the beerType is missing for beer", () => {
  let values = mockValues(~productKind=#BEER, ~color=#AMBER, ())
  expect(CatalogProductEditForm.validate(~schema, ~values))->toStrictEqual(
    Error([(Field(BeerType), "Please fulfill this field.")]),
  )
})

it("should return an error when the family is missing for spirituous", () => {
  let values = mockValues(~productKind=#SPIRITUOUS, ())
  expect(CatalogProductEditForm.validate(~schema, ~values))->toStrictEqual(
    Error([(Field(Family), "Please fulfill this field.")]),
  )
})
