open Vitest

open OrderEdit

test("isBeforeReception", () => {
  expect(isBeforeReception(~statuses=[#RECEIVING]))->toBe(false)
  expect(isBeforeReception(~statuses=[#RECEIVED, #TO_PAY]))->toBe(false)
  expect(isBeforeReception(~statuses=[#DRAFT]))->toBe(true)
  expect(isBeforeReception(~statuses=[#ACCEPTED, #NOT_RECEIVED]))->toBe(true)
})

test("isLimitedEdition", () => {
  expect(isLimitedEdition(~statuses=[#DRAFT]))->toBe(false)
  expect(isLimitedEdition(~statuses=[#RECEIVING]))->toBe(false)
  expect(isLimitedEdition(~statuses=[#RECEIVED, #PAID]))->toBe(false)
  expect(isLimitedEdition(~statuses=[#RECEIVED, #TO_PAY]))->toBe(true)
  expect(isLimitedEdition(~statuses=[#TO_PAY]))->toBe(false) // out of specs
})

test("isInitialFullEdition", () => {
  expect(isInitialFullEdition(~statuses=[#DRAFT], ~created=false))->toBe(true)
  expect(isInitialFullEdition(~statuses=[#DRAFT], ~created=true))->toBe(false) // out of specs
  expect(isInitialFullEdition(~statuses=[#DRAFT, #FINALIZED], ~created=false))->toBe(true) // out of specs
  expect(isInitialFullEdition(~statuses=[#FINALIZED], ~created=true))->toBe(false) // out of specs
  expect(isInitialFullEdition(~statuses=[#FINALIZED], ~created=false))->toBe(false) // out of specs
  expect(isInitialFullEdition(~statuses=[#RECEIVING], ~created=true))->toBe(true)
  expect(isInitialFullEdition(~statuses=[#RECEIVING], ~created=false))->toBe(false) // out of specs
  expect(isInitialFullEdition(~statuses=[#RECEIVED], ~created=true))->toBe(false)
})
