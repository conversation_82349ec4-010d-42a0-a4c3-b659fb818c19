open Vitest

open CatalogVariantEditUrlState

let mockedUuid = "24526a25-20f0-4956-96b9-e24e9a93262e"->Uuid.fromString->Option.getExn

let makeState = (
  ~tax=Some({CatalogProductEditForm.Tax.id: mockedUuid, value: 20.}),
  ~kind=#SIMPLE,
  ~categoryId=?,
  ~color=?,
  ~wineType=?,
  ~whiteWineType=?,
  (),
) => {
  CatalogProductEditForm.Lenses.kind,
  color,
  wineType,
  whiteWineType,
  name: "",
  tax,
  categoryId,
  producer: "",
  family: "",
  designation: "",
  country: "",
  region: "",
  beerType: "",
}

it("should encode and decode correctly", () => {
  let json = CreateVariantFromProduct.encode(makeState())

  expect(json)->toUnsafeStrictEqual({
    "beerType": "",
    "country": "",
    "designation": "",
    "family": "",
    "kind": "SIMPLE",
    "name": "",
    "producer": "",
    "region": "",
    "tax": Some({
      "id": mockedUuid,
      "value": 20,
    }),
  })
  expect(CreateVariantFromProduct.decode(Some(json)))->toStrictEqual(
    Some(
      Ok({
        beerType: "",
        categoryId: None,
        country: "",
        designation: "",
        family: "",
        kind: #SIMPLE,
        name: "",
        producer: "",
        region: "",
        color: None,
        wineType: None,
        whiteWineType: None,
        tax: Some({
          id: mockedUuid,
          value: 20.,
        }),
      }),
    ),
  )

  let json = CreateVariantFromProduct.encode(
    makeState(
      ~kind=#WINE,
      ~tax=None,
      ~categoryId=mockedUuid,
      ~color=#WHITE,
      ~wineType=#EFFERVESCENT,
      ~whiteWineType=#SOFT,
      (),
    ),
  )

  expect(CreateVariantFromProduct.decode(Some(json)))->toStrictEqual(
    Some(
      Ok({
        beerType: "",
        categoryId: Some(mockedUuid),
        country: "",
        designation: "",
        family: "",
        kind: #WINE,
        name: "",
        producer: "",
        region: "",
        color: Some(#WHITE),
        wineType: Some(#EFFERVESCENT),
        whiteWineType: Some(#SOFT),
        tax: None,
      }),
    ),
  )

  let json = CreateVariantFromProduct.encode(
    makeState(
      ~tax=Some({id: mockedUuid, value: 150.}),
      ~kind=#SPIRITUOUS,
      ~color=#RED,
      ~whiteWineType=#SWEET,
      (),
    ),
  )

  expect(CreateVariantFromProduct.decode(Some(json)))->toStrictEqual(
    Some(
      Ok({
        beerType: "",
        categoryId: None,
        country: "",
        designation: "",
        family: "",
        kind: #SPIRITUOUS,
        name: "",
        producer: "",
        region: "",
        color: Some(#RED),
        wineType: None,
        whiteWineType: Some(#SWEET),
        tax: Some({
          id: mockedUuid,
          value: 150.,
        }),
      }),
    ),
  )
})

testEach2([
  (
    makeState(
      ~tax=Some({
        id: "not-an-uuid"->Obj.magic,
        value: 20.,
      }),
      (),
    ),
    #SyntaxError("Invalid tax uuid"),
  ),
  (makeState(~kind=#INVALID->Obj.magic, ()), #SyntaxError("Invalid kind")),
  (makeState(~categoryId="not-an-uuid"->Obj.magic, ()), #SyntaxError("Invalid category uuid")),
  (makeState(~color=#INVALID->Obj.magic, ()), #SyntaxError("Invalid color")),
  (makeState(~wineType=#INVALID->Obj.magic, ()), #SyntaxError("Invalid wine type")),
  (makeState(~whiteWineType=#INVALID->Obj.magic, ()), #SyntaxError("Invalid white wine type")),
  (
    makeState(~whiteWineType=#INVALID->Obj.magic, ~kind=#INVALID->Obj.magic, ()),
    #SyntaxError("Invalid kind"),
  ),
])(."should encode then decode with the first SyntaxError found (#%#)", (input, output) => {
  let json = CreateVariantFromProduct.encode(input)

  expect(CreateVariantFromProduct.decode(Some(json)))->toStrictEqual(Some(Error(output)))
  expect(CreateVariantFromProduct.decode(None))->toStrictEqual(None)
})
