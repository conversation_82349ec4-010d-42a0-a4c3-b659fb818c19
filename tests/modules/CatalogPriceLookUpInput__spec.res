open Vitest
open TestingLibraryReact

open Auth__Mock

describe("makeValue", () => {
  let {makeValue} = module(CatalogPriceLookUpInput)

  it("should return the first available PLU", () => {
    let pluRange = (1, 12)

    let alreadyConsummedValues = []
    expect(makeValue(~alreadyConsummedValues, ~pluRange, ()))->toStrictEqual(Ok(1))

    let alreadyConsummedValues = [1, 2, 4, 3, 10, 7]
    expect(makeValue(~alreadyConsummedValues, ~pluRange, ()))->toStrictEqual(Ok(5))

    let alreadyConsummedValues = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]
    expect(makeValue(~alreadyConsummedValues, ~pluRange, ()))->toStrictEqual(Ok(12))
  })

  it("should return an error when all PLU are consummed", () => {
    let pluRange = (1, 1)
    let alreadyConsummedValues = [1]

    expect(makeValue(~alreadyConsummedValues, ~pluRange, ()))->toStrictEqual(Error())

    let pluRange = (1, 0)
    let alreadyConsummedValues = []

    expect(makeValue(~alreadyConsummedValues, ~pluRange, ()))->toStrictEqual(Error())
  })
})

module Query = CatalogPriceLookUpInput.ConsummedPriceLookUpCodesQuery

let {setupNodeServer, use, listen, resetHandlers, close} = module(MSW)
let {ctxData, makeLink} = module(MSW.GraphQL)

let makeHandler = (~responseData) => {
  let gatewayLink = makeLink("http://localhost/graphql")

  gatewayLink->MSWHelpers.apolloQueryExn(module(Query), (_req, res, ctx) =>
    res(
      ctx->ctxData({
        variantsPriceLookUpCodes: responseData,
      }),
    )
  )
}

let server = setupNodeServer([makeHandler(~responseData=[])])

let serverUseHandler = (~variantsPriceLookUpCodes) =>
  server->use([makeHandler(~responseData=variantsPriceLookUpCodes)])

beforeAll(() => server->listen({onUnhandledRequest: #warn}))
afterEach(() => server->resetHandlers)
afterAll(() => server->close)

module TestableCatalogPriceLookUpInput = {
  @react.component
  let make = (
    ~defaultValue=?,
    ~errorMessage=?,
    ~onAlreadyConsummedValuesFetched=fn1(ignore)->fn,
  ) => {
    let (value, setValue) = React.useState(() => defaultValue)
    let onChange = value => setValue(_ => value)
    <CatalogPriceLookUpInput value errorMessage onAlreadyConsummedValuesFetched onChange />
  }
}

it("should render the PLU input with the generate PLU button", () => {
  <Providers>
    <TestableCatalogPriceLookUpInput />
  </Providers>
  ->render
  ->ignore

  let input = screen->getByLabelTextExn("PLU code")

  expect(input)->toBeVisible
  expect(input)->toHaveDisplayValue("")

  let button = screen->getByRoleWithOptionsExn(#button, {name: "Generate code"})

  expect(button)->toBeVisible
  expect(button)->toHaveTextContent("Generate code")
})

itPromise("should fetch and return the already consummed PLU codes", async () => {
  serverUseHandler(~variantsPriceLookUpCodes=[2, 3])

  let onAlreadyConsummedValuesFetched = fn1(ignore)

  <Providers>
    <TestableCatalogPriceLookUpInput
      onAlreadyConsummedValuesFetched={onAlreadyConsummedValuesFetched->fn}
    />
  </Providers>
  ->render
  ->ignore

  let input = screen->getByLabelTextExn("PLU code")

  expect(input)->toHaveAttributeValue("placeholder", "Loading...")

  await waitFor(() => expect(input)->toHaveAttributeValue("placeholder", "1-9997"))
  expect(input)->toHaveDisplayValue("")
  expect(onAlreadyConsummedValuesFetched)->toHaveBeenCalledTimes(1)
  expect(onAlreadyConsummedValuesFetched)->toHaveBeenCalledWith1([2, 3])
})

itPromise(
  "should fill the input with a PLU code when clicking on the generate button",
  async () => {
    let userEvent = TestingLibraryEvent.setup()
    serverUseHandler(~variantsPriceLookUpCodes=[1, 2, 5, 3])

    <Providers>
      <TestableCatalogPriceLookUpInput />
    </Providers>
    ->render
    ->ignore

    let input = screen->getByLabelTextExn("PLU code")
    let button = screen->getByRoleWithOptionsExn(#button, {name: "Generate code"})

    expect(input)->toHaveDisplayValue("")

    await userEvent->TestingLibraryEvent.click(button)

    expect(input)->toHaveDisplayValue("4")
  },
)

it("should render a disabled generate code button when no active shop is found", () => {
  serverUseHandler(~variantsPriceLookUpCodes=[1, 2, 3])
  <Providers auth={Logged(mockAuthState(~activeShop=None, ()))}>
    <TestableCatalogPriceLookUpInput />
  </Providers>
  ->render
  ->ignore

  let input = screen->getByLabelTextExn("PLU code")
  let button = screen->getByRoleWithOptionsExn(#button, {name: "Generate code"})

  expect(input)->toHaveDisplayValue("")
  expect(button)->toHaveAttributeValue("aria-disabled", "true")
})

itPromise(
  "should consider the default value as an available PLU code for editing purpose",
  async () => {
    let userEvent = TestingLibraryEvent.setup()
    serverUseHandler(~variantsPriceLookUpCodes=[1, 2, 3])

    <Providers>
      <TestableCatalogPriceLookUpInput defaultValue=2 />
    </Providers>
    ->render
    ->ignore

    let input = screen->getByLabelTextExn("PLU code")
    let button = screen->getByRoleWithOptionsExn(#button, {name: "Generate code"})

    expect(input)->toHaveDisplayValue("2")

    await userEvent->TestingLibraryEvent.click(button)

    expect(input)->toHaveDisplayValue("2")
  },
)

itPromise("should set the maximum PLU code when typing an exceeding value", async () => {
  let {documentBody} = module(WebAPI)

  let userEvent = TestingLibraryEvent.setup()
  let (_, maxPluCode) = CatalogPriceLookUpInput.pluRange

  <Providers>
    <TestableCatalogPriceLookUpInput />
  </Providers>
  ->render
  ->ignore

  let input = screen->getByLabelTextExn("PLU code")

  await waitFor(() => expect(input)->toHaveAttributeValue("placeholder", "1-9997"))
  expect(input)->toHaveDisplayValue("")

  await userEvent->TestingLibraryEvent.type_(input, "10000")
  expect(input)->toHaveDisplayValue("10000")

  await userEvent->TestingLibraryEvent.click(documentBody)
  expect(input)->toHaveDisplayValue(maxPluCode->Int.toString)
})

it("should render the  error message", () => {
  <Providers>
    <TestableCatalogPriceLookUpInput errorMessage="Some error" />
  </Providers>
  ->render
  ->ignore

  let input = screen->getByLabelTextExn("PLU code")
  let error = screen->getByTextExn("Some error")

  expect(input)->toHaveDisplayValue("")
  expect(error)->toBeVisible
})
