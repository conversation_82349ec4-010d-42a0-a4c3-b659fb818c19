open Vitest

let {makeQuantity, makeProductInput} = module(CartProduct)

describe("makeQuantity", () => {
  it("should make a default quantity taking into account the bulkPrecision", () => {
    let input = makeQuantity()
    expect(input)->toBe(1)

    let input = makeQuantity(~bulkPrecision=0, ())
    expect(input)->toBe(1)

    let input = makeQuantity(~bulkPrecision=3, ())
    expect(input)->toBe(1000)
  })

  it("should not take into account passed stock quantity without the max threshold", () => {
    let input = makeQuantity(~stockRawQuantity=5, ())
    expect(input)->toBe(1)

    let input = makeQuantity(~stockRawQuantity=5, ~bulkPrecision=2, ())
    expect(input)->toBe(100)
  })

  it("should make a quantity to match the stock with the max threshold", () => {
    let input = makeQuantity(~maxStockThreshold=5, ~stockRawQuantity=3, ())
    expect(input)->toBe(2)

    let input = makeQuantity(~maxStockThreshold=5000, ~stockRawQuantity=3000, ~bulkPrecision=3, ())
    expect(input)->toBe(2000)

    let input = makeQuantity(~maxStockThreshold=5, ~stockRawQuantity=-1, ())
    expect(input)->toBe(6)

    let input = makeQuantity(~maxStockThreshold=5, ~stockRawQuantity=999, ())
    expect(input)->toBe(1)
  })

  it("should make a quantity fitting the packaging", () => {
    let input = makeQuantity(~packaging=3, ())
    expect(input)->toBe(3)

    let input = makeQuantity(~maxStockThreshold=6, ~stockRawQuantity=0, ~packaging=2, ())
    expect(input)->toBe(6)

    let input = makeQuantity(~maxStockThreshold=6, ~stockRawQuantity=0, ~packaging=4, ())
    expect(input)->toBe(4)

    let input = makeQuantity(
      ~maxStockThreshold=600,
      ~stockRawQuantity=0,
      ~packaging=400,
      ~bulkPrecision=2,
      (),
    )
    expect(input)->toBe(400)
  })
})

describe("makeProductInput", () => {
  let makeProductInputPartial = makeProductInput(
    ~id="mock-id",
    ~name="mock-name",
    ~description="mock-description",
    ~taxValue=20.,
  )

  it("should make a Unit product input with only required parameters", () => {
    let input = makeProductInputPartial()

    expect(input)->toStrictEqual(
      Unit({
        product: {
          id: None,
          identifier: Some("mock-id"),
          stockKeepingUnit: None,
          name: "mock-name",
          description: "mock-description",
          packaging: None,
          stock: 0,
          quantity: 0,
          expectedQuantity: None,
          capacityUnit: None,
          unitPrice: 0.,
          tax: 20.,
          fees: [],
          discounts: [],
        },
      }),
    )
  })

  it("should make a Unit product input and ignore the capacityUnit if no bulkPrecision", () => {
    let input = makeProductInputPartial(~capacityUnit="L", ())

    expect(input)->toStrictEqual(
      Unit({
        product: {
          id: None,
          identifier: Some("mock-id"),
          stockKeepingUnit: None,
          name: "mock-name",
          description: "mock-description",
          packaging: None,
          stock: 0,
          quantity: 0,
          expectedQuantity: None,
          capacityUnit: None,
          unitPrice: 0.,
          tax: 20.,
          fees: [],
          discounts: [],
        },
      }),
    )
  })

  it("should make a Unit product input with extensive parameters", () => {
    let input = makeProductInputPartial(
      ~sku="mock-sku",
      ~packagingRawQuantity=6,
      ~stockRawQuantity=37,
      ~rawQuantity=12,
      ~unitPrice=25.72,
      (),
    )

    expect(input)->toStrictEqual(
      Unit({
        product: {
          id: None,
          identifier: Some("mock-id"),
          stockKeepingUnit: Some("mock-sku"),
          name: "mock-name",
          description: "mock-description",
          packaging: Some(6),
          stock: 37,
          quantity: 12,
          expectedQuantity: None,
          capacityUnit: None,
          unitPrice: 25.72,
          tax: 20.,
          fees: [],
          discounts: [],
        },
      }),
    )
  })

  it("should make a Bulk product input when a bulk precision is passed", () => {
    let input = makeProductInputPartial(~bulkPrecision=2, ())

    expect(input)->toStrictEqual(
      Bulk({
        product: {
          id: None,
          identifier: Some("mock-id"),
          stockKeepingUnit: None,
          name: "mock-name",
          description: "mock-description",
          packaging: None,
          stock: 0.,
          quantity: 0.,
          expectedQuantity: None,
          capacityUnit: None,
          unitPrice: 0.,
          tax: 20.,
          fees: [],
          discounts: [],
        },
        precision: 2,
      }),
    )
  })

  it("should make a Bulk product input with extensive parameters", () => {
    let input = makeProductInputPartial(
      ~sku="mock-sku",
      ~stockRawQuantity=37000,
      ~packagingRawQuantity=6000,
      ~rawQuantity=12000,
      ~unitPrice=25.72,
      ~bulkPrecision=3,
      (),
    )

    expect(input)->toStrictEqual(
      Bulk({
        product: {
          id: None,
          identifier: Some("mock-id"),
          stockKeepingUnit: Some("mock-sku"),
          name: "mock-name",
          description: "mock-description",
          packaging: Some(6.),
          stock: 37.,
          quantity: 12.,
          expectedQuantity: None,
          capacityUnit: None,
          unitPrice: 25.72,
          tax: 20.,
          fees: [],
          discounts: [],
        },
        precision: 3,
      }),
    )
  })
})
