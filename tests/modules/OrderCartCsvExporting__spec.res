open Vitest

open OrderCartCsvExporting

describe("makeFilename", () => {
  it("should return a string with purchase order", () => {
    expect(makeFilename(~statuses=[#RECEIVED], ~name="name"))->toStrictEqual(
      "receiptorder_name.csv",
    )
  })

  it("should return a string with purchase order", () => {
    expect(makeFilename(~statuses=[#RECEIVING], ~name="name"))->toStrictEqual(
      "receiptorder_name.csv",
    )
  })

  it("should return a string with receipt order", () => {
    expect(makeFilename(~statuses=[#ACCEPTED], ~name="name"))->toStrictEqual(
      "purchaseorder_name.csv",
    )
  })
})
