open Vitest

open CatalogProductEditFormMutations

let mockedUuid = "24526a25-20f0-4956-96b9-e24e9a93262e"->Uuid.fromString->Option.getExn

let mockCatalogProductEditFormState = (
  ~name="mock-name",
  ~kind=#SIMPLE,
  ~color=?,
  ~tax=Some({CatalogProductEditForm.Tax.id: mockedUuid, value: 20.}),
  ~categoryId=?,
  ~producer="",
  ~family="",
  ~designation="",
  ~country="",
  ~region="",
  ~beerType="",
  ~wineType=?,
  ~whiteWineType=?,
  (),
) => {
  CatalogProductEditForm.Lenses.name,
  kind,
  color,
  tax,
  categoryId,
  producer,
  family,
  designation,
  country,
  region,
  beerType,
  wineType,
  whiteWineType,
}

describe("makeCreateProductInput", () => {
  it("should make the input", () => {
    let shop = Some(Auth__Mock.mockShop(~id="mock-shop-id", ()))
    let input = makeCreateProductInput(
      ~state=mockCatalogProductEditFormState(
        ~kind=#SPIRITUOUS,
        ~color=#AMBER,
        ~wineType=#STILL,
        ~whiteWineType=#SWEET,
        ~producer="mock-producer",
        ~family="mock-family",
        ~designation="mock-designation",
        ~country="mock-country",
        ~region="mock-region",
        ~beerType="mock-beer-type",
        ~categoryId=mockedUuid,
        (),
      ),
      ~shop,
    )

    expect(input)->toStrictEqual({
      name: "mock-name",
      kind: #SPIRITUOUS,
      color: Some(#AMBER),
      wineType: Some(#STILL),
      whiteWineType: Some(#SWEET),
      taxId: mockedUuid->Uuid.toString,
      categoryId: Some(mockedUuid->Uuid.toString),
      producer: Some("mock-producer"),
      family: Some("mock-family"),
      designation: Some("mock-designation"),
      country: "mock-country",
      region: Some("mock-region"),
      beerType: Some("mock-beer-type"),
      shopId: "mock-shop-id",
      internalNote: None,
    })
  })

  it("should make the input with undefined values from empty strings", () => {
    let shop = Some(Auth__Mock.mockShop(~id="mock-shop-id", ()))
    let input = makeCreateProductInput(~state=mockCatalogProductEditFormState(), ~shop)

    expect(input)->toStrictEqual({
      name: "mock-name",
      kind: #SIMPLE,
      color: None,
      taxId: mockedUuid->Uuid.toString,
      categoryId: None,
      producer: Some("—"),
      family: None,
      designation: None,
      country: "",
      region: None,
      beerType: None,
      wineType: None,
      whiteWineType: None,
      shopId: "mock-shop-id",
      internalNote: None,
    })

    let input = makeCreateProductInput(
      ~state=mockCatalogProductEditFormState(
        ~producer="mock-producer",
        ~family="mock-family",
        ~designation="mock-designation",
        ~country="mock-country",
        ~region="mock-region",
        ~beerType="mock-beer-type",
        ~categoryId=mockedUuid,
        (),
      ),
      ~shop,
    )

    expect(input)->toStrictEqual({
      name: "mock-name",
      kind: #SIMPLE,
      color: None,
      taxId: mockedUuid->Uuid.toString,
      producer: Some("mock-producer"),
      family: Some("mock-family"),
      designation: Some("mock-designation"),
      country: "mock-country",
      region: Some("mock-region"),
      beerType: Some("mock-beer-type"),
      categoryId: Some(mockedUuid->Uuid.toString),
      wineType: None,
      whiteWineType: None,
      shopId: "mock-shop-id",
      internalNote: None,
    })
  })

  it("should throw an error when the passed name is empty", () => {
    let shop = Some(Auth__Mock.mockShop())
    let makeInput = () =>
      makeCreateProductInput(~state=mockCatalogProductEditFormState(~name="", ()), ~shop)

    expect(makeInput)->toRaise(EmptyProductName)
  })

  it("should throw an error when no tax is passed", () => {
    let shop = Some(Auth__Mock.mockShop())
    let makeInput = () =>
      makeCreateProductInput(~state=mockCatalogProductEditFormState(~tax=None, ()), ~shop)

    expect(makeInput)->toRaise(TaxIdNotFound)
  })

  it("should throw an error when no shop is passed", () => {
    let shop = None
    let makeInput = () => makeCreateProductInput(~state=mockCatalogProductEditFormState(), ~shop)

    expect(makeInput)->toRaise(ShopIdNotFound)
  })
})

describe("makeUpdateProductInput", () => {
  it("should make the input", () => {
    let input = makeUpdateProductInput(
      ~state=mockCatalogProductEditFormState(
        ~kind=#SPIRITUOUS,
        ~color=#AMBER,
        ~wineType=#STILL,
        ~whiteWineType=#SWEET,
        ~producer="mock-producer",
        ~family="mock-family",
        ~designation="mock-designation",
        ~country="mock-country",
        ~region="mock-region",
        ~beerType="mock-beer-type",
        ~categoryId=mockedUuid,
        (),
      ),
    )

    expect(input)->toStrictEqual({
      name: Some("mock-name"),
      kind: Some(#SPIRITUOUS),
      color: Some(#AMBER),
      wineType: Some(#STILL),
      whiteWineType: Some(#SWEET),
      taxId: Some(mockedUuid->Uuid.toString),
      producer: Some("mock-producer"),
      family: Some("mock-family"),
      designation: Some("mock-designation"),
      country: "mock-country",
      region: Some("mock-region"),
      beerType: Some("mock-beer-type"),
      categoryId: None, // categoryId remains unchanged
      internalNote: None,
      metadata: None,
    })
  })

  it("should make the input with undefined values from empty strings", () => {
    let input = makeUpdateProductInput(~state=mockCatalogProductEditFormState())

    expect(input)->toStrictEqual({
      name: Some("mock-name"),
      kind: Some(#SIMPLE),
      color: None,
      wineType: None,
      whiteWineType: None,
      taxId: Some(mockedUuid->Uuid.toString),
      producer: None,
      family: None,
      designation: None,
      country: "",
      region: None,
      beerType: None,
      categoryId: None,
      internalNote: None,
      metadata: None,
    })

    let input = makeUpdateProductInput(
      ~state=mockCatalogProductEditFormState(
        ~producer="mock-producer",
        ~family="mock-family",
        ~designation="mock-designation",
        ~country="mock-country",
        ~region="mock-region",
        ~beerType="mock-beer-type",
        ~categoryId=mockedUuid,
        (),
      ),
    )

    expect(input)->toStrictEqual({
      name: Some("mock-name"),
      kind: Some(#SIMPLE),
      color: None,
      wineType: None,
      whiteWineType: None,
      taxId: Some(mockedUuid->Uuid.toString),
      producer: Some("mock-producer"),
      family: Some("mock-family"),
      designation: Some("mock-designation"),
      country: "mock-country",
      region: Some("mock-region"),
      beerType: Some("mock-beer-type"),
      categoryId: None, // categoryId remains unchanged
      internalNote: None,
      metadata: None,
    })
  })

  it("should throw an error when the passed name is empty", () => {
    let makeInput = () =>
      makeUpdateProductInput(~state=mockCatalogProductEditFormState(~name="", ()))

    expect(makeInput)->toRaise(EmptyProductName)
  })

  it("should throw an error when no tax is passed", () => {
    let makeInput = () =>
      makeUpdateProductInput(~state=mockCatalogProductEditFormState(~tax=None, ()))

    expect(makeInput)->toRaise(TaxIdNotFound)
  })
})
