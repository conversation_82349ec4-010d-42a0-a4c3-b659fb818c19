open Vitest
open TestingLibraryReact

it("should show basic formatted value", () => {
  <StockActivityQuantityTableCell
    value=51922 capacityUnit="kg" capacityPrecision=3 kind=#DELIVERY
  />
  ->render
  ->ignore
  expect(screen->getByTextExn("+51.922 kg"))->toBeVisible
})

it("should show advanced formatted value", () => {
  <StockActivityQuantityTableCell value=5112922 kind=#LOSS />->render->ignore
  expect(screen->getByTextExn("-5,112,922"))->toBeVisible
})
