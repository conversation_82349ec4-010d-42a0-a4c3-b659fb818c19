open Vitest

let {toRawValue, format} = module(StockActivityQuantity)

describe("toRawValue", () => {
  it("should return raw value if capacityPrecision is None", () => {
    expect(1.0->toRawValue)->toBe(1)
    expect(1.9->toRawValue)->toBe(1)
  })
  it("should return raw value if capacityPrecision is defined", () => {
    expect(1.0->toRawValue(~capacityPrecision=1))->toBe(10)
    expect(1.55->toRawValue(~capacityPrecision=2))->toBe(155)
    expect(0.555->toRawValue(~capacityPrecision=3))->toBe(555)
  })
})

describe("format", () => {
  it("should format", () => {
    expect(4850->format(~capacityPrecision=3, ~kind=#REFUND))->toStrictEqual("+4.85")
    expect(4850->format(~capacityPrecision=1, ~kind=#DELIVERY))->toStrictEqual("+485")
    expect(4850->format(~capacityPrecision=1, ~kind=#DELIVERY, ~capacityUnit="kg"))->toStrictEqual(
      "+485 kg",
    )
    expect(4850->format(~capacityPrecision=1, ~kind=#DELIVERY, ~capacityUnit="KG"))->toStrictEqual(
      "+485 kg",
    )
    expect(4850->format(~capacityPrecision=1, ~kind=#DELIVERY, ~capacityUnit="l"))->toStrictEqual(
      "+485 L",
    )
    expect(4850->format(~capacityPrecision=1, ~kind=#DELIVERY, ~capacityUnit="L"))->toStrictEqual(
      "+485 L",
    )
    expect(4850->format(~capacityPrecision=1, ~kind=#DELIVERY, ~capacityUnit="g"))->toStrictEqual(
      "+485 g",
    )
    expect(4850->format(~capacityPrecision=1, ~kind=#DELIVERY, ~capacityUnit="G"))->toStrictEqual(
      "+485 g",
    )
    expect(237->format(~capacityPrecision=3, ~kind=#INCOMING_TRANSFER))->toStrictEqual("+0.237")
    expect(237->format(~capacityPrecision=2, ~kind=#CREDIT_MEMO))->toStrictEqual("+2.37")
    expect(52->format(~capacityPrecision=3, ~kind=#RECEPTION))->toStrictEqual("+0.052")
    expect(52->format(~capacityPrecision=1, ~kind=#LOSS))->toStrictEqual("-5.2")
    expect(6->format(~capacityPrecision=3, ~kind=#SALE))->toStrictEqual("-0.006")
    expect(6->format(~capacityPrecision=3, ~kind=#SALE, ~capacityUnit="cl"))->toStrictEqual(
      "-0.006 cl",
    )
    expect(67->format(~capacityPrecision=2, ~kind=#DELIVERY_RECEIPT))->toStrictEqual("-0.67")
    expect(67->format(~capacityPrecision=1, ~kind=#OUTGOING_TRANSFER))->toStrictEqual("-6.7")
    expect(0->format(~capacityPrecision=3, ~kind=#RESET))->toStrictEqual("=0")
    expect(100->format(~kind=#REFUND))->toStrictEqual("+100")
    expect(10->format(~kind=#LOSS))->toStrictEqual("-10")
    expect(1->format(~kind=#RESET))->toStrictEqual("=1")
    expect(1->format(~capacityUnit="L", ~kind=#RESET))->toStrictEqual("=1 L")
  })
})
