open Vitest

test("decode", () => {
  let {decode} = module(DeviceName)

  expect(decode(~slug="d", ~name=""))->toBe(DashboardWinoFr)
  expect(decode(~slug="W", ~name=""))->toBe(AppWinoFr)
  expect(decode(~slug="ERP", ~name=""))->toBe(ERP)
  expect(decode(~slug="BOT", ~name=""))->toBe(Bot)
  expect(decode(~slug="winopay", ~name=""))->toBe(WinoPay)
  expect(decode(~slug="A01", ~name="<PERSON><PERSON><PERSON>"))->toStrictEqual(
    CashRegister({
      slug: "A01",
      name: "<PERSON><PERSON><PERSON>",
    }),
  )
  expect(decode(~slug="", ~name=""))->toStrictEqual(
    CashRegister({
      slug: "",
      name: "",
    }),
  )
})

test("format", () => {
  let {format} = module(DeviceName)

  expect(format(DashboardWinoFr))->toBe("dashboard.wino.fr")
  expect(format(AppWinoFr))->toBe("app.wino.fr")
  expect(format(ERP))->toBe("ERP")
  expect(format(Bot))->toBe("BOT")
  expect(format(WinoPay))->toBe("WinoPay")
  expect(
    format(
      CashRegister({
        slug: "A01",
        name: "Jean-Yves",
      }),
    ),
  )->toBe("A01 - Jean-Yves")
  expect(
    format(
      CashRegister({
        slug: "",
        name: "",
      }),
    ),
  )->toBe(" - ")
})
