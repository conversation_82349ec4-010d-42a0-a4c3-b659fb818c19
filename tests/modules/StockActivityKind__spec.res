open Vitest

open StockActivityKind

describe("toString", () => {
  it("should return a string", () => {
    expect(#LOSS->toString)->toBe("LOSS")
    expect(#SALE->toString)->toBe("SALE")
    expect(#REFUND->toString)->toBe("REFUND")
    expect(#DELIVERY->toString)->toBe("DELIVERY")
    expect(#DELIVERY_RECEIPT->toString)->toBe("DELIVERY_RECEIPT")
    expect(#RESET->toString)->toBe("RESET")
    expect(#RECEPTION->toString)->toBe("RECEPTION")
    expect(#CREDIT_MEMO->toString)->toBe("CREDIT_MEMO")
    expect(#INCOMING_TRANSFER->toString)->toBe("INCOMING_TRANSFER")
    expect(#OUTGOING_TRANSFER->toString)->toBe("OUTGOING_TRANSFER")
  })
})

describe("toLabel", () => {
  it("should return a label", () => {
    expect(#LOSS->toLabel)->toBe("Loss")
    expect(#SALE->toLabel)->toBe("Sale")
    expect(#REFUND->toLabel)->toBe("Refund")
    expect(#DELIVERY->toLabel)->toBe("Delivery")
    expect(#DELIVERY_RECEIPT->toLabel)->toBe("Delivery receipt")
    expect(#RESET->toLabel)->toBe("Reseting")
    expect(#RECEPTION->toLabel)->toBe("Reception")
    expect(#CREDIT_MEMO->toLabel)->toBe("Credit note")
    expect(#INCOMING_TRANSFER->toLabel)->toBe("Incoming transfer")
    expect(#OUTGOING_TRANSFER->toLabel)->toBe("Outgoing transfer")
  })
})

describe("toColor", () => {
  it("should return a color according to the kind", () => {
    expect(#REFUND->toColor)->toBe("#1dbb7a")
    expect(#DELIVERY->toColor)->toBe("#1dbb7a")
    expect(#INCOMING_TRANSFER->toColor)->toBe("#1dbb7a")
    expect(#CREDIT_MEMO->toColor)->toBe("#1dbb7a")
    expect(#RECEPTION->toColor)->toBe("#1dbb7a")
    expect(#LOSS->toColor)->toBe("#e61e5a")
    expect(#SALE->toColor)->toBe("#e61e5a")
    expect(#DELIVERY_RECEIPT->toColor)->toBe("#e61e5a")
    expect(#OUTGOING_TRANSFER->toColor)->toBe("#e61e5a")
    expect(#RESET->toColor)->toBe("#25243a")
  })
})
