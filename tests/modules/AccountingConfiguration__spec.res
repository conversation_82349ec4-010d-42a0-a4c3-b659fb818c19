open Vitest

describe("PaymentAccount", () => {
  test("decodeFromJson", () => {
    let {decodeFromJson} = module(AccountingConfiguration.PaymentAccount)
    expect(
      decodeFromJson(
        Json.fromObjExn({
          "paymentMethod": "CASH",
          "accountNumber": "123",
          "label": "my-label",
          "isaComptaJournalCode": Js.null,
        }),
      ),
    )->toStrictEqual(
      Some({
        paymentMethod: Cash,
        accountNumber: "123",
        accountLabel: "my-label",
        isaComptaJournalCode: None,
      }),
    )
    expect(
      decodeFromJson(
        Json.fromObjExn({
          "paymentMethod": "DEBIT_CARD",
          "accountNumber": "0",
          "label": "",
          "isaComptaJournalCode": "abc123",
        }),
      ),
    )->toStrictEqual(
      Some({
        paymentMethod: DebitCard,
        accountNumber: "0",
        accountLabel: "",
        isaComptaJournalCode: Some("abc123"),
      }),
    )
  })

  test("encodeToJson", () => {
    let {encodeToJson} = module(AccountingConfiguration.PaymentAccount)
    expect(
      encodeToJson({
        paymentMethod: Cash,
        accountNumber: "123",
        accountLabel: "my-label",
        isaComptaJournalCode: None,
      }),
    )->toStrictEqual(
      Json.fromObjExn({
        "paymentMethod": "CASH",
        "accountNumber": "123",
        "label": "my-label",
        "isaComptaJournalCode": Js.null,
      }),
    )
    expect(
      encodeToJson({
        paymentMethod: DebitCard,
        accountNumber: "0",
        accountLabel: "",
        isaComptaJournalCode: Some("abc123"),
      }),
    )->toStrictEqual(
      Json.fromObjExn({
        "paymentMethod": "DEBIT_CARD",
        "accountNumber": "0",
        "label": "",
        "isaComptaJournalCode": "abc123",
      }),
    )
  })
})

todo("autocompleteBasicPaymentsAccounts")

describe("TaxAccount", () => {
  test("fillWithStandard", () => {
    let {fillWithStandard} = module(AccountingConfiguration.TaxAccount)
    expect(fillWithStandard(~taxId="mock-tax-id", ~taxRate=20.))->toStrictEqual({
      taxId: "mock-tax-id",
      isaComptaCode: Some("V5"),
      productsSold: ("********", "Ventes marchandises TVA 20%"),
      deductibleTax: Some(("**********", "TVA collectée 20%")),
    })
    expect(fillWithStandard(~taxId="mock-tax-id", ~taxRate=100.))->toStrictEqual({
      taxId: "mock-tax-id",
      isaComptaCode: None,
      productsSold: ("", ""),
      deductibleTax: Some(("", "")),
    })
  })

  test("decodeFromJson", () => {
    let {decodeFromJson} = module(AccountingConfiguration.TaxAccount)
    expect(
      decodeFromJson(
        Json.fromObjExn({
          "deductibleTax": {
            "accountNumber": "mock-account-number",
            "label": "mock-label",
          },
          "isaComptaCode": "mock-isacompta-code",
          "productsSold": {
            "accountNumber": "mock-account-number",
            "label": "mock-label",
          },
          "taxId": "mock-tax-id",
        }),
      ),
    )->toStrictEqual(
      Some({
        taxId: "mock-tax-id",
        isaComptaCode: Some("mock-isacompta-code"),
        deductibleTax: Some(("mock-account-number", "mock-label")),
        productsSold: ("mock-account-number", "mock-label"),
      }),
    )
    expect(
      decodeFromJson(
        Json.fromObjExn({
          "deductibleTax": Js.null,
          "isaComptaCode": Js.null,
          "productsSold": {
            "accountNumber": "mock-account-number",
            "label": "mock-label",
          },
          "taxId": "mock-tax-id",
        }),
      ),
    )->toStrictEqual(
      Some({
        taxId: "mock-tax-id",
        isaComptaCode: None,
        deductibleTax: None,
        productsSold: ("mock-account-number", "mock-label"),
      }),
    )
  })

  test("encodeToJson", () => {
    let {encodeToJson} = module(AccountingConfiguration.TaxAccount)
    expect(
      encodeToJson({
        taxId: "mock-tax-id",
        isaComptaCode: Some("mock-isacompta-code"),
        deductibleTax: Some(("mock-account-number", "mock-label")),
        productsSold: ("mock-account-number", "mock-label"),
      }),
    )->toStrictEqual(
      Json.fromObjExn({
        "deductibleTax": {
          "accountNumber": "mock-account-number",
          "label": "mock-label",
        },
        "isaComptaCode": "mock-isacompta-code",
        "productsSold": {
          "accountNumber": "mock-account-number",
          "label": "mock-label",
        },
        "taxId": "mock-tax-id",
      }),
    )
    expect(
      encodeToJson({
        taxId: "mock-tax-id",
        isaComptaCode: None,
        deductibleTax: None,
        productsSold: ("mock-account-number", "mock-label"),
      }),
    )->toStrictEqual(
      Json.fromObjExn({
        "deductibleTax": Js.null,
        "isaComptaCode": Js.null,
        "productsSold": {
          "accountNumber": "mock-account-number",
          "label": "mock-label",
        },
        "taxId": "mock-tax-id",
      }),
    )
  })
})

test("endpoint", () => {
  let {endpoint} = module(AccountingConfiguration)
  expect(endpoint(~shopId="shop-id"))->toMatch("/accounting-export-configurations/shop-id")
})

test("decodeInvalidRequestFailure", () => {
  let {decodeInvalidRequestFailure} = module(AccountingConfiguration)

  expect(decodeInvalidRequestFailure({kind: "abc", message: "", data: None}))->toStrictEqual(
    UnknownServerFailure,
  )
  expect(
    decodeInvalidRequestFailure({
      kind: "NotFoundAccountingExportConfiguration",
      message: "",
      data: None,
    }),
  )->toStrictEqual(NotFoundAccountingExportConfigurationFailure)
})

test("decodeRequestError", () => {
  let {decodeRequestError} = module(AccountingConfiguration)
  expect(decodeRequestError(MalformedResponse))->toStrictEqual(Future.value(Error()))
  expect(decodeRequestError(JwtAuthenticationRedirection))->toStrictEqual(Future.value(Error()))
  expect(decodeRequestError(UnexpectedServerError))->toStrictEqual(Future.value(Error()))
  expect(
    decodeRequestError(
      InvalidRequestFailures([
        {kind: "abc", message: "", data: None},
        {kind: "NotFoundAccountingExportConfiguration", message: "", data: None},
      ]),
    ),
  )->toStrictEqual(Future.value(Error()))
  expect(
    decodeRequestError(InvalidRequestFailures([{kind: "abc", message: "", data: None}])),
  )->toStrictEqual(Future.value(Error()))
  expect(
    decodeRequestError(
      InvalidRequestFailures([
        {kind: "NotFoundAccountingExportConfiguration", message: "", data: None},
      ]),
    ),
  )->toStrictEqual(Future.value(Ok(None)))
  expect(
    decodeRequestError(
      InvalidRequestFailures([
        {kind: "NotFoundAccountingExportConfiguration", message: "", data: None},
        {kind: "abc", message: "", data: None},
      ]),
    ),
  )->toStrictEqual(Future.value(Ok(None)))
})

test("decodeResponse", () => {
  let {decodeResponse} = module(AccountingConfiguration)
  expect(
    decodeResponse(
      Json.fromObjExn({
        "shopId": "mock-shop-id",
        "fiscalYearOpeningMonth": 0,
        "breakdownOfConsumerSalesByCashRegisterDailyReport": false,
        "includeCashFromIndividualsInPaymentJournal": false,
        "accountingExportTaxAccounts": [
          {
            "taxId": "mock-tax-id",
            "productsSold": {
              "accountNumber": "mock-account-number",
              "label": "mock-label",
            },
          },
        ],
      }),
    ),
  )->toStrictEqual(
    Some({
      shopId: "mock-shop-id",
      isaComptaAccountNumber: None,
      fiscalYearOpeningMonth: January,
      breakdownOfConsumerSalesByCashRegisterDailyReport: false,
      includeCashFromIndividualsInPaymentJournal: false,
      taxesAccounts: [
        {
          taxId: "mock-tax-id",
          isaComptaCode: None,
          deductibleTax: None,
          productsSold: ("mock-account-number", "mock-label"),
        },
      ],
      maybePaymentsAccounts: None,
    }),
  )
  expect(
    decodeResponse(
      Json.fromObjExn({
        "shopId": "mock-shop-id",
        "fiscalYearOpeningMonth": 0,
        "breakdownOfConsumerSalesByCashRegisterDailyReport": false,
        "includeCashFromIndividualsInPaymentJournal": false,
        "accountingExportTaxAccounts": [
          {
            "taxId": "mock-tax-id",
            "productsSold": {
              "accountNumber": "mock-account-number",
              "label": "mock-label",
            },
          },
        ],
        "accountingExportPaymentAccounts": [
          {
            "paymentMethod": "CASH",
            "accountNumber": "123",
            "label": "my-label",
          },
          {
            "paymentMethod": "DEBIT_CARD",
            "accountNumber": "0",
            "label": "",
          },
        ],
      }),
    ),
  )->toStrictEqual(
    Some({
      shopId: "mock-shop-id",
      isaComptaAccountNumber: None,
      fiscalYearOpeningMonth: January,
      breakdownOfConsumerSalesByCashRegisterDailyReport: false,
      includeCashFromIndividualsInPaymentJournal: false,
      taxesAccounts: [
        {
          taxId: "mock-tax-id",
          isaComptaCode: None,
          deductibleTax: None,
          productsSold: ("mock-account-number", "mock-label"),
        },
      ],
      maybePaymentsAccounts: Some([
        {
          paymentMethod: Cash,
          accountNumber: "123",
          accountLabel: "my-label",
          isaComptaJournalCode: None,
        },
        {
          paymentMethod: DebitCard,
          accountNumber: "0",
          accountLabel: "",
          isaComptaJournalCode: None,
        },
      ]),
    }),
  )
  expect(
    decodeResponse(
      Json.fromObjExn({
        "shopId": "mock-shop-id",
        "isaComptaAccountNumber": "mock-isacompta-account-number",
        "fiscalYearOpeningMonth": 10,
        "breakdownOfConsumerSalesByCashRegisterDailyReport": true,
        "includeCashFromIndividualsInPaymentJournal": false,
        "accountingExportTaxAccounts": [
          {
            "taxId": "mock-a-tax-id",
            "isaComptaCode": Some("mock-isacompta-code"),
            "productsSold": {
              "accountNumber": "mock-a-account-number",
              "label": "mock-a-label",
            },
            "deductibleTax": Some({
              "accountNumber": "mock-a-account-number",
              "label": "mock-a-label",
            }),
          },
          {
            "taxId": "mock-b-tax-id",
            "isaComptaCode": None,
            "productsSold": {
              "accountNumber": "mock-b-account-number",
              "label": "mock-b-label",
            },
            "deductibleTax": None,
          },
        ],
      }),
    ),
  )->toStrictEqual(
    Some({
      shopId: "mock-shop-id",
      isaComptaAccountNumber: Some("mock-isacompta-account-number"),
      fiscalYearOpeningMonth: November,
      breakdownOfConsumerSalesByCashRegisterDailyReport: true,
      includeCashFromIndividualsInPaymentJournal: false,
      taxesAccounts: [
        {
          taxId: "mock-a-tax-id",
          isaComptaCode: Some("mock-isacompta-code"),
          deductibleTax: Some(("mock-a-account-number", "mock-a-label")),
          productsSold: ("mock-a-account-number", "mock-a-label"),
        },
        {
          taxId: "mock-b-tax-id",
          isaComptaCode: None,
          deductibleTax: None,
          productsSold: ("mock-b-account-number", "mock-b-label"),
        },
      ],
      maybePaymentsAccounts: None,
    }),
  )
  expect(
    decodeResponse(
      Json.fromObjExn({
        "shopId": "mock-shop-id",
        "isaComptaAccountNumber": "mock-isacompta-account-number",
        "fiscalYearOpeningMonth": 10,
        "breakdownOfConsumerSalesByCashRegisterDailyReport": true,
        "includeCashFromIndividualsInPaymentJournal": false,
        "accountingExportTaxAccounts": [
          {
            "taxId": "mock-a-tax-id",
            "isaComptaCode": Some("mock-isacompta-code"),
            "productsSold": {
              "accountNumber": "mock-a-account-number",
              "label": "mock-a-label",
            },
            "deductibleTax": Some({
              "accountNumber": "mock-a-account-number",
              "label": "mock-a-label",
            }),
          },
          {
            "taxId": "mock-b-tax-id",
            "isaComptaCode": None,
            "productsSold": {
              "accountNumber": "mock-b-account-number",
              "label": "mock-b-label",
            },
            "deductibleTax": None,
          },
        ],
        "accountingExportPaymentAccounts": [
          {
            "paymentMethod": "BANK_TRANSFER",
            "accountNumber": "",
            "label": "99",
            "isaComptaJournalCode": "abc-123",
          },
        ],
      }),
    ),
  )->toStrictEqual(
    Some({
      shopId: "mock-shop-id",
      isaComptaAccountNumber: Some("mock-isacompta-account-number"),
      fiscalYearOpeningMonth: November,
      breakdownOfConsumerSalesByCashRegisterDailyReport: true,
      includeCashFromIndividualsInPaymentJournal: false,
      taxesAccounts: [
        {
          taxId: "mock-a-tax-id",
          isaComptaCode: Some("mock-isacompta-code"),
          deductibleTax: Some(("mock-a-account-number", "mock-a-label")),
          productsSold: ("mock-a-account-number", "mock-a-label"),
        },
        {
          taxId: "mock-b-tax-id",
          isaComptaCode: None,
          deductibleTax: None,
          productsSold: ("mock-b-account-number", "mock-b-label"),
        },
      ],
      maybePaymentsAccounts: Some([
        {
          paymentMethod: BankTransfer,
          accountNumber: "",
          accountLabel: "99",
          isaComptaJournalCode: Some("abc-123"),
        },
      ]),
    }),
  )
})

todo("request")
