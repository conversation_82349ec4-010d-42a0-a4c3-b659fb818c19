open Vitest

open PromotionEditDiscountedProductReducer

let reducer = PromotionEditDiscountedProductReducer.make

mockPackage("uuid", () =>
  {
    "v4": () => "",
    "validate": () => true,
    "version": () => 4,
  }
)

let mockDiscount = (~amount, ~kind) => {
  PromotionEditDiscountedProduct.Discount.id: "",
  amount,
  kind,
}
let mockProduct = (~cku, ~discount=?, ()) => {
  PromotionEditDiscountedProduct.id: "",
  cku,
  name: "",
  description: "",
  stockKeepingUnit: None,
  purchasePrice: 0.,
  bulkUnit: None,
  retailPrices: None,
  discount: discount->Option.getWithDefault({
    PromotionEditDiscountedProduct.Discount.id: "",
    amount: 0.,
    kind: #CURRENCY,
  }),
}

let (productCkuA, productCkuB, productCkuC, productCkuD, productCkuNotFound) = (
  "product-A",
  "product-B",
  "product-C",
  "product-D",
  "productNotFound",
)

describe("ProductsAdded", () => {
  let initialState = [mockProduct(~cku=productCkuA, ()), mockProduct(~cku=productCkuB, ())]

  it("should add a set of new products at the head of the array", () => {
    let state =
      initialState->reducer(
        ProductsAdded([
          mockProduct(~cku=productCkuC, ~discount=mockDiscount(~amount=10., ~kind=#PERCENT), ()),
          mockProduct(~cku=productCkuD, ~discount=mockDiscount(~amount=15., ~kind=#CURRENCY), ()),
        ]),
      )

    expect(state)->toStrictEqual([
      {
        id: "",
        cku: "product-D",
        name: "",
        description: "",
        stockKeepingUnit: None,
        purchasePrice: 0.,
        bulkUnit: None,
        retailPrices: None,
        discount: {
          id: "",
          amount: 15.,
          kind: #CURRENCY,
        },
      },
      {
        id: "",
        cku: "product-C",
        name: "",
        description: "",
        stockKeepingUnit: None,
        purchasePrice: 0.,
        bulkUnit: None,
        retailPrices: None,
        discount: {
          id: "",
          amount: 10.,
          kind: #PERCENT,
        },
      },
      {
        id: "",
        cku: "product-A",
        name: "",
        description: "",
        stockKeepingUnit: None,
        purchasePrice: 0.,
        bulkUnit: None,
        retailPrices: None,
        discount: {
          id: "",
          amount: 0.,
          kind: #CURRENCY,
        },
      },
      {
        id: "",
        cku: "product-B",
        name: "",
        description: "",
        stockKeepingUnit: None,
        purchasePrice: 0.,
        bulkUnit: None,
        retailPrices: None,
        discount: {
          id: "",
          amount: 0.,
          kind: #CURRENCY,
        },
      },
    ])
  })

  it(
    "should update the existing added product and put it at the head without modifying the discount",
    () => {
      let state =
        initialState->reducer(
          ProductsAdded([
            mockProduct(~cku=productCkuB, ~discount=mockDiscount(~amount=999., ~kind=#PERCENT), ()),
          ]),
        )

      expect(state)->toStrictEqual([
        {
          id: "",
          cku: "product-B",
          name: "",
          description: "",
          stockKeepingUnit: None,
          purchasePrice: 0.,
          bulkUnit: None,
          retailPrices: None,
          discount: {
            id: "",
            amount: 0.,
            kind: #CURRENCY,
          },
        },
        {
          id: "",
          cku: "product-A",
          name: "",
          description: "",
          stockKeepingUnit: None,
          purchasePrice: 0.,
          bulkUnit: None,
          retailPrices: None,
          discount: {
            id: "",
            amount: 0.,
            kind: #CURRENCY,
          },
        },
      ])
    },
  )
})

describe("ProductRemoved", () => {
  let initialState = [mockProduct(~cku=productCkuA, ()), mockProduct(~cku=productCkuB, ())]

  it("should add a new product at the head of the array", () => {
    let state = initialState->reducer(ProductRemoved(productCkuA))

    expect(state)->toStrictEqual([
      {
        id: "",
        cku: "product-B",
        name: "",
        description: "",
        stockKeepingUnit: None,
        purchasePrice: 0.,
        bulkUnit: None,
        retailPrices: None,
        discount: {
          id: "",
          amount: 0.,
          kind: #CURRENCY,
        },
      },
    ])
  })

  it("should throw an exception if the ID is not found", () => {
    let execState = () => initialState->reducer(ProductRemoved(productCkuNotFound))

    expect(execState)->toThrow
  })
})

describe("ProductDiscountUpdated", () => {
  let initialState = [mockProduct(~cku=productCkuA, ()), mockProduct(~cku=productCkuB, ())]

  it("should modify the discount of a given product id", () => {
    let state = initialState->reducer(ProductDiscountUpdated(productCkuA, 10., #PERCENT))

    expect(state)->toStrictEqual([
      {
        id: "",
        cku: "product-A",
        name: "",
        description: "",
        stockKeepingUnit: None,
        purchasePrice: 0.,
        bulkUnit: None,
        retailPrices: None,
        discount: {
          id: "",
          amount: 10.,
          kind: #PERCENT,
        },
      },
      {
        id: "",
        cku: "product-B",
        name: "",
        description: "",
        stockKeepingUnit: None,
        purchasePrice: 0.,
        bulkUnit: None,
        retailPrices: None,
        discount: {
          id: "",
          amount: 0.,
          kind: #CURRENCY,
        },
      },
    ])
  })

  it("should throw an exception if the ID is not found", () => {
    let execState = () =>
      initialState->reducer(ProductDiscountUpdated(productCkuNotFound, 10., #PERCENT))

    expect(execState)->toThrow
  })
})

describe("ProductDiscountReplicated", () => {
  let initialState = [
    mockProduct(~cku=productCkuA, ~discount=mockDiscount(~amount=10., ~kind=#PERCENT), ()),
    mockProduct(~cku=productCkuB, ()),
  ]

  it("should extend the discount of the given product id to all products", () => {
    let state = initialState->reducer(ProductDiscountReplicated(productCkuA))

    expect(state)->toStrictEqual([
      {
        id: "",
        cku: "product-A",
        name: "",
        description: "",
        stockKeepingUnit: None,
        purchasePrice: 0.,
        bulkUnit: None,
        retailPrices: None,
        discount: {
          id: "",
          amount: 10.,
          kind: #PERCENT,
        },
      },
      {
        id: "",
        cku: "product-B",
        name: "",
        description: "",
        stockKeepingUnit: None,
        purchasePrice: 0.,
        bulkUnit: None,
        retailPrices: None,
        discount: {
          id: "",
          amount: 10.,
          kind: #PERCENT,
        },
      },
    ])
  })

  it("should throw an exception if the ID is not found", () => {
    let execState = () =>
      initialState->reducer(ProductDiscountUpdated(productCkuNotFound, 10., #PERCENT))

    expect(execState)->toThrow
  })
})
