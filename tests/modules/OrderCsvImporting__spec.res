open Vitest

open OrderCsvImporting__Config

describe("Config CSV parsing", () => {
  module Config = OrderCsvImporting__Config.ParserConfig
  module OrderCsvParserAndDecoder = Sheet.CsvParserAndDecoder(Config)

  itFuture("should parse and output required references and quantities", () => {
    let csvFile = Obj.magic(`
N1VEARSOLUR201575C6;1
C1VLVLCHARB201875C6;12
`)

    OrderCsvParserAndDecoder.make(csvFile)->Future.tap(
      value =>
        expect(value)->toStrictEqual(
          Ok((
            [
              {
                quantity: 1.,
                sku: "N1VEARSOLUR201575C6",
                unitPrice: None,
              },
              {
                quantity: 12.,
                sku: "C1VLVLCHARB201875C6",
                unitPrice: None,
              },
            ],
            [],
          )),
        ),
    )
  })

  itFuture("should parse and output bulk quantities", () => {
    let csvFile = Obj.magic(`
N1VEARSOLUR201575C6;0,75
C1VLVLCHARB201875C6;2.225
`)

    OrderCsvParserAndDecoder.make(csvFile)->Future.tap(
      value =>
        expect(value)->toStrictEqual(
          Ok((
            [
              {
                quantity: 0.75,
                sku: "N1VEARSOLUR201575C6",
                unitPrice: None,
              },
              {
                quantity: 2.225,
                sku: "C1VLVLCHARB201875C6",
                unitPrice: None,
              },
            ],
            [],
          )),
        ),
    )
  })

  itFuture("should parse and output errors for invalid references (not SKU/ID form)", () => {
    let csvFile = Obj.magic(`
N 1;23
;19
N&1;23
N-1;19
0;19
`)

    OrderCsvParserAndDecoder.make(csvFile)->Future.tap(
      value =>
        expect(value)->toStrictEqual(
          Ok((
            [
              {
                quantity: 19.,
                sku: "N-1",
                unitPrice: None,
              },
              {
                quantity: 19.,
                sku: "0",
                unitPrice: None,
              },
            ],
            [
              InvalidCell({
                entryIndex: 1,
                rowIndex: 1,
                message: "invalid reference (SKU)",
                value: "N 1",
              }),
              InvalidCell({
                entryIndex: 1,
                rowIndex: 2,
                message: "invalid reference (SKU)",
                value: "",
              }),
              InvalidCell({
                entryIndex: 1,
                rowIndex: 3,
                message: "invalid reference (SKU)",
                value: "N&1",
              }),
            ],
          )),
        ),
    )
  })

  itFuture("should parse and output errors for null or negative quantities", () => {
    let csvFile = Obj.magic(`
N1VEARSOLUR201575C6;0
C1VLVLCHARB201875C6;-2
`)

    OrderCsvParserAndDecoder.make(csvFile)->Future.tap(
      value =>
        expect(value)->toStrictEqual(
          Ok((
            [],
            [
              InvalidCell({entryIndex: 2, rowIndex: 1, message: "invalid quantity", value: "0"}),
              InvalidCell({entryIndex: 2, rowIndex: 2, message: "invalid quantity", value: "-2"}),
            ],
          )),
        ),
    )
  })

  itFuture("should parse and output optional price unit", () => {
    let csvFile = Obj.magic(`
N1VEARSOLUR201575C6;1;0.00
C1VLVLCHARB201875C6;12;44,255
`)

    OrderCsvParserAndDecoder.make(csvFile)->Future.tap(
      value =>
        expect(value)->toStrictEqual(
          Ok((
            [
              {
                quantity: 1.,
                sku: "N1VEARSOLUR201575C6",
                unitPrice: Some(0.00),
              },
              {
                quantity: 12.,
                sku: "C1VLVLCHARB201875C6",
                unitPrice: Some(44.255),
              },
            ],
            [],
          )),
        ),
    )
  })

  itFuture("should parse and output errors for incorrect price unit format", () => {
    let csvFile = Obj.magic(`
C1VLVLCHARB201875C6;1;3,0239
C1VLVLCHARB201875C6;1;4a4.255
C1VLVLCHARB201875C6;1; 2,19
C1VLVLCHARB201875C6;1;99,,19
`)

    OrderCsvParserAndDecoder.make(csvFile)->Future.tap(
      value =>
        expect(value)->toStrictEqual(
          Ok((
            [],
            [
              InvalidCell({
                entryIndex: 3,
                rowIndex: 1,
                message: "invalid purchasing price",
                value: "3,0239",
              }),
              InvalidCell({
                entryIndex: 3,
                rowIndex: 2,
                message: "invalid purchasing price",
                value: "4a4.255",
              }),
              InvalidCell({
                entryIndex: 3,
                rowIndex: 3,
                message: "invalid purchasing price",
                value: " 2,19",
              }),
              InvalidCell({
                entryIndex: 3,
                rowIndex: 4,
                message: "invalid purchasing price",
                value: "99,,19",
              }),
            ],
          )),
        ),
    )
  })
})

describe("floatFromString", () => {
  it("should convert a string to a float supporting comma decimal separator", () => {
    expect(floatFromString("1"))->toStrictEqual(Ok(1.))
    expect(floatFromString("1."))->toStrictEqual(Ok(1.))
    expect(floatFromString("1,2"))->toStrictEqual(Ok(1.2))
    expect(floatFromString("10.20"))->toStrictEqual(Ok(10.2))
    expect(floatFromString("00,05"))->toStrictEqual(Ok(0.05))
    expect(floatFromString("1a2 "))->toStrictEqual(Ok(1.))
    expect(floatFromString(" 1.2.3"))->toStrictEqual(Ok(1.2))
    expect(floatFromString(",1"))->toStrictEqual(Ok(0.1))
  })

  it("should fail converting a string to a float", () => {
    expect(floatFromString("a1"))->toStrictEqual(Error())
  })
})
