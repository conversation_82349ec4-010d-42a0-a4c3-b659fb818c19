open Vitest

open CatalogVariantEditFormMutations

let mockCatalogVariantEditFormState = (
  ~capacityValue,
  ~capacityUnit,
  ~alcoholVolume,
  ~year,
  ~bulk,
  ~supplierId,
  ~minStockThreshold=0.,
  ~maxStockThreshold=0.,
  ~stockOrderTriggerThreshold=0.,
  ~priceLookUpCode={
    CatalogVariantEditForm.invalidValues: [],
    value: None,
  },
  (),
) => {
  CatalogVariantEditForm.Lenses.name: "foo",
  capacityUnit,
  capacityValue,
  capacityPrecision: bulk ? Some(CatalogVariant.defaultBulkCapacityPrecision) : None,
  ean13: "foo",
  stockKeepingUnit: "foo",
  priceLookUpCode,
  internalCode: "foo",
  alcoholVolume,
  year,
  bulk,
  purchasePrice: 0.,
  variantPrices: Some([]),
  initialStockQuantity: 0.,
  initialStockComment: "foo",
  supplierId,
  minStockThreshold,
  maxStockThreshold,
  stockOrderTriggerThreshold,
}

describe("makeCreateVariantInput", () => {
  it("should make the input ignoring supplierId empty string", () => {
    let input = makeCreateVariantInput(
      ~state=mockCatalogVariantEditFormState(
        ~bulk=true,
        ~capacityValue=None,
        ~capacityUnit=Some("L"),
        ~year=None,
        ~alcoholVolume=None,
        ~priceLookUpCode={
          invalidValues: [],
          value: None,
        },
        ~supplierId="",
        (),
      ),
      ~productId="foo",
      ~shopId="foo",
    )

    expect(input)->toStrictEqual({
      name: "foo",
      capacityUnit: Some("L"),
      capacityValue: None,
      capacityPrecision: Some(3),
      bulk: Some(true),
      purchasedPrice: Some(0.),
      year: None,
      alcoholVolume: None,
      ean13: Some("foo"),
      stockKeepingUnit: Some("foo"),
      priceLookUpCode: None,
      internalCode: Some("foo"),
      packaging: None,
      minStockThreshold: None,
      maxStockThreshold: None,
      stockOrderTriggerThreshold: None,
      supplierId: None,
      productId: "foo",
      shopId: "foo",
    })
  })

  it(
    "should return an input with capacityValue and capacityUnit but without capacityPrecision when bulk is false",
    () => {
      let input = makeCreateVariantInput(
        ~state=mockCatalogVariantEditFormState(
          ~bulk=false,
          ~capacityValue=Some(5.),
          ~capacityUnit=Some("L"),
          ~year=Some(2021.),
          ~alcoholVolume=Some(10.),
          ~supplierId="foo",
          (),
        ),
        ~productId="foo",
        ~shopId="foo",
      )

      expect(input)->toStrictEqual({
        name: "foo",
        bulk: Some(false),
        capacityValue: Some(5.),
        capacityUnit: Some("L"),
        capacityPrecision: None,
        year: Some(2021),
        alcoholVolume: Some(10.),
        supplierId: Some("foo"),
        purchasedPrice: Some(0.),
        ean13: Some("foo"),
        stockKeepingUnit: Some("foo"),
        priceLookUpCode: None,
        internalCode: Some("foo"),
        packaging: None,
        minStockThreshold: None,
        maxStockThreshold: None,
        stockOrderTriggerThreshold: None,
        productId: "foo",
        shopId: "foo",
      })
    },
  )

  it("should not return a capacityValue when there is no capacityUnit and bulk is false", () => {
    let input = makeCreateVariantInput(
      ~state=mockCatalogVariantEditFormState(
        ~bulk=false,
        ~capacityValue=Some(5.),
        ~capacityUnit=None,
        ~year=Some(2021.),
        ~alcoholVolume=Some(10.),
        ~supplierId="foo",
        (),
      ),
      ~productId="foo",
      ~shopId="foo",
    )

    expect(input)->toStrictEqual({
      name: "foo",
      bulk: Some(false),
      capacityValue: None,
      capacityUnit: None,
      capacityPrecision: None,
      year: Some(2021),
      alcoholVolume: Some(10.),
      supplierId: Some("foo"),
      purchasedPrice: Some(0.),
      ean13: Some("foo"),
      stockKeepingUnit: Some("foo"),
      priceLookUpCode: None,
      internalCode: Some("foo"),
      packaging: None,
      minStockThreshold: None,
      maxStockThreshold: None,
      stockOrderTriggerThreshold: None,
      productId: "foo",
      shopId: "foo",
    })
  })

  it("should throw an error when capacityValue is positive and bulk is true", () => {
    let makeInput = () =>
      makeCreateVariantInput(
        ~state=mockCatalogVariantEditFormState(
          ~bulk=true,
          ~capacityValue=Some(5.),
          ~capacityUnit=Some("L"),
          ~year=Some(2021.),
          ~alcoholVolume=Some(10.),
          ~supplierId="foo",
          (),
        ),
        ~productId="foo",
        ~shopId="foo",
      )

    expect(makeInput)->toRaise(CreateVariantInput_CapacityValueError)
  })

  it("should throw an error when the variant name is an empty string", () => {
    let makeInput = () =>
      makeCreateVariantInput(
        ~state={
          name: "",
          bulk: false,
          capacityValue: Some(5.),
          capacityUnit: Some("L"),
          capacityPrecision: None,
          year: None,
          alcoholVolume: None,
          supplierId: "",
          ean13: "foo",
          stockKeepingUnit: "foo",
          priceLookUpCode: {
            invalidValues: [],
            value: None,
          },
          internalCode: "foo",
          purchasePrice: 0.,
          variantPrices: Some([]),
          initialStockQuantity: 0.,
          initialStockComment: "foo",
          minStockThreshold: 0.,
          maxStockThreshold: 0.,
          stockOrderTriggerThreshold: 0.,
        },
        ~productId="foo",
        ~shopId="foo",
      )

    expect(makeInput)->toRaise(CreateVariantInput_NameError)
  })

  it("should return an input with the correct stock threshold values", () => {
    let makeInput = makeCreateVariantInput(
      ~state=mockCatalogVariantEditFormState(
        ~bulk=false,
        ~capacityValue=Some(5.),
        ~capacityUnit=Some("L"),
        ~year=Some(2021.),
        ~alcoholVolume=Some(10.),
        ~supplierId="foo",
        ~minStockThreshold=1.,
        ~maxStockThreshold=3.,
        ~stockOrderTriggerThreshold=2.,
        (),
      ),
      ~productId="foo",
      ~shopId="foo",
    )

    expect(makeInput)->toStrictEqual({
      name: "foo",
      bulk: Some(false),
      capacityValue: Some(5.),
      capacityUnit: Some("L"),
      capacityPrecision: None,
      year: Some(2021),
      alcoholVolume: Some(10.),
      supplierId: Some("foo"),
      purchasedPrice: Some(0.),
      ean13: Some("foo"),
      stockKeepingUnit: Some("foo"),
      priceLookUpCode: None,
      internalCode: Some("foo"),
      packaging: None,
      minStockThreshold: Some(1),
      maxStockThreshold: Some(3),
      stockOrderTriggerThreshold: Some(2),
      productId: "foo",
      shopId: "foo",
    })
  })

  it("should throw an error when minStockThreshold is greater than maxStockThreshold ", () => {
    let makeInput = () =>
      makeCreateVariantInput(
        ~state=mockCatalogVariantEditFormState(
          ~bulk=false,
          ~capacityValue=Some(5.),
          ~capacityUnit=Some("L"),
          ~year=Some(2021.),
          ~alcoholVolume=Some(10.),
          ~supplierId="foo",
          ~minStockThreshold=2.,
          ~maxStockThreshold=1.,
          ~stockOrderTriggerThreshold=0.,
          (),
        ),
        ~productId="foo",
        ~shopId="foo",
      )

    expect(makeInput)->toRaise(CreateVariantInput_StockThresholdsInvalid)
  })

  it(
    "should throw an error when minStockThreshold is greater than stockOrderTriggerThreshold ",
    () => {
      let makeInput = () =>
        makeCreateVariantInput(
          ~state=mockCatalogVariantEditFormState(
            ~bulk=false,
            ~capacityValue=Some(5.),
            ~capacityUnit=Some("L"),
            ~year=Some(2021.),
            ~alcoholVolume=Some(10.),
            ~supplierId="foo",
            ~minStockThreshold=2.,
            ~maxStockThreshold=0.,
            ~stockOrderTriggerThreshold=1.,
            (),
          ),
          ~productId="foo",
          ~shopId="foo",
        )

      expect(makeInput)->toRaise(CreateVariantInput_StockThresholdsInvalid)
    },
  )

  it(
    "should throw an error when stockOrderTriggerThreshold is greater than or equal to maxStockThreshold ",
    () => {
      let makeInput = () =>
        makeCreateVariantInput(
          ~state=mockCatalogVariantEditFormState(
            ~bulk=false,
            ~capacityValue=Some(5.),
            ~capacityUnit=Some("L"),
            ~year=Some(2021.),
            ~alcoholVolume=Some(10.),
            ~supplierId="foo",
            ~minStockThreshold=0.,
            ~maxStockThreshold=1.,
            ~stockOrderTriggerThreshold=2.,
            (),
          ),
          ~productId="foo",
          ~shopId="foo",
        )

      expect(makeInput)->toRaise(CreateVariantInput_StockThresholdsInvalid)
    },
  )
})

describe("makeUpdateVariantInput", () => {
  it("should return an input with `null` for values with `None`", () => {
    let input = makeUpdateVariantInput(
      ~state=mockCatalogVariantEditFormState(
        ~capacityValue=Some(1.),
        ~capacityUnit=Some("cL"),
        ~alcoholVolume=Some(10.),
        ~year=Some(2021.),
        ~bulk=false,
        ~supplierId="foo",
        ~priceLookUpCode={
          invalidValues: [],
          value: Some(5),
        },
        (),
      ),
    )

    expect(input)->toStrictEqual({
      name: Some("foo"),
      capacityValue: Some(1.),
      capacityUnit: Some("cL"),
      capacityPrecision: None,
      purchasedPrice: None,
      year: Some(2021),
      alcoholVolume: Some(10.),
      ean13: Some("foo"),
      stockKeepingUnit: Some("foo"),
      priceLookUpCode: Some(5),
      tastingNote: None,
      internalNote: None,
      internalCode: Some("foo"),
      packaging: None,
      minStockThreshold: None,
      maxStockThreshold: None,
      stockOrderTriggerThreshold: None,
      supplierId: None,
      metadata: None,
    })

    let input = makeUpdateVariantInput(
      ~state=mockCatalogVariantEditFormState(
        ~capacityValue=None,
        ~capacityUnit=Some("cL"),
        ~alcoholVolume=None,
        ~year=None,
        ~bulk=false,
        ~supplierId="foo",
        (),
      ),
    )

    expect(input)->toStrictEqual({
      name: Some("foo"),
      capacityValue: %raw(`null`),
      capacityUnit: Some("cL"),
      capacityPrecision: None,
      purchasedPrice: None,
      year: %raw(`null`),
      alcoholVolume: %raw(`null`),
      ean13: Some("foo"),
      stockKeepingUnit: Some("foo"),
      priceLookUpCode: %raw(`null`),
      tastingNote: None,
      internalNote: None,
      internalCode: Some("foo"),
      packaging: None,
      minStockThreshold: None,
      maxStockThreshold: None,
      stockOrderTriggerThreshold: None,
      supplierId: None,
      metadata: None,
    })
  })

  it("should return a `null` capacityValue when capacityUnit is `None`", () => {
    let input = makeUpdateVariantInput(
      ~state=mockCatalogVariantEditFormState(
        ~capacityValue=Some(1.),
        ~capacityUnit=None,
        ~alcoholVolume=Some(10.),
        ~year=Some(2021.),
        ~bulk=false,
        ~supplierId="foo",
        ~priceLookUpCode={
          invalidValues: [],
          value: Some(5),
        },
        (),
      ),
    )
    expect(input)->toStrictEqual({
      name: Some("foo"),
      capacityValue: %raw(`null`),
      capacityUnit: %raw(`null`),
      capacityPrecision: None,
      purchasedPrice: None,
      year: Some(2021),
      alcoholVolume: Some(10.),
      ean13: Some("foo"),
      stockKeepingUnit: Some("foo"),
      priceLookUpCode: Some(5),
      tastingNote: None,
      internalNote: None,
      internalCode: Some("foo"),
      packaging: None,
      minStockThreshold: None,
      maxStockThreshold: None,
      stockOrderTriggerThreshold: None,
      supplierId: None,
      metadata: None,
    })
  })

  it("should throw an error when the variant name is an empty string", () => {
    let makeInput = () =>
      makeUpdateVariantInput(
        ~state={
          name: "",
          capacityUnit: Some("cL"),
          capacityValue: None,
          capacityPrecision: None,
          ean13: "foo",
          stockKeepingUnit: "foo",
          priceLookUpCode: {
            invalidValues: [],
            value: Some(0),
          },
          internalCode: "foo",
          alcoholVolume: None,
          year: None,
          bulk: false,
          purchasePrice: 0.,
          variantPrices: Some([]),
          initialStockQuantity: 0.,
          initialStockComment: "foo",
          supplierId: "foo",
          minStockThreshold: 0.,
          maxStockThreshold: 0.,
          stockOrderTriggerThreshold: 0.,
        },
      )

    expect(makeInput)->toRaise(UpdateVariantInput_NameError)
  })
})
