open Vitest

mockPackage("uuid", () =>
  {
    "v4": () => "",
    "validate": () => true,
    "version": () => 4,
  }
)

open PromotionEditDiscountedProduct

let mockDiscountedProduct = (~discountAmountCurrency, ~retailPrices=?, ()) => {
  PromotionEditDiscountedProduct.id: "",
  cku: "",
  name: "",
  description: "",
  stockKeepingUnit: None,
  purchasePrice: 0.,
  bulkUnit: None,
  retailPrices,
  discount: {
    id: "",
    amount: discountAmountCurrency,
    kind: #CURRENCY,
  },
}

describe("make", () => {
  let retailPricesData = [("Tarif Cave", 10.), ("Tarif CHR", 20.)]

  it("should make a product without retailPrices when no selectedPriceName is passed", () => {
    let selectedPriceName = None

    expect(
      make(
        ~id="",
        ~cku="",
        ~name="",
        ~description="",
        ~stockKeepingUnit=None,
        ~purchasePrice=0.,
        ~bulkUnit=None,
        ~retailPricesData,
        ~selectedPriceName,
      ),
    )->toStrictEqual({
      id: "",
      cku: "",
      name: "",
      description: "",
      stockKeepingUnit: None,
      purchasePrice: 0.,
      bulkUnit: None,
      retailPrices: None,
      discount: {
        id: "",
        amount: 0.,
        kind: #CURRENCY,
      },
    })
  })

  it("should make a product without retailPrices when no selectedPriceName is matching", () => {
    let selectedPriceName = Some("Tarif Association")

    expect(
      make(
        ~id="",
        ~cku="",
        ~name="",
        ~description="",
        ~stockKeepingUnit=None,
        ~purchasePrice=0.,
        ~bulkUnit=None,
        ~retailPricesData,
        ~selectedPriceName,
      ),
    )->toStrictEqual({
      id: "",
      cku: "",
      name: "",
      description: "",
      stockKeepingUnit: None,
      purchasePrice: 0.,
      bulkUnit: None,
      retailPrices: None,
      discount: {
        id: "",
        amount: 0.,
        kind: #CURRENCY,
      },
    })
  })

  it(
    "should make a product with all its retailPrices when one is matching the selectedPriceName",
    () => {
      let selectedPriceName = Some("Tarif Cave")

      expect(
        make(
          ~id="",
          ~cku="",
          ~name="",
          ~description="",
          ~stockKeepingUnit=None,
          ~purchasePrice=0.,
          ~bulkUnit=None,
          ~retailPricesData,
          ~selectedPriceName,
        ),
      )->toStrictEqual({
        id: "",
        cku: "",
        name: "",
        description: "",
        stockKeepingUnit: None,
        purchasePrice: 0.,
        bulkUnit: None,
        retailPrices: Some([
          {
            name: "Tarif Cave",
            value: 10.,
          },
          {
            name: "Tarif CHR",
            value: 20.,
          },
        ]),
        discount: {
          id: "",
          amount: 0.,
          kind: #CURRENCY,
        },
      })
    },
  )
})

describe("isValid", () => {
  let retailPrices: array<ProductPrice.t> = [
    {name: "Tarif Cave", value: 10.},
    {name: "Tarif CHR", value: 20.},
  ]

  it(
    "should return true when the discounted product price is inferior to the matching price",
    () => {
      let selectedPriceName = Some("Tarif Cave")
      let discountedProduct = mockDiscountedProduct(~discountAmountCurrency=5., ~retailPrices, ())

      expect(discountedProduct->isValid(~selectedPriceName))->toBe(true)
    },
  )

  it("should return false when the discounted product price is negative", () => {
    let selectedPriceName = Some("Tarif CHR")
    let discountedProduct = mockDiscountedProduct(~discountAmountCurrency=99., ~retailPrices, ())

    expect(discountedProduct->isValid(~selectedPriceName))->toBe(false)
  })

  it("should return false when the discounted product has no retail prices", () => {
    let selectedPriceName = Some("Tarif Cave")
    let discountedProduct = mockDiscountedProduct(~discountAmountCurrency=5., ())

    expect(discountedProduct->isValid(~selectedPriceName))->toBe(false)
  })

  it(
    "should return false when the discounted product has no retail prices matching the selectedPriceName",
    () => {
      let selectedPriceName = Some("Tarif Mystérieux")
      let discountedProduct = mockDiscountedProduct(~discountAmountCurrency=5., ~retailPrices, ())

      expect(discountedProduct->isValid(~selectedPriceName))->toBe(false)
    },
  )
})

describe("ProductPrice.makeDiscounted", () => {
  open ProductPrice

  it("should return the discounted value from a discount kind of currency", () => {
    expect(makeDiscounted(~value=10., ~amount=1., ~kind=#CURRENCY))->toBe(9.)
  })

  it("should return the discounted value from a discount kind of percentage", () => {
    expect(makeDiscounted(~value=10., ~amount=10., ~kind=#PERCENT))->toBe(9.)
    expect(makeDiscounted(~value=10., ~amount=0., ~kind=#PERCENT))->toBe(10.)
    expect(makeDiscounted(~value=0., ~amount=10., ~kind=#PERCENT))->toBe(0.)
    expect(makeDiscounted(~value=0., ~amount=0., ~kind=#PERCENT))->toBe(0.)
  })
})
