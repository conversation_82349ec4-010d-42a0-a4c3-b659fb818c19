open Vitest
open TestingLibraryReact

let userEvent = TestingLibraryEvent.setup()

let mockStateSettings = (
  ~productBarcodeDisplayed=false,
  ~missingProductBarcodeGenerated=false,
  ~priceDisplayed=false,
  ~producerDisplayed=false,
  ~alcoholVolumeDisplayed=false,
  ~productCode=CatalogLabel.ProductCode.Hidden,
  ~labelFormat=CatalogLabel.StateSettingsUserPreferences.GlobalLabelFormat.LabelPrint(
    CatalogLabel.Print.LabelFormat.Label31x22,
  ),
  (),
) => {
  CatalogLabel.StateSettingsUserPreferences.priceId: "mock-price-id"->Uuid.unsafeFromString,
  priceName: "mock-price-name",
  productBarcodeDisplayed,
  missingProductBarcodeGenerated,
  priceDisplayed,
  producerDisplayed,
  alcoholVolumeDisplayed,
  productCode,
  labelFormat,
  printOffset: 1,
  borderEnabled: false,
  sort: CatalogLabel.Sheet.LabelSort.Unsorted,
}

let mockedBarcodeCompletionRequestOkResult = switch CatalogLabel.BarcodeCompletionRequest.encodeBodyJson(
  ~productsValues=Picked([
    {
      variantId: "mock-variant-id",
      repetitions: 1,
    },
  ]),
  ~shopId="mock-shop-id",
) {
| Ok(json) => Ok(json)
| Error(_) => Error(Request.MalformedResponse)
}

module TestCatalogLabelQuickPrintModal = {
  @react.component
  let make = (
    ~requestBarcodeCompletion,
    ~requestLabelsPrinting,
    ~opened,
    ~onRequestClose,
    ~onRequestNotification,
    ~stateSettingsStorageUseRead,
  ) => {
    // FIXME - asynchronous issue at portal creation
    let _ = SettingsBillingAccountShowPage.EditPaymentMethodForm.useFormPropState({
      initialValues: {ibanNumber: ""},
      schema: [],
      onSubmitSuccess: _ => (),
      onSubmitFailure: _ => (),
      resetValuesAfterSubmission: false,
    })

    <Providers>
      <CatalogLabelQuickPrintButton.CatalogLabelQuickPrintModal
        fromEditRedirection=false
        variantId="mock-variant-id"
        shopId="mock-shop-id"
        requestBarcodeCompletion
        requestLabelsPrinting
        opened
        onRequestClose
        onRequestNotification
        stateSettingsStorageUseRead
      />
    </Providers>
  }
}

testPromise("CatalogLabelQuickPrintModal", async () => {
  let requestLabelsPrinting = fn2((_bodyJson, _shopId) => Future.value(Ok()))
  let requestBarcodeCompletion = fn1(_bodyJson =>
    Future.value(mockedBarcodeCompletionRequestOkResult)
  )
  let onRequestClose = fn1(ignore)
  let onRequestNotification = fn1(ignore)

  let stateSettingsStorageUseRead = () => None
  let {baseElement, rerender} = render(
    <TestCatalogLabelQuickPrintModal
      opened=false
      requestLabelsPrinting={requestLabelsPrinting->fn}
      requestBarcodeCompletion={requestBarcodeCompletion->fn}
      onRequestClose={onRequestClose->fn}
      onRequestNotification={onRequestNotification->fn}
      stateSettingsStorageUseRead
    />,
  )

  let modalElement = baseElement->querySelectorUnsafe("#portals-modal")

  expect(onRequestClose)->toHaveBeenCalledTimes(0)

  let _ = rerender(
    <TestCatalogLabelQuickPrintModal
      opened=true
      requestLabelsPrinting={requestLabelsPrinting->fn}
      requestBarcodeCompletion={requestBarcodeCompletion->fn}
      onRequestClose={onRequestClose->fn}
      onRequestNotification={onRequestNotification->fn}
      stateSettingsStorageUseRead
    />,
  )

  let modalTitleElement = within(modalElement)->getByTextExn("Print label")
  let commitModalButtonElement =
    within(modalElement)->getByRoleWithOptionsExn(#button, {name: "Set settings"})

  expect(modalTitleElement)->toBeVisible
  expect(commitModalButtonElement)->toBeVisible
  expect(modalElement)->toHaveTextContent("No print settings set")

  expect(requestLabelsPrinting)->toHaveBeenCalledTimes(0)
  expect(requestBarcodeCompletion)->toHaveBeenCalledTimes(0)
  expect(onRequestClose)->toHaveBeenCalledTimes(0)
  expect(onRequestNotification)->toHaveBeenCalledTimes(0)

  let stateSettingsStorageUseRead = () => Some(
    mockStateSettings(~labelFormat=LabelSheet(Grid21), ()),
  )
  let _ = rerender(
    <TestCatalogLabelQuickPrintModal
      opened=true
      requestLabelsPrinting={requestLabelsPrinting->fn}
      requestBarcodeCompletion={requestBarcodeCompletion->fn}
      onRequestClose={onRequestClose->fn}
      onRequestNotification={onRequestNotification->fn}
      stateSettingsStorageUseRead
    />,
  )

  expect(modalTitleElement)->toBeVisible
  expect(commitModalButtonElement)->toBeVisible
  expect(modalElement)->toHaveTextContent("No print settings set")

  expect(requestLabelsPrinting)->toHaveBeenCalledTimes(0)
  expect(onRequestClose)->toHaveBeenCalledTimes(0)
  expect(onRequestNotification)->toHaveBeenCalledTimes(0)

  let stateSettingsStorageUseRead = () => Some(
    mockStateSettings(~labelFormat=LabelPrint(Label31x22), ()),
  )
  let _ = rerender(
    <TestCatalogLabelQuickPrintModal
      opened=true
      requestLabelsPrinting={requestLabelsPrinting->fn}
      requestBarcodeCompletion={requestBarcodeCompletion->fn}
      onRequestClose={onRequestClose->fn}
      onRequestNotification={onRequestNotification->fn}
      stateSettingsStorageUseRead
    />,
  )

  let copiesNumberInputElement = screen->getByLabelTextExn("Copies")
  let commitModalButtonElement =
    within(modalElement)->getByRoleWithOptionsExn(#button, {name: "Print label"})

  expect(modalElement)->Vitest.not->toBeEmptyDOMElement
  expect(modalTitleElement)->toBeVisible
  expect(commitModalButtonElement)->toBeVisible
  expect(copiesNumberInputElement)->toHaveDisplayValue("1")
  expect(modalElement)->toHaveTextContent("Print settings")
  expect(modalElement)->toHaveTextContent("mock-price-name")
  expect(modalElement)->toHaveTextContent("Format: 3,10 x 2,20 cm")

  expect(requestLabelsPrinting)->toHaveBeenCalledTimes(0)
  expect(onRequestClose)->toHaveBeenCalledTimes(0)
  expect(onRequestNotification)->toHaveBeenCalledTimes(0)

  await userEvent->TestingLibraryEvent.type_(copiesNumberInputElement, "{backspace}2")

  expect(copiesNumberInputElement)->toHaveDisplayValue("2")

  await userEvent->TestingLibraryEvent.click(commitModalButtonElement)

  expect(requestLabelsPrinting)->toHaveBeenCalledTimes(1)
  expect(requestLabelsPrinting)->toHaveBeenCalledWith2(
    Json.fromObjExn({
      "priceId": "mock-price-id",
      "labelFormat": "W31_X_H22",
      "items": [
        {
          "variantId": "mock-variant-id",
          "repetitions": 2,
        },
      ],
      "priceEnabled": false,
      "producerEnabled": false,
      "barcodeEnabled": false,
      "alcoholVolumeEnabled": false,
    }),
    "mock-shop-id",
  )
  expect(requestBarcodeCompletion)->toHaveBeenCalledTimes(0)
  expect(onRequestClose)->toHaveBeenCalledTimes(1)
  expect(onRequestNotification)->toHaveBeenCalledTimes(1)
  expect(onRequestNotification)->toHaveBeenCalledWith1(
    Ok(
      "2 prints of the label has been started.[Monitor printing from your StarPrinter Online account](https://portal.starprinter.online/Dashboard)",
    ),
  )

  mockClear(requestLabelsPrinting)
  mockClear(onRequestClose)
  mockClear(onRequestNotification)

  let requestLabelsPrinting = fn2((_bodyJson, _shopId) =>
    Future.value(Error(CatalogLabel.Print.LabelsRequest.Unknown))
  )
  let stateSettingsStorageUseRead = () => Some(
    mockStateSettings(
      ~labelFormat=LabelPrint(Label57x19),
      ~productBarcodeDisplayed=true,
      ~missingProductBarcodeGenerated=false,
      ~priceDisplayed=true,
      ~producerDisplayed=true,
      ~alcoholVolumeDisplayed=true,
      ~productCode=SKU,
      (),
    ),
  )
  let _ = rerender(
    <TestCatalogLabelQuickPrintModal
      opened=true
      requestLabelsPrinting={requestLabelsPrinting->fn}
      requestBarcodeCompletion={requestBarcodeCompletion->fn}
      onRequestClose={onRequestClose->fn}
      onRequestNotification={onRequestNotification->fn}
      stateSettingsStorageUseRead
    />,
  )

  expect(modalElement)->toHaveTextContent("Print settings")
  expect(modalElement)->toHaveTextContent("mock-price-name")
  expect(modalElement)->toHaveTextContent("Format: 5,70 x 1,90 cm")
  expect(modalElement)->toHaveTextContent("Display barcode")
  expect(modalElement)->toHaveTextContent("Display retail price")
  expect(modalElement)->toHaveTextContent("Display producer")
  expect(modalElement)->toHaveTextContent("Display alcohol volume")
  expect(modalElement)->toHaveTextContent("Display SKU")

  await userEvent->TestingLibraryEvent.type_(copiesNumberInputElement, "{backspace}1")

  expect(copiesNumberInputElement)->toHaveDisplayValue("1")

  await userEvent->TestingLibraryEvent.click(commitModalButtonElement)

  expect(requestLabelsPrinting)->toHaveBeenCalledTimes(1)
  expect(requestLabelsPrinting)->toHaveBeenCalledWith2(
    Json.fromObjExn({
      "priceId": "mock-price-id",
      "labelCodeKind": "SKU",
      "items": [
        {
          "variantId": "mock-variant-id",
          "repetitions": 1,
        },
      ],
      "priceEnabled": true,
      "producerEnabled": true,
      "barcodeEnabled": true,
      "alcoholVolumeEnabled": true,
    }),
    "mock-shop-id",
  )
  expect(requestBarcodeCompletion)->toHaveBeenCalledTimes(0)
  expect(onRequestClose)->toHaveBeenCalledTimes(1)
  expect(onRequestNotification)->toHaveBeenCalledTimes(1)
  expect(onRequestNotification)->toHaveBeenCalledWith1(
    Error("An unexpected error occured. Please try again or contact the support."),
  )

  mockClear(requestLabelsPrinting)
  mockClear(onRequestClose)
  mockClear(onRequestNotification)

  let requestLabelsPrinting = fn2((_bodyJson, _shopId) => Future.value(Ok()))
  let stateSettingsStorageUseRead = () => Some(
    mockStateSettings(~productBarcodeDisplayed=true, ~missingProductBarcodeGenerated=true, ()),
  )
  let _ = rerender(
    <TestCatalogLabelQuickPrintModal
      opened=true
      requestLabelsPrinting={requestLabelsPrinting->fn}
      requestBarcodeCompletion={requestBarcodeCompletion->fn}
      onRequestClose={onRequestClose->fn}
      onRequestNotification={onRequestNotification->fn}
      stateSettingsStorageUseRead
    />,
  )

  expect(modalElement)->toHaveTextContent("Print settings")
  expect(modalElement)->toHaveTextContent("mock-price-name")
  expect(modalElement)->toHaveTextContent("Format: 3,10 x 2,20 cm")
  expect(modalElement)->toHaveTextContent("Display barcode (generate automatically if missing)")

  await userEvent->TestingLibraryEvent.click(commitModalButtonElement)

  expect(requestLabelsPrinting)->toHaveBeenCalledTimes(1)
  expect(requestLabelsPrinting)->toHaveBeenCalledWith2(
    Json.fromObjExn({
      "priceId": "mock-price-id",
      "labelFormat": "W31_X_H22",
      "items": [
        {
          "variantId": "mock-variant-id",
          "repetitions": 1,
        },
      ],
      "priceEnabled": false,
      "producerEnabled": false,
      "barcodeEnabled": true,
      "alcoholVolumeEnabled": false,
    }),
    "mock-shop-id",
  )
  expect(requestBarcodeCompletion)->toHaveBeenCalledTimes(1)
  expect(requestBarcodeCompletion)->toHaveBeenCalledWith1(
    Json.fromObjExn({
      "shopId": "mock-shop-id",
      "variantIds": ["mock-variant-id"],
    }),
  )
  expect(onRequestClose)->toHaveBeenCalledTimes(1)
  expect(onRequestNotification)->toHaveBeenCalledTimes(1)
  expect(onRequestNotification)->toHaveBeenCalledWith1(
    Ok(
      "1 print of the label has been started.[Monitor printing from your StarPrinter Online account](https://portal.starprinter.online/Dashboard)",
    ),
  )
})

describe("CatalogLabelQuickPrintButton", () => {
  module TestCatalogLabelQuickPrintButton = {
    @react.component
    let make = (
      ~featureLocked,
      ~fromEditRedirection=false,
      ~variation,
      ~requestLabelsPrinting,
      ~requestBarcodeCompletion,
      ~onRequestNotification,
      ~stateSettingsStorageUseRead=?,
    ) => {
      React.useEffect1(() => {
        HelpCenter.install(~appId=Env.intercomAppID())
        None
      }, [])

      <Providers>
        <CatalogLabelQuickPrintButton
          variantId="mock-variant-id"
          shopId="mock-shop-id"
          featureLocked
          fromEditRedirection
          variation
          requestLabelsPrinting
          requestBarcodeCompletion
          onRequestNotification
          ?stateSettingsStorageUseRead
        />
      </Providers>
    }
  }

  testPromise("When the feature is locked", async () => {
    let requestLabelsPrinting = fn2((_bodyJson, _shopId) => Future.value(Ok()))
    let requestBarcodeCompletion = fn1(
      _bodyJson => Future.value(mockedBarcodeCompletionRequestOkResult),
    )
    let onRequestNotification = fn1(ignore)

    let {rerender} = render(
      <TestCatalogLabelQuickPrintButton
        featureLocked=true
        variation=#standard
        requestLabelsPrinting={requestLabelsPrinting->fn}
        requestBarcodeCompletion={requestBarcodeCompletion->fn}
        onRequestNotification={onRequestNotification->fn}
      />,
    )

    let baseButtonElement = screen->getByRoleWithOptionsExn(#button, {name: "Print label"})
    let popoverDialogElement = screen->queryByRoleWithOptions(#dialog, {name: "popover-dialog"})

    expect(baseButtonElement)->toBeVisible
    expect(popoverDialogElement)->Vitest.not->toBeDefined

    await userEvent->TestingLibraryEvent.click(baseButtonElement)

    let popoverDialogElement = screen->getByRoleWithOptionsExn(#dialog, {name: "popover-dialog"})
    let popoverDialogButtonElement =
      within(popoverDialogElement)->getByRoleWithOptionsExn(#button, {name: "Get the feature"})

    expect(popoverDialogElement)->toBeVisible
    expect(popoverDialogElement)->toHaveTextContent(
      "This new feature lets you print a label directly from a product page with a Star printer.",
    )
    expect(popoverDialogButtonElement)->toBeVisible

    await userEvent->TestingLibraryEvent.click(popoverDialogButtonElement)
    await userEvent->TestingLibraryEvent.click(baseButtonElement)

    expect(popoverDialogElement)->Vitest.not->toBeVisible

    expect(requestLabelsPrinting)->toHaveBeenCalledTimes(0)
    expect(requestBarcodeCompletion)->toHaveBeenCalledTimes(0)
    expect(onRequestNotification)->toHaveBeenCalledTimes(0)

    let _ = rerender(
      <TestCatalogLabelQuickPrintButton
        featureLocked=true
        variation=#rounded
        requestLabelsPrinting={requestLabelsPrinting->fn}
        requestBarcodeCompletion={requestBarcodeCompletion->fn}
        onRequestNotification={onRequestNotification->fn}
      />,
    )

    let baseButtonElement = screen->queryByRole(#button)

    expect(baseButtonElement)->Vitest.not->toBeDefined

    expect(requestLabelsPrinting)->toHaveBeenCalledTimes(0)
    expect(requestBarcodeCompletion)->toHaveBeenCalledTimes(0)
    expect(onRequestNotification)->toHaveBeenCalledTimes(0)
  })

  testPromise("When the feature is unlocked", async () => {
    let requestLabelsPrinting = fn2((_bodyJson, _shopId) => Future.value(Ok()))
    let requestBarcodeCompletion = fn1(
      _bodyJson => Future.value(mockedBarcodeCompletionRequestOkResult),
    )
    let onRequestNotification = fn1(ignore)

    let {baseElement, unmount} = render(
      <TestCatalogLabelQuickPrintButton
        featureLocked=false
        variation=#standard
        requestLabelsPrinting={requestLabelsPrinting->fn}
        requestBarcodeCompletion={requestBarcodeCompletion->fn}
        onRequestNotification={onRequestNotification->fn}
      />,
    )

    let baseButtonElement = screen->getByRoleWithOptionsExn(#button, {name: "Print label"})
    let modalElement = baseElement->querySelectorUnsafe("#portals-modal")

    expect(baseButtonElement)->toBeVisible

    await userEvent->TestingLibraryEvent.click(baseButtonElement)

    let modalTitleElement = within(modalElement)->getByTextExn("Print label")

    let commitModalButtonElement =
      within(modalElement)->getByRoleWithOptionsExn(#button, {name: "Set settings"})

    expect(modalTitleElement)->toBeVisible
    expect(commitModalButtonElement)->toBeVisible
    expect(modalElement)->toHaveTextContent("No print settings set")

    unmount()

    let {baseElement, unmount} = render(
      <TestCatalogLabelQuickPrintButton
        featureLocked=false
        variation=#rounded
        requestLabelsPrinting={requestLabelsPrinting->fn}
        requestBarcodeCompletion={requestBarcodeCompletion->fn}
        onRequestNotification={onRequestNotification->fn}
      />,
    )

    let baseButtonElement = screen->getByRoleWithOptionsExn(#button, {name: "", expanded: false})
    let modalElement = baseElement->querySelectorUnsafe("#portals-modal")

    expect(baseButtonElement)->toBeVisible

    await userEvent->TestingLibraryEvent.click(baseButtonElement)

    let modalTitleElement = within(modalElement)->getByTextExn("Print label")

    let commitModalButtonElement =
      within(modalElement)->getByRoleWithOptionsExn(#button, {name: "Set settings"})

    expect(modalTitleElement)->toBeVisible
    expect(commitModalButtonElement)->toBeVisible
    expect(modalElement)->toHaveTextContent("No print settings set")

    unmount()

    let stateSettingsStorageUseRead = () => Some(mockStateSettings())
    let {baseElement, unmount} = render(
      <TestCatalogLabelQuickPrintButton
        featureLocked=false
        variation=#standard
        requestLabelsPrinting={requestLabelsPrinting->fn}
        requestBarcodeCompletion={requestBarcodeCompletion->fn}
        onRequestNotification={onRequestNotification->fn}
        stateSettingsStorageUseRead
      />,
    )

    let baseButtonElement = screen->getByRoleWithOptionsExn(#button, {name: "Print label"})
    let modalElement = baseElement->querySelectorUnsafe("#portals-modal")

    expect(baseButtonElement)->toBeVisible

    await userEvent->TestingLibraryEvent.click(baseButtonElement)

    let (modalTitleElement, commitModalButtonElement) =
      within(modalElement)->getAllByTextExn2("Print label")

    expect(modalTitleElement)->toBeVisible
    expect(commitModalButtonElement)->toBeVisible
    expect(modalElement)->toHaveTextContent("Print settings")

    expect(requestLabelsPrinting)->toHaveBeenCalledTimes(0)
    expect(requestBarcodeCompletion)->toHaveBeenCalledTimes(0)
    expect(onRequestNotification)->toHaveBeenCalledTimes(0)

    await userEvent->TestingLibraryEvent.click(commitModalButtonElement)

    expect(baseButtonElement)->toBeVisible
    expect(modalTitleElement)->Vitest.not->toBeVisible
    expect(commitModalButtonElement)->Vitest.not->toBeVisible

    expect(requestLabelsPrinting)->toHaveBeenCalledTimes(1)
    expect(requestLabelsPrinting)->toHaveBeenCalledWith2(
      Json.fromObjExn({
        "priceId": "mock-price-id",
        "labelFormat": "W31_X_H22",
        "items": [
          {
            "variantId": "mock-variant-id",
            "repetitions": 1,
          },
        ],
        "priceEnabled": false,
        "producerEnabled": false,
        "barcodeEnabled": false,
        "alcoholVolumeEnabled": false,
      }),
      "mock-shop-id",
    )
    expect(requestBarcodeCompletion)->toHaveBeenCalledTimes(0)
    expect(onRequestNotification)->toHaveBeenCalledTimes(1)
    expect(onRequestNotification)->toHaveBeenCalledWith1(
      Ok(
        "1 print of the label has been started.[Monitor printing from your StarPrinter Online account](https://portal.starprinter.online/Dashboard)",
      ),
    )

    unmount()
    mockClear(requestLabelsPrinting)
    mockClear(onRequestNotification)

    let stateSettingsStorageUseRead = () => Some(mockStateSettings())
    let {baseElement, unmount} = render(
      <TestCatalogLabelQuickPrintButton
        featureLocked=false
        fromEditRedirection=true
        variation=#standard
        requestLabelsPrinting={requestLabelsPrinting->fn}
        requestBarcodeCompletion={requestBarcodeCompletion->fn}
        onRequestNotification={onRequestNotification->fn}
        stateSettingsStorageUseRead
      />,
    )

    let (baseButtonElement, _commitModalButtonElement) =
      screen->getAllByRoleWithOptionsExn2(#button, {name: "Print label"})
    let modalElement = baseElement->querySelectorUnsafe("#portals-modal")
    let (modalTitleElement, commitModalButtonElement) =
      within(modalElement)->getAllByTextExn2("Print label")

    expect(baseButtonElement)->toBeVisible
    expect(modalTitleElement)->toBeVisible
    expect(commitModalButtonElement)->toBeVisible

    expect(modalElement)->toHaveTextContent("Print parameters set correctly!")
    expect(modalElement)->toHaveTextContent("Print settings")

    expect(requestLabelsPrinting)->toHaveBeenCalledTimes(0)
    expect(requestBarcodeCompletion)->toHaveBeenCalledTimes(0)
    expect(onRequestNotification)->toHaveBeenCalledTimes(0)

    await userEvent->TestingLibraryEvent.click(commitModalButtonElement)

    expect(modalTitleElement)->Vitest.not->toBeVisible
    expect(commitModalButtonElement)->Vitest.not->toBeVisible

    expect(requestLabelsPrinting)->toHaveBeenCalledTimes(1)
    expect(requestLabelsPrinting)->toHaveBeenCalledWith2(
      Json.fromObjExn({
        "priceId": "mock-price-id",
        "labelFormat": "W31_X_H22",
        "items": [
          {
            "variantId": "mock-variant-id",
            "repetitions": 1,
          },
        ],
        "priceEnabled": false,
        "producerEnabled": false,
        "barcodeEnabled": false,
        "alcoholVolumeEnabled": false,
      }),
      "mock-shop-id",
    )
    expect(requestBarcodeCompletion)->toHaveBeenCalledTimes(0)
    expect(onRequestNotification)->toHaveBeenCalledTimes(1)
    expect(onRequestNotification)->toHaveBeenCalledWith1(
      Ok(
        "1 print of the label has been started.[Monitor printing from your StarPrinter Online account](https://portal.starprinter.online/Dashboard)",
      ),
    )

    unmount()
    mockClear(requestLabelsPrinting)
    mockClear(onRequestNotification)

    let requestLabelsPrinting = fn2(
      (_bodyJson, _shopId) => Future.value(Error(CatalogLabel.Print.LabelsRequest.PriceUnknown)),
    )
    let stateSettingsStorageUseRead = () => Some(mockStateSettings())
    let {baseElement} = render(
      <TestCatalogLabelQuickPrintButton
        featureLocked=false
        variation=#rounded
        requestLabelsPrinting={requestLabelsPrinting->fn}
        requestBarcodeCompletion={requestBarcodeCompletion->fn}
        onRequestNotification={onRequestNotification->fn}
        stateSettingsStorageUseRead
      />,
    )

    let baseButtonElement = screen->getByRoleWithOptionsExn(#button, {name: "", expanded: false})
    let modalElement = baseElement->querySelectorUnsafe("#portals-modal")

    expect(baseButtonElement)->toBeVisible

    await userEvent->TestingLibraryEvent.click(baseButtonElement)

    let (modalTitleElement, commitModalButtonElement) =
      within(modalElement)->getAllByTextExn2("Print label")

    expect(modalTitleElement)->toBeVisible
    expect(commitModalButtonElement)->toBeVisible
    expect(modalElement)->toHaveTextContent("Print settings")

    expect(requestLabelsPrinting)->toHaveBeenCalledTimes(0)
    expect(requestBarcodeCompletion)->toHaveBeenCalledTimes(0)
    expect(onRequestNotification)->toHaveBeenCalledTimes(0)

    await userEvent->TestingLibraryEvent.click(commitModalButtonElement)

    expect(baseButtonElement)->toBeVisible
    expect(modalTitleElement)->Vitest.not->toBeVisible
    expect(commitModalButtonElement)->Vitest.not->toBeVisible

    expect(requestLabelsPrinting)->toHaveBeenCalledTimes(1)
    expect(requestLabelsPrinting)->toHaveBeenCalledWith2(
      Json.fromObjExn({
        "priceId": "mock-price-id",
        "labelFormat": "W31_X_H22",
        "items": [
          {
            "variantId": "mock-variant-id",
            "repetitions": 1,
          },
        ],
        "priceEnabled": false,
        "producerEnabled": false,
        "barcodeEnabled": false,
        "alcoholVolumeEnabled": false,
      }),
      "mock-shop-id",
    )
    expect(requestBarcodeCompletion)->toHaveBeenCalledTimes(0)
    expect(onRequestNotification)->toHaveBeenCalledTimes(1)
    expect(onRequestNotification)->toHaveBeenCalledWith1(
      Error(
        "The label printing has failed: please enter the retail price for the price list set up for printing (mock-price-name).",
      ),
    )
  })
})
