open Vitest
open TestingLibraryReact

let mockQueryParentCategoryData = (~id, ~formattedName) => {
  CatalogCategorySelectFilter.Query.id,
  formattedName,
  __typename: "",
}

let mockQueryParentCategories = data => {
  CatalogCategorySelectFilter.Query.parentCategories: data,
}

describe("component", () => {
  let userEvent = TestingLibraryEvent.setup()

  itPromise("should be disabled when the data is loading", async () => {
    let onChange = fn1(ignore)
    let useCategoriesQuery = _variables => AsyncData.Loading

    render(
      <CatalogCategorySelectFilter
        shopId=Some("mock-shop-id") value=None useCategoriesQuery onChange={onChange->fn}
      />,
    )->ignore

    let triggerElement = screen->getByRoleExn(#button)

    expect(triggerElement)->toBeVisible
    expect(triggerElement)->toHaveTextContent("Category: All")
    expect(triggerElement)->toBeDisabled
    expect(onChange)->toHaveBeenCalledTimes(0)
  })

  itPromise("should be disabled when there is no shopId", async () => {
    let onChange = fn1(ignore)
    let useCategoriesQuery = _variables => AsyncData.Done(Ok(mockQueryParentCategories([])))

    render(
      <CatalogCategorySelectFilter
        shopId=None value=None useCategoriesQuery onChange={onChange->fn}
      />,
    )->ignore

    let triggerElement = screen->getByRoleExn(#button)

    expect(triggerElement)->toBeVisible
    expect(triggerElement)->toHaveTextContent("Category: All")
    expect(triggerElement)->toBeDisabled
    expect(onChange)->toHaveBeenCalledTimes(0)
  })

  itPromise("should render with the value passed selected", async () => {
    let onChange = fn1(ignore)
    let useCategoriesQuery = _variables => AsyncData.Done(
      Ok(
        mockQueryParentCategories([
          mockQueryParentCategoryData(
            ~id="mock-category-id-1",
            ~formattedName="mock-category-name-1",
          ),
          mockQueryParentCategoryData(
            ~id="mock-category-id-0",
            ~formattedName="mock-category-name-0",
          ),
        ]),
      ),
    )

    render(
      <CatalogCategorySelectFilter
        shopId=Some("mock-shop-id")
        value=Some({
          CatalogCategorySelectFilter.id: "mock-category-id-1"->Js.Nullable.return,
          name: "mock-category-name-1",
        })
        useCategoriesQuery
        onChange={onChange->fn}
      />,
    )->ignore

    let triggerElement = screen->getByRoleExn(#button)

    expect(triggerElement)->toBeVisible
    expect(triggerElement)->toHaveTextContent("Category: mock-category-name-1")
    expect(triggerElement)->Vitest.not->toBeDisabled
    expect(onChange)->toHaveBeenCalledTimes(0)

    await userEvent->TestingLibraryEvent.click(triggerElement)

    let listbox = screen->getByRoleExn(#listbox)
    let (option1, option2, option3, option4) = within(listbox)->getAllByRoleExn4(#option)

    expect(option1)->toHaveTextContent("All")
    expect(option2)->toHaveTextContent("Not classified")
    expect(option3)->toHaveTextContent("mock-category-name-1")
    expect(option4)->toHaveTextContent("mock-category-name-0")

    await userEvent->TestingLibraryEvent.click(option1)

    expect(onChange)->toHaveBeenCalledTimes(1)
    expect(onChange)->toHaveBeenLastCalledWith1(None)
  })

  itPromise("should render with a null category value passed and set it", async () => {
    let onChange = fn1(ignore)
    let useCategoriesQuery = _variables => AsyncData.Done(Ok(mockQueryParentCategories([])))

    render(
      <CatalogCategorySelectFilter
        shopId=Some("mock-shop-id") value=None useCategoriesQuery onChange={onChange->fn}
      />,
    )->ignore

    let triggerElement = screen->getByRoleExn(#button)

    expect(triggerElement)->toBeVisible
    expect(triggerElement)->toHaveTextContent("Category: All")
    expect(triggerElement)->Vitest.not->toBeDisabled
    expect(onChange)->toHaveBeenCalledTimes(0)

    await userEvent->TestingLibraryEvent.click(triggerElement)

    let listbox = screen->getByRoleExn(#listbox)
    let (option1, option2) = within(listbox)->getAllByRoleExn2(#option)

    expect(option1)->toHaveTextContent("All")
    expect(option2)->toHaveTextContent("Not classified")

    await userEvent->TestingLibraryEvent.click(option2)

    expect(onChange)->toHaveBeenCalledTimes(1)
    expect(onChange)->toHaveBeenLastCalledWith1(
      Some({id: Js.Nullable.null, name: "Not classified"}),
    )
  })

  itPromise("should render with the placeholder when the passed value is not found", async () => {
    let onChange = fn1(ignore)
    let useCategoriesQuery = _variables => AsyncData.Done(Ok(mockQueryParentCategories([])))

    render(
      <CatalogCategorySelectFilter
        shopId=Some("mock-shop-id")
        value=Some({
          CatalogCategorySelectFilter.id: "mock-category-id"->Js.Nullable.return,
          name: "mock-category-name",
        })
        useCategoriesQuery
        onChange={onChange->fn}
      />,
    )->ignore

    let triggerElement = screen->getByRoleExn(#button)

    expect(triggerElement)->toBeVisible
    expect(triggerElement)->toHaveTextContent("Category: Select")
    expect(triggerElement)->Vitest.not->toBeDisabled
    expect(onChange)->toHaveBeenCalledTimes(0)

    await userEvent->TestingLibraryEvent.click(triggerElement)

    let listbox = screen->getByRoleExn(#listbox)
    let (option1, option2) = within(listbox)->getAllByRoleExn2(#option)

    expect(option1)->toHaveTextContent("All")
    expect(option2)->toHaveTextContent("Not classified")
  })
})
