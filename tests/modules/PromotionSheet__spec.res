open Vitest

open PromotionSheet

let mockTypename = () => "mock-typename"

let mockPromotionCampaign = (
  ~value=1.,
  ~kind=#PERCENT,
  ~id="mock-variant-id",
  ~stockKeepingUnit=Some("mock-variant-stockKeepingUnit"),
  (),
) => {
  PromotionSheet.value,
  kind,
  variantId: id,
  variantStockKeepingUnit: stockKeepingUnit,
}

let mockRawVariantPromotionCampaign = (
  ~id="mock-raw-variant-products-id",
  ~stockKeepingUnit=Some("mock-raw-variant-products-stockKeepingUnit"),
  (),
) => {
  Query.Raw.id,
  stockKeepingUnit: stockKeepingUnit->Js.Nullable.fromOption,
  __typename: mockTypename(),
}

let mockRawPromotionCampaignEdge = (~value, ~kind, ~variant) => {
  Query.Raw.node: {
    value,
    kind,
    variant,
    __typename: mockTypename(),
  },
  __typename: mockTypename(),
}

let mockRawPromotionCampaignDiscounts = (edges, ~hasNextPage=None, ~endCursor=None, ()) => {
  Query.Raw.pageInfo: {
    __typename: mockTypename(),
    hasNextPage: switch hasNextPage {
    | Some(hasNextPage) => hasNextPage->Js.Nullable.return
    | None => Js.Nullable.null
    },
    endCursor: switch endCursor {
    | Some(endCursor) => endCursor->Js.Nullable.return
    | None => Js.Nullable.null
    },
  },
  edges,
  __typename: mockTypename(),
}

let mockRawPromotionCampaign = (~discounts=mockRawPromotionCampaignDiscounts([], ()), ()) => {
  Query.Raw.startDate: Js.Date.make()->Scalar.Datetime.serialize,
  discounts,
  __typename: mockTypename(),
}

let {setupNodeServer, use, listen, resetHandlers, close} = module(MSW)
let {ctxData, makeLink} = module(MSW.GraphQL)

let server = setupNodeServer([])
let gatewayLink = makeLink("http://localhost/graphql")

beforeAll(() => server->listen({onUnhandledRequest: #warn}))
afterEach(() => server->resetHandlers)
afterAll(() => server->close)

let serverUsePromotionCampaignHandler = promotionCampaign =>
  server->use([
    gatewayLink->MSWHelpers.apolloQueryExn(module(Query), (request, send, ctx) => {
      let response = ctx->ctxData({
        promotionCampaign: switch promotionCampaign {
        | Some({Query.Raw.discounts: discounts}) =>
          let {edges: discountsEdges, pageInfo: discountsInfo} =
            discounts.edges->MSWHelpers.cursorPaginateExn(
              ~first=?request.variables.first->Js.Nullable.toOption,
              ~after=?request.variables.after->Js.Nullable.toOption,
              (),
            )

          mockRawPromotionCampaign(
            ~discounts=mockRawPromotionCampaignDiscounts(
              discountsEdges,
              ~endCursor=discountsInfo.endCursor,
              ~hasNextPage=discountsInfo.hasNextPage,
              (),
            ),
            (),
          )->Js.Nullable.return
        | None => Js.Nullable.null
        },
      })

      send(response)
    }),
  ])

describe("queryPromotionDiscountProducts", () => {
  let rawPromotionCampaign = mockRawPromotionCampaign(
    ~discounts=mockRawPromotionCampaignDiscounts(
      [
        mockRawPromotionCampaignEdge(
          ~value=1.,
          ~kind="CURRENCY",
          ~variant=mockRawVariantPromotionCampaign(
            ~id="mock-variant-id-a",
            ~stockKeepingUnit=Some("mock-stockKeepingUnit-id-a"),
            (),
          ),
        ),
        mockRawPromotionCampaignEdge(
          ~value=2.,
          ~kind="PERCENT",
          ~variant=mockRawVariantPromotionCampaign(
            ~id="mock-variant-id-b",
            ~stockKeepingUnit=Some("mock-stockKeepingUnit-id-b"),
            (),
          ),
        ),
        mockRawPromotionCampaignEdge(
          ~value=3.,
          ~kind="PERCENT",
          ~variant=mockRawVariantPromotionCampaign(
            ~id="mock-variant-id-c",
            ~stockKeepingUnit=Some("mock-stockKeepingUnit-id-c"),
            (),
          ),
        ),
        mockRawPromotionCampaignEdge(
          ~value=4.,
          ~kind="CURRENCY",
          ~variant=mockRawVariantPromotionCampaign(
            ~id="mock-variant-id-d",
            ~stockKeepingUnit=Some("mock-stockKeepingUnit-id-d"),
            (),
          ),
        ),
      ],
      (),
    ),
    (),
  )

  let promotionCampaign = rawPromotionCampaign.discounts.edges->Array.map(({node}) =>
    mockPromotionCampaign(
      ~kind={
        switch node.kind {
        | "CURRENCY" => #CURRENCY
        | _ => #PERCENT
        }
      },
      ~value=node.value,
      ~id=node.variant.id,
      ~stockKeepingUnit=node.variant.stockKeepingUnit->Js.Nullable.toOption,
      (),
    )
  )

  itFuture("should return discounted products", () => {
    serverUsePromotionCampaignHandler(Some(rawPromotionCampaign))

    queryPromotionDiscountProducts(~promotionCampaignId="", ~first=2, ())->Future.tap(
      result => {
        expect(
          switch result {
          | Ok(Some(array)) => array
          | _ => []
          },
        )->toHaveLength(4)
        expect(result)->toStrictEqual(Ok(Some(promotionCampaign)))
      },
    )
  })

  itFuture("should return no data without error", () => {
    serverUsePromotionCampaignHandler(Some(mockRawPromotionCampaign()))

    queryPromotionDiscountProducts(~promotionCampaignId="", ())->Future.tap(
      result => {
        expect(result)->toStrictEqual(Ok(Some([])))
      },
    )
  })
})

describe("queryAndMakeCsvBlob", () => {
  let rawPromotionCampaign = mockRawPromotionCampaign(
    ~discounts=mockRawPromotionCampaignDiscounts(
      [
        mockRawPromotionCampaignEdge(
          ~value=10.,
          ~kind="CURRENCY",
          ~variant=mockRawVariantPromotionCampaign(
            ~id="mock-variant-id-a",
            ~stockKeepingUnit=Some("mock-stockKeepingUnit-id-a"),
            (),
          ),
        ),
      ],
      (),
    ),
    (),
  )

  itFuture("should fetch promotionCampaign data and make csv blob out of it", () => {
    serverUsePromotionCampaignHandler(Some(rawPromotionCampaign))

    queryAndMakeCsvBlob(~promotionCampaignId="")
    ->Future.mapError(_ => ())
    ->Future.tap(
      result => {
        let blobType = result->Result.map(blob => blob->Blob.type_)
        expect(blobType)->toStrictEqual(Ok("text/csv;charset=utf-8"))
      },
    )
    ->Future.flatMapOk(blob => blob->Blob.text->FuturePromise.fromPromise->Future.mapError(_ => ()))
    ->Future.tap(
      result => {
        expect(result)->toStrictEqual(
          Ok(`Variant ID;SKU;Promotional value;Promotional type
mock-variant-id-a;mock-stockKeepingUnit-id-a;10;eur
`),
        )
      },
    )
  })

  itFuture("should not make a Csv blob and return an error when no data has been fetched", () => {
    serverUsePromotionCampaignHandler(Some(mockRawPromotionCampaign()))

    queryAndMakeCsvBlob(~promotionCampaignId="")->Future.tap(
      result => {
        expect(result)->toStrictEqual(Error(NoDataFailure))
      },
    )
  })
})

describe("queryAndMakeExcelBlob", () => {
  let rawPromotionCampaign = mockRawPromotionCampaign(
    ~discounts=mockRawPromotionCampaignDiscounts(
      [
        mockRawPromotionCampaignEdge(
          ~value=10.,
          ~kind="CURRENCY",
          ~variant=mockRawVariantPromotionCampaign(
            ~id="mock-variant-id-a",
            ~stockKeepingUnit=Some("mock-stockKeepingUnit-id-a"),
            (),
          ),
        ),
      ],
      (),
    ),
    (),
  )

  itFuture("should fetch promotionCampaign data and make csv blob out of it", () => {
    serverUsePromotionCampaignHandler(Some(rawPromotionCampaign))

    queryAndMakeExcelBlob(~promotionCampaignId="")->Future.tap(
      result => {
        let blob = result->Result.getExn

        expect(blob->Blob.size)->toBeGreaterThan(5000.)
        expect(blob->Blob.type_)->toBe(
          "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        )
      },
    )
  })

  itFuture("should not make a Excel blob and return an error when no data has been fetched", () => {
    serverUsePromotionCampaignHandler(Some(mockRawPromotionCampaign()))

    queryAndMakeExcelBlob(~promotionCampaignId="")->Future.tap(
      result => {
        expect(result)->toStrictEqual(Error(NoDataFailure))
      },
    )
  })
})

describe("makeFilename", () => {
  it("should return a string", () =>
    expect(
      "name"->makeFilename(
        ~date=Js.Date.makeWithYMD(~year=1970., ~month=1., ~date=1., ()),
        ~extension="abc",
      ),
    )->toStrictEqual("promotionsexport_name_2-1-1970.abc")
  )

  it("should return a string with no empty space", () =>
    expect(
      "empty space name"->makeFilename(
        ~date=Js.Date.makeWithYMD(~year=1970., ~month=1., ~date=1., ()),
        ~extension="abc",
      ),
    )->toStrictEqual("promotionsexport_empty_space_name_2-1-1970.abc")
  )
})

describe("makeCsvFilename", () => {
  it("should return a string", () =>
    expect(
      "name"->makeCsvFilename(~date=Js.Date.makeWithYMD(~year=1970., ~month=1., ~date=1., ())),
    )->toStrictEqual("promotionsexport_name_2-1-1970.csv")
  )
})

describe("makeExcelFilename", () => {
  it("should return a string", () =>
    expect(
      "name"->makeExcelFilename(~date=Js.Date.makeWithYMD(~year=1970., ~month=1., ~date=1., ())),
    )->toStrictEqual("promotionsexport_name_2-1-1970.xlsx")
  )
})

describe("makeRows", () => {
  it("should return an array of strings", () => {
    let promotionCampaign = [
      mockPromotionCampaign(
        ~value=1.,
        ~id="mock-variant-id-a",
        ~stockKeepingUnit=Some("mock-variant-stockKeepingUnit-a"),
        (),
      ),
      mockPromotionCampaign(
        ~value=2.1,
        ~kind=#CURRENCY,
        ~id="mock-variant-id-b",
        ~stockKeepingUnit=Some("mock-variant-stockKeepingUnit-b"),
        (),
      ),
    ]

    expect(promotionCampaign->makeRows)->toStrictEqual([
      ["Variant ID", "SKU", "Promotional value", "Promotional type"],
      ["mock-variant-id-a", "mock-variant-stockKeepingUnit-a", "1", "%"],
      ["mock-variant-id-b", "mock-variant-stockKeepingUnit-b", "2,1", "eur"],
    ])
  })
})

describe("makeCsvPlainText", () => {
  it("should return a string", () => {
    let promotionCampaign = [
      mockPromotionCampaign(
        ~value=1.,
        ~id="mock-variant-id-a",
        ~stockKeepingUnit=Some("mock-variant-stockKeepingUnit-a"),
        (),
      ),
      mockPromotionCampaign(
        ~value=2.,
        ~kind=#CURRENCY,
        ~id="mock-variant-id-b",
        ~stockKeepingUnit=Some("mock-variant-stockKeepingUnit-b"),
        (),
      ),
    ]

    expect(promotionCampaign->makeCsvPlainText)->toStrictEqual(
      Ok(`Variant ID;SKU;Promotional value;Promotional type
mock-variant-id-a;mock-variant-stockKeepingUnit-a;1;%
mock-variant-id-b;mock-variant-stockKeepingUnit-b;2;eur
`),
    )
  })
})

// NOTE - use of raw JS to test value's prototype
let isBlobInstance: Blob.t => bool = %raw(`value => value instanceof Blob`)

describe("makeCsvBlob", () => {
  let blob = [mockPromotionCampaign()]->makeCsvBlob->Result.getExn

  it("should return a blob instance", () => expect(isBlobInstance(blob))->toBe(true))

  it("should return csv mime type", () => expect(Blob.type_(blob))->toBe("text/csv;charset=utf-8"))
})

describe("makeExcelBlob", () => {
  let excelBlob = [mockPromotionCampaign()]->makeExcelBlob

  itFuture("should resolve Blob instance with excel mime type", () =>
    excelBlob->Future.tap(
      excelBlob =>
        expect(excelBlob->Result.map(Blob.type_))->toStrictEqual(
          Ok("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"),
        ),
    )
  )
})
