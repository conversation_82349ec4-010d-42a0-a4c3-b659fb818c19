open Vitest

let {rowsFromEdgesData, makeVariables} = module(StockTransferFormProductPickerModalTable)

let mockTypename = () => ""

let mockQueryProductTaxData = (
  ~productTaxValue=5.5,
  ~productId="",
  ~productName="",
  ~productKind=#SIMPLE,
  ~productColor=None,
  ~productProducer=None,
  ~productDesignation=None,
  ~productWineType=None,
  ~productWhiteWineType=None,
  ~productFamily=None,
  ~productBeerType=None,
  ~productRegion=None,
  ~productCountry=None,
  (),
) => {
  id: productId,
  name: productName,
  kind: productKind,
  producer: productProducer,
  designation: productDesignation,
  family: productFamily,
  beerType: productBeerType,
  color: productColor,
  wineType: productWineType,
  whiteWineType: productWhiteWineType,
  region: productRegion,
  country: productCountry,
  StockTransferFormProductPickerModalTable.Query.tax: {
    value: productTaxValue,
    __typename: mockTypename(),
  },
  __typename: mockTypename(),
}

let mockQueryData = (
  ~id="mock-id",
  ~name="",
  ~formattedName="",
  ~supplierCompanyName="",
  ~bulk=None,
  ~formattedCategory=None,
  ~formattedPurchasedPrice=None,
  ~stockKeepingUnit=None,
  ~priceLookUpCode=None,
  ~internalCode=None,
  ~alcoholVolume=None,
  ~stockState=None,
  ~product=mockQueryProductTaxData(),
  (),
) => {
  StockTransferFormProductPickerModalTable.Query.node: {
    __typename: mockTypename(),
    id,
    cku: "mock-cku",
    name,
    createdAt: Js.Date.makeWithYM(~year=2023., ~month=1., ()),
    formattedName,
    formattedCategory,
    stockKeepingUnit,
    priceLookUpCode,
    internalCode,
    alcoholVolume,
    bulk,
    formattedPurchasedPrice,
    supplier: Some({
      companyName: supplierCompanyName,
      __typename: mockTypename(),
    }),
    stock: {
      __typename: "VariantStock",
      rawQuantity: Some(0),
      formattedQuantity: Some("mock-stock-formatted-quantity"),
      state: stockState,
    },
    product,
  },
  __typename: mockTypename(),
}

describe("rowsFromEdgesData", () => {
  it("should map with default values", () => {
    let result = rowsFromEdgesData([mockQueryData()])
    expect(result)->toEqual([
      {
        id: "mock-id",
        cku: "mock-cku",
        createdAt: Js.Date.makeWithYM(~year=2023., ~month=1., ()),
        formattedName: "",
        productKind: #SIMPLE,
        purchasePrice: None,
        bulk: false,
        stockRawQuantity: 0,
        stockFormattedQuantity: "mock-stock-formatted-quantity",
        stockState: None,
        information: {
          productName: "",
          variantName: "",
          country: "—",
          categoryName: "—",
          supplierName: "",
        },
      },
    ])

    let result = rowsFromEdgesData([
      mockQueryData(
        ~formattedPurchasedPrice=Some("$2.99"),
        ~product=mockQueryProductTaxData(
          ~productTaxValue=5.5,
          ~productId="mock-product-id",
          ~productName="mock-product-name",
          ~productProducer=Some("mock-product-producer"),
          ~productKind=#SIMPLE,
          ~productColor=Some(#AMBER),
          ~productDesignation=Some("mock-product-designation"),
          ~productWineType=Some(#STILL),
          ~productWhiteWineType=Some(#SWEET),
          ~productFamily=Some("mock-product-family"),
          ~productBeerType=Some("mock-product-beertype"),
          ~productRegion=Some("mock-product-region"),
          ~productCountry=Some("mock-product-country"),
          (),
        ),
        ~stockKeepingUnit=Some("12345"),
        ~priceLookUpCode=Some(1),
        ~internalCode=Some("abcde"),
        ~formattedName="mock-formatted-name",
        ~name="mock-variant-name",
        ~supplierCompanyName="mock-supplier-name",
        ~formattedCategory=Some("mock-formatted-category"),
        ~alcoholVolume=Some(12.),
        ~stockState=Some(#ALERT),
        (),
      ),
    ])
    expect(result)->toStrictEqual([
      {
        id: "mock-id",
        cku: "mock-cku",
        createdAt: Js.Date.makeWithYM(~year=2023., ~month=1., ()),
        formattedName: "mock-formatted-name",
        productKind: #SIMPLE,
        purchasePrice: Some("$2.99"),
        bulk: false,
        stockRawQuantity: 0,
        stockFormattedQuantity: "mock-stock-formatted-quantity",
        stockState: Some(#ALERT),
        information: {
          productName: "mock-product-name",
          variantName: "mock-variant-name",
          producerName: "mock-product-producer",
          color: #AMBER,
          designation: "mock-product-designation",
          wineType: #STILL,
          whiteWineType: #SWEET,
          productFamily: "mock-product-family",
          beerType: "mock-product-beertype",
          region: "mock-product-region",
          country: "mock-product-country",
          categoryName: "mock-formatted-category",
          supplierName: "mock-supplier-name",
          alcoholVolume: "12°",
          sku: "12345",
          plu: "1",
          internalCode: "abcde",
        },
      },
    ])
  })
})

test("makeVariables", () => {
  let result = makeVariables(~search="", ~shopId="", ())
  expect(result)->toStrictEqual({
    search: Some(""),
    after: None,
    first: Some(20),
    filterBy: Some({
      shopIds: Some({_in: [""]}),
      active: None,
      archived: None,
    }),
  })

  let result = makeVariables(~after="mock-after", ~search="mock-search", ~shopId="mock-shop-id", ())
  expect(result)->toStrictEqual({
    search: Some("mock-search"),
    after: Some("mock-after"),
    first: Some(20),
    filterBy: Some({
      shopIds: Some({_in: ["mock-shop-id"]}),
      active: None,
      archived: None,
    }),
  })
})
