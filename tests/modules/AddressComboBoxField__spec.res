open Vitest

describe("AddressListRequest", () => {
  test("decode", () => {
    let {decode} = module(AddressComboBoxField.AddressListRequest)

    expect(
      Json.fromObjExn({
        "features": [
          {
            "properties": {
              "label": "address-label-a",
              "name": "address-name-a",
              "postcode": "address-postcode-a"->Js.Nullable.return,
              "city": "address-city-a"->Js.Nullable.return,
              "country": "address-country-a"->Js.Nullable.return,
            },
          },
          {
            "properties": {
              "label": "address-label-b",
              "name": "address-name-b",
              "postcode": Js.Nullable.null,
              "city": Js.Nullable.null,
              "country": Js.Nullable.null,
            },
          },
        ],
      })->decode,
    )->toStrictEqual(
      Ok([
        {
          label: "address-label-a",
          name: "address-name-a",
          postcode: "address-postcode-a",
          city: "address-city-a",
          country: "address-country-a",
        },
        {
          label: "address-label-b",
          name: "address-name-b",
          postcode: "",
          city: "",
          country: "France",
        },
      ]),
    )

    expect(Json.fromObjExn("")->decode)->toStrictEqual(Error(Request.MalformedResponse))

    expect(
      Json.fromObjExn([
        {
          "label": "address-label-a",
          "xx": "address-name-a",
          "postcode": "address-postcode-a"->Js.Nullable.return,
          "city": "address-city-a"->Js.Nullable.return,
          "country": "address-country-a"->Js.Nullable.return,
        },
      ])->decode,
    )->toMatchObject(Error(Request.MalformedResponse))

    expect(
      Json.fromObjExn([
        {
          "xx": "address-label-a",
          "name": "address-name-a",
          "postcode": "address-postcode-a"->Js.Nullable.return,
          "city": "address-city-a"->Js.Nullable.return,
          "country": "address-country-a"->Js.Nullable.return,
        },
      ])->decode,
    )->toMatchObject(Error(Request.MalformedResponse))
  })
})

todo("component")
