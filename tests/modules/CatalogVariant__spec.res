open Vitest

type fake = {id: string, name: string}

open CatalogVariant

describe("Name", () => {
  open Name

  describe("placeholderFromProductKind", () => {
    it(
      "should return placeholder",
      () => {
        expect(#WINE->placeholderFromProductKind)->toBe("0,75L - 2017")
        expect(#SPIRITUOUS->placeholderFromProductKind)->toBe("0,7L - 10 years")
        expect(#BEER->placeholderFromProductKind)->toBe("0,33L")
        expect(#SIMPLE->placeholderFromProductKind)->toBe("0,5kg")
      },
    )
  })

  describe("make", () => {
    it(
      "should return an empty string if unit is null and bulk is true",
      () => expect(make(~capacityValue=None, ~capacityUnit=None, ~bulk=true, ~year=None))->toBe(""),
    )
    it(
      "should return string if capacityUnit is null",
      () => {
        expect(make(~capacityValue=None, ~capacityUnit=None, ~bulk=false, ~year=None))->toBe(
          "Piece",
        )
        expect(make(~capacityValue=Some(0.), ~capacityUnit=None, ~bulk=false, ~year=None))->toBe(
          "Piece",
        )
        expect(make(~capacityValue=Some(1.), ~capacityUnit=None, ~bulk=false, ~year=None))->toBe(
          "Piece",
        )
        expect(make(~capacityValue=Some(2.), ~capacityUnit=None, ~bulk=false, ~year=None))->toBe(
          "Piece",
        )
      },
    )
    it(
      "should return string if value is positive with a capacityUnit",
      () =>
        expect(
          make(~capacityValue=Some(5.), ~capacityUnit=Some("L"), ~bulk=false, ~year=None),
        )->toBe("5L"),
    )
    it(
      "should return string if value is a decimal with a capacityUnit",
      () =>
        expect(
          make(~capacityValue=Some(0.75), ~capacityUnit=Some("L"), ~bulk=false, ~year=None),
        )->toBe("0,75L"),
    )
    it(
      "should return string if value is a decimal with a capacityUnit and an absolute year",
      () =>
        expect(
          make(~capacityValue=Some(0.75), ~capacityUnit=Some("L"), ~bulk=false, ~year=Some(2017)),
        )->toBe("0,75L - 2017"),
    )
    it(
      "should return string if value is a decimal with a capacityUnit and relative year",
      () =>
        expect(
          make(~capacityValue=Some(0.75), ~capacityUnit=Some("L"), ~bulk=false, ~year=Some(10)),
        )->toBe("0,75L - 10 years"),
    )
    it(
      "should return string if value is a decimal with a capacityUnit and year is 1",
      () =>
        expect(
          make(~capacityValue=Some(0.75), ~capacityUnit=Some("L"), ~bulk=false, ~year=Some(1)),
        )->toBe("0,75L - 1 year"),
    )
    it(
      "should return string if bulk mode is on without capacity unit",
      () => expect(make(~capacityValue=None, ~capacityUnit=None, ~bulk=true, ~year=None))->toBe(""),
    )
    it(
      "should return string if bulk mode is on with a capacity unit",
      () =>
        expect(
          make(~capacityValue=Some(0.), ~capacityUnit=Some("g"), ~bulk=true, ~year=None),
        )->toBe("Bulk by g"),
    )
    it(
      "should return string if bulk mode is on with a capacity unit and a year",
      () =>
        expect(
          make(~capacityValue=Some(1.), ~capacityUnit=Some("g"), ~bulk=true, ~year=Some(2021)),
        )->toBe("Bulk by g - 2021"),
    )
  })
})

describe("CapacityUnit", () => {
  open CapacityUnit

  describe("getDefaultFromProductKind", () => {
    it(
      "should return default string",
      () => {
        expect(#SIMPLE->getDefaultFromProductKind)->toBe(Some("kg"))
        expect(#SPIRITUOUS->getDefaultFromProductKind)->toBe(Some("L"))
        expect(#WINE->getDefaultFromProductKind)->toBe(Some("L"))
        expect(#BEER->getDefaultFromProductKind)->toBe(Some("L"))
      },
    )
  })

  describe("makeOptions", () => {
    it(
      "when product kind is wine",
      () => expect(makeOptions(~productKind=#WINE))->toStrictEqual(["L", "cL"]),
    )
    it(
      "when product kind is spirituous",
      () => expect(makeOptions(~productKind=#SPIRITUOUS))->toStrictEqual(["L", "cL"]),
    )
    it(
      "when product kind is beer",
      () => expect(makeOptions(~productKind=#BEER))->toStrictEqual(["L", "cL", "mL"]),
    )
    it(
      "when product kind is simple",
      () => expect(makeOptions(~productKind=#SIMPLE))->toStrictEqual(["kg", "g", "L", "cL", "mL"]),
    )
  })
})

describe("CapacityValue", () => {
  open CapacityValue

  describe("placeholderFromProductKind", () => {
    it(
      "should return default string",
      () => {
        expect(#SIMPLE->placeholderFromProductKind)->toBe("0,50")
        expect(#SPIRITUOUS->placeholderFromProductKind)->toBe("0,70")
        expect(#WINE->placeholderFromProductKind)->toBe("0,75")
        expect(#BEER->placeholderFromProductKind)->toBe("0,33")
      },
    )
  })
})
describe("Year", () => {
  open Year

  describe("placeholderFromProductKind", () => {
    it(
      "should return default string",
      () => {
        expect(#SIMPLE->placeholderFromProductKind)->toBe("")
        expect(#SPIRITUOUS->placeholderFromProductKind)->toBe("10")
        expect(#WINE->placeholderFromProductKind)->toBe("2017")
        expect(#BEER->placeholderFromProductKind)->toBe("")
      },
    )
  })
})
describe("Volume", () => {
  open Volume

  describe("placeholderFromProductKind", () => {
    it(
      "should return default string",
      () => {
        expect(#WINE->placeholderFromProductKind)->toBe("13,10")
        expect(#SPIRITUOUS->placeholderFromProductKind)->toBe("42,00")
        expect(#BEER->placeholderFromProductKind)->toBe("4,50")
        expect(#SIMPLE->placeholderFromProductKind)->toBe("")
      },
    )
  })
})

describe("MultiShops", () => {
  open MultiShops

  describe("arrayKeepUniqueBy", () => {
    it(
      "should remove duplicates with one property",
      () => {
        let data = [
          {id: "a9832", name: "apple"},
          {id: "b1735", name: "blueberry"},
          {id: "c0953", name: "apple"},
        ]
        expect(data->arrayKeepUniqueBy((a, b) => a.name == b.name))->toStrictEqual([
          {id: "a9832", name: "apple"},
          {id: "b1735", name: "blueberry"},
        ])
      },
    )

    it(
      "should remove duplicates with two properties",
      () => {
        let data = [
          {id: "a9832", name: "apple"},
          {id: "b1735", name: "blueberry"},
          {id: "c0953", name: "mango"},
          {id: "d0291", name: "apple"},
        ]
        expect(data->arrayKeepUniqueBy((a, b) => a.id == b.id && a.name == b.name))->toStrictEqual(
          data,
        )
      },
    )
  })

  describe("isReferenceDiffering", () => {
    let shop: Auth.shop = {
      id: "",
      name: "",
      corporateName: "",
      kind: #INTEGRATED,
      address: "",
      postalCode: "",
      city: "",
      country: "",
      phoneNumber: "",
      email: "",
      legalRepresentative: None,
      logoUri: None,
      activeWebDeviceId: "",
      bankName: None,
      cityOfRegistryOffice: None,
      website: None,
      fiscalYearEndClosingMonth: None,
      legalForm: None,
      amountOfShareCapital: None,
      tvaNumber: None,
      siretNumber: None,
      rcsNumber: None,
      apeNafCode: None,
      bankCode: None,
      bankAccountHolder: None,
      bankAccountNumber: None,
      bicCode: None,
      ibanNumber: None,
    }

    it(
      "when references are differing and scope isn't shop filtering",
      () => {
        let shopsReference = [
          {id: "a9832", name: "Richebourg Grand Cru"},
          {id: "2kd83", name: "Anjou Blanc Montgilet"},
        ]
        expect(
          shopsReference->isReferenceDiffering(
            ~scope=Organisation({activeShop: None, shops: []}),
            ~predicate=(referenceA, referenceB) => referenceA.name == referenceB.name,
          ),
        )->toBe(true)
      },
    )

    it(
      "when references are not differing and scope isn't shop filtering",
      () => {
        let shopsReference = [
          {id: "a9832", name: "Richebourg Grand Cru"},
          {id: "c0451", name: "Richebourg Grand Cru"},
        ]
        expect(
          shopsReference->isReferenceDiffering(
            ~scope=Organisation({activeShop: None, shops: []}),
            ~predicate=(referenceA, referenceB) => referenceA.name == referenceB.name,
          ),
        )->toBe(false)
      },
    )

    it(
      "when references are not differing and scope is shop filtering",
      () => {
        let shopsReference = [
          {id: "a9832", name: "Richebourg Grand Cru"},
          {id: "2kd83", name: "Anjou Blanc Montgilet"},
        ]
        expect(
          shopsReference->isReferenceDiffering(
            ~scope=Organisation({activeShop: None, shops: []}),
            ~predicate=(referenceA, referenceB) => referenceA.name == referenceB.name,
          ),
        )->toBe(true)
      },
    )

    it(
      "when references are differing and scope is shop filtering",
      () => {
        let shopsReference = [
          {id: "a9832", name: "Richebourg Grand Cru"},
          {id: "2kd83", name: "Anjou Blanc Montgilet"},
        ]
        expect(
          shopsReference->isReferenceDiffering(
            ~scope=Organisation({activeShop: Some(shop), shops: []}),
            ~predicate=(referenceA, referenceB) => referenceA.name == referenceB.name,
          ),
        )->toBe(false)
      },
    )
  })
})

describe("StockQuantity", () => {
  open StockQuantity

  it("should format", () => {
    expect(4850->format(~capacityPrecision=Some(3), ~capacityUnit=None))->toStrictEqual("4,85")
    expect(4850->format(~capacityPrecision=Some(1), ~capacityUnit=None))->toStrictEqual("485")
    expect(4850->format(~capacityPrecision=Some(1), ~capacityUnit=Some("kg")))->toStrictEqual(
      "485kg",
    )
    expect(237->format(~capacityPrecision=Some(3), ~capacityUnit=None))->toStrictEqual("0,237")
    expect(237->format(~capacityPrecision=Some(2), ~capacityUnit=None))->toStrictEqual("2,37")
    expect(52->format(~capacityPrecision=Some(3), ~capacityUnit=None))->toStrictEqual("0,052")
    expect(52->format(~capacityPrecision=Some(1), ~capacityUnit=None))->toStrictEqual("5,2")
    expect(6->format(~capacityPrecision=Some(3), ~capacityUnit=None))->toStrictEqual("0,006")
    expect(6->format(~capacityPrecision=Some(3), ~capacityUnit=Some("cL")))->toStrictEqual(
      "0,006cL",
    )
    expect(67->format(~capacityPrecision=Some(2), ~capacityUnit=None))->toStrictEqual("0,67")
    expect(67->format(~capacityPrecision=Some(1), ~capacityUnit=None))->toStrictEqual("6,7")
    expect(0->format(~capacityPrecision=Some(3), ~capacityUnit=None))->toStrictEqual("0")
    expect(100->format(~capacityPrecision=None, ~capacityUnit=None))->toStrictEqual("100")
    expect(10->format(~capacityPrecision=None, ~capacityUnit=None))->toStrictEqual("10")
    expect(1->format(~capacityPrecision=None, ~capacityUnit=None))->toStrictEqual("1")
    expect(1->format(~capacityPrecision=None, ~capacityUnit=Some("L")))->toStrictEqual("1L")
  })
})
