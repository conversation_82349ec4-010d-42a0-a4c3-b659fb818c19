open Vitest

open StockTransferSheet

let mockTypename = () => "mock-typename"
let mockId = () => "mock-id"

let mockStockTranferProductEdge = (
  ~formattedName,
  ~formattedDescription,
  ~quantity,
  ~stockKeepingUnit=?,
  (),
) => {
  Query.__typename: mockTypename(),
  node: {
    recipientVariant: {
      formattedName,
      formattedDescription,
      stockKeepingUnit,
      __typename: mockTypename(),
    },
    quantity,
    __typename: mockTypename(),
  },
}

let mockRawStockTranferProductEdge = (
  ~formattedName,
  ~formattedDescription,
  ~quantity,
  ~stockKeepingUnit=?,
  (),
) => {
  Query.Raw.__typename: mockTypename(),
  node: {
    recipientVariant: {
      formattedName,
      formattedDescription,
      stockKeepingUnit: switch stockKeepingUnit {
      | Some(stockKeepingUnit) => stockKeepingUnit->Js.Nullable.return
      | None => Js.Nullable.null
      },
      __typename: mockTypename(),
    },
    quantity,
    __typename: mockTypename(),
  },
}

let mockStockTransferProducts = (
  edges,
  ~totalCount=edges->Array.length,
  ~hasNextPage=None,
  ~endCursor=None,
  (),
) => {
  Query.totalCount,
  __typename: mockTypename(),
  edges,
  pageInfo: {
    __typename: mockTypename(),
    hasNextPage,
    endCursor,
  },
}

let mockRawStockTransferProducts = (
  edges,
  ~totalCount=edges->Array.length,
  ~hasNextPage=None,
  ~endCursor=None,
  (),
) => {
  Query.Raw.totalCount,
  __typename: mockTypename(),
  edges,
  pageInfo: {
    __typename: mockTypename(),
    hasNextPage: switch hasNextPage {
    | Some(hasNextPage) => hasNextPage->Js.Nullable.return
    | None => Js.Nullable.null
    },
    endCursor: switch endCursor {
    | Some(endCursor) => endCursor->Js.Nullable.return
    | None => Js.Nullable.null
    },
  },
}

let mockStockTransferSenderShop = (
  ~name="mock-sendershop-name",
  ~address="mock-sendershop-address",
  ~city="mock-sendershop-city",
  ~postalCode="mock-sendershop-postalcode",
  ~country="mock-sendershop-country",
  ~phoneNumber=?,
  (),
): Query.t_stockTransfer_senderShop => {
  Query.name,
  address,
  city,
  postalCode,
  country,
  phoneNumber,
  __typename: mockTypename(),
}

let mockRawStockTransferSenderShop = (
  ~name="mock-sendershop-name",
  ~address="mock-sendershop-address",
  ~city="mock-sendershop-city",
  ~postalCode="mock-sendershop-postalcode",
  ~country="mock-sendershop-country",
  ~phoneNumber=?,
  (),
): Query.Raw.t_stockTransfer_senderShop => {
  Query.Raw.name,
  address,
  city,
  postalCode,
  country,
  phoneNumber: switch phoneNumber {
  | Some(phoneNumber) => phoneNumber->Js.Nullable.return
  | None => Js.Nullable.null
  },
  __typename: mockTypename(),
}

let mockStockTransferRecipientShop = (
  ~name="mock-recipientshop-name",
  ~address="mock-recipientshop-address",
  ~city="mock-recipientshop-city",
  ~postalCode="mock-recipientshop-postalcode",
  ~country="mock-recipientshop-country",
  ~phoneNumber=?,
  (),
): Query.t_stockTransfer_recipientShop => {
  Query.name,
  address,
  city,
  postalCode,
  country,
  phoneNumber,
  __typename: mockTypename(),
}

let mockRawStockTransferRecipientShop = (
  ~name="mock-recipientshop-name",
  ~address="mock-recipientshop-address",
  ~city="mock-recipientshop-city",
  ~postalCode="mock-recipientshop-postalcode",
  ~country="mock-recipientshop-country",
  ~phoneNumber=?,
  (),
): Query.Raw.t_stockTransfer_recipientShop => {
  Query.Raw.name,
  address,
  city,
  postalCode,
  country,
  phoneNumber: switch phoneNumber {
  | Some(phoneNumber) => phoneNumber->Js.Nullable.return
  | None => Js.Nullable.null
  },
  __typename: mockTypename(),
}

let mockStockTransfer = (
  ~id=mockId(),
  ~code="mock-code",
  ~senderShop=mockStockTransferSenderShop(),
  ~recipientShop=mockStockTransferRecipientShop(),
  ~createdAt=Js.Date.makeWithYMDH(~year=1970., ~month=0., ~date=1., ~hours=0., ()),
  ~products=mockStockTransferProducts([], ()),
  (),
) => {
  Query.id,
  code,
  senderShop,
  recipientShop,
  createdAt,
  products,
  __typename: mockTypename(),
}

let mockRawStockTransfer = (
  ~id=mockId(),
  ~code="mock-code",
  ~senderShop=mockRawStockTransferSenderShop(),
  ~recipientShop=mockRawStockTransferRecipientShop(),
  ~createdAt=Js.Date.makeWithYMDH(~year=1970., ~month=0., ~date=1., ~hours=0., ()),
  ~products=mockRawStockTransferProducts([], ()),
  (),
) => {
  Query.Raw.id,
  code,
  senderShop,
  recipientShop,
  createdAt: createdAt->Scalar.Datetime.serialize,
  products,
  __typename: mockTypename(),
}

let {setupNodeServer, use, listen, resetHandlers, close} = module(MSW)
let {ctxData, makeLink} = module(MSW.GraphQL)

let server = setupNodeServer([])
let gatewayLink = makeLink("http://localhost/graphql")

beforeAll(() => server->listen({onUnhandledRequest: #warn}))
afterEach(() => server->resetHandlers)
afterAll(() => server->close)

let serverUseStockTransferHandler = stockTransfer =>
  server->use([
    gatewayLink->MSWHelpers.apolloQueryExn(module(Query), (req, res, ctx) => {
      let response = ctx->ctxData({
        stockTransfer: switch stockTransfer {
        | Some({Query.Raw.products: products}) =>
          let {edges: productsEdges, pageInfo: productsPageInfo, totalCount} =
            products.edges->MSWHelpers.cursorPaginateExn(
              ~first=?req.variables.productsFirst->Js.Nullable.toOption,
              ~after=?req.variables.productsAfter->Js.Nullable.toOption,
              (),
            )

          mockRawStockTransfer(
            ~products=mockRawStockTransferProducts(
              productsEdges,
              ~totalCount,
              ~endCursor=productsPageInfo.endCursor,
              ~hasNextPage=productsPageInfo.hasNextPage,
              (),
            ),
            (),
          )->Js.Nullable.return
        | None => Js.Nullable.null
        },
      })

      res(response)
    }),
  ])

describe("queryWithAllProducts", () => {
  let rawStockTransfer = mockRawStockTransfer(
    ~products=mockRawStockTransferProducts(
      [
        mockRawStockTranferProductEdge(
          ~formattedName="mock-product-a-name",
          ~formattedDescription="mock-product-a-description",
          ~stockKeepingUnit="mock-product-a-stockkeepingunit",
          ~quantity=1,
          (),
        ),
        mockRawStockTranferProductEdge(
          ~formattedName="mock-product-b-name",
          ~formattedDescription="mock-product-b-description",
          ~stockKeepingUnit="mock-product-b-stockkeepingunit",
          ~quantity=0,
          (),
        ),
        mockRawStockTranferProductEdge(
          ~formattedName="mock-product-c-name",
          ~formattedDescription="mock-product-c-description",
          ~stockKeepingUnit="mock-product-c-stockkeepingunit",
          ~quantity=24,
          (),
        ),
      ],
      (),
    ),
    (),
  )

  let stockTransfer = mockStockTransfer(
    ~products=mockStockTransferProducts(
      rawStockTransfer.products.edges->Array.map(({node}) =>
        mockStockTranferProductEdge(
          ~formattedName=node.recipientVariant.formattedName,
          ~formattedDescription=node.recipientVariant.formattedDescription,
          ~stockKeepingUnit=?node.recipientVariant.stockKeepingUnit->Js.Nullable.toOption,
          ~quantity=node.quantity,
          (),
        )
      ),
      (),
    ),
    (),
  )

  itFuture("should paginate at once", () => {
    serverUseStockTransferHandler(Some(rawStockTransfer))

    StockTransferSheet.queryWithAllProducts(~stockTransferId="", ())->Future.tap(
      result =>
        expect(result)->toStrictEqual(
          Ok(
            Some(
              mockStockTransfer(
                ~products=mockStockTransferProducts(
                  stockTransfer.products.edges,
                  ~endCursor=Some("2"),
                  ~hasNextPage=Some(false),
                  (),
                ),
                (),
              ),
            ),
          ),
        ),
    )
  })

  itFuture("should paginate stock transfer products one by one", () => {
    serverUseStockTransferHandler(Some(rawStockTransfer))

    StockTransferSheet.queryWithAllProducts(
      ~stockTransferId="",
      ~productEdgesPerFetch=1,
      (),
    )->Future.tap(
      result =>
        expect(result)->toStrictEqual(
          Ok(
            Some(
              mockStockTransfer(
                ~products=mockStockTransferProducts(
                  stockTransfer.products.edges,
                  ~endCursor=Some("2"),
                  ~hasNextPage=Some(false),
                  (),
                ),
                (),
              ),
            ),
          ),
        ),
    )
  })

  itFuture("should return no product data without error", () => {
    serverUseStockTransferHandler(Some(mockRawStockTransfer()))

    StockTransferSheet.queryWithAllProducts(~stockTransferId="", ())->Future.tap(
      result => expect(result)->toStrictEqual(Ok(Some(mockStockTransfer()))),
    )
  })
})

describe("queryAndMakeCsvBlob", () => {
  let rawStockTransfer = mockRawStockTransfer(
    ~products=mockRawStockTransferProducts(
      [
        mockRawStockTranferProductEdge(
          ~formattedName="mock-product-a-name",
          ~formattedDescription="mock-product-a-description",
          ~quantity=1,
          (),
        ),
      ],
      (),
    ),
    (),
  )

  itFuture("should fetch stockTransfer data and make Csv blob out of it", () => {
    serverUseStockTransferHandler(Some(rawStockTransfer))

    queryAndMakeCsvBlob(~stockTransferId="")
    ->Future.mapError(_ => ())
    ->Future.tap(
      result => {
        let blobType = result->Result.map(blob => blob->Blob.type_)
        expect(blobType)->toStrictEqual(Ok("text/csv;charset=utf-8"))
      },
    )
    ->Future.flatMapOk(blob => blob->Blob.text->FuturePromise.fromPromise->Future.mapError(_ => ()))
    ->Future.tap(
      result =>
        expect(result)->toStrictEqual(
          Ok(`Transfer of 1/1/70 at 12:00 AM;Reference : mock-code;;
;;;
Sender :;Recipient :;;
mock-sendershop-name;mock-recipientshop-name;;
mock-sendershop-address mock-sendershop-postalcode mock-sendershop-city, mock-sendershop-country;mock-recipientshop-address mock-recipientshop-postalcode mock-recipientshop-city, mock-recipientshop-country;;
;;;
;;;
SKU;Product's name;Description;Transferred quantity
;mock-product-a-name;mock-product-a-description;1
`),
        ),
    )
  })

  itFuture("should fetch stockTransfer data and without products", () => {
    serverUseStockTransferHandler(Some(mockRawStockTransfer()))

    queryAndMakeCsvBlob(~stockTransferId="")
    ->Future.mapError(_ => ())
    ->Future.tap(
      result => {
        let blobType = result->Result.map(blob => blob->Blob.type_)
        expect(blobType)->toStrictEqual(Ok("text/csv;charset=utf-8"))
      },
    )
    ->Future.flatMapOk(blob => blob->Blob.text->FuturePromise.fromPromise->Future.mapError(_ => ()))
    ->Future.tap(
      result =>
        expect(result)->toStrictEqual(
          Ok(`Transfer of 1/1/70 at 12:00 AM;Reference : mock-code;;
;;;
Sender :;Recipient :;;
mock-sendershop-name;mock-recipientshop-name;;
mock-sendershop-address mock-sendershop-postalcode mock-sendershop-city, mock-sendershop-country;mock-recipientshop-address mock-recipientshop-postalcode mock-recipientshop-city, mock-recipientshop-country;;
;;;
;;;
SKU;Product's name;Description;Transferred quantity
`),
        ),
    )
  })

  itFuture("should not make a Csv blob and return an error when no data has been fetched", () => {
    serverUseStockTransferHandler(None)

    StockTransferSheet.queryAndMakeCsvBlob(~stockTransferId="")->Future.tap(
      result => expect(result)->toStrictEqual(Error(NoDataFailure)),
    )
  })
})

describe("queryAndMakeExcelBlob", () => {
  let rawStockTransfer = mockRawStockTransfer(
    ~products=mockRawStockTransferProducts(
      [
        mockRawStockTranferProductEdge(
          ~formattedName="mock-product-a-name",
          ~formattedDescription="mock-product-a-description",
          ~quantity=1,
          (),
        ),
      ],
      (),
    ),
    (),
  )

  itFuture("should fetch stockTransfer data and make Excel blob out of it", () => {
    serverUseStockTransferHandler(Some(rawStockTransfer))

    StockTransferSheet.queryAndMakeExcelBlob(~stockTransferId="")->Future.tap(
      result =>
        expect(result->Result.getExn->Blob.type_)->toBe(
          "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        ),
    )
  })

  itFuture("should not make a Excel blob and return an error when no data has been fetched", () => {
    serverUseStockTransferHandler(None)

    StockTransferSheet.queryAndMakeExcelBlob(~stockTransferId="")->Future.tap(
      result => expect(result)->toStrictEqual(Error(NoDataFailure)),
    )
  })
})

describe("makeRows", () => {
  it("should return an array of strings", () => {
    let stockTransfer = mockStockTransfer(
      ~products=mockStockTransferProducts(
        [
          mockStockTranferProductEdge(
            ~formattedName="mock-product-a-name",
            ~formattedDescription="mock-product-a-description",
            ~quantity=1,
            (),
          ),
          mockStockTranferProductEdge(
            ~formattedName="mock-product-b-name",
            ~formattedDescription="mock-product-b-description",
            ~quantity=0,
            (),
          ),
          mockStockTranferProductEdge(
            ~formattedName="mock-product-c-name",
            ~formattedDescription="mock-product-c-description",
            ~quantity=24,
            ~stockKeepingUnit="mock-product-stockkeepingunit",
            (),
          ),
        ],
        (),
      ),
      (),
    )

    expect(stockTransfer->makeRows)->toStrictEqual([
      ["Transfer of 1/1/70 at 12:00 AM", "Reference : mock-code"],
      [],
      ["Sender :", "Recipient :"],
      ["mock-sendershop-name", "mock-recipientshop-name"],
      [
        "mock-sendershop-address mock-sendershop-postalcode mock-sendershop-city, mock-sendershop-country",
        "mock-recipientshop-address mock-recipientshop-postalcode mock-recipientshop-city, mock-recipientshop-country",
      ],
      ["", ""],
      [],
      ["SKU", "Product's name", "Description", "Transferred quantity"],
      ["", "mock-product-a-name", "mock-product-a-description", "1"],
      ["", "mock-product-b-name", "mock-product-b-description", "0"],
      ["mock-product-stockkeepingunit", "mock-product-c-name", "mock-product-c-description", "24"],
    ])

    let stockTransfer = mockStockTransfer(
      ~senderShop=mockStockTransferSenderShop(~phoneNumber="mock-sendershop-phonenumber", ()),
      ~recipientShop=mockStockTransferRecipientShop(),
      (),
    )

    expect(stockTransfer->makeRows)->toStrictEqual([
      ["Transfer of 1/1/70 at 12:00 AM", "Reference : mock-code"],
      [],
      ["Sender :", "Recipient :"],
      ["mock-sendershop-name", "mock-recipientshop-name"],
      [
        "mock-sendershop-address mock-sendershop-postalcode mock-sendershop-city, mock-sendershop-country",
        "mock-recipientshop-address mock-recipientshop-postalcode mock-recipientshop-city, mock-recipientshop-country",
      ],
      ["mock-sendershop-phonenumber", ""],
      [],
      ["SKU", "Product's name", "Description", "Transferred quantity"],
    ])

    let stockTransfer = mockStockTransfer(
      ~senderShop=mockStockTransferSenderShop(~phoneNumber="mock-sendershop-phonenumber", ()),
      ~recipientShop=mockStockTransferRecipientShop(
        ~phoneNumber="mock-recipientshop-phonenumber",
        (),
      ),
      (),
    )

    expect(stockTransfer->makeRows)->toStrictEqual([
      ["Transfer of 1/1/70 at 12:00 AM", "Reference : mock-code"],
      [],
      ["Sender :", "Recipient :"],
      ["mock-sendershop-name", "mock-recipientshop-name"],
      [
        "mock-sendershop-address mock-sendershop-postalcode mock-sendershop-city, mock-sendershop-country",
        "mock-recipientshop-address mock-recipientshop-postalcode mock-recipientshop-city, mock-recipientshop-country",
      ],
      ["mock-sendershop-phonenumber", "mock-recipientshop-phonenumber"],
      [],
      ["SKU", "Product's name", "Description", "Transferred quantity"],
    ])
  })
})

describe("makeCsvPlainText", () => {
  it("should return a string", () => {
    let stockTransfer = mockStockTransfer(
      ~products=mockStockTransferProducts(
        [
          mockStockTranferProductEdge(
            ~formattedName="mock-product-a-name",
            ~formattedDescription="mock-product-a-description",
            ~quantity=1,
            (),
          ),
          mockStockTranferProductEdge(
            ~formattedName="mock-product-b-name",
            ~formattedDescription="mock-product-b-description",
            ~quantity=0,
            ~stockKeepingUnit="mock-product-stockkeepingunit",
            (),
          ),
          mockStockTranferProductEdge(
            ~formattedName="mock-product-c-name",
            ~formattedDescription="mock-product-c-description",
            ~quantity=24,
            (),
          ),
        ],
        (),
      ),
      (),
    )

    expect(stockTransfer->makeCsvPlainText)->toStrictEqual(
      Ok(`Transfer of 1/1/70 at 12:00 AM;Reference : mock-code;;
;;;
Sender :;Recipient :;;
mock-sendershop-name;mock-recipientshop-name;;
mock-sendershop-address mock-sendershop-postalcode mock-sendershop-city, mock-sendershop-country;mock-recipientshop-address mock-recipientshop-postalcode mock-recipientshop-city, mock-recipientshop-country;;
;;;
;;;
SKU;Product's name;Description;Transferred quantity
;mock-product-a-name;mock-product-a-description;1
mock-product-stockkeepingunit;mock-product-b-name;mock-product-b-description;0
;mock-product-c-name;mock-product-c-description;24
`),
    )

    let stockTransfer = mockStockTransfer(
      ~senderShop=mockStockTransferSenderShop(~phoneNumber="mock-sendershop-phonenumber", ()),
      ~recipientShop=mockStockTransferRecipientShop(
        ~phoneNumber="mock-recipientshop-phonenumber",
        (),
      ),
      (),
    )

    expect(stockTransfer->makeCsvPlainText)->toStrictEqual(
      Ok(`Transfer of 1/1/70 at 12:00 AM;Reference : mock-code;;
;;;
Sender :;Recipient :;;
mock-sendershop-name;mock-recipientshop-name;;
mock-sendershop-address mock-sendershop-postalcode mock-sendershop-city, mock-sendershop-country;mock-recipientshop-address mock-recipientshop-postalcode mock-recipientshop-city, mock-recipientshop-country;;
mock-sendershop-phonenumber;mock-recipientshop-phonenumber;;
;;;
SKU;Product's name;Description;Transferred quantity
`),
    )
  })
})

describe("makeFilename", () =>
  it("should return a string", () =>
    expect(makeFilename(~code="code", ~extension="abc"))->toStrictEqual("transferorder_code.abc")
  )
)

describe("makeCsvFilename", () => {
  it("should return a string", () =>
    expect(makeCsvFilename(~code="code"))->toStrictEqual("transferorder_code.csv")
  )
})

describe("makeExcelFilename", () => {
  it("should return a string", () =>
    expect(makeExcelFilename(~code="code"))->toStrictEqual("transferorder_code.xlsx")
  )
})

// NOTE - use of raw JS to test value's prototype
let isBlobInstance: Blob.t => bool = %raw(`value => value instanceof Blob`)

describe("makeCsvBlob", () => {
  let blob = mockStockTransfer()->makeCsvBlob->Result.getExn

  it("should return Blob instance", () => expect(isBlobInstance(blob))->toBe(true))

  it("should return csv mime type", () => expect(Blob.type_(blob))->toBe("text/csv;charset=utf-8"))
})

describe("makeExcelBlob", () => {
  let blob = mockStockTransfer()->makeExcelBlob

  itFuture("should resolve Blob instance with excel mime type", () =>
    blob->Future.tap(
      result => {
        let blob = result->Result.getExn
        expect(Blob.type_(blob))->toBe(
          "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        )
        expect(isBlobInstance(blob))->toBe(true)
      },
    )
  )
})
