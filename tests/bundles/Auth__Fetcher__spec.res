open Vitest

describe("decodeUserResultWithJwt", () => {
  let {decodeUserResultWithJwt} = module(Auth__Fetcher)

  it("should decode user result", () => {
    // { sub: 'id-a', name: 'name-a', iat: 1678903107, exp: 4789303107 }
    let jwt = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJpZC1hIiwibmFtZSI6Im5hbWUtYSIsImlhdCI6MTY3ODkwMzEwNywiZXhwIjo0Nzg5MzAzMTA3fQ.wD06YizPgLKiZOPe9S3-xkwffGw1-3lW6eM1QVCBmwE"
    let userJson = `{
      "id": "id-a",
      "name": "name-a",
      "organizationName": "organization-name-a",
      "profilePictureUri": "profile-picture-uri-a",
      "username": "username-a",
      "scope": {}
    }`
    expect(decodeUserResultWithJwt(~jwt, ~userResult=Json.parseExn(userJson)))->toStrictEqual(
      Ok({
        id: "id-a",
        name: "name-a",
        organizationName: "organization-name-a",
        profilePictureUri: Some("profile-picture-uri-a"),
        username: "username-a",
        canUseImpersonation: false,
        impersonating: false,
      }),
    )

    // { sub: 'id-a', name: 'name-a', iat: 1678903107, exp: 4789303107 }
    let jwt = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJpZC1hIiwibmFtZSI6Im5hbWUtYSIsImlhdCI6MTY3ODkwMzEwNywiZXhwIjo0Nzg5MzAzMTA3fQ.wD06YizPgLKiZOPe9S3-xkwffGw1-3lW6eM1QVCBmwE"
    let userJson = `{
      "id": "id-a",
      "name": "name-a",
      "organizationName": "organization-name-a",
      "username": "username-a",
      "scope": {}
    }`
    expect(decodeUserResultWithJwt(~jwt, ~userResult=Json.parseExn(userJson)))->toStrictEqual(
      Ok({
        id: "id-a",
        name: "name-a",
        organizationName: "organization-name-a",
        profilePictureUri: None,
        username: "username-a",
        canUseImpersonation: false,
        impersonating: false,
      }),
    )

    // { sub: 'id-a', name: 'name-a', iat: 1678903107, exp: 4789303107 }
    let jwt = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJpZC1hIiwibmFtZSI6Im5hbWUtYSIsImlhdCI6MTY3ODkwMzEwNywiZXhwIjo0Nzg5MzAzMTA3fQ.wD06YizPgLKiZOPe9S3-xkwffGw1-3lW6eM1QVCBmwE"
    let userJson = `{
      "id": "id-a",
      "name": "name-a",
      "organizationName": "organization-name-a",
      "username": "username-a",
      "scope": { "canUseImpersonation": true }
    }`
    expect(decodeUserResultWithJwt(~jwt, ~userResult=Json.parseExn(userJson)))->toStrictEqual(
      Ok({
        id: "id-a",
        name: "name-a",
        organizationName: "organization-name-a",
        profilePictureUri: None,
        username: "username-a",
        canUseImpersonation: true,
        impersonating: false,
      }),
    )

    // { sub: 'id-a', name: 'name-a', iat: 1678903107, exp: 4789303107, scope: { impersonationUser: 'dummy' } }
    let jwt = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJpZC1hIiwibmFtZSI6Im5hbWUtYSIsInNjb3BlIjp7ImltcGVyc29uYXRpb25Vc2VyIjoiZHVtbXkifSwiaWF0IjoxNjc4OTAzMzMxLCJleHAiOjQ3ODkzMDMzMzF9.c1Y645LcplEcfK_PMJJuo2YwSJQED-TL2oIJbBU3avc"
    let userJson = `{
      "id": "id-a",
      "name": "name-a",
      "organizationName": "organization-name-a",
      "username": "username-a",
      "scope": { "canUseImpersonation": true }
    }`
    expect(decodeUserResultWithJwt(~jwt, ~userResult=Json.parseExn(userJson)))->toStrictEqual(
      Ok({
        id: "id-a",
        name: "name-a",
        organizationName: "organization-name-a",
        profilePictureUri: None,
        username: "username-a",
        canUseImpersonation: true,
        impersonating: false,
      }),
    )

    // { sub: 'id-a', name: 'name-a', iat: 1678903107, exp: 4789303107, scope: { impersonationUser: { id: 'impersonation-user-id' } } }
    let jwt = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJpZC1hIiwibmFtZSI6Im5hbWUtYSIsInNjb3BlIjp7ImltcGVyc29uYXRpb25Vc2VyIjp7ImlkIjoiaW1wZXJzb25hdGlvbi11c2VyLWlkIn19LCJpYXQiOjE2Nzg5MDMzODEsImV4cCI6NDc4OTMwMzM4MX0.MS9MXXVVKFQQlFvJGkP98Uh5gN20sel4rs3JhrG5kkM"
    let userJson = `{
      "id": "id-a",
      "name": "name-a",
      "organizationName": "organization-name-a",
      "username": "username-a",
      "scope": {}
    }`
    expect(decodeUserResultWithJwt(~jwt, ~userResult=Json.parseExn(userJson)))->toStrictEqual(
      Ok({
        id: "id-a",
        name: "name-a",
        organizationName: "organization-name-a",
        profilePictureUri: None,
        username: "username-a",
        canUseImpersonation: false,
        impersonating: true,
      }),
    )
  })

  it("should not decode user result", () => {
    // { sub: 'id-a', name: 'name-a', iat: 1678903107, exp: 4789303107 }
    let jwt = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJpZC1hIiwibmFtZSI6Im5hbWUtYSIsImlhdCI6MTY3ODkwMzEwNywiZXhwIjo0Nzg5MzAzMTA3fQ.wD06YizPgLKiZOPe9S3-xkwffGw1-3lW6eM1QVCBmwE"
    let userResult = Json.encodeString("dummy")
    expect(decodeUserResultWithJwt(~jwt, ~userResult))->toStrictEqual(Error())

    // { sub: 'id-a', name: 'name-a', iat: 1678903107, exp: 4789303107 }
    let jwt = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJpZC1hIiwibmFtZSI6Im5hbWUtYSIsImlhdCI6MTY3ODkwMzEwNywiZXhwIjo0Nzg5MzAzMTA3fQ.wD06YizPgLKiZOPe9S3-xkwffGw1-3lW6eM1QVCBmwE"
    let userJson = `{
      "name": "name-a",
      "organizationName": "organization-name-a",
      "profilePictureUri": "profile-picture-uri-a",
      "username": "username-a",
      "scope": { "impersonationUser": true }
    }`
    let userResult = Json.parseExn(userJson)
    expect(decodeUserResultWithJwt(~jwt, ~userResult))->toStrictEqual(Error())

    let userJson = `{
      "id": "id-a",
      "name": "name-a",
      "organizationName": "organization-name-a",
      "profilePictureUri": "profile-picture-uri-a",
      "username": "username-a"
    }`
    let userResult = Json.parseExn(userJson)
    expect(decodeUserResultWithJwt(~jwt="", ~userResult))->toStrictEqual(Error())
  })
})
