open Vitest
open TestingLibraryReact

let {createState} = module(Auth__Context)
let {get: getJwt, set: setJwt, remove: removeJwt} = module(Auth__Jwt)

beforeEach(() => setJwt("token", ()))
afterEach(() => removeJwt())

let testHook = hookCallback =>
  renderHookWithOptions(
    hookCallback,
    ~options={
      wrapper: props => <Providers> {props["children"]} </Providers>,
    },
  )

let mockAuthState = () => {
  Auth__Types.user: {
    organizationName: "user-organization-name",
    id: "user-id",
    name: "user-name",
    username: "user-username",
    profilePictureUri: None,
    canUseImpersonation: false,
    impersonating: false,
  },
  shops: [
    {
      id: "shop-id",
      name: "shop-name",
      corporateName: "shop-corporate-name",
      kind: #INDEPENDENT,
      address: "shop-address",
      postalCode: "shop-postalCode",
      city: "shop-city",
      country: "shop-country",
      phoneNumber: "shop-phone-number",
      email: "shop-email",
      activeWebDeviceId: "shop-active-web-device-id",
      logoUri: None,
      legalRepresentative: Some("shop-legal-representative"),
      bankName: Some("shop-bankName"),
      cityOfRegistryOffice: Some("shop-cityOfRegistryOffice"),
      website: Some("shop-website"),
      fiscalYearEndClosingMonth: Some("shop-fiscalYearEndClosingMonth"),
      legalForm: Some("shop-legalForm"),
      amountOfShareCapital: Some("shop-amountOfShareCapital"),
      tvaNumber: Some("shop-tvaNumber"),
      siretNumber: Some("shop-siretNumber"),
      rcsNumber: Some("shop-rcsNumber"),
      apeNafCode: Some("shop-apeNafCode"),
      bankCode: Some("shop-bankCode"),
      bankAccountHolder: Some("shop-bankAccountHolder"),
      bankAccountNumber: Some("shop-bankAccountNumber"),
      bicCode: Some("shop-bicCode"),
      ibanNumber: Some("shop-ibanNumber"),
    },
  ],
  activeShop: None,
}

describe("createState", () => {
  todo("test without passing initialState")

  it("should initiate the logging proccess", () => {
    let execHook = () => createState(~initialState=Unlogged, ())
    let result = testHook(execHook).result

    let (state, dispatch) = result.current

    expect(state)->toStrictEqual(Some(Unlogged))
    expect(getJwt())->toBe(None)

    act(() => LogRequested({jwt: "token"})->dispatch)

    let (state, _) = result.current

    expect(state)->toEqual(Some(Logging({jwt: "token"})))
    expect(getJwt())->toBe(Some("token"))
  })

  it("should unlog", () => {
    let execHook = () => createState(~initialState=Logged(mockAuthState()), ())
    let result = testHook(execHook).result

    let (state, dispatch) = result.current

    expect(state)->toStrictEqual(Some(Logged(mockAuthState())))
    expect(getJwt())->toBe(Some("token"))

    act(() => Unlogged->dispatch)

    let (state, _) = result.current

    expect(state)->toStrictEqual(Some(Unlogged))
    expect(getJwt())->toBe(None)
  })
})

todo("provider")
