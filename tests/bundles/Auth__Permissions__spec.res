open Vitest

let {mockShop} = module(Auth__Mock)

describe("hasCavavinPermission", () => {
  it(
    "should be authorized if every shops corporateName match the static corporateName (case insensitive)",
    () => {
      let shops = [mockShop(~corporateName="witradis", ()), mockShop(~corporateName="witradis", ())]
      expect(Auth.hasCavavinPermission(~username="", ~organizationName="", ~shops))->toBe(true)

      let shops = [
        mockShop(~corporateName="wino-technologies", ()),
        mockShop(~corporateName="witradis", ()),
      ]
      expect(Auth.hasCavavinPermission(~username="", ~organizationName="", ~shops))->toBe(false)
    },
  )

  it("should be authorized if the username matches with a cavavin email address", () => {
    let username = "<EMAIL>"
    expect(Auth.hasCavavinPermission(~username, ~organizationName="", ~shops=[mockShop()]))->toBe(
      true,
    )
    let username = "<EMAIL>"
    expect(Auth.hasCavavinPermission(~username, ~organizationName="", ~shops=[mockShop()]))->toBe(
      true,
    )
    let username = "<EMAIL>"
    expect(Auth.hasCavavinPermission(~username, ~organizationName="", ~shops=[mockShop()]))->toBe(
      true,
    )

    let username = "<EMAIL>"
    expect(Auth.hasCavavinPermission(~username, ~organizationName="", ~shops=[mockShop()]))->toBe(
      false,
    )

    let username = "cavavin.fr"
    expect(Auth.hasCavavinPermission(~username, ~organizationName="", ~shops=[mockShop()]))->toBe(
      false,
    )
  })

  it("should be authorized if the organizationName matches with cavavin", () => {
    let organizationName = "Cavavin France"
    let username = "<EMAIL>"
    expect(Auth.hasCavavinPermission(~username, ~organizationName, ~shops=[mockShop()]))->toBe(true)

    let organizationName = "Ma franchise cavavin"
    let username = "<EMAIL>"
    expect(Auth.hasCavavinPermission(~username, ~organizationName, ~shops=[mockShop()]))->toBe(true)

    let organizationName = "Groupe CAVAVIN"
    let username = "<EMAIL>"
    expect(Auth.hasCavavinPermission(~username, ~organizationName, ~shops=[mockShop()]))->toBe(true)

    let organizationName = "Groupe Cava"
    let username = "<EMAIL>"
    expect(Auth.hasCavavinPermission(~username, ~organizationName, ~shops=[mockShop()]))->toBe(
      false,
    )
  })

  it("should be authorized if either the username or the shops corporateName match", () => {
    let username = "<EMAIL>"
    let shops = [mockShop(~corporateName="wino-technologies", ())]
    expect(Auth.hasCavavinPermission(~username, ~organizationName="", ~shops))->toBe(true)

    let username = "<EMAIL>"
    let shops = [mockShop(~corporateName="witradis", ())]
    expect(Auth.hasCavavinPermission(~username, ~organizationName="", ~shops))->toBe(true)

    let username = "cavavin.fr"
    let shops = [mockShop(~corporateName="wino-technologies", ())]
    expect(Auth.hasCavavinPermission(~username, ~organizationName="", ~shops))->toBe(false)
  })
})
