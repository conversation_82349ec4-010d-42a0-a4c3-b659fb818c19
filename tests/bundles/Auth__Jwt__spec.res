open Vitest

let {isImpersonated, storageItemKey} = module(Auth__Jwt)

test("isImpersonated", () => {
  // { sub: 'id-a', name: 'name-a', iat: 1678903107, exp: 4789303107 }
  let jwt = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJpZC1hIiwibmFtZSI6Im5hbWUtYSIsImlhdCI6MTY3ODkwMzEwNywiZXhwIjo0Nzg5MzAzMTA3fQ.wD06YizPgLKiZOPe9S3-xkwffGw1-3lW6eM1QVCBmwE"
  expect(isImpersonated(jwt))->toBe(false)

  // { sub: 'id-a', name: 'name-a', iat: 1678903107, exp: 4789303107, scope: { impersonationUser: 'dummy' } }
  let jwt = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJpZC1hIiwibmFtZSI6Im5hbWUtYSIsInNjb3BlIjp7ImltcGVyc29uYXRpb25Vc2VyIjoiZHVtbXkifSwiaWF0IjoxNjc4OTAzMzMxLCJleHAiOjQ3ODkzMDMzMzF9.c1Y645LcplEcfK_PMJJuo2YwSJQED-TL2oIJbBU3avc"
  expect(isImpersonated(jwt))->toBe(false)

  // { sub: 'id-a', name: 'name-a', iat: 1678903107, exp: 4789303107, scope: { impersonationUser: { id: 'impersonation-user-id' } } }
  let jwt = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJpZC1hIiwibmFtZSI6Im5hbWUtYSIsInNjb3BlIjp7ImltcGVyc29uYXRpb25Vc2VyIjp7ImlkIjoiaW1wZXJzb25hdGlvbi11c2VyLWlkIn19LCJpYXQiOjE2Nzg5MDMzODEsImV4cCI6NDc4OTMwMzM4MX0.MS9MXXVVKFQQlFvJGkP98Uh5gN20sel4rs3JhrG5kkM"
  expect(isImpersonated(jwt))->toBe(true)
})

todo("get")
todo("set")
todo("remove")

test("storageItemKey shoud not change", () => {
  expect(storageItemKey)->toMatchSnapshot
})
