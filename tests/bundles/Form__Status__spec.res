open Vitest

test("isErrored", () => {
  let {isErrored} = module(Form__Status)

  expect(isErrored(Pristine))->toBe(false)
  expect(isErrored(Valid))->toBe(false)
  expect(isErrored(Errored))->toBe(true)
})

test("isErrored", () => {
  let {isValid} = module(Form__Status)

  expect(isValid(Pristine))->toBe(false)
  expect(isValid(Valid))->toBe(true)
  expect(isValid(Errored))->toBe(false)
})

test("isPristine", () => {
  let {isPristine} = module(Form__Status)

  expect(isPristine(Pristine))->toBe(true)
  expect(isPristine(Valid))->toBe(false)
  expect(isPristine(Errored))->toBe(false)
})
