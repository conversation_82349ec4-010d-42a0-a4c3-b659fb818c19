open Vitest

module Lenses = %lenses(
  type state = {
    fullName: string,
    number: int,
    float: float,
    email: string,
    password: string,
    phoneNumber: string,
    locations: array<string>,
  }
)

module Schema = Form__Schema.Make(Lenses)

let initialValues = {
  Lenses.fullName: "12345",
  number: 9,
  float: 9.,
  email: "<EMAIL>",
  password: "azertyUIOP^1",
  phoneNumber: "+33682082955",
  locations: ["13 Rue de Belzunce, Paris", "38 Rue Jean Mermoz, Paris"],
}

describe("validate", () => {
  let schema = [
    Schema.Password(Password),
    Email(Email),
    ArrayMin(Locations, 2),
    StringMin(FullName, 5),
  ]

  it("should return no errors", () => {
    let values = initialValues
    expect(Schema.validate(~schema, ~values))->toStrictEqual(Ok())
  })

  it("should return one error", () => {
    let values = {...initialValues, password: ""}
    let expected = Error([(<PERSON>hem<PERSON><PERSON>(Password), "Invalid password.")])
    expect(Schema.validate(~schema, ~values))->toStrictEqual(expected)
  })

  it("should return multiple errors", () => {
    let values = {
      ...initialValues,
      fullName: "123",
      email: "",
      password: "",
      locations: ["38 Rue Jean Mermoz, Paris"],
    }
    expect(Schema.validate(~schema, ~values))->toStrictEqual(
      Error([
        (Field(Password), "Invalid password."),
        (Field(Email), "Invalid email address."),
        (Field(Locations), "This value must contain at least 2 item(s)."),
        (Field(FullName), "This value must be at least 5 character(s)."),
      ]),
    )
  })
})

test("IntRange", () => {
  let schema = [Schema.IntRange(Number, None, Some(3))]
  let values = {...initialValues, number: 3}
  expect(Schema.validate(~schema, ~values))->toStrictEqual(Ok())

  let schema = [Schema.IntRange(Number, Some(2), None)]
  let values = {...initialValues, number: 2}
  expect(Schema.validate(~schema, ~values))->toStrictEqual(Ok())

  let schema = [Schema.IntRange(Number, Some(1), Some(3))]
  let values = {...initialValues, number: 2}
  expect(Schema.validate(~schema, ~values))->toStrictEqual(Ok())

  let schema = [Schema.IntRange(Number, None, Some(3))]
  let values = {...initialValues, number: 4}
  let expected = Error([(Schema.Field(Number), "This number must be inferior or equal to 3.")])
  expect(Schema.validate(~schema, ~values))->toStrictEqual(expected)

  let schema = [Schema.IntRange(Number, Some(2), None)]
  let values = {...initialValues, number: 1}
  let expected = Error([(Schema.Field(Number), "This number must be superior or equal to 2.")])
  expect(Schema.validate(~schema, ~values))->toStrictEqual(expected)

  let schema = [Schema.IntRange(Number, Some(1), Some(3))]
  let values = {...initialValues, number: 4}
  expect(Schema.validate(~schema, ~values))->toStrictEqual(
    Error([(Schema.Field(Number), "This number must range between 1 and 3 inclusive.")]),
  )
})

test("StringMin", () => {
  let schema = [Schema.StringMin(Email, 3)]

  let values = {...initialValues, email: "abc"}
  expect(Schema.validate(~schema, ~values))->toStrictEqual(Ok())

  let values = {...initialValues, email: "ab"}
  let expected = Error([(Schema.Field(Email), "This value must be at least 3 character(s).")])
  expect(Schema.validate(~schema, ~values))->toStrictEqual(expected)
})

test("NotEmpty", () => {
  let schema = [Schema.StringNotEmpty(Password)]

  let values = {...initialValues, password: "abc"}
  expect(Schema.validate(~schema, ~values))->toStrictEqual(Ok())

  let values = {...initialValues, password: ""}
  let expected = Error([(Schema.Field(Password), "Please fulfill this field.")])
  expect(Schema.validate(~schema, ~values))->toStrictEqual(expected)
})

test("PhoneNumber", () => {
  let schema = [Schema.PhoneNumber(PhoneNumber)]
  let expectedError = "This value is not a valid phone number."

  let values = {...initialValues, phoneNumber: "0149813743"}
  expect(Schema.validate(~schema, ~values))->toStrictEqual(Ok())

  let values = {...initialValues, phoneNumber: "01 49 81 37 43"}
  expect(Schema.validate(~schema, ~values))->toStrictEqual(Ok())

  let values = {...initialValues, phoneNumber: "+33649813743"}
  expect(Schema.validate(~schema, ~values))->toStrictEqual(Ok())

  let values = {...initialValues, phoneNumber: "+336 49 81 37 43"}
  expect(Schema.validate(~schema, ~values))->toStrictEqual(Ok())

  let values = {...initialValues, phoneNumber: "+3364981374"}
  let expected = Error([(Schema.Field(PhoneNumber), expectedError)])
  expect(Schema.validate(~schema, ~values))->toStrictEqual(expected)

  let values = {...initialValues, phoneNumber: "+22649813743"}
  let expected = Error([(Schema.Field(PhoneNumber), expectedError)])
  expect(Schema.validate(~schema, ~values))->toStrictEqual(expected)

  let values = {...initialValues, phoneNumber: "33649813743"}
  let expected = Error([(Schema.Field(PhoneNumber), expectedError)])
  expect(Schema.validate(~schema, ~values))->toStrictEqual(expected)

  let values = {...initialValues, phoneNumber: "+ 33649813743"}
  let expected = Error([(Schema.Field(PhoneNumber), expectedError)])
  expect(Schema.validate(~schema, ~values))->toStrictEqual(expected)

  let values = {...initialValues, phoneNumber: "0149813743a"}
  let expected = Error([(Schema.Field(PhoneNumber), expectedError)])
  expect(Schema.validate(~schema, ~values))->toStrictEqual(expected)

  let values = {...initialValues, phoneNumber: "01498137431"}
  let expected = Error([(Schema.Field(PhoneNumber), expectedError)])
  expect(Schema.validate(~schema, ~values))->toStrictEqual(expected)

  let values = {...initialValues, phoneNumber: ""}
  let expected = Error([(Schema.Field(PhoneNumber), expectedError)])
  expect(Schema.validate(~schema, ~values))->toStrictEqual(expected)
})

test("Email", () => {
  let schema = [Schema.Email(Email)]
  let expectedError = "Invalid email address."

  let values = {...initialValues, email: "<EMAIL>"}
  expect(Schema.validate(~schema, ~values))->toStrictEqual(Ok())

  let values = {...initialValues, email: "<EMAIL>"}
  expect(Schema.validate(~schema, ~values))->toStrictEqual(Ok())

  let values = {...initialValues, email: "a@a.f"}
  let expected = Error([(Schema.Field(Email), expectedError)])
  expect(Schema.validate(~schema, ~values))->toStrictEqual(expected)

  let values = {...initialValues, email: ""}
  let expected = Error([(Schema.Field(Email), expectedError)])
  expect(Schema.validate(~schema, ~values))->toStrictEqual(expected)
})

test("Password", () => {
  let schema = [Schema.Password(Password)]
  let expectedError = "Invalid password."

  let values = {...initialValues, password: "azertyUIOP^1"}
  expect(Schema.validate(~schema, ~values))->toStrictEqual(Ok())

  let values = {...initialValues, password: "azertyUIOP^"}
  let expected = Error([(Schema.Field(Password), expectedError)])
  expect(Schema.validate(~schema, ~values))->toStrictEqual(expected)

  let values = {...initialValues, password: "azertyuiop^1"}
  let expected = Error([(Schema.Field(Password), expectedError)])
  expect(Schema.validate(~schema, ~values))->toStrictEqual(expected)

  let values = {...initialValues, password: "AZERTYUIOP^1"}
  let expected = Error([(Schema.Field(Password), expectedError)])
  expect(Schema.validate(~schema, ~values))->toStrictEqual(expected)

  let values = {...initialValues, password: "azUIO^1"}
  let expected = Error([(Schema.Field(Password), expectedError)])
  expect(Schema.validate(~schema, ~values))->toStrictEqual(expected)

  let values = {...initialValues, password: ""}
  let expected = Error([(Schema.Field(Password), expectedError)])
  expect(Schema.validate(~schema, ~values))->toStrictEqual(expected)
})

test("CustomString", () => {
  let schema = [Schema.CustomString(Password, (_, _) => Ok())]
  let values = initialValues
  expect(Schema.validate(~schema, ~values))->toStrictEqual(Ok())

  let schema = [Schema.CustomString(Password, (_, _) => Error("Not valid"))]
  let values = initialValues
  let expected = Error([(Schema.Field(Password), "Not valid")])
  expect(Schema.validate(~schema, ~values))->toStrictEqual(expected)
})

test("Custom", () => {
  let schema = [Schema.Custom(Password, _ => Ok())]
  let values = initialValues
  expect(Schema.validate(~schema, ~values))->toStrictEqual(Ok())

  let schema = [Schema.Custom(Password, _ => Error("Not valid"))]
  let values = initialValues
  let expected = Error([(Schema.Field(Password), "Not valid")])
  expect(Schema.validate(~schema, ~values))->toStrictEqual(expected)
})

test("ArrayMin", () => {
  let schema = [Schema.ArrayMin(Locations, 1)]

  let values = {...initialValues, locations: ["hello"]}
  expect(Schema.validate(~schema, ~values))->toStrictEqual(Ok())

  let values = {...initialValues, locations: []}
  let expected = Error([(Schema.Field(Locations), "This value must contain at least 1 item(s).")])
  expect(Schema.validate(~schema, ~values))->toStrictEqual(expected)
})

test("FloatRange", () => {
  let schema = [Schema.FloatRange(Float, None, Some(3.))]
  let values = {...initialValues, float: 3.}
  expect(Schema.validate(~schema, ~values))->toStrictEqual(Ok())

  let schema = [Schema.FloatRange(Float, Some(2.), None)]
  let values = {...initialValues, float: 2.}
  expect(Schema.validate(~schema, ~values))->toStrictEqual(Ok())

  let schema = [Schema.FloatRange(Float, Some(1.), Some(3.))]
  let values = {...initialValues, float: 2.}
  expect(Schema.validate(~schema, ~values))->toStrictEqual(Ok())

  let schema = [Schema.FloatRange(Float, None, Some(3.))]
  let values = {...initialValues, float: 4.}
  let expected = Error([(Schema.Field(Float), "This number must be inferior or equal to 4.")])
  expect(Schema.validate(~schema, ~values))->toStrictEqual(expected)

  let schema = [Schema.FloatRange(Float, Some(2.), None)]
  let values = {...initialValues, float: 1.}
  let expected = Error([(Schema.Field(Float), "This number must be superior or equal to 1.")])
  expect(Schema.validate(~schema, ~values))->toStrictEqual(expected)

  let schema = [Schema.FloatRange(Float, Some(1.), Some(3.))]
  let values = {...initialValues, float: 4.}
  let expected = Error([(Schema.Field(Float), "This number must range between 1 and 3 inclusive.")])
  expect(Schema.validate(~schema, ~values))->toStrictEqual(expected)
})
