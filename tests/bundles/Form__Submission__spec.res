open Vitest

test("isRequested", () => {
  let {isRequested} = module(Form__Submission)

  expect(isRequested(Pending))->toBe(false)
  expect(isRequested(Requested))->toBe(true)
  expect(isRequested(Failed("")))->toBe(false)
  expect(isRequested(Succeeded(None)))->toBe(false)
  expect(isRequested(Succeeded(Some(""))))->toBe(false)
})

test("isSucceeded", () => {
  let {isSucceeded} = module(Form__Submission)

  expect(isSucceeded(Pending))->toBe(false)
  expect(isSucceeded(Requested))->toBe(false)
  expect(isSucceeded(Failed("")))->toBe(false)
  expect(isSucceeded(Succeeded(None)))->toBe(true)
  expect(isSucceeded(Succeeded(Some(""))))->toBe(true)
})
