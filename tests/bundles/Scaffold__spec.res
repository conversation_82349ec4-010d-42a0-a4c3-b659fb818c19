open Vitest
open TestingLibraryReact

test("makeTotalPages", () => {
  let {makeTotalPages} = module(Scaffold)

  expect(makeTotalPages(0, 8))->toBe(1)
  expect(makeTotalPages(-1, 1))->toBe(1)
  expect(makeTotalPages(1, -1))->toBe(1)
  expect(makeTotalPages(-1, 0))->toBe(1)
  expect(makeTotalPages(0, -1))->toBe(1)
  expect(makeTotalPages(0, 0))->toBe(1)
  expect(makeTotalPages(20, 8))->toBe(3)
  expect(makeTotalPages(103, 7))->toBe(15)
  expect(makeTotalPages(2, 1))->toBe(2)
  expect(makeTotalPages(1, 1))->toBe(1)
  expect(makeTotalPages(1, 2))->toBe(1)
  expect(makeTotalPages(2, 2))->toBe(1)
  expect(makeTotalPages(8, 20))->toBe(1)
})

module Query = %graphql(`
  query RandomQuery($before: String, $after: String, $first: Int, $last: Int, $filterBy: InputDevicesQueryFilter) {
    devices(before: $before, after: $after, first: $first, last: $last, filterBy: $filterBy) {
      pageInfo {
        startCursor
        endCursor
      }
      edges {
        node {
          id
          name
          slug
        }
      }
      totalCount
    }
  }
`)

module Filters = {
  type t = {
    name?: string,
    slug?: string,
  }

  let encoder = ({?name, ?slug}) => (name, slug)

  let decoder = ((name, slug)) => Ok({
    ?name,
    ?slug,
  })

  let useJsonCodec = () =>
    JsonCodec.object2(
      encoder,
      decoder,
      JsonCodec.field("name", JsonCodec.string)->JsonCodec.optional,
      JsonCodec.field("slug", JsonCodec.string)->JsonCodec.optional,
    )
}

module Row = {
  type t = {
    id: string,
    name: string,
    slug: string,
  }
}

describe("Make", () => {
  module Scaffolded = Scaffold.Make({
    type filters = Filters.t
    let useFiltersJsonCodec = Filters.useJsonCodec

    module QueryInner = Query.Query_inner
    type queryVariableFilterBy = Query.t_variables_InputDevicesQueryFilter
    let useQuery = Query.use

    let makeQueryVariables = (
      _defaultQueryVariables,
      ~connectionArguments,
      ~search=?,
      ~filterBy=?,
      (),
    ) => {
      search->ignore
      {
        QueryInner.first: connectionArguments.first,
        last: connectionArguments.last,
        before: connectionArguments.Scaffold.before,
        after: connectionArguments.after,
        filterBy,
      }
    }

    let makeQueryVariablesFilterBy = ({Filters.name: ?name}) =>
      Query.makeInputObjectInputDevicesQueryFilter(
        ~name=?switch name {
        | Some(name) => Some(Query.makeInputObjectStringEqualsFilter(~_equals=name, ()))
        | None => None
        },
        (),
      )

    let totalCountFromQueryData = ({Query.devices: devices}) => devices.totalCount
    let cursorsFromQueryData = ({Query.devices: devices}) => (
      devices.pageInfo.startCursor,
      devices.pageInfo.endCursor,
    )

    type row = Row.t
    let rowsFromQueryDataAndState = ({Query.devices: devices}, _) =>
      devices.edges->Array.map(({node}) => {Row.id: node.id, name: node.name, slug: node.slug})

    let keyExtractor = ({Row.id: id}) => id
  })

  test("makeInitialState", () => {
    let {makeInitialState} = module(Scaffolded)

    expect(makeInitialState(~filters={}))->toStrictEqual({
      currentPage: 1,
      previousPage: -1,
      searchQuery: None,
      filters: {},
      connectionArguments: {first: 10},
    })

    expect(makeInitialState(~filters={slug: "mock-slug"}))->toStrictEqual({
      currentPage: 1,
      previousPage: -1,
      searchQuery: None,
      filters: {slug: "mock-slug"},
      connectionArguments: {first: 10},
    })

    expect(makeInitialState(~filters={slug: "mock-slug", name: "mock-name"}))->toStrictEqual({
      currentPage: 1,
      previousPage: -1,
      searchQuery: None,
      filters: {slug: "mock-slug", name: "mock-name"},
      connectionArguments: {first: 10},
    })
  })

  test("use", () => {
    let history = History.createMemoryHistory()
    let wrapper = props => <Providers history> {props["children"]} </Providers>
    let testHook = hookCallback => renderHookWithOptions(hookCallback, ~options={wrapper: wrapper})
    let {use, makeInitialState} = module(Scaffolded)

    let hookResult = testHook(
      () => use(() => makeInitialState(~filters={slug: "mock-slug", name: "mock-name"})),
    )
    let (state, dispatch) = hookResult.result.current

    expect(state)->toStrictEqual({
      Scaffold.currentPage: 1,
      previousPage: -1,
      searchQuery: None,
      filters: {slug: "mock-slug", name: "mock-name"},
      connectionArguments: {first: 10},
    })
    expect(history.location.search)->toBe(
      "?page=1&previousPage=-1&filters[name]=%22mock-name%22&filters[slug]=%22mock-slug%22&first=10",
    )

    act(
      () =>
        Navigated({
          nextPage: 2,
          queryTotalCount: 99,
          queryCursors: (None, Some("mock-end-cursor")),
        })->dispatch,
    )

    let (state, _) = hookResult.result.current

    expect(state)->toStrictEqual({
      Scaffold.currentPage: 2,
      previousPage: 1,
      searchQuery: None,
      filters: {slug: "mock-slug", name: "mock-name"},
      connectionArguments: {first: 10, after: "mock-end-cursor"},
    })
    expect(history.location.search)->toBe(
      "?page=2&previousPage=1&filters[name]=%22mock-name%22&filters[slug]=%22mock-slug%22&first=10&after=%22mock-end-cursor%22",
    )

    act(() => Searched("my search query")->dispatch)

    let (state, _) = hookResult.result.current

    expect(state)->toStrictEqual({
      Scaffold.currentPage: 1,
      previousPage: -1,
      searchQuery: Some("my search query"),
      filters: {slug: "mock-slug", name: "mock-name"},
      connectionArguments: {first: 10},
    })
    expect(history.location.search)->toBe(
      "?page=1&previousPage=-1&search=%22my%20search%20query%22&filters[name]=%22mock-name%22&filters[slug]=%22mock-slug%22&first=10",
    )

    act(() => FiltersUpdated(prevFilters => {...prevFilters, name: "new-name"})->dispatch)

    let (state, _) = hookResult.result.current

    expect(state)->toStrictEqual({
      Scaffold.currentPage: 1,
      previousPage: -1,
      searchQuery: Some("my search query"),
      filters: {slug: "mock-slug", name: "new-name"},
      connectionArguments: {first: 10},
    })
    expect(history.location.search)->toBe(
      "?page=1&previousPage=-1&search=%22my%20search%20query%22&filters[name]=%22new-name%22&filters[slug]=%22mock-slug%22&first=10",
    )

    act(
      () =>
        Navigated({
          nextPage: 2,
          queryTotalCount: 99,
          queryCursors: (None, Some("mock-end-cursor")),
        })->dispatch,
    )

    let (state, _) = hookResult.result.current

    expect(state)->toStrictEqual({
      Scaffold.currentPage: 2,
      previousPage: 1,
      searchQuery: Some("my search query"),
      filters: {slug: "mock-slug", name: "new-name"},
      connectionArguments: {first: 10, after: "mock-end-cursor"},
    })
    expect(history.location.search)->toBe(
      "?page=2&previousPage=1&search=%22my%20search%20query%22&filters[name]=%22new-name%22&filters[slug]=%22mock-slug%22&first=10&after=%22mock-end-cursor%22",
    )

    act(() => FiltersUpdated(prevFilters => prevFilters)->dispatch)

    let (state, _) = hookResult.result.current

    expect(state)->toStrictEqual({
      Scaffold.currentPage: 1,
      previousPage: -1,
      searchQuery: Some("my search query"),
      filters: {slug: "mock-slug", name: "new-name"},
      connectionArguments: {first: 10},
    })
    expect(history.location.search)->toBe(
      "?page=1&previousPage=-1&search=%22my%20search%20query%22&filters[name]=%22new-name%22&filters[slug]=%22mock-slug%22&first=10",
    )

    act(() => Searched("my new search query")->dispatch)

    let (state, _) = hookResult.result.current

    expect(state)->toStrictEqual({
      Scaffold.currentPage: 1,
      previousPage: -1,
      searchQuery: Some("my new search query"),
      filters: {slug: "mock-slug", name: "new-name"},
      connectionArguments: {first: 10},
    })
    expect(history.location.search)->toBe(
      "?page=1&previousPage=-1&search=%22my%20new%20search%20query%22&filters[name]=%22new-name%22&filters[slug]=%22mock-slug%22&first=10",
    )

    act(
      () =>
        Navigated({
          nextPage: 8,
          queryTotalCount: 99,
          queryCursors: (None, Some("mock-end-cursor")),
        })->dispatch,
    )

    let (state, _) = hookResult.result.current

    expect(state)->toStrictEqual({
      Scaffold.currentPage: 1,
      previousPage: -1,
      searchQuery: Some("my new search query"),
      filters: {slug: "mock-slug", name: "new-name"},
      connectionArguments: {first: 10},
    })
    expect(history.location.search)->toBe(
      "?page=1&previousPage=-1&search=%22my%20new%20search%20query%22&filters[name]=%22new-name%22&filters[slug]=%22mock-slug%22&first=10",
    )

    act(
      () =>
        Navigated({
          nextPage: 1,
          queryTotalCount: 99,
          queryCursors: (Some("mock-start-cursor"), None),
        })->dispatch,
    )

    let (state, _) = hookResult.result.current

    expect(state)->toStrictEqual({
      Scaffold.currentPage: 1,
      previousPage: 1,
      searchQuery: Some("my new search query"),
      filters: {slug: "mock-slug", name: "new-name"},
      connectionArguments: {first: 10},
    })
    expect(history.location.search)->toBe(
      "?page=1&previousPage=1&search=%22my%20new%20search%20query%22&filters[name]=%22new-name%22&filters[slug]=%22mock-slug%22&first=10",
    )

    act(() => Searched("")->dispatch)

    let (state, _) = hookResult.result.current

    expect(state)->toStrictEqual({
      Scaffold.currentPage: 1,
      previousPage: -1,
      searchQuery: None,
      filters: {slug: "mock-slug", name: "new-name"},
      connectionArguments: {first: 10},
    })
    expect(history.location.search)->toBe(
      "?page=1&previousPage=-1&filters[name]=%22new-name%22&filters[slug]=%22mock-slug%22&first=10",
    )
  })

  let mockStateFilters = () => {Filters.slug: "mock-slug", name: "mock-name"}
  let mockState = (~filters=mockStateFilters(), ~currentPage=1, ~previousPage=-1, ()) => {
    Scaffold.currentPage,
    previousPage,
    searchQuery: None,
    filters,
    connectionArguments: {first: 10},
  }

  describe("makeNextPage", () => {
    let {makeNextPage} = module(Scaffolded)

    it(
      "should go Next",
      () => {
        let action = LegacyPagination.Next

        let nextPage = makeNextPage(~totalPages=0, ~state=mockState(~currentPage=0, ()), ~action)
        expect(nextPage)->toBeNone

        let nextPage = makeNextPage(~totalPages=0, ~state=mockState(~currentPage=1, ()), ~action)
        expect(nextPage)->toBeNone

        let nextPage = makeNextPage(~totalPages=1, ~state=mockState(~currentPage=0, ()), ~action)
        expect(nextPage)->toBeNone

        let nextPage = makeNextPage(~totalPages=0, ~state=mockState(~currentPage=1, ()), ~action)
        expect(nextPage)->toBeNone

        let nextPage = makeNextPage(~totalPages=1, ~state=mockState(~currentPage=1, ()), ~action)
        expect(nextPage)->toBeNone

        let nextPage = makeNextPage(~totalPages=2, ~state=mockState(~currentPage=1, ()), ~action)
        expect(nextPage)->toBe(Some(2))

        let nextPage = makeNextPage(~totalPages=2, ~state=mockState(~currentPage=1, ()), ~action)
        expect(nextPage)->toBe(Some(2))

        let nextPage = makeNextPage(~totalPages=10, ~state=mockState(~currentPage=1, ()), ~action)
        expect(nextPage)->toBe(Some(2))

        let nextPage = makeNextPage(~totalPages=2, ~state=mockState(~currentPage=3, ()), ~action)
        expect(nextPage)->toBe(Some(2))

        let nextPage = makeNextPage(~totalPages=10, ~state=mockState(~currentPage=10, ()), ~action)
        expect(nextPage)->toBeNone
      },
    )

    it(
      "should go Prev",
      () => {
        let action = LegacyPagination.Prev

        let nextPage = makeNextPage(~totalPages=0, ~state=mockState(~currentPage=0, ()), ~action)
        expect(nextPage)->toBeNone

        let nextPage = makeNextPage(~totalPages=0, ~state=mockState(~currentPage=1, ()), ~action)
        expect(nextPage)->toBeNone

        let nextPage = makeNextPage(~totalPages=1, ~state=mockState(~currentPage=0, ()), ~action)
        expect(nextPage)->toBeNone

        let nextPage = makeNextPage(~totalPages=1, ~state=mockState(~currentPage=1, ()), ~action)
        expect(nextPage)->toBeNone

        let nextPage = makeNextPage(~totalPages=1, ~state=mockState(~currentPage=2, ()), ~action)
        expect(nextPage)->toBe(Some(1))

        let nextPage = makeNextPage(~totalPages=2, ~state=mockState(~currentPage=2, ()), ~action)
        expect(nextPage)->toBe(Some(1))

        let nextPage = makeNextPage(~totalPages=90, ~state=mockState(~currentPage=2, ()), ~action)
        expect(nextPage)->toBe(Some(1))

        let nextPage = makeNextPage(~totalPages=90, ~state=mockState(~currentPage=100, ()), ~action)
        expect(nextPage)->toBe(Some(90))

        let nextPage = makeNextPage(~totalPages=90, ~state=mockState(~currentPage=92, ()), ~action)
        expect(nextPage)->toBe(Some(90))

        let nextPage = makeNextPage(~totalPages=90, ~state=mockState(~currentPage=91, ()), ~action)
        expect(nextPage)->toBe(Some(90))

        let nextPage = makeNextPage(~totalPages=90, ~state=mockState(~currentPage=90, ()), ~action)
        expect(nextPage)->toBe(Some(89))
      },
    )

    it(
      "should go First",
      () => {
        let action = LegacyPagination.First

        let nextPage = makeNextPage(~totalPages=1, ~state=mockState(~currentPage=1, ()), ~action)
        expect(nextPage)->toBeNone

        let nextPage = makeNextPage(~totalPages=10, ~state=mockState(~currentPage=1, ()), ~action)
        expect(nextPage)->toBe(Some(1))

        let nextPage = makeNextPage(~totalPages=8, ~state=mockState(~currentPage=4, ()), ~action)
        expect(nextPage)->toBe(Some(1))

        let nextPage = makeNextPage(~totalPages=8, ~state=mockState(~currentPage=8, ()), ~action)
        expect(nextPage)->toBe(Some(1))
      },
    )

    it(
      "should go Last",
      () => {
        let action = LegacyPagination.Last

        let nextPage = makeNextPage(~totalPages=1, ~state=mockState(~currentPage=1, ()), ~action)
        expect(nextPage)->toBeNone

        let nextPage = makeNextPage(~totalPages=10, ~state=mockState(~currentPage=1, ()), ~action)
        expect(nextPage)->toBe(Some(10))
      },
    )
  })

  test("makeConnectionArguments", () => {
    let {makeConnectionArguments} = module(Scaffolded)

    expect(
      makeConnectionArguments(
        ~currentPage=1,
        ~previousPage=-1,
        ~totalCount=0,
        ~cursors=(None, None),
      ),
    )->toStrictEqual(Some({first: 10}))

    expect(
      makeConnectionArguments(
        ~currentPage=1,
        ~previousPage=-1,
        ~totalCount=1,
        ~cursors=(None, None),
      ),
    )->toStrictEqual(Some({first: 10}))

    expect(
      makeConnectionArguments(
        ~currentPage=1,
        ~previousPage=-1,
        ~totalCount=9,
        ~cursors=(None, None),
      ),
    )->toStrictEqual(Some({first: 10}))

    expect(
      makeConnectionArguments(
        ~currentPage=1,
        ~previousPage=-1,
        ~totalCount=100,
        ~cursors=(None, None),
      ),
    )->toStrictEqual(Some({first: 10}))

    expect(
      makeConnectionArguments(
        ~currentPage=1,
        ~previousPage=-1,
        ~totalCount=0,
        ~cursors=(Some("mock-start-cursor"), Some("mock-end-cursor")),
      ),
    )->toStrictEqual(Some({first: 10}))

    expect(
      makeConnectionArguments(
        ~currentPage=2,
        ~previousPage=1,
        ~totalCount=1,
        ~cursors=(None, None),
      ),
    )->toStrictEqual(None)

    expect(
      makeConnectionArguments(
        ~currentPage=2,
        ~previousPage=1,
        ~totalCount=100,
        ~cursors=(None, None),
      ),
    )->toStrictEqual(None)

    expect(
      makeConnectionArguments(
        ~currentPage=2,
        ~previousPage=1,
        ~totalCount=100,
        ~cursors=(Some("mock-start-cursor"), Some("mock-end-cursor")),
      ),
    )->toStrictEqual(Some({first: 10, after: "mock-end-cursor"}))

    expect(
      makeConnectionArguments(
        ~currentPage=2,
        ~previousPage=1,
        ~totalCount=9,
        ~cursors=(Some("mock-start-cursor"), Some("mock-end-cursor")),
      ),
    )->toStrictEqual(Some({first: 10, after: "mock-end-cursor"}))

    expect(
      makeConnectionArguments(
        ~currentPage=2,
        ~previousPage=3,
        ~totalCount=50,
        ~cursors=(Some("mock-start-cursor"), Some("mock-end-cursor")),
      ),
    )->toStrictEqual(Some({last: 10, before: "mock-start-cursor"}))

    expect(
      makeConnectionArguments(
        ~currentPage=4,
        ~previousPage=5,
        ~totalCount=50,
        ~cursors=(Some("mock-start-cursor"), Some("mock-end-cursor")),
      ),
    )->toStrictEqual(Some({last: 10, before: "mock-start-cursor"}))

    expect(
      makeConnectionArguments(
        ~currentPage=2,
        ~previousPage=4,
        ~totalCount=50,
        ~cursors=(Some("mock-start-cursor"), Some("mock-end-cursor")),
      ),
    )->toStrictEqual(None)

    expect(
      makeConnectionArguments(
        ~currentPage=4,
        ~previousPage=1,
        ~totalCount=50,
        ~cursors=(Some("mock-start-cursor"), Some("mock-end-cursor")),
      ),
    )->toStrictEqual(None)

    expect(
      makeConnectionArguments(
        ~currentPage=5,
        ~previousPage=1,
        ~totalCount=50,
        ~cursors=(Some("mock-start-cursor"), Some("mock-end-cursor")),
      ),
    )->toStrictEqual(Some({last: 10}))
  })

  let testHook = (hookCallback, ~history) =>
    renderHookWithOptions(
      hookCallback,
      ~options={
        wrapper: props => <Providers history> {props["children"]} </Providers>,
      },
    )

  module Mock = {
    type state = {count: int}
    type action = Increment | Decrement

    let initialState = {count: 0}
    let reducer = (state, action) =>
      switch action {
      | Increment => {count: state.count + 1}
      | Decrement => {count: state.count - 1}
      }

    module Codec = {
      let encoder = ({count}) => count
      let decoder = count => Ok({count: count})

      let value = JsonCodec.object1(encoder, decoder, JsonCodec.field("count", JsonCodec.int))
    }
  }

  describe("useQueryStringPersistReducer", () => {
    let {useQueryStringPersistReducer} = module(Scaffold)

    it(
      "should set the url params and return the initial state",
      () => {
        let history = History.createMemoryHistory()

        expect(history.location.search)->toBe("")

        let (state, _) = testHook(
          () => useQueryStringPersistReducer(Mock.Codec.value, Mock.reducer, Mock.initialState),
          ~history,
        ).result.current

        expect(state)->toStrictEqual({count: 0})
        expect(history.location.search)->toBe("?count=0")
      },
    )

    it(
      "should persist state to the URL upon requested actions",
      () => {
        let history = History.createMemoryHistory()

        expect(history.location.search)->toBe("")

        let {result} = testHook(
          () => useQueryStringPersistReducer(Mock.Codec.value, Mock.reducer, Mock.initialState),
          ~history,
        )

        let (state, dispatch) = result.current
        expect(state)->toStrictEqual(Mock.initialState)
        expect(history.location.search)->toBe("?count=0")

        act(() => Increment->dispatch)

        let (state, dispatch) = result.current
        expect(state)->toStrictEqual({count: 1})
        expect(history.location.search)->toBe("?count=1")

        act(() => Increment->dispatch)

        let (state, _dispatch) = result.current
        expect(state)->toStrictEqual({count: 2})
        expect(history.location.search)->toBe("?count=2")

        act(() => Decrement->dispatch)

        let (state, _dispatch) = result.current
        expect(state)->toStrictEqual({count: 1})
        expect(history.location.search)->toBe("?count=1")
      },
    )

    it(
      "should override the initial state from the URL params",
      () => {
        let history = History.createMemoryHistory(~options={initialEntries: ["/?count=99"]}, ())

        expect(history.location.search)->toBe("?count=99")

        let (state, _dispatch) = testHook(
          () => useQueryStringPersistReducer(Mock.Codec.value, Mock.reducer, Mock.initialState),
          ~history,
        ).result.current

        expect(state)->toStrictEqual({count: 99})
        expect(history.location.search)->toBe("?count=99")
      },
    )

    it(
      "should keep the initial state when URL params are unrelated to the codec state",
      () => {
        let initialEntries = ["/?count=unrelated&other=99"]
        let history = History.createMemoryHistory(~options={initialEntries: initialEntries}, ())

        expect(history.location.search)->toBe("?count=unrelated&other=99")

        let (state, _dispatch) = testHook(
          () => useQueryStringPersistReducer(Mock.Codec.value, Mock.reducer, Mock.initialState),
          ~history,
        ).result.current

        expect(state)->toStrictEqual(Mock.initialState)
        expect(history.location.search)->toBe("?count=0")
      },
    )
  })

  // Some integration tests to ensure basic interactions
  // with the other components and GraphQL might be valuable
  // at some point.
  todo("Integration test")
})
