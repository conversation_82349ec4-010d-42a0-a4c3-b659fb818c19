open TestingLibraryReact
open Vitest

module Lenses = %lenses(
  type state = {
    fullName: string,
    email: string,
    password: string,
  }
)

let initialValues = {
  Lenses.fullName: "",
  email: "<EMAIL>",
  password: "0900",
}

module TestableForm = Form.Make(Lenses)

module TestableFormProviderWithUseFormPropState = {
  @react.component
  let make = (
    ~initialValues,
    ~schema,
    ~onChangeValues=?,
    ~onSubmitFailure=?,
    ~onSubmitSuccess=?,
  ) => {
    let onSubmit = (_, {Lenses.email: email, fullName, password}) =>
      Future.make(resolve => {
        let timeoutId = Js.Global.setTimeout(() =>
          if password === "1234" {
            resolve(Error("Dude, your password is too simple."))
          } else {
            resolve(Ok(Some(email ++ fullName ++ password)))
          }
        , 300)
        Some(() => Js.Global.clearTimeout(timeoutId))
      })

    let (formState, formDispatch) = TestableForm.useFormPropState({
      initialValues,
      schema,
      onSubmitFailure: ?onSubmitFailure->Option.map(fn),
      onSubmitSuccess: ?onSubmitSuccess->Option.map(fn),
    })

    React.useEffect1(() => {
      switch onChangeValues {
      | Some(onChangeValues) => onChangeValues->fn(formState.values)
      | None => ()
      }
      None
    }, [formState.values])

    <TestableForm.FormProvider propState={(formState, formDispatch)}>
      <TestableForm.InputText label="Full name" field=FullName autoTrim=false />
      <TestableForm.InputText label="Email" field=Email autoTrim=false />
      <TestableForm.InputText label="Password" field=Password autoTrim=false />
      <TestableForm.SubmitButton text="Submit" onSubmit />
    </TestableForm.FormProvider>
  }
}

describe("FormProvider with useFormPropState", () => {
  it("should render a form", () => {
    let _ = <TestableFormProviderWithUseFormPropState initialValues schema=[] />->render
    expect(screen->getByLabelTextExn("Full name"))->toBeVisible
    expect(screen->getByLabelTextExn("Email"))->toBeVisible
    expect(screen->getByLabelTextExn("Password"))->toBeVisible
  })

  it("should render a form with initial values", () => {
    let onChangeValues = fn1(ignore)

    let _ = <TestableFormProviderWithUseFormPropState
      initialValues={
        Lenses.fullName: "mock-full-name",
        email: "mock-email",
        password: "mock-password",
      }
      schema=[]
      onChangeValues
    />->render
    expect(screen->getByLabelTextExn("Full name"))->toBeVisible
    expect(screen->getByLabelTextExn("Email"))->toBeVisible
    expect(screen->getByLabelTextExn("Password"))->toBeVisible

    let emailInput = screen->getByLabelTextExn("Email")
    expect(emailInput)->toBeVisible
    let fullNameInput = screen->getByLabelTextExn("Full name")
    expect(fullNameInput)->toBeVisible
    let passwordInput = screen->getByLabelTextExn("Password")
    expect(passwordInput)->toBeVisible

    expect(emailInput)->toHaveValue("mock-email")
    expect(fullNameInput)->toHaveValue("mock-full-name")
    expect(passwordInput)->toHaveValue("mock-password")

    expect(onChangeValues)->toHaveBeenCalledTimes(1)
    expect(onChangeValues)->toHaveBeenCalledWith1({
      fullName: "mock-full-name",
      email: "mock-email",
      password: "mock-password",
    })
  })

  itPromise("should track changes and handle validation", async () => {
    let userEvent = TestingLibraryEvent.setup()
    let onChangeValues = fn1(ignore)
    let schemaA = [
      TestableForm.Schema.Custom(
        FullName,
        values =>
          switch values.fullName {
          | "" => Ok()
          | _ => Error("My custom error #1")
          },
      ),
      Email(Email),
    ]
    let {rerender} =
      <TestableFormProviderWithUseFormPropState
        initialValues schema=schemaA onChangeValues
      />->render

    let emailInput = screen->getByLabelTextExn("Email")
    expect(emailInput)->toBeVisible
    let fullNameInput = screen->getByLabelTextExn("Full name")
    expect(fullNameInput)->toBeVisible
    let passwordInput = screen->getByLabelTextExn("Password")
    expect(passwordInput)->toBeVisible

    expect(emailInput)->toHaveValue("<EMAIL>")
    expect(fullNameInput)->toHaveValue("")
    expect(passwordInput)->toHaveValue("0900")
    expect(screen->getByTextExn("Full name *"))->toBeVisible
    expect(screen->getByTextExn("Email *"))->toBeVisible
    expect(screen->getByTextExn("Password"))->toBeVisible
    expect(screen->queryByText("Password *"))->toBeNone
    expect(screen->queryByText("Invalid email address."))->toBeNone
    expect(screen->queryByText("My custom error #1"))->toBeNone
    expect(onChangeValues)->toHaveBeenCalledTimes(1)
    expect(onChangeValues)->toHaveBeenCalledWith1({
      fullName: "",
      email: "<EMAIL>",
      password: "0900",
    })
    onChangeValues->mockClear

    await userEvent->TestingLibraryEvent.click(emailInput)

    expect(emailInput)->toHaveValue("<EMAIL>")
    expect(fullNameInput)->toHaveValue("")
    expect(passwordInput)->toHaveValue("0900")
    expect(screen->getByTextExn("Full name *"))->toBeVisible
    expect(screen->getByTextExn("Email *"))->toBeVisible
    expect(screen->getByTextExn("Password"))->toBeVisible
    expect(screen->queryByText("Password *"))->toBeNone
    expect(screen->queryByText("Invalid email address."))->toBeNone
    expect(screen->queryByText("My custom error #1"))->toBeNone
    expect(onChangeValues)->toHaveBeenCalledTimes(0)

    await userEvent->TestingLibraryEvent.typeWithOptions(
      emailInput,
      "23",
      {initialSelectionStart: 0, initialSelectionEnd: 11},
    )

    expect(emailInput)->toHaveValue("23")
    expect(fullNameInput)->toHaveValue("")
    expect(passwordInput)->toHaveValue("0900")
    expect(screen->getByTextExn("Full name *"))->toBeVisible
    expect(screen->getByTextExn("Email *"))->toBeVisible
    expect(screen->getByTextExn("Password"))->toBeVisible
    expect(screen->queryByText("Password *"))->toBeNone
    expect(screen->queryByText("Invalid email address."))->toBeNone
    expect(screen->queryByText("My custom error #1"))->toBeNone
    expect(onChangeValues)->toHaveBeenCalledTimes(2)
    expect(onChangeValues)->toHaveBeenNthCalledWith1(
      1,
      {
        fullName: "",
        email: "2",
        password: "0900",
      },
    )
    expect(onChangeValues)->toHaveBeenNthCalledWith1(
      2,
      {
        fullName: "",
        email: "23",
        password: "0900",
      },
    )
    onChangeValues->mockClear

    await userEvent->TestingLibraryEvent.tab

    expect(emailInput)->toHaveValue("23")
    expect(fullNameInput)->toHaveValue("")
    expect(passwordInput)->toHaveValue("0900")
    expect(screen->getByTextExn("Full name *"))->toBeVisible
    expect(screen->getByTextExn("Email *"))->toBeVisible
    expect(screen->getByTextExn("Password"))->toBeVisible
    expect(screen->queryByText("Password *"))->toBeNone
    expect(screen->getByTextExn("Invalid email address."))->toBeVisible
    expect(screen->queryByText("My custom error #1"))->toBeNone
    expect(onChangeValues)->toHaveBeenCalledTimes(0)

    await userEvent->TestingLibraryEvent.tabShift

    expect(emailInput)->toHaveValue("23")
    expect(fullNameInput)->toHaveValue("")
    expect(passwordInput)->toHaveValue("0900")
    expect(screen->getByTextExn("Full name *"))->toBeVisible
    expect(screen->getByTextExn("Email *"))->toBeVisible
    expect(screen->getByTextExn("Password"))->toBeVisible
    expect(screen->queryByText("Password *"))->toBeNone
    expect(screen->getByTextExn("Invalid email address."))->toBeVisible
    expect(screen->queryByText("My custom error #1"))->toBeNone
    expect(onChangeValues)->toHaveBeenCalledTimes(0)

    await userEvent->TestingLibraryEvent.typeWithOptions(
      emailInput,
      "<EMAIL>",
      {initialSelectionStart: 0, initialSelectionEnd: 2},
    )

    expect(emailInput)->toHaveValue("<EMAIL>")
    expect(fullNameInput)->toHaveValue("")
    expect(passwordInput)->toHaveValue("0900")
    expect(screen->getByTextExn("Full name *"))->toBeVisible
    expect(screen->getByTextExn("Email *"))->toBeVisible
    expect(screen->getByTextExn("Password"))->toBeVisible
    expect(screen->queryByText("Password *"))->toBeNone
    expect(screen->queryByText("Invalid email address."))->toBeNone
    expect(screen->queryByText("My custom error #1"))->toBeNone
    expect(onChangeValues)->toHaveBeenCalledTimes(11)
    expect(onChangeValues)->toHaveBeenLastCalledWith1({
      fullName: "",
      email: "<EMAIL>",
      password: "0900",
    })
    onChangeValues->mockClear

    await userEvent->TestingLibraryEvent.typeWithOptions(
      emailInput,
      "leo@wino",
      {initialSelectionStart: 0, initialSelectionEnd: 11},
    )

    expect(emailInput)->toHaveValue("leo@wino")
    expect(fullNameInput)->toHaveValue("")
    expect(passwordInput)->toHaveValue("0900")
    expect(screen->getByTextExn("Full name *"))->toBeVisible
    expect(screen->getByTextExn("Email *"))->toBeVisible
    expect(screen->getByTextExn("Password"))->toBeVisible
    expect(screen->queryByText("Password *"))->toBeNone
    expect(screen->getByTextExn("Invalid email address."))->toBeVisible
    expect(screen->queryByText("My custom error #1"))->toBeNone
    expect(onChangeValues)->toHaveBeenCalledTimes(8)
    expect(onChangeValues)->toHaveBeenLastCalledWith1({
      fullName: "",
      email: "leo@wino",
      password: "0900",
    })
    onChangeValues->mockClear

    await userEvent->TestingLibraryEvent.tab

    expect(emailInput)->toHaveValue("leo@wino")
    expect(fullNameInput)->toHaveValue("")
    expect(passwordInput)->toHaveValue("0900")
    expect(screen->getByTextExn("Full name *"))->toBeVisible
    expect(screen->getByTextExn("Email *"))->toBeVisible
    expect(screen->getByTextExn("Password"))->toBeVisible
    expect(screen->queryByText("Password *"))->toBeNone
    expect(screen->getByTextExn("Invalid email address."))->toBeVisible
    expect(screen->queryByText("My custom error #1"))->toBeNone
    expect(onChangeValues)->toHaveBeenCalledTimes(0)

    await userEvent->TestingLibraryEvent.type_(fullNameInput, "leo")

    expect(emailInput)->toHaveValue("leo@wino")
    expect(fullNameInput)->toHaveValue("leo")
    expect(passwordInput)->toHaveValue("0900")
    expect(screen->getByTextExn("Full name *"))->toBeVisible
    expect(screen->getByTextExn("Email *"))->toBeVisible
    expect(screen->getByTextExn("Password"))->toBeVisible
    expect(screen->queryByText("Password *"))->toBeNone
    expect(screen->getByTextExn("Invalid email address."))->toBeVisible
    expect(screen->queryByText("My custom error #1"))->toBeNone
    expect(onChangeValues)->toHaveBeenCalledTimes(3)
    expect(onChangeValues)->toHaveBeenLastCalledWith1({
      fullName: "leo",
      email: "leo@wino",
      password: "0900",
    })
    onChangeValues->mockClear

    await userEvent->TestingLibraryEvent.tab

    expect(emailInput)->toHaveValue("leo@wino")
    expect(fullNameInput)->toHaveValue("leo")
    expect(passwordInput)->toHaveValue("0900")
    expect(screen->getByTextExn("Full name *"))->toBeVisible
    expect(screen->getByTextExn("Email *"))->toBeVisible
    expect(screen->getByTextExn("Password"))->toBeVisible
    expect(screen->queryByText("Password *"))->toBeNone
    expect(screen->getByTextExn("Invalid email address."))->toBeVisible
    expect(screen->getByTextExn("My custom error #1"))->toBeVisible
    expect(onChangeValues)->toHaveBeenCalledTimes(0)

    // NOTE - when a new schema is sent, the form must be validated again
    // however, we can't do this at the moment because of the OrderContainer module
    // which doesn't have a pure schema ...
    // > see more on Form__Core module (useContainerValue)

    let schemaB = [
      TestableForm.Schema.Custom(
        FullName,
        values =>
          switch values.password {
          | "0900" => Error("My custom error #2")
          | _ => Ok()
          },
      ),
      Email(Email),
    ]
    let _ =
      <TestableFormProviderWithUseFormPropState
        initialValues schema=schemaB onChangeValues
      />->rerender

    expect(emailInput)->toHaveValue("leo@wino")
    expect(fullNameInput)->toHaveValue("leo")
    expect(passwordInput)->toHaveValue("0900")
    expect(screen->getByTextExn("Full name *"))->toBeVisible
    expect(screen->getByTextExn("Email *"))->toBeVisible
    expect(screen->getByTextExn("Password"))->toBeVisible
    expect(screen->queryByText("Password *"))->toBeNone
    expect(screen->getByTextExn("Invalid email address."))->toBeVisible
    expect(screen->getByTextExn("My custom error #1"))->toBeVisible
    expect(screen->queryByText("My custom error #2"))->toBeNone
    expect(onChangeValues)->toHaveBeenCalledTimes(0)

    await userEvent->TestingLibraryEvent.click(fullNameInput)

    expect(emailInput)->toHaveValue("leo@wino")
    expect(fullNameInput)->toHaveValue("leo")
    expect(passwordInput)->toHaveValue("0900")
    expect(screen->getByTextExn("Full name *"))->toBeVisible
    expect(screen->getByTextExn("Email *"))->toBeVisible
    expect(screen->getByTextExn("Password"))->toBeVisible
    expect(screen->queryByText("Password *"))->toBeNone
    expect(screen->getByTextExn("Invalid email address."))->toBeVisible
    expect(screen->getByTextExn("My custom error #1"))->toBeVisible
    expect(screen->queryByText("My custom error #2"))->toBeNone
    expect(onChangeValues)->toHaveBeenCalledTimes(0)

    await userEvent->TestingLibraryEvent.tab

    expect(emailInput)->toHaveValue("leo@wino")
    expect(fullNameInput)->toHaveValue("leo")
    expect(passwordInput)->toHaveValue("0900")
    expect(screen->getByTextExn("Full name *"))->toBeVisible
    expect(screen->getByTextExn("Email *"))->toBeVisible
    expect(screen->getByTextExn("Password"))->toBeVisible
    expect(screen->queryByText("Password *"))->toBeNone
    expect(screen->getByTextExn("Invalid email address."))->toBeVisible
    expect(screen->getByTextExn("My custom error #1"))->toBeVisible
    expect(screen->queryByText("My custom error #2"))->toBeNone
    expect(onChangeValues)->toHaveBeenCalledTimes(0)

    await userEvent->TestingLibraryEvent.tab

    expect(emailInput)->toHaveValue("leo@wino")
    expect(fullNameInput)->toHaveValue("leo")
    expect(passwordInput)->toHaveValue("0900")
    expect(screen->getByTextExn("Full name *"))->toBeVisible
    expect(screen->getByTextExn("Email *"))->toBeVisible
    expect(screen->getByTextExn("Password"))->toBeVisible
    expect(screen->queryByText("Password *"))->toBeNone
    expect(screen->getByTextExn("Invalid email address."))->toBeVisible
    expect(screen->getByTextExn("My custom error #1"))->toBeVisible
    expect(screen->queryByText("My custom error #2"))->toBeNone
    expect(onChangeValues)->toHaveBeenCalledTimes(0)

    await userEvent->TestingLibraryEvent.typeWithOptions(
      passwordInput,
      "6023",
      {initialSelectionStart: 0, initialSelectionEnd: 4},
    )

    expect(emailInput)->toHaveValue("leo@wino")
    expect(fullNameInput)->toHaveValue("leo")
    expect(passwordInput)->toHaveValue("6023")
    expect(screen->getByTextExn("Full name *"))->toBeVisible
    expect(screen->getByTextExn("Email *"))->toBeVisible
    expect(screen->getByTextExn("Password"))->toBeVisible
    expect(screen->queryByText("Password *"))->toBeNone
    expect(screen->getByTextExn("Invalid email address."))->toBeVisible
    expect(screen->queryByText("My custom error #1"))->toBeNone
    expect(screen->queryByText("My custom error #2"))->toBeNone
    expect(onChangeValues)->toHaveBeenCalledTimes(4)
    expect(onChangeValues)->toHaveBeenLastCalledWith1({
      fullName: "leo",
      email: "leo@wino",
      password: "6023",
    })
    onChangeValues->mockClear

    await userEvent->TestingLibraryEvent.typeWithOptions(
      passwordInput,
      "0900",
      {initialSelectionStart: 0, initialSelectionEnd: 4},
    )

    expect(emailInput)->toHaveValue("leo@wino")
    expect(fullNameInput)->toHaveValue("leo")
    expect(passwordInput)->toHaveValue("0900")
    expect(screen->getByTextExn("Full name *"))->toBeVisible
    expect(screen->getByTextExn("Email *"))->toBeVisible
    expect(screen->getByTextExn("Password"))->toBeVisible
    expect(screen->queryByText("Password *"))->toBeNone
    expect(screen->getByTextExn("Invalid email address."))->toBeVisible
    expect(screen->queryByText("My custom error #1"))->toBeNone
    expect(screen->getByTextExn("My custom error #2"))->toBeVisible
    expect(onChangeValues)->toHaveBeenCalledTimes(4)
    expect(onChangeValues)->toHaveBeenLastCalledWith1({
      fullName: "leo",
      email: "leo@wino",
      password: "0900",
    })
    onChangeValues->mockClear

    await userEvent->TestingLibraryEvent.tabShift

    expect(emailInput)->toHaveValue("leo@wino")
    expect(fullNameInput)->toHaveValue("leo")
    expect(passwordInput)->toHaveValue("0900")
    expect(screen->getByTextExn("Full name *"))->toBeVisible
    expect(screen->getByTextExn("Email *"))->toBeVisible
    expect(screen->getByTextExn("Password"))->toBeVisible
    expect(screen->queryByText("Password *"))->toBeNone
    expect(screen->getByTextExn("Invalid email address."))->toBeVisible
    expect(screen->queryByText("My custom error #1"))->toBeNone
    expect(screen->getByTextExn("My custom error #2"))->toBeVisible
    expect(onChangeValues)->toHaveBeenCalledTimes(0)

    await userEvent->TestingLibraryEvent.tabShift

    expect(emailInput)->toHaveValue("leo@wino")
    expect(fullNameInput)->toHaveValue("leo")
    expect(passwordInput)->toHaveValue("0900")
    expect(screen->getByTextExn("Full name *"))->toBeVisible
    expect(screen->getByTextExn("Email *"))->toBeVisible
    expect(screen->getByTextExn("Password"))->toBeVisible
    expect(screen->queryByText("Password *"))->toBeNone
    expect(screen->getByTextExn("Invalid email address."))->toBeVisible
    expect(screen->queryByText("My custom error #1"))->toBeNone
    expect(screen->getByTextExn("My custom error #2"))->toBeVisible
    expect(onChangeValues)->toHaveBeenCalledTimes(0)
  })

  itPromise("should handle submission", async () => {
    let userEvent = TestingLibraryEvent.setup()

    let onChangeValues = fn1(ignore)
    let onSubmitSuccess = fn1(ignore)
    let onSubmitFailure = fn1(ignore)
    let schema = [TestableForm.Schema.StringMin(FullName, 2), Email(Email)]

    <TestableFormProviderWithUseFormPropState
      initialValues schema onChangeValues onSubmitFailure onSubmitSuccess
    />
    ->render
    ->ignore

    let emailInput = screen->getByLabelTextExn("Email")
    expect(emailInput)->toBeVisible
    let fullNameInput = screen->getByLabelTextExn("Full name")
    expect(fullNameInput)->toBeVisible
    let passwordInput = screen->getByLabelTextExn("Password")
    expect(passwordInput)->toBeVisible
    let submitButton = screen->getByRoleWithOptionsExn(#button, {name: "Submit"})
    expect(submitButton)->toBeVisible

    expect(emailInput)->toHaveValue("<EMAIL>")
    expect(fullNameInput)->toHaveValue("")
    expect(passwordInput)->toHaveValue("0900")
    expect(screen->getByTextExn("Full name *"))->toBeVisible
    expect(screen->getByTextExn("Email *"))->toBeVisible
    expect(screen->getByTextExn("Password"))->toBeVisible
    expect(screen->queryByText("Password *"))->toBeNone
    expect(screen->queryByText("Invalid email address."))->toBeNone
    expect(screen->queryByText("This value must be at least 2 character(s)."))->toBeNone
    expect(onChangeValues)->toHaveBeenCalledTimes(1)
    expect(onChangeValues)->toHaveBeenCalledWith1({
      fullName: "",
      email: "<EMAIL>",
      password: "0900",
    })
    onChangeValues->mockClear
    expect(onSubmitFailure)->toHaveBeenCalledTimes(0)
    expect(onSubmitSuccess)->toHaveBeenCalledTimes(0)

    await userEvent->TestingLibraryEvent.click(submitButton)

    expect(emailInput)->toHaveValue("<EMAIL>")
    expect(fullNameInput)->toHaveValue("")
    expect(passwordInput)->toHaveValue("0900")
    expect(screen->getByTextExn("Full name *"))->toBeVisible
    expect(screen->getByTextExn("Email *"))->toBeVisible
    expect(screen->getByTextExn("Password"))->toBeVisible
    expect(screen->queryByText("Password *"))->toBeNone
    expect(screen->queryByText("Invalid email address."))->toBeNone
    expect(screen->getByTextExn("This value must be at least 2 character(s)."))->toBeVisible
    expect(onChangeValues)->toHaveBeenCalledTimes(0)
    expect(onSubmitFailure)->toHaveBeenCalledTimes(1)
    expect(onSubmitFailure)->toHaveBeenCalledWith1(
      "There are some errors in the form, please correct them before trying to send it again.",
    )
    onSubmitFailure->mockClear
    expect(onSubmitSuccess)->toHaveBeenCalledTimes(0)

    await userEvent->TestingLibraryEvent.type_(fullNameInput, "Léo")

    expect(emailInput)->toHaveValue("<EMAIL>")
    expect(fullNameInput)->toHaveValue("Léo")
    expect(passwordInput)->toHaveValue("0900")
    expect(screen->getByTextExn("Full name *"))->toBeVisible
    expect(screen->getByTextExn("Email *"))->toBeVisible
    expect(screen->getByTextExn("Password"))->toBeVisible
    expect(screen->queryByText("Password *"))->toBeNone
    expect(screen->queryByText("Invalid email address."))->toBeNone
    expect(screen->queryByText("This value must be at least 2 character(s)."))->toBeNone
    expect(onChangeValues)->toHaveBeenCalledTimes(3)
    expect(onChangeValues)->toHaveBeenLastCalledWith1({
      fullName: "Léo",
      email: "<EMAIL>",
      password: "0900",
    })
    onChangeValues->mockClear
    expect(onSubmitFailure)->toHaveBeenCalledTimes(0)
    expect(onSubmitSuccess)->toHaveBeenCalledTimes(0)

    await userEvent->TestingLibraryEvent.click(submitButton)

    expect(emailInput)->toHaveValue("<EMAIL>")
    expect(fullNameInput)->toHaveValue("Léo")
    expect(passwordInput)->toHaveValue("0900")
    expect(screen->getByTextExn("Full name *"))->toBeVisible
    expect(screen->getByTextExn("Email *"))->toBeVisible
    expect(screen->getByTextExn("Password"))->toBeVisible
    expect(screen->queryByText("Password *"))->toBeNone
    expect(screen->queryByText("Invalid email address."))->toBeNone
    expect(screen->queryByText("This value must be at least 2 character(s)."))->toBeNone
    expect(onChangeValues)->toHaveBeenCalledTimes(0)
    expect(onSubmitFailure)->toHaveBeenCalledTimes(0)
    expect(onSubmitSuccess)->toHaveBeenCalledTimes(0)

    await waitFor(() => expect(onSubmitSuccess)->toHaveBeenCalledTimes(1))

    expect(emailInput)->toHaveValue("<EMAIL>")
    expect(fullNameInput)->toHaveValue("Léo")
    expect(passwordInput)->toHaveValue("0900")
    expect(screen->getByTextExn("Full name *"))->toBeVisible
    expect(screen->getByTextExn("Email *"))->toBeVisible
    expect(screen->getByTextExn("Password"))->toBeVisible
    expect(screen->queryByText("Password *"))->toBeNone
    expect(screen->queryByText("Invalid email address."))->toBeNone
    expect(screen->queryByText("This value must be at least 2 character(s)."))->toBeNone
    expect(onChangeValues)->toHaveBeenCalledTimes(0)
    expect(onSubmitFailure)->toHaveBeenCalledTimes(0)
    expect(onSubmitSuccess)->toHaveBeenCalledWith1(Some("************éo0900"))
    onSubmitSuccess->mockClear

    await userEvent->TestingLibraryEvent.typeWithOptions(
      passwordInput,
      "1234",
      {initialSelectionStart: 0, initialSelectionEnd: 4},
    )

    expect(emailInput)->toHaveValue("<EMAIL>")
    expect(fullNameInput)->toHaveValue("Léo")
    expect(passwordInput)->toHaveValue("1234")
    expect(screen->getByTextExn("Full name *"))->toBeVisible
    expect(screen->getByTextExn("Email *"))->toBeVisible
    expect(screen->getByTextExn("Password"))->toBeVisible
    expect(screen->queryByText("Password *"))->toBeNone
    expect(screen->queryByText("Invalid email address."))->toBeNone
    expect(screen->queryByText("This value must be at least 2 character(s)."))->toBeNone
    expect(onChangeValues)->toHaveBeenCalledTimes(4)
    expect(onChangeValues)->toHaveBeenLastCalledWith1({
      fullName: "Léo",
      email: "<EMAIL>",
      password: "1234",
    })
    onChangeValues->mockClear
    expect(onSubmitFailure)->toHaveBeenCalledTimes(0)
    expect(onSubmitSuccess)->toHaveBeenCalledTimes(0)

    await userEvent->TestingLibraryEvent.click(submitButton)

    expect(emailInput)->toHaveValue("<EMAIL>")
    expect(fullNameInput)->toHaveValue("Léo")
    expect(passwordInput)->toHaveValue("1234")
    expect(screen->getByTextExn("Full name *"))->toBeVisible
    expect(screen->getByTextExn("Email *"))->toBeVisible
    expect(screen->getByTextExn("Password"))->toBeVisible
    expect(screen->queryByText("Password *"))->toBeNone
    expect(screen->queryByText("Invalid email address."))->toBeNone
    expect(screen->queryByText("This value must be at least 2 character(s)."))->toBeNone
    expect(onChangeValues)->toHaveBeenCalledTimes(0)
    expect(onSubmitFailure)->toHaveBeenCalledTimes(0)
    expect(onSubmitSuccess)->toHaveBeenCalledTimes(0)

    await waitFor(() => expect(onSubmitFailure)->toHaveBeenCalledTimes(1))

    expect(emailInput)->toHaveValue("<EMAIL>")
    expect(fullNameInput)->toHaveValue("Léo")
    expect(passwordInput)->toHaveValue("1234")
    expect(screen->getByTextExn("Full name *"))->toBeVisible
    expect(screen->getByTextExn("Email *"))->toBeVisible
    expect(screen->getByTextExn("Password"))->toBeVisible
    expect(screen->queryByText("Password *"))->toBeNone
    expect(screen->queryByText("Invalid email address."))->toBeNone
    expect(screen->queryByText("This value must be at least 2 character(s)."))->toBeNone
    expect(onChangeValues)->toHaveBeenCalledTimes(0)
    expect(onSubmitSuccess)->toHaveBeenCalledTimes(0)
    expect(onSubmitFailure)->toHaveBeenCalledWith1("Dude, your password is too simple.")
    onSubmitFailure->mockClear
  })
})

todo("FormLegacyProvider")
