open Vitest

let {decode, parse, stringify} = module(Auth__Reducer)
let {mockUser, mockShop} = module(Auth__Mock)

let mockAuthState = (~shops, ~activeShop) => {
  Auth__Types.user: mockUser(),
  shops,
  activeShop,
}

todo("encodeUser")
todo("decodeUser")
todo("encodeShop")
todo("decodeShop")
todo("encode")

describe("decode", () => {
  it("should decode and preserve the activeShop reference correctly", () => {
    let auth = mockAuthState(
      ~activeShop=Some(mockShop(~id="shop-id-a", ~name="shop-name-a", ())),
      ~shops=[
        mockShop(~id="shop-id-a", ~name="shop-name-a", ()),
        mockShop(~id="shop-id-b", ~name="shop-name-b", ()),
      ],
    )
    let json = auth->Js.Json.stringifyAny->Option.getExn->Js.Json.parseExn
    let decoded = decode(json)

    expect(decoded)->toStrictEqual(Ok(auth))
    expect((decoded->Result.getExn).activeShop)->toBe((decoded->Result.getExn).shops[0])
  })
})

describe("parse", () => {
  it("should parse", () => {
    let json = `{
      "user": {
        "organizationName": "user-organizationName",
        "id": "user-id",
        "name": "user-name",
        "username": "user-username",
        "canUseImpersonation": true,
        "impersonating": true
      },
      "shops": [
         {
          "id": "shop-a-id",
          "name": "shop-a-name",
          "corporateName": "shop-a-corporate-name",
          "activeWebDeviceId": "shop-a-active-web-device-id",
          "address": "shop-a-address",
          "postalCode": "shop-a-postal-code",
          "city": "shop-a-city",
          "country": "shop-a-city",
          "phoneNumber": "shop-a-phone-number",
          "email": "shop-a-email",
          "legalRepresentative": "shop-a-legal-representative",
          "bankName": "shop-a-bankName",
          "cityOfRegistryOffice": "shop-a-cityOfRegistryOffice",
          "website": "shop-a-website",
          "fiscalYearEndClosingMonth": "shop-a-fiscalYearEndClosingMonth",
          "legalForm": "shop-a-legalForm",
          "amountOfShareCapital": "shop-a-amountOfShareCapital",
          "tvaNumber": "shop-a-tvaNumber",
          "siretNumber": "shop-a-siretNumber",
          "rcsNumber": "shop-a-rcsNumber",
          "apeNafCode": "shop-a-apeNafCode",
          "bankCode": "shop-a-bankCode",
          "bankAccountHolder": "shop-a-bankAccountHolder",
          "bankAccountNumber": "shop-a-bankAccountNumber",
          "bicCode": "shop-a-bicCode",
          "ibanNumber": "shop-a-ibanNumber"
        },
        {
          "id": "shop-b-id",
          "name": "shop-b-name",
          "corporateName": "shop-b-corporate-name",
          "activeWebDeviceId": "shop-b-active-web-device-id",
          "kind": "AFFILIATED",
          "address": "shop-b-address",
          "postalCode": "shop-b-postal-code",
          "city": "shop-b-city",
          "country": "shop-b-city",
          "phoneNumber": "shop-b-phone-number",
          "email": "shop-b-email",
          "legalRepresentative": "shop-b-legal-representative",
          "bankName": "shop-b-bankName",
          "cityOfRegistryOffice": "shop-b-cityOfRegistryOffice",
          "website": "shop-b-website",
          "fiscalYearEndClosingMonth": "shop-b-fiscalYearEndClosingMonth",
          "legalForm": "shop-b-legalForm",
          "amountOfShareCapital": "shop-b-amountOfShareCapital",
          "tvaNumber": "shop-b-tvaNumber",
          "siretNumber": "shop-b-siretNumber",
          "rcsNumber": "shop-b-rcsNumber",
          "apeNafCode": "shop-b-apeNafCode",
          "bankCode": "shop-b-bankCode",
          "bankAccountHolder": "shop-b-bankAccountHolder",
          "bankAccountNumber": "shop-b-bankAccountNumber",
          "bicCode": "shop-b-bicCode",
          "ibanNumber": "shop-b-ibanNumber"
        }
      ]
    }`
    expect(json->parse)->toStrictEqual(
      Ok({
        user: {
          organizationName: "user-organizationName",
          id: "user-id",
          name: "user-name",
          username: "user-username",
          profilePictureUri: None,
          canUseImpersonation: true,
          impersonating: true,
        },
        shops: [
          {
            id: "shop-a-id",
            name: "shop-a-name",
            corporateName: "shop-a-corporate-name",
            activeWebDeviceId: "shop-a-active-web-device-id",
            kind: #INDEPENDENT,
            address: "shop-a-address",
            postalCode: "shop-a-postal-code",
            city: "shop-a-city",
            country: "shop-a-city",
            phoneNumber: "shop-a-phone-number",
            email: "shop-a-email",
            logoUri: None,
            legalRepresentative: Some("shop-a-legal-representative"),
            bankName: Some("shop-a-bankName"),
            cityOfRegistryOffice: Some("shop-a-cityOfRegistryOffice"),
            website: Some("shop-a-website"),
            fiscalYearEndClosingMonth: Some("shop-a-fiscalYearEndClosingMonth"),
            legalForm: Some("shop-a-legalForm"),
            amountOfShareCapital: Some("shop-a-amountOfShareCapital"),
            tvaNumber: Some("shop-a-tvaNumber"),
            siretNumber: Some("shop-a-siretNumber"),
            rcsNumber: Some("shop-a-rcsNumber"),
            apeNafCode: Some("shop-a-apeNafCode"),
            bankCode: Some("shop-a-bankCode"),
            bankAccountHolder: Some("shop-a-bankAccountHolder"),
            bankAccountNumber: Some("shop-a-bankAccountNumber"),
            bicCode: Some("shop-a-bicCode"),
            ibanNumber: Some("shop-a-ibanNumber"),
          },
          {
            id: "shop-b-id",
            name: "shop-b-name",
            corporateName: "shop-b-corporate-name",
            activeWebDeviceId: "shop-b-active-web-device-id",
            kind: #AFFILIATED,
            address: "shop-b-address",
            postalCode: "shop-b-postal-code",
            city: "shop-b-city",
            country: "shop-b-city",
            phoneNumber: "shop-b-phone-number",
            email: "shop-b-email",
            logoUri: None,
            legalRepresentative: Some("shop-b-legal-representative"),
            bankName: Some("shop-b-bankName"),
            cityOfRegistryOffice: Some("shop-b-cityOfRegistryOffice"),
            website: Some("shop-b-website"),
            fiscalYearEndClosingMonth: Some("shop-b-fiscalYearEndClosingMonth"),
            legalForm: Some("shop-b-legalForm"),
            amountOfShareCapital: Some("shop-b-amountOfShareCapital"),
            tvaNumber: Some("shop-b-tvaNumber"),
            siretNumber: Some("shop-b-siretNumber"),
            rcsNumber: Some("shop-b-rcsNumber"),
            apeNafCode: Some("shop-b-apeNafCode"),
            bankCode: Some("shop-b-bankCode"),
            bankAccountHolder: Some("shop-b-bankAccountHolder"),
            bankAccountNumber: Some("shop-b-bankAccountNumber"),
            bicCode: Some("shop-b-bicCode"),
            ibanNumber: Some("shop-b-ibanNumber"),
          },
        ],
        activeShop: None,
      }),
    )

    let json = `{
      "user": {
        "organizationName": "user-organizationName",
        "id": "user-id",
        "name": "user-name",
        "username": "user-username",
        "profilePictureUri": "user-profile-picture-uri"
      },
      "shops": [
         {
          "name": "shop-a-name",
          "id": "shop-a-id",
          "corporateName": "shop-a-corporate-name",
          "activeWebDeviceId": "shop-a-active-web-device-id",
          "kind": "INTEGRATED",
          "address": "shop-a-address",
          "postalCode": "shop-a-postal-code",
          "city": "shop-a-city",
          "country": "shop-a-city",
          "phoneNumber": "shop-a-phone-number",
          "email": "shop-a-email",
          "legalRepresentative": "shop-a-legal-representative",
          "logoUri": "shop-a-profile-picture-uri",
          "bankName": "shop-a-bankName",
          "cityOfRegistryOffice": "shop-a-cityOfRegistryOffice",
          "website": "shop-a-website",
          "fiscalYearEndClosingMonth": "shop-a-fiscalYearEndClosingMonth",
          "legalForm": "shop-a-legalForm",
          "amountOfShareCapital": "shop-a-amountOfShareCapital",
          "tvaNumber": "shop-a-tvaNumber",
          "siretNumber": "shop-a-siretNumber",
          "rcsNumber": "shop-a-rcsNumber",
          "apeNafCode": "shop-a-apeNafCode",
          "bankCode": "shop-a-bankCode",
          "bankAccountHolder": "shop-a-bankAccountHolder",
          "bankAccountNumber": "shop-a-bankAccountNumber",
          "bicCode": "shop-a-bicCode",
          "ibanNumber": "shop-a-ibanNumber"
        },
        {
          "name": "shop-b-name",
          "id": "shop-b-id",
          "corporateName": "shop-b-corporate-name",
          "activeWebDeviceId": "shop-b-active-web-device-id",
          "kind": "AFFILIATED",
          "address": "shop-b-address",
          "postalCode": "shop-b-postal-code",
          "city": "shop-b-city",
          "country": "shop-b-city",
          "phoneNumber": "shop-b-phone-number",
          "email": "shop-b-email",
          "legalRepresentative": "shop-b-legal-representative",
          "bankName": "shop-b-bankName",
          "cityOfRegistryOffice": "shop-b-cityOfRegistryOffice",
          "website": "shop-b-website",
          "fiscalYearEndClosingMonth": "shop-b-fiscalYearEndClosingMonth",
          "legalForm": "shop-b-legalForm",
          "amountOfShareCapital": "shop-b-amountOfShareCapital",
          "tvaNumber": "shop-b-tvaNumber",
          "siretNumber": "shop-b-siretNumber",
          "rcsNumber": "shop-b-rcsNumber",
          "apeNafCode": "shop-b-apeNafCode",
          "bankCode": "shop-b-bankCode",
          "bankAccountHolder": "shop-b-bankAccountHolder",
          "bankAccountNumber": "shop-b-bankAccountNumber",
          "bicCode": "shop-b-bicCode",
          "ibanNumber": "shop-b-ibanNumber"
        }
      ],
      "activeShop": {
        "name": "shop-a-name",
          "id": "shop-a-id",
          "corporateName": "shop-a-corporate-name",
          "activeWebDeviceId": "shop-a-active-web-device-id",
          "kind": "INTEGRATED",
          "address": "shop-a-address",
          "postalCode": "shop-a-postal-code",
          "city": "shop-a-city",
          "country": "shop-a-city",
          "phoneNumber": "shop-a-phone-number",
          "email": "shop-a-email",
          "legalRepresentative": "shop-a-legal-representative",
          "logoUri": "shop-a-profile-picture-uri",
          "bankName": "shop-a-bankName",
          "cityOfRegistryOffice": "shop-a-cityOfRegistryOffice",
          "website": "shop-a-website",
          "fiscalYearEndClosingMonth": "shop-a-fiscalYearEndClosingMonth",
          "legalForm": "shop-a-legalForm",
          "amountOfShareCapital": "shop-a-amountOfShareCapital",
          "tvaNumber": "shop-a-tvaNumber",
          "siretNumber": "shop-a-siretNumber",
          "rcsNumber": "shop-a-rcsNumber",
          "apeNafCode": "shop-a-apeNafCode",
          "bankCode": "shop-a-bankCode",
          "bankAccountHolder": "shop-a-bankAccountHolder",
          "bankAccountNumber": "shop-a-bankAccountNumber",
          "bicCode": "shop-a-bicCode",
          "ibanNumber": "shop-a-ibanNumber"
      }
    }`
    expect(json->parse)->toStrictEqual(
      Ok({
        user: {
          organizationName: "user-organizationName",
          id: "user-id",
          name: "user-name",
          username: "user-username",
          profilePictureUri: Some("user-profile-picture-uri"),
          canUseImpersonation: false,
          impersonating: false,
        },
        shops: [
          {
            id: "shop-a-id",
            name: "shop-a-name",
            corporateName: "shop-a-corporate-name",
            activeWebDeviceId: "shop-a-active-web-device-id",
            kind: #INTEGRATED,
            address: "shop-a-address",
            postalCode: "shop-a-postal-code",
            city: "shop-a-city",
            country: "shop-a-city",
            phoneNumber: "shop-a-phone-number",
            email: "shop-a-email",
            logoUri: Some("shop-a-profile-picture-uri"),
            legalRepresentative: Some("shop-a-legal-representative"),
            bankName: Some("shop-a-bankName"),
            cityOfRegistryOffice: Some("shop-a-cityOfRegistryOffice"),
            website: Some("shop-a-website"),
            fiscalYearEndClosingMonth: Some("shop-a-fiscalYearEndClosingMonth"),
            legalForm: Some("shop-a-legalForm"),
            amountOfShareCapital: Some("shop-a-amountOfShareCapital"),
            tvaNumber: Some("shop-a-tvaNumber"),
            siretNumber: Some("shop-a-siretNumber"),
            rcsNumber: Some("shop-a-rcsNumber"),
            apeNafCode: Some("shop-a-apeNafCode"),
            bankCode: Some("shop-a-bankCode"),
            bankAccountHolder: Some("shop-a-bankAccountHolder"),
            bankAccountNumber: Some("shop-a-bankAccountNumber"),
            bicCode: Some("shop-a-bicCode"),
            ibanNumber: Some("shop-a-ibanNumber"),
          },
          {
            id: "shop-b-id",
            name: "shop-b-name",
            corporateName: "shop-b-corporate-name",
            activeWebDeviceId: "shop-b-active-web-device-id",
            kind: #AFFILIATED,
            address: "shop-b-address",
            postalCode: "shop-b-postal-code",
            city: "shop-b-city",
            country: "shop-b-city",
            phoneNumber: "shop-b-phone-number",
            email: "shop-b-email",
            logoUri: None,
            legalRepresentative: Some("shop-b-legal-representative"),
            bankName: Some("shop-b-bankName"),
            cityOfRegistryOffice: Some("shop-b-cityOfRegistryOffice"),
            website: Some("shop-b-website"),
            fiscalYearEndClosingMonth: Some("shop-b-fiscalYearEndClosingMonth"),
            legalForm: Some("shop-b-legalForm"),
            amountOfShareCapital: Some("shop-b-amountOfShareCapital"),
            tvaNumber: Some("shop-b-tvaNumber"),
            siretNumber: Some("shop-b-siretNumber"),
            rcsNumber: Some("shop-b-rcsNumber"),
            apeNafCode: Some("shop-b-apeNafCode"),
            bankCode: Some("shop-b-bankCode"),
            bankAccountHolder: Some("shop-b-bankAccountHolder"),
            bankAccountNumber: Some("shop-b-bankAccountNumber"),
            bicCode: Some("shop-b-bicCode"),
            ibanNumber: Some("shop-b-ibanNumber"),
          },
        ],
        activeShop: Some({
          id: "shop-a-id",
          name: "shop-a-name",
          corporateName: "shop-a-corporate-name",
          activeWebDeviceId: "shop-a-active-web-device-id",
          kind: #INTEGRATED,
          address: "shop-a-address",
          postalCode: "shop-a-postal-code",
          city: "shop-a-city",
          country: "shop-a-city",
          phoneNumber: "shop-a-phone-number",
          email: "shop-a-email",
          logoUri: Some("shop-a-profile-picture-uri"),
          legalRepresentative: Some("shop-a-legal-representative"),
          bankName: Some("shop-a-bankName"),
          cityOfRegistryOffice: Some("shop-a-cityOfRegistryOffice"),
          website: Some("shop-a-website"),
          fiscalYearEndClosingMonth: Some("shop-a-fiscalYearEndClosingMonth"),
          legalForm: Some("shop-a-legalForm"),
          amountOfShareCapital: Some("shop-a-amountOfShareCapital"),
          tvaNumber: Some("shop-a-tvaNumber"),
          siretNumber: Some("shop-a-siretNumber"),
          rcsNumber: Some("shop-a-rcsNumber"),
          apeNafCode: Some("shop-a-apeNafCode"),
          bankCode: Some("shop-a-bankCode"),
          bankAccountHolder: Some("shop-a-bankAccountHolder"),
          bankAccountNumber: Some("shop-a-bankAccountNumber"),
          bicCode: Some("shop-a-bicCode"),
          ibanNumber: Some("shop-a-ibanNumber"),
        }),
      }),
    )
  })

  it("shouldn't parse", () => {
    let json = `{}`
    expect(json->parse)->toStrictEqual(Error())
    let json = `{"user":"dummy"}`
    expect(json->parse)->toStrictEqual(Error())
    let json = `{"user":"dummy","shops":[]}`
    expect(json->parse)->toStrictEqual(Error())
    let json = `{"shops":[]}`
    expect(json->parse)->toStrictEqual(Error())
  })
})

describe("stringify", () => {
  it("should stringify", () => {
    let minify = json => json->Json.parseExn->Json.stringify

    let auth = {
      Auth__Types.user: {
        organizationName: "user-organizationName",
        id: "user-id",
        name: "user-name",
        username: "user-username",
        profilePictureUri: None,
        canUseImpersonation: false,
        impersonating: false,
      },
      shops: [
        {
          id: "shop-a-id",
          name: "shop-a-name",
          corporateName: "shop-a-corporate-name",
          activeWebDeviceId: "shop-a-active-web-device-id",
          kind: #INDEPENDENT,
          address: "shop-a-address",
          postalCode: "shop-a-postal-code",
          city: "shop-a-city",
          country: "shop-a-city",
          phoneNumber: "shop-a-phone-number",
          email: "shop-a-email",
          logoUri: None,
          legalRepresentative: Some("shop-a-legal-representative"),
          bankName: Some("shop-a-bankName"),
          cityOfRegistryOffice: Some("shop-a-cityOfRegistryOffice"),
          website: Some("shop-a-website"),
          fiscalYearEndClosingMonth: Some("shop-a-fiscalYearEndClosingMonth"),
          legalForm: Some("shop-a-legalForm"),
          amountOfShareCapital: Some("shop-a-amountOfShareCapital"),
          tvaNumber: Some("shop-a-tvaNumber"),
          siretNumber: Some("shop-a-siretNumber"),
          rcsNumber: Some("shop-a-rcsNumber"),
          apeNafCode: Some("shop-a-apeNafCode"),
          bankCode: Some("shop-a-bankCode"),
          bankAccountHolder: Some("shop-a-bankAccountHolder"),
          bankAccountNumber: Some("shop-a-bankAccountNumber"),
          bicCode: Some("shop-a-bicCode"),
          ibanNumber: Some("shop-a-ibanNumber"),
        },
        {
          id: "shop-b-id",
          name: "shop-b-name",
          corporateName: "shop-b-corporate-name",
          activeWebDeviceId: "shop-b-active-web-device-id",
          kind: #AFFILIATED,
          address: "shop-b-address",
          postalCode: "shop-b-postal-code",
          city: "shop-b-city",
          country: "shop-b-city",
          phoneNumber: "shop-b-phone-number",
          email: "shop-b-email",
          logoUri: None,
          legalRepresentative: Some("shop-b-legal-representative"),
          bankName: Some("shop-b-bankName"),
          cityOfRegistryOffice: Some("shop-b-cityOfRegistryOffice"),
          website: Some("shop-b-website"),
          fiscalYearEndClosingMonth: Some("shop-b-fiscalYearEndClosingMonth"),
          legalForm: Some("shop-b-legalForm"),
          amountOfShareCapital: Some("shop-b-amountOfShareCapital"),
          tvaNumber: Some("shop-b-tvaNumber"),
          siretNumber: Some("shop-b-siretNumber"),
          rcsNumber: Some("shop-b-rcsNumber"),
          apeNafCode: Some("shop-b-apeNafCode"),
          bankCode: Some("shop-b-bankCode"),
          bankAccountHolder: Some("shop-b-bankAccountHolder"),
          bankAccountNumber: Some("shop-b-bankAccountNumber"),
          bicCode: Some("shop-b-bicCode"),
          ibanNumber: Some("shop-b-ibanNumber"),
        },
      ],
      activeShop: None,
    }
    let json = `{
      "user": {
        "organizationName": "user-organizationName",
        "id": "user-id",
        "name": "user-name",
        "username": "user-username",
        "canUseImpersonation": false,
        "impersonating": false
      },
      "shops": [
         {
          "name": "shop-a-name",
          "id": "shop-a-id",
          "corporateName": "shop-a-corporate-name",
          "activeWebDeviceId": "shop-a-active-web-device-id",
          "kind": "INDEPENDENT",
          "address": "shop-a-address",
          "postalCode": "shop-a-postal-code",
          "city": "shop-a-city",
          "country": "shop-a-city",
          "phoneNumber": "shop-a-phone-number",
          "email": "shop-a-email",
          "legalRepresentative": "shop-a-legal-representative",
          "bankName": "shop-a-bankName",
          "cityOfRegistryOffice": "shop-a-cityOfRegistryOffice",
          "website": "shop-a-website",
          "fiscalYearEndClosingMonth": "shop-a-fiscalYearEndClosingMonth",
          "legalForm": "shop-a-legalForm",
          "amountOfShareCapital": "shop-a-amountOfShareCapital",
          "tvaNumber": "shop-a-tvaNumber",
          "siretNumber": "shop-a-siretNumber",
          "rcsNumber": "shop-a-rcsNumber",
          "apeNafCode": "shop-a-apeNafCode",
          "bankCode": "shop-a-bankCode",
          "bankAccountHolder": "shop-a-bankAccountHolder",
          "bankAccountNumber": "shop-a-bankAccountNumber",
          "bicCode": "shop-a-bicCode",
          "ibanNumber": "shop-a-ibanNumber"
        },
        {
          "name": "shop-b-name",
          "id": "shop-b-id",
          "corporateName": "shop-b-corporate-name",
          "activeWebDeviceId": "shop-b-active-web-device-id",
          "kind": "AFFILIATED",
          "address": "shop-b-address",
          "postalCode": "shop-b-postal-code",
          "city": "shop-b-city",
          "country": "shop-b-city",
          "phoneNumber": "shop-b-phone-number",
          "email": "shop-b-email",
          "legalRepresentative": "shop-b-legal-representative",
          "bankName": "shop-b-bankName",
          "cityOfRegistryOffice": "shop-b-cityOfRegistryOffice",
          "website": "shop-b-website",
          "fiscalYearEndClosingMonth": "shop-b-fiscalYearEndClosingMonth",
          "legalForm": "shop-b-legalForm",
          "amountOfShareCapital": "shop-b-amountOfShareCapital",
          "tvaNumber": "shop-b-tvaNumber",
          "siretNumber": "shop-b-siretNumber",
          "rcsNumber": "shop-b-rcsNumber",
          "apeNafCode": "shop-b-apeNafCode",
          "bankCode": "shop-b-bankCode",
          "bankAccountHolder": "shop-b-bankAccountHolder",
          "bankAccountNumber": "shop-b-bankAccountNumber",
          "bicCode": "shop-b-bicCode",
          "ibanNumber": "shop-b-ibanNumber"
        }
      ]
    }`
    expect(Logged(auth)->stringify)->toStrictEqual(Some(json->minify))

    let auth = {
      Auth__Types.user: {
        organizationName: "user-organizationName",
        id: "user-id",
        name: "user-name",
        username: "user-username",
        canUseImpersonation: true,
        impersonating: true,
        profilePictureUri: Some("user-profile-picture-uri"),
      },
      shops: [
        {
          id: "shop-a-id",
          name: "shop-a-name",
          corporateName: "shop-a-corporate-name",
          activeWebDeviceId: "shop-a-active-web-device-id",
          kind: #INTEGRATED,
          address: "shop-a-address",
          postalCode: "shop-a-postal-code",
          city: "shop-a-city",
          country: "shop-a-city",
          phoneNumber: "shop-a-phone-number",
          email: "shop-a-email",
          logoUri: Some("shop-a-profile-picture-uri"),
          legalRepresentative: Some("shop-a-legal-representative"),
          bankName: Some("shop-a-bankName"),
          cityOfRegistryOffice: Some("shop-a-cityOfRegistryOffice"),
          website: Some("shop-a-website"),
          fiscalYearEndClosingMonth: Some("shop-a-fiscalYearEndClosingMonth"),
          legalForm: Some("shop-a-legalForm"),
          amountOfShareCapital: Some("shop-a-amountOfShareCapital"),
          tvaNumber: Some("shop-a-tvaNumber"),
          siretNumber: Some("shop-a-siretNumber"),
          rcsNumber: Some("shop-a-rcsNumber"),
          apeNafCode: Some("shop-a-apeNafCode"),
          bankCode: Some("shop-a-bankCode"),
          bankAccountHolder: Some("shop-a-bankAccountHolder"),
          bankAccountNumber: Some("shop-a-bankAccountNumber"),
          bicCode: Some("shop-a-bicCode"),
          ibanNumber: Some("shop-a-ibanNumber"),
        },
        {
          id: "shop-b-id",
          name: "shop-b-name",
          corporateName: "shop-b-corporate-name",
          activeWebDeviceId: "shop-b-active-web-device-id",
          kind: #AFFILIATED,
          address: "shop-b-address",
          postalCode: "shop-b-postal-code",
          city: "shop-b-city",
          country: "shop-b-city",
          phoneNumber: "shop-b-phone-number",
          email: "shop-b-email",
          logoUri: None,
          legalRepresentative: Some("shop-b-legal-representative"),
          bankName: Some("shop-b-bankName"),
          cityOfRegistryOffice: Some("shop-b-cityOfRegistryOffice"),
          website: Some("shop-b-website"),
          fiscalYearEndClosingMonth: Some("shop-b-fiscalYearEndClosingMonth"),
          legalForm: Some("shop-b-legalForm"),
          amountOfShareCapital: Some("shop-b-amountOfShareCapital"),
          tvaNumber: Some("shop-b-tvaNumber"),
          siretNumber: Some("shop-b-siretNumber"),
          rcsNumber: Some("shop-b-rcsNumber"),
          apeNafCode: Some("shop-b-apeNafCode"),
          bankCode: Some("shop-b-bankCode"),
          bankAccountHolder: Some("shop-b-bankAccountHolder"),
          bankAccountNumber: Some("shop-b-bankAccountNumber"),
          bicCode: Some("shop-b-bicCode"),
          ibanNumber: Some("shop-b-ibanNumber"),
        },
      ],
      activeShop: Some({
        id: "shop-a-id",
        name: "shop-a-name",
        corporateName: "shop-a-corporate-name",
        activeWebDeviceId: "shop-a-active-web-device-id",
        kind: #INTEGRATED,
        address: "shop-a-address",
        postalCode: "shop-a-postal-code",
        city: "shop-a-city",
        country: "shop-a-city",
        phoneNumber: "shop-a-phone-number",
        email: "shop-a-email",
        logoUri: Some("shop-a-profile-picture-uri"),
        legalRepresentative: Some("shop-a-legal-representative"),
        bankName: Some("shop-a-bankName"),
        cityOfRegistryOffice: Some("shop-a-cityOfRegistryOffice"),
        website: Some("shop-a-website"),
        fiscalYearEndClosingMonth: Some("shop-a-fiscalYearEndClosingMonth"),
        legalForm: Some("shop-a-legalForm"),
        amountOfShareCapital: Some("shop-a-amountOfShareCapital"),
        tvaNumber: Some("shop-a-tvaNumber"),
        siretNumber: Some("shop-a-siretNumber"),
        rcsNumber: Some("shop-a-rcsNumber"),
        apeNafCode: Some("shop-a-apeNafCode"),
        bankCode: Some("shop-a-bankCode"),
        bankAccountHolder: Some("shop-a-bankAccountHolder"),
        bankAccountNumber: Some("shop-a-bankAccountNumber"),
        bicCode: Some("shop-a-bicCode"),
        ibanNumber: Some("shop-a-ibanNumber"),
      }),
    }

    let json = `{
      "user": {
        "organizationName": "user-organizationName",
        "id": "user-id",
        "name": "user-name",
        "username": "user-username",
        "canUseImpersonation": true,
        "impersonating": true,
        "profilePictureUri": "user-profile-picture-uri"
      },
      "shops": [
         {
          "name": "shop-a-name",
          "id": "shop-a-id",
          "corporateName": "shop-a-corporate-name",
          "activeWebDeviceId": "shop-a-active-web-device-id",
          "kind": "INTEGRATED",
          "address": "shop-a-address",
          "postalCode": "shop-a-postal-code",
          "city": "shop-a-city",
          "country": "shop-a-city",
          "phoneNumber": "shop-a-phone-number",
          "email": "shop-a-email",
          "legalRepresentative": "shop-a-legal-representative",
          "bankName": "shop-a-bankName",
          "cityOfRegistryOffice": "shop-a-cityOfRegistryOffice",
          "website": "shop-a-website",
          "fiscalYearEndClosingMonth": "shop-a-fiscalYearEndClosingMonth",
          "legalForm": "shop-a-legalForm",
          "amountOfShareCapital": "shop-a-amountOfShareCapital",
          "tvaNumber": "shop-a-tvaNumber",
          "siretNumber": "shop-a-siretNumber",
          "rcsNumber": "shop-a-rcsNumber",
          "apeNafCode": "shop-a-apeNafCode",
          "bankCode": "shop-a-bankCode",
          "bankAccountHolder": "shop-a-bankAccountHolder",
          "bankAccountNumber": "shop-a-bankAccountNumber",
          "bicCode": "shop-a-bicCode",
          "ibanNumber": "shop-a-ibanNumber",
          "logoUri": "shop-a-profile-picture-uri"
        },
        {
          "name": "shop-b-name",
          "id": "shop-b-id",
          "corporateName": "shop-b-corporate-name",
          "activeWebDeviceId": "shop-b-active-web-device-id",
          "kind": "AFFILIATED",
          "address": "shop-b-address",
          "postalCode": "shop-b-postal-code",
          "city": "shop-b-city",
          "country": "shop-b-city",
          "phoneNumber": "shop-b-phone-number",
          "email": "shop-b-email",
          "legalRepresentative": "shop-b-legal-representative",
          "bankName": "shop-b-bankName",
          "cityOfRegistryOffice": "shop-b-cityOfRegistryOffice",
          "website": "shop-b-website",
          "fiscalYearEndClosingMonth": "shop-b-fiscalYearEndClosingMonth",
          "legalForm": "shop-b-legalForm",
          "amountOfShareCapital": "shop-b-amountOfShareCapital",
          "tvaNumber": "shop-b-tvaNumber",
          "siretNumber": "shop-b-siretNumber",
          "rcsNumber": "shop-b-rcsNumber",
          "apeNafCode": "shop-b-apeNafCode",
          "bankCode": "shop-b-bankCode",
          "bankAccountHolder": "shop-b-bankAccountHolder",
          "bankAccountNumber": "shop-b-bankAccountNumber",
          "bicCode": "shop-b-bicCode",
          "ibanNumber": "shop-b-ibanNumber"
        }
      ],
      "activeShop": {
        "name": "shop-a-name",
        "id": "shop-a-id",
        "corporateName": "shop-a-corporate-name",
        "activeWebDeviceId": "shop-a-active-web-device-id",
        "kind": "INTEGRATED",
        "address": "shop-a-address",
        "postalCode": "shop-a-postal-code",
        "city": "shop-a-city",
        "country": "shop-a-city",
        "phoneNumber": "shop-a-phone-number",
        "email": "shop-a-email",
        "legalRepresentative": "shop-a-legal-representative",
        "bankName": "shop-a-bankName",
        "cityOfRegistryOffice": "shop-a-cityOfRegistryOffice",
        "website": "shop-a-website",
        "fiscalYearEndClosingMonth": "shop-a-fiscalYearEndClosingMonth",
        "legalForm": "shop-a-legalForm",
        "amountOfShareCapital": "shop-a-amountOfShareCapital",
        "tvaNumber": "shop-a-tvaNumber",
        "siretNumber": "shop-a-siretNumber",
        "rcsNumber": "shop-a-rcsNumber",
        "apeNafCode": "shop-a-apeNafCode",
        "bankCode": "shop-a-bankCode",
        "bankAccountHolder": "shop-a-bankAccountHolder",
        "bankAccountNumber": "shop-a-bankAccountNumber",
        "bicCode": "shop-a-bicCode",
        "ibanNumber": "shop-a-ibanNumber",
        "logoUri": "shop-a-profile-picture-uri"
      }
    }`

    expect(Logged(auth)->stringify)->toStrictEqual(Some(json->minify))

    let json = Unlogged->stringify
    expect(json)->toStrictEqual(None)

    let json = Logging({jwt: "jwt-mock"})->stringify
    expect(json)->toStrictEqual(None)
  })
})

todo("make")
