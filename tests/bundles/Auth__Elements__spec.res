open Vitest
open TestingLibraryReact

describe("truncateWithMiddleEllipsis", () => {
  let {truncateWithMiddleEllipsis} = module(Auth__Elements)

  it("should truncates string with middle ellipsis and trim whitespaces around", () => {
    let input = "This is a very long string that needs truncating"
    let maxLen = 20
    let expectedOutput = "This is...uncating"
    let result = truncateWithMiddleEllipsis(input, ~maxLen)
    expect(result)->toBe(expectedOutput)

    let input = "This is a very long string that needs"
    let maxLen = 16
    let expectedOutput = "This i...needs"
    let result = truncateWithMiddleEllipsis(input, ~maxLen)
    expect(result)->toBe(expectedOutput)
  })

  it("should returns the same string if length is equal to maxLen", () => {
    let input = "short string"
    let maxLen = 12
    let expectedOutput = "short string"
    let result = truncateWithMiddleEllipsis(input, ~maxLen)
    expect(result)->toBe(expectedOutput)
  })

  it("should returns the same string if length is less than maxLen", () => {
    let input = "tiny"
    let maxLen = 10
    let expectedOutput = "tiny"
    let result = truncateWithMiddleEllipsis(input, ~maxLen)
    expect(result)->toBe(expectedOutput)
  })

  it("should handles edge case where maxLen is less than the length of ellipsis", () => {
    let input = "edge case string"
    let maxLen = 2
    let expectedOutput = "..."
    let result = truncateWithMiddleEllipsis(input, ~maxLen)
    expect(result)->toBe(expectedOutput)
  })

  it("should handles edge case where maxLen equals to 0", () => {
    let input = "zero length string"
    let maxLen = 0
    let expectedOutput = "zero length strin..."
    let result = truncateWithMiddleEllipsis(input, ~maxLen)
    expect(result)->toBe(expectedOutput)
  })
})

let {mockUser, mockShop} = module(Auth__Mock)

let mockAuthState = (~shops, ~activeShop) => {
  Auth__Types.user: mockUser(),
  shops,
  activeShop,
}

let userEvent = TestingLibraryEvent.setup()

describe("SelectShopFilter", () => {
  module TestableSelectShopFilter = {
    @react.component
    let make = (~value=?, ~disabledIds=[], ~onChange, ~spyActiveShop) => {
      let activeShop = Auth.useActiveShop()

      ReactUpdateEffect.use1(() => {
        spyActiveShop(activeShop)
        None
      }, [activeShop])

      <Auth.SelectShopFilter ?value disabledIds onChange />
    }
  }

  itPromise("should trigger onChange and change activeShop", async () => {
    let shopA = mockShop(~id="shop-id-a", ~name="shop-name-a", ())
    let shopB = mockShop(~id="shop-id-b", ~name="shop-name-b", ())
    let shopC = mockShop(~id="shop-id-c", ~name="shop-name-c", ())
    let auth = mockAuthState(~activeShop=Some(shopA), ~shops=[shopA, shopB, shopC])

    let spyActiveShop = fn1(ignore)
    let onChange = fn1(ignore)

    let {rerender} = render(
      <Providers auth=Logged(auth)>
        <TestableSelectShopFilter onChange={onChange->fn} spyActiveShop={spyActiveShop->fn} />
      </Providers>,
    )

    expect(onChange)->toHaveBeenCalledTimes(0)
    expect(spyActiveShop)->toHaveBeenCalledTimes(0)

    let trigger = screen->getByRoleExn(#button)

    expect(trigger)->toHaveTextContent("shop-name-a")

    await userEvent->TestingLibraryEvent.click(trigger)

    let option = screen->getByRoleWithOptionsExn(#option, {name: "shop-name-b"})

    await userEvent->TestingLibraryEvent.click(option)

    expect(onChange)->toHaveBeenCalledTimes(1)
    expect(onChange)->toHaveBeenCalledWith1(Some(shopB))
    expect(spyActiveShop)->toHaveBeenCalledTimes(1)
    expect(spyActiveShop)->toHaveBeenCalledWith1(Some(shopB))

    let trigger = screen->getByRoleExn(#button)

    expect(trigger)->toHaveTextContent("shop-name-b")
    expect(trigger)->toBeVisible

    rerender(
      <Providers auth=Logged(auth)>
        <TestableSelectShopFilter
          value=shopC onChange={onChange->fn} spyActiveShop={spyActiveShop->fn}
        />
      </Providers>,
    )->ignore

    let trigger = screen->getByRoleExn(#button)

    expect(trigger)->toHaveTextContent("shop-name-c")
    expect(trigger)->toBeVisible
  })

  itPromise("should disable some options based on params", async () => {
    let shopA = mockShop(~id="shop-id-a", ~name="shop-name-a", ())
    let shopB = mockShop(~id="shop-id-b", ~name="shop-name-b", ())
    let shopC = mockShop(~id="shop-id-c", ~name="shop-name-c", ())
    let auth = mockAuthState(~activeShop=Some(shopA), ~shops=[shopA, shopB, shopC])

    let spyActiveShop = fn1(ignore)
    let onChange = fn1(ignore)

    render(
      <Providers auth=Logged(auth)>
        <TestableSelectShopFilter
          disabledIds=[shopC.id] onChange={onChange->fn} spyActiveShop={spyActiveShop->fn}
        />
      </Providers>,
    )->ignore

    expect(onChange)->toHaveBeenCalledTimes(0)
    expect(spyActiveShop)->toHaveBeenCalledTimes(0)

    let trigger = screen->getByRoleExn(#button)

    expect(trigger)->toHaveTextContent("shop-name-a")

    await userEvent->TestingLibraryEvent.click(trigger)

    let disabledOption = screen->getByRoleWithOptionsExn(#option, {name: "shop-name-c"})

    expect(disabledOption)->toHaveAttributeValue("aria-disabled", "true")

    await userEvent->TestingLibraryEvent.click(disabledOption)

    expect(onChange)->toHaveBeenCalledTimes(0)
    expect(spyActiveShop)->toHaveBeenCalledTimes(0)
  })

  itPromise("should be able to select `All` option", async () => {
    let shopA = mockShop(~id="shop-id-a", ~name="shop-name-a", ())
    let shopB = mockShop(~id="shop-id-b", ~name="shop-name-b", ())
    let shopC = mockShop(~id="shop-id-c", ~name="shop-name-c", ())
    let auth = mockAuthState(~activeShop=Some(shopA), ~shops=[shopA, shopB, shopC])

    let spyActiveShop = fn1(ignore)
    let onChange = fn1(ignore)

    render(
      <Providers auth=Logged(auth)>
        <TestableSelectShopFilter onChange={onChange->fn} spyActiveShop={spyActiveShop->fn} />
      </Providers>,
    )->ignore

    expect(onChange)->toHaveBeenCalledTimes(0)
    expect(spyActiveShop)->toHaveBeenCalledTimes(0)

    let trigger = screen->getByRoleExn(#button)

    expect(trigger)->toHaveTextContent("shop-name-a")

    await userEvent->TestingLibraryEvent.click(trigger)

    let option = screen->getByRoleWithOptionsExn(#option, {name: "All"})

    await userEvent->TestingLibraryEvent.click(option)

    expect(onChange)->toHaveBeenCalledTimes(1)
    expect(spyActiveShop)->toHaveBeenCalledTimes(1)
    expect(spyActiveShop)->toHaveBeenCalledWith1(None)

    let trigger = screen->getByRoleExn(#button)

    expect(trigger)->toHaveTextContent("All")
    expect(trigger)->toBeVisible
  })
})

describe("SelectSingleShopFilter", () => {
  module TestableSelectSingleShopFilter = {
    @react.component
    let make = (~defaultValue, ~disabledIds=[], ~onChange, ~spyActiveShop) => {
      let (value, setValue) = React.useState(() => defaultValue)
      let activeShop = Auth.useActiveShop()

      ReactUpdateEffect.use1(() => {
        spyActiveShop(activeShop)
        None
      }, [activeShop])

      let onChange = value => {
        onChange(value)
        setValue(_ => value)
      }

      <Auth.SelectSingleShopFilter value disabledIds onChange />
    }
  }

  itPromise("should trigger onChange and change activeShop", async () => {
    let shopA = mockShop(~id="shop-id-a", ~name="shop-name-a", ())
    let shopB = mockShop(~id="shop-id-b", ~name="shop-name-b", ())
    let auth = mockAuthState(~activeShop=Some(shopA), ~shops=[shopA, shopB])

    let defaultValue = shopA
    let spyActiveShop = fn1(ignore)
    let onChange = fn1(ignore)

    render(
      <Providers auth=Logged(auth)>
        <TestableSelectSingleShopFilter
          defaultValue onChange={onChange->fn} spyActiveShop={spyActiveShop->fn}
        />
      </Providers>,
    )->ignore

    expect(onChange)->toHaveBeenCalledTimes(0)
    expect(spyActiveShop)->toHaveBeenCalledTimes(0)

    let trigger = screen->getByRoleExn(#button)

    expect(trigger)->toHaveTextContent("shop-name-a")

    await userEvent->TestingLibraryEvent.click(trigger)

    expect(onChange)->toHaveBeenCalledTimes(0)
    expect(spyActiveShop)->toHaveBeenCalledTimes(0)

    let option = screen->getByRoleWithOptionsExn(#option, {name: "shop-name-b"})

    await userEvent->TestingLibraryEvent.click(option)

    expect(onChange)->toHaveBeenCalledTimes(1)
    expect(onChange)->toHaveBeenCalledWith1(shopB)
    expect(spyActiveShop)->toHaveBeenCalledTimes(1)
    expect(spyActiveShop)->toHaveBeenCalledWith1(Some(shopB))

    let trigger = screen->getByRoleExn(#button)

    expect(trigger)->toHaveTextContent("shop-name-b")
    expect(trigger)->toBeVisible
  })

  itPromise("should disable some options based on params", async () => {
    let shopA = mockShop(~id="shop-id-a", ~name="shop-name-a", ())
    let shopB = mockShop(~id="shop-id-b", ~name="shop-name-b", ())
    let shopC = mockShop(~id="shop-id-c", ~name="shop-name-c", ())
    let auth = mockAuthState(~activeShop=Some(shopA), ~shops=[shopA, shopB, shopC])

    let defaultValue = shopA
    let spyActiveShop = fn1(ignore)
    let onChange = fn1(ignore)

    render(
      <Providers auth=Logged(auth)>
        <TestableSelectSingleShopFilter
          defaultValue
          disabledIds=[shopC.id]
          onChange={onChange->fn}
          spyActiveShop={spyActiveShop->fn}
        />
      </Providers>,
    )->ignore

    expect(onChange)->toHaveBeenCalledTimes(0)
    expect(spyActiveShop)->toHaveBeenCalledTimes(0)

    let trigger = screen->getByRoleExn(#button)

    expect(trigger)->toHaveTextContent("shop-name-a")

    await userEvent->TestingLibraryEvent.click(trigger)

    let disabledOption = screen->getByRoleWithOptionsExn(#option, {name: "shop-name-c"})

    expect(disabledOption)->toHaveAttributeValue("aria-disabled", "true")

    await userEvent->TestingLibraryEvent.click(disabledOption)

    expect(onChange)->toHaveBeenCalledTimes(0)
    expect(spyActiveShop)->toHaveBeenCalledTimes(0)
  })

  itPromise("should not render any `All shops` default option", async () => {
    let shopA = mockShop(~id="shop-id-a", ~name="shop-name-a", ())
    let shopB = mockShop(~id="shop-id-b", ~name="shop-name-b", ())
    let shopC = mockShop(~id="shop-id-c", ~name="shop-name-c", ())
    let auth = mockAuthState(~activeShop=Some(shopA), ~shops=[shopA, shopB, shopC])

    let defaultValue = shopA
    let spyActiveShop = fn1(ignore)
    let onChange = fn1(ignore)

    render(
      <Providers auth=Logged(auth)>
        <TestableSelectSingleShopFilter
          defaultValue onChange={onChange->fn} spyActiveShop={spyActiveShop->fn}
        />
      </Providers>,
    )->ignore

    expect(onChange)->toHaveBeenCalledTimes(0)
    expect(spyActiveShop)->toHaveBeenCalledTimes(0)

    let trigger = screen->getByRoleExn(#button)

    expect(trigger)->toHaveTextContent("shop-name-a")

    await userEvent->TestingLibraryEvent.click(trigger)

    let option = screen->queryByRoleWithOptions(#option, {name: "All shops"})

    expect(option)->toBeNone
  })
})

describe("InputSelectSingleShopField", () => {
  module TestableInputSelectSingleShopField = {
    @react.component
    let make = (~defaultValue, ~disabledIds=[], ~onChange, ~spyActiveShop) => {
      let (value, setValue) = React.useState(() => defaultValue)
      let activeShop = Auth.useActiveShop()

      ReactUpdateEffect.use1(() => {
        spyActiveShop(activeShop)
        None
      }, [activeShop])

      let onChange = value => {
        onChange(value)
        setValue(_ => value)
      }

      <Auth.SelectSingleShopFilter value disabledIds onChange />
    }
  }

  itPromise("should trigger onChange and change activeShop", async () => {
    let shopA = mockShop(~id="shop-id-a", ~name="shop-name-a", ())
    let shopB = mockShop(~id="shop-id-b", ~name="shop-name-b", ())
    let auth = mockAuthState(~activeShop=Some(shopA), ~shops=[shopA, shopB])

    let defaultValue = shopA
    let spyActiveShop = fn1(ignore)
    let onChange = fn1(ignore)

    render(
      <Providers auth=Logged(auth)>
        <TestableInputSelectSingleShopField
          defaultValue onChange={onChange->fn} spyActiveShop={spyActiveShop->fn}
        />
      </Providers>,
    )->ignore

    expect(onChange)->toHaveBeenCalledTimes(0)
    expect(spyActiveShop)->toHaveBeenCalledTimes(0)

    let trigger = screen->getByRoleExn(#button)

    expect(trigger)->toHaveTextContent("shop-name-a")

    await userEvent->TestingLibraryEvent.click(trigger)

    expect(onChange)->toHaveBeenCalledTimes(0)
    expect(spyActiveShop)->toHaveBeenCalledTimes(0)

    let option = screen->getByRoleWithOptionsExn(#option, {name: "shop-name-b"})

    await userEvent->TestingLibraryEvent.click(option)

    expect(onChange)->toHaveBeenCalledTimes(1)
    expect(onChange)->toHaveBeenCalledWith1(shopB)
    expect(spyActiveShop)->toHaveBeenCalledTimes(1)
    expect(spyActiveShop)->toHaveBeenCalledWith1(Some(shopB))

    let trigger = screen->getByRoleExn(#button)

    expect(trigger)->toHaveTextContent("shop-name-b")
    expect(trigger)->toBeVisible
  })

  itPromise("should disable some options based on params", async () => {
    let shopA = mockShop(~id="shop-id-a", ~name="shop-name-a", ())
    let shopB = mockShop(~id="shop-id-b", ~name="shop-name-b", ())
    let shopC = mockShop(~id="shop-id-c", ~name="shop-name-c", ())
    let auth = mockAuthState(~activeShop=Some(shopA), ~shops=[shopA, shopB, shopC])

    let defaultValue = shopA
    let spyActiveShop = fn1(ignore)
    let onChange = fn1(ignore)

    render(
      <Providers auth=Logged(auth)>
        <TestableInputSelectSingleShopField
          defaultValue
          disabledIds=[shopC.id]
          onChange={onChange->fn}
          spyActiveShop={spyActiveShop->fn}
        />
      </Providers>,
    )->ignore

    expect(onChange)->toHaveBeenCalledTimes(0)
    expect(spyActiveShop)->toHaveBeenCalledTimes(0)

    let trigger = screen->getByRoleExn(#button)

    expect(trigger)->toHaveTextContent("shop-name-a")

    await userEvent->TestingLibraryEvent.click(trigger)

    let disabledOption = screen->getByRoleWithOptionsExn(#option, {name: "shop-name-c"})

    expect(disabledOption)->toHaveAttributeValue("aria-disabled", "true")

    await userEvent->TestingLibraryEvent.click(disabledOption)

    expect(onChange)->toHaveBeenCalledTimes(0)
    expect(spyActiveShop)->toHaveBeenCalledTimes(0)
  })

  itPromise("should not render any `All shops` default option", async () => {
    let shopA = mockShop(~id="shop-id-a", ~name="shop-name-a", ())
    let shopB = mockShop(~id="shop-id-b", ~name="shop-name-b", ())
    let shopC = mockShop(~id="shop-id-c", ~name="shop-name-c", ())
    let auth = mockAuthState(~activeShop=Some(shopA), ~shops=[shopA, shopB, shopC])

    let defaultValue = shopA
    let spyActiveShop = fn1(ignore)
    let onChange = fn1(ignore)

    render(
      <Providers auth=Logged(auth)>
        <TestableInputSelectSingleShopField
          defaultValue onChange={onChange->fn} spyActiveShop={spyActiveShop->fn}
        />
      </Providers>,
    )->ignore

    expect(onChange)->toHaveBeenCalledTimes(0)
    expect(spyActiveShop)->toHaveBeenCalledTimes(0)

    let trigger = screen->getByRoleExn(#button)

    expect(trigger)->toHaveTextContent("shop-name-a")

    await userEvent->TestingLibraryEvent.click(trigger)

    let option = screen->queryByRoleWithOptions(#option, {name: "All shops"})

    expect(option)->toBeNone
  })
})
