open Vitest
open TestingLibraryReact

module TestableQuery = AuthLoginRecoveryPage.PasswordRecoveryRequest

describe("PasswordRecoveryRequest", () => {
  let {encodeBody} = module(TestableQuery)

  test("encodeBody", () =>
    expect(
      encodeBody(~username="mock-username")->Json.stringify,
    )->toStrictEqual(`{"username":"mock-username"}`)
  )
})

let mockPasswordRecoveryRequest = (~futureResult) => {
  (~username as _) => {
    Future.makePure(resolve => resolve(futureResult()))
  }
}

module TestableAuthLoginRecoveryPage = {
  @react.component
  let make = (~history=?, ~passwordRecoveryRequest) => {
    <Providers ?history>
      <AuthLoginRecoveryPage
        loginRecoveryEmailSentRoute=AuthRoutes.loginRecoveryEmailSentRoute passwordRecoveryRequest
      />
    </Providers>
  }
}

let isAriaDisabled = expect => expect->toHaveAttributeValue("aria-disabled", "true")

itPromise("should display an error when input value is a non-valid email", async () => {
  let userEvent = TestingLibraryEvent.setup()

  let requestResult = fn1(() => Ok())
  let passwordRecoveryRequest = mockPasswordRecoveryRequest(~futureResult=requestResult->fn)

  let _ = <TestableAuthLoginRecoveryPage passwordRecoveryRequest />->render

  let input = screen->getByLabelTextExn("Email")
  expect(input)->toBeVisible

  await userEvent->TestingLibraryEvent.typeWithOptions(
    input,
    "noelwino.fr",
    {initialSelectionStart: 0, initialSelectionEnd: 2},
  )

  let button = screen->getByRoleExn(#button)
  expect(button)->toBeVisible
  expect(button)->toHaveTextContent("Send a recovery link")

  await userEvent->TestingLibraryEvent.click(button)

  expect(
    screen->getByTextExn(
      "There are some errors in the form, please correct them before trying to send it again.",
    ),
  )->toBeVisible
})

itPromise("should display error when response is unexpected", async () => {
  let userEvent = TestingLibraryEvent.setup()

  let requestResult = fn1(() => Error())
  let passwordRecoveryRequest = mockPasswordRecoveryRequest(~futureResult=requestResult->fn)

  let _ = <TestableAuthLoginRecoveryPage passwordRecoveryRequest />->render

  let input = screen->getByLabelTextExn("Email")

  await userEvent->TestingLibraryEvent.typeWithOptions(
    input,
    "<EMAIL>",
    {initialSelectionStart: 0, initialSelectionEnd: 2},
  )

  let button = screen->getByRoleExn(#button)

  await userEvent->TestingLibraryEvent.click(button)

  await waitFor(() =>
    expect(
      screen->getByTextExn("An unexpected error occured. Please try again or contact the support."),
    )->toBeVisible
  )
})

itPromise("should redirect when query succeed", async () => {
  let userEvent = TestingLibraryEvent.setup()

  let history = History.createMemoryHistory()

  let requestResult = fn1(() => Ok())
  let passwordRecoveryRequest = mockPasswordRecoveryRequest(~futureResult=requestResult->fn)
  let _ = <TestableAuthLoginRecoveryPage history passwordRecoveryRequest />->render

  let input = screen->getByLabelTextExn("Email")
  expect(input)->toBeVisible

  await userEvent->TestingLibraryEvent.typeWithOptions(
    input,
    "<EMAIL>",
    {initialSelectionStart: 0, initialSelectionEnd: 2},
  )

  let button = screen->getByRoleExn(#button)
  expect(button)->toBeVisible
  expect(button)->toHaveTextContent("Send a recovery link")

  expect(history.location.pathname)->toBe("/")

  await userEvent->TestingLibraryEvent.click(button)

  expect(history.location.pathname)->toBe("/auth/login/recovery-email-sent")
})
