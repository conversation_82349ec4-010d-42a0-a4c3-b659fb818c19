open Vitest
open TestingLibraryReact

module TestableQuery = AuthLoginResetPage.ResetPasswordRequest

describe("ResetPasswordRequest", () => {
  let {encodeBody, decodeInvalidRequestFailure} = module(TestableQuery)

  test("encodeBody", () =>
    expect(
      encodeBody(
        ~tokenId="mock-token-id",
        ~token="mock-token",
        ~userId="mock-userId",
        ~newPassword="mock-password",
      )->Json.stringify,
    )->toStrictEqual(`{"tokenId":"mock-token-id","token":"mock-token","userId":"mock-userId","newPassword":"mock-password"}`)
  )

  test("decodeInvalidRequestFailure", () => {
    let serverFailure = {
      Request.kind: "ExpiredOneTimeToken",
      message: "",
      data: None,
    }
    expect(serverFailure->decodeInvalidRequestFailure)->toStrictEqual(ExpiredOneTimeToken)

    let serverFailure = {
      Request.kind: "NotFoundPasswordResetOneTimeToken",
      message: "",
      data: None,
    }
    expect(serverFailure->decodeInvalidRequestFailure)->toStrictEqual(
      NotFoundPasswordResetOneTimeToken,
    )

    let serverFailure = {
      Request.kind: "Unknown",
      message: "",
      data: None,
    }
    expect(serverFailure->decodeInvalidRequestFailure)->toStrictEqual(UnknownServerFailure)
  })
})

module TestableAuthLoginResetPage = {
  @react.component
  let make = (~userId, ~tokenId, ~token, ~history=?, ~resetPasswordRequest) => {
    <Providers ?history>
      <AuthLoginResetPage
        userId
        tokenId
        token
        successRoute={AuthRoutes.loginRecoverySuccessRoute}
        resetPasswordRequest
      />
    </Providers>
  }
}

let mockedResponseSuccess = Json.stringifyAny({"data": ""})->Option.getExn->Json.parseExn

let mockedResponseErrored =
  Json.stringifyAny({"errors": Json.encodeString("some error")})->Option.getExn->Json.parseExn

let validPasswordMock = "ValidPassword123"
let invalidPasswordMock = "invalid"

let isAriaDisabled = expect => expect->toHaveAttributeValue("aria-disabled", "true")

let mockUsernameUpdateRequest = (
  ~futureResult,
  ~tokenId as _,
  ~token as _,
  ~userId as _,
  ~newPassword as _,
) => Future.makePure(resolve => resolve(futureResult()))

itPromise("should display an error when confirmation password is missing", async () => {
  let userEvent = TestingLibraryEvent.setup()

  let requestResult = fn1(() => Error(None))
  let resetPasswordRequest = mockUsernameUpdateRequest(~futureResult=requestResult->fn)

  let _ =
    <TestableAuthLoginResetPage
      userId="dummy-id" tokenId="dummy-token-id" token="dummy-token" resetPasswordRequest
    />->render

  let input = screen->getByLabelTextExn("New password")
  expect(input)->toBeVisible

  await userEvent->TestingLibraryEvent.typeWithOptions(
    input,
    validPasswordMock,
    {initialSelectionStart: 0, initialSelectionEnd: 2},
  )

  let button = screen->getByRoleWithOptionsExn(#button, {name: "Reset password"})
  expect(button)->toBeVisible
  expect(button)->toHaveTextContent("Reset password")

  await userEvent->TestingLibraryEvent.click(button)

  expect(
    screen->getByTextExn(
      "There are some errors in the form, please correct them before trying to send it again.",
    ),
  )->toBeVisible
  expect(screen->getByTextExn("Password and its confirmation must be identical"))->toBeVisible
})

itPromise("should display an error when password does not match policy", async () => {
  let userEvent = TestingLibraryEvent.setup()

  let requestResult = fn1(() => Error(None))
  let resetPasswordRequest = mockUsernameUpdateRequest(~futureResult=requestResult->fn)

  let _ =
    <TestableAuthLoginResetPage
      userId="dummy-id" tokenId="dummy-token-id" token="dummy-token" resetPasswordRequest
    />->render

  let inputPassword = screen->getByLabelTextExn("New password")
  expect(inputPassword)->toBeVisible

  await userEvent->TestingLibraryEvent.typeWithOptions(
    inputPassword,
    invalidPasswordMock,
    {initialSelectionStart: 0, initialSelectionEnd: 2},
  )

  let inputConfirmationPassword = screen->getByLabelTextExn("New password confirmation")
  expect(inputConfirmationPassword)->toBeVisible

  await userEvent->TestingLibraryEvent.typeWithOptions(
    inputConfirmationPassword,
    invalidPasswordMock,
    {initialSelectionStart: 0, initialSelectionEnd: 2},
  )

  let button = screen->getByRoleWithOptionsExn(#button, {name: "Reset password"})
  expect(button)->toBeVisible
  expect(button)->toHaveTextContent("Reset password")

  await userEvent->TestingLibraryEvent.click(button)

  expect(
    screen->getByTextExn(
      "There are some errors in the form, please correct them before trying to send it again.",
    ),
  )->toBeVisible
})

itPromise("should display an error when request does not succeed", async () => {
  let userEvent = TestingLibraryEvent.setup()

  let requestResult = fn1(() => Error(None))
  let resetPasswordRequest = mockUsernameUpdateRequest(~futureResult=requestResult->fn)

  let _ =
    <TestableAuthLoginResetPage
      userId="dummy-id" tokenId="dummy-token-id" token="dummy-token" resetPasswordRequest
    />->render

  let inputPassword = screen->getByLabelTextExn("New password")
  expect(inputPassword)->toBeVisible

  await userEvent->TestingLibraryEvent.typeWithOptions(
    inputPassword,
    validPasswordMock,
    {initialSelectionStart: 0, initialSelectionEnd: 2},
  )

  let inputConfirmationPassword = screen->getByLabelTextExn("New password confirmation")
  expect(inputConfirmationPassword)->toBeVisible

  await userEvent->TestingLibraryEvent.typeWithOptions(
    inputConfirmationPassword,
    validPasswordMock,
    {initialSelectionStart: 0, initialSelectionEnd: 2},
  )

  let button = screen->getByRoleWithOptionsExn(#button, {name: "Reset password"})
  expect(button)->toBeVisible
  expect(button)->toHaveTextContent("Reset password")

  await userEvent->TestingLibraryEvent.click(button)

  await waitFor(() =>
    expect(
      screen->getByTextExn("An unexpected error occured. Please try again or contact the support."),
    )->toBeVisible
  )
})

itPromise("should redirect to login/password-reset when request succeeds", async () => {
  let userEvent = TestingLibraryEvent.setup()
  let requestResult = fn1(() => Ok(Js.Dict.empty()->Js.Json.object_))
  let resetPasswordRequest = mockUsernameUpdateRequest(~futureResult=requestResult->fn)

  let history = History.createMemoryHistory()
  let _ =
    <TestableAuthLoginResetPage
      userId="dummy-id" tokenId="dummy-token-id" token="dummy-token" history resetPasswordRequest
    />->render

  let inputPassword = screen->getByLabelTextExn("New password")
  expect(inputPassword)->toBeVisible

  await userEvent->TestingLibraryEvent.typeWithOptions(
    inputPassword,
    "123456789Aa",
    {initialSelectionStart: 0, initialSelectionEnd: 2},
  )

  let inputConfirmationPassword = screen->getByLabelTextExn("New password confirmation")
  expect(inputConfirmationPassword)->toBeVisible

  await userEvent->TestingLibraryEvent.typeWithOptions(
    inputConfirmationPassword,
    "123456789Aa",
    {initialSelectionStart: 0, initialSelectionEnd: 2},
  )

  let button = screen->getByRoleWithOptionsExn(#button, {name: "Reset password"})
  expect(button)->toBeVisible
  expect(button)->toHaveTextContent("Reset password")

  expect(history.location.pathname)->toBe("/")

  await userEvent->TestingLibraryEvent.click(button)

  expect(history.location.pathname)->toBe("/auth/login/password-reset")
})

todo("Test expired token error")
