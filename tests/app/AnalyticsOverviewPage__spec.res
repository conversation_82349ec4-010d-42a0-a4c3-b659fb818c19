open Vitest

let {getPreviousPeriod, getPreviousYearPeriod} = module(DateHelpers)

let {make} = module(AnalyticsOverviewPage.Reducer)

describe("reducer", () => {
  it("should change comparison period when a new period is given", () => {
    let selectedPeriodStart = Js.Date.makeWithYMD(~year=2022., ~month=08., ~date=04., ())
    let selectedPeriodEnd = Js.Date.makeWithYMD(~year=2022., ~month=08., ~date=06., ())
    let selectedPeriod = (selectedPeriodStart, selectedPeriodEnd)

    let comparisonPeriodStart = Js.Date.makeWithYMD(~year=2022., ~month=08., ~date=01., ())
    let comparisonPeriodEnd = Js.Date.makeWithYMD(~year=2022., ~month=08., ~date=03., ())
    let comparisonPeriod = (comparisonPeriodStart, comparisonPeriodEnd)

    let state: AnalyticsOverviewPage.Reducer.state = {
      selectedPeriod,
      comparisonPeriod,
      comparisonMode: PreviousPeriod,
      previousPeriod: getPreviousPeriod(selectedPeriodStart, selectedPeriodEnd),
      previousYearPeriod: getPreviousYearPeriod(selectedPeriodStart, selectedPeriodEnd),
    }

    let selectedNewPeriodStart = Js.Date.makeWithYMD(~year=2022., ~month=07., ~date=04., ())
    let selectedNewPeriodEnd = Js.Date.makeWithYMD(~year=2022., ~month=07., ~date=06., ())

    let newPeriod = (selectedNewPeriodStart, selectedNewPeriodEnd)

    let updatedComparisonPeriod = getPreviousPeriod(selectedNewPeriodStart, selectedNewPeriodEnd)
    let previousYearPeriod = getPreviousYearPeriod(selectedNewPeriodStart, selectedNewPeriodEnd)

    let expectedState: AnalyticsOverviewPage.Reducer.state = {
      selectedPeriod: newPeriod,
      comparisonPeriod: updatedComparisonPeriod,
      comparisonMode: PreviousPeriod,
      previousPeriod: updatedComparisonPeriod,
      previousYearPeriod,
    }
    expect(make(state, SelectedPeriodChanged(newPeriod)))->toStrictEqual(expectedState)
  })

  it("should change comparison period when comparisonMode changes", () => {
    let selectedPeriodStart = Js.Date.makeWithYMD(~year=2022., ~month=08., ~date=04., ())
    let selectedPeriodEnd = Js.Date.makeWithYMD(~year=2022., ~month=08., ~date=06., ())
    let selectedPeriod = (selectedPeriodStart, selectedPeriodEnd)

    let comparisonPeriodStart = Js.Date.makeWithYMD(~year=2022., ~month=08., ~date=01., ())
    let comparisonPeriodEnd = Js.Date.makeWithYMD(~year=2022., ~month=08., ~date=03., ())
    let comparisonPeriod = (comparisonPeriodStart, comparisonPeriodEnd)

    let state: AnalyticsOverviewPage.Reducer.state = {
      selectedPeriod,
      comparisonPeriod,
      comparisonMode: PreviousPeriod,
      previousPeriod: getPreviousPeriod(selectedPeriodStart, selectedPeriodEnd),
      previousYearPeriod: getPreviousYearPeriod(selectedPeriodStart, selectedPeriodEnd),
    }
    let updatedComparisonPeriod = (
      Js.Date.makeWithYMD(~year=2021., ~month=08., ~date=04., ()),
      Js.Date.makeWithYMD(~year=2021., ~month=08., ~date=06., ()),
    )
    let expectedState: AnalyticsOverviewPage.Reducer.state = {
      selectedPeriod,
      comparisonPeriod: updatedComparisonPeriod,
      comparisonMode: PreviousYearPeriod,
      previousPeriod: getPreviousPeriod(selectedPeriodStart, selectedPeriodEnd),
      previousYearPeriod: getPreviousYearPeriod(selectedPeriodStart, selectedPeriodEnd),
    }
    expect(make(state, ComparisonModeChanged(PreviousYearPeriod)))->toStrictEqual(expectedState)
  })
})

let mockGeneralKeyPerformanceIndicators: AnalyticsOverviewPage.AnalyticsPerformanceIndicatorsQueries.Raw.t = {
  indicatorsCurrent: {
    __typename: "GeneralKeyPerformanceIndicators",
    ticketsRevenue: {
      __typename: "TicketsRevenue",
      includingTaxes: 5297.98,
      excludingTaxes: 4574.1,
    },
    invoicesRevenue: {
      __typename: "InvoicesRevenue",
      includingTaxes: 1974.95,
      excludingTaxes: 1659.27,
    },
    globalRevenue: {
      __typename: "GlobalRevenue",
      includingTaxes: 7272.93,
      excludingTaxes: 6233.37,
    },
    salesCount: 64,
    salesIndex: 7.7,
    shoppingCartAverage: 113.64,
    totalAmountOfTaxes: 1039.56,
    productsSoldCount: 493,
    margin: 2680.33,
    marginRate: 43.62,
    markupRate: 77.37,
    totalPurchaseCost: 3464.21,
  },
  indicatorPrevious: {
    __typename: "GeneralKeyPerformanceIndicators",
    ticketsRevenue: {
      __typename: "TicketsRevenue",
      includingTaxes: 4173.98,
      excludingTaxes: 3517.7,
    },
    invoicesRevenue: {
      __typename: "InvoicesRevenue",
      includingTaxes: 4353.28,
      excludingTaxes: 3631.74,
    },
    globalRevenue: {
      __typename: "GlobalRevenue",
      includingTaxes: 8527.26,
      excludingTaxes: 7149.44,
    },
    salesCount: 75,
    salesIndex: 8.24,
    shoppingCartAverage: 113.7,
    totalAmountOfTaxes: 1377.82,
    productsSoldCount: 618,
    margin: 2832.46,
    marginRate: 41.9,
    markupRate: 72.11,
    totalPurchaseCost: 3927.97,
  },
}

let {setupNodeServer, use, listen, resetHandlers, close, ctxDelayDuration} = module(MSW)
let {ctxData, makeLink} = module(MSW.GraphQL)

let gatewayLink = makeLink("http://localhost/graphql")
let server = setupNodeServer([
  gatewayLink->MSWHelpers.apolloQueryWithDelayExn(
    module(AnalyticsOverviewPage.AnalyticsPerformanceIndicatorsQueries),
    (_, res, ctx) =>
      res(. ctx->ctxDelayDuration(100), ctx->ctxData(mockGeneralKeyPerformanceIndicators)),
  ),
])

beforeAll(() => server->listen({onUnhandledRequest: #warn}))
afterEach(() => server->resetHandlers)
afterAll(() => server->close)

let userEvent = TestingLibraryEvent.setup()

open TestingLibraryReact
module TestableAnalyticsOverviewPage = {
  @react.component
  let make = () => {
    <Providers>
      <AnalyticsOverviewPage />
    </Providers>
  }
}

// NOTE - This is not exhaustive, the aim is just to test that nothing breaks from query to display
itPromise("should display progression indicators", async () => {
  let _ = <TestableAnalyticsOverviewPage />->render

  await waitFor(() => {
    let progressionBadges = screen->getAllByLabelTextExn("progression")

    expect(progressionBadges->Array.getExn(0))->toHaveTextContent("+ 30%") // Tickets revenue exct. VAT
    expect(progressionBadges->Array.getExn(1))->toHaveTextContent("- 54%") // Invoice revenue exct. VAT
    expect(progressionBadges->Array.getExn(2))->toHaveTextContent("- 13%") // Global revenue exct. VAT
    expect(progressionBadges->Array.getExn(3))->toHaveTextContent("- 5%") //  Margin
    expect(progressionBadges->Array.getExn(7))->toHaveTextContent("- 15%") // Sales count
  })
})
