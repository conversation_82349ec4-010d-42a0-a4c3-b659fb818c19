open Vitest

let {mockUser, mockShop, mockAuthSingleScope, mockAuthOrganisationScope} = module(Auth__Mock)

describe("createRoute", () => {
  it("should return Ok with the route in Single(_) scope", () => {
    let shop = mockShop(~id="shop-id-a", ~name="shop-name-a", ~kind=#INDEPENDENT, ())
    let authScope = mockAuthSingleScope(~shop, ())
    expect(PromotionRouter.createRoute(~authScope))->toStrictEqual(Ok("/promotions/create"))
  })

  it("it should return Error in Single shop scope with Warehouse kind", () => {
    let shop = mockShop(~id="shop-id-a", ~name="shop-name-a", ~kind=#WAREHOUSE, ())
    let authScope = mockAuthSingleScope(~shop, ())
    expect(PromotionRouter.createRoute(~authScope))->toStrictEqual(Error())
  })

  it("it should return Ok with the route in Organisation shop scope", () => {
    let authScope = mockAuthOrganisationScope(
      ~activeShop=Some(mockShop()),
      ~shops=[mockShop(), mockShop()],
      (),
    )
    expect(PromotionRouter.createRoute(~authScope))->toStrictEqual(Ok("/promotions/create"))
  })

  it("it should return Error in Organisation shop scope when it counts >= 6 shops", () => {
    let authScope = mockAuthOrganisationScope(
      ~activeShop=None,
      ~shops=[mockShop(), mockShop(), mockShop(), mockShop(), mockShop(), mockShop()],
      (),
    )
    expect(PromotionRouter.createRoute(~authScope))->toStrictEqual(Error())
  })
})
