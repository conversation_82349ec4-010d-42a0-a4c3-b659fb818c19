open Vitest
open TestingLibraryReact

let mockTableRowShop = (~name="", ~kind=#INDEPENDENT, ()) => {
  AdminImpersonationPage.TableRow.name,
  kind,
}

let mockTableRow = (~id, ~organizationName="", ~userName="", ~email="", ~shops=[], ()) => {
  AdminImpersonationPage.TableRow.id,
  organizationName,
  userName,
  email,
  shops,
}

let mockQueryAllUsersWithAssociatedShopsRequestShop = (~name="", ~type_=?, ()) => {
  AdminImpersonationPage.QueryAllUsersWithAssociatedShopsRequest.name,
  type_,
}

let mockQueryAllUsersWithAssociatedShopsRequestItem = (
  ~id="",
  ~organizationName="",
  ~username="",
  ~name="",
  ~shops=[],
  (),
) => {
  AdminImpersonationPage.QueryAllUsersWithAssociatedShopsRequest.id,
  organizationName,
  username,
  name,
  shops,
}

let mockQueryAllUsersRequestItem = (
  ~id="",
  ~organizationName="",
  ~username="",
  ~name="",
  ~shopsIds=[],
  (),
) => {
  AdminImpersonationPage.QueryAllUsersRequest.id,
  organizationName,
  username,
  name,
  shopsIds,
}

let mockQueryAllShopsRequestItem = (~id="", ~name="", ~type_=?, ()) => {
  AdminImpersonationPage.QueryAllShopsRequest.id,
  name,
  type_,
}

describe("TableRow", () => {
  test("keyExtractor", () => {
    let {keyExtractor} = module(AdminImpersonationPage.TableRow)

    expect(keyExtractor(mockTableRow(~id="", ())))->toBe("")
    expect(keyExtractor(mockTableRow(~id="row-id", ())))->toBe("row-id")
  })

  test("sanitize", () => {
    let {sanitize} = module(AdminImpersonationPage.TableRow)

    expect(sanitize("abc"))->toStrictEqual("abc")
    expect(sanitize("Àbç!"))->toStrictEqual("abc")
  })

  test("matchShopsNames", () => {
    let {matchShopsNames} = module(AdminImpersonationPage.TableRow)

    expect(["Àbç", "xYz"]->matchShopsNames("abc"))->toBe(true)
    expect(["Àbç", "xYz"]->matchShopsNames("B"))->toBe(true)
    expect(["Àbç", "xYz"]->matchShopsNames("l"))->toBe(false)
    expect(["Àbç", "xYz"]->matchShopsNames("cd"))->toBe(false)
    expect(["Àbç", "xYz"]->matchShopsNames("abc"))->toBe(true)
    expect(["Àbç", "xYz"]->matchShopsNames("xyz"))->toBe(true)
  })

  test("match", () => {
    let {match} = module(AdminImpersonationPage.TableRow)

    let row = mockTableRow(
      ~id="id",
      ~email="email",
      ~organizationName="organizationName",
      ~userName="userNàme",
      ~shops=[
        mockTableRowShop(~name="Shop-à-Name", ()),
        mockTableRowShop(~name="shop-B-name", ()),
      ],
      (),
    )

    expect(match(row, ""))->toBe(true)
    expect(match(row, "id"))->toBe(true)
    expect(match(row, "dummy"))->toBe(false)
    expect(match(row, "maiK"))->toBe(false)
    expect(match(row, "email userName"))->toBe(false)
    expect(match(row, "organizationName"))->toBe(true)
    expect(match(row, "zationna"))->toBe(true)
    expect(match(row, "zatioNna"))->toBe(true)
    expect(match(row, "userName"))->toBe(true)
    expect(match(row, "username"))->toBe(true)
    expect(match(row, "ùsernàme"))->toBe(true)
    expect(match(row, "user"))->toBe(true)
    expect(match(row, "mail"))->toBe(true)
    expect(match(row, "Shop*à"))->toBe(true)
  })

  test("fromQueryItem", () => {
    let {fromQueryItem} = module(AdminImpersonationPage.TableRow)

    let queryItem = mockQueryAllUsersWithAssociatedShopsRequestItem(
      ~id="item-id",
      ~name="item-name",
      ~username="item-username",
      ~organizationName="item-organizationName",
      ~shops=[mockQueryAllUsersWithAssociatedShopsRequestShop(~name="item-shop-name", ())],
      (),
    )
    expect(fromQueryItem(queryItem))->toStrictEqual({
      AdminImpersonationPage.TableRow.id: "item-id",
      organizationName: "item-organizationName",
      email: "item-username",
      userName: "item-name",
      shops: [{name: "item-shop-name", kind: #INDEPENDENT}],
    })

    let queryItem = mockQueryAllUsersWithAssociatedShopsRequestItem(
      ~id="item-id",
      ~shops=[
        mockQueryAllUsersWithAssociatedShopsRequestShop(
          ~name="item-shop-name",
          ~type_="INTEGRATED",
          (),
        ),
      ],
      (),
    )
    expect(fromQueryItem(queryItem))->toStrictEqual({
      AdminImpersonationPage.TableRow.id: "item-id",
      organizationName: "",
      email: "",
      userName: "",
      shops: [{name: "item-shop-name", kind: #INTEGRATED}],
    })

    let queryItem = mockQueryAllUsersWithAssociatedShopsRequestItem(
      ~id="item-id",
      ~shops=[
        mockQueryAllUsersWithAssociatedShopsRequestShop(
          ~name="item-shop-name",
          ~type_="AFFILIATED",
          (),
        ),
      ],
      (),
    )
    expect(fromQueryItem(queryItem))->toStrictEqual({
      AdminImpersonationPage.TableRow.id: "item-id",
      organizationName: "",
      email: "",
      userName: "",
      shops: [{name: "item-shop-name", kind: #AFFILIATED}],
    })

    let queryItem = mockQueryAllUsersWithAssociatedShopsRequestItem(
      ~id="item-id",
      ~shops=[
        mockQueryAllUsersWithAssociatedShopsRequestShop(
          ~name="item-shop-name",
          ~type_="FRANCHISED",
          (),
        ),
      ],
      (),
    )
    expect(fromQueryItem(queryItem))->toStrictEqual({
      AdminImpersonationPage.TableRow.id: "item-id",
      organizationName: "",
      email: "",
      userName: "",
      shops: [{name: "item-shop-name", kind: #FRANCHISED}],
    })

    let queryItem = mockQueryAllUsersWithAssociatedShopsRequestItem(
      ~id="item-id",
      ~shops=[
        mockQueryAllUsersWithAssociatedShopsRequestShop(
          ~name="item-shop-name",
          ~type_="WAREHOUSE",
          (),
        ),
      ],
      (),
    )
    expect(fromQueryItem(queryItem))->toStrictEqual({
      AdminImpersonationPage.TableRow.id: "item-id",
      organizationName: "",
      email: "",
      userName: "",
      shops: [{name: "item-shop-name", kind: #WAREHOUSE}],
    })

    let queryItem = mockQueryAllUsersWithAssociatedShopsRequestItem(
      ~id="item-id",
      ~shops=[
        mockQueryAllUsersWithAssociatedShopsRequestShop(
          ~name="item-shop-name",
          ~type_="UNKNOWN",
          (),
        ),
      ],
      (),
    )
    expect(fromQueryItem(queryItem))->toStrictEqual({
      AdminImpersonationPage.TableRow.id: "item-id",
      organizationName: "",
      email: "",
      userName: "",
      shops: [{name: "item-shop-name", kind: #INDEPENDENT}],
    })
  })
})

describe("TableRows", () => {
  let {totalPages, search, paginate, fromQueryAllUsers} = module(AdminImpersonationPage.TableRows)

  test("totalPages", () => {
    expect(totalPages([]))->toBe(0)
    expect(totalPages([mockTableRow(~id="1")]))->toBe(1)

    let tableRowFromIndex = index => mockTableRow(~id=index->Int.toString, ())

    expect(totalPages(Array.makeBy(2, tableRowFromIndex)))->toBe(1)
    expect(totalPages(Array.makeBy(5, tableRowFromIndex)))->toBe(1)
    expect(totalPages(Array.makeBy(9, tableRowFromIndex)))->toBe(1)
    expect(totalPages(Array.makeBy(10, tableRowFromIndex)))->toBe(1)
    expect(totalPages(Array.makeBy(11, tableRowFromIndex)))->toBe(2)
    expect(totalPages(Array.makeBy(19, tableRowFromIndex)))->toBe(2)
    expect(totalPages(Array.makeBy(20, tableRowFromIndex)))->toBe(2)
    expect(totalPages(Array.makeBy(21, tableRowFromIndex)))->toBe(3)
    expect(totalPages(Array.makeBy(99, tableRowFromIndex)))->toBe(10)
  })

  test("search", () => {
    let tableRowA = mockTableRow(
      ~id="id-a",
      ~email="email-a",
      ~organizationName="organizationName-a",
      ~userName="userName-a",
      (),
    )
    let tableRowB = mockTableRow(
      ~id="id-b",
      ~email="email-b",
      ~organizationName="organizationName-b",
      ~userName="userName-b",
      (),
    )

    let tableRows = [tableRowA, tableRowB]

    expect(search(tableRows, ""))->toStrictEqual([tableRowA, tableRowB])
    expect(search(tableRows, "id"))->toStrictEqual([])
    expect(search(tableRows, "id-a"))->toStrictEqual([tableRowA])
    expect(search(tableRows, "userName"))->toStrictEqual([tableRowA, tableRowB])
    expect(search(tableRows, "username-a"))->toStrictEqual([tableRowA])
    expect(search(tableRows, "username-b"))->toStrictEqual([tableRowB])
  })

  test("paginate", () => {
    let tableRowFromIndex = index => mockTableRow(~id=index->Int.toString, ())

    expect(paginate(Array.makeBy(2, tableRowFromIndex), 1))->toStrictEqual(
      Array.makeBy(2, tableRowFromIndex),
    )
    expect(paginate(Array.makeBy(2, tableRowFromIndex), 2))->toStrictEqual([])
    expect(paginate(Array.makeBy(2, tableRowFromIndex), 3))->toStrictEqual([])
    expect(paginate(Array.makeBy(11, tableRowFromIndex), 1))->toStrictEqual(
      Array.makeBy(10, tableRowFromIndex),
    )
    expect(paginate(Array.makeBy(11, tableRowFromIndex), 2))->toStrictEqual(
      Array.makeBy(1, index => tableRowFromIndex(index + 10)),
    )
    expect(paginate(Array.makeBy(11, tableRowFromIndex), 3))->toStrictEqual([])
  })

  test("fromQueryAllUsers", () => {
    let queryItemA = mockQueryAllUsersWithAssociatedShopsRequestItem(
      ~id="item-a-id",
      ~name="item-a-name",
      ~username="item-a-username",
      ~organizationName="item-a-organizationName",
      ~shops=[mockQueryAllUsersWithAssociatedShopsRequestShop(~name="item-shop-name", ())],
      (),
    )
    let queryItemB = mockQueryAllUsersWithAssociatedShopsRequestItem(
      ~id="item-b-id",
      ~name="item-b-name",
      ~username="item-b-username",
      ~organizationName="item-b-organizationName",
      ~shops=[
        mockQueryAllUsersWithAssociatedShopsRequestShop(~name="item-shop-name-a", ()),
        mockQueryAllUsersWithAssociatedShopsRequestShop(
          ~name="item-shop-name-b",
          ~type_="WAREHOUSE",
          (),
        ),
      ],
      (),
    )

    expect(fromQueryAllUsers([]))->toStrictEqual([])
    expect(fromQueryAllUsers([queryItemA]))->toStrictEqual([
      {
        AdminImpersonationPage.TableRow.id: "item-a-id",
        organizationName: "item-a-organizationName",
        email: "item-a-username",
        userName: "item-a-name",
        shops: [{name: "item-shop-name", kind: #INDEPENDENT}],
      },
    ])
    expect(fromQueryAllUsers([queryItemA, queryItemB]))->toStrictEqual([
      {
        AdminImpersonationPage.TableRow.id: "item-a-id",
        organizationName: "item-a-organizationName",
        email: "item-a-username",
        userName: "item-a-name",
        shops: [{name: "item-shop-name", kind: #INDEPENDENT}],
      },
      {
        id: "item-b-id",
        organizationName: "item-b-organizationName",
        email: "item-b-username",
        userName: "item-b-name",
        shops: [
          {name: "item-shop-name-a", kind: #INDEPENDENT},
          {name: "item-shop-name-b", kind: #WAREHOUSE},
        ],
      },
    ])
  })
})

describe("QueryAllUsersRequest", () => {
  let {decodeResult, decodeResultItem} = module(AdminImpersonationPage.QueryAllUsersRequest)

  let mockJsonUser = (~id, ~organizationName, ~username, ~name, ~shopsIds, ()) => {
    let userScopeDict = Js.Dict.fromArray([
      ("shopIds", shopsIds->Array.map(Json.encodeString)->Json.encodeArray),
    ])
    Js.Dict.fromArray([
      ("id", id->Json.encodeString),
      ("organizationName", organizationName->Json.encodeString),
      ("username", username->Json.encodeString),
      ("name", name->Json.encodeString),
      ("scope", userScopeDict->Json.encodeDict),
    ])->Json.encodeDict
  }

  test("decodeResultItem", () => {
    expect(decodeResultItem(""->Json.encodeString))->toStrictEqual(None)

    let dummyErrors = Js.Dict.empty()
    dummyErrors->Js.Dict.set("errors", "dummy-error"->Json.encodeString)
    expect(decodeResultItem(dummyErrors->Json.encodeDict))->toStrictEqual(None)

    let jsonUser = mockJsonUser(
      ~id="user-id",
      ~organizationName="user-organizationName",
      ~username="user-username",
      ~name="user-name",
      ~shopsIds=["user-shop-id-a", "user-shop-id-b"],
      (),
    )
    expect(decodeResultItem(jsonUser))->toStrictEqual(
      Some({
        id: "user-id",
        organizationName: "user-organizationName",
        username: "user-username",
        name: "user-name",
        shopsIds: ["user-shop-id-a", "user-shop-id-b"],
      }),
    )

    let jsonUser = mockJsonUser(
      ~id="user-id",
      ~organizationName="user-organizationName",
      ~username="user-username",
      ~name="user-name",
      ~shopsIds=["user-shop-id-a", "user-shop-id-b"],
      (),
    )
    expect(decodeResultItem(jsonUser))->toStrictEqual(
      Some({
        id: "user-id",
        organizationName: "user-organizationName",
        username: "user-username",
        name: "user-name",
        shopsIds: ["user-shop-id-a", "user-shop-id-b"],
      }),
    )

    let jsonUser = mockJsonUser(
      ~id="user-id",
      ~organizationName="user-organizationName",
      ~username="user-username",
      ~name="user-name",
      ~shopsIds=["user-shop-id-a", "user-shop-id-b"],
      (),
    )
    expect(decodeResultItem(jsonUser))->toStrictEqual(
      Some({
        id: "user-id",
        organizationName: "user-organizationName",
        username: "user-username",
        name: "user-name",
        shopsIds: ["user-shop-id-a", "user-shop-id-b"],
      }),
    )
  })

  test("decodeResult", () => {
    expect(decodeResult("data"->Json.encodeString))->toStrictEqual([])

    let dummyErrors = Js.Dict.empty()
    dummyErrors->Js.Dict.set("errors", "dummy-error"->Json.encodeString)
    expect(decodeResult(dummyErrors->Json.encodeDict))->toStrictEqual([])

    let dummyData = Js.Dict.empty()
    dummyData->Js.Dict.set("a", "b"->Json.encodeString)
    expect(decodeResult(dummyData->Json.encodeDict))->toStrictEqual([])

    let dummyItem = Js.Dict.empty()
    dummyItem->Js.Dict.set("foo", "bar"->Json.encodeString)
    expect(decodeResult([dummyItem->Json.encodeDict]->Json.encodeArray))->toStrictEqual([])

    let jsonUserA = mockJsonUser(
      ~id="user-a-id",
      ~organizationName="user-a-organizationName",
      ~username="user-a-username",
      ~name="user-a-name",
      ~shopsIds=["user-a-shop-id-a"],
      (),
    )

    let json = Json.fromObjExn([jsonUserA])
    expect(decodeResult(json))->toStrictEqual([
      {
        id: "user-a-id",
        organizationName: "user-a-organizationName",
        username: "user-a-username",
        name: "user-a-name",
        shopsIds: ["user-a-shop-id-a"],
      },
    ])

    let json = Json.fromObjExn([dummyItem->Json.encodeDict, jsonUserA])
    expect(decodeResult(json))->toStrictEqual([
      {
        id: "user-a-id",
        organizationName: "user-a-organizationName",
        username: "user-a-username",
        name: "user-a-name",
        shopsIds: ["user-a-shop-id-a"],
      },
    ])

    let jsonUserB = mockJsonUser(
      ~id="user-b-id",
      ~organizationName="user-b-organizationName",
      ~username="user-b-username",
      ~name="user-b-name",
      ~shopsIds=["user-b-shop-id-a", "user-b-shop-id-b"],
      (),
    )

    let json = Json.fromObjExn([jsonUserB, jsonUserA])
    expect(decodeResult(json))->toStrictEqual([
      {
        id: "user-b-id",
        organizationName: "user-b-organizationName",
        username: "user-b-username",
        name: "user-b-name",
        shopsIds: ["user-b-shop-id-a", "user-b-shop-id-b"],
      },
      {
        id: "user-a-id",
        organizationName: "user-a-organizationName",
        username: "user-a-username",
        name: "user-a-name",
        shopsIds: ["user-a-shop-id-a"],
      },
    ])
  })
})

describe("QueryAllShopsRequest", () => {
  let {decodeResult, decodeResultItem} = module(AdminImpersonationPage.QueryAllShopsRequest)

  let mockJsonShop = (~id, ~name, ~type_) =>
    Js.Dict.fromArray([
      ("id", id->Json.encodeString),
      ("name", name->Json.encodeString),
      ("type", type_->Json.encodeString),
    ])->Json.encodeDict

  test("decodeResultItem", () => {
    expect(decodeResultItem(""->Json.encodeString))->toStrictEqual(None)

    let dummyErrors = Js.Dict.empty()
    dummyErrors->Js.Dict.set("errors", "dummy-error"->Json.encodeString)
    expect(decodeResultItem(dummyErrors->Json.encodeDict))->toStrictEqual(None)

    let dummyJsonShop =
      Js.Dict.fromArray([
        ("identifier", ""->Json.encodeString),
        ("shop-name", ""->Json.encodeString),
      ])->Json.encodeDict
    expect(decodeResultItem(dummyJsonShop))->toStrictEqual(None)

    let jsonShop = mockJsonShop(~id="shop-id", ~name="shop-name", ~type_="shop-type")
    expect(decodeResultItem(jsonShop))->toStrictEqual(
      Some({id: "shop-id", name: "shop-name", type_: Some("shop-type")}),
    )
  })

  test("decodeResult", () => {
    expect(decodeResult("data"->Json.encodeString))->toStrictEqual([])

    let dummyErrors = Js.Dict.empty()
    dummyErrors->Js.Dict.set("errors", "dummy-error"->Json.encodeString)
    expect(decodeResult(dummyErrors->Json.encodeDict))->toStrictEqual([])

    let dummyData = Js.Dict.empty()
    dummyData->Js.Dict.set("a", "b"->Json.encodeString)
    expect(decodeResult(dummyData->Json.encodeDict))->toStrictEqual([])

    let dummyItem = Js.Dict.empty()
    dummyItem->Js.Dict.set("foo", "bar"->Json.encodeString)
    expect(decodeResult([dummyItem->Json.encodeDict]->Json.encodeArray))->toStrictEqual([])

    let jsonShopA = mockJsonShop(~id="shop-a-id", ~name="shop-a-name", ~type_="shop-a-type")

    let json = Json.fromObjExn([jsonShopA])
    expect(decodeResult(json))->toStrictEqual([
      {id: "shop-a-id", name: "shop-a-name", type_: Some("shop-a-type")},
    ])

    let json = Json.fromObjExn([dummyItem->Json.encodeDict, jsonShopA])
    expect(decodeResult(json))->toStrictEqual([
      {id: "shop-a-id", name: "shop-a-name", type_: Some("shop-a-type")},
    ])

    let jsonShopB = mockJsonShop(~id="user-b-id", ~name="user-b-name", ~type_="shop-b-type")

    let json = Json.fromObjExn([jsonShopB, jsonShopA])
    expect(decodeResult(json))->toStrictEqual([
      {
        id: "user-b-id",
        name: "user-b-name",
        type_: Some("shop-b-type"),
      },
      {
        id: "shop-a-id",
        name: "shop-a-name",
        type_: Some("shop-a-type"),
      },
    ])
  })
})

describe("QueryAllUsersWithAssociatedShopsRequest", () => {
  let {mergeQueryAllUsersResultWithQueryAllShopsResult} = module(
    AdminImpersonationPage.QueryAllUsersWithAssociatedShopsRequest
  )

  test("mergeQueryAllUsersResultWithQueryAllShopsResult", () => {
    expect(
      mergeQueryAllUsersResultWithQueryAllShopsResult((
        Error(Request.InvalidRequestFailures([])),
        Error(Request.UnexpectedServerError),
      )),
    )->toStrictEqual(Error())

    expect(
      mergeQueryAllUsersResultWithQueryAllShopsResult((
        Error(Request.InvalidRequestFailures([])),
        Ok([]),
      )),
    )->toStrictEqual(Error())

    expect(
      mergeQueryAllUsersResultWithQueryAllShopsResult((
        Ok([]),
        Error(Request.UnexpectedServerError),
      )),
    )->toStrictEqual(Error())

    expect(mergeQueryAllUsersResultWithQueryAllShopsResult((Ok([]), Ok([]))))->toStrictEqual(
      Error(),
    )

    expect(
      mergeQueryAllUsersResultWithQueryAllShopsResult((
        Ok([mockQueryAllUsersRequestItem()]),
        Ok([mockQueryAllShopsRequestItem()]),
      )),
    )->toStrictEqual(
      Ok([
        {
          id: "",
          name: "",
          organizationName: "",
          username: "",
          shops: [],
        },
      ]),
    )

    expect(
      mergeQueryAllUsersResultWithQueryAllShopsResult((
        Ok([
          mockQueryAllUsersRequestItem(~id="user-a-id", ~shopsIds=["shop-id-a", "shop-id-b"], ()),
          mockQueryAllUsersRequestItem(~id="user-b-id", ~shopsIds=["shop-id-c", "shop-id-b"], ()),
        ]),
        Ok([
          mockQueryAllShopsRequestItem(~id="shop-id-a", ~name="shop-a", ~type_="INDEPENDENT", ()),
          mockQueryAllShopsRequestItem(~id="shop-id-b", ~name="shop-b", ()),
          mockQueryAllShopsRequestItem(~id="shop-id-d", ~name="shop-d", ()),
        ]),
      )),
    )->toStrictEqual(
      Ok([
        {
          id: "user-a-id",
          name: "",
          organizationName: "",
          username: "",
          shops: [{name: "shop-a", type_: Some("INDEPENDENT")}, {name: "shop-b", type_: None}],
        },
        {
          id: "user-b-id",
          name: "",
          organizationName: "",
          username: "",
          shops: [{name: "shop-b", type_: None}],
        },
      ]),
    )
  })
})

describe("Reducer", () => {
  open AdminImpersonationPage.Reducer

  let fulfilledState = {
    searchQuery: "dummy-search-query",
    currentPage: 2,
    asyncResult: AsyncData.loading(),
  }

  test("make with SearchQueryChanged(_)", () => {
    expect(make(initialState, SearchQueryChanged("any-search-query")))->toStrictEqual({
      searchQuery: "any-search-query",
      currentPage: 1,
      asyncResult: AsyncResult.notAsked(),
    })
    expect(make(fulfilledState, SearchQueryChanged("any-search-query")))->toStrictEqual({
      searchQuery: "any-search-query",
      currentPage: 1,
      asyncResult: AsyncData.loading(),
    })
  })

  test("make with AsyncResultGet(_)", () => {
    expect(make(initialState, AsyncResultGet(AsyncResult.done(Ok([])))))->toStrictEqual({
      searchQuery: "",
      currentPage: 1,
      asyncResult: AsyncResult.done(Ok([])),
    })
    expect(make(fulfilledState, AsyncResultGet(AsyncResult.done(Ok([])))))->toStrictEqual({
      searchQuery: "dummy-search-query",
      currentPage: 1,
      asyncResult: AsyncResult.done(Ok([])),
    })
  })

  test("make with Paginated(_)", () => {
    expect(make(initialState, Paginated(LegacyPagination.First, 1)))->toStrictEqual({
      searchQuery: "",
      currentPage: 1,
      asyncResult: AsyncResult.notAsked(),
    })
    expect(make(fulfilledState, Paginated(LegacyPagination.First, 1)))->toStrictEqual({
      searchQuery: "dummy-search-query",
      currentPage: 1,
      asyncResult: AsyncResult.loading(),
    })

    expect(make(initialState, Paginated(LegacyPagination.Prev, 1)))->toStrictEqual({
      searchQuery: "",
      currentPage: 1,
      asyncResult: AsyncResult.notAsked(),
    })
    expect(make(fulfilledState, Paginated(LegacyPagination.Prev, 1)))->toStrictEqual({
      searchQuery: "dummy-search-query",
      currentPage: 1,
      asyncResult: AsyncResult.loading(),
    })

    expect(make(initialState, Paginated(LegacyPagination.Next, 3)))->toStrictEqual({
      searchQuery: "",
      currentPage: 2,
      asyncResult: AsyncResult.notAsked(),
    })
    expect(make(fulfilledState, Paginated(LegacyPagination.Next, 2)))->toStrictEqual({
      searchQuery: "dummy-search-query",
      currentPage: 2,
      asyncResult: AsyncResult.loading(),
    })

    expect(make(initialState, Paginated(LegacyPagination.Last, 1)))->toStrictEqual({
      searchQuery: "",
      currentPage: 1,
      asyncResult: AsyncResult.notAsked(),
    })
    expect(make(fulfilledState, Paginated(LegacyPagination.Last, 3)))->toStrictEqual({
      searchQuery: "dummy-search-query",
      currentPage: 3,
      asyncResult: AsyncResult.loading(),
    })
  })
})

testPromise("TableItemShopsCell", async () => {
  let userEvent = TestingLibraryEvent.setup()

  render(<AdminImpersonationPage.TableItemShopsCell shopsNames=[] />)->ignore
  expect(screen->getByTextExn("No shops associated"))->toBeVisible

  render(<AdminImpersonationPage.TableItemShopsCell shopsNames=["shop-a"] />)->ignore
  expect(screen->getByTextExn("shop-a"))->toBeVisible

  let {container} = render(
    <AdminImpersonationPage.TableItemShopsCell shopsNames=["shop-a", "shop-b"] />,
  )
  let textElement = screen->getByTextExn("2 shops")

  expect(textElement)->toBeVisible

  await userEvent->TestingLibraryEvent.hover(container) // hacky user event
  await userEvent->TestingLibraryEvent.hover(textElement)

  let textTooltip = await waitForReturn(() => screen->getByRoleExn(#tooltip))

  expect(textTooltip)->toBeVisible
  expect(textTooltip)->toHaveTextContent("• shop-a • shop-b") // NOTE - newlines ignored here
})

// Some integration tests to ensure basic interactions
// with the other components and gateway api be valuable
// at some point.
todo("Integration test")
