open Vitest

open CatalogVariant__Config

let shopVariants: array<variantInformation> = [
  {
    id: "8c672384-05fd-4f1d-9d62-ef5b78badb81",
    shopId: "0608490f-456e-4807-861a-306407a3c58c",
    shopName: "Vinea",
    updatedAt: Js.Date.fromString("2021-11-02T10:33:04.154Z"),
    status: Some(Active),
    active: true,
    name: "0,75L - 2018",
    formattedName: "100 % Groslot - 0,75L - 2018",
    formattedCapacity: Some("0,75 L"),
    formattedAlcoholVolume: None,
    year: Some("2018"),
    stockKeepingUnit: Some("GRANDGROLBTRG18"),
    priceLookUpCode: Some(23),
    purchasePrice: Some(9.),
    taxRate: 20.,
    ean13: None,
    internalCode: None,
    internalNote: "1,7",
    tastingNote: "",
    capacityUnit: None,
    bulk: false,
  },
  {
    id: "2a50c084-ef3f-4102-a431-15d73c867221",
    shopId: "a4752837-be0b-44bd-8c73-4708c1a76b42",
    shopName: "Entrepôt",
    updatedAt: Js.Date.fromString("2021-11-02T10:32:34.117Z"),
    status: Some(Active),
    active: false,
    name: "0.75L, 2019",
    formattedName: "100 % Groslot - 0.75L, 2019",
    formattedCapacity: Some("0,75 L"),
    year: Some("2019"),
    formattedAlcoholVolume: Some("25°"),
    stockKeepingUnit: Some("GRANDGROLBTRG18"),
    priceLookUpCode: Some(24),
    purchasePrice: Some(6.4),
    taxRate: 20.,
    ean13: None,
    internalCode: None,
    internalNote: "1,7",
    tastingNote: "b",
    capacityUnit: None,
    bulk: false,
  },
  {
    id: "fd0b2f5c-cc02-4907-8658-9d919050356e",
    shopId: "c83706ce-f468-4a71-aa2c-c80297f5b880",
    shopName: "La Cave Spirituelle",
    updatedAt: Js.Date.fromString("2021-11-02T10:32:41.886Z"),
    status: Some(Active),
    active: false,
    name: "2018, 0,75L",
    formattedName: "100 % Groslot - 2018, 0,75L",
    formattedCapacity: Some("0,75 L"),
    formattedAlcoholVolume: None,
    year: Some("2018"),
    stockKeepingUnit: Some("GRANDGROLBTRG18"),
    priceLookUpCode: Some(25),
    purchasePrice: Some(9.),
    taxRate: 20.,
    ean13: None,
    internalCode: None,
    internalNote: "1,7n",
    tastingNote: "",
    capacityUnit: None,
    bulk: false,
  },
]

let variantPrices: array<array<variantRetailPrice>> = [
  [
    {
      id: Some("76bd6ea4-aa04-4e2f-9a84-054ba0ceb30c"),
      priceId: "c399a9d3-2f39-474c-85c2-e0e44258c9f1",
      variantId: "8c672384-05fd-4f1d-9d62-ef5b78badb81",
      shopId: "0608490f-456e-4807-861a-306407a3c58c",
      shopName: "Vinea",
      name: "Tarif Cave",
      valueExcludingTax: 15.,
      valueIncludingTax: 18.,
      taxIncluded: true,
      taxRate: 20.,
      toQuantity: None,
      fromQuantity: None,
      capacityUnit: None,
    },
    {
      id: Some("05a83114-cab2-445e-862b-bc09a7c105cb"),
      priceId: "31741f67-90fe-4101-a760-fa542a835bf0",
      variantId: "8c672384-05fd-4f1d-9d62-ef5b78badb81",
      shopId: "0608490f-456e-4807-861a-306407a3c58c",
      shopName: "Vinea",
      name: "Tarif E-commerce",
      valueExcludingTax: 1.67,
      valueIncludingTax: 2.,
      taxIncluded: true,
      taxRate: 20.,
      toQuantity: None,
      fromQuantity: None,
      capacityUnit: None,
    },
    {
      id: Some("66123bd4-daa8-4d06-bb93-0b70560b0254"),
      priceId: "7e1bf007-bfb4-4e4d-976c-f4830045f990",
      variantId: "8c672384-05fd-4f1d-9d62-ef5b78badb81",
      shopId: "0608490f-456e-4807-861a-306407a3c58c",
      shopName: "Vinea",
      name: "Tarif Pro",
      valueExcludingTax: 2.08,
      valueIncludingTax: 2.19,
      taxIncluded: false,
      taxRate: 5.5,
      toQuantity: None,
      fromQuantity: None,
      capacityUnit: None,
    },
  ],
  [
    {
      id: Some("a79c43c2-6275-4954-a1cb-4ee516031a80"),
      priceId: "a0e024af-f354-4a12-a783-1f8ebc943dad",
      variantId: "2a50c084-ef3f-4102-a431-15d73c867221",
      shopId: "a4752837-be0b-44bd-8c73-4708c1a76b42",
      shopName: "Entrepôt",
      name: "Tarif Cave",
      valueExcludingTax: 9.17,
      valueIncludingTax: 11.,
      taxIncluded: true,
      taxRate: 20.,
      toQuantity: None,
      fromQuantity: None,
      capacityUnit: None,
    },
    {
      id: Some("0b260987-776c-46ec-a52f-ae697a25c1cb"),
      priceId: "31d0da1f-8006-43e3-bd33-7989427fc697",
      variantId: "2a50c084-ef3f-4102-a431-15d73c867221",
      shopId: "a4752837-be0b-44bd-8c73-4708c1a76b42",
      shopName: "Entrepôt",
      name: "Tarif Pro",
      valueExcludingTax: 36.,
      valueIncludingTax: 37.98,
      taxIncluded: false,
      taxRate: 5.5,
      toQuantity: None,
      fromQuantity: None,
      capacityUnit: None,
    },
  ],
  [
    {
      id: Some("a087dc37-945e-4e63-a784-5916c6e876b8"),
      priceId: "dec58b36-116f-4469-8186-1fac60d955fd",
      variantId: "fd0b2f5c-cc02-4907-8658-9d919050356e",
      shopId: "c83706ce-f468-4a71-aa2c-c80297f5b880",
      shopName: "La Cave Spirituelle",
      name: "Tarif Cave",
      valueExcludingTax: 11.67,
      valueIncludingTax: 14.,
      taxIncluded: true,
      taxRate: 20.,
      toQuantity: None,
      fromQuantity: None,
      capacityUnit: None,
    },
    {
      id: Some("dba4567d-5536-4051-8b17-a91a67e021f7"),
      priceId: "0e0bae8b-2fe3-4661-afcf-861f01f8b44a",
      variantId: "fd0b2f5c-cc02-4907-8658-9d919050356e",
      shopId: "c83706ce-f468-4a71-aa2c-c80297f5b880",
      shopName: "La Cave Spirituelle",
      name: "Tarif E-commerce",
      valueExcludingTax: 18.33,
      valueIncludingTax: 22.,
      taxIncluded: true,
      taxRate: 20.,
      toQuantity: None,
      fromQuantity: None,
      capacityUnit: None,
    },
  ],
]

let pricesData: pricesData = {
  prices: {
    __typename: "PriceConnection",
    edges: [
      {
        __typename: "PriceEdge",
        node: {
          __typename: "Price",
          id: "31741f67-90fe-4101-a760-fa542a835bf0",
          name: "Tarif E-commerce",
          taxIncluded: true,
          shop: {
            __typename: "Shop",
            id: "0608490f-456e-4807-861a-306407a3c58c",
          },
        },
      },
      {
        __typename: "PriceEdge",
        node: {
          __typename: "Price",
          id: "7e1bf007-bfb4-4e4d-976c-f4830045f990",
          name: "Tarif Pro",
          taxIncluded: false,
          shop: {
            __typename: "Shop",
            id: "0608490f-456e-4807-861a-306407a3c58c",
          },
        },
      },
      {
        __typename: "PriceEdge",
        node: {
          __typename: "Price",
          id: "c399a9d3-2f39-474c-85c2-e0e44258c9f1",
          name: "Tarif Cave",
          taxIncluded: true,
          shop: {
            __typename: "Shop",
            id: "0608490f-456e-4807-861a-306407a3c58c",
          },
        },
      },
      {
        __typename: "PriceEdge",
        node: {
          __typename: "Price",
          id: "52d794ed-639b-4577-b965-8b86604715e3",
          name: "Tarif E-commerce",
          taxIncluded: true,
          shop: {
            __typename: "Shop",
            id: "a4752837-be0b-44bd-8c73-4708c1a76b42",
          },
        },
      },
      {
        __typename: "PriceEdge",
        node: {
          __typename: "Price",
          id: "31d0da1f-8006-43e3-bd33-7989427fc697",
          name: "Tarif Pro",
          taxIncluded: false,
          shop: {
            __typename: "Shop",
            id: "a4752837-be0b-44bd-8c73-4708c1a76b42",
          },
        },
      },
      {
        __typename: "PriceEdge",
        node: {
          __typename: "Price",
          id: "a0e024af-f354-4a12-a783-1f8ebc943dad",
          name: "Tarif Cave",
          taxIncluded: true,
          shop: {
            __typename: "Shop",
            id: "a4752837-be0b-44bd-8c73-4708c1a76b42",
          },
        },
      },
      {
        __typename: "PriceEdge",
        node: {
          __typename: "Price",
          id: "0e0bae8b-2fe3-4661-afcf-861f01f8b44a",
          name: "Tarif E-commerce",
          taxIncluded: true,
          shop: {
            __typename: "Shop",
            id: "c83706ce-f468-4a71-aa2c-c80297f5b880",
          },
        },
      },
      {
        __typename: "PriceEdge",
        node: {
          __typename: "Price",
          id: "23e0bcbf-3c13-463a-943c-a7284c03dac7",
          name: "Tarif Pro",
          taxIncluded: false,
          shop: {
            __typename: "Shop",
            id: "c83706ce-f468-4a71-aa2c-c80297f5b880",
          },
        },
      },
      {
        __typename: "PriceEdge",
        node: {
          __typename: "Price",
          id: "dec58b36-116f-4469-8186-1fac60d955fd",
          name: "Tarif Cave",
          taxIncluded: true,
          shop: {
            __typename: "Shop",
            id: "c83706ce-f468-4a71-aa2c-c80297f5b880",
          },
        },
      },
    ],
  },
}

describe("makeRetailPricesList", () => {
  let priceLists = shopVariants->makeRetailPricesList(~pricesData, ~variantPrices)

  it("should make an array composed of every pricelists by shop as entries", () => {
    expect(priceLists)->toHaveLength(3)

    expect(
      priceLists->Array.keepMap(pricesByShop => pricesByShop[0]->Option.map(price => price.shopId)),
    )->toStrictEqual(shopVariants->Array.map(variantByShop => variantByShop.shopId))
  })

  it(
    "should append at the end of each pricelist their missing price with an undefined ID and a value of 0",
    () => {
      expect(
        priceLists->Array.keepMap(
          pricesByShop =>
            pricesByShop[pricesByShop->Array.length - 1]->Option.map(price => price.id),
        ),
      )->toStrictEqual([Some("66123bd4-daa8-4d06-bb93-0b70560b0254"), None, None])

      expect(
        priceLists->Array.keepMap(
          pricesByShop =>
            pricesByShop[pricesByShop->Array.length - 1]->Option.map(
              price => price.valueExcludingTax,
            ),
        ),
      )->toStrictEqual([2.08, 0., 0.])
    },
  )
})
