open Vitest
open TestingLibraryReact

// NOTE - this testing uses MSW to intercept https queries and mock response
// but the module should ideally uses IoC: dependancy inversion principle which
// benefits from mapping functions passed as props (which depend on the operation)
// rather than modeling the operation directly to include looping logic itself.

let {mockUser, mockShop} = module(Auth__Mock)

let mockAuthState = (~shops, ~activeShop) => {
  Auth__Types.user: mockUser(),
  shops,
  activeShop,
}

describe("SettingsLabelPrinterListRequest", () => {
  let {mapFutureResult, makeEndpoint, decodeResultItem} = module(
    SettingsLabelPrinterPage.SettingsLabelPrinterListRequest
  )

  it("should make the endpoint with shopId and printersHubName in slug", () => {
    expect(makeEndpoint(~shopId="mock-shop-id"))->toBe(
      Env.gatewayUrl() ++ "/printers-hub/StarPrintersHub/mock-shop-id/printers",
    )
    expect(makeEndpoint(~shopId=""))->toBe(
      Env.gatewayUrl() ++ "/printers-hub/StarPrintersHub//printers",
    )
  })

  it("should decode a printer item", () => {
    let json = Json.fromObjExn({
      "hubName": "mock-hub-name",
      "name": "mock-name",
      "macAddress": "mock-mac-address",
      "config": {
        "id": "mock-config-id",
        "groupId": "mock-config-group-id",
      },
    })
    expect(json->decodeResultItem)->toStrictEqual(
      Some({
        id: "mock-config-id",
        groupId: "mock-config-group-id",
        name: "mock-name",
        hubName: "mock-hub-name",
        macAddress: "mock-mac-address",
      }),
    )

    let json = Json.fromObjExn({
      "hubName": "mock-hub-name",
      "name": "mock-name",
      "macAddress": "mock-mac-address",
    })
    expect(json->decodeResultItem)->toStrictEqual(None)

    let json = Json.fromObjExn({
      "hubName": "mock-hub-name",
      "name": "mock-name",
      "config": {
        "id": "mock-config-id",
        "groupId": "mock-config-group-id",
      },
    })
    expect(json->decodeResultItem)->toStrictEqual(None)
  })

  itFuture("mapFutureResult (1)", () => {
    let future = Future.value(
      Ok(
        Json.fromObjExn([
          {
            "hubName": "mock-hub-name",
            "name": "mock-name",
            "macAddress": "mock-mac-address",
            "config": {
              "id": "mock-config-id",
              "groupId": "mock-config-group-id",
            },
          },
          {
            "hubName": "mock-hub-name",
            "name": "mock-name",
            "macAddress": "mock-mac-address",
            "config": {
              "id": "mock-config-id",
              "groupId": "mock-config-group-id",
            },
          },
        ]),
      ),
    )

    mapFutureResult(future)->Future.tap(
      result =>
        expect(result)->toStrictEqual(
          Ok([
            {
              SettingsLabelPrinterPage.id: "mock-config-id",
              groupId: "mock-config-group-id",
              name: "mock-name",
              hubName: "mock-hub-name",
              macAddress: "mock-mac-address",
            },
            {
              id: "mock-config-id",
              groupId: "mock-config-group-id",
              name: "mock-name",
              hubName: "mock-hub-name",
              macAddress: "mock-mac-address",
            },
          ]),
        ),
    )
  })

  itFuture("mapFutureResult (2)", () =>
    mapFutureResult(Future.value(Ok(Json.fromObjExn([]))))->Future.tap(
      result => expect(result)->toStrictEqual(Ok([])),
    )
  )

  itFuture("mapFutureResult (3)", () =>
    mapFutureResult(Future.value(Error(Request.UnexpectedServerError)))->Future.tap(
      result => expect(result)->toStrictEqual(Error(UnexpectedFailure)),
    )
  )

  itFuture("mapFutureResult (4)", () =>
    mapFutureResult(Future.value(Error(Request.MalformedResponse)))->Future.tap(
      result => expect(result)->toStrictEqual(Error(UnexpectedFailure)),
    )
  )

  itFuture("mapFutureResult (5)", () =>
    mapFutureResult(
      Future.value(
        Error(
          Request.InvalidRequestFailures([
            {kind: "NotFoundShopPrintersHub", message: "error", data: None},
          ]),
        ),
      ),
    )->Future.tap(result => expect(result)->toStrictEqual(Error(NoPrinterFound)))
  )

  itFuture("mapFutureResult (6)", () =>
    mapFutureResult(Future.value(Error(Request.UnexpectedServerError)))->Future.tap(
      result => expect(result)->toStrictEqual(Error(UnexpectedFailure)),
    )
  )

  itFuture("mapFutureResult (7)", () =>
    mapFutureResult(Future.value(Error(Request.MalformedResponse)))->Future.tap(
      result => expect(result)->toStrictEqual(Error(UnexpectedFailure)),
    )
  )

  itFuture("mapFutureResult (8)", () =>
    mapFutureResult(
      Future.value(
        Error(
          Request.InvalidRequestFailures([
            {kind: "NotFoundShopPrintersHub", message: "error", data: None},
          ]),
        ),
      ),
    )->Future.tap(result => expect(result)->toStrictEqual(Error(NoPrinterFound)))
  )
})

describe("SettingsLabelDefaultPrinterIdRequest", () => {
  let {mapFutureResult, makeEndpoint, decodeResult} = module(
    SettingsLabelPrinterPage.SettingsLabelDefaultPrinterIdRequest
  )

  it("should make the endpoint with shopId and printersHubName in slug", () => {
    expect(makeEndpoint(~shopId="mock-shop-id"))->toBe(
      Env.gatewayUrl() ++ "/printers-hub/StarPrintersHub/mock-shop-id/default-printer",
    )
    expect(makeEndpoint(~shopId=""))->toBe(
      Env.gatewayUrl() ++ "/printers-hub/StarPrintersHub//default-printer",
    )
  })

  it("should decode the default printer id", () => {
    let json = Json.fromObjExn({
      "hubName": "mock-hub-name",
      "name": "mock-name",
      "macAddress": "mock-mac-address",
      "config": {
        "id": "mock-config-id",
        "groupId": "mock-config-group-id",
      },
    })
    expect(json->decodeResult)->toStrictEqual(Some("mock-config-id"))

    let json = Json.fromObjExn({
      "config": {
        "id": "mock-config-id",
      },
    })
    expect(json->decodeResult)->toStrictEqual(Some("mock-config-id"))

    let json = Json.fromObjExn({
      "hubName": "mock-hub-name",
      "name": "mock-name",
      "macAddress": "mock-mac-address",
    })
    expect(json->decodeResult)->toStrictEqual(None)
  })

  itFuture("mapFutureResult (1)", () => {
    let future = Future.value(
      Ok(
        Json.fromObjExn({
          "hubName": "mock-hub-name",
          "name": "mock-name",
          "macAddress": "mock-mac-address",
          "config": {
            "id": "mock-config-id",
            "groupId": "mock-config-group-id",
          },
        }),
      ),
    )

    mapFutureResult(future)->Future.tap(
      result => expect(result)->toStrictEqual(Ok(Some("mock-config-id"))),
    )
  })

  itFuture("mapFutureResult (2)", () =>
    mapFutureResult(Future.value(Error(Request.UnexpectedServerError)))->Future.tap(
      result => expect(result)->toStrictEqual(Error(UnexpectedFailure)),
    )
  )

  itFuture("mapFutureResult (3)", () =>
    mapFutureResult(Future.value(Error(Request.MalformedResponse)))->Future.tap(
      result => expect(result)->toStrictEqual(Error(UnexpectedFailure)),
    )
  )

  itFuture("mapFutureResult (4)", () =>
    mapFutureResult(
      Future.value(
        Error(
          Request.InvalidRequestFailures([
            {kind: "NotFoundShopDefaultPrinterConfig", message: "error", data: None},
          ]),
        ),
      ),
    )->Future.tap(result => expect(result)->toStrictEqual(Error(NoPrinterFound)))
  )
})

describe("SettingsLabelPrinterHubSetupRequest", () => {
  let {mapFutureResult, makeEndpoint, decodeResultItem, encodeBodyJson} = module(
    SettingsLabelPrinterPage.SettingsLabelPrinterHubSetupRequest
  )

  it("should make the endpoint with shopId and printersHubName in slug", () => {
    expect(makeEndpoint(~shopId="mock-shop-id"))->toBe(
      Env.gatewayUrl() ++ "/printers-hub/StarPrintersHub/mock-shop-id/setup",
    )
    expect(makeEndpoint(~shopId=""))->toBe(
      Env.gatewayUrl() ++ "/printers-hub/StarPrintersHub//setup",
    )
  })

  it("should return an encoded json", () => {
    let result = encodeBodyJson(~apiKey="mock-api-key")

    expect(result)->toUnsafeStrictEqual({
      "printersHubConfig": {
        "apiKey": "mock-api-key",
      },
    })
  })

  it("should decode a printer item", () => {
    let json = Json.fromObjExn({
      "hubName": "mock-hub-name",
      "name": "mock-name",
      "macAddress": "mock-mac-address",
      "config": {
        "id": "mock-config-id",
        "groupId": "mock-config-group-id",
      },
    })
    expect(json->decodeResultItem)->toStrictEqual(
      Some({
        id: "mock-config-id",
        groupId: "mock-config-group-id",
        name: "mock-name",
        hubName: "mock-hub-name",
        macAddress: "mock-mac-address",
      }),
    )

    let json = Json.fromObjExn({
      "hubName": "mock-hub-name",
      "name": "mock-name",
      "macAddress": "mock-mac-address",
    })
    expect(json->decodeResultItem)->toStrictEqual(None)

    let json = Json.fromObjExn({
      "hubName": "mock-hub-name",
      "name": "mock-name",
      "config": {
        "id": "mock-config-id",
        "groupId": "mock-config-group-id",
      },
    })
    expect(json->decodeResultItem)->toStrictEqual(None)
  })

  itFuture("mapFutureResult (1)", () => {
    let future = Future.value(
      Ok(
        Json.fromObjExn([
          {
            "hubName": "mock-hub-name",
            "name": "mock-name",
            "macAddress": "mock-mac-address",
            "config": {
              "id": "mock-config-id",
              "groupId": "mock-config-group-id",
            },
          },
          {
            "hubName": "mock-hub-name",
            "name": "mock-name",
            "macAddress": "mock-mac-address",
            "config": {
              "id": "mock-config-id",
              "groupId": "mock-config-group-id",
            },
          },
        ]),
      ),
    )

    mapFutureResult(future)->Future.tap(
      result =>
        expect(result)->toStrictEqual(
          Ok([
            {
              SettingsLabelPrinterPage.id: "mock-config-id",
              groupId: "mock-config-group-id",
              name: "mock-name",
              hubName: "mock-hub-name",
              macAddress: "mock-mac-address",
            },
            {
              id: "mock-config-id",
              groupId: "mock-config-group-id",
              name: "mock-name",
              hubName: "mock-hub-name",
              macAddress: "mock-mac-address",
            },
          ]),
        ),
    )
  })

  itFuture("mapFutureResult (2)", () =>
    mapFutureResult(Future.value(Ok(Json.fromObjExn([]))))->Future.tap(
      result => expect(result)->toStrictEqual(Ok([])),
    )
  )

  itFuture("mapFutureResult (3)", () =>
    mapFutureResult(Future.value(Error(Request.UnexpectedServerError)))->Future.tap(
      result => expect(result)->toStrictEqual(Error(UnexpectedFailure)),
    )
  )

  itFuture("mapFutureResult (4)", () =>
    mapFutureResult(Future.value(Error(Request.MalformedResponse)))->Future.tap(
      result => expect(result)->toStrictEqual(Error(UnexpectedFailure)),
    )
  )

  itFuture("mapFutureResult (5)", () =>
    mapFutureResult(
      Future.value(
        Error(
          Request.InvalidRequestFailures([
            {kind: "InvalidPrinterHubConfig", message: "error", data: None},
          ]),
        ),
      ),
    )->Future.tap(result => expect(result)->toStrictEqual(Error(InvalidAPIKey)))
  )

  itFuture("mapFutureResult (6)", () =>
    mapFutureResult(
      Future.value(
        Error(
          Request.InvalidRequestFailures([
            {kind: "PrintersHubPermissionsCheck", message: "error", data: None},
          ]),
        ),
      ),
    )->Future.tap(result => expect(result)->toStrictEqual(Error(InvalidAPIKey)))
  )
})

describe("SettingsLabelPrinterDefaultSelectionRequest", () => {
  let {mapFutureResult, makeEndpoint, encodeBodyJson} = module(
    SettingsLabelPrinterPage.SettingsLabelPrinterDefaultSelectionRequest
  )

  it("should make the endpoint with shopId and printersHubName in slug", () => {
    expect(makeEndpoint(~shopId="mock-shop-id"))->toBe(
      Env.gatewayUrl() ++ "/printers-hub/StarPrintersHub/mock-shop-id/default-printer",
    )
    expect(makeEndpoint(~shopId=""))->toBe(
      Env.gatewayUrl() ++ "/printers-hub/StarPrintersHub//default-printer",
    )
  })

  it("should return an encoded json", () => {
    let result = encodeBodyJson(~id="mock-id", ~groupId="mock-group-id")

    expect(result)->toUnsafeStrictEqual({
      "defaultPrinterDeviceConfig": {
        "id": "mock-id",
        "groupId": "mock-group-id",
      },
    })
  })

  itFuture("mapFutureResult (1)", () =>
    mapFutureResult(Future.value(Ok("OK")))->Future.tap(
      result => expect(result)->toStrictEqual(Ok()),
    )
  )

  itFuture("mapFutureResult (2)", () =>
    mapFutureResult(Future.value(Ok("")))->Future.tap(
      result => expect(result)->toStrictEqual(Error()),
    )
  )

  itFuture("mapFutureResult (3)", () =>
    mapFutureResult(Future.value(Error("error")))->Future.tap(
      result => expect(result)->toStrictEqual(Error()),
    )
  )
})

let userEvent = TestingLibraryEvent.setup()

let toBeAriaDisabled = expect => expect->toHaveAttributeValue("aria-disabled", "true")

testPromise("SettingsLabelPrinterApiKeyEdit", async () => {
  let onSubmit = fn1(ignore)

  let {unmount} = render(
    <SettingsLabelPrinterPage.SettingsLabelPrinterApiKeyEdit
      editionStage=false onSubmit={onSubmit->fn}
    />,
  )

  let apiKeyTextInputElement = screen->getByRoleExn(#textbox)

  expect(apiKeyTextInputElement)->toHaveDisplayValue("")
  expect(apiKeyTextInputElement)->toBeVisible
  expect(apiKeyTextInputElement)->toHaveAttributeValue("placeholder", "Enter API key")

  let saveButtonElement = screen->getByRoleWithOptionsExn(#button, {name: "Save"})

  expect(saveButtonElement)->toBeVisible
  expect(saveButtonElement)->toBeAriaDisabled

  await userEvent->TestingLibraryEvent.type_(apiKeyTextInputElement, "my api key")

  expect(onSubmit)->toHaveBeenCalledTimes(0)
  expect(apiKeyTextInputElement)->toHaveDisplayValue("my api key")
  expect(saveButtonElement)->Vitest.not->toBeAriaDisabled

  await userEvent->TestingLibraryEvent.click(saveButtonElement)

  expect(onSubmit)->toHaveBeenCalledTimes(1)
  expect(onSubmit)->toHaveBeenCalledWith1("my api key")

  onSubmit->mockClear
  unmount()

  let {unmount} = render(
    <SettingsLabelPrinterPage.SettingsLabelPrinterApiKeyEdit
      editionStage=false loading=true onSubmit={onSubmit->fn}
    />,
  )

  let saveButtonElement = screen->getByRoleExn(#button)

  expect(onSubmit)->toHaveBeenCalledTimes(0)
  expect(saveButtonElement)->toHaveTextContent("")
  expect(saveButtonElement)->toBeVisible
  expect(saveButtonElement)->toBeAriaDisabled
  expect(
    within(saveButtonElement)->getByRoleWithOptionsExn(#status, {name: "Loading..."}),
  )->toBeVisible

  onSubmit->mockClear
  unmount()

  let {baseElement} = render(
    <Providers>
      <SettingsLabelPrinterPage.SettingsLabelPrinterApiKeyEdit
        editionStage=true onSubmit={onSubmit->fn}
      />
    </Providers>,
  )

  let editModalElement = baseElement->querySelectorUnsafe("#portals-modal")
  let apiKeyPlaceholderTextElement =
    screen->getByTextExn(
      "• • • • • • • • • • • • • • • • • • • • • • • • • • •",
    )
  let editApiKeyIconButtonElement = screen->getByRoleExn(#button)

  expect(onSubmit)->toHaveBeenCalledTimes(0)
  expect(apiKeyPlaceholderTextElement)->toBeVisible
  expect(editApiKeyIconButtonElement)->toBeVisible

  await userEvent->TestingLibraryEvent.click(editApiKeyIconButtonElement)

  let apiKeyModalTextInputElement = within(editModalElement)->getByRoleExn(#textbox)
  let saveModalButtonElement =
    within(editModalElement)->getByRoleWithOptionsExn(#button, {name: "Save"})

  expect(onSubmit)->toHaveBeenCalledTimes(0)
  expect(editModalElement)->Vitest.not->toBeEmptyDOMElement
  expect(saveModalButtonElement)->toBeVisible
  expect(saveModalButtonElement)->toBeAriaDisabled
  expect(apiKeyModalTextInputElement)->toBeVisible
  expect(apiKeyModalTextInputElement)->toHaveDisplayValue("")
  expect(apiKeyModalTextInputElement)->toHaveAttributeValue("placeholder", "Enter API key")

  await userEvent->TestingLibraryEvent.click(saveModalButtonElement)

  expect(onSubmit)->toHaveBeenCalledTimes(0)

  await userEvent->TestingLibraryEvent.type_(apiKeyModalTextInputElement, "my new api key")

  expect(onSubmit)->toHaveBeenCalledTimes(0)
  expect(apiKeyModalTextInputElement)->toHaveDisplayValue("my new api key")

  await userEvent->TestingLibraryEvent.click(saveModalButtonElement)

  expect(onSubmit)->toHaveBeenCalledTimes(1)
  expect(onSubmit)->toHaveBeenCalledWith1("my new api key")

  // FIXME - modal doesn't close
  // screen->debugElement(editModalElement)
  // expect(editModalElement)->toBeEmptyDOMElement
  // await waitFor(() => expect(editModalElement)->toBeEmptyDOMElement)
})

describe("SettingsLabelPrinterDefaultSelection", () => {
  let {formatPrinterLabel} = module(SettingsLabelPrinterPage.SettingsLabelPrinterDefaultSelection)

  test("formatPrinterLabel", () => {
    expect(formatPrinterLabel(~name="name", ~macAddress="macAddress"))->toBe("name (macAddress)")
    expect(formatPrinterLabel(~name="", ~macAddress="macAddress"))->toBe("Anonymous (macAddress)")
    expect(formatPrinterLabel(~name="name", ~macAddress=""))->toBe("name ()")
    expect(formatPrinterLabel(~name="", ~macAddress=""))->toBe("Anonymous ()")
  })

  testPromise("component", async () => {
    let onSubmit = fn2((_defaultPrinter, _printers) => ())

    let defaultPrinterId = Some("mock-config-id")
    let printers = [
      {
        SettingsLabelPrinterPage.id: "mock-config-id",
        groupId: "mock-config-group-id",
        name: "mock-name",
        hubName: "mock-hub-name",
        macAddress: "mock-mac-address",
      },
    ]
    let {unmount, baseElement} = render(
      <SettingsLabelPrinterPage.SettingsLabelPrinterDefaultSelection
        printers defaultPrinterId onSubmit={onSubmit->fn}
      />,
    )

    let printerNameTextElement = screen->getByTextExn("mock-name (mock-mac-address)")
    let dashboardLinkElement = baseElement->querySelectorUnsafe("a")
    let dashboardLinkTextElement =
      within(dashboardLinkElement)->getByTextExn("StarPrinter Online Dashboard")

    expect(onSubmit)->toHaveBeenCalledTimes(0)
    expect(printerNameTextElement)->toBeVisible
    expect(dashboardLinkElement)->toHaveAttributeValue(
      "href",
      "https://portal.starprinter.online/Dashboard",
    )
    expect(dashboardLinkTextElement)->toBeVisible

    unmount()
    onSubmit->mockClear

    let defaultPrinterId = None
    let printers = [
      {
        SettingsLabelPrinterPage.id: "mock-a-config-id",
        groupId: "mock-a-config-group-id",
        name: "mock-a-name",
        hubName: "mock-a-hub-name",
        macAddress: "mock-a-mac-address",
      },
      {
        SettingsLabelPrinterPage.id: "mock-b-config-id",
        groupId: "mock-b-config-group-id",
        name: "mock-b-name",
        hubName: "mock-b-hub-name",
        macAddress: "mock-b-mac-address",
      },
    ]
    let {unmount, baseElement} = render(
      <SettingsLabelPrinterPage.SettingsLabelPrinterDefaultSelection
        printers defaultPrinterId onSubmit={onSubmit->fn}
      />,
    )

    let printerSelectTriggerButtonElement = screen->getByLabelTextExn("Label printer")
    let dashboardLinkElement = baseElement->querySelectorUnsafe("a")
    let dashboardLinkTextElement =
      within(dashboardLinkElement)->getByTextExn("StarPrinter Online Dashboard")
    let saveButtonElement = screen->getByRoleWithOptionsExn(#button, {name: "Save"})

    expect(onSubmit)->toHaveBeenCalledTimes(0)
    expect(printerSelectTriggerButtonElement)->toBeVisible
    expect(printerSelectTriggerButtonElement)->toHaveTextContent("Select a printer")
    expect(dashboardLinkElement)->toHaveAttributeValue(
      "href",
      "https://portal.starprinter.online/Dashboard",
    )
    expect(dashboardLinkTextElement)->toBeVisible
    expect(saveButtonElement)->toBeVisible
    expect(saveButtonElement)->toBeAriaDisabled

    await userEvent->TestingLibraryEvent.click(printerSelectTriggerButtonElement)

    let listboxElement = screen->getByRoleExn(#listbox)
    let (section1, _section2) = within(listboxElement)->getAllByRoleExn2(#presentation)
    let groupElement = within(section1)->getByRoleExn(#group)
    let (option1, option2) = within(groupElement)->getAllByRoleExn2(#option)

    expect(option1)->toHaveAttributeValue("aria-selected", "false")
    expect(option1)->toHaveTextContent("mock-a-name (mock-a-mac-address)")
    expect(option2)->toHaveAttributeValue("aria-selected", "false")
    expect(option2)->toHaveTextContent("mock-b-name (mock-b-mac-address)")

    await userEvent->TestingLibraryEvent.click(option1)

    expect(onSubmit)->toHaveBeenCalledTimes(0)
    expect(saveButtonElement)->Vitest.not->toBeAriaDisabled

    await userEvent->TestingLibraryEvent.click(saveButtonElement)

    expect(onSubmit)->toHaveBeenCalledTimes(1)
    expect(onSubmit)->toHaveBeenCalledWith2(
      {
        SettingsLabelPrinterPage.id: "mock-a-config-id",
        groupId: "mock-a-config-group-id",
        name: "mock-a-name",
        hubName: "mock-a-hub-name",
        macAddress: "mock-a-mac-address",
      },
      printers,
    )

    unmount()
    onSubmit->mockClear

    let defaultPrinterId = Some("mock-b-config-id")
    let printers = [
      {
        SettingsLabelPrinterPage.id: "mock-a-config-id",
        groupId: "mock-a-config-group-id",
        name: "mock-a-name",
        hubName: "mock-a-hub-name",
        macAddress: "mock-a-mac-address",
      },
      {
        SettingsLabelPrinterPage.id: "mock-b-config-id",
        groupId: "mock-b-config-group-id",
        name: "mock-b-name",
        hubName: "mock-b-hub-name",
        macAddress: "mock-b-mac-address",
      },
    ]
    let _ = render(
      <SettingsLabelPrinterPage.SettingsLabelPrinterDefaultSelection
        printers defaultPrinterId onSubmit={onSubmit->fn}
      />,
    )

    let printerSelectTriggerButtonElement = screen->getByLabelTextExn("Label printer")
    let saveButtonElement = screen->getByRoleWithOptionsExn(#button, {name: "Save"})

    expect(onSubmit)->toHaveBeenCalledTimes(0)
    expect(printerSelectTriggerButtonElement)->toBeVisible
    expect(printerSelectTriggerButtonElement)->toHaveTextContent("mock-b-name (mock-b-mac-address)")
    expect(saveButtonElement)->toBeVisible
    expect(saveButtonElement)->Vitest.not->toBeAriaDisabled

    await userEvent->TestingLibraryEvent.click(printerSelectTriggerButtonElement)

    let listboxElement = screen->getByRoleExn(#listbox)
    let (section1, _section2) = within(listboxElement)->getAllByRoleExn2(#presentation)
    let groupElement = within(section1)->getByRoleExn(#group)
    let (option1, option2) = within(groupElement)->getAllByRoleExn2(#option)

    expect(option1)->toHaveAttributeValue("aria-selected", "false")
    expect(option2)->toHaveAttributeValue("aria-selected", "true")
  })
})

let {setupNodeServer, use, listen, resetHandlers, close, ctxDelayDuration} = module(MSW)
let {
  ctxText,
  ctxUnsafeObject,
  ctxStatus,
  postWithStatus,
  asyncPostWithDelayAndStatus,
  get,
} = module(MSW.Rest)

let mockedPrintersErrorResponseJson = {
  "errors": [
    {
      "message": "no printer found",
      "kind": "NotFoundShopPrintersHub",
    },
  ],
}
let mockedDefaultPrinterErrorResponseJson = {
  "errors": [
    {
      "message": "no default printer found",
      "kind": "NotFoundShopDefaultPrinterConfig",
    },
  ],
}
let mockedSetupErrorResponseJson = {
  "errors": [
    {
      "message": "received invalid api key",
      "kind": "PrintersHubPermissionsCheck",
    },
  ],
}

let shop = mockShop(~id="shop-id", ~name="shop-name", ())
let server = setupNodeServer([
  asyncPostWithDelayAndStatus(
    SettingsLabelPrinterPage.SettingsLabelPrinterHubSetupRequest.makeEndpoint(~shopId=shop.id),
    async (req, res, ctx) => {
      let bodyJson = await req->MSW.Rest.Request.json
      let dict = bodyJson->Json.decodeDict
      let dictConfig = dict->Json.flatDecodeDictFieldDict("printersHubConfig")
      let apiKey = dictConfig->Json.flatDecodeDictFieldString("apiKey")

      if apiKey === Some("mock-api-key") {
        res(.
          ctx->ctxDelayDuration(100),
          ctx->ctxStatus(200),
          ctx->ctxUnsafeObject({
            "data": [
              {
                "hubName": "mock-hub-name",
                "name": "mock-name",
                "macAddress": "mock-mac-address",
                "config": {
                  "id": "mock-config-id",
                  "groupId": "mock-config-group-id",
                },
              },
            ],
          }),
        )
      } else {
        res(.
          ctx->ctxDelayDuration(100),
          ctx->ctxStatus(400),
          ctx->ctxUnsafeObject({
            "errors": [
              {
                "message": "received invalid api key",
                "kind": "PrintersHubPermissionsCheck",
              },
            ],
          }),
        )
      }
    },
  ),
  postWithStatus(
    SettingsLabelPrinterPage.SettingsLabelPrinterDefaultSelectionRequest.makeEndpoint(
      ~shopId=shop.id,
    ),
    (_req, res, ctx) => res(. ctx->ctxStatus(200), ctx->ctxText("OK")),
  ),
])

beforeAll(() => server->listen({onUnhandledRequest: #bypass}))
afterEach(() => server->resetHandlers)
afterAll(() => server->close)

describe("component", () => {
  testPromise("Enter an API key in the field then save it for the first time", async () => {
    let shop = mockShop(~id="shop-id", ~name="shop-name", ())
    let auth = mockAuthState(~activeShop=Some(shop), ~shops=[shop])

    server->use([
      get(
        SettingsLabelPrinterPage.SettingsLabelPrinterListRequest.makeEndpoint(~shopId=shop.id),
        (_req, res, ctx) => res(ctx->ctxUnsafeObject(mockedPrintersErrorResponseJson)),
      ),
      get(
        SettingsLabelPrinterPage.SettingsLabelDefaultPrinterIdRequest.makeEndpoint(~shopId=shop.id),
        (_req, res, ctx) => res(ctx->ctxUnsafeObject(mockedPrintersErrorResponseJson)),
      ),
    ])

    let {baseElement} = render(
      <Providers auth={Logged(auth)}>
        <SettingsLabelPrinterPage />
      </Providers>,
    )

    expect(baseElement)->toBeVisible
    expect(screen->getByTextExn("Loading..."))->toBeVisible

    let apiKeyTextInputElement = await waitForReturn(() => screen->getByRoleExn(#textbox))
    let saveButtonElement = screen->getByRoleWithOptionsExn(#button, {name: "Save"})

    expect(apiKeyTextInputElement)->toBeVisible
    expect(apiKeyTextInputElement)->toHaveTextContent("")
    expect(saveButtonElement)->toBeVisible
    expect(saveButtonElement)->toBeAriaDisabled

    await userEvent->TestingLibraryEvent.type_(apiKeyTextInputElement, "mock-errored-api-key")

    expect(saveButtonElement)->Vitest.not->toBeAriaDisabled

    await userEvent->TestingLibraryEvent.click(saveButtonElement)

    expect(
      within(saveButtonElement)->getByRoleWithOptionsExn(#status, {name: "Loading..."}),
    )->toBeVisible
    expect(saveButtonElement)->toHaveTextContent("")
    expect(saveButtonElement)->toBeAriaDisabled

    await waitFor(
      () =>
        expect(
          screen->getByTextExn(
            "The API key is not recognized. You can try to generate a new one or contact our support.",
          ),
        )->toBeVisible,
    )

    expect(apiKeyTextInputElement)->toHaveTextContent("")
    expect(saveButtonElement)->toHaveTextContent("Save")

    server->use([
      get(
        SettingsLabelPrinterPage.SettingsLabelDefaultPrinterIdRequest.makeEndpoint(~shopId=shop.id),
        (_req, res, ctx) => res(ctx->ctxText("OK")),
      ),
    ])

    await userEvent->TestingLibraryEvent.clear(apiKeyTextInputElement)
    await userEvent->TestingLibraryEvent.type_(apiKeyTextInputElement, "mock-api-key")
    await userEvent->TestingLibraryEvent.click(saveButtonElement)

    expect(
      within(saveButtonElement)->getByRoleWithOptionsExn(#status, {name: "Loading..."}),
    )->toBeVisible

    await waitFor(
      () =>
        expect(screen->getByTextExn("The printer has been successfully registered."))->toBeVisible,
    )
  })

  testPromise("Switching to another shop", async () => {
    let shopA = mockShop(~id="shop-id-a", ~name="shop-name-a", ())
    let shopB = mockShop(~id="shop-id-b", ~name="shop-name-b", ())
    let auth = mockAuthState(~activeShop=Some(shopA), ~shops=[shopA, shopB])

    server->use([
      get(
        SettingsLabelPrinterPage.SettingsLabelPrinterListRequest.makeEndpoint(~shopId=shopA.id),
        (_req, res, ctx) => res(ctx->ctxUnsafeObject(mockedPrintersErrorResponseJson)),
      ),
      get(
        SettingsLabelPrinterPage.SettingsLabelPrinterListRequest.makeEndpoint(~shopId=shopB.id),
        (_req, res, ctx) => res(ctx->ctxUnsafeObject(mockedPrintersErrorResponseJson)),
      ),
    ])

    let {baseElement} = render(
      <Providers auth={Logged(auth)}>
        <SettingsLabelPrinterPage />
      </Providers>,
    )

    expect(baseElement)->toBeVisible
    expect(screen->getByTextExn("Loading..."))->toBeVisible

    let apiKeyTextInputElement = await waitForReturn(() => screen->getByRoleExn(#textbox))
    let saveButtonElement = screen->getByRoleWithOptionsExn(#button, {name: "Save"})

    expect(apiKeyTextInputElement)->toBeVisible
    expect(apiKeyTextInputElement)->toHaveDisplayValue("")
    expect(saveButtonElement)->toBeVisible
    expect(saveButtonElement)->toBeAriaDisabled

    await userEvent->TestingLibraryEvent.type_(apiKeyTextInputElement, "mock-api-key")

    expect(saveButtonElement)->Vitest.not->toBeAriaDisabled
    expect(apiKeyTextInputElement)->toHaveDisplayValue("mock-api-key")

    let shopSelectTriggerElement = screen->getByLabelTextExn("Shop")

    expect(shopSelectTriggerElement)->toHaveTextContent("shop-name-a")

    await userEvent->TestingLibraryEvent.click(shopSelectTriggerElement)

    let shopSelectListElement = screen->getByRoleExn(#listbox)
    let (section1, _section2) = within(shopSelectListElement)->getAllByRoleExn2(#presentation)
    let groupElement = within(section1)->getByRoleExn(#group)
    let (_option1, option2) = within(groupElement)->getAllByRoleExn2(#option)

    await userEvent->TestingLibraryEvent.click(option2)

    expect(shopSelectTriggerElement)->toHaveTextContent("shop-name-b")
    expect(apiKeyTextInputElement)->Vitest.not->toBeVisible

    // FIXME - only SVG appears this time even with request delay
    // expect(screen->getByTextExn("Loading..."))->toBeVisible
    expect(baseElement->querySelectorUnsafe("svg"))->toBeVisible

    let apiKeyTextInputElement = await waitForReturn(() => screen->getByRoleExn(#textbox))

    expect(apiKeyTextInputElement)->toBeVisible
    expect(apiKeyTextInputElement)->toHaveDisplayValue("")
  })

  testPromise("Editing the API key", async () => {
    let shop = mockShop(~id="shop-id", ~name="shop-name", ())
    let auth = mockAuthState(~activeShop=Some(shop), ~shops=[shop])

    server->use([
      get(
        SettingsLabelPrinterPage.SettingsLabelPrinterListRequest.makeEndpoint(~shopId=shop.id),
        (_req, res, ctx) =>
          res(
            ctx->ctxUnsafeObject({
              "data": [
                {
                  "hubName": "mock-hub-name",
                  "name": "mock-name",
                  "macAddress": "mock-mac-address",
                  "config": {
                    "id": "mock-config-id",
                    "groupId": "mock-config-group-id",
                  },
                },
              ],
            }),
          ),
      ),
      get(
        SettingsLabelPrinterPage.SettingsLabelDefaultPrinterIdRequest.makeEndpoint(~shopId=shop.id),
        (_req, res, ctx) =>
          res(
            ctx->ctxUnsafeObject({
              "data": {
                "hubName": "mock-hub-name",
                "name": "mock-name",
                "macAddress": "mock-mac-address",
                "config": {
                  "id": "mock-config-id",
                  "groupId": "mock-config-group-id",
                },
              },
            }),
          ),
      ),
    ])

    let {baseElement} = render(
      <Providers auth={Logged(auth)}>
        <SettingsLabelPrinterPage />
      </Providers>,
    )

    expect(baseElement)->toBeVisible
    expect(screen->getByTextExn("Loading..."))->toBeVisible

    let editModalElement = baseElement->querySelectorUnsafe("#portals-modal")
    let editApiKeyIconButtonElement = await waitForReturn(
      () => screen->getByRoleWithOptionsExn(#button, {name: "edit_light"}),
    )

    expect(editApiKeyIconButtonElement)->toBeVisible

    await userEvent->TestingLibraryEvent.click(editApiKeyIconButtonElement)

    let apiKeyModalTextInputElement = within(editModalElement)->getByRoleExn(#textbox)
    let saveModalButtonElement =
      within(editModalElement)->getByRoleWithOptionsExn(#button, {name: "Save"})

    expect(apiKeyModalTextInputElement)->toBeVisible
    expect(apiKeyModalTextInputElement)->toHaveDisplayValue("")
    expect(saveModalButtonElement)->toBeAriaDisabled

    await userEvent->TestingLibraryEvent.type_(apiKeyModalTextInputElement, "mock-api-key")

    expect(apiKeyModalTextInputElement)->toHaveDisplayValue("mock-api-key")
    expect(saveModalButtonElement)->Vitest.not->toBeAriaDisabled

    await userEvent->TestingLibraryEvent.click(saveModalButtonElement)

    await waitFor(
      () =>
        expect(screen->getByTextExn("The printer has been successfully registered."))->toBeVisible,
    )
  })

  testPromise("Setting the default printer among a list of printers", async () => {
    let shop = mockShop(~id="shop-id", ~name="shop-name", ())
    let auth = mockAuthState(~activeShop=Some(shop), ~shops=[shop])

    server->use([
      get(
        SettingsLabelPrinterPage.SettingsLabelPrinterListRequest.makeEndpoint(~shopId=shop.id),
        (_req, res, ctx) =>
          res(
            ctx->ctxUnsafeObject({
              "data": [
                {
                  "hubName": "mock-hub-name-a",
                  "name": "mock-name-a",
                  "macAddress": "mock-mac-address-a",
                  "config": {
                    "id": "mock-config-id-a",
                    "groupId": "mock-config-group-id-a",
                  },
                },
                {
                  "hubName": "mock-hub-name-b",
                  "name": "mock-name-b",
                  "macAddress": "mock-mac-address-b",
                  "config": {
                    "id": "mock-config-id-b",
                    "groupId": "mock-config-group-id-b",
                  },
                },
              ],
            }),
          ),
      ),
      get(
        SettingsLabelPrinterPage.SettingsLabelDefaultPrinterIdRequest.makeEndpoint(~shopId=shop.id),
        (_req, res, ctx) => res(ctx->ctxUnsafeObject(mockedDefaultPrinterErrorResponseJson)),
      ),
    ])

    let {baseElement} = render(
      <Providers auth={Logged(auth)}>
        <SettingsLabelPrinterPage />
      </Providers>,
    )

    expect(baseElement)->toBeVisible
    expect(screen->getByTextExn("Loading..."))->toBeVisible

    let printerSelectTriggerButtonElement = await waitForReturn(
      () => screen->getByRoleWithOptionsExn(#button, {name: "Label printer"}),
    )
    let saveButtonElement = screen->getByRoleWithOptionsExn(#button, {name: "Save"})

    expect(printerSelectTriggerButtonElement)->toBeVisible
    expect(printerSelectTriggerButtonElement)->toHaveTextContent("Select a printer")
    expect(saveButtonElement)->toBeVisible
    expect(saveButtonElement)->toBeAriaDisabled

    await userEvent->TestingLibraryEvent.click(printerSelectTriggerButtonElement)

    let shopSelectListElement = screen->getByRoleExn(#listbox)
    let (section1, _section2) = within(shopSelectListElement)->getAllByRoleExn2(#presentation)
    let groupElement = within(section1)->getByRoleExn(#group)
    let (option1, option2) = within(groupElement)->getAllByRoleExn2(#option)

    expect(option1)->toHaveTextContent("mock-name-a (mock-mac-address-a)")
    expect(option2)->toHaveTextContent("mock-name-b (mock-mac-address-b)")

    await userEvent->TestingLibraryEvent.click(option2)

    expect(printerSelectTriggerButtonElement)->toHaveTextContent("mock-name-b (mock-mac-address-b)")
    expect(saveButtonElement)->Vitest.not->toBeAriaDisabled

    server->use([
      get(
        SettingsLabelPrinterPage.SettingsLabelDefaultPrinterIdRequest.makeEndpoint(~shopId=shop.id),
        (_req, res, ctx) =>
          res(
            ctx->ctxUnsafeObject({
              "data": {
                "hubName": "mock-hub-name-b",
                "name": "mock-name-b",
                "macAddress": "mock-mac-address-b",
                "config": {
                  "id": "mock-config-id-b",
                  "groupId": "mock-config-group-id-b",
                },
              },
            }),
          ),
      ),
    ])

    await userEvent->TestingLibraryEvent.click(saveButtonElement)

    // FIXME - works with delay upon the default printer GET request but then can't waitFor success notification.
    // expect(within(saveButtonElement)->getByLabelTextExn("spinner"))->toBeVisible
    // expect(saveButtonElement)->toBeAriaDisabled

    await waitFor(
      () =>
        expect(
          screen->getByTextExn("The default printer has been successfully registered."),
        )->toBeVisible,
    )
  })
})
