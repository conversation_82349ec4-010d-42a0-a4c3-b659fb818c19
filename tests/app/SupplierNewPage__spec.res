open Vitest

let mockValues = (
  ~shopId="",
  ~companyName="",
  ~contactLastName="",
  ~contactFirstName="",
  ~locationLabel="",
  ~locationRecipient="",
  (),
) => {
  SupplierNewPage.SupplierNewFormLenses.shopId,
  companyName,
  email: "",
  phoneNumber: "",
  mobileNumber: "",
  intraCommunityVat: "",
  siretNumber: "",
  internalCode: "",
  note: "",
  contactFirstName,
  contactLastName,
  contactCivility: #NEUTRAL,
  contactPosition: "",
  contactEmail: "",
  contactPhoneNumber: "",
  contactMobileNumber: "",
  locationLabel,
  locationRecipient,
  locationAddress: "",
  locationPostalCode: "",
  locationCity: "",
  locationCountry: "",
}

test("stripEmptyString", () => {
  let {stripEmptyString} = module(SupplierNewPage)

  expect(stripEmptyString(""))->toBe(None)
  expect(stripEmptyString(" "))->toBe(None)
  expect(stripEmptyString("1"))->toBe(Some("1"))
  expect(stripEmptyString("abc"))->toBe(Some("abc"))
})

test("supplierNewFormInitialValues", () => {
  let {supplierNewFormInitialValues} = module(SupplierNewPage)

  expect(supplierNewFormInitialValues(~shopId="shop-id"))->toStrictEqual({
    SupplierNewPage.SupplierNewFormLenses.shopId: "shop-id",
    companyName: "",
    email: "",
    phoneNumber: "",
    mobileNumber: "",
    intraCommunityVat: "",
    siretNumber: "",
    internalCode: "",
    note: "",
    contactFirstName: "",
    contactLastName: "",
    contactCivility: #NEUTRAL,
    contactPosition: "",
    contactEmail: "",
    contactPhoneNumber: "",
    contactMobileNumber: "",
    locationLabel: "",
    locationRecipient: "",
    locationPostalCode: "",
    locationCity: "",
    locationCountry: "France",
    locationAddress: "",
  })
})

test("supplierNewFormSchema", () => {
  module SupplierNewFormSchema = SupplierNewPage.SupplierNewForm.Schema
  let {supplierNewFormSchema: schema} = module(SupplierNewPage)
  let {validate} = module(SupplierNewPage.SupplierNewForm)

  let values = mockValues()
  let expectedErrors = [(SupplierNewFormSchema.Field(CompanyName), "Please fulfill this field.")]
  expect(validate(~schema, ~values))->toStrictEqual(Error(expectedErrors))

  let values = mockValues(~companyName="mock", ())
  expect(validate(~schema, ~values))->toStrictEqual(Ok())

  let values = mockValues(
    ~companyName="mock",
    ~contactFirstName="mock",
    ~locationRecipient="mock",
    (),
  )
  let expectedErrors = [
    (SupplierNewFormSchema.Field(ContactLastName), "This value must not be empty."),
    (Field(LocationLabel), "This value must not be empty."),
  ]
  expect(validate(~schema, ~values))->toStrictEqual(Error(expectedErrors))

  let values = mockValues(
    ~companyName="mock",
    ~contactFirstName="mock",
    ~contactLastName="mock",
    ~locationRecipient="mock",
    ~locationLabel="",
    (),
  )
  let expectedErrors = [
    (SupplierNewFormSchema.Field(LocationLabel), "This value must not be empty."),
  ]
  expect(validate(~schema, ~values))->toStrictEqual(Error(expectedErrors))

  let values = mockValues(
    ~companyName="mock",
    ~contactFirstName="mock",
    ~contactLastName="mock",
    ~locationRecipient="mock",
    ~locationLabel="mock",
    (),
  )
  expect(validate(~schema, ~values))->toStrictEqual(Ok())
})

test("createSupplierMutationVariablesFromFormValues", () => {
  let {createSupplierMutationVariablesFromFormValues} = module(SupplierNewPage)

  let values = mockValues()
  expect(createSupplierMutationVariablesFromFormValues(values))->toStrictEqual({
    supplierInput: {
      companyName: "",
      shopId: "",
      intraCommunityVat: None,
      siretNumber: None,
      phoneNumber: None,
      mobileNumber: None,
      email: None,
      note: None,
      internalCode: None,
    },
    contactsInput: None,
    locationsInput: None,
  })

  let values = mockValues(~locationLabel="mock", ~contactLastName="mock", ())
  expect(createSupplierMutationVariablesFromFormValues(values))->toStrictEqual({
    supplierInput: {
      companyName: "",
      shopId: "",
      intraCommunityVat: None,
      siretNumber: None,
      phoneNumber: None,
      mobileNumber: None,
      email: None,
      note: None,
      internalCode: None,
    },
    contactsInput: Some([
      {
        lastName: "mock",
        firstName: None,
        position: None,
        email: None,
        phoneNumber: None,
        mobileNumber: None,
        isDefault: true,
        civility: Some(#NEUTRAL),
      },
    ]),
    locationsInput: Some([
      {
        label: "mock",
        recipient: None,
        address: None,
        postalCode: None,
        city: None,
        country: None,
        defaults: [#DELIVERY, #BILLING],
      },
    ]),
  })
})

// Some integration tests to ensure basic interactions
// with the other components and GraphQL might be valuable
// at some point.
todo("Integration test")
