open Vitest
open TestingLibraryReact

describe("SupplierTotalPurchasedCard", () => {
  module SupplierTotalPurchasedCard = SupplierShowPage.SupplierTotalPurchasedCard

  it("should render with empty value", () => {
    let _ = <SupplierTotalPurchasedCard value="" />->render
    let element = screen->getByTextExn("€0.00")
    expect(element)->toBeVisible
  })

  it("should render with bad value", () => {
    let {rerender} = <SupplierTotalPurchasedCard value="abc" />->render
    let element = screen->getByTextExn("€0.00")
    expect(element)->toBeVisible
    let _ = <SupplierTotalPurchasedCard value="^^'" />->rerender
    let element = screen->getByTextExn("€0.00")
    expect(element)->toBeVisible
    let _ = <SupplierTotalPurchasedCard value="$$" />->rerender
    let element = screen->getByTextExn("€0.00")
    expect(element)->toBeVisible
  })

  it("should render with 0 value", () => {
    let {rerender} = <SupplierTotalPurchasedCard value="0,00€" />->render
    let element = screen->getByTextExn("€0.00")
    expect(element)->toBeVisible
    let _ = <SupplierTotalPurchasedCard value="0.0€" />->rerender
    let element = screen->getByTextExn("€0.00")
    expect(element)->toBeVisible
    let _ = <SupplierTotalPurchasedCard value="0€" />->rerender
    let element = screen->getByTextExn("€0.00")
    expect(element)->toBeVisible
  })

  it("should render with amount value", () => {
    let {rerender} = <SupplierTotalPurchasedCard value="12,98€" />->render
    let element = screen->getByTextExn("€12.98")
    expect(element)->toBeVisible
    let _ = <SupplierTotalPurchasedCard value="0.92€" />->rerender
    let element = screen->getByTextExn("€0.92")
    expect(element)->toBeVisible
    let _ = <SupplierTotalPurchasedCard value="10.123€" />->rerender
    let element = screen->getByTextExn("€10.12")
    expect(element)->toBeVisible
    let _ = <SupplierTotalPurchasedCard value="10.129€" />->rerender
    let element = screen->getByTextExn("€10.13")
    expect(element)->toBeVisible
  })
})

describe("SupplierContactCard", () => {
  module SupplierContactCard = SupplierShowPage.SupplierContactCard

  let supplierEditContactRoute = SupplierRoutes.editContactRoute

  test("title", () => {
    let {title} = module(SupplierContactCard)

    let expectedTitle = "Mr. The President"
    expect(title(~civility=#MR, ~firstName="The", ~lastName="President", ()))->toBe(expectedTitle)
    expect(title(~lastName="President", ()))->toBe("President")
    expect(title(~firstName="The", ~lastName="President", ()))->toBe("The President")
    expect(title(~civility=#MR, ~lastName="President", ()))->toBe("Mr. President")
  })

  describe("component", () => {
    let mockItem = (~lastName) => {SupplierContactCard.lastName: lastName}

    it(
      "should render no contact informations",
      () => {
        let _ = render(
          <Providers>
            <SupplierContactCard id="mock-id" supplierEditContactRoute items=[] />
          </Providers>,
        )

        expect(screen->getByTextExn("Contact information not specified"))->toBeVisible
      },
    )

    it(
      "should render multiple contacts informations",
      () => {
        let _ = render(
          <Providers>
            <SupplierContactCard
              id="mock-id"
              supplierEditContactRoute
              items=[mockItem(~lastName="President"), mockItem(~lastName="Dude")]
            />
          </Providers>,
        )

        expect(screen->queryByText("Contact information not specified"))->toBeNone
        expect(screen->getByTextExn("President"))->toBeVisible
        expect(screen->queryByText("Dude"))->toBeNone
      },
    )

    it(
      "should render one contact information",
      () => {
        let _ = render(
          <Providers>
            <SupplierContactCard
              id="mock-id" supplierEditContactRoute items=[mockItem(~lastName="President")]
            />
          </Providers>,
        )

        expect(screen->queryByText("Contact information not specified"))->toBeNone
        expect(screen->getByTextExn("President"))->toBeVisible
      },
    )
  })
})

describe("SupplierLocationCard", () => {
  module SupplierLocationCard = SupplierShowPage.SupplierLocationCard

  test("postalCodeCityCountry", () => {
    let {postalCodeCityCountry} = module(SupplierLocationCard)

    expect(postalCodeCityCountry())->toBeNone

    expect(postalCodeCityCountry(~postalCode="", ()))->toBeNone
    expect(postalCodeCityCountry(~city="", ()))->toBeNone
    expect(postalCodeCityCountry(~country="", ()))->toBeNone

    expect(postalCodeCityCountry(~postalCode="1234", ()))->toBe(Some("1234"))
    expect(postalCodeCityCountry(~postalCode="1234", ~city="", ~country="", ()))->toBe(Some("1234"))
    expect(postalCodeCityCountry(~city="Paris", ()))->toBe(Some("Paris"))
    expect(postalCodeCityCountry(~postalCode="", ~city="Paris", ~country="", ()))->toBe(
      Some("Paris"),
    )
    expect(postalCodeCityCountry(~postalCode="", ~city="", ~country="Utopia", ()))->toBe(
      Some("Utopia"),
    )
    expect(postalCodeCityCountry(~country="Utopia", ()))->toBe(Some("Utopia"))

    let city = "Island"
    let country = "Utopia"
    let postalCode = "0"
    expect(postalCodeCityCountry(~postalCode="", ~city, ~country, ()))->toBe(Some("Island, Utopia"))
    expect(postalCodeCityCountry(~city, ~country, ()))->toBe(Some("Island, Utopia"))
    expect(postalCodeCityCountry(~postalCode, ~city, ~country, ()))->toBe(Some("0 Island, Utopia"))
    expect(postalCodeCityCountry(~postalCode, ~city="", ~country, ()))->toBe(Some("0 Utopia"))
  })
})

todo("SupplierInformationsCard")
todo("SupplierStatusBadge")
todo("SupplierMenuEditingActions")
todo("SupplierMenuReadingActions")
todo("SupplierMenuActions")
todo("SupplierOrderTableCard")

let supplierBaseRoute = SupplierRoutes.baseRoute
let supplierEditRoute = SupplierRoutes.editRoute
let supplierEditLocationRoute = SupplierRoutes.editLocationRoute
let supplierEditContactRoute = SupplierRoutes.editContactRoute

test("should load the supplier", () => {
  render(
    <Providers>
      <SupplierShowPage
        id="mock-id"
        supplierBaseRoute
        supplierEditRoute
        supplierEditLocationRoute
        supplierEditContactRoute
      />
    </Providers>,
  )->ignore

  expect(screen->getByTextExn("Loading..."))->toBeVisible
})

module SupplierQuery = SupplierShowPage.SupplierQuery

let mockTypename = () => "mock-typename"

let mockSupplier = (~id) => {
  SupplierQuery.Raw.id,
  companyName: "",
  updatedAt: Js.Date.make()->Scalar.Datetime.serialize,
  archivedAt: Js.Nullable.null,
  intraCommunityVat: Js.Nullable.null,
  internalCode: Js.Nullable.null,
  phoneNumber: Js.Nullable.null,
  mobileNumber: Js.Nullable.null,
  email: Js.Nullable.null,
  note: Js.Nullable.return("My old note"->Js.Json.string),
  siretNumber: Js.Nullable.null,
  formattedOrdersTotalAmountIncludingTaxes: "",
  shop: {id: "mock-shop-id", name: "mock-shop-name", __typename: mockTypename()},
  contacts: {
    edges: [],
    __typename: mockTypename(),
  },
  locations: {
    edges: [],
    __typename: mockTypename(),
  },
  orders: {
    edges: [],
    pageInfo: {
      hasNextPage: Js.Nullable.return(false),
      __typename: mockTypename(),
    },
    __typename: mockTypename(),
  },
  __typename: mockTypename(),
}

let gatewayLink = MSW.GraphQL.makeLink("http://localhost/graphql")
let server = MSW.setupNodeServer([
  gatewayLink->MSWHelpers.apolloQueryExn(module(SupplierShowPage.SupplierQuery), (req, res, ctx) =>
    res(
      ctx->MSW.GraphQL.ctxData({
        supplier: Js.Nullable.return(mockSupplier(~id=req.variables.id)),
      }),
    )
  ),
])

beforeAll(() => server->MSW.listen({onUnhandledRequest: #warn}))
afterEach(() => server->MSW.resetHandlers)
afterAll(() => server->MSW.close)

testPromise("should toggle submit/discard button on note field changing", async () => {
  let userEvent = TestingLibraryEvent.setup()

  let _ = render(
    <Providers>
      <SupplierShowPage
        id="mock-id"
        supplierBaseRoute
        supplierEditRoute
        supplierEditLocationRoute
        supplierEditContactRoute
      />
    </Providers>,
  )

  let noteField = await waitForReturn(() => screen->getByRoleExn(#textbox))

  expect(noteField)->toBeVisible
  expect(noteField)->toHaveValue("My old note")
  expect(screen->queryByRoleWithOptions(#button, {name: "Discard"}))->toBeNone
  expect(screen->queryByRoleWithOptions(#button, {name: "Save"}))->toBeNone

  await userEvent->TestingLibraryEvent.typeWithOptions(
    noteField,
    "My note",
    {initialSelectionStart: 0, initialSelectionEnd: 11},
  )

  let (discardButtonTop, discardButtonBottom) =
    screen->getAllByRoleWithOptionsExn2(#button, {name: "Discard"})
  expect(noteField)->toBeVisible
  expect(noteField)->toHaveValue("My note")
  expect(discardButtonTop)->toBeVisible
  expect(discardButtonBottom)->toBeVisible
  let (saveButtonTop, saveButtonBottom) =
    screen->getAllByRoleWithOptionsExn2(#button, {name: "Save"})
  expect(saveButtonTop)->toBeVisible
  expect(saveButtonBottom)->toBeVisible

  await userEvent->TestingLibraryEvent.click(discardButtonTop)

  expect(noteField)->toBeVisible
  expect(noteField)->toHaveValue("My old note")
  expect(screen->queryByRoleWithOptions(#button, {name: "Discard"}))->toBeNone
  expect(screen->queryByRoleWithOptions(#button, {name: "Save"}))->toBeNone
})

// Some integration tests to ensure basic interactions
// with the other components and GraphQL might be valuable
// at some point.
todo("should archive")
todo("should unarchive")
todo("should update supplier note")
