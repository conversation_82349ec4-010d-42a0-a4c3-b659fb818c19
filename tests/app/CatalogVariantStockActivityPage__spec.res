open Vitest
open TestingLibraryReact

let {mockShop} = module(Auth__Mock)

let mockedUuid = Uuid.make()->Uuid.toString

let mockRow = (~id="", ()) => {
  CatalogVariantStockActivityPage.Row.id,
  formattedKind: "",
  kind: #SALE,
  reason: None,
  quantity: 0,
  variantCapacityPrecision: None,
  variantCapacityUnit: None,
  description: None,
  date: Js.Date.fromFloat(0.),
  shopName: "",
  deviceName: "",
}

let mockTypename = () => ""

let mockQueryDataEdge = (
  ~id="",
  ~createdAt=Js.Date.fromFloat(0.),
  ~quantity=0,
  ~kind=#SALE,
  ~reason=None,
  ~comment=None,
  ~variantCapacityPrecision=None,
  ~variantCapacityUnit=None,
  ~variantBulk=None,
  ~shopName="",
  ~deviceName="",
  ~deviceSlug="",
  (),
) => {
  CatalogVariantStockActivityPage.Query.node: {
    id,
    createdAt,
    quantity,
    kind,
    reason,
    comment,
    variant: Some({
      capacityPrecision: variantCapacityPrecision,
      capacityUnit: variantCapacityUnit,
      bulk: variantBulk,
      __typename: mockTypename(),
    }),
    shop: {
      name: shopName,
      __typename: mockTypename(),
    },
    device: {
      name: deviceName,
      slug: deviceSlug,
      __typename: mockTypename(),
    },
    __typename: mockTypename(),
  },
  __typename: mockTypename(),
}

let mockQueryDataPageInfo = (~startCursor=?, ~endCursor=?, ()) => {
  CatalogVariantStockActivityPage.Query.startCursor,
  endCursor,
  __typename: mockTypename(),
}

let mockQueryData = (~edges=[], ~pageInfo=mockQueryDataPageInfo(), ~totalCount=0, ()) => {
  CatalogVariantStockActivityPage.Query.stockActivitiesByVariantCku: {
    edges,
    pageInfo,
    totalCount,
    __typename: mockTypename(),
  },
}

let mockQueryVariablesFilterBy = () =>
  CatalogVariantStockActivityPage.Query.makeInputObjectInputStockActivitiesQueryFilter()

let mockQueryVariables = (~cku=""->Json.encodeString, ()) => {
  CatalogVariantStockActivityPage.Query.cku,
  first: None,
  last: None,
  before: None,
  after: None,
  filterBy: None,
  search: None,
}

let mockStateFilters = (~shop=?, ~dateRange=?, ~kind=?, ~reason=?, ()) => {
  CatalogVariantStockActivityPage.Filters.shop,
  dateRange,
  kind,
  reason,
}

let mockState = (
  ~filters=mockStateFilters(),
  ~currentPage=1,
  ~previousPage=-1,
  ~searchQuery=?,
  (),
) => {
  Scaffold.filters,
  connectionArguments: {},
  currentPage,
  previousPage,
  searchQuery,
}

describe("Scaffolded", () => {
  todo("useFiltersJsonCodec")

  test("makeQueryVariables", () => {
    let {makeQueryVariables} = module(CatalogVariantStockActivityPage.Scaffolded)

    expect(makeQueryVariables(mockQueryVariables(), ~connectionArguments={}, ()))->toStrictEqual({
      CatalogVariantStockActivityPage.Query.cku: ""->Json.encodeString,
      first: None,
      last: None,
      before: None,
      after: None,
      filterBy: None,
      search: None,
    })

    expect(
      makeQueryVariables(
        mockQueryVariables(~cku=mockedUuid->Scalar.CKU.serialize, ()),
        ~connectionArguments={
          first: 1,
          last: 2,
          before: "before",
          after: "after",
        },
        ~search="search",
        ~filterBy=mockQueryVariablesFilterBy(),
        (),
      ),
    )->toStrictEqual({
      CatalogVariantStockActivityPage.Query.cku: mockedUuid->Json.encodeString,
      first: Some(1),
      last: Some(2),
      before: Some("before"),
      after: Some("after"),
      search: Some("search"),
      filterBy: Some(
        CatalogVariantStockActivityPage.Query.makeInputObjectInputStockActivitiesQueryFilter(),
      ),
    })
  })

  test("makeQueryVariablesFilterBy", () => {
    let {makeQueryVariablesFilterBy} = module(CatalogVariantStockActivityPage.Scaffolded)

    let filters = {
      CatalogVariantStockActivityPage.Filters.shop: None,
      dateRange: None,
      kind: None,
      reason: None,
    }

    expect(makeQueryVariablesFilterBy(filters))->toStrictEqual({
      CatalogVariantStockActivityPage.Query.shopIds: None,
      date: None,
      kind: None,
      reason: None,
    })

    let filters = {
      CatalogVariantStockActivityPage.Filters.shop: Some(mockShop(~id="mock-shop-id", ())),
      dateRange: None,
      kind: None,
      reason: Some(#TRADE_IN_SUPPLIER),
    }

    expect(makeQueryVariablesFilterBy(filters))->toStrictEqual({
      CatalogVariantStockActivityPage.Query.shopIds: Some({_in: ["mock-shop-id"]}),
      date: None,
      kind: None,
      reason: Some({_in: ["TRADE_IN_SUPPLIER"]}),
    })

    let filters = {
      CatalogVariantStockActivityPage.Filters.shop: Some(mockShop(~id="mock-shop-id", ())),
      dateRange: Some((Js.Date.fromFloat(0.), Js.Date.fromFloat(1.))),
      kind: None,
      reason: None,
    }

    expect(makeQueryVariablesFilterBy(filters))->toStrictEqual({
      CatalogVariantStockActivityPage.Query.shopIds: Some({_in: ["mock-shop-id"]}),
      date: Some({
        _before: None,
        _after: None,
        _between: Some([
          Js.Date.fromFloat(0.)->Scalar.Datetime.serialize,
          Js.Date.fromFloat(1.)->Scalar.Datetime.serialize,
        ]),
      }),
      kind: None,
      reason: None,
    })

    let filters = {
      CatalogVariantStockActivityPage.Filters.shop: None,
      dateRange: Some((Js.Date.fromFloat(-1.), Js.Date.fromFloat(1.))),
      kind: Some(#DELIVERY),
      reason: Some(#TASTING),
    }

    expect(makeQueryVariablesFilterBy(filters))->toStrictEqual({
      CatalogVariantStockActivityPage.Query.shopIds: None,
      date: Some({
        _before: None,
        _after: None,
        _between: Some([
          Js.Date.fromFloat(-1.)->Scalar.Datetime.serialize,
          Js.Date.fromFloat(1.)->Scalar.Datetime.serialize,
        ]),
      }),
      kind: Some({
        _in: Some(["DELIVERY"]),
        _notIn: None,
      }),
      reason: Some({_in: ["TASTING"]}),
    })
  })

  test("totalCountFromQueryData", () => {
    let {totalCountFromQueryData} = module(CatalogVariantStockActivityPage.Scaffolded)

    let queryData = mockQueryData()
    expect(totalCountFromQueryData(queryData))->toBe(0)

    let queryData = mockQueryData(~totalCount=1, ())
    expect(totalCountFromQueryData(queryData))->toBe(1)

    let queryData = mockQueryData(~totalCount=100, ())
    expect(totalCountFromQueryData(queryData))->toBe(100)

    let queryData = mockQueryData(~totalCount=-1, ())
    expect(totalCountFromQueryData(queryData))->toBe(-1)
  })

  test("cursorsFromQueryData", () => {
    let {cursorsFromQueryData} = module(CatalogVariantStockActivityPage.Scaffolded)

    let queryData = mockQueryData()
    expect(cursorsFromQueryData(queryData))->toStrictEqual((None, None))

    let queryData = mockQueryData(~pageInfo=mockQueryDataPageInfo(~startCursor="start", ()), ())
    expect(cursorsFromQueryData(queryData))->toStrictEqual((Some("start"), None))

    let queryData = mockQueryData(
      ~pageInfo=mockQueryDataPageInfo(~startCursor="start", ~endCursor="end", ()),
      (),
    )
    expect(cursorsFromQueryData(queryData))->toStrictEqual((Some("start"), Some("end")))

    let queryData = mockQueryData(~pageInfo=mockQueryDataPageInfo(~endCursor="end", ()), ())
    expect(cursorsFromQueryData(queryData))->toStrictEqual((None, Some("end")))
  })

  test("rowsFromQueryDataAndState", () => {
    let {rowsFromQueryDataAndState} = module(CatalogVariantStockActivityPage.Scaffolded)

    let queryData = mockQueryData()
    expect(rowsFromQueryDataAndState(queryData, mockState()))->toStrictEqual([])

    let queryData = mockQueryData(~edges=[mockQueryDataEdge()], ())
    expect(rowsFromQueryDataAndState(queryData, mockState()))->toStrictEqual([
      {
        CatalogVariantStockActivityPage.Row.id: "",
        formattedKind: "Sale",
        kind: #SALE,
        reason: None,
        quantity: 0,
        variantCapacityPrecision: None,
        variantCapacityUnit: None,
        description: None,
        date: Js.Date.fromFloat(0.),
        shopName: "",
        deviceName: " - ",
      },
    ])

    let queryData = mockQueryData(
      ~edges=[
        mockQueryDataEdge(
          ~id="mock-id",
          ~createdAt=Js.Date.fromFloat(1.),
          ~quantity=5,
          ~kind=#LOSS,
          ~reason=Some(#DAMAGE),
          ~comment=Some("mock-comment"),
          ~variantCapacityPrecision=Some(0),
          ~variantCapacityUnit=Some("mock-variant-capacity-precision"),
          ~variantBulk=Some(false),
          ~shopName="mock-shop-name",
          ~deviceName="mock-device-name",
          ~deviceSlug="mock-device-slug",
          (),
        ),
      ],
      (),
    )
    expect(rowsFromQueryDataAndState(queryData, mockState()))->toStrictEqual([
      {
        CatalogVariantStockActivityPage.Row.id: "mock-id",
        formattedKind: "Loss",
        kind: #LOSS,
        reason: Some(#DAMAGE),
        quantity: 5,
        variantCapacityPrecision: None,
        variantCapacityUnit: None,
        description: Some("mock-comment"),
        date: Js.Date.fromFloat(1.),
        shopName: "mock-shop-name",
        deviceName: "mock-device-slug - mock-device-name",
      },
    ])

    let queryData = mockQueryData(
      ~edges=[
        mockQueryDataEdge(
          ~variantBulk=Some(true),
          ~variantCapacityPrecision=Some(0),
          ~variantCapacityUnit=Some("mock-variant-capacity-precision"),
          (),
        ),
        mockQueryDataEdge(
          ~variantBulk=Some(true),
          ~variantCapacityPrecision=Some(0),
          ~variantCapacityUnit=None,
          (),
        ),
        mockQueryDataEdge(
          ~variantBulk=Some(true),
          ~variantCapacityPrecision=None,
          ~variantCapacityUnit=Some("mock-variant-capacity-precision"),
          (),
        ),
      ],
      (),
    )
    expect(rowsFromQueryDataAndState(queryData, mockState()))->toStrictEqual([
      {
        CatalogVariantStockActivityPage.Row.id: "",
        variantCapacityPrecision: Some(0),
        variantCapacityUnit: Some("mock-variant-capacity-precision"),
        formattedKind: "Sale",
        kind: #SALE,
        reason: None,
        quantity: 0,
        description: None,
        date: Js.Date.fromFloat(0.),
        shopName: "",
        deviceName: " - ",
      },
      {
        id: "",
        variantCapacityPrecision: Some(0),
        variantCapacityUnit: None,
        formattedKind: "Sale",
        kind: #SALE,
        reason: None,
        quantity: 0,
        description: None,
        date: Js.Date.fromFloat(0.),
        shopName: "",
        deviceName: " - ",
      },
      {
        id: "",
        variantCapacityPrecision: None,
        variantCapacityUnit: Some("mock-variant-capacity-precision"),
        formattedKind: "Sale",
        kind: #SALE,
        reason: None,
        quantity: 0,
        description: None,
        date: Js.Date.fromFloat(0.),
        shopName: "",
        deviceName: " - ",
      },
    ])
  })

  test("keyExtractor", () => {
    let {keyExtractor} = module(CatalogVariantStockActivityPage.Scaffolded)

    let row = mockRow()
    expect(keyExtractor(row))->toBe("")

    let row = mockRow(~id="mock-id", ())
    expect(keyExtractor(row))->toBe("mock-id")
  })
})

test("use", () => {
  let wrapper = props => <Providers> {props["children"]} </Providers>

  let initialState = CatalogVariantStockActivityPage.Scaffolded.makeInitialState(
    ~filters={
      shop: Some(mockShop()),
      kind: None,
      reason: None,
      dateRange: None,
    },
  )

  let {result} = renderHookWithOptions(
    () => CatalogVariantStockActivityPage.use(~initialState),
    ~options={wrapper: wrapper},
  )

  let (state, dispatch) = result.current

  expect(state)->toStrictEqual({
    filters: {
      shop: Some(mockShop()),
      kind: None,
      reason: None,
      dateRange: None,
    },
    connectionArguments: {
      first: 10,
    },
    currentPage: 1,
    previousPage: -1,
    searchQuery: None,
  })

  act(() => dispatch(FiltersUpdated(prev => {...prev, kind: Some(#SALE)})))

  let (state, _) = result.current

  expect(state)->toStrictEqual({
    filters: {
      shop: Some(mockShop()),
      kind: Some(#SALE),
      reason: None,
      dateRange: None,
    },
    connectionArguments: {
      first: 10,
    },
    currentPage: 1,
    previousPage: -1,
    searchQuery: None,
  })

  act(() =>
    dispatch(FiltersUpdated(prev => {...prev, kind: Some(#LOSS), reason: Some(#TRADE_IN_SUPPLIER)}))
  )

  let (state, _) = result.current

  expect(state)->toStrictEqual({
    filters: {
      shop: Some(mockShop()),
      kind: Some(#LOSS),
      reason: Some(#TRADE_IN_SUPPLIER),
      dateRange: None,
    },
    connectionArguments: {
      first: 10,
    },
    currentPage: 1,
    previousPage: -1,
    searchQuery: None,
  })

  act(() => dispatch(FiltersUpdated(prev => {...prev, kind: Some(#DELIVERY)})))

  let (state, _) = result.current

  expect(state)->toStrictEqual({
    filters: {
      shop: Some(mockShop()),
      kind: Some(#DELIVERY),
      reason: None,
      dateRange: None,
    },
    connectionArguments: {
      first: 10,
    },
    currentPage: 1,
    previousPage: -1,
    searchQuery: None,
  })

  act(() => dispatch(FiltersUpdated(prev => {...prev, reason: Some(#DAMAGE)})))

  let (state, _) = result.current

  expect(state)->toStrictEqual({
    filters: {
      shop: Some(mockShop()),
      kind: Some(#DELIVERY),
      reason: None,
      dateRange: None,
    },
    connectionArguments: {
      first: 10,
    },
    currentPage: 1,
    previousPage: -1,
    searchQuery: None,
  })
})

// Some integration tests to ensure basic interactions
// with the other components and GraphQL might be valuable
// at some point.
todo("Integration test")
