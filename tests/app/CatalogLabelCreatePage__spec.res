open Vitest

describe("CatalogLabelCreateReducer", () => {
  open CatalogLabelCreatePage.CatalogLabelCreateReducer

  todo("defaultPriceIdFromPriceList")
  todo("isErrored")

  test("reducer", () => {
    let initialStateLabelSheetForm = {
      CatalogLabelCreatePage.CatalogLabelCreateLabelSheetForm.submitResult: NotAsked,
      values: CatalogLabelCreatePage.CatalogLabelCreateLabelSheetForm.initialValues(
        ~priceId="mock-price-id-0"->Uuid.unsafeFromString,
        ~productBarcodeDisplayed=false,
        ~missingProductBarcodeGenerated=false,
        ~productCode=CatalogLabel.ProductCode.Hidden,
        ~labelFormat=CatalogLabel.Sheet.LabelFormat.Grid21,
        ~printOffset=1,
        ~borderEnabled=false,
        ~sort=CatalogLabel.Sheet.LabelSort.Unsorted,
        (),
      ),
      errorMessages: {},
    }
    let initialState = {
      CatalogLabelCreatePage.priceList: [
        {
          id: "mock-price-id-0"->Uuid.unsafeFromString,
          name: "Price ID 0",
          default: true,
        },
        {
          id: "mock-price-id-1"->Uuid.unsafeFromString,
          name: "Price ID 1",
          default: false,
        },
      ],
      form: LabelSheet(initialStateLabelSheetForm),
    }

    expect(
      reducer(
        initialState,
        LabelSheetEdited({
          ...CatalogLabelCreatePage.CatalogLabelCreateLabelSheetForm.initialValues(
            ~priceId="mock-price-id-0"->Uuid.unsafeFromString,
            ~productBarcodeDisplayed=false,
            ~missingProductBarcodeGenerated=false,
            ~priceDisplayed=false,
            ~producerDisplayed=false,
            ~alcoholVolumeDisplayed=false,
            ~productCode=Hidden,
            ~labelFormat=Grid21,
            ~printOffset=1,
            ~borderEnabled=false,
            ~sort=Unsorted,
            (),
          ),
          productValues: Picked([]),
        }),
      ),
    )->toStrictEqual({
      {
        priceList: initialState.priceList,
        form: LabelSheet({
          submitResult: NotAsked,
          values: {
            priceId: "mock-price-id-0"->Uuid.unsafeFromString,
            productBarcodeDisplayed: false,
            missingProductBarcodeGenerated: false,
            priceDisplayed: false,
            producerDisplayed: false,
            alcoholVolumeDisplayed: false,
            productCode: Hidden,
            labelFormat: Grid21,
            printOffset: 1,
            borderEnabled: false,
            sort: Unsorted,
            productValues: Picked([]),
          },
          errorMessages: {
            printOffset: Obj.magic(None),
            productValues: "Please select at least one product.",
          },
        }),
      }
    })

    expect(
      reducer(
        initialState,
        LabelSheetEdited({
          ...CatalogLabelCreatePage.CatalogLabelCreateLabelSheetForm.initialValues(
            ~priceId="mock-price-id-0"->Uuid.unsafeFromString,
            ~productBarcodeDisplayed=true,
            ~missingProductBarcodeGenerated=true,
            ~priceDisplayed=true,
            ~producerDisplayed=true,
            ~alcoholVolumeDisplayed=true,
            ~productCode=SKU,
            ~labelFormat=Grid64,
            ~printOffset=3,
            ~borderEnabled=true,
            ~sort=AscProductName,
            (),
          ),
          productValues: All,
        }),
      ),
    )->toStrictEqual({
      {
        priceList: initialState.priceList,
        form: LabelSheet({
          submitResult: NotAsked,
          values: {
            priceId: "mock-price-id-0"->Uuid.unsafeFromString,
            productBarcodeDisplayed: true,
            missingProductBarcodeGenerated: true,
            priceDisplayed: true,
            producerDisplayed: true,
            alcoholVolumeDisplayed: true,
            productCode: SKU,
            labelFormat: Grid64,
            printOffset: 3,
            borderEnabled: true,
            sort: AscProductName,
            productValues: All,
          },
          errorMessages: {
            printOffset: Obj.magic(None),
            productValues: Obj.magic(None),
          },
        }),
      }
    })

    expect(
      reducer(
        initialState,
        LabelSheetEdited({
          ...CatalogLabelCreatePage.CatalogLabelCreateLabelSheetForm.initialValues(
            ~priceId="mock-price-id-0"->Uuid.unsafeFromString,
            ~productBarcodeDisplayed=false,
            ~missingProductBarcodeGenerated=true,
            (),
          ),
          productValues: Picked([]),
        }),
      ),
    )->toStrictEqual({
      {
        priceList: initialState.priceList,
        form: LabelSheet({
          submitResult: NotAsked,
          values: {
            priceId: "mock-price-id-0"->Uuid.unsafeFromString,
            productBarcodeDisplayed: true,
            missingProductBarcodeGenerated: true,
            priceDisplayed: true,
            producerDisplayed: false,
            alcoholVolumeDisplayed: false,
            productCode: Hidden,
            labelFormat: Grid21,
            printOffset: 1,
            borderEnabled: false,
            sort: Unsorted,
            productValues: Picked([]),
          },
          errorMessages: {
            printOffset: Obj.magic(None),
            productValues: "Please select at least one product.",
          },
        }),
      }
    })

    expect(
      reducer(
        {
          ...initialState,
          form: LabelSheet({
            ...initialStateLabelSheetForm,
            values: {
              ...initialStateLabelSheetForm.values,
              productBarcodeDisplayed: true,
              missingProductBarcodeGenerated: true,
            },
          }),
        },
        LabelSheetEdited({
          ...CatalogLabelCreatePage.CatalogLabelCreateLabelSheetForm.initialValues(
            ~priceId="mock-price-id-0"->Uuid.unsafeFromString,
            ~productBarcodeDisplayed=false,
            ~missingProductBarcodeGenerated=true,
            (),
          ),
          productValues: Picked([]),
        }),
      ),
    )->toStrictEqual({
      {
        priceList: initialState.priceList,
        form: LabelSheet({
          submitResult: NotAsked,
          values: {
            priceId: "mock-price-id-0"->Uuid.unsafeFromString,
            productBarcodeDisplayed: false,
            missingProductBarcodeGenerated: false,
            priceDisplayed: true,
            producerDisplayed: false,
            alcoholVolumeDisplayed: false,
            productCode: Hidden,
            labelFormat: Grid21,
            printOffset: 1,
            borderEnabled: false,
            sort: Unsorted,
            productValues: Picked([]),
          },
          errorMessages: {
            printOffset: Obj.magic(None),
            productValues: "Please select at least one product.",
          },
        }),
      }
    })

    expect(
      reducer(
        initialState,
        LabelSheetEdited({
          ...CatalogLabelCreatePage.CatalogLabelCreateLabelSheetForm.initialValues(
            ~priceId="mock-price-id-0"->Uuid.unsafeFromString,
            (),
          ),
          productValues: Picked([
            {
              productVariantId: "mock-product-variant-id",
              printingQuantity: 0,
              productInformation: {
                name: "mock-product-name",
                description: "mock-product-description",
                internalCode: None,
                stockKeepingUnit: None,
              },
            },
          ]),
        }),
      ),
    )->toStrictEqual({
      {
        priceList: initialState.priceList,
        form: LabelSheet({
          submitResult: NotAsked,
          values: {
            priceId: "mock-price-id-0"->Uuid.unsafeFromString,
            productBarcodeDisplayed: false,
            missingProductBarcodeGenerated: false,
            priceDisplayed: true,
            producerDisplayed: false,
            alcoholVolumeDisplayed: false,
            productCode: Hidden,
            labelFormat: Grid21,
            printOffset: 1,
            borderEnabled: false,
            sort: Unsorted,
            productValues: Picked([
              {
                productVariantId: "mock-product-variant-id",
                printingQuantity: 0,
                productInformation: {
                  name: "mock-product-name",
                  description: "mock-product-description",
                  internalCode: None,
                  stockKeepingUnit: None,
                },
              },
            ]),
          },
          errorMessages: {
            printOffset: Obj.magic(None),
            productValues: Obj.magic(None),
          },
        }),
      }
    })

    expect(
      reducer(
        initialState,
        LabelSheetEdited({
          ...CatalogLabelCreatePage.CatalogLabelCreateLabelSheetForm.initialValues(
            ~priceId="mock-price-id-0"->Uuid.unsafeFromString,
            ~labelFormat=Grid21,
            ~printOffset=22,
            (),
          ),
          productValues: All,
        }),
      ),
    )->toStrictEqual({
      {
        priceList: initialState.priceList,
        form: LabelSheet({
          submitResult: NotAsked,
          values: {
            priceId: "mock-price-id-0"->Uuid.unsafeFromString,
            productBarcodeDisplayed: false,
            missingProductBarcodeGenerated: false,
            priceDisplayed: true,
            producerDisplayed: false,
            alcoholVolumeDisplayed: false,
            productCode: Hidden,
            labelFormat: Grid21,
            printOffset: 22,
            borderEnabled: false,
            sort: Unsorted,
            productValues: All,
          },
          errorMessages: {
            printOffset: "This number must range between 1 and 21 inclusive.",
            productValues: Obj.magic(None),
          },
        }),
      }
    })

    expect(
      reducer(
        initialState,
        LabelSheetEdited({
          ...CatalogLabelCreatePage.CatalogLabelCreateLabelSheetForm.initialValues(
            ~priceId="mock-price-id-0"->Uuid.unsafeFromString,
            ~labelFormat=Grid64,
            ~printOffset=65,
            (),
          ),
          productValues: Picked([]),
        }),
      ),
    )->toStrictEqual({
      {
        priceList: initialState.priceList,
        form: LabelSheet({
          submitResult: NotAsked,
          values: {
            priceId: "mock-price-id-0"->Uuid.unsafeFromString,
            productBarcodeDisplayed: false,
            missingProductBarcodeGenerated: false,
            priceDisplayed: true,
            producerDisplayed: false,
            alcoholVolumeDisplayed: false,
            productCode: Hidden,
            labelFormat: Grid64,
            printOffset: 65,
            borderEnabled: false,
            sort: Unsorted,
            productValues: Picked([]),
          },
          errorMessages: {
            printOffset: "This number must range between 1 and 64 inclusive.",
            productValues: "Please select at least one product.",
          },
        }),
      }
    })

    expect(
      reducer(
        {
          ...initialState,
          form: LabelSheet({
            ...initialStateLabelSheetForm,
            values: {
              ...initialStateLabelSheetForm.values,
              productValues: All,
            },
          }),
        },
        GenerateLabelsSubmitted(NotAsked),
      ),
    )->toStrictEqual({
      {
        priceList: initialState.priceList,
        form: LabelSheet({
          submitResult: NotAsked,
          values: {
            priceId: "mock-price-id-0"->Uuid.unsafeFromString,
            productBarcodeDisplayed: false,
            missingProductBarcodeGenerated: false,
            priceDisplayed: true,
            producerDisplayed: false,
            alcoholVolumeDisplayed: false,
            productCode: Hidden,
            labelFormat: Grid21,
            printOffset: 1,
            borderEnabled: false,
            sort: Unsorted,
            productValues: All,
          },
          errorMessages: {
            printOffset: Obj.magic(None),
            productValues: Obj.magic(None),
          },
        }),
      }
    })

    expect(
      reducer(
        {
          ...initialState,
          form: LabelSheet({
            ...initialStateLabelSheetForm,
            values: {
              ...initialStateLabelSheetForm.values,
              productValues: All,
            },
          }),
        },
        GenerateLabelsSubmitted(Loading),
      ),
    )->toStrictEqual({
      {
        priceList: initialState.priceList,
        form: LabelSheet({
          submitResult: Loading,
          values: {
            priceId: "mock-price-id-0"->Uuid.unsafeFromString,
            productBarcodeDisplayed: false,
            missingProductBarcodeGenerated: false,
            priceDisplayed: true,
            producerDisplayed: false,
            alcoholVolumeDisplayed: false,
            productCode: Hidden,
            labelFormat: Grid21,
            printOffset: 1,
            borderEnabled: false,
            sort: Unsorted,
            productValues: All,
          },
          errorMessages: {
            printOffset: Obj.magic(None),
            productValues: Obj.magic(None),
          },
        }),
      }
    })

    expect(
      reducer(
        {
          ...initialState,
          form: LabelSheet({
            ...initialStateLabelSheetForm,
            values: {
              ...initialStateLabelSheetForm.values,
              productValues: Picked([]),
            },
          }),
        },
        GenerateLabelsSubmitted(Loading),
      ),
    )->toStrictEqual({
      {
        priceList: initialState.priceList,
        form: LabelSheet({
          submitResult: Done(
            Error(
              "There are some errors in the form, please correct them before trying to send it again.",
            ),
          ),
          values: {
            priceId: "mock-price-id-0"->Uuid.unsafeFromString,
            productBarcodeDisplayed: false,
            missingProductBarcodeGenerated: false,
            priceDisplayed: true,
            producerDisplayed: false,
            alcoholVolumeDisplayed: false,
            productCode: Hidden,
            labelFormat: Grid21,
            printOffset: 1,
            borderEnabled: false,
            sort: Unsorted,
            productValues: Picked([]),
          },
          errorMessages: {
            printOffset: Obj.magic(None),
            productValues: "Please select at least one product.",
          },
        }),
      }
    })

    expect(
      reducer(
        {
          ...initialState,
          form: LabelSheet({
            ...initialStateLabelSheetForm,
            values: {
              ...initialStateLabelSheetForm.values,
              labelFormat: Grid21,
              printOffset: 22,
              productValues: All,
            },
          }),
        },
        GenerateLabelsSubmitted(Loading),
      ),
    )->toStrictEqual({
      {
        priceList: initialState.priceList,
        form: LabelSheet({
          submitResult: Done(
            Error(
              "There are some errors in the form, please correct them before trying to send it again.",
            ),
          ),
          values: {
            priceId: "mock-price-id-0"->Uuid.unsafeFromString,
            productBarcodeDisplayed: false,
            missingProductBarcodeGenerated: false,
            priceDisplayed: true,
            producerDisplayed: false,
            alcoholVolumeDisplayed: false,
            productCode: Hidden,
            labelFormat: Grid21,
            printOffset: 22,
            borderEnabled: false,
            sort: Unsorted,
            productValues: All,
          },
          errorMessages: {
            printOffset: "This number must range between 1 and 21 inclusive.",
            productValues: Obj.magic(None),
          },
        }),
      }
    })

    let initialStateLabelPrintForm = {
      CatalogLabelCreatePage.CatalogLabelCreateLabelPrintForm.submitResult: NotAsked,
      values: CatalogLabelCreatePage.CatalogLabelCreateLabelPrintForm.initialValues(
        ~priceId="mock-price-id-0"->Uuid.unsafeFromString,
        ~productBarcodeDisplayed=false,
        ~missingProductBarcodeGenerated=false,
        ~priceDisplayed=true,
        ~producerDisplayed=false,
        ~alcoholVolumeDisplayed=false,
        ~productCode=Hidden,
        ~labelFormat=Label31x22,
        (),
      ),
      errorMessages: {},
      settingsOnly: None,
    }
    let initialState = {
      CatalogLabelCreatePage.priceList: [
        {
          id: "mock-price-id-0"->Uuid.unsafeFromString,
          name: "Price ID 0",
          default: true,
        },
      ],
      form: LabelPrint(initialStateLabelPrintForm),
    }

    expect(
      reducer(
        initialState,
        LabelPrintEdited({
          ...CatalogLabelCreatePage.CatalogLabelCreateLabelPrintForm.initialValues(
            ~priceId="mock-price-id-0"->Uuid.unsafeFromString,
            ~productBarcodeDisplayed=false,
            ~missingProductBarcodeGenerated=false,
            ~priceDisplayed=true,
            ~producerDisplayed=false,
            ~alcoholVolumeDisplayed=false,
            ~productCode=Hidden,
            ~labelFormat=Label31x22,
            (),
          ),
          productValues: Picked([]),
        }),
      ),
    )->toStrictEqual({
      {
        priceList: initialState.priceList,
        form: LabelPrint({
          submitResult: NotAsked,
          values: {
            priceId: "mock-price-id-0"->Uuid.unsafeFromString,
            productBarcodeDisplayed: false,
            missingProductBarcodeGenerated: false,
            priceDisplayed: true,
            producerDisplayed: false,
            alcoholVolumeDisplayed: false,
            productCode: Hidden,
            labelFormat: Label31x22,
            productValues: Picked([]),
          },
          errorMessages: {
            productValues: "Please select at least one product.",
          },
          settingsOnly: None,
        }),
      }
    })

    expect(
      reducer(
        initialState,
        LabelPrintEdited({
          ...CatalogLabelCreatePage.CatalogLabelCreateLabelPrintForm.initialValues(
            ~priceId="mock-price-id-0"->Uuid.unsafeFromString,
            ~productBarcodeDisplayed=false,
            ~missingProductBarcodeGenerated=false,
            ~priceDisplayed=false,
            ~producerDisplayed=false,
            ~alcoholVolumeDisplayed=false,
            ~productCode=Hidden,
            ~labelFormat=Label31x22,
            (),
          ),
          productValues: All,
        }),
      ),
    )->toStrictEqual({
      {
        priceList: initialState.priceList,
        form: LabelPrint({
          submitResult: NotAsked,
          values: {
            priceId: "mock-price-id-0"->Uuid.unsafeFromString,
            productBarcodeDisplayed: false,
            missingProductBarcodeGenerated: false,
            priceDisplayed: false,
            producerDisplayed: false,
            alcoholVolumeDisplayed: false,
            productCode: Hidden,
            labelFormat: Label31x22,
            productValues: All,
          },
          errorMessages: {
            productValues: Obj.magic(None),
          },
          settingsOnly: None,
        }),
      }
    })

    expect(
      reducer(
        initialState,
        LabelPrintEdited({
          ...CatalogLabelCreatePage.CatalogLabelCreateLabelPrintForm.initialValues(
            ~priceId="mock-price-id-0"->Uuid.unsafeFromString,
            (),
          ),
          productValues: Picked([
            {
              productVariantId: "mock-product-variant-id",
              printingQuantity: 0,
              productInformation: {
                name: "mock-product-name",
                description: "mock-product-description",
                internalCode: None,
                stockKeepingUnit: None,
              },
            },
          ]),
        }),
      ),
    )->toStrictEqual({
      {
        priceList: initialState.priceList,
        form: LabelPrint({
          submitResult: NotAsked,
          values: {
            priceId: "mock-price-id-0"->Uuid.unsafeFromString,
            productBarcodeDisplayed: false,
            missingProductBarcodeGenerated: false,
            priceDisplayed: true,
            producerDisplayed: false,
            alcoholVolumeDisplayed: false,
            productCode: Hidden,
            labelFormat: Label31x22,
            productValues: Picked([
              {
                productVariantId: "mock-product-variant-id",
                printingQuantity: 0,
                productInformation: {
                  name: "mock-product-name",
                  description: "mock-product-description",
                  internalCode: None,
                  stockKeepingUnit: None,
                },
              },
            ]),
          },
          errorMessages: {
            productValues: Obj.magic(None),
          },
          settingsOnly: None,
        }),
      }
    })

    expect(
      reducer(
        initialState,
        LabelPrintEdited({
          ...CatalogLabelCreatePage.CatalogLabelCreateLabelPrintForm.initialValues(
            ~priceId="mock-price-id-0"->Uuid.unsafeFromString,
            ~productBarcodeDisplayed=true,
            ~missingProductBarcodeGenerated=true,
            ~priceDisplayed=true,
            ~producerDisplayed=true,
            ~alcoholVolumeDisplayed=true,
            ~productCode=SKU,
            ~labelFormat=Label57x19,
            (),
          ),
          productValues: All,
        }),
      ),
    )->toStrictEqual({
      {
        priceList: initialState.priceList,
        form: LabelPrint({
          submitResult: NotAsked,
          values: {
            priceId: "mock-price-id-0"->Uuid.unsafeFromString,
            productBarcodeDisplayed: true,
            missingProductBarcodeGenerated: true,
            priceDisplayed: true,
            producerDisplayed: true,
            alcoholVolumeDisplayed: true,
            productCode: SKU,
            labelFormat: Label57x19,
            productValues: All,
          },
          errorMessages: {
            productValues: Obj.magic(None),
          },
          settingsOnly: None,
        }),
      }
    })

    expect(
      reducer(
        initialState,
        LabelPrintEdited({
          ...CatalogLabelCreatePage.CatalogLabelCreateLabelPrintForm.initialValues(
            ~priceId="mock-price-id-0"->Uuid.unsafeFromString,
            ~productBarcodeDisplayed=false,
            ~missingProductBarcodeGenerated=true,
            (),
          ),
          productValues: All,
        }),
      ),
    )->toStrictEqual({
      {
        priceList: initialState.priceList,
        form: LabelPrint({
          submitResult: NotAsked,
          values: {
            priceId: "mock-price-id-0"->Uuid.unsafeFromString,
            productBarcodeDisplayed: true,
            missingProductBarcodeGenerated: true,
            priceDisplayed: true,
            producerDisplayed: false,
            alcoholVolumeDisplayed: false,
            productCode: Hidden,
            labelFormat: Label31x22,
            productValues: All,
          },
          errorMessages: {
            productValues: Obj.magic(None),
          },
          settingsOnly: None,
        }),
      }
    })

    expect(
      reducer(
        {
          ...initialState,
          form: LabelPrint({
            ...initialStateLabelPrintForm,
            values: {
              ...initialStateLabelPrintForm.values,
              productBarcodeDisplayed: true,
              missingProductBarcodeGenerated: true,
            },
          }),
        },
        LabelPrintEdited({
          ...CatalogLabelCreatePage.CatalogLabelCreateLabelPrintForm.initialValues(
            ~priceId="mock-price-id-0"->Uuid.unsafeFromString,
            ~productBarcodeDisplayed=false,
            ~missingProductBarcodeGenerated=true,
            (),
          ),
          productValues: All,
        }),
      ),
    )->toStrictEqual({
      {
        priceList: initialState.priceList,
        form: LabelPrint({
          submitResult: NotAsked,
          values: {
            priceId: "mock-price-id-0"->Uuid.unsafeFromString,
            productBarcodeDisplayed: false,
            missingProductBarcodeGenerated: false,
            priceDisplayed: true,
            producerDisplayed: false,
            alcoholVolumeDisplayed: false,
            productCode: Hidden,
            labelFormat: Label31x22,
            productValues: All,
          },
          errorMessages: {
            productValues: Obj.magic(None),
          },
          settingsOnly: None,
        }),
      }
    })

    expect(
      reducer(
        {
          ...initialState,
          form: LabelPrint({
            ...initialStateLabelPrintForm,
            values: {
              ...initialStateLabelPrintForm.values,
              productValues: All,
            },
          }),
        },
        PrintLabelsSubmitted(NotAsked),
      ),
    )->toStrictEqual({
      {
        priceList: initialState.priceList,
        form: LabelPrint({
          submitResult: NotAsked,
          values: {
            priceId: "mock-price-id-0"->Uuid.unsafeFromString,
            productBarcodeDisplayed: false,
            missingProductBarcodeGenerated: false,
            priceDisplayed: true,
            producerDisplayed: false,
            alcoholVolumeDisplayed: false,
            productCode: Hidden,
            labelFormat: Label31x22,
            productValues: All,
          },
          errorMessages: {
            productValues: Obj.magic(None),
          },
          settingsOnly: None,
        }),
      }
    })

    expect(
      reducer(
        {
          ...initialState,
          form: LabelPrint({
            ...initialStateLabelPrintForm,
            values: {
              ...initialStateLabelPrintForm.values,
              productValues: Picked([]),
            },
          }),
        },
        PrintLabelsSubmitted(NotAsked),
      ),
    )->toStrictEqual({
      {
        priceList: initialState.priceList,
        form: LabelPrint({
          submitResult: NotAsked,
          values: {
            priceId: "mock-price-id-0"->Uuid.unsafeFromString,
            productBarcodeDisplayed: false,
            missingProductBarcodeGenerated: false,
            priceDisplayed: true,
            producerDisplayed: false,
            alcoholVolumeDisplayed: false,
            productCode: Hidden,
            labelFormat: Label31x22,
            productValues: Picked([]),
          },
          errorMessages: {
            productValues: "Please select at least one product.",
          },
          settingsOnly: None,
        }),
      }
    })

    expect(
      reducer(
        {
          ...initialState,
          form: LabelPrint({
            ...initialStateLabelPrintForm,
            values: {
              ...initialStateLabelPrintForm.values,
              productValues: All,
            },
          }),
        },
        PrintLabelsSubmitted(Loading),
      ),
    )->toStrictEqual({
      {
        priceList: initialState.priceList,
        form: LabelPrint({
          submitResult: Loading,
          values: {
            priceId: "mock-price-id-0"->Uuid.unsafeFromString,
            productBarcodeDisplayed: false,
            missingProductBarcodeGenerated: false,
            priceDisplayed: true,
            producerDisplayed: false,
            alcoholVolumeDisplayed: false,
            productCode: Hidden,
            labelFormat: Label31x22,
            productValues: All,
          },
          errorMessages: {
            productValues: Obj.magic(None),
          },
          settingsOnly: None,
        }),
      }
    })

    expect(
      reducer(
        {
          ...initialState,
          form: LabelPrint({
            ...initialStateLabelPrintForm,
            values: {
              ...initialStateLabelPrintForm.values,
              productValues: Picked([]),
            },
          }),
        },
        PrintLabelsSubmitted(Loading),
      ),
    )->toStrictEqual({
      {
        priceList: initialState.priceList,
        form: LabelPrint({
          submitResult: Done(
            Error(
              "There are some errors in the form, please correct them before trying to send it again.",
            ),
          ),
          values: {
            priceId: "mock-price-id-0"->Uuid.unsafeFromString,
            productBarcodeDisplayed: false,
            missingProductBarcodeGenerated: false,
            priceDisplayed: true,
            producerDisplayed: false,
            alcoholVolumeDisplayed: false,
            productCode: Hidden,
            labelFormat: Label31x22,
            productValues: Picked([]),
          },
          errorMessages: {
            productValues: "Please select at least one product.",
          },
          settingsOnly: None,
        }),
      }
    })

    expect(
      reducer(
        {
          ...initialState,
          form: LabelPrint({
            ...initialStateLabelPrintForm,
            values: {
              ...initialStateLabelPrintForm.values,
              productValues: Picked([]),
            },
          }),
        },
        SaveSettingsSubmitted,
      ),
    )->toStrictEqual({
      {
        priceList: initialState.priceList,
        form: LabelPrint({
          submitResult: Loading,
          values: {
            priceId: "mock-price-id-0"->Uuid.unsafeFromString,
            productBarcodeDisplayed: false,
            missingProductBarcodeGenerated: false,
            priceDisplayed: true,
            producerDisplayed: false,
            alcoholVolumeDisplayed: false,
            productCode: Hidden,
            labelFormat: Label31x22,
            productValues: Picked([]),
          },
          errorMessages: {},
          settingsOnly: None,
        }),
      }
    })

    expect(
      reducer(
        {
          ...initialState,
          form: LabelPrint({
            ...initialStateLabelPrintForm,
            settingsOnly: Some({
              fromPathname: "mock-pathname",
              variantIdFromCatalogRedirection: "mock-variant-id",
            }),
            values: {
              ...initialStateLabelPrintForm.values,
              productValues: Picked([]),
            },
          }),
        },
        SaveSettingsSubmitted,
      ),
    )->toStrictEqual({
      {
        priceList: initialState.priceList,
        form: LabelPrint({
          submitResult: Loading,
          values: {
            priceId: "mock-price-id-0"->Uuid.unsafeFromString,
            productBarcodeDisplayed: false,
            missingProductBarcodeGenerated: false,
            priceDisplayed: true,
            producerDisplayed: false,
            alcoholVolumeDisplayed: false,
            productCode: Hidden,
            labelFormat: Label31x22,
            productValues: Picked([]),
          },
          errorMessages: {},
          settingsOnly: Some({
            fromPathname: "mock-pathname",
            variantIdFromCatalogRedirection: "mock-variant-id",
          }),
        }),
      }
    })
  })
})

describe("CatalogLabelCreatePageInvalidBarcodesBanner", () => {
  let {formateDetails} = module(CatalogLabelCreatePage.CatalogLabelCreatePageInvalidBarcodesBanner)

  test("formateDetails", () => {
    let invalidBarcodesVariants = []
    let result = formateDetails(~productValues=Picked([]), ~invalidBarcodesVariants)
    expect(result)->toStrictEqual([])

    let invalidBarcodesVariants = [
      {
        CatalogLabel.RequestSheetLabelsType.id: "mock-id-999",
        productName: "mock-product-name-9999",
        name: "mock-variant-name-999",
      },
    ]
    let productValues = CatalogLabelCreatePage.CatalogLabelCreateProductsValue.Picked([
      {
        productVariantId: "mock-id-0",
        printingQuantity: 1,
        productInformation: {
          name: "mock-full-name-0",
          description: "",
          internalCode: None,
          stockKeepingUnit: None,
        },
      },
    ])
    let result = formateDetails(~productValues, ~invalidBarcodesVariants)
    expect(result)->toStrictEqual([])

    let invalidBarcodesVariants = [
      {
        CatalogLabel.RequestSheetLabelsType.id: "mock-id-0",
        productName: "mock-product-name-0",
        name: "mock-variant-name-0",
      },
    ]
    let productValues = CatalogLabelCreatePage.CatalogLabelCreateProductsValue.Picked([
      {
        productVariantId: "mock-id-0",
        printingQuantity: 1,
        productInformation: {
          name: "mock-full-name-0",
          description: "",
          internalCode: None,
          stockKeepingUnit: Some("mock-sku-0"),
        },
      },
    ])
    let result = formateDetails(~productValues, ~invalidBarcodesVariants)
    expect(result)->toStrictEqual(["mock-full-name-0 (mock-sku-0)"])

    let invalidBarcodesVariants = [
      {
        CatalogLabel.RequestSheetLabelsType.id: "mock-id-0",
        productName: "mock-product-name-0",
        name: "mock-variant-name-0",
      },
    ]
    let productValues = CatalogLabelCreatePage.CatalogLabelCreateProductsValue.Picked([
      {
        productVariantId: "mock-id-0",
        printingQuantity: 1,
        productInformation: {
          name: "mock-full-name-0",
          description: "",
          internalCode: None,
          stockKeepingUnit: None,
        },
      },
    ])
    let result = formateDetails(~productValues, ~invalidBarcodesVariants)
    expect(result)->toStrictEqual(["mock-full-name-0"])

    let invalidBarcodesVariants = [
      {
        CatalogLabel.RequestSheetLabelsType.id: "mock-id-0",
        productName: "mock-product-name-0",
        name: "mock-variant-name-0",
      },
    ]
    let result = formateDetails(~productValues=All, ~invalidBarcodesVariants)
    expect(result)->toStrictEqual(["mock-product-name-0 - mock-variant-name-0"])
  })
})
todo("CatalogLabelCreateInformationCard")
todo("CatalogLabelCreatePrintSettingsCard")
todo("CatalogLabelCreateProductsCard")
todo("CatalogLabelCreatePageLoaded")
todo("component")

// describe("CatalogLabelCreatePrintSettingsCard", () => {
//   let {initialValues} = module(CatalogLabelCreatePage.CatalogLabelCreateLabelSheetForm)

//   let userEvent = TestingLibraryEvent.setup()

//   module TestableCatalogLabelCreateInformationLabelSheetCard = {
//     @react.component
//     let make = (~priceList, ~defaultValues, ~onChange) => {
//       let (values, setValues) = React.useState(() => defaultValues)

//       React.useEffect1(() => {
//         onChange(values)
//         None
//       }, [values])

//       <CatalogLabelCreatePage.CatalogLabelCreateInformationCard.LabelSheet
//         priceList values onChange={values => setValues(_ => values)}
//       />
//     }
//   }

//   testPromise("component", async () => {
//     let onChange = fn1(ignore)

//     render(
//       <TestableCatalogLabelCreateInformationLabelSheetCard
//         defaultValues={initialValues(
//           ~priceId="mock-price-id-0"->Uuid.unsafeFromString,
//           ~productBarcodeDisplayed=false,
//           ~missingProductBarcodeGenerated=false,
//           ~productCode=CatalogLabel.ProductCode.Hidden,
//           ~labelFormat=CatalogLabel.Sheet.LabelFormat.Grid21,
//           ~printOffset=1,
//           ~borderEnabled=false,
//           ~sort=CatalogLabel.Sheet.LabelSort.Unsorted,
//           (),
//         )}
//         priceList=[
//           {
//             CatalogLabelCreatePage.id: "mock-price-id-0"->Uuid.unsafeFromString,
//             name: "Price ID 0",
//             default: true,
//           },
//           {
//             id: "mock-price-id-1"->Uuid.unsafeFromString,
//             name: "Price ID 1",
//             default: false,
//           },
//         ]
//         onChange={onChange->fn}
//       />,
//     )->ignore

//     let printOffsetValue =
//       screen->getByRoleWithOptionsExn(#textbox, {name: "Offset of the first printed label"})
//     let labelFormatInput =
//       screen->getByRoleWithOptionsExn(#button, {name: "Sheet of 21 labels (format 6,35 x 3,81 cm)"})

//     expect(printOffsetValue)->toBeVisible
//     expect(labelFormatInput)->toBeVisible
//   })
// })

//   testPromise("component", async () => {
//     let spyOnChange = fn1(ignore)

//     render(
//       <TestableCatalogLabelCreatePrintSettingsCard
//         defaultState={mockPrintSettingsState()} onChange={spyOnChange->fn}
//       />,
//     )->ignore

// let printOffsetValue =
//   screen->getByRoleWithOptionsExn(#textbox, {name: "Offset of the first printed label"})
// let labelFormatInput =
//   screen->getByRoleWithOptionsExn(#button, {name: "Sheet of 21 labels (format 6,35 x 3,81 cm)"})

// expect(printOffsetValue)->toBeVisible
// expect(labelFormatInput)->toBeVisible
//     expect(labelFormatInput)->toHaveTextContent("Sheet of 21 labels (format 6,35 x 3,81 cm)")
//     expect(screen->queryByRole(#listbox))->toBeNone
//     expect(spyOnChange)->toHaveBeenCalledTimes(1)
//     expect(spyOnChange)->toHaveBeenCalledWith1(mockPrintSettingsState())
//     spyOnChange->mockClear
