open Vitest

let {mockShop} = module(Auth__Mock)

let mockRow = (~variantId="", ()) => {
  AnalyticsTopPerformingProductsPage.Row.variantId,
  margin: "0.",
  marginRate: "0.",
  markupRate: "0.",
  totalPurchaseCost: "0.",
  revenueIncludingTaxes: 0.,
  revenueExcludingTaxes: 0.,
  unitSoldQuantity: 0,
  bulkSoldQuantity: None,
  stockQuantity: "",
  purchasedPrice: "",
  name: "",
  kind: #SIMPLE,
  description: "",
  color: None,
  stockKeepingUnit: None,
  cku: None,
}

let mockTypename = () => ""

let mockQueryDataEdge = (
  ~variantId="",
  ~name="",
  ~margin=0.,
  ~marginRate=0.,
  ~markupRate=0.,
  ~totalPurchaseCost=0.,
  ~revenueIncludingTaxes=0.,
  ~revenueExcludingTaxes=0.,
  ~occurrences=0,
  ~quantity=0,
  ~kind=#SIMPLE,
  ~description="",
  ~capacityPrecision=None,
  ~capacityUnit=None,
  ~color=None,
  ~stockKeepingUnit=None,
  ~stockRawQuantity=None,
  ~purchasedPrice=None,
  ~cku=None,
  (),
) => {
  AnalyticsTopPerformingProductsPage.Query.node: {
    variantId,
    name,
    margin,
    marginRate,
    markupRate,
    totalPurchaseCost,
    revenueIncludingTaxes,
    revenueExcludingTaxes,
    quantity,
    occurrences,
    kind,
    description,
    capacityPrecision,
    capacityUnit,
    color,
    stockKeepingUnit,
    stockRawQuantity,
    purchasedPrice,
    cku,
    __typename: mockTypename(),
  },
  __typename: mockTypename(),
}

let mockQueryDataPageInfo = (~startCursor=?, ~endCursor=?, ()) => {
  AnalyticsTopPerformingProductsPage.Query.startCursor,
  endCursor,
  __typename: mockTypename(),
}

let mockQueryData = (~edges=[], ~pageInfo=mockQueryDataPageInfo(), ~totalCount=0, ()) => {
  AnalyticsTopPerformingProductsPage.Query.topPerformingProducts: {
    edges,
    pageInfo,
    totalCount,
    __typename: mockTypename(),
  },
}

let mockQueryVariablesFilterBy = () =>
  AnalyticsTopPerformingProductsPage.Query.makeInputObjectTopPerformingProductsQueryFilter()

let mockQueryVariables = (~sortBy=None, ()) => {
  AnalyticsTopPerformingProductsPage.Query.filterBy: None,
  sortBy,
}

let mockStateFilters = (~shop, ~dateRange, ()) => {
  AnalyticsTopPerformingProductsPage.Filters.shop,
  dateRange,
}

let mockState = (
  ~filters=mockStateFilters(
    ~shop=mockShop(~id="mock-shop-id", ()),
    ~dateRange=(Js.Date.fromFloat(0.), Js.Date.fromFloat(1.)),
    (),
  ),
  ~currentPage=1,
  ~previousPage=-1,
  ~searchQuery=?,
  (),
) => {
  Scaffold.filters,
  currentPage,
  previousPage,
  searchQuery,
  connectionArguments: {first: 10},
}

describe("Row", () => {
  todo("unapplyArbitraryPrecision")
  todo("formatStockQuantity")
  todo("formatPurchasedPrice")
  todo("fromQueryDataEdge")
})

describe("Scaffolded", () => {
  todo("useFiltersJsonCodec")

  test("makeQueryVariables", () => {
    let {makeQueryVariables} = module(AnalyticsTopPerformingProductsPage.Scaffolded)

    expect(
      makeQueryVariables(
        mockQueryVariables(~sortBy=Some(#QUANTITY), ()),
        ~connectionArguments={},
        (),
      ),
    )->toStrictEqual({
      AnalyticsTopPerformingProductsPage.Query.filterBy: None,
      sortBy: Some(#QUANTITY),
    })

    expect(
      makeQueryVariables(
        mockQueryVariables(),
        ~connectionArguments={
          first: 1,
          last: 2,
          before: "before",
          after: "after",
        },
        ~search="search",
        ~filterBy=mockQueryVariablesFilterBy(),
        (),
      ),
    )->toStrictEqual({
      AnalyticsTopPerformingProductsPage.Query.filterBy: Some(
        AnalyticsTopPerformingProductsPage.Query.makeInputObjectTopPerformingProductsQueryFilter(),
      ),
      sortBy: None,
    })
  })

  test("makeQueryVariablesFilterBy", () => {
    let {makeQueryVariablesFilterBy} = module(AnalyticsTopPerformingProductsPage.Scaffolded)

    let filters = {
      AnalyticsTopPerformingProductsPage.Filters.shop: mockShop(~id="mock-shop-id", ()),
      dateRange: (Js.Date.fromFloat(0.), Js.Date.fromFloat(1.)),
    }
    expect(makeQueryVariablesFilterBy(filters))->toStrictEqual({
      AnalyticsTopPerformingProductsPage.Query.shopIds: Some({_in: ["mock-shop-id"]}),
      startDate: Some(Js.Date.fromFloat(0.)->Scalar.Datetime.serialize),
      endDate: Some(Js.Date.fromFloat(1.)->Scalar.Datetime.serialize),
    })

    let filters = {
      AnalyticsTopPerformingProductsPage.Filters.shop: mockShop(~id="mock-shop-id", ()),
      dateRange: (Js.Date.fromFloat(0.), Js.Date.fromFloat(1.)),
    }
    expect(makeQueryVariablesFilterBy(filters))->toStrictEqual({
      AnalyticsTopPerformingProductsPage.Query.shopIds: Some({_in: ["mock-shop-id"]}),
      startDate: Some(Js.Date.fromFloat(0.)->Scalar.Datetime.serialize),
      endDate: Some(Js.Date.fromFloat(1.)->Scalar.Datetime.serialize),
    })
  })

  test("totalCountFromQueryData", () => {
    let {totalCountFromQueryData} = module(AnalyticsTopPerformingProductsPage.Scaffolded)

    let queryData = mockQueryData()
    expect(totalCountFromQueryData(queryData))->toBe(0)

    let queryData = mockQueryData(~totalCount=1, ())
    expect(totalCountFromQueryData(queryData))->toBe(1)

    let queryData = mockQueryData(~totalCount=100, ())
    expect(totalCountFromQueryData(queryData))->toBe(100)

    let queryData = mockQueryData(~totalCount=-1, ())
    expect(totalCountFromQueryData(queryData))->toBe(-1)
  })

  test("cursorsFromQueryData", () => {
    let {cursorsFromQueryData} = module(AnalyticsTopPerformingProductsPage.Scaffolded)

    let queryData = mockQueryData()
    expect(cursorsFromQueryData(queryData))->toStrictEqual((None, None))

    let queryData = mockQueryData(~pageInfo=mockQueryDataPageInfo(~startCursor="start", ()), ())
    expect(cursorsFromQueryData(queryData))->toStrictEqual((Some("start"), None))

    let queryData = mockQueryData(
      ~pageInfo=mockQueryDataPageInfo(~startCursor="start", ~endCursor="end", ()),
      (),
    )
    expect(cursorsFromQueryData(queryData))->toStrictEqual((Some("start"), Some("end")))

    let queryData = mockQueryData(~pageInfo=mockQueryDataPageInfo(~endCursor="end", ()), ())
    expect(cursorsFromQueryData(queryData))->toStrictEqual((None, Some("end")))
  })

  test("rowsFromQueryDataAndState", () => {
    let {rowsFromQueryDataAndState} = module(AnalyticsTopPerformingProductsPage.Scaffolded)

    let queryData = mockQueryData()
    expect(rowsFromQueryDataAndState(queryData, mockState()))->toStrictEqual([])

    let queryData = mockQueryData(~edges=[mockQueryDataEdge()], ())
    expect(rowsFromQueryDataAndState(queryData, mockState()))->toStrictEqual([
      {
        variantId: "",
        margin: "€0.00",
        marginRate: "0.00%",
        markupRate: "0.00%",
        totalPurchaseCost: "€0.00",
        revenueIncludingTaxes: 0.,
        revenueExcludingTaxes: 0.,
        unitSoldQuantity: 0,
        bulkSoldQuantity: None,
        stockQuantity: "0",
        purchasedPrice: "—",
        name: "",
        kind: #SIMPLE,
        description: "",
        color: None,
        stockKeepingUnit: None,
        cku: None,
      },
    ])

    let queryData = mockQueryData(
      ~edges=[
        mockQueryDataEdge(),
        mockQueryDataEdge(
          ~variantId="variant-id",
          ~name="ProductName - variantName",
          ~margin=1.2,
          ~marginRate=1.3,
          ~markupRate=2.3,
          ~totalPurchaseCost=23.4,
          ~revenueIncludingTaxes=1.23,
          ~revenueExcludingTaxes=2.34,
          ~quantity=1,
          ~occurrences=10,
          ~kind=#WINE,
          ~description="Something, Someone, Somewhere, France",
          ~capacityPrecision=None,
          ~capacityUnit=Some("L"),
          ~color=Some(#RED),
          ~stockKeepingUnit=Some("sku"),
          ~stockRawQuantity=Some(23),
          ~purchasedPrice=Some(12.3992),
          ~cku=Some("cku"),
          (),
        ),
        mockQueryDataEdge(
          ~variantId="variant-id",
          ~name="ProductName - variantName",
          ~margin=-2.,
          ~marginRate=-1.,
          ~markupRate=-2.,
          ~totalPurchaseCost=-23.4,
          ~revenueIncludingTaxes=-9.,
          ~revenueExcludingTaxes=-8.,
          ~quantity=-31,
          ~kind=#WINE,
          ~occurrences=2,
          ~description="Something, Someone, Somewhere, France",
          ~capacityPrecision=Some(3),
          ~capacityUnit=Some("L"),
          ~color=Some(#WHITE),
          ~stockKeepingUnit=Some("sku"),
          ~stockRawQuantity=Some(23),
          ~purchasedPrice=Some(12.),
          ~cku=None,
          (),
        ),
        mockQueryDataEdge(
          ~variantId="variant-id",
          ~name="ProductName - variantName",
          ~margin=266.2,
          ~marginRate=12.2,
          ~markupRate=34.2,
          ~totalPurchaseCost=10.4,
          ~revenueIncludingTaxes=120.,
          ~revenueExcludingTaxes=120.,
          ~quantity=30,
          ~kind=#BEER,
          ~description="Something, Someone, Somewhere, France",
          ~capacityPrecision=Some(3),
          ~capacityUnit=None,
          ~color=Some(#ROSE),
          ~stockKeepingUnit=None,
          ~stockRawQuantity=Some(23),
          ~purchasedPrice=Some(12.459),
          ~cku=Some("cku"),
          (),
        ),
      ],
      (),
    )
    expect(rowsFromQueryDataAndState(queryData, mockState()))->toStrictEqual([
      {
        variantId: "",
        margin: "€0.00",
        marginRate: "0.00%",
        markupRate: "0.00%",
        totalPurchaseCost: "€0.00",
        revenueIncludingTaxes: 0.,
        revenueExcludingTaxes: 0.,
        unitSoldQuantity: 0,
        bulkSoldQuantity: None,
        stockQuantity: "0",
        purchasedPrice: "—",
        name: "",
        kind: #SIMPLE,
        description: "",
        color: None,
        stockKeepingUnit: None,
        cku: None,
      },
      {
        variantId: "variant-id",
        margin: "€1.20",
        marginRate: "1.30%",
        markupRate: "2.30%",
        totalPurchaseCost: "€23.40",
        revenueIncludingTaxes: 1.23,
        revenueExcludingTaxes: 2.34,
        unitSoldQuantity: 1,
        bulkSoldQuantity: None,
        stockQuantity: "23",
        purchasedPrice: "€12.399",
        name: "ProductName - variantName",
        kind: #WINE,
        description: "Something, Someone, Somewhere, France",
        color: Some(#RED),
        stockKeepingUnit: Some("sku"),
        cku: Some("cku"),
      },
      {
        variantId: "variant-id",
        margin: "-€2.00",
        marginRate: "-1.00%",
        markupRate: "-2.00%",
        totalPurchaseCost: "-€23.40",
        revenueIncludingTaxes: -9.,
        revenueExcludingTaxes: -8.,
        unitSoldQuantity: 2,
        bulkSoldQuantity: Some("-0.031 L"),
        stockQuantity: "0.023 L",
        purchasedPrice: "€12.00/L",
        name: "ProductName - variantName",
        kind: #WINE,
        description: "Something, Someone, Somewhere, France",
        color: Some(#WHITE),
        stockKeepingUnit: Some("sku"),
        cku: None,
      },
      {
        variantId: "variant-id",
        margin: "€266.20",
        marginRate: "12.20%",
        markupRate: "34.20%",
        totalPurchaseCost: "€10.40",
        revenueIncludingTaxes: 120.,
        revenueExcludingTaxes: 120.,
        unitSoldQuantity: 30,
        bulkSoldQuantity: None,
        stockQuantity: "23",
        purchasedPrice: "€12.459",
        name: "ProductName - variantName",
        kind: #BEER,
        description: "Something, Someone, Somewhere, France",
        color: Some(#ROSE),
        stockKeepingUnit: None,
        cku: Some("cku"),
      },
    ])

    let queryData = mockQueryData(
      ~edges=[
        // first page
        mockQueryDataEdge(),
        mockQueryDataEdge(),
        mockQueryDataEdge(),
        mockQueryDataEdge(),
        mockQueryDataEdge(),
        mockQueryDataEdge(),
        mockQueryDataEdge(),
        mockQueryDataEdge(),
        mockQueryDataEdge(),
        mockQueryDataEdge(),
        // second page
        mockQueryDataEdge(
          ~variantId="variant-id",
          ~name="ProductName - variantName",
          ~margin=266.2,
          ~marginRate=12.2,
          ~markupRate=34.2,
          ~totalPurchaseCost=10.4,
          ~revenueIncludingTaxes=1.23,
          ~revenueExcludingTaxes=2.34,
          ~quantity=1,
          ~kind=#WINE,
          ~description="Something, Someone, Somewhere, France",
          ~capacityPrecision=None,
          ~capacityUnit=Some("L"),
          ~color=Some(#RED),
          ~stockKeepingUnit=Some("sku"),
          ~stockRawQuantity=Some(23),
          ~purchasedPrice=Some(12.3),
          ~cku=Some("cku"),
          (),
        ),
      ],
      (),
    )

    expect(
      rowsFromQueryDataAndState(
        queryData,
        mockState(
          ~filters=mockStateFilters(
            ~shop=mockShop(~id="mock-shop-id", ()),
            ~dateRange=(Js.Date.fromFloat(0.), Js.Date.fromFloat(1.)),
            (),
          ),
          ~currentPage=2,
          ~previousPage=1,
          ~searchQuery="",
          (),
        ),
      ),
    )->toStrictEqual([
      {
        variantId: "variant-id",
        margin: "€266.20",
        marginRate: "12.20%",
        markupRate: "34.20%",
        totalPurchaseCost: "€10.40",
        revenueIncludingTaxes: 1.23,
        revenueExcludingTaxes: 2.34,
        unitSoldQuantity: 1,
        bulkSoldQuantity: None,
        stockQuantity: "23",
        purchasedPrice: "€12.30",
        name: "ProductName - variantName",
        kind: #WINE,
        description: "Something, Someone, Somewhere, France",
        color: Some(#RED),
        stockKeepingUnit: Some("sku"),
        cku: Some("cku"),
      },
    ])
  })

  test("keyExtractor", () => {
    let {keyExtractor} = module(AnalyticsTopPerformingProductsPage.Scaffolded)

    let row = mockRow()
    expect(keyExtractor(row))->toBe("")

    let row = mockRow(~variantId="mock-variantId", ())
    expect(keyExtractor(row))->toBe("mock-variantId")
  })
})

// Some integration tests to ensure basic interactions
// with the other components and GraphQL might be valuable
// at some point.
todo("Integration test")
