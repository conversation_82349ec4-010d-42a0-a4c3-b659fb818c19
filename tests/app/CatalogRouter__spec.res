open Vitest

let {mockUser, mockShop, mockAuthSingleScope, mockAuthOrganisationScope} = module(Auth__Mock)

describe("isIndependentKindShops", () => {
  let {isIndependentKindShops} = module(CatalogRouter)

  it("should be true when a single shop is independent", () => {
    let shop = mockShop(~id="shop-id", ~name="shop-name", ~kind=#INDEPENDENT, ())
    let authScope = mockAuthSingleScope(~shop, ())
    expect(isIndependentKindShops(~authScope))->toBe(true)

    let shop = mockShop(~id="shop-id", ~name="shop-name", ~kind=#FRANCHISED, ())
    let authScope = mockAuthSingleScope(~shop, ())
    expect(isIndependentKindShops(~authScope))->toBe(false)

    let shop = mockShop(~id="shop-id", ~name="shop-name", ~kind=#AFFILIATED, ())
    let authScope = mockAuthSingleScope(~shop, ())
    expect(isIndependentKindShops(~authScope))->toBe(false)

    let shop = mockShop(~id="shop-id", ~name="shop-name", ~kind=#INTEGRATED, ())
    let authScope = mockAuthSingleScope(~shop, ())
    expect(isIndependentKindShops(~authScope))->toBe(false)
  })

  it("should be true when an organization has its active shop that is independent", () => {
    let shop = mockShop(~id="shop-id", ~name="shop-name", ~kind=#INDEPENDENT, ())
    let authScope = mockAuthOrganisationScope(
      ~activeShop=Some(shop),
      ~shops=[shop, mockShop(~kind=#FRANCHISED, ())],
      (),
    )
    expect(isIndependentKindShops(~authScope))->toBe(true)

    let shop = mockShop(~id="shop-id", ~name="shop-name", ~kind=#INDEPENDENT, ())
    let authScope = mockAuthOrganisationScope(
      ~activeShop=Some(mockShop(~kind=#WAREHOUSE, ())),
      ~shops=[shop],
      (),
    )
    expect(isIndependentKindShops(~authScope))->toBe(false)
  })

  it("should be true when an organization has all its shops that are independent", () => {
    let shopA = mockShop(~id="shop-id-a", ~name="shop-name-a", ~kind=#INDEPENDENT, ())
    let shopB = mockShop(~id="shop-id-b", ~name="shop-name-b", ~kind=#INDEPENDENT, ())
    let authScope = mockAuthOrganisationScope(~activeShop=None, ~shops=[shopA, shopB], ())
    expect(isIndependentKindShops(~authScope))->toBe(true)

    let shopA = mockShop(~id="shop-id-a", ~name="shop-name-a", ~kind=#INDEPENDENT, ())
    let shopB = mockShop(~id="shop-id-b", ~name="shop-name-b", ~kind=#INTEGRATED, ())
    let shopC = mockShop(~id="shop-id-c", ~name="shop-name-c", ~kind=#AFFILIATED, ())
    let authScope = mockAuthOrganisationScope(~activeShop=None, ~shops=[shopA, shopB, shopC], ())
    expect(isIndependentKindShops(~authScope))->toBe(false)
  })
})
