open Vitest
open TestingLibraryReact

describe("StockActivityExportMenuItem", () => {
  let {encodeRequestBodyJson, makeVariablesFromFilters} = module(
    StockActivityPage.StockActivityExportMenuItem
  )

  test("makeRequestBodyJson", () => {
    expect(encodeRequestBodyJson(~shopIds=["mock-shop-id", "mock-shop-id"], ()))->toStrictEqual(
      [
        (
          "shopIds",
          ["mock-shop-id", "mock-shop-id"]->Array.map(Json.encodeString)->Json.encodeArray,
        ),
        ("timeZone", "UTC"->Json.encodeString),
      ]
      ->Js.Dict.fromArray
      ->Json.encodeDict,
    )
    expect(
      encodeRequestBodyJson(~shopIds=["mock-shop-id"], ~kind=Kind(#DELIVERY_RECEIPT), ()),
    )->toStrictEqual(
      [
        ("shopIds", ["mock-shop-id"]->Array.map(Json.encodeString)->Json.encodeArray),
        ("stockActivityKind", "DELIVERY_RECEIPT"->Json.encodeString),
        ("timeZone", "UTC"->Json.encodeString),
      ]
      ->Js.Dict.fromArray
      ->Json.encodeDict,
    )
    expect(
      encodeRequestBodyJson(~shopIds=["mock-shop-id"], ~kind=ExcludingReset, ()),
    )->toStrictEqual(
      [
        ("shopIds", ["mock-shop-id"]->Array.map(Json.encodeString)->Json.encodeArray),
        ("stockActivityNotKind", "RESET"->Json.encodeString),
        ("timeZone", "UTC"->Json.encodeString),
      ]
      ->Js.Dict.fromArray
      ->Json.encodeDict,
    )
    expect(encodeRequestBodyJson(~shopIds=[], ~reason=#DAMAGE, ()))->toStrictEqual(
      [
        ("shopIds", []->Json.encodeArray),
        ("stockActivityLossReason", "DAMAGE"->Json.encodeString),
        ("timeZone", "UTC"->Json.encodeString),
      ]
      ->Js.Dict.fromArray
      ->Json.encodeDict,
    )
    expect(
      encodeRequestBodyJson(
        ~shopIds=[],
        ~dateRange=(
          Js.Date.fromString("Tue Nov 02 06:32:34 2021"),
          Js.Date.fromString("Tue Nov 03 06:32:34 2021"),
        ),
        (),
      ),
    )->toStrictEqual(
      [
        ("shopIds", []->Json.encodeArray),
        ("startDate", "2021-11-02T06:32:34.000Z"->Json.encodeString),
        ("endDate", "2021-11-03T06:32:34.000Z"->Json.encodeString),
        ("timeZone", "UTC"->Json.encodeString),
      ]
      ->Js.Dict.fromArray
      ->Json.encodeDict,
    )
  })

  it("makeVariablesFromFilters", () => {
    expect(
      makeVariablesFromFilters(
        ~shopIds=["mock-shop-id"],
        ~kind=Kind(#DELIVERY),
        ~dateRange=(
          Js.Date.fromString("Tue Nov 02 06:32:34 2021"),
          Js.Date.fromString("Tue Nov 03 06:32:34 2021"),
        ),
        (),
      ),
    )->toStrictEqual({
      filterBy: Some({
        shopIds: Some({
          _in: ["mock-shop-id"],
        }),
        kind: Some({
          _in: Some(["DELIVERY"]),
          _notIn: None,
        }),
        reason: None,
        date: Some({
          _after: None,
          _before: None,
          _between: Some([
            "2021-11-02T06:32:34.000Z"->Js.Date.fromString->Scalar.Datetime.serialize,
            "2021-11-03T06:32:34.000Z"->Js.Date.fromString->Scalar.Datetime.serialize,
          ]),
        }),
      }),
    })

    expect(
      makeVariablesFromFilters(
        ~shopIds=["mock-shop-id"],
        ~kind=ExcludingReset,
        ~dateRange=(
          Js.Date.fromString("Tue Nov 02 06:32:34 2021"),
          Js.Date.fromString("Tue Nov 03 06:32:34 2021"),
        ),
        (),
      ),
    )->toStrictEqual({
      filterBy: Some({
        shopIds: Some({
          _in: ["mock-shop-id"],
        }),
        kind: Some({
          _in: None,
          _notIn: Some(["RESET"]),
        }),
        reason: None,
        date: Some({
          _after: None,
          _before: None,
          _between: Some([
            "2021-11-02T06:32:34.000Z"->Js.Date.fromString->Scalar.Datetime.serialize,
            "2021-11-03T06:32:34.000Z"->Js.Date.fromString->Scalar.Datetime.serialize,
          ]),
        }),
      }),
    })

    expect(
      makeVariablesFromFilters(~shopIds=[], ~kind=Kind(#LOSS), ~reason=#TASTING, ()),
    )->toStrictEqual({
      filterBy: Some({
        shopIds: Some({
          _in: [],
        }),
        kind: Some({
          _in: Some(["LOSS"]),
          _notIn: None,
        }),
        reason: Some({
          _in: ["TASTING"],
        }),
        date: None,
      }),
    })
  })
})

let {mockShop} = module(Auth__Mock)

let mockRow = (~id="", ~formattedKind="", ()) => {
  StockActivityPage.Row.id,
  variantId: "",
  variantCku: "",
  variantName: "",
  variantDescription: "",
  variantStockKeepingUnit: None,
  variantCapacityPrecision: None,
  variantCapacityUnit: None,
  color: None,
  formattedKind,
  kind: #SALE,
  reason: None,
  quantity: 0,
  description: None,
  date: Js.Date.fromFloat(0.),
  shopName: "",
  deviceName: "",
}

let mockTypename = () => ""

let mockQueryDataEdge = (
  ~id="",
  ~variantId="",
  ~variantCku="",
  ~variantFormattedName="",
  ~variantFormattedDescription="",
  ~variantStockKeepingUnit=None,
  ~variantCapacityPrecision=None,
  ~variantCapacityUnit=None,
  ~variantBulk=None,
  ~variantProductColor=None,
  ~kind=#SALE,
  ~reason=None,
  ~comment=None,
  ~quantity=0,
  ~createdAt=Js.Date.fromFloat(0.),
  ~shopName="",
  ~deviceName="",
  ~deviceSlug="",
  (),
) => {
  StockActivityPage.Query.node: {
    id,
    variant: Some({
      id: variantId,
      cku: variantCku,
      formattedName: variantFormattedName,
      formattedDescription: variantFormattedDescription,
      stockKeepingUnit: variantStockKeepingUnit,
      capacityPrecision: variantCapacityPrecision,
      capacityUnit: variantCapacityUnit,
      bulk: variantBulk,
      product: {
        color: variantProductColor,
        __typename: mockTypename(),
      },
      __typename: mockTypename(),
    }),
    kind,
    reason,
    comment,
    quantity,
    createdAt,
    shop: {
      name: shopName,
      __typename: mockTypename(),
    },
    device: {
      name: deviceName,
      slug: deviceSlug,
      __typename: mockTypename(),
    },
    __typename: mockTypename(),
  },
  __typename: mockTypename(),
}

let mockQueryDataPageInfo = (~startCursor=?, ~endCursor=?, ()) => {
  StockActivityPage.Query.startCursor,
  endCursor,
  __typename: mockTypename(),
}

let mockQueryData = (~edges=[], ~pageInfo=mockQueryDataPageInfo(), ~totalCount=0, ()) => {
  StockActivityPage.Query.stockActivities: {
    edges,
    pageInfo,
    totalCount,
    __typename: mockTypename(),
  },
}

let mockQueryVariablesFilterBy = () =>
  StockActivityPage.Query.makeInputObjectInputStockActivitiesQueryFilter()

let mockQueryVariables = () => {
  StockActivityPage.Query.first: None,
  last: None,
  before: None,
  after: None,
  filterBy: None,
  search: None,
}

let mockStateFilters = (~shop=?, ~kind=?, ~reason=?, ~dateRange=?, ()) => {
  StockActivityPage.Filters.shop,
  kind,
  reason,
  dateRange,
}

let mockState = (
  ~filters=mockStateFilters(),
  ~currentPage=1,
  ~previousPage=-1,
  ~searchQuery=?,
  (),
) => {
  Scaffold.filters,
  currentPage,
  previousPage,
  searchQuery,
  connectionArguments: {first: 10},
}

describe("Scaffolded", () => {
  todo("useFiltersJsonCodec")

  test("makeQueryVariables", () => {
    let {makeQueryVariables} = module(StockActivityPage.Scaffolded)

    expect(makeQueryVariables(mockQueryVariables(), ~connectionArguments={}, ()))->toStrictEqual({
      StockActivityPage.Query.first: None,
      last: None,
      before: None,
      after: None,
      filterBy: None,
      search: None,
    })

    expect(
      makeQueryVariables(
        mockQueryVariables(),
        ~connectionArguments={
          first: 1,
          last: 2,
          before: "before",
          after: "after",
        },
        ~search="search",
        ~filterBy=mockQueryVariablesFilterBy(),
        (),
      ),
    )->toStrictEqual({
      StockActivityPage.Query.first: Some(1),
      last: Some(2),
      before: Some("before"),
      after: Some("after"),
      search: Some("search"),
      filterBy: Some(StockActivityPage.Query.makeInputObjectInputStockActivitiesQueryFilter()),
    })
  })

  test("makeQueryVariablesFilterBy", () => {
    let {makeQueryVariablesFilterBy} = module(StockActivityPage.Scaffolded)

    let filters = {
      StockActivityPage.Filters.shop: None,
      dateRange: None,
      kind: None,
      reason: None,
    }

    expect(makeQueryVariablesFilterBy(filters))->toStrictEqual({
      StockActivityPage.Query.shopIds: None,
      date: None,
      kind: None,
      reason: None,
    })

    let filters = {
      StockActivityPage.Filters.shop: Some(mockShop(~id="mock-shop-id", ())),
      dateRange: None,
      kind: None,
      reason: Some(#TRADE_IN_SUPPLIER),
    }

    expect(makeQueryVariablesFilterBy(filters))->toStrictEqual({
      StockActivityPage.Query.shopIds: Some({_in: ["mock-shop-id"]}),
      date: None,
      kind: None,
      reason: Some({_in: ["TRADE_IN_SUPPLIER"]}),
    })

    let filters = {
      StockActivityPage.Filters.shop: Some(mockShop(~id="mock-shop-id", ())),
      dateRange: Some((Js.Date.fromFloat(0.), Js.Date.fromFloat(1.))),
      kind: None,
      reason: None,
    }

    expect(makeQueryVariablesFilterBy(filters))->toStrictEqual({
      StockActivityPage.Query.shopIds: Some({_in: ["mock-shop-id"]}),
      date: Some({
        _before: None,
        _after: None,
        _between: Some([
          Js.Date.fromFloat(0.)->Scalar.Datetime.serialize,
          Js.Date.fromFloat(1.)->Scalar.Datetime.serialize,
        ]),
      }),
      kind: None,
      reason: None,
    })

    let filters = {
      StockActivityPage.Filters.shop: None,
      dateRange: Some((Js.Date.fromFloat(-1.), Js.Date.fromFloat(1.))),
      kind: Some(Kind(#DELIVERY)),
      reason: Some(#TASTING),
    }

    expect(makeQueryVariablesFilterBy(filters))->toStrictEqual({
      StockActivityPage.Query.shopIds: None,
      date: Some({
        _before: None,
        _after: None,
        _between: Some([
          Js.Date.fromFloat(-1.)->Scalar.Datetime.serialize,
          Js.Date.fromFloat(1.)->Scalar.Datetime.serialize,
        ]),
      }),
      kind: Some({
        _in: Some(["DELIVERY"]),
        _notIn: None,
      }),
      reason: Some({_in: ["TASTING"]}),
    })

    let filters = {
      StockActivityPage.Filters.shop: None,
      dateRange: Some((Js.Date.fromFloat(-1.), Js.Date.fromFloat(1.))),
      kind: Some(ExcludingReset),
      reason: None,
    }

    expect(makeQueryVariablesFilterBy(filters))->toStrictEqual({
      StockActivityPage.Query.shopIds: None,
      date: Some({
        _before: None,
        _after: None,
        _between: Some([
          Js.Date.fromFloat(-1.)->Scalar.Datetime.serialize,
          Js.Date.fromFloat(1.)->Scalar.Datetime.serialize,
        ]),
      }),
      kind: Some({
        _in: None,
        _notIn: Some(["RESET"]),
      }),
      reason: None,
    })
  })

  test("totalCountFromQueryData", () => {
    let {totalCountFromQueryData} = module(StockActivityPage.Scaffolded)

    let queryData = mockQueryData()
    expect(totalCountFromQueryData(queryData))->toBe(0)

    let queryData = mockQueryData(~totalCount=1, ())
    expect(totalCountFromQueryData(queryData))->toBe(1)

    let queryData = mockQueryData(~totalCount=100, ())
    expect(totalCountFromQueryData(queryData))->toBe(100)

    let queryData = mockQueryData(~totalCount=-1, ())
    expect(totalCountFromQueryData(queryData))->toBe(-1)
  })

  test("cursorsFromQueryData", () => {
    let {cursorsFromQueryData} = module(StockActivityPage.Scaffolded)

    let queryData = mockQueryData()

    expect(cursorsFromQueryData(queryData))->toStrictEqual((None, None))

    let queryData = mockQueryData(~pageInfo=mockQueryDataPageInfo(~startCursor="start", ()), ())

    expect(cursorsFromQueryData(queryData))->toStrictEqual((Some("start"), None))

    let queryData = mockQueryData(
      ~pageInfo=mockQueryDataPageInfo(~startCursor="start", ~endCursor="end", ()),
      (),
    )

    expect(cursorsFromQueryData(queryData))->toStrictEqual((Some("start"), Some("end")))

    let queryData = mockQueryData(~pageInfo=mockQueryDataPageInfo(~endCursor="end", ()), ())

    expect(cursorsFromQueryData(queryData))->toStrictEqual((None, Some("end")))
  })

  test("rowsFromQueryDataAndState", () => {
    let {rowsFromQueryDataAndState} = module(StockActivityPage.Scaffolded)

    let queryData = mockQueryData()

    expect(rowsFromQueryDataAndState(queryData, mockState()))->toStrictEqual([])

    let queryData = mockQueryData(~edges=[mockQueryDataEdge()], ())

    expect(rowsFromQueryDataAndState(queryData, mockState()))->toStrictEqual([
      {
        StockActivityPage.Row.id: "",
        variantId: "",
        variantCku: "",
        variantName: "",
        variantDescription: "",
        variantStockKeepingUnit: None,
        variantCapacityPrecision: None,
        variantCapacityUnit: None,
        color: None,
        formattedKind: "Sale",
        kind: #SALE,
        reason: None,
        quantity: 0,
        description: None,
        date: Js.Date.fromFloat(0.),
        shopName: "",
        deviceName: " - ",
      },
    ])

    let queryData = mockQueryData(
      ~edges=[
        mockQueryDataEdge(~variantCapacityUnit=Some("mock-variant-capacity-unit"), ()),
        mockQueryDataEdge(
          ~id="mock-id",
          ~variantId="mock-variant-id",
          ~variantCku="mock-variant-cku",
          ~variantFormattedName="mock-variant-formatted-name",
          ~variantFormattedDescription="mock-variant-formatted-description",
          ~variantStockKeepingUnit=Some("mock-variant-stock-keeping-unit"),
          ~variantCapacityPrecision=Some(3),
          ~variantCapacityUnit=Some("mock-variant-capacity-unit"),
          ~variantBulk=Some(true),
          ~variantProductColor=Some(#ORANGE),
          ~kind=#LOSS,
          ~reason=Some(#TRADE_IN_SUPPLIER),
          ~comment=Some("mock-comment"),
          ~quantity=1,
          ~createdAt=Js.Date.fromFloat(-1.),
          ~shopName="mock-shop-name",
          ~deviceName="mock-device-name",
          ~deviceSlug="mock-device-slug",
          (),
        ),
        mockQueryDataEdge(
          ~id="mock-id",
          ~variantId="mock-variant-id",
          ~variantCku="mock-variant-cku",
          ~variantFormattedName="mock-variant-formatted-name",
          ~variantFormattedDescription="mock-variant-formatted-description",
          ~variantStockKeepingUnit=Some("mock-variant-stock-keeping-unit"),
          ~variantCapacityPrecision=Some(3),
          ~variantCapacityUnit=Some("mock-variant-capacity-unit"),
          ~variantBulk=Some(false),
          ~variantProductColor=Some(#ORANGE),
          ~kind=#LOSS,
          ~reason=Some(#TRADE_IN_SUPPLIER),
          ~comment=Some("mock-comment"),
          ~quantity=1,
          ~createdAt=Js.Date.fromFloat(-1.),
          ~shopName="mock-shop-name",
          ~deviceName="mock-device-name",
          ~deviceSlug="d",
          (),
        ),
      ],
      (),
    )

    expect(rowsFromQueryDataAndState(queryData, mockState()))->toStrictEqual([
      {
        id: "",
        variantId: "",
        variantCku: "",
        variantName: "",
        variantDescription: "",
        variantStockKeepingUnit: None,
        variantCapacityPrecision: None,
        variantCapacityUnit: None,
        color: None,
        formattedKind: "Sale",
        kind: #SALE,
        reason: None,
        quantity: 0,
        description: None,
        date: Js.Date.fromFloat(0.),
        shopName: "",
        deviceName: " - ",
      },
      {
        id: "mock-id",
        variantId: "mock-variant-id",
        variantCku: "mock-variant-cku",
        variantName: "mock-variant-formatted-name",
        variantDescription: "mock-variant-formatted-description",
        variantStockKeepingUnit: Some("mock-variant-stock-keeping-unit"),
        variantCapacityPrecision: Some(3),
        variantCapacityUnit: Some("mock-variant-capacity-unit"),
        color: Some(#ORANGE),
        formattedKind: "Loss",
        kind: #LOSS,
        reason: Some(#TRADE_IN_SUPPLIER),
        quantity: 1,
        description: Some("mock-comment"),
        date: Js.Date.fromFloat(-1.),
        shopName: "mock-shop-name",
        deviceName: "mock-device-slug - mock-device-name",
      },
      {
        id: "mock-id",
        variantId: "mock-variant-id",
        variantCku: "mock-variant-cku",
        variantName: "mock-variant-formatted-name",
        variantDescription: "mock-variant-formatted-description",
        variantStockKeepingUnit: Some("mock-variant-stock-keeping-unit"),
        variantCapacityPrecision: None,
        variantCapacityUnit: None,
        color: Some(#ORANGE),
        formattedKind: "Loss",
        kind: #LOSS,
        reason: Some(#TRADE_IN_SUPPLIER),
        quantity: 1,
        description: Some("mock-comment"),
        date: Js.Date.fromFloat(-1.),
        shopName: "mock-shop-name",
        deviceName: "dashboard.wino.fr",
      },
    ])
  })

  test("keyExtractor", () => {
    let {keyExtractor} = module(StockActivityPage.Scaffolded)

    let row = mockRow()

    expect(keyExtractor(row))->toBe("")

    let row = mockRow(~id="mock-id", ())

    expect(keyExtractor(row))->toBe("mock-id")
  })
})

describe("SheetExportInput", () => {
  test("makeFromStateExn", () => {
    let {makeFromStateExn} = module(StockActivityPage.SheetExportInput)

    let state = mockState()

    expect(makeFromStateExn(state))->toStrictEqual({
      shopId: None,
      kind: None,
      reason: None,
      dateRange: None,
    })

    let state = mockState(
      ~filters=mockStateFilters(
        ~kind=Kind(#LOSS),
        ~reason=#THEFT,
        ~shop=mockShop(~id="mock-shop-id", ()),
        ~dateRange=(Js.Date.fromFloat(0.), Js.Date.fromFloat(1.)),
        (),
      ),
      (),
    )

    expect(makeFromStateExn(state))->toStrictEqual({
      shopId: Some("mock-shop-id"),
      kind: Some(Kind(#LOSS)),
      reason: Some(#THEFT),
      dateRange: Some((Js.Date.fromFloat(0.), Js.Date.fromFloat(1.))),
    })

    let state = mockState(
      ~filters=mockStateFilters(
        ~reason=#TRADE_IN_SUPPLIER,
        ~shop=mockShop(~id="mock-shop-id", ()),
        (),
      ),
      (),
    )

    expect(makeFromStateExn(state))->toStrictEqual({
      shopId: Some("mock-shop-id"),
      kind: None,
      reason: Some(#TRADE_IN_SUPPLIER),
      dateRange: None,
    })

    let state = mockState(
      ~filters=mockStateFilters(~kind=ExcludingReset, ~shop=mockShop(~id="mock-shop-id", ()), ()),
      (),
    )

    expect(makeFromStateExn(state))->toStrictEqual({
      shopId: Some("mock-shop-id"),
      kind: Some(ExcludingReset),
      reason: None,
      dateRange: None,
    })
  })
})

test("use", () => {
  let wrapper = props => <Providers> {props["children"]} </Providers>

  let initialState = StockActivityPage.Scaffolded.makeInitialState(
    ~filters={
      shop: Some(mockShop()),
      kind: None,
      reason: None,
      dateRange: None,
    },
  )

  let {result} = renderHookWithOptions(
    () => StockActivityPage.use(~initialState),
    ~options={wrapper: wrapper},
  )

  let (state, dispatch) = result.current

  expect(state)->toStrictEqual({
    filters: {
      shop: Some(mockShop()),
      kind: None,
      reason: None,
      dateRange: None,
    },
    currentPage: 1,
    previousPage: -1,
    searchQuery: None,
    connectionArguments: {first: 10},
  })

  act(() => dispatch(FiltersUpdated(prev => {...prev, kind: Some(Kind(#SALE))})))

  let (state, _) = result.current

  expect(state)->toStrictEqual({
    filters: {
      shop: Some(mockShop()),
      kind: Some(Kind(#SALE)),
      reason: None,
      dateRange: None,
    },
    currentPage: 1,
    previousPage: -1,
    searchQuery: None,
    connectionArguments: {first: 10},
  })

  act(() =>
    dispatch(
      FiltersUpdated(prev => {...prev, kind: Some(Kind(#LOSS)), reason: Some(#TRADE_IN_SUPPLIER)}),
    )
  )

  let (state, _) = result.current

  expect(state)->toStrictEqual({
    filters: {
      shop: Some(mockShop()),
      kind: Some(Kind(#LOSS)),
      reason: Some(#TRADE_IN_SUPPLIER),
      dateRange: None,
    },
    currentPage: 1,
    previousPage: -1,
    searchQuery: None,
    connectionArguments: {first: 10},
  })

  act(() => dispatch(FiltersUpdated(prev => {...prev, kind: Some(Kind(#DELIVERY))})))

  let (state, _) = result.current

  expect(state)->toStrictEqual({
    filters: {
      shop: Some(mockShop()),
      kind: Some(Kind(#DELIVERY)),
      reason: None,
      dateRange: None,
    },
    currentPage: 1,
    previousPage: -1,
    searchQuery: None,
    connectionArguments: {first: 10},
  })

  act(() => dispatch(FiltersUpdated(prev => {...prev, reason: Some(#DAMAGE)})))

  let (state, _) = result.current

  expect(state)->toStrictEqual({
    filters: {
      shop: Some(mockShop()),
      kind: Some(Kind(#DELIVERY)),
      reason: None,
      dateRange: None,
    },
    currentPage: 1,
    previousPage: -1,
    searchQuery: None,
    connectionArguments: {first: 10},
  })

  act(() => dispatch(FiltersUpdated(prev => {...prev, kind: Some(ExcludingReset)})))

  let (state, _) = result.current

  expect(state)->toStrictEqual({
    filters: {
      shop: Some(mockShop()),
      kind: Some(ExcludingReset),
      reason: None,
      dateRange: None,
    },
    currentPage: 1,
    previousPage: -1,
    searchQuery: None,
    connectionArguments: {first: 10},
  })
})

// Some integration tests to ensure basic interactions
// with the other components and GraphQL might be valuable
// at some point.
todo("Integration test")
