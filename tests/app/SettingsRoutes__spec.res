open Vitest

it("should encode and decode query string correctly", () => {
  let jsonQueryString = SettingsRoutes.encodeEditBillingAccountQueryString(
    ~activeShopId="mock-shop-id",
    ~corporateName="mock-corporate-name",
    ~shopName="mock-shop-name",
    ~email="mock-email",
    ~phone="mock-phone",
    ~vatNumber=Some("mock-vatNumber"),
    ~billingAddress=Some({
      address: "mock-address",
      postalCode: "mock-postalCode",
      city: "mock-city",
      country: "mock-country",
    }),
    ~shippingAddress=Some({
      address: "mock-shipping-address",
      postalCode: "mock-shipping-postalCode",
      city: "mock-shipping-city",
      country: "mock-shipping-country",
    }),
  )

  expect(jsonQueryString)->toUnsafeStrictEqual(
    "activeShopId=%22mock-shop-id%22&corporateName=%22mock-corporate-name%22&shopName=%22mock-shop-name%22&email=%22mock-email%22&phone=%22mock-phone%22&vatNumber=%22mock-vatNumber%22&billingAddress[address]=%22mock-address%22&billingAddress[postalCode]=%22mock-postalCode%22&billingAddress[city]=%22mock-city%22&billingAddress[country]=%22mock-country%22&shippingAddress[address]=%22mock-shipping-address%22&shippingAddress[postalCode]=%22mock-shipping-postalCode%22&shippingAddress[city]=%22mock-shipping-city%22&shippingAddress[country]=%22mock-shipping-country%22",
  )

  expect(SettingsRoutes.decodeEditBillingAccountQueryString(jsonQueryString))->toUnsafeStrictEqual(
    Ok({
      "activeShopId": "mock-shop-id",
      "corporateName": "mock-corporate-name",
      "shopName": "mock-shop-name",
      "email": "mock-email",
      "phone": "mock-phone",
      "vatNumber": "mock-vatNumber",
      "billingAddress": {
        CorporateEntity.Address.address: "mock-address",
        postalCode: "mock-postalCode",
        city: "mock-city",
        country: "mock-country",
      },
      "shippingAddress": {
        CorporateEntity.Address.address: "mock-shipping-address",
        postalCode: "mock-shipping-postalCode",
        city: "mock-shipping-city",
        country: "mock-shipping-country",
      },
    }),
  )
})
