open Vitest

let {mockShop} = module(Auth__Mock)

let mockDateNow: float => unit = %raw(`timestamp => global.Date.now = vi.fn(() => timestamp)`)

let mockedUuid = Uuid.make()->Uuid.toString

let mockQueryVariablesFilterBy = () =>
  CatalogVariantOrderSupplyPage.Query.makeInputObjectInputOrderProductsByVariantCkuQueryFilter()

let mockTypename = () => ""

let mockQueryDataPageInfo = (~startCursor=?, ~endCursor=?, ()) => {
  CatalogVariantOrderSupplyPage.Query.startCursor,
  endCursor,
  __typename: mockTypename(),
}

let mockQueryData = (~edges=[], ~pageInfo=mockQueryDataPageInfo(), ~totalCount=0, ()) => {
  CatalogVariantOrderSupplyPage.Query.orderProductsByVariantCku: {
    edges,
    pageInfo,
    totalCount,
    __typename: mockTypename(),
  },
}

let mockRow = (~id="", ()) => {
  CatalogVariantOrderSupplyPage.Row.id,
  name: "",
  shopName: "",
  receptionDate: Js.Date.fromFloat(0.),
  quantity: "",
  totalAmountExcludingTaxes: 0.,
  totalAmountIncludingTaxes: 0.,
  supplierId: "",
  supplierCompanyName: "",
}

let mockQueryDataEdge = (
  ~quantity=0,
  ~totalAmountExcludingTaxes=0.,
  ~totalAmountIncludingTaxes=0.,
  ~orderId="",
  ~orderName=None,
  ~orderSupplierCompanyName="",
  ~orderEstimatedReceptionDate=Js.Date.fromFloat(0.),
  ~orderSupplierId="",
  ~shopName="",
  (),
) => {
  CatalogVariantOrderSupplyPage.Query.node: {
    quantity,
    totalAmountExcludingTaxes,
    totalAmountIncludingTaxes,
    order: Some({
      id: orderId,
      name: orderName,
      estimatedReceptionDate: orderEstimatedReceptionDate,
      supplierCompanyName: orderSupplierCompanyName,
      supplier: Some({
        id: orderSupplierId,
        __typename: mockTypename(),
      }),
      __typename: mockTypename(),
    }),
    shop: {
      name: shopName,
      __typename: mockTypename(),
    },
    __typename: mockTypename(),
  },
  __typename: mockTypename(),
}

let mockQueryVariables = (~cku=""->Json.encodeString, ()) => {
  CatalogVariantOrderSupplyPage.Query.cku,
  first: None,
  last: None,
  before: None,
  after: None,
  filterBy: None,
  search: None,
}

let mockStateFilters = (~shop=?, ()) => {
  CatalogVariantOrderSupplyPage.Filters.shop: shop,
}

let mockState = (
  ~filters=mockStateFilters(),
  ~currentPage=1,
  ~previousPage=-1,
  ~searchQuery=?,
  (),
) => {
  Scaffold.filters,
  connectionArguments: {},
  currentPage,
  previousPage,
  searchQuery,
}

describe("Scaffolded", () => {
  todo("useFiltersJsonCodec")

  test("makeQueryVariables", () => {
    let {makeQueryVariables} = module(CatalogVariantOrderSupplyPage.Scaffolded)

    expect(makeQueryVariables(mockQueryVariables(), ~connectionArguments={}, ()))->toStrictEqual({
      CatalogVariantOrderSupplyPage.Query.cku: ""->Json.encodeString,
      first: None,
      last: None,
      before: None,
      after: None,
      filterBy: None,
      search: None,
    })

    expect(
      makeQueryVariables(
        mockQueryVariables(~cku=mockedUuid->Scalar.CKU.serialize, ()),
        ~connectionArguments={
          first: 1,
          last: 2,
          before: "before",
          after: "after",
        },
        ~search="search",
        ~filterBy=mockQueryVariablesFilterBy(),
        (),
      ),
    )->toStrictEqual({
      CatalogVariantOrderSupplyPage.Query.cku: mockedUuid->Json.encodeString,
      first: Some(1),
      last: Some(2),
      before: Some("before"),
      after: Some("after"),
      search: Some("search"),
      filterBy: Some(
        CatalogVariantOrderSupplyPage.Query.makeInputObjectInputOrderProductsByVariantCkuQueryFilter(),
      ),
    })
  })

  test("makeQueryVariablesFilterBy", () => {
    let {makeQueryVariablesFilterBy} = module(CatalogVariantOrderSupplyPage.Scaffolded)

    mockDateNow(1530518293994.)

    let filters = {
      CatalogVariantOrderSupplyPage.Filters.shop: None,
    }

    expect(makeQueryVariablesFilterBy(filters))->toStrictEqual({
      CatalogVariantOrderSupplyPage.Query.shopIds: None,
      archived: Some(#INCLUDED),
      receptionFinishedAt: Some({
        _before: Some(Js.Date.now()->Js.Date.fromFloat->Scalar.Datetime.serialize),
        _after: None,
        _between: None,
      }),
    })

    let filters = {
      CatalogVariantOrderSupplyPage.Filters.shop: Some(mockShop(~id="mock-shop-id", ())),
    }

    expect(makeQueryVariablesFilterBy(filters))->toStrictEqual({
      CatalogVariantOrderSupplyPage.Query.shopIds: Some({_in: ["mock-shop-id"]}),
      archived: Some(#INCLUDED),
      receptionFinishedAt: Some({
        _before: Some(Js.Date.now()->Js.Date.fromFloat->Scalar.Datetime.serialize),
        _after: None,
        _between: None,
      }),
    })
  })

  test("totalCountFromQueryData", () => {
    let {totalCountFromQueryData} = module(CatalogVariantOrderSupplyPage.Scaffolded)

    let queryData = mockQueryData()
    expect(totalCountFromQueryData(queryData))->toBe(0)

    let queryData = mockQueryData(~totalCount=1, ())
    expect(totalCountFromQueryData(queryData))->toBe(1)

    let queryData = mockQueryData(~totalCount=100, ())
    expect(totalCountFromQueryData(queryData))->toBe(100)

    let queryData = mockQueryData(~totalCount=-1, ())
    expect(totalCountFromQueryData(queryData))->toBe(-1)
  })

  test("cursorsFromQueryData", () => {
    let {cursorsFromQueryData} = module(CatalogVariantOrderSupplyPage.Scaffolded)

    let queryData = mockQueryData()
    expect(cursorsFromQueryData(queryData))->toStrictEqual((None, None))

    let queryData = mockQueryData(~pageInfo=mockQueryDataPageInfo(~startCursor="start", ()), ())
    expect(cursorsFromQueryData(queryData))->toStrictEqual((Some("start"), None))

    let queryData = mockQueryData(
      ~pageInfo=mockQueryDataPageInfo(~startCursor="start", ~endCursor="end", ()),
      (),
    )
    expect(cursorsFromQueryData(queryData))->toStrictEqual((Some("start"), Some("end")))

    let queryData = mockQueryData(~pageInfo=mockQueryDataPageInfo(~endCursor="end", ()), ())
    expect(cursorsFromQueryData(queryData))->toStrictEqual((None, Some("end")))
  })

  test("rowsFromQueryDataAndState", () => {
    let {rowsFromQueryDataAndState} = module(CatalogVariantOrderSupplyPage.Scaffolded)

    let queryData = mockQueryData()
    expect(rowsFromQueryDataAndState(queryData, mockState()))->toStrictEqual([])

    let queryData = mockQueryData(~edges=[mockQueryDataEdge()], ())
    expect(rowsFromQueryDataAndState(queryData, mockState()))->toStrictEqual([
      {
        CatalogVariantOrderSupplyPage.Row.id: "",
        name: "?",
        shopName: "",
        receptionDate: Js.Date.fromFloat(0.),
        quantity: "0",
        totalAmountExcludingTaxes: 0.,
        totalAmountIncludingTaxes: 0.,
        supplierId: "",
        supplierCompanyName: "",
      },
    ])

    let queryData = mockQueryData(
      ~edges=[
        mockQueryDataEdge(
          ~quantity=1,
          ~totalAmountExcludingTaxes=5.,
          ~totalAmountIncludingTaxes=6.,
          ~orderId="mock-order-id",
          ~orderName=Some("mock-order-name"),
          ~orderSupplierCompanyName="mock-order-supplier-company-name",
          ~orderEstimatedReceptionDate=Js.Date.fromFloat(1.),
          ~orderSupplierId="mock-supplier-id",
          ~shopName="mock-shop-name",
          (),
        ),
      ],
      (),
    )

    expect(rowsFromQueryDataAndState(queryData, mockState()))->toStrictEqual([
      {
        CatalogVariantOrderSupplyPage.Row.id: "mock-order-id",
        name: "mock-order-name",
        shopName: "mock-shop-name",
        receptionDate: Js.Date.fromFloat(1.),
        quantity: "1",
        totalAmountExcludingTaxes: 5.,
        totalAmountIncludingTaxes: 6.,
        supplierId: "mock-supplier-id",
        supplierCompanyName: "mock-order-supplier-company-name",
      },
    ])
  })

  test("keyExtractor", () => {
    let {keyExtractor} = module(CatalogVariantOrderSupplyPage.Scaffolded)

    let row = mockRow()
    expect(keyExtractor(row))->toBe("")

    let row = mockRow(~id="mock-id", ())
    expect(keyExtractor(row))->toBe("mock-id")
  })
})

// Some integration tests to ensure basic interactions
// with the other components and GraphQL might be valuable
// at some point.
todo("Integration test")
