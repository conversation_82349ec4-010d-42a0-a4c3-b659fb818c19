open Vitest
open TestingLibraryReact

open SettingsBillingAccountShowPage

let mockBillingAddress = {
  CorporateEntity.Address.address: "mock-address",
  postalCode: "mock-postalcode",
  city: "mock-city",
  country: "FR",
}

let mockShippingAddress = {
  CorporateEntity.Address.address: "mock-shipping-address",
  postalCode: "mock-shipping-postalcode",
  city: "mock-shipping-city",
  country: "LU",
}

let {mockShop, mockUser} = module(Auth__Mock)

let mockAuthState = (~shops, ~activeShop) => {
  Auth__Types.user: mockUser(),
  shops,
  activeShop,
}

let mockBillingAccount: BillingAccount.info = {
  {
    corporateName: Some("mock-corporate-name"),
    vatNumber: Some("mock-vatNumber"),
    phone: Some("mock-phone"),
    email: Some("mock-email"),
    shopName: Some("mock-shop-name"),
    iban: Some({last4: "5555"}),
    billingAddress: Some(mockBillingAddress),
    shippingAddress: Some(mockShippingAddress),
  }
}

describe("BillingAccountRequest", () => {
  let {decodeResult} = module(BillingAccountRequest)

  test("decodeResult with all values", () =>
    expect(
      Json.fromObjExn({
        "corporateName": "mock-corporate-name",
        "shopName": "mock-shop-name",
        "phone": "mock-phone",
        "email": "mock-email",
        "address": {
          CorporateEntity.Address.address: "mock-address",
          postalCode: "mock-postalcode",
          city: "mock-city",
          country: "FR",
        },
        "shipping": {
          "address": {
            CorporateEntity.Address.address: "mock-shipping-address",
            postalCode: "mock-shipping-postalcode",
            city: "mock-shipping-city",
            country: "LU",
          },
        },
        "defaultPaymentMethod": {
          "kind": "sepa",
          "iban": {
            "bankCode": "BUKB",
            "branchCode": "202015",
            "country": "GB",
            "last4": "5555",
          },
        },
        "vatNumber": "mock-vatNumber",
      })->decodeResult,
    )->toStrictEqual(
      Some({
        corporateName: Some("mock-corporate-name"),
        shopName: Some("mock-shop-name"),
        email: Some("mock-email"),
        phone: Some("mock-phone"),
        billingAddress: Some({
          address: "mock-address",
          postalCode: "mock-postalcode",
          city: "mock-city",
          country: "FR",
        }),
        shippingAddress: Some(mockShippingAddress),
        vatNumber: Some("mock-vatNumber"),
        iban: Some({last4: "5555"}),
      }),
    )
  )

  test("decodeResult with missing values", () =>
    expect(
      Json.fromObjExn({
        "corporateName": "mock-corporate-name",
        "shopName": "mock-corporate-name",
        "phone": "mock-phone",
        "email": "mock-email",
      })->decodeResult,
    )->toStrictEqual(
      Some({
        corporateName: Some("mock-corporate-name"),
        shopName: Some("mock-corporate-name"),
        email: Some("mock-email"),
        phone: Some("mock-phone"),
        billingAddress: None,
        shippingAddress: None,
        vatNumber: None,
        iban: None,
      }),
    )
  )
})

test("BillingStatusRequest", () => {
  let {decodeBillingIssues} = module(BillingStatusRequest)

  expect(
    {Request.kind: "UnknownFailure", data: None, message: ""}->decodeBillingIssues,
  )->toStrictEqual(None)

  expect(
    {Request.kind: "InvalidBillingMandate", data: None, message: ""}->decodeBillingIssues,
  )->toStrictEqual(Some(InvalidBillingMandate))

  expect(
    {Request.kind: "MissingPaymentMethod", data: None, message: ""}->decodeBillingIssues,
  )->toStrictEqual(Some(MissingPaymentMethod))
  expect(
    {
      Request.kind: "PaymentOverdue",
      data: Some(Js.Dict.fromArray([("dueDate", Js.Json.number(10.))])->Js.Json.object_),
      message: "",
    }->decodeBillingIssues,
  )->toStrictEqual(Some(PaymentOverdue(Js.Date.fromFloat(10.))))
})

describe("SepaMandateRequest", () => {
  let {decodeResult} = module(SepaMandateRequest)

  test("decodeResult with active mandate", () => {
    let now = Js.Date.now()
    expect(
      Json.fromObjExn({
        "status": "active",
        "acceptedAt": now,
      })->decodeResult,
    )->toStrictEqual(
      Some({
        status: Active,
        acceptedAt: Some(now),
      }),
    )
  })

  test("decodeResult with inactive mandate", () => {
    let now = Js.Date.now()
    expect(
      Json.fromObjExn({
        "status": "inactive",
        "acceptedAt": now,
      })->decodeResult,
    )->toStrictEqual(
      Some({
        status: Inactive,
        acceptedAt: Some(now),
      }),
    )
  })

  test("decodeResult with pending mandate", () => {
    let now = Js.Date.now()
    expect(
      Json.fromObjExn({
        "status": "pending",
        "acceptedAt": now,
      })->decodeResult,
    )->toStrictEqual(
      Some({
        status: Pending,
        acceptedAt: Some(now),
      }),
    )
  })
})

describe("SubscriptionsRequest", () => {
  let {decodeResult} = module(SubscriptionsRequest)

  test("decodeResult with no plans nor options", () => {
    expect(
      Json.fromObjExn({
        "plans": [],
        "options": [],
      })->decodeResult,
    )->toStrictEqual(
      Some({
        plans: [],
        options: [],
      }),
    )
  })

  test("decodeResult with plans & options", () => {
    let now = Js.Date.now()
    expect(
      Json.fromObjExn({
        "plans": [
          {
            "kind": "boost",
            "upcomingInvoiceDate": now,
            "quantity": 3,
          },
        ],
        "options": [
          {
            "name": "option-mock",
            "upcomingInvoiceDate": now,
            "quantity": 2,
          },
        ],
      })->decodeResult,
    )->toStrictEqual(
      Some({
        plans: [
          {
            kind: Boost,
            quantity: 3,
            upcomingInvoiceDate: Some(Js.Date.fromFloat(now)),
          },
        ],
        options: [
          {
            name: "option-mock",
            quantity: 2,
            upcomingInvoiceDate: Some(Js.Date.fromFloat(now)),
          },
        ],
      }),
    )
  })
})

describe("ConfirmSepaMandateRequest", () => {
  let {encodeBody, decodeInvalidRequestFailure} = module(ConfirmSepaMandateRequest)

  test("encodeBody", () =>
    expect(
      encodeBody(
        ~shopId="mock-shopId",
        ~ipAddress="mock-ip",
        ~userAgent="mock-user-agent",
        ~acceptedAt=0.,
      ),
    )->toUnsafeStrictEqual({
      "shopId": "mock-shopId",
      "ipAddress": "mock-ip",
      "userAgent": "mock-user-agent",
      "acceptedAt": 0.,
    })
  )

  test("decodeInvalidRequestFailure", () => {
    let serverFailure = {
      Request.kind: "WrongSepaMandateAcceptanceDetails",
      message: "",
      data: None,
    }
    expect(serverFailure->decodeInvalidRequestFailure)->toStrictEqual(
      WrongSepaMandateAcceptanceDetails,
    )

    let serverFailure = {
      Request.kind: "Unknown",
      message: "",
      data: None,
    }
    expect(serverFailure->decodeInvalidRequestFailure)->toStrictEqual(Unknown)
  })
})

describe("AddressBox", () => {
  module AddressBox = AddressBox

  it("should display message if country code not known", () => {
    let mockAddress = {
      CorporateEntity.Address.address: "mock-address",
      postalCode: "mock-postalcode",
      city: "mock-city",
      country: "RU",
    }
    <AddressBox value=Some(mockAddress) title="mock-title" />->render->ignore

    expect(screen->getByTextExn("Country not specified"))->toBeVisible
  })
})

describe("SubscriptionsCard", () => {
  module TestableSubscriptionsCard = {
    @react.component
    let make = (~subscriptions) => {
      <Providers>
        <SettingsBillingAccountShowPage.SubscriptionsCard subscriptions={Some(subscriptions)} />
      </Providers>
    }
  }

  it("should display warning banner if no subscriptions", () => {
    <TestableSubscriptionsCard
      subscriptions={
        BillingAccount.plans: [],
        options: [],
      }
    />
    ->render
    ->ignore
    let warningMessage =
      screen->getByTextExn("Unable to display information about your subscription.")
    expect(warningMessage)->toBeVisible
  })

  it("it should display subscriptions", () => {
    <TestableSubscriptionsCard
      subscriptions={
        BillingAccount.plans: [
          {
            kind: Standard,
            quantity: 3,
            upcomingInvoiceDate: Some(Js.Date.fromFloat(0.)),
          },
        ],
        options: [],
      }
    />
    ->render
    ->ignore

    let subscriptionKindAndQuantity = screen->getByTextExn("Standard (x3)")
    expect(subscriptionKindAndQuantity)->toBeVisible
  })

  itPromise("it should show options when 'Options' is clicked", async () => {
    let userEvent = TestingLibraryEvent.setup()

    <TestableSubscriptionsCard
      subscriptions={
        BillingAccount.plans: [
          {
            kind: Standard,
            quantity: 1,
            upcomingInvoiceDate: Some(Js.Date.fromFloat(0.)),
          },
        ],
        options: [
          {
            name: "My option",
            quantity: 1,
            upcomingInvoiceDate: Some(Js.Date.fromFloat(0.)),
          },
        ],
      }
    />
    ->render
    ->ignore

    let option = screen->getByTextExn("My option")
    expect(option)->Vitest.not->toBeVisible

    let optionButton = screen->getByTextExn("Options")
    expect(optionButton)->toBeVisible

    await userEvent->TestingLibraryEvent.click(optionButton)

    let option = screen->getByTextExn("My option")
    expect(option)->toBeVisible
  })

  itPromise("it should show quantity only if > 1", async () => {
    let userEvent = TestingLibraryEvent.setup()

    <TestableSubscriptionsCard
      subscriptions={
        BillingAccount.plans: [
          {
            kind: Boost,
            quantity: 1,
            upcomingInvoiceDate: Some(Js.Date.fromFloat(0.)),
          },
        ],
        options: [
          {
            name: "My option",
            quantity: 2,
            upcomingInvoiceDate: Some(Js.Date.fromFloat(0.)),
          },
        ],
      }
    />
    ->render
    ->ignore

    let subscriptionKindAndQuantity = screen->getByTextExn("Boost")
    expect(subscriptionKindAndQuantity)->toBeVisible

    let optionButton = screen->getByTextExn("Options")
    expect(optionButton)->toBeVisible

    await userEvent->TestingLibraryEvent.click(optionButton)

    let option = screen->getByTextExn("My option (x2)")
    expect(option)->toBeVisible
  })

  // Waiting for subscription page to be created (CSM)
  // it("should display 'Discover our subscriptions.' link", () => {
  //  <TestableSubscriptionsCard
  //     subscriptions={
  //       BillingAccount.plans: [],
  //       options: [],
  //     }
  //   />->render->ignore
  //   let termsAndConditionsLink =
  //     screen->getByRoleWithOptionsExn(#link, {name: "Discover our subscriptions."})
  //   expect(termsAndConditionsLink)->toHaveAttributeValue("href", "https://wino.fr/")
  //   expect(termsAndConditionsLink)->toBeVisible
  // })
})

describe("UpdatePaymentMethodRequest", () => {
  let {encodeBody, decodeInvalidRequestFailure} = module(UpdatePaymentMethodRequest)

  test("encodeBody", () =>
    expect(
      encodeBody(
        ~shopId="mock-shopId",
        ~ibanNumber="mock-iban-number",
        ~ipAddress="mock-ip",
        ~userAgent="mock-user-agent",
        ~acceptedAt=0.,
      ),
    )->toUnsafeStrictEqual({
      "shopId": "mock-shopId",
      "iban": "mock-iban-number",
      "ipAddress": "mock-ip",
      "userAgent": "mock-user-agent",
      "acceptedAt": 0.,
    })
  )

  test("decodeInvalidRequestFailure", () => {
    let serverFailure = {
      Request.kind: "WrongSepaMandateAcceptanceDetails",
      message: "",
      data: None,
    }
    expect(serverFailure->decodeInvalidRequestFailure)->toStrictEqual(
      WrongSepaMandateAcceptanceDetails,
    )

    let serverFailure = {
      Request.kind: "InvalidIban",
      message: "",
      data: None,
    }
    expect(serverFailure->decodeInvalidRequestFailure)->toStrictEqual(InvalidIban)

    let serverFailure = {
      Request.kind: "Unknown",
      message: "",
      data: None,
    }
    expect(serverFailure->decodeInvalidRequestFailure)->toStrictEqual(Unknown)
  })
})

describe("EditPaymentMethodForm", () => {
  let {schema} = module(EditPaymentMethodFormModal)
  let {validate} = module(EditPaymentMethodForm)
  let mockState = (~ibanNumber="**********************", ()) => {
    EditPaymentMethodFormLenses.ibanNumber: ibanNumber,
  }

  it("should return no errors when all mandatory fields are well filled", () => {
    let values = mockState()
    expect(validate(~schema, ~values))->toStrictEqual(Ok())
  })

  it("should return an error on all noncorrect fields", () => {
    let values = mockState(~ibanNumber="", ())

    expect(validate(~schema, ~values))->toStrictEqual(
      Error([(Field(IbanNumber), "The IBAN number is not valid.")]),
    )
  })
})

describe("EditPaymentMethodFormModal", () => {
  module TestableEditPaymentMethodModal = {
    @react.component
    let make = (
      ~opened=true,
      ~onRequestCloseModal=() => (),
      ~title,
      ~activeShopId,
      ~alertBannerContent,
      ~updatePaymentMethodRequest,
      ~reloadBillingAccount,
      ~ipAddressRequest,
      ~reloadAlertBar,
      ~info,
    ) =>
      <Providers>
        <EditPaymentMethodFormModal
          opened
          title
          activeShopId
          onRequestCloseModal
          alertBannerContent
          updatePaymentMethodRequest
          reloadBillingAccount
          ipAddressRequest
          reloadAlertBar
          info
        />
      </Providers>
  }

  let mockUpdateRequest = (
    ~futureResult,
    ~shopId as _,
    ~ibanNumber as _,
    ~ipAddress as _,
    ~userAgent as _,
    ~acceptedAt as _,
  ) => {
    Future.makePure(resolve => resolve(futureResult()))
  }

  itPromise("show modal content and error message on form error", async () => {
    let updateRequestResult = fn1(() => Error(Some(UpdatePaymentMethodRequest.Unknown)))
    let updatePaymentMethodRequest = mockUpdateRequest(~futureResult=updateRequestResult->fn)
    let reloadBillingAccount = fn1(() => Future.makePure(resolve => resolve(Ok())))
    let ipAddressRequest = fn1(() => Future.makePure(resolve => resolve(Ok("mock-ip"))))
    let reloadAlertBar = fn1(() => Future.makePure(resolve => resolve(Ok())))
    let onRequestCloseModal = fn1(() => ())

    <TestableEditPaymentMethodModal
      title="Mock title"
      activeShopId="mock-shop-id"
      alertBannerContent=Some(Banner.Danger("mock-banner-content"))
      updatePaymentMethodRequest
      reloadBillingAccount={reloadBillingAccount->fn}
      ipAddressRequest={ipAddressRequest->fn}
      reloadAlertBar={reloadAlertBar->fn}
      onRequestCloseModal={onRequestCloseModal->fn}
      info=Some("mock-info")
    />
    ->render
    ->ignore

    let userEvent = TestingLibraryEvent.setup()

    let expectedText = "Mock title"
    expect(screen->getByTextExn(expectedText))->toBeVisible

    let expectedText = "mock-banner-content"
    expect(screen->getByTextExn(expectedText))->toBeVisible

    let ibanNumberInput = screen->getByLabelTextExn("IBAN number")
    await waitFor(() => expect(ibanNumberInput)->toBeVisible)
    await userEvent->TestingLibraryEvent.type_(ibanNumberInput, "wrong-iban")

    let button = screen->getAllByRoleWithOptionsExn(#button, {name: "Confirm"})->Array.getExn(0)
    expect(button)->toBeVisible
    expect(ipAddressRequest)->toHaveBeenCalledTimes(0)
    expect(updateRequestResult)->toHaveBeenCalledTimes(0)
    expect(reloadBillingAccount)->toHaveBeenCalledTimes(0)
    expect(reloadAlertBar)->toHaveBeenCalledTimes(0)
    expect(onRequestCloseModal)->toHaveBeenCalledTimes(0)

    await userEvent->TestingLibraryEvent.click(button)

    let expectedText = "There are some errors in the form, please correct them before trying to send it again."
    expect(screen->getByTextExn(expectedText))->toBeVisible

    let expectedLabel = "mock-info"
    expect(screen->getByTextExn(expectedLabel))->toBeVisible

    expect(ipAddressRequest)->toHaveBeenCalledTimes(0)
    expect(updateRequestResult)->toHaveBeenCalledTimes(0)
    expect(reloadBillingAccount)->toHaveBeenCalledTimes(0)
    expect(reloadAlertBar)->toHaveBeenCalledTimes(0)
    expect(onRequestCloseModal)->toHaveBeenCalledTimes(0)
  })

  itPromise("show error message on request failure", async () => {
    let updateRequestResult = fn1(() => Error(Some(UpdatePaymentMethodRequest.Unknown)))
    let updatePaymentMethodRequest = mockUpdateRequest(~futureResult=updateRequestResult->fn)
    let reloadBillingAccount = fn1(() => Future.makePure(resolve => resolve(Ok())))
    let ipAddressRequest = fn1(() => Future.makePure(resolve => resolve(Ok("mock-ip"))))
    let reloadAlertBar = fn1(() => Future.makePure(resolve => resolve(Ok())))
    let onRequestCloseModal = fn1(() => ())

    <TestableEditPaymentMethodModal
      title="Mock title"
      activeShopId="mock-shop-id"
      alertBannerContent=Some(Banner.Danger("mock-banner-content"))
      updatePaymentMethodRequest
      reloadBillingAccount={reloadBillingAccount->fn}
      ipAddressRequest={ipAddressRequest->fn}
      reloadAlertBar={reloadAlertBar->fn}
      onRequestCloseModal={onRequestCloseModal->fn}
      info={None}
    />
    ->render
    ->ignore

    let userEvent = TestingLibraryEvent.setup()

    let ibanNumberInput = screen->getByLabelTextExn("IBAN number")
    await waitFor(() => expect(ibanNumberInput)->toBeVisible)
    await userEvent->TestingLibraryEvent.type_(ibanNumberInput, "**********************")

    let button = screen->getAllByRoleWithOptionsExn(#button, {name: "Confirm"})->Array.getExn(0)
    expect(button)->toBeVisible
    expect(ipAddressRequest)->toHaveBeenCalledTimes(0)
    expect(updateRequestResult)->toHaveBeenCalledTimes(0)
    expect(reloadBillingAccount)->toHaveBeenCalledTimes(0)
    expect(reloadAlertBar)->toHaveBeenCalledTimes(0)
    expect(onRequestCloseModal)->toHaveBeenCalledTimes(0)

    await userEvent->TestingLibraryEvent.click(button)

    let expectedText = "An unexpected error occured. Please try again or contact the support."
    expect(screen->getByTextExn(expectedText))->toBeVisible

    expect(ipAddressRequest)->toHaveBeenCalledTimes(1)
    expect(updateRequestResult)->toHaveBeenCalledTimes(1)
    expect(reloadBillingAccount)->toHaveBeenCalledTimes(0)
    expect(reloadAlertBar)->toHaveBeenCalledTimes(0)
    expect(onRequestCloseModal)->toHaveBeenCalledTimes(0)
  })

  itPromise("show error message on request IP failure", async () => {
    let updateRequestResult = fn1(() => Error(Some(UpdatePaymentMethodRequest.Unknown)))
    let updatePaymentMethodRequest = mockUpdateRequest(~futureResult=updateRequestResult->fn)
    let reloadBillingAccount = fn1(() => Future.makePure(resolve => resolve(Ok())))
    let ipAddressRequest = fn1(() => Future.makePure(resolve => resolve(Error())))
    let reloadAlertBar = fn1(() => Future.makePure(resolve => resolve(Ok())))
    let onRequestCloseModal = fn1(() => ())

    <TestableEditPaymentMethodModal
      title="Mock title"
      activeShopId="mock-shop-id"
      alertBannerContent=Some(Banner.Danger("mock-banner-content"))
      updatePaymentMethodRequest
      reloadBillingAccount={reloadBillingAccount->fn}
      ipAddressRequest={ipAddressRequest->fn}
      reloadAlertBar={reloadAlertBar->fn}
      onRequestCloseModal={onRequestCloseModal->fn}
      info={None}
    />
    ->render
    ->ignore

    let userEvent = TestingLibraryEvent.setup()

    let ibanNumberInput = screen->getByLabelTextExn("IBAN number")
    await waitFor(() => expect(ibanNumberInput)->toBeVisible)

    await userEvent->TestingLibraryEvent.type_(ibanNumberInput, "**********************")

    let button = screen->getAllByRoleWithOptionsExn(#button, {name: "Confirm"})->Array.getExn(0)
    expect(button)->toBeVisible
    expect(ipAddressRequest)->toHaveBeenCalledTimes(0)
    expect(reloadBillingAccount)->toHaveBeenCalledTimes(0)
    expect(reloadAlertBar)->toHaveBeenCalledTimes(0)
    expect(onRequestCloseModal)->toHaveBeenCalledTimes(0)

    await userEvent->TestingLibraryEvent.click(button)

    let expectedText = "An unexpected error occured. Please try again or contact the support."
    expect(screen->getByTextExn(expectedText))->toBeVisible
    expect(ipAddressRequest)->toHaveBeenCalledTimes(1)
    expect(reloadBillingAccount)->toHaveBeenCalledTimes(0)
    expect(reloadAlertBar)->toHaveBeenCalledTimes(0)
    expect(onRequestCloseModal)->toHaveBeenCalledTimes(0)
  })

  itPromise("should call appropriate callbacks on success", async () => {
    let updateRequestResult = fn1(() => Ok())
    let updatePaymentMethodRequest = mockUpdateRequest(~futureResult=updateRequestResult->fn)
    let reloadBillingAccount = fn1(() => Future.makePure(resolve => resolve(Ok())))
    let ipAddressRequest = fn1(() => Future.makePure(resolve => resolve(Ok("mock-ip"))))
    let reloadAlertBar = fn1(() => Future.makePure(resolve => resolve(Ok())))
    let onRequestCloseModal = fn1(() => ())

    <TestableEditPaymentMethodModal
      title="Mock title"
      activeShopId="mock-shop-id"
      alertBannerContent=Some(Banner.Danger("mock-banner-content"))
      updatePaymentMethodRequest
      reloadBillingAccount={reloadBillingAccount->fn}
      ipAddressRequest={ipAddressRequest->fn}
      reloadAlertBar={reloadAlertBar->fn}
      onRequestCloseModal={onRequestCloseModal->fn}
      info={None}
    />
    ->render
    ->ignore

    let userEvent = TestingLibraryEvent.setup()

    expect(updateRequestResult)->toHaveBeenCalledTimes(0)
    let ibanNumberInput = screen->getByLabelTextExn("IBAN number")
    await waitFor(() => expect(ibanNumberInput)->toBeVisible)

    await userEvent->TestingLibraryEvent.type_(ibanNumberInput, "**********************")

    let button = screen->getAllByRoleWithOptionsExn(#button, {name: "Confirm"})->Array.getExn(0)
    expect(button)->toBeVisible
    expect(updateRequestResult)->toHaveBeenCalledTimes(0)
    expect(ipAddressRequest)->toHaveBeenCalledTimes(0)
    expect(reloadBillingAccount)->toHaveBeenCalledTimes(0)
    expect(reloadAlertBar)->toHaveBeenCalledTimes(0)
    expect(onRequestCloseModal)->toHaveBeenCalledTimes(0)

    await userEvent->TestingLibraryEvent.click(button)

    expect(updateRequestResult)->toHaveBeenCalledTimes(1)
    expect(ipAddressRequest)->toHaveBeenCalledTimes(1)
    expect(reloadBillingAccount)->toHaveBeenCalledTimes(1)
    expect(reloadAlertBar)->toHaveBeenCalledTimes(1)
    expect(onRequestCloseModal)->toHaveBeenCalledTimes(1)
  })
})

describe("EditPaymentMethodForm", () => {
  let {schema} = module(EditPaymentMethodFormModal)
  let {validate} = module(EditPaymentMethodForm)
  let mockState = (~ibanNumber="**********************", ()) => {
    EditPaymentMethodFormLenses.ibanNumber: ibanNumber,
  }

  it("should return no errors when all mandatory fields are well filled", () => {
    let values = mockState()
    expect(validate(~schema, ~values))->toStrictEqual(Ok())
  })

  it("should return an error on all noncorrect fields", () => {
    let values = mockState(~ibanNumber="", ())

    expect(validate(~schema, ~values))->toStrictEqual(
      Error([(Field(IbanNumber), "The IBAN number is not valid.")]),
    )
  })
})

describe("ConfirmSepaMandateModal", () => {
  module TestableConfirmSepaMandateModal = {
    @react.component
    let make = (
      ~activeShopId,
      ~alertBannerContent,
      ~confirmSepaMandateRequest,
      ~reloadBillingAccount,
      ~ipAddressRequest,
      ~reloadAlertBar,
      ~onRequestCloseModal=() => (),
    ) => {
      <Providers>
        <ConfirmSepaMandateModal
          opened=true
          onRequestCloseModal
          activeShopId
          alertBannerContent
          confirmSepaMandateRequest
          reloadBillingAccount
          ipAddressRequest
          reloadAlertBar
        />
      </Providers>
    }
  }

  let mockUpdateRequest = (
    ~futureResult,
    ~shopId as _,
    ~ipAddress as _,
    ~userAgent as _,
    ~acceptedAt as _,
  ) => {
    Future.makePure(resolve => resolve(futureResult()))
  }

  itPromise("show modal content and error message on IP request error", async () => {
    let confirmSepaMandateRequestResult = fn1(() => Error(Some(ConfirmSepaMandateRequest.Unknown)))
    let confirmSepaMandateRequest = mockUpdateRequest(
      ~futureResult=confirmSepaMandateRequestResult->fn,
    )
    let reloadBillingAccount = fn1(() => Future.makePure(resolve => resolve(Ok())))
    let ipAddressRequest = fn1(() => Future.makePure(resolve => resolve(Error())))
    let reloadAlertBar = fn1(() => Future.makePure(resolve => resolve(Ok())))
    let onRequestCloseModal = fn1(() => ())

    <TestableConfirmSepaMandateModal
      activeShopId="mock-shop-id"
      onRequestCloseModal={onRequestCloseModal->fn}
      alertBannerContent=Some(Banner.Danger("mock-banner-content"))
      confirmSepaMandateRequest
      reloadBillingAccount={reloadBillingAccount->fn}
      ipAddressRequest={ipAddressRequest->fn}
      reloadAlertBar={reloadAlertBar->fn}
    />
    ->render
    ->ignore

    let expectedText = "mock-banner-content"
    expect(screen->getByTextExn(expectedText))->toBeVisible

    expect(ipAddressRequest)->toHaveBeenCalledTimes(0)

    let userEvent = TestingLibraryEvent.setup()

    expect(ipAddressRequest)->toHaveBeenCalledTimes(0)

    let button = screen->getAllByRoleWithOptionsExn(#button, {name: "Confirm"})->Array.getExn(0)
    expect(button)->toBeVisible
    expect(ipAddressRequest)->toHaveBeenCalledTimes(0)
    expect(reloadAlertBar)->toHaveBeenCalledTimes(0)
    expect(reloadBillingAccount)->toHaveBeenCalledTimes(0)
    expect(onRequestCloseModal)->toHaveBeenCalledTimes(0)

    await userEvent->TestingLibraryEvent.click(button)

    let expectedText = "Your internet connection has been interrupted, please reload the page before submitting the form again."
    expect(screen->getByTextExn(expectedText))->toBeVisible
    expect(ipAddressRequest)->toHaveBeenCalledTimes(1)
    expect(reloadAlertBar)->toHaveBeenCalledTimes(0)
    expect(reloadBillingAccount)->toHaveBeenCalledTimes(0)
    expect(onRequestCloseModal)->toHaveBeenCalledTimes(0)
  })

  itPromise("show modal content and error message on request error", async () => {
    let confirmSepaMandateRequestResult = fn1(() => Error(Some(ConfirmSepaMandateRequest.Unknown)))
    let confirmSepaMandateRequest = mockUpdateRequest(
      ~futureResult=confirmSepaMandateRequestResult->fn,
    )
    let reloadBillingAccount = fn1(() => Future.makePure(resolve => resolve(Ok())))
    let ipAddressRequest = fn1(() => Future.makePure(resolve => resolve(Ok("mock-ip"))))
    let reloadAlertBar = fn1(() => Future.makePure(resolve => resolve(Ok())))
    let onRequestCloseModal = fn1(() => ())

    <TestableConfirmSepaMandateModal
      activeShopId="mock-shop-id"
      alertBannerContent=Some(Banner.Danger("mock-banner-content"))
      confirmSepaMandateRequest
      reloadBillingAccount={reloadBillingAccount->fn}
      ipAddressRequest={ipAddressRequest->fn}
      reloadAlertBar={reloadAlertBar->fn}
      onRequestCloseModal={onRequestCloseModal->fn}
    />
    ->render
    ->ignore

    let expectedText = "mock-banner-content"
    expect(screen->getByTextExn(expectedText))->toBeVisible

    expect(confirmSepaMandateRequestResult)->toHaveBeenCalledTimes(0)

    let userEvent = TestingLibraryEvent.setup()

    expect(confirmSepaMandateRequestResult)->toHaveBeenCalledTimes(0)

    let button = screen->getAllByRoleWithOptionsExn(#button, {name: "Confirm"})->Array.getExn(0)
    expect(button)->toBeVisible

    expect(confirmSepaMandateRequestResult)->toHaveBeenCalledTimes(0)
    expect(reloadAlertBar)->toHaveBeenCalledTimes(0)
    expect(reloadBillingAccount)->toHaveBeenCalledTimes(0)
    expect(onRequestCloseModal)->toHaveBeenCalledTimes(0)

    await userEvent->TestingLibraryEvent.click(button)

    let expectedText = "An unexpected error occured. Please try again or contact the support."
    expect(screen->getByTextExn(expectedText))->toBeVisible
    expect(confirmSepaMandateRequestResult)->toHaveBeenCalledTimes(1)

    expect(reloadAlertBar)->toHaveBeenCalledTimes(0)
    expect(reloadBillingAccount)->toHaveBeenCalledTimes(0)
    expect(onRequestCloseModal)->toHaveBeenCalledTimes(0)
  })

  itPromise("close modal and call reloads when request succeeds", async () => {
    let confirmSepaMandateRequestResult = fn1(() => Ok())
    let confirmSepaMandateRequest = mockUpdateRequest(
      ~futureResult=confirmSepaMandateRequestResult->fn,
    )
    let reloadBillingAccount = fn1(() => Future.makePure(resolve => resolve(Ok())))
    let ipAddressRequest = fn1(() => Future.makePure(resolve => resolve(Ok("mock-ip"))))
    let reloadAlertBar = fn1(() => Future.makePure(resolve => resolve(Ok())))
    let onRequestCloseModal = fn1(() => ())

    <TestableConfirmSepaMandateModal
      activeShopId="mock-shop-id"
      alertBannerContent=Some(Banner.Danger("mock-banner-content"))
      confirmSepaMandateRequest
      reloadBillingAccount={reloadBillingAccount->fn}
      ipAddressRequest={ipAddressRequest->fn}
      reloadAlertBar={reloadAlertBar->fn}
      onRequestCloseModal={onRequestCloseModal->fn}
    />
    ->render
    ->ignore

    let expectedText = "mock-banner-content"
    expect(screen->getByTextExn(expectedText))->toBeVisible

    expect(confirmSepaMandateRequestResult)->toHaveBeenCalledTimes(0)

    let userEvent = TestingLibraryEvent.setup()

    expect(confirmSepaMandateRequestResult)->toHaveBeenCalledTimes(0)

    let button = screen->getAllByRoleWithOptionsExn(#button, {name: "Confirm"})->Array.getExn(0)
    expect(button)->toBeVisible
    expect(confirmSepaMandateRequestResult)->toHaveBeenCalledTimes(0)
    expect(reloadAlertBar)->toHaveBeenCalledTimes(0)
    expect(reloadBillingAccount)->toHaveBeenCalledTimes(0)
    expect(onRequestCloseModal)->toHaveBeenCalledTimes(0)

    await userEvent->TestingLibraryEvent.click(button)

    expect(confirmSepaMandateRequestResult)->toHaveBeenCalledTimes(1)
    expect(reloadAlertBar)->toHaveBeenCalledTimes(1)
    expect(reloadBillingAccount)->toHaveBeenCalledTimes(1)
    expect(onRequestCloseModal)->toHaveBeenCalledTimes(1)
  })
})

describe("PaymentMethodCard", () => {
  module TestablePaymentMethodCard = {
    @react.component
    let make = (
      ~confirmSepaModalOpened,
      ~editPaymentMethodModalOpened,
      ~billingAccount,
      ~billingStatus,
      ~sepaMandateRequest,
      ~activeShopId,
      ~shopName,
      ~updatePaymentMethodRequest,
      ~confirmSepaMandateRequest,
      ~ipAddressRequest,
      ~reloadBillingAccount,
      ~reloadAlertBar,
      ~multishop=false,
    ) => {
      let shop = mockShop()
      let shops = switch multishop {
      | true => [shop, mockShop()]
      | false => [shop]
      }
      <Providers auth={Logged(mockAuthState(~activeShop=Some(shop), ~shops))}>
        <PaymentMethodCard
          confirmSepaModalOpened
          editPaymentMethodModalOpened
          onRequestCloseModal={() => ()}
          handleOpenEditPaymentMethodModal={() => ()}
          handleOpenConfirmMandateModal={() => ()}
          billingAccount
          billingStatus
          sepaMandateRequest
          activeShopId
          shopName
          updatePaymentMethodRequest
          confirmSepaMandateRequest
          ipAddressRequest
          reloadBillingAccount
          reloadAlertBar
        />
      </Providers>
    }
  }

  let mockUpdateRequest = (
    ~futureResult,
    ~shopId as _,
    ~ipAddress as _,
    ~userAgent as _,
    ~acceptedAt as _,
  ) => {
    Future.makePure(resolve => resolve(futureResult()))
  }

  let mockSepaMandateRequest = (~futureResult, ~shopId as _) => {
    Future.makePure(resolve => resolve(futureResult()))
  }

  itPromise("show banner if no payment method, but don't open modal", async () => {
    let confirmSepaMandateRequestResult = fn1(() => Ok())
    let confirmSepaMandateRequest = mockUpdateRequest(
      ~futureResult=confirmSepaMandateRequestResult->fn,
    )
    let sepaMandateRequestResult = fn1(() => Ok(None))
    let sepaMandateRequest = mockSepaMandateRequest(~futureResult=sepaMandateRequestResult->fn)
    let reloadBillingAccount = fn1(() => Future.makePure(resolve => resolve(Ok())))->fn
    let ipAddressRequest = fn1(() => Future.makePure(resolve => resolve(Error())))->fn
    let reloadAlertBar = fn1(() => Future.makePure(resolve => resolve(Ok())))->fn

    <TestablePaymentMethodCard
      confirmSepaModalOpened=false
      editPaymentMethodModalOpened=true
      billingAccount={Some({
        BillingAccount.corporateName: Some("mock-corporate-name"),
        vatNumber: Some("mock-vatNumber"),
        phone: Some("mock-phone"),
        email: Some("mock-email"),
        shopName: Some("mock-shop-name"),
        iban: None,
        billingAddress: Some(mockBillingAddress),
        shippingAddress: Some(mockShippingAddress),
      })}
      billingStatus=None
      sepaMandateRequest
      activeShopId="mock-shop-id"
      shopName="mock-shop-name"
      updatePaymentMethodRequest
      confirmSepaMandateRequest
      ipAddressRequest
      reloadBillingAccount
      reloadAlertBar
    />
    ->render
    ->ignore

    let expectedText = "Attention, no valid payment method is registered."
    expect(screen->getByTextExn(expectedText))->toBeVisible

    expect(sepaMandateRequestResult)->toHaveBeenCalledTimes(1)

    expect(screen->queryByText("Valid payment method is required."))->toBeNone
  })

  itPromise("show banner in modal if no payment method", async () => {
    let confirmSepaMandateRequestResult = fn1(() => Ok())
    let confirmSepaMandateRequest = mockUpdateRequest(
      ~futureResult=confirmSepaMandateRequestResult->fn,
    )
    let sepaMandateRequestResult = fn1(() => Ok(None))
    let sepaMandateRequest = mockSepaMandateRequest(~futureResult=sepaMandateRequestResult->fn)
    let reloadBillingAccount = fn1(() => Future.makePure(resolve => resolve(Ok())))->fn
    let ipAddressRequest = fn1(() => Future.makePure(resolve => resolve(Error())))->fn
    let reloadAlertBar = fn1(() => Future.makePure(resolve => resolve(Ok())))->fn

    <TestablePaymentMethodCard
      confirmSepaModalOpened=false
      editPaymentMethodModalOpened=true
      billingAccount={Some({
        BillingAccount.corporateName: Some("mock-corporate-name"),
        vatNumber: Some("mock-vatNumber"),
        phone: Some("mock-phone"),
        email: Some("mock-email"),
        shopName: Some("mock-shop-name"),
        iban: None,
        billingAddress: Some(mockBillingAddress),
        shippingAddress: Some(mockShippingAddress),
      })}
      billingStatus=None
      sepaMandateRequest
      activeShopId="mock-shop-id"
      shopName="mock-shop-name"
      updatePaymentMethodRequest
      confirmSepaMandateRequest
      ipAddressRequest
      reloadBillingAccount
      reloadAlertBar
      multishop=true
    />
    ->render
    ->ignore

    let expectedText = "Attention, no valid payment method is registered."
    expect(screen->getByTextExn(expectedText))->toBeVisible

    expect(sepaMandateRequestResult)->toHaveBeenCalledTimes(1)

    expect(
      screen->queryByText("Valid payment method is required for the shop mock-shop-name."),
    )->toBeNone
  })

  itPromise("display shop name in update payment modal banner if multi shop", async () => {
    let confirmSepaMandateRequestResult = fn1(() => Ok())
    let confirmSepaMandateRequest = mockUpdateRequest(
      ~futureResult=confirmSepaMandateRequestResult->fn,
    )
    let sepaMandateRequestResult = fn1(() => Ok(None))
    let sepaMandateRequest = mockSepaMandateRequest(~futureResult=sepaMandateRequestResult->fn)
    let reloadBillingAccount = fn1(() => Future.makePure(resolve => resolve(Ok())))->fn
    let ipAddressRequest = fn1(() => Future.makePure(resolve => resolve(Error())))->fn
    let reloadAlertBar = fn1(() => Future.makePure(resolve => resolve(Ok())))->fn

    <TestablePaymentMethodCard
      confirmSepaModalOpened=false
      editPaymentMethodModalOpened=true
      billingAccount={Some({
        BillingAccount.corporateName: Some("mock-corporate-name"),
        vatNumber: Some("mock-vatNumber"),
        phone: Some("mock-phone"),
        email: Some("mock-email"),
        shopName: Some("mock-shop-name"),
        iban: None,
        billingAddress: Some(mockBillingAddress),
        shippingAddress: Some(mockShippingAddress),
      })}
      billingStatus=Some(BillingAccount.BillingIssue.MissingPaymentMethod)
      sepaMandateRequest
      activeShopId="mock-shop-id"
      shopName="mock-shop-name"
      updatePaymentMethodRequest
      confirmSepaMandateRequest
      ipAddressRequest
      reloadBillingAccount
      reloadAlertBar
    />
    ->render
    ->ignore

    let expectedText = "Attention, no valid payment method is registered."
    expect(screen->getByTextExn(expectedText))->toBeVisible

    expect(sepaMandateRequestResult)->toHaveBeenCalledTimes(0)

    let expectedText = "Valid payment method is required."
    expect(screen->getByTextExn(expectedText))->toBeVisible
  })

  itPromise("show banner if no mandate, but don't open modal", async () => {
    let confirmSepaMandateRequestResult = fn1(() => Ok())
    let confirmSepaMandateRequest = mockUpdateRequest(
      ~futureResult=confirmSepaMandateRequestResult->fn,
    )
    let sepaMandateRequestResult = fn1(() => Ok(None))
    let sepaMandateRequest = mockSepaMandateRequest(~futureResult=sepaMandateRequestResult->fn)
    let reloadBillingAccount = fn1(() => Future.makePure(resolve => resolve(Ok())))->fn
    let ipAddressRequest = fn1(() => Future.makePure(resolve => resolve(Error())))->fn
    let reloadAlertBar = fn1(() => Future.makePure(resolve => resolve(Ok())))->fn

    <TestablePaymentMethodCard
      confirmSepaModalOpened=false
      editPaymentMethodModalOpened=false
      billingAccount={Some({
        BillingAccount.corporateName: Some("mock-corporate-name"),
        vatNumber: Some("mock-vatNumber"),
        phone: Some("mock-phone"),
        email: Some("mock-email"),
        shopName: Some("mock-shop-name"),
        iban: Some({last4: "mock-iban"}),
        billingAddress: Some(mockBillingAddress),
        shippingAddress: Some(mockShippingAddress),
      })}
      billingStatus=Some(BillingAccount.BillingIssue.InvalidBillingMandate)
      sepaMandateRequest
      activeShopId="mock-shop-id"
      shopName="mock-shop-name"
      updatePaymentMethodRequest
      confirmSepaMandateRequest
      ipAddressRequest
      reloadBillingAccount
      reloadAlertBar
    />
    ->render
    ->ignore

    let expectedText = "Please confirm your SEPA direct debit authorization."
    expect(screen->getByTextExn(expectedText))->toBeVisible
  })

  itPromise("show banner in modal if no mandate", async () => {
    let confirmSepaMandateRequestResult = fn1(() => Ok())
    let confirmSepaMandateRequest = mockUpdateRequest(
      ~futureResult=confirmSepaMandateRequestResult->fn,
    )
    let sepaMandateRequestResult = fn1(() => Ok(None))
    let sepaMandateRequest = mockSepaMandateRequest(~futureResult=sepaMandateRequestResult->fn)
    let reloadBillingAccount = fn1(() => Future.makePure(resolve => resolve(Ok())))->fn
    let ipAddressRequest = fn1(() => Future.makePure(resolve => resolve(Error())))->fn
    let reloadAlertBar = fn1(() => Future.makePure(resolve => resolve(Ok())))->fn

    <TestablePaymentMethodCard
      confirmSepaModalOpened=true
      editPaymentMethodModalOpened=false
      billingAccount={Some({
        BillingAccount.corporateName: Some("mock-corporate-name"),
        vatNumber: Some("mock-vatNumber"),
        phone: Some("mock-phone"),
        email: Some("mock-email"),
        shopName: Some("mock-shop-name"),
        iban: None,
        billingAddress: Some(mockBillingAddress),
        shippingAddress: Some(mockShippingAddress),
      })}
      billingStatus=Some(BillingAccount.BillingIssue.InvalidBillingMandate)
      sepaMandateRequest
      activeShopId="mock-shop-id"
      shopName="mock-shop-name"
      updatePaymentMethodRequest
      confirmSepaMandateRequest
      ipAddressRequest
      reloadBillingAccount
      reloadAlertBar
      multishop=true
    />
    ->render
    ->ignore

    let expectedText = "Please confirm your SEPA direct debit authorization."
    expect(screen->getByTextExn(expectedText))->toBeVisible
  })
})

module TestableSettingsBillingAccountShowPage = {
  @react.component
  let make = (
    ~shopId,
    ~billingAccountRequest,
    ~billingStatusRequest,
    ~sepaMandateRequest,
    ~updatePaymentMethodRequest,
    ~invoicesRequest,
    ~subscriptionsRequest,
    ~confirmSepaMandateRequest,
    ~ipAddressRequest,
    ~reloadAlertBar,
    ~confirmSepaModalOpened,
    ~editPaymentMethodModalOpened,
  ) => {
    <Providers>
      <SettingsBillingAccountShowPage
        shopId
        billingAccountRequest
        confirmSepaModalOpened
        billingStatusRequest
        editPaymentMethodModalOpened
        sepaMandateRequest
        updatePaymentMethodRequest
        invoicesRequest
        subscriptionsRequest
        confirmSepaMandateRequest
        ipAddressRequest
        reloadAlertBar
      />
    </Providers>
  }
}

let mockCustomerSepaMandateRequest = (
  ~mandate=Some({
    CorporateEntity.SepaMandate.status: Active,
    acceptedAt: Some(Js.Date.makeWithYMD(~year=2022., ~month=08., ~date=04., ())->Js.Date.getTime),
  }),
  (),
) => {
  (~shopId as _) => Future.value(Ok(mandate))
}

let mockInvoicesRequest = (
  ~invoices=[
    {
      BillingAccount.id: "mock-invoice-id",
      number: "mock-invoice-number",
      total: 10.,
      status: Some(BillingAccount.InvoiceStatus.Open),
      pdfLink: Some("https://mock-invoice-url.com/"),
      paymentLink: None,
      date: Js.Date.make(),
    },
  ],
  (),
) => {
  (~shopId as _) => Future.value(Ok(invoices))
}

let mockValidateSepaMandateRequest = (
  ~shopId as _,
  ~ipAddress as _,
  ~userAgent as _,
  ~acceptedAt as _,
  (),
) => Future.value(Ok())

let mockCustomerSubscriptionsRequest = (
  ~subscriptions={
    BillingAccount.plans: [],
    options: [],
  },
  (),
) => {
  (~shopId as _) => Future.value(Ok(Some(subscriptions)))
}

let mockBillingAccountRequest = (~shopId as _, ~billingAccount=Some(mockBillingAccount), ()) =>
  Future.makePure(resolve => resolve(Ok(billingAccount)))

let mockPaymentMethodUpdateRequest = (
  ~shopId as _,
  ~ibanNumber as _,
  ~ipAddress as _,
  ~userAgent as _,
  ~acceptedAt as _,
) => {
  Future.makePure(resolve => resolve(Ok()))
}

let mockIpAddressRequest = () => Future.makePure(resolve => resolve(Ok("mock-ip")))
let mockReloadAlertBarRequest = () => Future.makePure(resolve => resolve(Ok()))
let mockStatusRequest = (result: option<BillingAccount.BillingIssue.t>) => {
  (~shopId: string) => {
    shopId->ignore
    Future.value(Ok(result))
  }
}

describe("SettingsBillingAccountShowPage", () => {
  itPromise("should display all information", async () => {
    <TestableSettingsBillingAccountShowPage
      shopId="mock-shop-id"
      billingStatusRequest={mockStatusRequest(None)}
      billingAccountRequest={mockBillingAccountRequest()}
      sepaMandateRequest={mockCustomerSepaMandateRequest()}
      updatePaymentMethodRequest={mockPaymentMethodUpdateRequest}
      invoicesRequest={mockInvoicesRequest()}
      subscriptionsRequest={mockCustomerSubscriptionsRequest()}
      confirmSepaMandateRequest={mockValidateSepaMandateRequest()}
      ipAddressRequest={mockIpAddressRequest}
      reloadAlertBar={mockReloadAlertBarRequest}
      confirmSepaModalOpened=false
      editPaymentMethodModalOpened=false
    />
    ->render
    ->ignore

    await waitFor(
      () => {
        expect(screen->getByTextExn("SEPA direct debit • • • • " ++ "5555"))->toBeVisible
      },
    )

    expect(screen->getByTextExn("Accepted mandate on 9/4/22"))->toBeVisible

    expect(screen->queryByText("Attention, no valid payment method is registered."))->toBeNone

    expect(screen->getByTextExn("mock-corporate-name"))->toBeVisible
    expect(screen->getByTextExn("mock-email"))->toBeVisible
    expect(screen->getByTextExn("mock-phone"))->toBeVisible

    expect(screen->getByTextExn("mock-vatNumber"))->toBeVisible

    expect(screen->getByTextExn("mock-address"))->toBeVisible
    expect(screen->getByTextExn("mock-postalcode mock-city"))->toBeVisible
    expect(screen->getByTextExn("France"))->toBeVisible

    expect(screen->getByTextExn("mock-shipping-address"))->toBeVisible
    expect(screen->getByTextExn("mock-shipping-postalcode mock-shipping-city"))->toBeVisible
    expect(screen->getByTextExn("Luxembourg"))->toBeVisible

    expect(screen->queryByText("Address not provided"))->toBeNone
    expect(screen->queryByText("Attention, important information needs to be completed."))->toBeNone
    let unexpectedText = "Some subscription and billing settings cannot be displayed at the moment."
    expect(screen->queryByText(unexpectedText))->toBeNone

    expect(screen->getByTextExn("Invoice history"))->toBeVisible
    let invoiceLink = screen->getByRoleWithOptionsExn(#link, {name: "mock-invoice-number"})
    expect(invoiceLink)->toHaveAttributeValue("href", "https://mock-invoice-url.com/")
    expect(invoiceLink)->toBeVisible
    expect(screen->queryByText("Search an invoice"))->toBeNone

    let termsAndConditionsLink =
      screen->getByRoleWithOptionsExn(#button, {name: "Review the terms and conditions of sale."})
    expect(termsAndConditionsLink)->toBeVisible
  })

  itPromise("should display warning banner on missing vatNumber", async () => {
    let billingAccount = Some({
      ...mockBillingAccount,
      vatNumber: None,
    })

    <TestableSettingsBillingAccountShowPage
      shopId="mock-shop-id"
      billingStatusRequest={mockStatusRequest(None)}
      billingAccountRequest={mockBillingAccountRequest(~billingAccount, ())}
      sepaMandateRequest={mockCustomerSepaMandateRequest()}
      updatePaymentMethodRequest={mockPaymentMethodUpdateRequest}
      invoicesRequest={mockInvoicesRequest()}
      subscriptionsRequest={mockCustomerSubscriptionsRequest()}
      confirmSepaMandateRequest={mockValidateSepaMandateRequest()}
      ipAddressRequest={mockIpAddressRequest}
      reloadAlertBar={mockReloadAlertBarRequest}
      confirmSepaModalOpened=false
      editPaymentMethodModalOpened=false
    />
    ->render
    ->ignore

    let expectedText = "VAT number not provided"
    await waitFor(() => {expect(screen->getByTextExn(expectedText))->toBeVisible})
    expect(
      screen->getByTextExn("Attention, important information needs to be completed."),
    )->toBeVisible
    expect(
      screen->getByTextExn(
        "Some billing information are missing. While this is not required to use the solution, providing it is necessary to ensure legal compliance.",
      ),
    )->toBeVisible
  })

  itPromise("should display warning banner on missing phone", async () => {
    let billingAccount = Some({
      ...mockBillingAccount,
      phone: None,
    })

    <TestableSettingsBillingAccountShowPage
      shopId="mock-shop-id"
      billingStatusRequest={mockStatusRequest(None)}
      billingAccountRequest={mockBillingAccountRequest(~billingAccount, ())}
      sepaMandateRequest={mockCustomerSepaMandateRequest()}
      updatePaymentMethodRequest={mockPaymentMethodUpdateRequest}
      invoicesRequest={mockInvoicesRequest()}
      subscriptionsRequest={mockCustomerSubscriptionsRequest()}
      confirmSepaMandateRequest={mockValidateSepaMandateRequest()}
      ipAddressRequest={mockIpAddressRequest}
      reloadAlertBar={mockReloadAlertBarRequest}
      confirmSepaModalOpened=false
      editPaymentMethodModalOpened=false
    />
    ->render
    ->ignore

    let expectedText = "Phone number not provided"
    await waitFor(() => {expect(screen->getByTextExn(expectedText))->toBeVisible})
    expect(
      screen->getByTextExn("Attention, important information needs to be completed."),
    )->toBeVisible
    expect(
      screen->getByTextExn(
        "Some billing information are missing. While this is not required to use the solution, providing it is necessary to ensure legal compliance.",
      ),
    )->toBeVisible
  })

  itPromise("should display warning banner on missing corporate name", async () => {
    let billingAccount = Some({
      ...mockBillingAccount,
      corporateName: None,
    })

    <TestableSettingsBillingAccountShowPage
      shopId="mock-shop-id"
      billingStatusRequest={mockStatusRequest(None)}
      billingAccountRequest={mockBillingAccountRequest(~billingAccount, ())}
      sepaMandateRequest={mockCustomerSepaMandateRequest()}
      updatePaymentMethodRequest={mockPaymentMethodUpdateRequest}
      invoicesRequest={mockInvoicesRequest()}
      subscriptionsRequest={mockCustomerSubscriptionsRequest()}
      confirmSepaMandateRequest={mockValidateSepaMandateRequest()}
      ipAddressRequest={mockIpAddressRequest}
      reloadAlertBar={mockReloadAlertBarRequest}
      confirmSepaModalOpened=false
      editPaymentMethodModalOpened=false
    />
    ->render
    ->ignore

    let expectedText = "Corporate name not provided"
    await waitFor(() => {expect(screen->getByTextExn(expectedText))->toBeVisible})
    expect(
      screen->getByTextExn("Attention, important information needs to be completed."),
    )->toBeVisible
    expect(
      screen->getByTextExn(
        "Some billing information are missing. While this is not required to use the solution, providing it is necessary to ensure legal compliance.",
      ),
    )->toBeVisible
  })

  itPromise("should display warning banner on missing email", async () => {
    let billingAccount = Some({
      ...mockBillingAccount,
      email: None,
    })

    <TestableSettingsBillingAccountShowPage
      shopId="mock-shop-id"
      billingStatusRequest={mockStatusRequest(None)}
      billingAccountRequest={mockBillingAccountRequest(~billingAccount, ())}
      sepaMandateRequest={mockCustomerSepaMandateRequest()}
      updatePaymentMethodRequest={mockPaymentMethodUpdateRequest}
      invoicesRequest={mockInvoicesRequest()}
      subscriptionsRequest={mockCustomerSubscriptionsRequest()}
      confirmSepaMandateRequest={mockValidateSepaMandateRequest()}
      ipAddressRequest={mockIpAddressRequest}
      reloadAlertBar={mockReloadAlertBarRequest}
      confirmSepaModalOpened=false
      editPaymentMethodModalOpened=false
    />
    ->render
    ->ignore

    let expectedText = "Email not provided"
    await waitFor(() => {expect(screen->getByTextExn(expectedText))->toBeVisible})
    expect(
      screen->getByTextExn("Attention, important information needs to be completed."),
    )->toBeVisible
    expect(
      screen->getByTextExn(
        "Some billing information are missing. While this is not required to use the solution, providing it is necessary to ensure legal compliance.",
      ),
    )->toBeVisible
  })

  itPromise("should display warning banner on missing billing address", async () => {
    let billingAccount = Some({
      ...mockBillingAccount,
      billingAddress: None,
    })

    <TestableSettingsBillingAccountShowPage
      shopId="mock-shop-id"
      billingStatusRequest={mockStatusRequest(None)}
      billingAccountRequest={mockBillingAccountRequest(~billingAccount, ())}
      sepaMandateRequest={mockCustomerSepaMandateRequest()}
      updatePaymentMethodRequest={mockPaymentMethodUpdateRequest}
      invoicesRequest={mockInvoicesRequest()}
      subscriptionsRequest={mockCustomerSubscriptionsRequest()}
      confirmSepaMandateRequest={mockValidateSepaMandateRequest()}
      ipAddressRequest={mockIpAddressRequest}
      reloadAlertBar={mockReloadAlertBarRequest}
      confirmSepaModalOpened=false
      editPaymentMethodModalOpened=false
    />
    ->render
    ->ignore

    let expectedText = "Address not provided"
    await waitFor(() => {expect(screen->getByTextExn(expectedText))->toBeVisible})
    expect(
      screen->getByTextExn("Attention, important information needs to be completed."),
    )->toBeVisible
    let unexpectedText = "Some billing information are missing. While this is not required to use the solution, providing it is necessary to ensure legal compliance."
    expect(screen->getByTextExn(unexpectedText))->toBeVisible
  })

  itPromise("should display warning banner on missing shipping address", async () => {
    let billingAccount = Some({
      ...mockBillingAccount,
      shippingAddress: None,
    })

    <TestableSettingsBillingAccountShowPage
      shopId="mock-shop-id"
      billingStatusRequest={mockStatusRequest(None)}
      billingAccountRequest={mockBillingAccountRequest(~billingAccount, ())}
      sepaMandateRequest={mockCustomerSepaMandateRequest()}
      updatePaymentMethodRequest={mockPaymentMethodUpdateRequest}
      invoicesRequest={mockInvoicesRequest()}
      subscriptionsRequest={mockCustomerSubscriptionsRequest()}
      confirmSepaMandateRequest={mockValidateSepaMandateRequest()}
      ipAddressRequest={mockIpAddressRequest}
      reloadAlertBar={mockReloadAlertBarRequest}
      confirmSepaModalOpened=false
      editPaymentMethodModalOpened=false
    />
    ->render
    ->ignore

    let expectedText = "Address not provided"
    await waitFor(() => {expect(screen->getByTextExn(expectedText))->toBeVisible})
    expect(
      screen->getByTextExn("Attention, important information needs to be completed."),
    )->toBeVisible
    let unexpectedText = "Some billing information are missing. While this is not required to use the solution, providing it is necessary to ensure legal compliance."
    expect(screen->getByTextExn(unexpectedText))->toBeVisible
  })

  itPromise("should display warning banner on invalid mandate", async () => {
    <TestableSettingsBillingAccountShowPage
      shopId="mock-shop-id"
      billingStatusRequest={mockStatusRequest(Some(InvalidBillingMandate))}
      billingAccountRequest={mockBillingAccountRequest()}
      sepaMandateRequest={mockCustomerSepaMandateRequest()}
      updatePaymentMethodRequest={mockPaymentMethodUpdateRequest}
      invoicesRequest={mockInvoicesRequest()}
      subscriptionsRequest={mockCustomerSubscriptionsRequest()}
      confirmSepaMandateRequest={mockValidateSepaMandateRequest()}
      ipAddressRequest={mockIpAddressRequest}
      reloadAlertBar={mockReloadAlertBarRequest}
      confirmSepaModalOpened=false
      editPaymentMethodModalOpened=false
    />
    ->render
    ->ignore

    let expectedText = "Please confirm your SEPA direct debit authorization."
    expect(screen->getByTextExn(expectedText))->toBeVisible
  })

  itPromise("should display error banner on billing account request failure", async () => {
    let mockBillingAccountRequest = (~shopId as _, ()) =>
      Future.makePure(resolve => resolve(Error()))
    <TestableSettingsBillingAccountShowPage
      shopId="mock-shop-id"
      billingStatusRequest={mockStatusRequest(Some(InvalidBillingMandate))}
      billingAccountRequest={mockBillingAccountRequest()}
      sepaMandateRequest={mockCustomerSepaMandateRequest()}
      updatePaymentMethodRequest={mockPaymentMethodUpdateRequest}
      invoicesRequest={mockInvoicesRequest()}
      subscriptionsRequest={mockCustomerSubscriptionsRequest()}
      confirmSepaMandateRequest={mockValidateSepaMandateRequest()}
      ipAddressRequest={mockIpAddressRequest}
      reloadAlertBar={mockReloadAlertBarRequest}
      confirmSepaModalOpened=false
      editPaymentMethodModalOpened=false
    />
    ->render
    ->ignore

    let expectedText = "An unexpected error occured. Please try again or contact the support."
    await waitFor(() => expect(screen->getByTextExn(expectedText))->toBeVisible)
  })

  itPromise("should open edit payment modal when set", async () => {
    <TestableSettingsBillingAccountShowPage
      shopId="mock-shop-id"
      billingStatusRequest={mockStatusRequest(Some(InvalidBillingMandate))}
      billingAccountRequest={mockBillingAccountRequest()}
      sepaMandateRequest={mockCustomerSepaMandateRequest()}
      updatePaymentMethodRequest={mockPaymentMethodUpdateRequest}
      invoicesRequest={mockInvoicesRequest()}
      subscriptionsRequest={mockCustomerSubscriptionsRequest()}
      confirmSepaMandateRequest={mockValidateSepaMandateRequest()}
      ipAddressRequest={mockIpAddressRequest}
      reloadAlertBar={mockReloadAlertBarRequest}
      confirmSepaModalOpened=true
      editPaymentMethodModalOpened=false
    />
    ->render
    ->ignore

    let expectedText = "Confirm Your SEPA Direct Debit Authorization"
    await waitFor(() => expect(screen->getByTextExn(expectedText))->toBeVisible)
  })

  itPromise("should open validate sepa modal when set", async () => {
    <TestableSettingsBillingAccountShowPage
      shopId="mock-shop-id"
      billingStatusRequest={mockStatusRequest(Some(MissingPaymentMethod))}
      billingAccountRequest={mockBillingAccountRequest(
        ~billingAccount=Some({...mockBillingAccount, iban: None}),
        (),
      )}
      sepaMandateRequest={mockCustomerSepaMandateRequest()}
      updatePaymentMethodRequest={mockPaymentMethodUpdateRequest}
      invoicesRequest={mockInvoicesRequest()}
      subscriptionsRequest={mockCustomerSubscriptionsRequest()}
      confirmSepaMandateRequest={mockValidateSepaMandateRequest()}
      ipAddressRequest={mockIpAddressRequest}
      reloadAlertBar={mockReloadAlertBarRequest}
      confirmSepaModalOpened=false
      editPaymentMethodModalOpened=true
    />
    ->render
    ->ignore

    let expectedText = "Add a payment method"
    await waitFor(() => expect(screen->getByTextExn(expectedText))->toBeVisible)
  })
})
