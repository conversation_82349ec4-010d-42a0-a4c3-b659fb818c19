open Vitest

let {mockShop} = module(Auth__Mock)

let mockRow = (~cku="", ~id="", ()) => {
  PromotionListPage.Row.cku,
  id,
  name: "",
  startDate: Js.Date.fromFloat(0.),
  endDate: Js.Date.fromFloat(0.),
  discountsCount: 0,
  priceName: "",
  formattedShopsNames: "",
  formattedStatus: #ARCHIVED,
}

let mockTypename = () => ""

let mockQueryDataEdge = (
  ~id="",
  ~cku="",
  ~name="",
  ~startDate=Js.Date.fromFloat(0.),
  ~endDate=Js.Date.fromFloat(0.),
  ~discountsTotalCount=0,
  ~formattedShopsNames="",
  ~priceName="",
  ~formattedStatus=#ARCHIVED,
  (),
) => {
  PromotionListPage.Query.node: {
    id,
    cku,
    name,
    startDate,
    endDate,
    formattedShopsNames,
    formattedStatus,
    discounts: {
      totalCount: discountsTotalCount,
      __typename: mockTypename(),
    },
    price: {
      name: priceName,
      __typename: mockTypename(),
    },
    __typename: mockTypename(),
  },
  __typename: mockTypename(),
}

let mockQueryDataPageInfo = (~startCursor=?, ~endCursor=?, ()) => {
  PromotionListPage.Query.startCursor,
  endCursor,
  __typename: mockTypename(),
}

let mockQueryData = (~edges=[], ~pageInfo=mockQueryDataPageInfo(), ~totalCount=0, ()) => {
  PromotionListPage.Query.promotionCampaignsDistinctOnCku: {
    edges,
    pageInfo,
    totalCount,
    __typename: mockTypename(),
  },
}

let mockQueryVariablesFilterBy = () =>
  PromotionListPage.Query.makeInputObjectInputPromotionCampaignsQueryFilter()

let mockQueryVariables = () => {
  PromotionListPage.Query.first: None,
  last: None,
  before: None,
  after: None,
  filterBy: None,
  search: None,
}

let mockStateFilters = (~shop=?, ()) => {
  PromotionListPage.Filters.shop: shop,
}

let mockState = (
  ~filters=mockStateFilters(),
  ~currentPage=1,
  ~previousPage=-1,
  ~searchQuery=?,
  (),
) => {
  Scaffold.filters,
  connectionArguments: {},
  currentPage,
  previousPage,
  searchQuery,
}

describe("Scaffolded", () => {
  todo("useFiltersJsonCodec")

  test("makeQueryVariables", () => {
    let {makeQueryVariables} = module(PromotionListPage.Scaffolded)

    expect(makeQueryVariables(mockQueryVariables(), ~connectionArguments={}, ()))->toStrictEqual({
      PromotionListPage.Query.first: None,
      last: None,
      before: None,
      after: None,
      filterBy: None,
      search: None,
    })

    expect(
      makeQueryVariables(
        mockQueryVariables(),
        ~connectionArguments={
          first: 1,
          last: 2,
          before: "before",
          after: "after",
        },
        ~search="search",
        ~filterBy=mockQueryVariablesFilterBy(),
        (),
      ),
    )->toStrictEqual({
      PromotionListPage.Query.first: Some(1),
      last: Some(2),
      before: Some("before"),
      after: Some("after"),
      search: Some("search"),
      filterBy: Some(PromotionListPage.Query.makeInputObjectInputPromotionCampaignsQueryFilter()),
    })
  })

  test("makeQueryVariablesFilterBy", () => {
    let {makeQueryVariablesFilterBy} = module(PromotionListPage.Scaffolded)

    let filters = {PromotionListPage.Filters.shop: None}
    expect(makeQueryVariablesFilterBy(filters))->toStrictEqual({
      PromotionListPage.Query.shopIds: None,
      archived: Some(#EXCLUDED),
    })

    let filters = {
      PromotionListPage.Filters.shop: Some(mockShop(~id="mock-shop-id", ())),
    }
    expect(makeQueryVariablesFilterBy(filters))->toStrictEqual({
      PromotionListPage.Query.shopIds: Some({_in: ["mock-shop-id"]}),
      archived: Some(#EXCLUDED),
    })
  })

  test("totalCountFromQueryData", () => {
    let {totalCountFromQueryData} = module(PromotionListPage.Scaffolded)

    let queryData = mockQueryData()
    expect(totalCountFromQueryData(queryData))->toBe(0)

    let queryData = mockQueryData(~totalCount=1, ())
    expect(totalCountFromQueryData(queryData))->toBe(1)

    let queryData = mockQueryData(~totalCount=100, ())
    expect(totalCountFromQueryData(queryData))->toBe(100)

    let queryData = mockQueryData(~totalCount=-1, ())
    expect(totalCountFromQueryData(queryData))->toBe(-1)
  })

  test("cursorsFromQueryData", () => {
    let {cursorsFromQueryData} = module(PromotionListPage.Scaffolded)

    let queryData = mockQueryData()
    expect(cursorsFromQueryData(queryData))->toStrictEqual((None, None))

    let queryData = mockQueryData(~pageInfo=mockQueryDataPageInfo(~startCursor="start", ()), ())
    expect(cursorsFromQueryData(queryData))->toStrictEqual((Some("start"), None))

    let queryData = mockQueryData(
      ~pageInfo=mockQueryDataPageInfo(~startCursor="start", ~endCursor="end", ()),
      (),
    )
    expect(cursorsFromQueryData(queryData))->toStrictEqual((Some("start"), Some("end")))

    let queryData = mockQueryData(~pageInfo=mockQueryDataPageInfo(~endCursor="end", ()), ())
    expect(cursorsFromQueryData(queryData))->toStrictEqual((None, Some("end")))
  })

  test("rowsFromQueryDataAndState", () => {
    let {rowsFromQueryDataAndState} = module(PromotionListPage.Scaffolded)

    let queryData = mockQueryData()
    expect(rowsFromQueryDataAndState(queryData, mockState()))->toStrictEqual([])

    let queryData = mockQueryData(~edges=[mockQueryDataEdge()], ())
    expect(rowsFromQueryDataAndState(queryData, mockState()))->toStrictEqual([
      {
        cku: "",
        id: "",
        name: "",
        startDate: Js.Date.fromFloat(0.),
        endDate: Js.Date.fromFloat(0.),
        discountsCount: 0,
        priceName: "",
        formattedShopsNames: "",
        formattedStatus: #ARCHIVED,
      },
    ])

    let queryData = mockQueryData(
      ~edges=[
        mockQueryDataEdge(),
        mockQueryDataEdge(
          ~id="mock-id",
          ~cku="mock-cku",
          ~name="mock-name",
          ~startDate=Js.Date.fromFloat(-1.),
          ~endDate=Js.Date.fromFloat(1.),
          ~discountsTotalCount=1,
          ~formattedShopsNames="mock-formattedShopsNames",
          ~priceName="mock-price-name",
          ~formattedStatus=#STOPPED,
          (),
        ),
      ],
      (),
    )
    expect(rowsFromQueryDataAndState(queryData, mockState()))->toStrictEqual([
      {
        cku: "",
        id: "",
        name: "",
        startDate: Js.Date.fromFloat(0.),
        endDate: Js.Date.fromFloat(0.),
        discountsCount: 0,
        priceName: "",
        formattedShopsNames: "",
        formattedStatus: #ARCHIVED,
      },
      {
        cku: "mock-cku",
        id: "mock-id",
        name: "mock-name",
        startDate: Js.Date.fromFloat(-1.),
        endDate: Js.Date.fromFloat(1.),
        discountsCount: 1,
        priceName: "mock-price-name",
        formattedShopsNames: "mock-formattedShopsNames",
        formattedStatus: #STOPPED,
      },
    ])
  })

  test("keyExtractor", () => {
    let {keyExtractor} = module(PromotionListPage.Scaffolded)

    let row = mockRow()
    expect(keyExtractor(row))->toBe("")

    let row = mockRow(~cku="mock-cku", ())
    expect(keyExtractor(row))->toBe("")

    let row = mockRow(~id="mock-id", ())
    expect(keyExtractor(row))->toBe("mock-id")
  })
})

// Some integration tests to ensure basic interactions
// with the other components and GraphQL might be valuable
// at some point.
todo("Integration test")
