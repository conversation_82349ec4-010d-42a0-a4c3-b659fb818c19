open Vitest
open TestingLibraryReact

describe("UpdatePasswordRequest", () => {
  let {encodeBody, decodeInvalidRequestFailure} = module(
    AuthUsernameUpdatePage.AuthUsernameUpdateRequest
  )

  test("encodeBody", () =>
    expect(
      encodeBody(
        ~token="mock-token",
        ~tokenId="mock-tokenId",
        ~userId="mock-userId",
        ~username="new-username",
      )->Json.stringify,
    )->toStrictEqual(`{"token":"mock-token","tokenId":"mock-tokenId","userId":"mock-userId","username":"new-username"}`)
  )

  test("decodeInvalidRequestFailure", () => {
    let serverFailure = {
      Request.kind: "ExpiredOneTimeToken",
      message: "",
      data: None,
    }
    expect(serverFailure->decodeInvalidRequestFailure)->toStrictEqual(ExpiredOneTimeToken)

    let serverFailure = {
      Request.kind: "DuplicateUserUsername",
      message: "",
      data: None,
    }
    expect(serverFailure->decodeInvalidRequestFailure)->toStrictEqual(DuplicateUserUsername)

    let serverFailure = {
      Request.kind: "Unknown",
      message: "",
      data: None,
    }
    expect(serverFailure->decodeInvalidRequestFailure)->toStrictEqual(UnknownServerFailure)
  })
})

describe("AuthUsernameUpdatePage", () => {
  let userId = "mock-userId"
  let tokenId = "mock-tokenId"
  let token = "mock-token"
  let username = "new-username"

  module TestableAuthUsernameUpdatePage = {
    @react.component
    let make = (~userId, ~tokenId, ~token, ~username, ~request, ~history=?) =>
      <Providers ?history>
        <AuthUsernameUpdatePage userId tokenId token username usernameUpdateRequest=request />
      </Providers>
  }

  let mockUsernameUpdateRequest = (~futureResult) => {
    (~token as _, ~tokenId as _, ~userId as _, ~username as _) =>
      Future.makePure(resolve => resolve(futureResult()))
  }

  itPromise("should display error when the request fails", async () => {
    let requestResult = fn1(() => Error(None))
    let request = mockUsernameUpdateRequest(~futureResult=requestResult->fn)

    let _ = <TestableAuthUsernameUpdatePage userId tokenId token username request />->render

    await waitFor(
      () =>
        expect(
          screen->getByTextExn(
            "An unexpected error occured. Please try again or contact the support.",
          ),
        )->toBeVisible,
    )
  })

  itPromise("should display error when the request fails with DuplicateUserUsername", async () => {
    let requestResult = fn1(
      () => Error(Some(AuthUsernameUpdatePage.AuthUsernameUpdateRequest.DuplicateUserUsername)),
    )
    let request = mockUsernameUpdateRequest(~futureResult=requestResult->fn)

    let _ = <TestableAuthUsernameUpdatePage userId tokenId token username request />->render

    await waitFor(
      () => expect(screen->getByTextExn("This email address is already used."))->toBeVisible,
    )
  })

  itPromise("should redirect to login on success", async () => {
    let requestResult = fn1(() => Ok(Js.Dict.empty()->Js.Json.object_))
    let request = mockUsernameUpdateRequest(~futureResult=requestResult->fn)

    let history = History.createMemoryHistory()
    expect(history.location.pathname)->toBe("/")

    let _ = <TestableAuthUsernameUpdatePage userId tokenId token username history request />->render

    await waitFor(
      () =>
        expect(history.location.pathname)->toBe("/auth/login/username-updated/" ++ username ++ "/"),
    )
  })
})
