open Vitest
open CatalogListPage

describe("CatalogPriceLookUpExportMenuItem", () => {
  test("encodeRequestBodyJson", () => {
    let {encodeRequestBodyJson} = module(CatalogListPriceLookUpExportMenuItem)

    expect(encodeRequestBodyJson(~shopId="mock-shop-id", ()))->toUnsafeStrictEqual({
      "shopId": "mock-shop-id",
    })
    expect(encodeRequestBodyJson(~shopId="", ()))->toUnsafeStrictEqual({
      "shopId": "",
    })
    expect(
      encodeRequestBodyJson(~shopId="mock-shop-id", ~variantsArchived=true, ()),
    )->toUnsafeStrictEqual({
      "shopId": "mock-shop-id",
      "variantsArchived": true,
    })
    expect(
      encodeRequestBodyJson(~shopId="mock-shop-id", ~variantsActive=true, ()),
    )->toUnsafeStrictEqual({
      "shopId": "mock-shop-id",
      "variantsActive": true,
    })
    expect(
      encodeRequestBodyJson(
        ~shopId="mock-shop-id",
        ~variantsArchived=false,
        ~variantsActive=true,
        (),
      ),
    )->toUnsafeStrictEqual({
      "shopId": "mock-shop-id",
      "variantsArchived": false,
      "variantsActive": true,
    })
  })
})

describe("CatalogListPricingSettings", () => {
  open CatalogListPricingSettings

  describe("PriceDifferentialIndicator", () => {
    let {toString, fromString} = module(PriceDifferentialIndicator)
    test(
      "toString",
      () => {
        expect(toString(Coefficient))->toBe("coefficient")
        expect(toString(MarkupRate))->toBe("markup-rate")
        expect(toString(MarginRate))->toBe("margin-rate")
      },
    )
    test(
      "fromString",
      () => {
        expect(fromString("coefficient"))->toStrictEqual(Ok(Coefficient))
        expect(fromString("markup-rate"))->toStrictEqual(Ok(MarkupRate))
        expect(fromString("margin-rate"))->toStrictEqual(Ok(MarginRate))
        expect(fromString("Other"))->toStrictEqual(Error())
        expect(fromString(""))->toStrictEqual(Error())
      },
    )
  })

  describe("PurchasePriceType", () => {
    let {toString, fromString} = module(PurchasePriceType)
    test(
      "toString",
      () => {
        expect(toString(Gross))->toBe("gross")
        expect(toString(Net))->toBe("net")
      },
    )
    test(
      "fromString",
      () => {
        expect(fromString("gross"))->toStrictEqual(Ok(Gross))
        expect(fromString("net"))->toStrictEqual(Ok(Net))
        expect(fromString("Other"))->toStrictEqual(Error())
        expect(fromString(""))->toStrictEqual(Error())
      },
    )
  })

  describe("RetailPriceCalculatingMethod", () => {
    let {toString, fromString} = module(RetailPriceCalculatingMethod)
    test(
      "toString",
      () => {
        expect(toString(PurchasePrice))->toBe("purchase-price")
        expect(toString(FullPurchasePrice))->toBe("full-purchase-price")
        expect(toString(AcquisitionCost))->toBe("acquisition-cost")
      },
    )
    test(
      "fromString",
      () => {
        expect(fromString("purchase-price"))->toStrictEqual(Ok(PurchasePrice))
        expect(fromString("full-purchase-price"))->toStrictEqual(Ok(FullPurchasePrice))
        expect(fromString("acquisition-cost"))->toStrictEqual(Ok(AcquisitionCost))
        expect(fromString("Other"))->toStrictEqual(Error())
        expect(fromString(""))->toStrictEqual(Error())
      },
    )
  })

  describe("UserPreferences", () => {
    let {encoder, decoder} = module(UserPreferences)

    let initialValue = {
      priceDifferentialIndicator: Coefficient,
      purchasePriceType: Gross,
      retailPriceCalculatingMethod: PurchasePrice,
    }

    it(
      "should encode and decode without error",
      () => {
        let json = encoder(initialValue)

        expect(json)->toUnsafeStrictEqual({
          "priceDifferentialIndicator": "coefficient",
          "purchasePriceType": "gross",
          "retailPriceCalculatingMethod": "purchase-price",
        })
        expect(decoder(json))->toStrictEqual(
          Ok({
            priceDifferentialIndicator: Coefficient,
            purchasePriceType: Gross,
            retailPriceCalculatingMethod: PurchasePrice,
          }),
        )
      },
    )

    it(
      "should encode with a syntax error",
      () => {
        let json = Json.fromObjExn({
          "unknown": "coefficient",
          "purchasePriceType": "gross",
          "retailPriceCalculatingMethod": "purchase-price",
        })
        expect(decoder(json))->toStrictEqual(Error(#MissingField([], "priceDifferentialIndicator")))

        let json = Json.fromObjExn({
          "priceDifferentialIndicator": "unknown",
          "purchasePriceType": "gross",
          "retailPriceCalculatingMethod": "purchase-price",
        })
        expect(decoder(json))->toStrictEqual(Error(#SyntaxError("Could not decode values")))
      },
    )
  })
})

describe("CatalogListSettingsPopoverButton", () => {
  open TestingLibraryReact

  let userEvent = TestingLibraryEvent.setup()

  let render = element =>
    element->renderWithOptions(
      ~options={wrapper: props => <Providers> {props["children"]} </Providers>},
    )

  testPromise("component", async () => {
    let onChange = fn1(ignore)
    let settingsUserPreferencesStore = fn1(_state => ())

    let settingsUserPreferencesRead = () => Some({
      CatalogListPricingSettings.priceDifferentialIndicator: MarkupRate,
      purchasePriceType: Gross,
      retailPriceCalculatingMethod: PurchasePrice,
    })

    render(
      <CatalogListSettingsPopoverButton
        settingsUserPreferencesRead
        settingsUserPreferencesStore={settingsUserPreferencesStore->fn}
        onChange={onChange->fn}
      />,
    )->ignore

    let triggerElement =
      screen->getByRoleWithOptionsExn(#button, {name: "settings", expanded: false, hidden: false})

    expect(triggerElement)->toBeVisible

    await userEvent->TestingLibraryEvent.click(triggerElement)

    expect(onChange)->toHaveBeenCalledTimes(1)
    expect(onChange)->toHaveBeenCalledWith1({
      priceDifferentialIndicator: MarkupRate,
      purchasePriceType: Gross,
      retailPriceCalculatingMethod: PurchasePrice,
    })

    let popoverDialog = screen->getByRoleWithOptionsExn(#dialog, {name: "Settings"})

    let (radioGroupPriceDifferentialIndicator, _, _) =
      within(popoverDialog)->getAllByRoleExn3(#radiogroup)
    let (radioMultiplier, radioMarginRate, radioMarkupRate) =
      within(radioGroupPriceDifferentialIndicator)->getAllByRoleExn3(#radio)

    expect(popoverDialog)->toBeVisible

    expect(radioMultiplier->WebAPI.DomElement.checked)->toBe(false)
    expect(radioMarginRate->WebAPI.DomElement.checked)->toBe(false)
    expect(radioMarkupRate->WebAPI.DomElement.checked)->toBe(true)

    let commitElement = screen->getByRoleWithOptionsExn(#button, {name: "Save settings"})

    expect(commitElement)->toBeVisible
    expect(commitElement)->toHaveAttributeValue("aria-disabled", "true")

    await userEvent->TestingLibraryEvent.click(radioMultiplier)

    expect(radioMultiplier->WebAPI.DomElement.checked)->toBe(true)
    expect(radioMarginRate->WebAPI.DomElement.checked)->toBe(false)
    expect(radioMarkupRate->WebAPI.DomElement.checked)->toBe(false)

    expect(commitElement)->Vitest.not->toHaveAttribute("aria-disabled")

    expect(settingsUserPreferencesStore)->toHaveBeenCalledTimes(0)
    expect(onChange)->toHaveBeenCalledTimes(1)
    expect(onChange)->toHaveBeenCalledWith1({
      priceDifferentialIndicator: MarkupRate,
      purchasePriceType: Gross,
      retailPriceCalculatingMethod: PurchasePrice,
    })

    await userEvent->TestingLibraryEvent.click(commitElement)

    expect(popoverDialog)->Vitest.not->toBeVisible

    let newSettings = {
      CatalogListPricingSettings.priceDifferentialIndicator: Coefficient,
      purchasePriceType: Gross,
      retailPriceCalculatingMethod: PurchasePrice,
    }

    expect(onChange)->toHaveBeenCalledTimes(2)
    expect(onChange)->toHaveBeenCalledWith1(newSettings)

    expect(settingsUserPreferencesStore)->toHaveBeenCalledTimes(1)
    expect(settingsUserPreferencesStore)->toHaveBeenCalledWith1(newSettings)

    await userEvent->TestingLibraryEvent.click(triggerElement)

    let commitElement = screen->getByRoleWithOptionsExn(#button, {name: "Save settings"})

    expect(commitElement)->toBeVisible
    expect(commitElement)->toHaveAttributeValue("aria-disabled", "true")

    expect(radioMultiplier->WebAPI.DomElement.checked)->toBe(true)
    expect(radioMarginRate->WebAPI.DomElement.checked)->toBe(false)
    expect(radioMarkupRate->WebAPI.DomElement.checked)->toBe(false)

    await userEvent->TestingLibraryEvent.click(radioMarginRate)

    expect(radioMultiplier->WebAPI.DomElement.checked)->toBe(false)
    expect(radioMarginRate->WebAPI.DomElement.checked)->toBe(true)
    expect(radioMarkupRate->WebAPI.DomElement.checked)->toBe(false)

    let discardElement = screen->getByRoleWithOptionsExn(#button, {name: "Discard"})

    await userEvent->TestingLibraryEvent.click(discardElement)

    expect(onChange)->toHaveBeenCalledTimes(2)
    expect(onChange)->toHaveBeenLastCalledWith1({
      priceDifferentialIndicator: Coefficient,
      purchasePriceType: Gross,
      retailPriceCalculatingMethod: PurchasePrice,
    })

    expect(settingsUserPreferencesStore)->toHaveBeenCalledTimes(1)
    expect(settingsUserPreferencesStore)->toHaveBeenCalledWith1({
      priceDifferentialIndicator: Coefficient,
      purchasePriceType: Gross,
      retailPriceCalculatingMethod: PurchasePrice,
    })
  })
})

describe("CatalogListStockCell", () => {
  open TestingLibraryReact

  let userEvent = TestingLibraryEvent.setup()

  let render = element =>
    element->renderWithOptions(
      ~options={wrapper: props => <Providers> {props["children"]} </Providers>},
    )

  module TestableCatalogListStockCell = {
    @react.component
    let make = (
      ~formattedStockQuantity="0",
      ~variantId="mock-variant-id",
      ~shopId="mock-shop-id",
      ~deviceId="mock-device-id",
      ~bulkUnit=None,
      ~stockIndicator=#ALERT,
      ~stockOrderTriggerThreshold=0.,
      ~minStockThreshold=0.,
      ~maxStockThreshold=0.,
      ~stockActivityMutationResult=fn5((_, _, _, _, _) => Ok()),
      ~stockThresholdsMutationResult=fn2((_, _) => Ok()),
    ) =>
      <CatalogListStockCell
        variantId
        shopId
        deviceId
        bulkUnit
        formattedStockQuantity
        stockIndicator
        stockOrderTriggerThreshold
        minStockThreshold
        maxStockThreshold
        mutateCreateStockActivity={(~variantId, ~shopId, ~deviceId, ~bulkUnit, ~stockValue) =>
          Future.value(
            stockActivityMutationResult->fn(variantId, shopId, deviceId, bulkUnit, stockValue),
          )}
        mutateStockThresholds={(~variantId, ~thresholdsValue) =>
          Future.value(stockThresholdsMutationResult->fn(variantId, thresholdsValue))}
      />
  }

  itPromise("should render the cell and open its popover when clicking the interacts", async () => {
    render(<TestableCatalogListStockCell formattedStockQuantity="13" />)->ignore

    let triggerElement =
      screen->getByRoleWithOptionsExn(#button, {name: "Stock cell", expanded: false, hidden: false})

    expect(triggerElement)->toBeVisible
    expect(triggerElement)->toHaveTextContent("13")

    expect(screen->queryByRole(#dialog))->Vitest.not->toBeDefined

    await userEvent->TestingLibraryEvent.click(triggerElement)

    let dialog = screen->getByRoleExn(#dialog)

    expect(dialog)->toBeVisible

    let tablist = screen->getByRoleExn(#tablist)
    let (movementsTab, thresholdsTab) = within(tablist)->getAllByRoleExn2(#tab)

    expect(movementsTab)->toHaveTextContent("Movements")
    expect(movementsTab)->toHaveAttributeValue("aria-selected", "true")
    expect(thresholdsTab)->toHaveTextContent("Thresholds")
    expect(thresholdsTab)->toHaveAttributeValue("aria-selected", "false")

    let tabpanel = screen->getByRoleExn(#tabpanel)

    let radioGroup = within(tabpanel)->getByRoleExn(#radiogroup)
    let (deliveryPanel, lossPanel) = within(radioGroup)->getAllByRoleExn2(#radio)

    expect(deliveryPanel)->toHaveTextContent("Delivery")
    expect(deliveryPanel)->toHaveAttributeValue("aria-checked", "true")
    expect(lossPanel)->toHaveTextContent("Loss")
    expect(lossPanel)->toHaveAttributeValue("aria-checked", "false")

    let discardButton = within(dialog)->getByRoleWithOptionsExn(#button, {name: "Discard"})
    let commitButton = within(dialog)->getByRoleWithOptionsExn(#button, {name: "Save"})

    expect(discardButton)->toBeVisible
    expect(commitButton)->toBeVisible

    expect(commitButton)->toHaveAttributeValue("aria-disabled", "true")

    await userEvent->TestingLibraryEvent.click(discardButton)

    expect(dialog)->Vitest.not->toBeVisible
  })

  itPromise("should make successful stock activity updates", async () => {
    let stockActivityMutationResult = fn5((_, _, _, _, _) => Ok())

    let {rerender} = render(
      <TestableCatalogListStockCell formattedStockQuantity="13" stockActivityMutationResult />,
    )

    expect(stockActivityMutationResult)->toHaveBeenCalledTimes(0)

    let triggerElement =
      screen->getByRoleWithOptionsExn(#button, {name: "Stock cell", expanded: false, hidden: false})

    expect(triggerElement)->toBeVisible
    expect(triggerElement)->toHaveTextContent("13")

    expect(screen->queryByRole(#dialog))->Vitest.not->toBeDefined

    await userEvent->TestingLibraryEvent.click(triggerElement)

    let dialog = screen->getByRoleExn(#dialog)
    let discardButton = within(dialog)->getByRoleWithOptionsExn(#button, {name: "Discard"})
    let commitButton = within(dialog)->getByRoleWithOptionsExn(#button, {name: "Save"})

    expect(dialog)->toBeVisible
    expect(discardButton)->toBeVisible
    expect(commitButton)->toBeVisible

    let tabpanel = within(dialog)->getByRoleExn(#tabpanel)
    let radioGroup = within(tabpanel)->getByRoleExn(#radiogroup)
    let (deliveryPanelRadio, lossPanelRadio) = within(radioGroup)->getAllByRoleExn2(#radio)

    let quantityInputNumber =
      within(tabpanel)->getByRoleWithOptionsExn(#textbox, {name: "Numeric input field"})
    let commentTextarea =
      within(tabpanel)->getByRoleWithOptionsExn(#textbox, {name: "Textarea input field"})

    expect(commitButton)->toHaveAttributeValue("aria-disabled", "true")
    expect(quantityInputNumber)->toHaveDisplayValue("")

    await userEvent->TestingLibraryEvent.type_(quantityInputNumber, "7")

    expect(quantityInputNumber)->toHaveDisplayValue("7")
    expect(commitButton)->Vitest.not->toHaveAttribute("aria-disabled")

    expect(commentTextarea)->toHaveDisplayValue("")

    await userEvent->TestingLibraryEvent.type_(commentTextarea, "test comment")

    expect(commentTextarea)->toHaveDisplayValue("test comment")

    await userEvent->TestingLibraryEvent.click(lossPanelRadio)
    await userEvent->TestingLibraryEvent.click(deliveryPanelRadio)

    expect(quantityInputNumber)->toHaveDisplayValue("7")
    expect(commentTextarea)->toHaveDisplayValue("test comment")

    await userEvent->TestingLibraryEvent.click(commitButton)

    expect(stockActivityMutationResult)->toHaveBeenCalledTimes(1)
    expect(stockActivityMutationResult)->toHaveBeenLastCalledWith5(
      "mock-variant-id",
      "mock-shop-id",
      "mock-device-id",
      None,
      {
        kind: #DELIVERY,
        quantity: Some(7.),
        lossReason: #LOSS,
        comment: "test comment",
      },
    )

    expect(dialog)->Vitest.not->toBeVisible

    mockClear(stockActivityMutationResult)

    rerender(
      <TestableCatalogListStockCell
        formattedStockQuantity="5,000 kg" bulkUnit=Some("kg") stockActivityMutationResult
      />,
    )->ignore

    await userEvent->TestingLibraryEvent.click(triggerElement)

    let dialog = screen->getByRoleExn(#dialog)
    let tabpanel = within(dialog)->getByRoleExn(#tabpanel)
    let radioGroup = within(tabpanel)->getByRoleExn(#radiogroup)
    let (deliveryPanelRadio, lossPanelRadio) = within(radioGroup)->getAllByRoleExn2(#radio)
    let commitButton = within(dialog)->getByRoleWithOptionsExn(#button, {name: "Save"})

    expect(dialog)->toBeVisible
    expect(deliveryPanelRadio)->toHaveAttributeValue("aria-checked", "true")
    expect(lossPanelRadio)->toHaveAttributeValue("aria-checked", "false")

    await userEvent->TestingLibraryEvent.click(lossPanelRadio)

    expect(deliveryPanelRadio)->toHaveAttributeValue("aria-checked", "false")
    expect(lossPanelRadio)->toHaveAttributeValue("aria-checked", "true")

    let quantityInputNumber =
      within(tabpanel)->getByRoleWithOptionsExn(#textbox, {name: "Numeric input field"})

    await userEvent->TestingLibraryEvent.type_(quantityInputNumber, "-0.575")

    expect(quantityInputNumber)->toHaveDisplayValue("0.575")
    expect(commitButton)->Vitest.not->toHaveAttribute("aria-disabled")

    let lossReasonSelectTrigger =
      screen->getByRoleWithOptionsExn(#button, {name: "Loss", expanded: false})

    await userEvent->TestingLibraryEvent.click(lossReasonSelectTrigger)

    let lossReasonSelectListbox = screen->getByRoleExn(#listbox)
    let optionElements = within(lossReasonSelectListbox)->getAllByRoleExn(#option)

    expect(lossReasonSelectListbox)->toBeVisible
    expect(optionElements->Array.getExn(2))->toHaveTextContent("Damage")

    await userEvent->TestingLibraryEvent.click(optionElements->Array.getExn(2))
    await userEvent->TestingLibraryEvent.click(commitButton)

    expect(stockActivityMutationResult)->toHaveBeenCalledTimes(1)
    expect(stockActivityMutationResult)->toHaveBeenLastCalledWith5(
      "mock-variant-id",
      "mock-shop-id",
      "mock-device-id",
      Some("kg"),
      {
        kind: #LOSS,
        quantity: Some(0.575),
        lossReason: #DAMAGE,
        comment: "",
      },
    )
  })

  itPromise("should make successful stock thresholds updates", async () => {
    let stockThresholdsMutationResult = fn2((_, _) => Ok())

    let {rerender} = render(
      <TestableCatalogListStockCell bulkUnit=Some("g") stockThresholdsMutationResult />,
    )

    let triggerElement =
      screen->getByRoleWithOptionsExn(#button, {name: "Stock cell", expanded: false, hidden: false})

    await userEvent->TestingLibraryEvent.click(triggerElement)

    let dialog = screen->getByRoleExn(#dialog)
    let tablist = screen->getByRoleExn(#tablist)
    let (_movementsTab, thresholdsTab) = within(tablist)->getAllByRoleExn2(#tab)

    expect(thresholdsTab)->toHaveAttributeValue("aria-disabled", "true")

    rerender(
      <TestableCatalogListStockCell
        minStockThreshold=0.
        maxStockThreshold=0.
        stockOrderTriggerThreshold=0.
        stockThresholdsMutationResult
      />,
    )->ignore

    expect(stockThresholdsMutationResult)->toHaveBeenCalledTimes(0)
    expect(thresholdsTab)->Vitest.not->toHaveAttribute("aria-disabled")

    await userEvent->TestingLibraryEvent.click(thresholdsTab)

    let tabpanel = within(dialog)->getByRoleExn(#tabpanel)
    let commitButton = within(dialog)->getByRoleWithOptionsExn(#button, {name: "Save"})

    let minimumThresholdInputNumber =
      within(tabpanel)->getByRoleWithOptionsExn(#textbox, {name: "Minimum threshold"})
    let maximumThresholdInputNumber =
      within(tabpanel)->getByRoleWithOptionsExn(#textbox, {name: "Maximum threshold"})
    let triggeringThresholdInputNumber =
      within(tabpanel)->getByRoleWithOptionsExn(#textbox, {name: "Threshold triggering the order"})

    expect(minimumThresholdInputNumber)->toHaveDisplayValue("")
    expect(maximumThresholdInputNumber)->toHaveDisplayValue("0")
    expect(triggeringThresholdInputNumber)->toHaveDisplayValue("0")

    expect(triggeringThresholdInputNumber)->toHaveAttributeValue("aria-disabled", "true")
    expect(commitButton)->toHaveAttributeValue("aria-disabled", "true")

    await userEvent->TestingLibraryEvent.type_(minimumThresholdInputNumber, "10")
    await userEvent->TestingLibraryEvent.tab

    expect(minimumThresholdInputNumber)->toHaveDisplayValue("10")
    expect(maximumThresholdInputNumber)->toHaveDisplayValue("11")
    expect(triggeringThresholdInputNumber)->toHaveDisplayValue("10")

    expect(triggeringThresholdInputNumber)->Vitest.not->toHaveAttribute("aria-disabled")
    expect(commitButton)->Vitest.not->toHaveAttribute("aria-disabled")

    await userEvent->TestingLibraryEvent.type_(triggeringThresholdInputNumber, "11")
    await userEvent->TestingLibraryEvent.tab

    expect(triggeringThresholdInputNumber)->toHaveDisplayValue("10")

    await userEvent->TestingLibraryEvent.type_(triggeringThresholdInputNumber, "9")
    await userEvent->TestingLibraryEvent.tab

    expect(triggeringThresholdInputNumber)->toHaveDisplayValue("10")

    await userEvent->TestingLibraryEvent.type_(
      maximumThresholdInputNumber,
      "{backspace}{backspace}15",
    )
    await userEvent->TestingLibraryEvent.type_(
      triggeringThresholdInputNumber,
      "{backspace}{backspace}11",
    )
    await userEvent->TestingLibraryEvent.type_(
      minimumThresholdInputNumber,
      "{backspace}{backspace}5",
    )

    expect(minimumThresholdInputNumber)->toHaveDisplayValue("5")
    expect(maximumThresholdInputNumber)->toHaveDisplayValue("15")
    expect(triggeringThresholdInputNumber)->toHaveDisplayValue("11")

    await userEvent->TestingLibraryEvent.click(commitButton)

    expect(stockThresholdsMutationResult)->toHaveBeenCalledTimes(1)
    expect(stockThresholdsMutationResult)->toHaveBeenCalledWith2(
      "mock-variant-id",
      {
        CatalogListStockCell.minStock: 5.,
        maxStock: 15.,
        stockOrderTrigger: 11.,
      },
    )

    expect(dialog)->Vitest.not->toBeVisible
  })
})

describe("CatalogListInventoryExportShortIconButton", () => {
  let {encodeRequestBodyJson} = module(CatalogListInventoryExportShortIconButton)

  test("encodeRequestBodyJson", () => {
    expect(encodeRequestBodyJson(~shopIds=["shop-a-id", "shop-b-id"], ()))->toUnsafeStrictEqual({
      "filterBy": {
        "shopIds": %raw(`{"_in": ["shop-a-id", "shop-b-id"]}`),
        "archived": "INCLUDED",
      },
      "timeZone": "UTC",
    })

    expect(
      encodeRequestBodyJson(~shopIds=["shop-a-id", "shop-b-id"], ~variantsActive=true, ()),
    )->toUnsafeStrictEqual({
      "filterBy": {
        "shopIds": %raw(`{"_in": ["shop-a-id", "shop-b-id"]}`),
        "active": {"_equals": true},
        "archived": "INCLUDED",
      },
      "timeZone": "UTC",
    })

    expect(
      encodeRequestBodyJson(~shopIds=["shop-a-id", "shop-b-id"], ~variantsActive=false, ()),
    )->toUnsafeStrictEqual({
      "filterBy": {
        "shopIds": %raw(`{"_in": ["shop-a-id", "shop-b-id"]}`),
        "active": {"_equals": false},
        "archived": "INCLUDED",
      },
      "timeZone": "UTC",
    })

    expect(
      encodeRequestBodyJson(~shopIds=["shop-a-id", "shop-b-id"], ~variantsArchived=true, ()),
    )->toUnsafeStrictEqual({
      "filterBy": {
        "shopIds": %raw(`{"_in": ["shop-a-id", "shop-b-id"]}`),
        "archived": "ONLY",
      },
      "timeZone": "UTC",
    })

    expect(
      encodeRequestBodyJson(~shopIds=["shop-a-id", "shop-b-id"], ~variantsArchived=false, ()),
    )->toUnsafeStrictEqual({
      "filterBy": {
        "shopIds": %raw(`{"_in": ["shop-a-id", "shop-b-id"]}`),
        "archived": "EXCLUDED",
      },
      "timeZone": "UTC",
    })

    expect(
      encodeRequestBodyJson(
        ~shopIds=["shop-a-id", "shop-b-id"],
        ~variantsArchived=false,
        ~variantsActive=true,
        (),
      ),
    )->toUnsafeStrictEqual({
      "filterBy": {
        "shopIds": %raw(`{"_in": ["shop-a-id", "shop-b-id"]}`),
        "archived": "EXCLUDED",
        "active": {"_equals": true},
      },
      "timeZone": "UTC",
    })

    expect(
      encodeRequestBodyJson(
        ~shopIds=["shop-a-id", "shop-b-id"],
        ~categoryId=Js.Nullable.return("mock-category-id"),
        ~supplierId=Js.Nullable.return("mock-supplier-id"),
        ~producer="mock-producer-name",
        ~stock={min: 0., max: 3.5},
        (),
      ),
    )->toUnsafeStrictEqual({
      "filterBy": {
        "shopIds": %raw(`{"_in": ["shop-a-id", "shop-b-id"]}`),
        "archived": "INCLUDED",
        "categoryId": {"_equals": Js.Nullable.return("mock-category-id")},
        "supplierId": {"_equals": Js.Nullable.return("mock-supplier-id")},
        "producer": {"_equals": "mock-producer-name"},
        "stock": {"_min": 0, "_max": 3.5},
      },
      "timeZone": "UTC",
    })

    expect(
      encodeRequestBodyJson(
        ~shopIds=["shop-a-id", "shop-b-id"],
        ~categoryId=Js.Nullable.null,
        ~supplierId=Js.Nullable.null,
        (),
      ),
    )->toUnsafeStrictEqual({
      "filterBy": {
        "shopIds": %raw(`{"_in": ["shop-a-id", "shop-b-id"]}`),
        "archived": "INCLUDED",
        "categoryId": {"_equals": Js.Nullable.null},
        "supplierId": {"_equals": Js.Nullable.null},
      },
      "timeZone": "UTC",
    })

    expect(
      encodeRequestBodyJson(~shopIds=["shop-a-id", "shop-b-id"], ~stock={min: -2.}, ()),
    )->toUnsafeStrictEqual({
      "filterBy": {
        "shopIds": %raw(`{"_in": ["shop-a-id", "shop-b-id"]}`),
        "archived": "INCLUDED",
        "stock": {"_min": -2., "_max": Js.Nullable.null},
      },
      "timeZone": "UTC",
    })

    expect(
      encodeRequestBodyJson(~shopIds=["shop-a-id", "shop-b-id"], ~stock={max: 2.}, ()),
    )->toUnsafeStrictEqual({
      "filterBy": {
        "shopIds": %raw(`{"_in": ["shop-a-id", "shop-b-id"]}`),
        "archived": "INCLUDED",
        "stock": {"_min": Js.Nullable.null, "_max": 2.},
      },
      "timeZone": "UTC",
    })

    expect(encodeRequestBodyJson(~shopIds=["shop-a-id"], ()))->toUnsafeStrictEqual({
      "filterBy": {
        "shopIds": %raw(`{"_in": ["shop-a-id"]}`),
        "archived": "INCLUDED",
      },
      "timeZone": "UTC",
    })

    expect(encodeRequestBodyJson(~shopIds=[], ()))->toUnsafeStrictEqual({
      "filterBy": {
        "shopIds": %raw(`{"_in": []}`),
        "archived": "INCLUDED",
      },
      "timeZone": "UTC",
    })
  })
})

describe("CatalogCentralizeRequestMenuItem", () => {
  let {encodeRequestBodyJson, decodeRequestResponseJson} = module(
    CatalogListCentralizeRequestMenuItem
  )

  test("encodeRequestBodyJson", () => {
    expect(encodeRequestBodyJson())->toUnsafeStrictEqual(Json.fromObjExn(Js.Obj.empty()))
  })

  test("decodeRequestResponseJson", () => {
    expect(
      decodeRequestResponseJson(
        Json.fromObjExn({"issuesCount": 0, "patchesCount": 0, "meta": "whatever"}),
      ),
    )->toStrictEqual(Ok({issuesCount: 0, patchesCount: 0, fileUrl: None}))

    expect(
      decodeRequestResponseJson(
        Json.fromObjExn({
          "issuesCount": 2,
          "patchesCount": 0,
          "file": {"url": "https://wino.test.fr"},
        }),
      ),
    )->toStrictEqual(
      Ok({issuesCount: 2, patchesCount: 0, fileUrl: Some("https://wino.test.fr"->Url.make)}),
    )

    expect(
      decodeRequestResponseJson(Json.fromObjExn({"issuesCount": "0", "patchesCount": 2})),
    )->toStrictEqual(Error(Request.MalformedResponse))

    expect(decodeRequestResponseJson(Json.fromObjExn({"issuesCount": 2})))->toStrictEqual(
      Error(Request.MalformedResponse),
    )
  })
})

let {mockShop} = module(Auth__Mock)

let mockTypename = () => ""

let mockMultiShopsQueryVariablesFilterBy = () =>
  CatalogListMultiShopsQuery.makeInputObjectInputVariantsDistinctOnCkuQueryFilter()

let mockSingleShopQueryVariablesFilterBy = () =>
  CatalogListSingleShopQuery.makeInputObjectInputVariantsQueryFilter()

let mockMultiShopsQueryDataPageInfo = (~startCursor=?, ~endCursor=?, ()) => {
  CatalogListMultiShopsQuery.startCursor,
  endCursor,
  __typename: mockTypename(),
}

let mockSingleShopQueryDataPageInfo = (~startCursor=?, ~endCursor=?, ()) => {
  CatalogListSingleShopQuery.startCursor,
  endCursor,
  __typename: mockTypename(),
}

let mockMultiShopsQueryData = (
  ~edges=[],
  ~pageInfo=mockMultiShopsQueryDataPageInfo(),
  ~totalCount=0,
  (),
) => {
  CatalogListMultiShopsQuery.variantsDistinctOnCku: {
    edges,
    pageInfo,
    totalCount,
    __typename: mockTypename(),
  },
}

let mockSingleShopQueryData = (
  ~edges=[],
  ~pageInfo=mockSingleShopQueryDataPageInfo(),
  ~totalCount=0,
  (),
) => {
  CatalogListSingleShopQuery.variants: {
    edges,
    pageInfo,
    totalCount,
    __typename: mockTypename(),
  },
}

let mockMultiShopsQueryDataEdge = (
  ~cku="",
  ~id="",
  ~name="",
  ~createdAt=Js.Date.fromFloat(-1.),
  ~supplierCompanyName="",
  ~productId="",
  ~productName="",
  ~productKind=#SIMPLE,
  ~productColor=None,
  ~productProducer=None,
  ~productDesignation=None,
  ~productWineType=None,
  ~productWhiteWineType=None,
  ~productFamily=None,
  ~productBeerType=None,
  ~productRegion=None,
  ~productCountry=None,
  ~productTaxValue=20.,
  ~alcoholVolume=None,
  ~formattedCategory=None,
  ~purchasedPrice=None,
  ~stockKeepingUnit=None,
  ~priceLookUpCode=None,
  ~internalCode=None,
  ~bulk=None,
  ~capacityUnit=None,
  ~maxStockThreshold=None,
  ~minStockThreshold=None,
  ~stockOrderTriggerThreshold=None,
  ~stockFormattedQuantity=None,
  ~stockState=None,
  ~stockFormattedShopsNames=None,
  ~formattedStatus=#ACTIVE,
  (),
) => {
  CatalogListMultiShopsQuery.node: {
    cku,
    id,
    createdAt,
    name,
    product: {
      id: productId,
      name: productName,
      kind: productKind,
      producer: productProducer,
      designation: productDesignation,
      family: productFamily,
      beerType: productBeerType,
      color: productColor,
      wineType: productWineType,
      whiteWineType: productWhiteWineType,
      region: productRegion,
      country: productCountry,
      tax: {
        value: productTaxValue,
        __typename: mockTypename(),
      },
      __typename: mockTypename(),
    },
    supplier: Some({
      companyName: supplierCompanyName,
      __typename: mockTypename(),
    }),
    alcoholVolume,
    formattedCategory,
    purchasedPrice,
    stockKeepingUnit,
    priceLookUpCode,
    internalCode,
    bulk,
    capacityUnit,
    maxStockThreshold,
    minStockThreshold,
    stockOrderTriggerThreshold,
    formattedStatus,
    stock: {
      formattedQuantity: stockFormattedQuantity,
      state: stockState,
      formattedShopsNames: stockFormattedShopsNames,
      __typename: mockTypename(),
    },
    __typename: mockTypename(),
  },
  __typename: mockTypename(),
}

open CatalogListSingleShopQuery

type variantPricesEdge = CatalogListSingleShopQuery.t_variants_edges_node_variantPrices_edges
let mockSingleShopQueryVariantPricesDataEdge = (
  ~priceId="",
  ~valueIncludingTax=0.,
  ~valueExcludingTax=0.,
  (),
): variantPricesEdge => {
  node: {
    id: "",
    valueIncludingTax,
    valueExcludingTax,
    price: Some({
      id: priceId,
      __typename: mockTypename(),
    }),
    __typename: mockTypename(),
  },
  __typename: mockTypename(),
}

let mockSingleShopQueryVariantPricesData: (
  ~edges: array<variantPricesEdge>=?,
  unit,
) => CatalogListSingleShopQuery.t_variants_edges_node_variantPrices = (~edges=[], ()) => {
  edges,
  __typename: mockTypename(),
}

type orderProductsEdge = CatalogListSingleShopQuery.t_variants_edges_node_orderProducts_edges
let mockSingleShopQueryOrderProductsDataEdge = (
  ~quantity=0,
  ~totalLocalDiscounts=0.,
  ~fees=Json.fromObjExn([]),
  (),
): orderProductsEdge => {
  node: {
    fees,
    quantity,
    totalLocalDiscounts,
    __typename: mockTypename(),
  },
  __typename: mockTypename(),
}

let mockSingleShopQueryOrderProductsData = (~edges=[], ()) => {
  edges,
  __typename: mockTypename(),
}

let mockSingleShopQueryDataEdge = (
  ~cku="",
  ~id="",
  ~createdAt=Js.Date.fromFloat(-1.),
  ~name="",
  ~supplierCompanyName="",
  ~productId="",
  ~productName="",
  ~productKind=#SIMPLE,
  ~productColor=None,
  ~productProducer=None,
  ~productDesignation=None,
  ~productWineType=None,
  ~productWhiteWineType=None,
  ~productFamily=None,
  ~productBeerType=None,
  ~productRegion=None,
  ~productCountry=None,
  ~productTaxValue=20.,
  ~alcoholVolume=None,
  ~formattedCategory=None,
  ~purchasedPrice=None,
  ~stockKeepingUnit=None,
  ~priceLookUpCode=None,
  ~internalCode=None,
  ~bulk=None,
  ~capacityUnit=None,
  ~maxStockThreshold=None,
  ~minStockThreshold=None,
  ~stockOrderTriggerThreshold=None,
  ~stockFormattedQuantity=None,
  ~stockState=None,
  ~stockFormattedShopsNames=None,
  ~formattedStatus=#ACTIVE,
  ~variantPrices=mockSingleShopQueryVariantPricesData(),
  ~orderProducts=mockSingleShopQueryOrderProductsData(),
  (),
) => {
  CatalogListSingleShopQuery.node: {
    cku,
    id,
    createdAt,
    name,
    product: {
      id: productId,
      name: productName,
      kind: productKind,
      producer: productProducer,
      designation: productDesignation,
      family: productFamily,
      beerType: productBeerType,
      color: productColor,
      wineType: productWineType,
      whiteWineType: productWhiteWineType,
      region: productRegion,
      country: productCountry,
      tax: {
        __typename: mockTypename(),
        value: productTaxValue,
      },
      __typename: mockTypename(),
    },
    variantPrices,
    orderProducts,
    supplier: Some({
      companyName: supplierCompanyName,
      __typename: mockTypename(),
    }),
    alcoholVolume,
    formattedCategory,
    purchasedPrice,
    stockKeepingUnit,
    priceLookUpCode,
    internalCode,
    bulk,
    capacityUnit,
    maxStockThreshold,
    minStockThreshold,
    stockOrderTriggerThreshold,
    formattedStatus,
    stock: {
      formattedQuantity: stockFormattedQuantity,
      state: stockState,
      formattedShopsNames: stockFormattedShopsNames,
      __typename: mockTypename(),
    },
    __typename: mockTypename(),
  },
  __typename: mockTypename(),
}

let mockRow = (~id="", ()) => {
  CatalogListTableRow.cku: "",
  id,
  createdAt: Js.Date.fromString("1969-12-31T23:59:59.999Z"),
  productId: "",
  color: None,
  producer: None,
  name: "",
  categoryName: "",
  productName: "",
  productTaxRate: 20.,
  country: None,
  region: None,
  productKind: #SIMPLE,
  designation: None,
  supplierName: None,
  family: None,
  alcoholVolume: None,
  feesList: {transportAmount: 0., taxesAmount: 0., otherAmount: 0.},
  purchasePriceDiscount: 0.,
  purchasePrice: 0.,
  retailPrice: None,
  stockKeepingUnit: None,
  priceLookUpCode: None,
  internalCode: None,
  stockQuantity: None,
  stockState: #ALERT,
  formattedShopsNames: None,
  status: None,
  bulkUnit: None,
  maxStockThreshold: 0.,
  minStockThreshold: 0.,
  stockOrderTriggerThreshold: 0.,
  bulk: false,
}

let mockStateFilters = (
  ~shop=?,
  ~status=?,
  ~category=?,
  ~supplier=?,
  ~producer=?,
  ~stock=?,
  (),
) => {
  CatalogListFilters.shop,
  status,
  category,
  supplier,
  producer,
  stock,
}

let mockStateSorting = (~sorting=CatalogListSorting.Name(#ascending), ()) => sorting

describe("CatalogListFilters", () => {
  let {getStatuses, getUpdatedStatus} = module(CatalogListFilters)

  test("getStatuses", () => {
    expect(getStatuses(~shopsIndependentKind=true))->toStrictEqual([Unarchived, Archived])
    expect(getStatuses(~shopsIndependentKind=false))->toStrictEqual([Active, Inactive, Archived])
  })

  test("getUpdatedStatus", () => {
    let statuses = getStatuses(~shopsIndependentKind=true)
    let currentStatus = Some(CatalogProduct.Status.Archived)
    expect(getUpdatedStatus(~statuses, ~currentStatus))->toBe(Some(Archived))

    let statuses = getStatuses(~shopsIndependentKind=false)
    let currentStatus = Some(CatalogProduct.Status.Archived)
    expect(getUpdatedStatus(~statuses, ~currentStatus))->toBe(Some(Archived))

    let statuses = getStatuses(~shopsIndependentKind=false)
    let currentStatus = Some(CatalogProduct.Status.Active)
    expect(getUpdatedStatus(~statuses, ~currentStatus))->toBe(Some(Active))

    let statuses = getStatuses(~shopsIndependentKind=true)
    let currentStatus = Some(CatalogProduct.Status.Active)
    expect(getUpdatedStatus(~statuses, ~currentStatus))->toBe(Some(Unarchived))

    let statuses = getStatuses(~shopsIndependentKind=true)
    let currentStatus = Some(CatalogProduct.Status.Inactive)
    expect(getUpdatedStatus(~statuses, ~currentStatus))->toBe(Some(Unarchived))

    let statuses = getStatuses(~shopsIndependentKind=false)
    let currentStatus = Some(CatalogProduct.Status.Inactive)
    expect(getUpdatedStatus(~statuses, ~currentStatus))->toBe(Some(Inactive))

    let statuses = getStatuses(~shopsIndependentKind=false)
    let currentStatus = Some(CatalogProduct.Status.Unarchived)
    expect(getUpdatedStatus(~statuses, ~currentStatus))->toBe(Some(Active))

    let statuses = getStatuses(~shopsIndependentKind=true)
    let currentStatus = Some(CatalogProduct.Status.Unarchived)
    expect(getUpdatedStatus(~statuses, ~currentStatus))->toBe(Some(Unarchived))

    let statuses = getStatuses(~shopsIndependentKind=false)
    let currentStatus = None
    expect(getUpdatedStatus(~statuses, ~currentStatus))->toBe(None)

    let statuses = getStatuses(~shopsIndependentKind=true)
    let currentStatus = None
    expect(getUpdatedStatus(~statuses, ~currentStatus))->toBe(None)
  })
})

describe("CatalogListSorting", () => {
  let {toSortDescriptor, fromSortDescriptor} = module(CatalogListSorting)

  testEach2([
    (
      CatalogListSorting.Name(#ascending),
      {ReactStately.Table.column: "reference", direction: #ascending},
    ),
    (Name(#descending), {column: "reference", direction: #descending}),
    (Producer(#ascending), {column: "producer", direction: #ascending}),
    (Producer(#descending), {column: "producer", direction: #descending}),
    (PurchasePrice(#ascending), {column: "purchase-price", direction: #ascending}),
    (PurchasePrice(#descending), {column: "purchase-price", direction: #descending}),
    (CreatedAt(#ascending), {column: "created-at", direction: #ascending}),
    (CreatedAt(#descending), {column: "created-at", direction: #descending}),
  ])(."toSortDescriptor", (input, output) => expect(toSortDescriptor(input))->toStrictEqual(output))

  testEach2([
    (
      {ReactStately.Table.column: "reference", direction: #ascending},
      Ok(CatalogListSorting.Name(#ascending)),
    ),
    ({column: "reference", direction: #descending}, Ok(Name(#descending))),
    ({column: "producer", direction: #ascending}, Ok(Producer(#ascending))),
    ({column: "producer", direction: #descending}, Ok(Producer(#descending))),
    ({column: "purchase-price", direction: #ascending}, Ok(PurchasePrice(#ascending))),
    ({column: "purchase-price", direction: #descending}, Ok(PurchasePrice(#descending))),
    ({column: "created-at", direction: #ascending}, Ok(CreatedAt(#ascending))),
    ({column: "created-at", direction: #descending}, Ok(CreatedAt(#descending))),
    ({column: "", direction: #ascending}, Error()),
    ({column: "unknown", direction: #descending}, Error()),
  ])(."fromSortDescriptor", (input, output) =>
    expect(fromSortDescriptor(input))->toStrictEqual(output)
  )
})

test("CatalogListTableRow.keyExtractor", () => {
  let {keyExtractor} = module(CatalogListTableRow)

  let row = mockRow()
  expect(keyExtractor(row))->toBe("")

  let row = mockRow(~id="mock-id", ())
  expect(keyExtractor(row))->toBe("mock-id")
})

test("catalogListMultiShopsQueryVariables", () => {
  let {catalogListMultiShopsQueryVariables} = module(CatalogListPage)

  expect(
    catalogListMultiShopsQueryVariables({
      filters: mockStateFilters(),
      sorting: mockStateSorting(),
      connectionArguments: {},
      currentPage: 0,
      previousPage: 0,
    }),
  )->toStrictEqual({
    CatalogListMultiShopsQuery.first: None,
    last: None,
    before: None,
    after: None,
    filterBy: Some(
      CatalogListMultiShopsQuery.makeInputObjectInputVariantsDistinctOnCkuQueryFilter(
        ~archived=#INCLUDED,
        (),
      ),
    ),
    orderBy: Some([
      {
        CatalogListMultiShopsQuery.name: Some(#ASC),
        producer: None,
        purchasedPrice: None,
        createdAt: None,
        active: None,
      },
    ]),
    search: None,
  })

  expect(
    catalogListMultiShopsQueryVariables({
      filters: mockStateFilters(),
      sorting: mockStateSorting(),
      connectionArguments: {
        first: 1,
        last: 2,
        before: "before",
        after: "after",
      },
      searchQuery: "search",
      currentPage: 0,
      previousPage: 0,
    }),
  )->toStrictEqual({
    CatalogListMultiShopsQuery.first: Some(1),
    last: Some(2),
    before: Some("before"),
    after: Some("after"),
    search: Some("search"),
    filterBy: Some(
      CatalogListMultiShopsQuery.makeInputObjectInputVariantsDistinctOnCkuQueryFilter(
        ~archived=#INCLUDED,
        (),
      ),
    ),
    orderBy: Some([
      {
        CatalogListMultiShopsQuery.name: Some(#ASC),
        producer: None,
        purchasedPrice: None,
        createdAt: None,
        active: None,
      },
    ]),
  })
})

test("catalogListSingleShopQueryVariables", () => {
  let {catalogListSingleShopQueryVariables} = module(CatalogListPage)

  expect(
    catalogListSingleShopQueryVariables({
      filters: mockStateFilters(),
      sorting: mockStateSorting(),
      connectionArguments: {},
      currentPage: 0,
      previousPage: 0,
    }),
  )->toStrictEqual({
    CatalogListSingleShopQuery.first: None,
    last: None,
    before: None,
    after: None,
    filterBy: Some(
      CatalogListSingleShopQuery.makeInputObjectInputVariantsQueryFilter(~archived=#INCLUDED, ()),
    ),
    orderBy: Some([
      {
        CatalogListSingleShopQuery.name: Some(#ASC),
        producer: None,
        purchasedPrice: None,
        createdAt: None,
        active: None,
      },
    ]),
    search: None,
  })

  expect(
    catalogListSingleShopQueryVariables({
      filters: mockStateFilters(),
      sorting: mockStateSorting(),
      connectionArguments: {
        first: 1,
        last: 2,
        before: "before",
        after: "after",
      },
      searchQuery: "search",
      currentPage: 0,
      previousPage: 0,
    }),
  )->toStrictEqual({
    CatalogListSingleShopQuery.first: Some(1),
    last: Some(2),
    before: Some("before"),
    after: Some("after"),
    search: Some("search"),
    filterBy: Some(
      CatalogListSingleShopQuery.makeInputObjectInputVariantsQueryFilter(~archived=#INCLUDED, ()),
    ),
    orderBy: Some([
      {
        CatalogListSingleShopQuery.name: Some(#ASC),
        producer: None,
        purchasedPrice: None,
        createdAt: None,
        active: None,
      },
    ]),
  })
})

test("catalogListMultiShopsQueryVariablesFilterBy", () => {
  let {catalogListMultiShopsQueryVariablesFilterBy} = module(CatalogListPage)

  let filters = {
    CatalogListFilters.shop: None,
    status: None,
    category: None,
    supplier: None,
    producer: None,
    stock: None,
  }
  expect(catalogListMultiShopsQueryVariablesFilterBy(filters))->toStrictEqual({
    CatalogListMultiShopsQuery.shopIds: None,
    active: None,
    archived: Some(#INCLUDED),
  })

  let filters = {
    CatalogListFilters.shop: Some(mockShop(~id="mock-shop-id", ())),
    status: None,
    category: None,
    supplier: None,
    producer: None,
    stock: None,
  }
  expect(catalogListMultiShopsQueryVariablesFilterBy(filters))->toStrictEqual({
    CatalogListMultiShopsQuery.shopIds: Some({_in: ["mock-shop-id"]}),
    active: None,
    archived: Some(#INCLUDED),
  })

  let filters = {
    CatalogListFilters.shop: None,
    status: Some(Unarchived),
    category: None,
    supplier: None,
    producer: None,
    stock: None,
  }
  expect(catalogListMultiShopsQueryVariablesFilterBy(filters))->toStrictEqual({
    CatalogListMultiShopsQuery.shopIds: None,
    active: None,
    archived: Some(#EXCLUDED),
  })

  let filters = {
    CatalogListFilters.shop: Some(mockShop(~id="mock-shop-id", ())),
    status: Some(Active),
    category: None,
    supplier: None,
    producer: None,
    stock: None,
  }
  expect(catalogListMultiShopsQueryVariablesFilterBy(filters))->toStrictEqual({
    CatalogListMultiShopsQuery.shopIds: Some({_in: ["mock-shop-id"]}),
    active: Some({_equals: true}),
    archived: Some(#EXCLUDED),
  })

  let filters = {
    CatalogListFilters.shop: Some(mockShop(~id="mock-shop-id", ())),
    status: Some(Inactive),
    category: None,
    supplier: None,
    producer: None,
    stock: None,
  }
  expect(catalogListMultiShopsQueryVariablesFilterBy(filters))->toStrictEqual({
    CatalogListMultiShopsQuery.shopIds: Some({_in: ["mock-shop-id"]}),
    active: Some({_equals: false}),
    archived: Some(#EXCLUDED),
  })

  let filters = {
    CatalogListFilters.shop: Some(mockShop(~id="mock-shop-id", ())),
    status: Some(Archived),
    category: None,
    supplier: None,
    producer: None,
    stock: None,
  }
  expect(catalogListMultiShopsQueryVariablesFilterBy(filters))->toStrictEqual({
    CatalogListMultiShopsQuery.shopIds: Some({_in: ["mock-shop-id"]}),
    active: None,
    archived: Some(#ONLY),
  })
})

test("catalogListSingleShopQueryVariablesFilterBy", () => {
  let {catalogListSingleShopQueryVariablesFilterBy} = module(CatalogListPage)

  let filters = {
    CatalogListFilters.shop: None,
    status: None,
    category: None,
    supplier: None,
    producer: None,
    stock: None,
  }
  expect(catalogListSingleShopQueryVariablesFilterBy(filters))->toStrictEqual({
    CatalogListSingleShopQuery.shopIds: None,
    active: None,
    archived: Some(#INCLUDED),
    categoryId: None,
    supplierId: None,
    producer: None,
    stock: None,
    stockKeepingUnit: None,
    ean13: None,
    createdAt: None,
    updatedAt: None,
  })

  let filters = {
    CatalogListFilters.shop: Some(mockShop(~id="mock-shop-id", ())),
    status: None,
    category: None,
    supplier: None,
    producer: None,
    stock: None,
  }
  expect(catalogListSingleShopQueryVariablesFilterBy(filters))->toStrictEqual({
    CatalogListSingleShopQuery.shopIds: Some({_in: ["mock-shop-id"]}),
    active: None,
    archived: Some(#INCLUDED),
    categoryId: None,
    supplierId: None,
    producer: None,
    stock: None,
    stockKeepingUnit: None,
    ean13: None,
    createdAt: None,
    updatedAt: None,
  })

  let filters = {
    CatalogListFilters.shop: None,
    status: Some(Unarchived),
    category: Some({id: Js.Nullable.return("mock-category-id"), name: ""}),
    supplier: Some({id: Js.Nullable.return("mock-supplier-id"), name: ""}),
    producer: Some("mock-producer-name"),
    stock: Some({min: 0., max: 3.5}),
  }
  expect(catalogListSingleShopQueryVariablesFilterBy(filters))->toStrictEqual({
    CatalogListSingleShopQuery.shopIds: None,
    active: None,
    archived: Some(#EXCLUDED),
    categoryId: Some({_equals: Some("mock-category-id")}),
    supplierId: Some({_equals: Some("mock-supplier-id")}),
    producer: Some({_equals: "mock-producer-name"}),
    stock: Some({
      _min: Some(0.),
      _max: Some(3.5),
      _between: None,
    }),
    stockKeepingUnit: None,
    ean13: None,
    createdAt: None,
    updatedAt: None,
  })

  let filters = {
    CatalogListFilters.shop: Some(mockShop(~id="mock-shop-id", ())),
    status: Some(Active),
    category: Some({id: Js.Nullable.null, name: ""}),
    supplier: Some({id: Js.Nullable.null, name: ""}),
    producer: None,
    stock: Some({max: -2.}),
  }
  expect(catalogListSingleShopQueryVariablesFilterBy(filters))->toStrictEqual({
    CatalogListSingleShopQuery.shopIds: Some({_in: ["mock-shop-id"]}),
    active: Some({_equals: true}),
    archived: Some(#EXCLUDED),
    categoryId: Some({_equals: Obj.magic(Js.Nullable.null)}),
    supplierId: Some({_equals: Obj.magic(Js.Nullable.null)}),
    producer: None,
    stock: Some({
      _min: None,
      _max: Some(-2.),
      _between: None,
    }),
    stockKeepingUnit: None,
    ean13: None,
    createdAt: None,
    updatedAt: None,
  })

  let filters = {
    CatalogListFilters.shop: Some(mockShop(~id="mock-shop-id", ())),
    status: Some(Inactive),
    category: None,
    supplier: None,
    producer: None,
    stock: Some({min: 99.}),
  }
  expect(catalogListSingleShopQueryVariablesFilterBy(filters))->toStrictEqual({
    CatalogListSingleShopQuery.shopIds: Some({_in: ["mock-shop-id"]}),
    active: Some({_equals: false}),
    archived: Some(#EXCLUDED),
    categoryId: None,
    supplierId: None,
    producer: None,
    stock: Some({
      _min: Some(99.),
      _max: None,
      _between: None,
    }),
    stockKeepingUnit: None,
    ean13: None,
    createdAt: None,
    updatedAt: None,
  })

  let filters = {
    CatalogListFilters.shop: Some(mockShop(~id="mock-shop-id", ())),
    status: Some(Archived),
    category: None,
    supplier: None,
    producer: None,
    stock: None,
  }
  expect(catalogListSingleShopQueryVariablesFilterBy(filters))->toStrictEqual({
    CatalogListSingleShopQuery.shopIds: Some({_in: ["mock-shop-id"]}),
    active: None,
    archived: Some(#ONLY),
    categoryId: None,
    supplierId: None,
    producer: None,
    stock: None,
    stockKeepingUnit: None,
    ean13: None,
    createdAt: None,
    updatedAt: None,
  })
})

test("catalogListMultiShopsQueryVariablesOrderBy", () => {
  let {catalogListMultiShopsQueryVariablesOrderBy} = module(CatalogListPage)

  let sort = CatalogListSorting.Name(#ascending)
  expect(catalogListMultiShopsQueryVariablesOrderBy(sort))->toStrictEqual({
    CatalogListMultiShopsQuery.name: Some(#ASC),
    producer: None,
    purchasedPrice: None,
    createdAt: None,
    active: None,
  })

  let sort = CatalogListSorting.Name(#descending)
  expect(catalogListMultiShopsQueryVariablesOrderBy(sort))->toStrictEqual({
    CatalogListMultiShopsQuery.name: Some(#DESC),
    producer: None,
    purchasedPrice: None,
    createdAt: None,
    active: None,
  })

  let sort = CatalogListSorting.Producer(#ascending)
  expect(catalogListMultiShopsQueryVariablesOrderBy(sort))->toStrictEqual({
    CatalogListMultiShopsQuery.name: None,
    producer: Some(#ASC),
    purchasedPrice: None,
    createdAt: None,
    active: None,
  })

  let sort = CatalogListSorting.PurchasePrice(#ascending)
  expect(catalogListMultiShopsQueryVariablesOrderBy(sort))->toStrictEqual({
    CatalogListMultiShopsQuery.name: None,
    producer: None,
    purchasedPrice: Some(#ASC),
    createdAt: None,
    active: None,
  })

  let sort = CatalogListSorting.CreatedAt(#ascending)
  expect(catalogListMultiShopsQueryVariablesOrderBy(sort))->toStrictEqual({
    CatalogListMultiShopsQuery.name: None,
    producer: None,
    purchasedPrice: None,
    createdAt: Some(#ASC),
    active: None,
  })
})

test("catalogListSingleShopQueryVariablesOrderBy", () => {
  let {catalogListSingleShopQueryVariablesOrderBy} = module(CatalogListPage)

  let sort = CatalogListSorting.Name(#descending)
  expect(catalogListSingleShopQueryVariablesOrderBy(sort))->toStrictEqual({
    CatalogListSingleShopQuery.name: Some(#DESC),
    producer: None,
    purchasedPrice: None,
    createdAt: None,
    active: None,
  })

  let sort = CatalogListSorting.Name(#ascending)
  expect(catalogListSingleShopQueryVariablesOrderBy(sort))->toStrictEqual({
    CatalogListSingleShopQuery.name: Some(#ASC),
    producer: None,
    purchasedPrice: None,
    createdAt: None,
    active: None,
  })

  let sort = CatalogListSorting.Producer(#descending)
  expect(catalogListSingleShopQueryVariablesOrderBy(sort))->toStrictEqual({
    CatalogListSingleShopQuery.name: None,
    producer: Some(#DESC),
    purchasedPrice: None,
    createdAt: None,
    active: None,
  })

  let sort = CatalogListSorting.PurchasePrice(#descending)
  expect(catalogListSingleShopQueryVariablesOrderBy(sort))->toStrictEqual({
    CatalogListSingleShopQuery.name: None,
    producer: None,
    purchasedPrice: Some(#DESC),
    createdAt: None,
    active: None,
  })

  let sort = CatalogListSorting.CreatedAt(#descending)
  expect(catalogListSingleShopQueryVariablesOrderBy(sort))->toStrictEqual({
    CatalogListSingleShopQuery.name: None,
    producer: None,
    purchasedPrice: None,
    createdAt: Some(#DESC),
    active: None,
  })
})

test("catalogListMultiShopsTableRowsFromQueryResult", () => {
  let {catalogListMultiShopsTableRowsFromQueryResult} = module(CatalogListPage)

  let queryData = mockMultiShopsQueryData()
  expect(catalogListMultiShopsTableRowsFromQueryResult(queryData))->toStrictEqual([])

  let queryData = mockMultiShopsQueryData(~edges=[mockMultiShopsQueryDataEdge()], ())
  expect(catalogListMultiShopsTableRowsFromQueryResult(queryData))->toEqual([
    {
      cku: "",
      id: "",
      createdAt: Js.Date.fromString("1969-12-31T23:59:59.999Z"),
      productId: "",
      color: None,
      producer: None,
      name: "",
      productName: "",
      productTaxRate: 20.,
      productKind: #SIMPLE,
      designation: None,
      wineType: ?None,
      whiteWineType: ?None,
      beerType: ?None,
      family: None,
      categoryName: "—",
      supplierName: Some(""),
      region: None,
      country: None,
      alcoholVolume: None,
      feesList: {transportAmount: 0., taxesAmount: 0., otherAmount: 0.},
      purchasePriceDiscount: 0.,
      purchasePrice: 0.,
      retailPrice: None,
      stockKeepingUnit: None,
      priceLookUpCode: None,
      internalCode: None,
      stockQuantity: None,
      stockState: #ALERT,
      formattedShopsNames: None,
      status: Some(Active),
      maxStockThreshold: 0.,
      minStockThreshold: 0.,
      stockOrderTriggerThreshold: 0.,
      bulk: false,
      bulkUnit: None,
    },
  ])

  let queryData = mockMultiShopsQueryData(
    ~edges=[
      mockMultiShopsQueryDataEdge(
        ~cku="mock-cku",
        ~id="mock-id",
        ~createdAt=Js.Date.makeWithYM(~year=2017., ~month=6., ()),
        ~name="mock-name",
        ~supplierCompanyName="mock-supplier-name",
        ~productId="mock-product-id",
        ~productName="mock-product-name",
        ~productKind=#SIMPLE,
        ~productColor=Some(#AMBER),
        ~productProducer=Some("mock-product-producer"),
        ~productDesignation=Some("mock-product-designation"),
        ~productWineType=Some(#STILL),
        ~productWhiteWineType=Some(#SWEET),
        ~productFamily=Some("mock-product-family"),
        ~productBeerType=Some("mock-product-beertype"),
        ~productRegion=Some("mock-product-region"),
        ~productCountry=Some("mock-product-country"),
        ~formattedCategory=Some("mock-formatted-category"),
        ~alcoholVolume=Some(12.),
        ~purchasedPrice=Some(10.),
        ~stockKeepingUnit=Some("mock-stock-keeping-unit"),
        ~priceLookUpCode=Some(1),
        ~internalCode=Some("mock-internal-code"),
        ~bulk=Some(false),
        ~capacityUnit=Some("mock-capacity-unit"),
        ~maxStockThreshold=Some(5),
        ~minStockThreshold=Some(1),
        ~stockOrderTriggerThreshold=Some(3),
        ~stockFormattedQuantity=Some("mock-formatted-quantity"),
        ~stockState=Some(#ALERT),
        ~stockFormattedShopsNames=Some("mock-formatted-shops-names"),
        ~formattedStatus=#ACTIVE,
        (),
      ),
      mockMultiShopsQueryDataEdge(
        ~bulk=Some(true),
        ~capacityUnit=Some("mock-capacity-unit"),
        ~productBeerType=Some("mock-beer-type"),
        (),
      ),
    ],
    (),
  )
  expect(catalogListMultiShopsTableRowsFromQueryResult(queryData))->toStrictEqual([
    {
      cku: "mock-cku",
      id: "mock-id",
      createdAt: Js.Date.fromString("2017-07-01T00:00:00.000Z"),
      name: "mock-name",
      productId: "mock-product-id",
      productName: "mock-product-name",
      productTaxRate: 20.,
      producer: Some("mock-product-producer"),
      productKind: #SIMPLE,
      color: Some(#AMBER),
      designation: Some("mock-product-designation"),
      wineType: #STILL,
      whiteWineType: #SWEET,
      family: Some("mock-product-family"),
      beerType: "mock-product-beertype",
      region: Some("mock-product-region"),
      country: Some("mock-product-country"),
      categoryName: "mock-formatted-category",
      supplierName: Some("mock-supplier-name"),
      alcoholVolume: Some(12.),
      feesList: {transportAmount: 0., taxesAmount: 0., otherAmount: 0.},
      purchasePriceDiscount: 0.,
      purchasePrice: 10.,
      retailPrice: None,
      stockKeepingUnit: Some("mock-stock-keeping-unit"),
      priceLookUpCode: Some(1),
      internalCode: Some("mock-internal-code"),
      stockQuantity: Some("mock-formatted-quantity"),
      stockState: #ALERT,
      formattedShopsNames: Some("mock-formatted-shops-names"),
      status: Some(Active),
      maxStockThreshold: 5.,
      minStockThreshold: 1.,
      stockOrderTriggerThreshold: 3.,
      bulk: false,
      bulkUnit: %raw(`undefined`),
    },
    {
      bulk: true,
      bulkUnit: Some("mock-capacity-unit"),
      cku: "",
      id: "",
      createdAt: Js.Date.fromString("1969-12-31T23:59:59.999Z"),
      name: "",
      productId: "",
      productName: "",
      productTaxRate: 20.,
      producer: %raw(`undefined`),
      productKind: #SIMPLE,
      color: %raw(`undefined`),
      designation: %raw(`undefined`),
      wineType: %raw(`undefined`),
      whiteWineType: %raw(`undefined`),
      beerType: "mock-beer-type",
      family: %raw(`undefined`),
      region: %raw(`undefined`),
      country: %raw(`undefined`),
      alcoholVolume: %raw(`undefined`),
      categoryName: "—",
      supplierName: Some(""),
      feesList: {transportAmount: 0., taxesAmount: 0., otherAmount: 0.},
      purchasePriceDiscount: 0.,
      purchasePrice: 0.,
      retailPrice: None,
      stockKeepingUnit: %raw(`undefined`),
      priceLookUpCode: %raw(`undefined`),
      internalCode: %raw(`undefined`),
      stockQuantity: %raw(`undefined`),
      stockState: #ALERT,
      formattedShopsNames: %raw(`undefined`),
      status: Some(Active),
      maxStockThreshold: 0.,
      minStockThreshold: 0.,
      stockOrderTriggerThreshold: 0.,
    },
  ])
})

test("catalogListSingleShopTableRowsFromQueryResult", () => {
  let {catalogListSingleShopTableRowsFromQueryResult} = module(CatalogListPage)

  let queryData = mockSingleShopQueryData()
  expect(
    catalogListSingleShopTableRowsFromQueryResult(queryData, ~currentPriceId=None),
  )->toStrictEqual([])

  let queryData = mockSingleShopQueryData(~edges=[mockSingleShopQueryDataEdge()], ())
  expect(
    catalogListSingleShopTableRowsFromQueryResult(queryData, ~currentPriceId=None),
  )->toStrictEqual([
    {
      cku: "",
      id: "",
      createdAt: Js.Date.fromString("1969-12-31T23:59:59.999Z"),
      productId: "",
      color: %raw(`undefined`),
      producer: %raw(`undefined`),
      name: "",
      productName: "",
      productTaxRate: 20.,
      productKind: #SIMPLE,
      designation: %raw(`undefined`),
      wineType: %raw(`undefined`),
      whiteWineType: %raw(`undefined`),
      beerType: %raw(`undefined`),
      family: %raw(`undefined`),
      categoryName: "—",
      supplierName: Some(""),
      region: %raw(`undefined`),
      country: %raw(`undefined`),
      alcoholVolume: %raw(`undefined`),
      feesList: {transportAmount: 0., taxesAmount: 0., otherAmount: 0.},
      purchasePriceDiscount: 0.,
      purchasePrice: 0.,
      retailPrice: %raw(`undefined`),
      stockKeepingUnit: %raw(`undefined`),
      priceLookUpCode: %raw(`undefined`),
      internalCode: %raw(`undefined`),
      stockQuantity: %raw(`undefined`),
      stockState: #ALERT,
      formattedShopsNames: %raw(`undefined`),
      status: Some(Active),
      maxStockThreshold: 0.,
      minStockThreshold: 0.,
      stockOrderTriggerThreshold: 0.,
      bulk: false,
      bulkUnit: %raw(`undefined`),
    },
  ])

  let queryData = mockSingleShopQueryData(
    ~edges=[
      mockSingleShopQueryDataEdge(
        ~cku="mock-cku",
        ~id="mock-id",
        ~name="mock-name",
        ~supplierCompanyName="mock-supplier-name",
        ~productId="mock-product-id",
        ~productName="mock-product-name",
        ~productKind=#SIMPLE,
        ~productColor=Some(#AMBER),
        ~productProducer=Some("mock-product-producer"),
        ~productDesignation=Some("mock-product-designation"),
        ~productWineType=Some(#STILL),
        ~productWhiteWineType=Some(#SWEET),
        ~productFamily=Some("mock-product-family"),
        ~productBeerType=Some("mock-product-beertype"),
        ~productRegion=Some("mock-product-region"),
        ~productCountry=Some("mock-product-country"),
        ~formattedCategory=Some("mock-formatted-category"),
        ~alcoholVolume=Some(12.),
        ~purchasedPrice=Some(10.),
        ~stockKeepingUnit=Some("mock-stock-keeping-unit"),
        ~priceLookUpCode=Some(2),
        ~internalCode=Some("mock-internal-code"),
        ~bulk=Some(false),
        ~capacityUnit=Some("mock-capacity-unit"),
        ~maxStockThreshold=Some(5),
        ~minStockThreshold=Some(1),
        ~stockOrderTriggerThreshold=Some(3),
        ~stockFormattedQuantity=Some("mock-formatted-quantity"),
        ~stockState=Some(#ALERT),
        ~stockFormattedShopsNames=Some("mock-formatted-shops-names"),
        ~formattedStatus=#ACTIVE,
        ~variantPrices=mockSingleShopQueryVariantPricesData(
          ~edges=[
            mockSingleShopQueryVariantPricesDataEdge(
              ~valueIncludingTax=6.12,
              ~valueExcludingTax=5.1,
              ~priceId="mock-price-id-a",
              (),
            ),
            mockSingleShopQueryVariantPricesDataEdge(
              ~valueIncludingTax=6.95,
              ~valueExcludingTax=5.79,
              ~priceId="mock-price-id-b",
              (),
            ),
          ],
          (),
        ),
        (),
      ),
      mockSingleShopQueryDataEdge(
        ~bulk=Some(true),
        ~capacityUnit=Some("mock-capacity-unit"),
        ~productBeerType=Some("mock-beer-type"),
        (),
      ),
    ],
    (),
  )
  expect(
    catalogListSingleShopTableRowsFromQueryResult(
      queryData,
      ~currentPriceId=Some("mock-price-id-b"),
    ),
  )->toStrictEqual([
    {
      cku: "mock-cku",
      id: "mock-id",
      createdAt: Js.Date.fromString("1969-12-31T23:59:59.999Z"),
      name: "mock-name",
      productId: "mock-product-id",
      productName: "mock-product-name",
      producer: Some("mock-product-producer"),
      productKind: #SIMPLE,
      color: Some(#AMBER),
      designation: Some("mock-product-designation"),
      wineType: #STILL,
      whiteWineType: #SWEET,
      family: Some("mock-product-family"),
      beerType: "mock-product-beertype",
      region: Some("mock-product-region"),
      country: Some("mock-product-country"),
      categoryName: "mock-formatted-category",
      supplierName: Some("mock-supplier-name"),
      alcoholVolume: Some(12.),
      feesList: {transportAmount: 0., taxesAmount: 0., otherAmount: 0.},
      purchasePriceDiscount: 0.,
      purchasePrice: 10.,
      retailPrice: Some({
        valueIncludingTax: 6.95,
        valueExcludingTax: 5.79,
      }),
      productTaxRate: 20.,
      stockKeepingUnit: Some("mock-stock-keeping-unit"),
      priceLookUpCode: Some(2),
      internalCode: Some("mock-internal-code"),
      stockQuantity: Some("mock-formatted-quantity"),
      stockState: #ALERT,
      formattedShopsNames: Some("mock-formatted-shops-names"),
      status: Some(Active),
      maxStockThreshold: 5.,
      minStockThreshold: 1.,
      stockOrderTriggerThreshold: 3.,
      bulk: false,
      bulkUnit: %raw(`undefined`),
    },
    {
      bulk: true,
      bulkUnit: Some("mock-capacity-unit"),
      cku: "",
      id: "",
      createdAt: Js.Date.fromString("1969-12-31T23:59:59.999Z"),
      name: "",
      productId: "",
      productName: "",
      productTaxRate: 20.,
      producer: %raw(`undefined`),
      productKind: #SIMPLE,
      color: %raw(`undefined`),
      designation: %raw(`undefined`),
      wineType: %raw(`undefined`),
      whiteWineType: %raw(`undefined`),
      beerType: "mock-beer-type",
      family: %raw(`undefined`),
      region: %raw(`undefined`),
      country: %raw(`undefined`),
      alcoholVolume: %raw(`undefined`),
      categoryName: "—",
      supplierName: Some(""),
      feesList: {transportAmount: 0., taxesAmount: 0., otherAmount: 0.},
      purchasePriceDiscount: 0.,
      purchasePrice: 0.,
      retailPrice: %raw(`undefined`),
      stockKeepingUnit: %raw(`undefined`),
      priceLookUpCode: %raw(`undefined`),
      internalCode: %raw(`undefined`),
      stockQuantity: %raw(`undefined`),
      stockState: #ALERT,
      formattedShopsNames: %raw(`undefined`),
      status: Some(Active),
      maxStockThreshold: 0.,
      minStockThreshold: 0.,
      stockOrderTriggerThreshold: 0.,
    },
  ])
})

describe("CatalogListSheetExportInput", () => {
  test("makeFromFilters", () => {
    let {makeFromFilters} = module(CatalogListSheetExportInput)

    let filters = mockStateFilters()
    expect(makeFromFilters(filters))->toStrictEqual({
      shopId: None,
      variantsActive: None,
      variantsArchived: None,
      categoryId: None,
      supplierId: None,
      producer: None,
      stock: None,
    })

    let filters = mockStateFilters(~shop=mockShop(~id="mock-shop-id", ()), ())
    expect(makeFromFilters(filters))->toStrictEqual({
      shopId: Some("mock-shop-id"),
      variantsActive: None,
      variantsArchived: None,
      categoryId: None,
      supplierId: None,
      producer: None,
      stock: None,
    })

    let filters = mockStateFilters(~shop=mockShop(~id="mock-shop-id", ()), ~status=Archived, ())
    expect(makeFromFilters(filters))->toStrictEqual({
      shopId: Some("mock-shop-id"),
      variantsActive: None,
      variantsArchived: Some(true),
      categoryId: None,
      supplierId: None,
      producer: None,
      stock: None,
    })

    let filters = mockStateFilters(~status=Active, ())
    expect(makeFromFilters(filters))->toStrictEqual({
      shopId: None,
      variantsActive: Some(true),
      variantsArchived: Some(false),
      categoryId: None,
      supplierId: None,
      producer: None,
      stock: None,
    })
    let filters = mockStateFilters(
      ~category={id: Js.Nullable.return("mock-category-id"), name: ""},
      ~supplier={id: Js.Nullable.return("mock-supplier-id"), name: ""},
      ~producer="mock-producer-name",
      ~stock={min: 0., max: 3.5},
      (),
    )
    expect(makeFromFilters(filters))->toStrictEqual({
      shopId: None,
      variantsActive: None,
      variantsArchived: None,
      categoryId: Some("mock-category-id"->Js.Nullable.return),
      supplierId: Some("mock-supplier-id"->Js.Nullable.return),
      producer: Some("mock-producer-name"),
      stock: Some({min: 0., max: 3.5}),
    })
    let filters = mockStateFilters(
      ~category={id: Js.Nullable.null, name: ""},
      ~supplier={id: Js.Nullable.null, name: ""},
      (),
    )
    expect(makeFromFilters(filters))->toStrictEqual({
      shopId: None,
      variantsActive: None,
      variantsArchived: None,
      categoryId: Some(Js.Nullable.null),
      supplierId: Some(Js.Nullable.null),
      producer: None,
      stock: None,
    })
  })
})

describe("CatalogListTableHeaderActionPricelistSelect", () => {
  let {selectByPricelists} = module(CatalogListTableHeaderActionPricelistSelect)

  describe("selectByPricelists", () => {
    it(
      "should select the pricelist matching the currentPrice id",
      () => {
        let currentPrice = Some({CatalogListExtraParamsPrice.id: "mock-id-b", name: "mock-name-b"})
        let pricelists = [
          {
            CatalogListPage.CatalogListPricelist.id: "mock-id-a",
            name: "mock-name",
            taxIncluded: true,
            enableByDefault: false,
          },
          {
            id: "mock-id-b",
            name: "mock-name",
            taxIncluded: false,
            enableByDefault: false,
          },
        ]
        expect(selectByPricelists(pricelists, ~currentPrice))->toStrictEqual(
          Some({
            id: "mock-id-b",
            name: "mock-name",
            taxIncluded: false,
            enableByDefault: false,
          }),
        )
      },
    )

    it(
      "should select the pricelist matching the currentPrice name when no id is matching",
      () => {
        let currentPrice = Some({CatalogListExtraParamsPrice.id: "mock-id-b", name: "mock-name-b"})
        let pricelists = [
          {
            CatalogListPage.CatalogListPricelist.id: "mock-id-a",
            name: "mock-name",
            taxIncluded: false,
            enableByDefault: false,
          },
          {
            id: "mock-id",
            name: "mock-name-b",
            taxIncluded: false,
            enableByDefault: false,
          },
        ]
        expect(selectByPricelists(pricelists, ~currentPrice))->toStrictEqual(
          Some({
            id: "mock-id",
            name: "mock-name-b",
            enableByDefault: false,
            taxIncluded: false,
          }),
        )

        let currentPrice = Some({CatalogListExtraParamsPrice.id: "mock-id-b", name: "mock-NAME-b"})
        let pricelists = [
          {
            CatalogListPage.CatalogListPricelist.id: "mock-id-a",
            name: "mock-name",
            taxIncluded: false,
            enableByDefault: false,
          },
          {
            id: "mock-id",
            name: "mock-NaMe-b",
            taxIncluded: false,
            enableByDefault: false,
          },
        ]
        expect(selectByPricelists(pricelists, ~currentPrice))->toStrictEqual(
          Some({
            id: "mock-id",
            name: "mock-NaMe-b",
            enableByDefault: false,
            taxIncluded: false,
          }),
        )
      },
    )

    it(
      "should select the first enableByDefault pricelist when no id/name is matching with currentPrice",
      () => {
        let currentPrice = Some({CatalogListExtraParamsPrice.id: "mock-id-b", name: "mock-name-b"})
        let pricelists = [
          {
            CatalogListPage.CatalogListPricelist.id: "mock-id-a",
            name: "mock-name",
            taxIncluded: false,
            enableByDefault: false,
          },
          {
            id: "mock-id-b",
            name: "mock-name",
            taxIncluded: false,
            enableByDefault: true,
          },
          {
            id: "mock-id-c",
            name: "mock-name",
            taxIncluded: false,
            enableByDefault: true,
          },
        ]
        expect(selectByPricelists(pricelists, ~currentPrice))->toStrictEqual(
          Some({
            id: "mock-id-b",
            name: "mock-name",
            enableByDefault: true,
            taxIncluded: false,
          }),
        )
      },
    )

    it(
      "should select the first pricelist if there is no match",
      () => {
        let currentPrice = Some({CatalogListExtraParamsPrice.id: "mock-id-z", name: "mock-name-z"})
        let pricelists = [
          {
            CatalogListPage.CatalogListPricelist.id: "mock-id-a",
            name: "mock-name",
            taxIncluded: false,
            enableByDefault: false,
          },
          {
            id: "mock-id-b",
            name: "mock-name",
            taxIncluded: false,
            enableByDefault: false,
          },
        ]
        expect(selectByPricelists(pricelists, ~currentPrice))->toStrictEqual(
          Some({
            id: "mock-id-a",
            name: "mock-name",
            enableByDefault: false,
            taxIncluded: false,
          }),
        )

        let currentPrice = None
        let pricelists = [
          {
            CatalogListPage.CatalogListPricelist.id: "mock-id-a",
            name: "mock-name",
            taxIncluded: false,
            enableByDefault: false,
          },
          {
            id: "mock-id-b",
            name: "mock-name",
            taxIncluded: false,
            enableByDefault: false,
          },
        ]
        expect(selectByPricelists(pricelists, ~currentPrice))->toStrictEqual(
          Some({
            id: "mock-id-a",
            name: "mock-name",
            enableByDefault: false,
            taxIncluded: false,
          }),
        )
      },
    )

    it(
      "should select nothing if there is no pricelist",
      () => {
        let currentPrice = Some({CatalogListExtraParamsPrice.id: "mock-id", name: "mock-name"})
        let pricelists = []
        expect(selectByPricelists(pricelists, ~currentPrice))->toStrictEqual(None)
      },
    )
  })

  describe("component", () => {
    open TestingLibraryReact

    let userEvent = TestingLibraryEvent.setup()

    let render = element =>
      element->renderWithOptions(
        ~options={wrapper: props => <Providers> {props["children"]} </Providers>},
      )

    itPromise(
      "should render an icon button that opens up a popover of select options of prices",
      async () => {
        let onRequestPriceChange = fn1(ignore)

        let shopId = "mock-shop-id"
        let pricelistsAsyncResult = AsyncData.Loading
        let price = Some({
          CatalogListExtraParamsPrice.id: "mock-price-id-a",
          name: "mock-price-name-a",
        })

        let {rerender} = render(
          <CatalogListTableHeaderActionPricelistSelect
            shopId pricelistsAsyncResult price onRequestPriceChange={onRequestPriceChange->fn}
          />,
        )

        expect(onRequestPriceChange)->toHaveBeenCalledTimes(0)

        let pricelistsAsyncResult = AsyncData.Done(
          Ok([
            {
              CatalogListPage.CatalogListPricelist.id: "mock-price-id-b",
              name: "mock-price-name-b",
              taxIncluded: true,
              enableByDefault: false,
            },
            {
              id: "mock-price-id-a",
              name: "mock-price-name-a",
              taxIncluded: false,
              enableByDefault: true,
            },
          ]),
        )

        rerender(
          <CatalogListTableHeaderActionPricelistSelect
            shopId pricelistsAsyncResult price onRequestPriceChange={onRequestPriceChange->fn}
          />,
        )->ignore

        expect(onRequestPriceChange)->toHaveBeenCalledTimes(0)

        let triggerElement = screen->getByRoleWithOptionsExn(#button, {expanded: false})

        expect(triggerElement)->toBeVisible
        expect(triggerElement)->toHaveTextContent("")

        expect(screen->queryByRole(#listbox))->toBeNone

        await userEvent->TestingLibraryEvent.click(triggerElement)

        let listboxElement = screen->getByRoleExn(#listbox)
        let (option1, option2) = within(listboxElement)->getAllByRoleExn2(#option)

        expect(listboxElement)->toBeVisible

        expect(option1)->toHaveAttributeValue("aria-selected", "true")
        expect(option1)->toHaveTextContent("mock-price-name-a")
        expect(option1)->toHaveTextContent("VAT excl.")

        expect(option2)->toHaveAttributeValue("aria-selected", "false")
        expect(option2)->toHaveTextContent("mock-price-name-b")
        expect(option2)->toHaveTextContent("VAT incl.")

        await userEvent->TestingLibraryEvent.click(option2)

        expect(onRequestPriceChange)->toHaveBeenCalledTimes(1)
        expect(onRequestPriceChange)->toHaveBeenCalledWith1({
          CatalogListExtraParamsPrice.id: "mock-price-id-b",
          name: "mock-price-name-b",
        })

        expect(screen->queryByRole(#listbox))->toBeNone
      },
    )

    itPromise(
      "should selects automatically the first price if none was passed already",
      async () => {
        let onRequestPriceChange = fn1(ignore)

        let shopId = "mock-shop-id"
        let pricelistsAsyncResult = AsyncData.Loading
        let price = None

        let {rerender} = render(
          <CatalogListTableHeaderActionPricelistSelect
            shopId pricelistsAsyncResult price onRequestPriceChange={onRequestPriceChange->fn}
          />,
        )

        let pricelistsAsyncResult = AsyncData.Done(
          Ok([
            {
              CatalogListPage.CatalogListPricelist.id: "mock-price-id-a",
              name: "mock-price-name-a",
              taxIncluded: false,
              enableByDefault: false,
            },
            {
              id: "mock-price-id-b",
              name: "mock-price-name-b",
              taxIncluded: false,
              enableByDefault: false,
            },
          ]),
        )

        rerender(
          <CatalogListTableHeaderActionPricelistSelect
            shopId pricelistsAsyncResult price onRequestPriceChange={onRequestPriceChange->fn}
          />,
        )->ignore

        expect(onRequestPriceChange)->toHaveBeenCalledTimes(1)
        expect(onRequestPriceChange)->toHaveBeenCalledWith1({
          CatalogListExtraParamsPrice.id: "mock-price-id-a",
          name: "mock-price-name-a",
        })

        let triggerElement = screen->getByRoleWithOptionsExn(#button, {expanded: false})

        await userEvent->TestingLibraryEvent.click(triggerElement)

        let listboxElement = screen->getByRoleExn(#listbox)
        let (option1, option2) = within(listboxElement)->getAllByRoleExn2(#option)

        expect(option1)->toHaveTextContent("mock-price-name-a")
        expect(option2)->toHaveTextContent("mock-price-name-b")
        expect(option1)->toHaveAttributeValue("aria-selected", "true")
        expect(option2)->toHaveAttributeValue("aria-selected", "false")
      },
    )

    itPromise(
      "should correctly change the selected price when the shopId has changed",
      async () => {
        let onRequestPriceChange = fn1(ignore)

        let shopId = "mock-shop-id-a"
        let pricelistsAsyncResult = AsyncData.Loading
        let price = None

        let {rerender} = render(
          <CatalogListTableHeaderActionPricelistSelect
            shopId pricelistsAsyncResult price onRequestPriceChange={onRequestPriceChange->fn}
          />,
        )

        let pricelistsAsyncResult = AsyncData.Done(
          Ok([
            {
              CatalogListPage.CatalogListPricelist.id: "mock-price-id-a",
              name: "mock-price-name",
              taxIncluded: false,
              enableByDefault: false,
            },
          ]),
        )

        rerender(
          <CatalogListTableHeaderActionPricelistSelect
            shopId pricelistsAsyncResult price onRequestPriceChange={onRequestPriceChange->fn}
          />,
        )->ignore

        expect(onRequestPriceChange)->toHaveBeenCalledTimes(1)
        expect(onRequestPriceChange)->toHaveBeenCalledWith1({
          CatalogListExtraParamsPrice.id: "mock-price-id-a",
          name: "mock-price-name",
        })

        let triggerElement = screen->getByRoleWithOptionsExn(#button, {expanded: false})

        await userEvent->TestingLibraryEvent.click(triggerElement)

        let listboxElement = screen->getByRoleExn(#listbox)
        let option1 = within(listboxElement)->getByRoleExn(#option)

        expect(option1)->toHaveTextContent("mock-price-name")
        expect(option1)->toHaveAttributeValue("aria-selected", "true")

        mockClear(onRequestPriceChange)

        let shopId = "mock-shop-id-b"
        let pricelistsAsyncResult = AsyncData.Loading
        let price = Some({
          CatalogListExtraParamsPrice.id: "mock-price-id-a",
          name: "mock-price-name",
        })

        rerender(
          <CatalogListTableHeaderActionPricelistSelect
            shopId pricelistsAsyncResult price onRequestPriceChange={onRequestPriceChange->fn}
          />,
        )->ignore

        let pricelistsAsyncResult = AsyncData.Done(
          Ok([
            {
              CatalogListPage.CatalogListPricelist.id: "mock-price-id-b",
              name: "mock-price-generic-name",
              taxIncluded: false,
              enableByDefault: false,
            },
          ]),
        )

        rerender(
          <CatalogListTableHeaderActionPricelistSelect
            shopId pricelistsAsyncResult price onRequestPriceChange={onRequestPriceChange->fn}
          />,
        )->ignore

        expect(onRequestPriceChange)->toHaveBeenCalledWith1({
          CatalogListExtraParamsPrice.id: "mock-price-id-b",
          name: "mock-price-generic-name",
        })

        let shopId = "mock-shop-id-c"
        let pricelistsAsyncResult = AsyncData.Loading
        let price = Some({
          CatalogListExtraParamsPrice.id: "mock-price-id-b",
          name: "mock-price-generic-name",
        })

        rerender(
          <CatalogListTableHeaderActionPricelistSelect
            shopId pricelistsAsyncResult price onRequestPriceChange={onRequestPriceChange->fn}
          />,
        )->ignore

        let pricelistsAsyncResult = AsyncData.Done(
          Ok([
            {
              CatalogListPage.CatalogListPricelist.id: "mock-price-id-c",
              name: "mock-price-generic-name",
              taxIncluded: false,
              enableByDefault: false,
            },
          ]),
        )

        rerender(
          <CatalogListTableHeaderActionPricelistSelect
            shopId pricelistsAsyncResult price onRequestPriceChange={onRequestPriceChange->fn}
          />,
        )->ignore

        expect(onRequestPriceChange)->toHaveBeenLastCalledWith1({
          CatalogListExtraParamsPrice.id: "mock-price-id-c",
          name: "mock-price-generic-name",
        })

        let listboxElement = screen->getByRoleExn(#listbox)
        let option1 = within(listboxElement)->getByRoleExn(#option)

        expect(option1)->toHaveTextContent("mock-price-generic-name")
        expect(option1)->toHaveAttributeValue("aria-selected", "true")
      },
    )
  })
})

// Some integration tests to ensure basic interactions
// with the other components and GraphQL might be valuable
// at some point.
todo("Integration test")
