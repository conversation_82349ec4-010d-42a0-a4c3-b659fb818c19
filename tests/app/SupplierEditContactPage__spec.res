open Vitest

test("stripEmptyString", () => {
  let {stripEmptyString} = module(SupplierEditContactPage)

  expect(stripEmptyString(""))->toBe(None)
  expect(stripEmptyString(" "))->toBe(None)
  expect(stripEmptyString("1"))->toBe(Some("1"))
  expect(stripEmptyString("abc"))->toBe(Some("abc"))
})

test("flatMapWithDefault", () => {
  let {flatMapWithDefault} = module(SupplierEditContactPage)

  expect(None->flatMapWithDefault("", a => Some(a)))->toBe("")
  expect(Some("hola")->flatMapWithDefault("", a => Some(a)))->toBe("hola")
  expect(Some("hola")->flatMapWithDefault("", _ => None))->toBe("")
})

test("flatMapOrEmptyString", () => {
  let {flatMapOrEmptyString} = module(SupplierEditContactPage)

  expect(None->flatMapOrEmptyString(a => Some(a)))->toBe("")
  expect(Some("hola")->flatMapOrEmptyString(a => Some(a)))->toBe("hola")
  expect(Some("hola")->flatMapOrEmptyString(_ => None))->toBe("")
})

module SupplierEditFormLenses = SupplierEditContactPage.SupplierEditFormLenses
module SupplierEditForm = SupplierEditContactPage.SupplierEditForm

let mockFormValues = (~contactId="", ~contactLastName="", ~contactFirstName="", ()) => {
  SupplierEditFormLenses.contactId,
  contactFirstName,
  contactLastName,
  contactCivility: #NEUTRAL,
  contactPosition: "",
  contactEmail: "",
  contactPhoneNumber: "",
  contactMobileNumber: "",
}

test("supplierEditFormSchema", () => {
  let {supplierEditFormSchema} = module(SupplierEditContactPage)
  let schema = supplierEditFormSchema

  let values = mockFormValues()
  expect(SupplierEditForm.validate(~values, ~schema))->toStrictEqual(
    Error([(Field(ContactLastName), "Please fulfill this field.")]),
  )

  let values = mockFormValues(~contactFirstName="mock", ())
  expect(SupplierEditForm.validate(~values, ~schema))->toStrictEqual(
    Error([(Field(ContactLastName), "Please fulfill this field.")]),
  )

  let values = mockFormValues(~contactLastName="mock", ~contactFirstName="mock", ())
  expect(SupplierEditForm.validate(~values, ~schema))->toStrictEqual(Ok())

  let values = mockFormValues(~contactLastName="mock", ())
  expect(SupplierEditForm.validate(~values, ~schema))->toStrictEqual(Ok())

  let values = mockFormValues(~contactLastName="mock", ~contactId="mock", ())
  expect(SupplierEditForm.validate(~values, ~schema))->toStrictEqual(Ok())
})

module SupplierFragment = SupplierEditContactPage.SupplierFragment

let mockTypename = () => ""

let mockQueryResult = (~email=?, ~contactsEdges=[], ()): SupplierFragment.t => {
  SupplierFragment.id: "",
  companyName: "",
  updatedAt: Js.Date.make(),
  archivedAt: None,
  intraCommunityVat: None,
  internalCode: None,
  phoneNumber: None,
  mobileNumber: None,
  email,
  note: None,
  siretNumber: None,
  shop: {id: "mock-shop-id", __typename: mockTypename()},
  contacts: {
    edges: contactsEdges,
    __typename: mockTypename(),
  },
  __typename: mockTypename(),
}

let mockQueryResultContactEdge = (
  ~id="mock-id",
  ~position=?,
  ~firstName=?,
  ~lastName="mock-last-name",
  ~civility=?,
  ~email=?,
  ~phoneNumber=?,
  ~mobileNumber=?,
  (),
) => {
  SupplierFragment.node: {
    position,
    id,
    firstName,
    lastName,
    civility,
    email,
    phoneNumber,
    mobileNumber,
    __typename: mockTypename(),
  },
  __typename: mockTypename(),
}

test("supplierEditFormInitialValuesFromQueryResult", () => {
  let {supplierEditFormInitialValuesFromQueryResult} = module(SupplierEditContactPage)

  let queryResult = mockQueryResult()
  expect(supplierEditFormInitialValuesFromQueryResult(queryResult))->toStrictEqual({
    SupplierEditFormLenses.contactId: "",
    contactFirstName: "",
    contactLastName: "",
    contactCivility: #NEUTRAL,
    contactPosition: "",
    contactEmail: "",
    contactPhoneNumber: "",
    contactMobileNumber: "",
  })

  let queryResult = mockQueryResult(~contactsEdges=[mockQueryResultContactEdge()], ())
  expect(supplierEditFormInitialValuesFromQueryResult(queryResult))->toStrictEqual({
    SupplierEditFormLenses.contactId: "mock-id",
    contactFirstName: "",
    contactLastName: "mock-last-name",
    contactCivility: #NEUTRAL,
    contactPosition: "",
    contactEmail: "",
    contactPhoneNumber: "",
    contactMobileNumber: "",
  })

  let queryResult = mockQueryResult(
    ~contactsEdges=[mockQueryResultContactEdge(~firstName="mock-first-name", ~civility=#MRS, ())],
    (),
  )
  expect(supplierEditFormInitialValuesFromQueryResult(queryResult))->toStrictEqual({
    SupplierEditFormLenses.contactId: "mock-id",
    contactFirstName: "mock-first-name",
    contactLastName: "mock-last-name",
    contactCivility: #MRS,
    contactPosition: "",
    contactEmail: "",
    contactPhoneNumber: "",
    contactMobileNumber: "",
  })

  let queryResult = mockQueryResult(
    ~contactsEdges=[
      mockQueryResultContactEdge(~email="mock-email", ~phoneNumber="mock-phone-number", ()),
    ],
    (),
  )
  expect(supplierEditFormInitialValuesFromQueryResult(queryResult))->toStrictEqual({
    SupplierEditFormLenses.contactId: "mock-id",
    contactFirstName: "",
    contactLastName: "mock-last-name",
    contactCivility: #NEUTRAL,
    contactPosition: "",
    contactEmail: "mock-email",
    contactPhoneNumber: "mock-phone-number",
    contactMobileNumber: "",
  })
})

module SupplierUpdateMutation = SupplierEditContactPage.SupplierUpdateMutation

test("supplierUpdateMutationVariablesFromFormValuesAndQueryResult", () => {
  let {supplierUpdateMutationVariablesFromFormValuesAndQueryResult} = module(
    SupplierEditContactPage
  )

  let formValues = mockFormValues()
  let queryResult = mockQueryResult()
  expect(
    supplierUpdateMutationVariablesFromFormValuesAndQueryResult(formValues, queryResult),
  )->toStrictEqual({
    SupplierUpdateMutation.id: "",
    supplierInput: {
      companyName: Some(""),
      intraCommunityVat: None,
      siretNumber: None,
      phoneNumber: None,
      mobileNumber: None,
      email: None,
      note: None,
      internalCode: None,
    },
    contactsInput: Some([
      {
        id: None,
        lastName: "",
        firstName: None,
        position: None,
        email: None,
        phoneNumber: None,
        mobileNumber: None,
        isDefault: true,
        civility: Some(#NEUTRAL),
      },
    ]),
  })

  let formValues = mockFormValues(
    ~contactFirstName="mock-contact-first-name",
    ~contactLastName="mock-contact-last-name",
    (),
  )
  let queryResult = mockQueryResult(~email="mock-email", ())
  expect(
    supplierUpdateMutationVariablesFromFormValuesAndQueryResult(formValues, queryResult),
  )->toStrictEqual({
    SupplierUpdateMutation.id: "",
    supplierInput: {
      companyName: Some(""),
      intraCommunityVat: None,
      siretNumber: None,
      phoneNumber: None,
      mobileNumber: None,
      email: Some("mock-email"),
      note: None,
      internalCode: None,
    },
    contactsInput: Some([
      {
        id: None,
        lastName: "mock-contact-last-name",
        firstName: Some("mock-contact-first-name"),
        position: None,
        email: None,
        phoneNumber: None,
        mobileNumber: None,
        isDefault: true,
        civility: Some(#NEUTRAL),
      },
    ]),
  })

  let formValues = mockFormValues(
    ~contactId="mock-contact-id",
    ~contactLastName="mock-contact-last-name",
    (),
  )
  let queryResult = mockQueryResult()
  expect(
    supplierUpdateMutationVariablesFromFormValuesAndQueryResult(formValues, queryResult),
  )->toStrictEqual({
    SupplierUpdateMutation.id: "",
    supplierInput: {
      companyName: Some(""),
      intraCommunityVat: None,
      siretNumber: None,
      phoneNumber: None,
      mobileNumber: None,
      email: None,
      note: None,
      internalCode: None,
    },
    contactsInput: Some([
      {
        id: Some("mock-contact-id"),
        lastName: "mock-contact-last-name",
        firstName: None,
        position: None,
        email: None,
        phoneNumber: None,
        mobileNumber: None,
        isDefault: true,
        civility: Some(#NEUTRAL),
      },
    ]),
  })

  let formValues = mockFormValues(
    ~contactId="mock-contact-id",
    ~contactLastName="mock-contact-last-name",
    (),
  )
  let queryResult = mockQueryResult(
    ~contactsEdges=[
      mockQueryResultContactEdge(
        ~id="contact-id",
        ~lastName="contact-last-name",
        ~email="contact-email",
        (),
      ),
    ],
    (),
  )
  expect(
    supplierUpdateMutationVariablesFromFormValuesAndQueryResult(formValues, queryResult),
  )->toStrictEqual({
    SupplierUpdateMutation.id: "",
    supplierInput: {
      companyName: Some(""),
      intraCommunityVat: None,
      siretNumber: None,
      phoneNumber: None,
      mobileNumber: None,
      email: None,
      note: None,
      internalCode: None,
    },
    contactsInput: Some([
      {
        id: Some("mock-contact-id"),
        lastName: "mock-contact-last-name",
        firstName: None,
        position: None,
        email: None,
        phoneNumber: None,
        mobileNumber: None,
        isDefault: true,
        civility: Some(#NEUTRAL),
      },
    ]),
  })
})

todo("SupplierEditFormContactFieldset")
todo("SuppplierEditFormActionsBar")

// Some integration tests to ensure basic interactions
// with the other components and GraphQL might be valuable
// at some point.
todo("Integration test")
