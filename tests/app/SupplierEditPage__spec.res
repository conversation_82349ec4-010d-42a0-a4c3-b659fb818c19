open Vitest

test("stripEmptyString", () => {
  let {stripEmptyString} = module(SupplierEditPage)

  expect(stripEmptyString(""))->toBe(None)
  expect(stripEmptyString(" "))->toBe(None)
  expect(stripEmptyString("1"))->toBe(Some("1"))
  expect(stripEmptyString("abc"))->toBe(Some("abc"))
})

test("orEmptyString", () => {
  let {orEmptyString} = module(SupplierEditPage)

  expect(orEmptyString(None))->toBe("")
  expect(orEmptyString(Some("")))->toBe("")
  expect(orEmptyString(Some(" ")))->toBe(" ")
  expect(orEmptyString(Some("abc")))->toBe("abc")
  expect(orEmptyString(Some("123")))->toBe("123")
})

let mockValues = (~companyName="", ()) => {
  SupplierEditPage.SupplierEditFormLenses.companyName,
  email: "",
  phoneNumber: "",
  mobileNumber: "",
  intraCommunityVat: "",
  siretNumber: "",
  internalCode: "",
  note: "",
}

test("supplierEditFormSchema", () => {
  let {supplierEditFormSchema: schema} = module(SupplierEditPage)
  let {validate} = module(SupplierEditPage.SupplierEditForm)

  let values = mockValues()
  expect(validate(~values, ~schema))->toStrictEqual(
    Error([(Field(CompanyName), "Please fulfill this field.")]),
  )

  let values = mockValues(~companyName="mock", ())
  expect(validate(~values, ~schema))->toStrictEqual(Ok())
})

let mockTypename = () => "mock-typename"

test("supplierEditFormInitialValuesFromQueryResult", () => {
  let {supplierEditFormInitialValuesFromQueryResult} = module(SupplierEditPage)

  expect(
    supplierEditFormInitialValuesFromQueryResult({
      __typename: mockTypename(),
      id: "",
      updatedAt: Js.Date.make(),
      archivedAt: None,
      companyName: "",
      intraCommunityVat: None,
      phoneNumber: None,
      mobileNumber: None,
      siretNumber: None,
      internalCode: None,
      email: None,
      note: None,
    }),
  )->toStrictEqual({
    companyName: "",
    email: "",
    phoneNumber: "",
    mobileNumber: "",
    intraCommunityVat: "",
    siretNumber: "",
    internalCode: "",
    note: "",
  })

  expect(
    supplierEditFormInitialValuesFromQueryResult({
      __typename: mockTypename(),
      id: "",
      updatedAt: Js.Date.make(),
      archivedAt: None,
      companyName: "mock-company-name",
      intraCommunityVat: Some(""),
      phoneNumber: Some(""),
      mobileNumber: Some(""),
      siretNumber: Some(""),
      internalCode: Some(""),
      email: Some(""),
      note: Some(""),
    }),
  )->toStrictEqual({
    companyName: "mock-company-name",
    email: "",
    phoneNumber: "",
    mobileNumber: "",
    intraCommunityVat: "",
    siretNumber: "",
    internalCode: "",
    note: "",
  })

  expect(
    supplierEditFormInitialValuesFromQueryResult({
      __typename: mockTypename(),
      id: "",
      updatedAt: Js.Date.make(),
      archivedAt: None,
      companyName: "mock-company-name",
      intraCommunityVat: Some("mock-intra-communityvat"),
      phoneNumber: Some("mock-phone-number"),
      mobileNumber: Some("mock-mobile-number"),
      siretNumber: Some("mock-siret-number"),
      internalCode: Some("mock-internal-code"),
      email: Some("mock-email"),
      note: Some("mock-note"),
    }),
  )->toStrictEqual({
    companyName: "mock-company-name",
    email: "mock-email",
    phoneNumber: "mock-phone-number",
    mobileNumber: "mock-mobile-number",
    intraCommunityVat: "mock-intra-communityvat",
    siretNumber: "mock-siret-number",
    internalCode: "mock-internal-code",
    note: "mock-note",
  })
})

test("mutationVariablesFromIdAndFormValues", () => {
  let {mutationVariablesFromIdAndFormValues} = module(SupplierEditPage)

  expect(mutationVariablesFromIdAndFormValues("", mockValues()))->toStrictEqual({
    id: "",
    supplierInput: {
      companyName: Some(""),
      intraCommunityVat: None,
      siretNumber: None,
      phoneNumber: None,
      mobileNumber: None,
      email: None,
      note: None,
      internalCode: None,
    },
  })

  expect(
    mutationVariablesFromIdAndFormValues(
      "",
      {
        companyName: "mock-company-name",
        email: "mock-email",
        phoneNumber: "mock-phone-number",
        mobileNumber: "mock-mobile-number",
        intraCommunityVat: "mock-intra-communityvat",
        siretNumber: "mock-siret-number",
        internalCode: "mock-internal-code",
        note: "mock-note",
      },
    ),
  )->toStrictEqual({
    id: "",
    supplierInput: {
      companyName: Some("mock-company-name"),
      email: Some("mock-email"),
      phoneNumber: Some("mock-phone-number"),
      mobileNumber: Some("mock-mobile-number"),
      intraCommunityVat: Some("mock-intra-communityvat"),
      siretNumber: Some("mock-siret-number"),
      internalCode: Some("mock-internal-code"),
      note: Some("mock-note"),
    },
  })
})

todo("SupplierEditFormInformationFieldset")
todo("SuppplierEditFormActionsBar")
todo("SupplierEditFormPage")

// Some integration tests to ensure basic interactions
// with the other components and GraphQL might be valuable
// at some point.
todo("Integration test")
