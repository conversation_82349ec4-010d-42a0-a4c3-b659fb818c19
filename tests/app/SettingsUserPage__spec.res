open Vitest
open TestingLibraryReact

module Lenses = SettingsUserPage.EditPasswordFormLenses
module Schema = SettingsUserPage.EditPasswordForm.Schema

describe("EditPasswordForm", () => {
  let schema = SettingsUserPage.EditPasswordFormModal.schema
  let mockState = (~currentPassword="", ~newPassword="", ~newPasswordConfirmation="", ()) => {
    SettingsUserPage.EditPasswordFormLenses.currentPassword,
    newPassword,
    newPasswordConfirmation,
  }

  it("should return no errors when all mandatory fields are well filled", () => {
    let values = mockState(
      ~currentPassword="Mock-password123",
      ~newPassword="Mock-new-password123",
      ~newPasswordConfirmation="Mock-new-password123",
      (),
    )

    expect(Schema.validate(~schema, ~values))->toStrictEqual(Ok())
  })

  it("should return an error when a mandatory field is missing", () => {
    let values = mockState()

    expect(Schema.validate(~schema, ~values))->toStrictEqual(
      Error([
        (Schema.Field(CurrentPassword), "Please fulfill this field."),
        (Field(NewPassword), "Invalid password."),
        (Field(NewPasswordConfirmation), "Please fulfill this field."),
      ]),
    )
  })

  it("should return an error when password does not match confirmation", () => {
    let values = mockState(
      ~currentPassword="Mock-password123",
      ~newPassword="Mock-new-password123",
      ~newPasswordConfirmation="Mock-new-password1234",
      (),
    )

    expect(Schema.validate(~schema, ~values))->toStrictEqual(
      Error([
        (
          Schema.Field(Lenses.NewPasswordConfirmation),
          "Password and its confirmation must be identical",
        ),
      ]),
    )
  })
})
describe("UpdatePasswordRequest", () => {
  let {encodeBody, decodeInvalidRequestFailure} = module(SettingsUserPage.UpdatePasswordRequest)

  test("encodeBody", () =>
    expect(
      encodeBody(
        ~currentPassword="old-dummy-password",
        ~newPassword="new-dummy-password",
      )->Json.stringify,
    )->toStrictEqual(`{"currentPassword":"old-dummy-password","newPassword":"new-dummy-password"}`)
  )

  test("decodeInvalidRequestFailure", () => {
    let serverFailure = {
      Request.kind: "WrongUserPassword",
      message: "",
      data: None,
    }
    expect(serverFailure->decodeInvalidRequestFailure)->toStrictEqual(WrongUserPassword)

    let serverFailure = {
      Request.kind: "Unknown",
      message: "",
      data: None,
    }
    expect(serverFailure->decodeInvalidRequestFailure)->toStrictEqual(UnknownServerFailure)
  })
})

let mockUpdatePasswordRequest = (~futureResult) => {
  (~currentPassword as _, ~newPassword as _) => Future.makePure(resolve => resolve(futureResult()))
}

describe("EditPasswordFormModal", () => {
  module TestableEditPasswordFormModal = {
    @react.component
    let make = (~onRequestClose, ~onNotification, ~updatePasswordRequest) =>
      <Providers>
        <SettingsUserPage.EditPasswordFormModal
          onRequestClose opened=true onNotification={onNotification->fn} updatePasswordRequest
        />
      </Providers>
  }

  let validPasswordMock = "ValidPassword123"
  let invalidPasswordMock = "invalid"

  itPromise("should display an error when password does not match policy", async () => {
    let userEvent = TestingLibraryEvent.setup()

    let onNotification = fn1(ignore)
    let onRequestClose = () => ()

    let requestResult = fn1(() => Error(None))
    let updatePasswordRequest = mockUpdatePasswordRequest(~futureResult=requestResult->fn)

    let _ =
      <TestableEditPasswordFormModal onRequestClose onNotification updatePasswordRequest />->render

    let inputPassword = screen->getByLabelTextExn("New password")
    expect(inputPassword)->toBeVisible

    await userEvent->TestingLibraryEvent.typeWithOptions(
      inputPassword,
      invalidPasswordMock,
      {initialSelectionStart: 0, initialSelectionEnd: 1},
    )

    let inputConfirmationPassword = screen->getByLabelTextExn("New password confirmation")
    expect(inputConfirmationPassword)->toBeVisible

    await userEvent->TestingLibraryEvent.typeWithOptions(
      inputConfirmationPassword,
      invalidPasswordMock,
      {initialSelectionStart: 0, initialSelectionEnd: 1},
    )

    let button = screen->getByRoleWithOptionsExn(#button, {name: "Save"})
    expect(button)->toBeVisible
    expect(button)->toHaveTextContent("Save")

    await userEvent->TestingLibraryEvent.click(button)

    expect(
      screen->getByTextExn(
        "There are some errors in the form, please correct them before trying to send it again.",
      ),
    )->toBeVisible
  })

  itPromise("should display error when server error", async () => {
    let userEvent = TestingLibraryEvent.setup()

    let onNotification = fn1(ignore)
    let onRequestClose = () => ()

    let requestResult = fn1(() => Error(None))
    let updatePasswordRequest = mockUpdatePasswordRequest(~futureResult=requestResult->fn)

    let _ =
      <TestableEditPasswordFormModal onRequestClose onNotification updatePasswordRequest />->render

    let inputCurrentPassword = screen->getByLabelTextExn("Current password")

    await userEvent->TestingLibraryEvent.typeWithOptions(
      inputCurrentPassword,
      validPasswordMock,
      {initialSelectionStart: 0, initialSelectionEnd: 1},
    )

    let inputPassword = screen->getByLabelTextExn("New password")

    await userEvent->TestingLibraryEvent.typeWithOptions(
      inputPassword,
      validPasswordMock,
      {initialSelectionStart: 0, initialSelectionEnd: 1},
    )

    let inputConfirmationPassword = screen->getByLabelTextExn("New password confirmation")

    await userEvent->TestingLibraryEvent.typeWithOptions(
      inputConfirmationPassword,
      validPasswordMock,
      {initialSelectionStart: 0, initialSelectionEnd: 1},
    )

    let button = screen->getByRoleWithOptionsExn(#button, {name: "Save"})

    await userEvent->TestingLibraryEvent.click(button)

    await waitFor(
      () =>
        expect(
          screen->getByTextExn(
            "An unexpected error occured. Please try again or contact the support.",
          ),
        )->toBeVisible,
    )
  })

  itPromise("should display success when server success and close modal", async () => {
    let userEvent = TestingLibraryEvent.setup()

    let onNotification = fn1(ignore)
    let onRequestClose = fn1(ignore)

    let requestResult = fn1(() => Ok(Js.Dict.empty()->Js.Json.object_))
    let updatePasswordRequest = mockUpdatePasswordRequest(~futureResult=requestResult->fn)

    let _ =
      <TestableEditPasswordFormModal
        onRequestClose={onRequestClose->fn} onNotification updatePasswordRequest
      />->render

    let inputCurrentPassword = screen->getByLabelTextExn("Current password")
    expect(inputCurrentPassword)->toBeVisible

    await userEvent->TestingLibraryEvent.typeWithOptions(
      inputCurrentPassword,
      validPasswordMock,
      {initialSelectionStart: 0, initialSelectionEnd: 1},
    )

    let inputPassword = screen->getByLabelTextExn("New password")

    await userEvent->TestingLibraryEvent.typeWithOptions(
      inputPassword,
      validPasswordMock,
      {initialSelectionStart: 0, initialSelectionEnd: 1},
    )

    let inputConfirmationPassword = screen->getByLabelTextExn("New password confirmation")

    await userEvent->TestingLibraryEvent.typeWithOptions(
      inputConfirmationPassword,
      validPasswordMock,
      {initialSelectionStart: 0, initialSelectionEnd: 1},
    )

    let button = screen->getByRoleWithOptionsExn(#button, {name: "Save"})

    await userEvent->TestingLibraryEvent.click(button)

    await waitFor(() => expect(onRequestClose)->toHaveBeenCalledTimes(1))

    expect(onNotification)->toHaveBeenCalledTimes(1)

    expect(onNotification)->toHaveBeenCalledWith1(
      Some(Success("Your user and account information has been successfully updated.")),
    )
  })
})

module EditEmailFormLenses = SettingsUserPage.EditEmailFormLenses
module EditEmailFormSchema = SettingsUserPage.EditEmailForm.Schema

describe("EditEmailForm", () => {
  let schema = SettingsUserPage.EditEmailModal.schema
  let mockState = (~newUsername="", ~password="", ()) => {
    SettingsUserPage.EditEmailFormLenses.newUsername,
    password,
  }

  it("should return no errors when all mandatory fields are well filled", () => {
    let values = mockState(~newUsername="<EMAIL>", ~password="mock-password", ())

    expect(EditEmailFormSchema.validate(~schema, ~values))->toStrictEqual(Ok())
  })

  it("should return an error when a mandatory field is missing", () => {
    let values = mockState()

    expect(EditEmailFormSchema.validate(~schema, ~values))->toStrictEqual(
      Error([
        (EditEmailFormSchema.Field(NewUsername), "Invalid email address."),
        (Field(Password), "Please fulfill this field."),
      ]),
    )
  })
})

describe("UpdateEmailRequest", () => {
  let {encodeBody, decodeInvalidRequestFailure} = module(SettingsUserPage.UpdateEmailRequest)

  test("encodeBody", () =>
    expect(
      encodeBody(~newUsername="dummy-username", ~password="dummy-password")->Json.stringify,
    )->toStrictEqual(`{"newUsername":"dummy-username","password":"dummy-password"}`)
  )

  test("decodeInvalidRequestFailure", () => {
    let serverFailure = {
      Request.kind: "WrongUserPassword",
      message: "",
      data: None,
    }
    expect(serverFailure->decodeInvalidRequestFailure)->toStrictEqual(WrongUserPassword)

    let serverFailure = {
      Request.kind: "Unknown",
      message: "",
      data: None,
    }
    expect(serverFailure->decodeInvalidRequestFailure)->toStrictEqual(UnknownServerFailure)
  })
})

let mockUpdateEmailRequest = (~futureResult) => {
  (~newUsername as _, ~password as _) => Future.makePure(resolve => resolve(futureResult()))
}

describe("EditEmailModal", () => {
  module TestableEditEmailModal = {
    @react.component
    let make = (~onRequestClose, ~onNotification, ~currentUsername, ~updateEmailRequest) => <>
      <Providers>
        <SettingsUserPage.EditEmailModal
          onRequestClose
          opened=true
          onNotification={onNotification->fn}
          currentUsername
          updateEmailRequest
        />
      </Providers>
    </>
  }

  let validEmailMock = "<EMAIL>"
  let invalidEmailMock = "invalid.email"
  let passwordMock = "dummy-password"

  itPromise("should display current email", async () => {
    let onNotification = fn1(ignore)
    let onRequestClose = () => ()

    let requestResult = fn1(() => Error(None))
    let updateEmailRequest = mockUpdateEmailRequest(~futureResult=requestResult->fn)

    let _ =
      <TestableEditEmailModal
        onRequestClose onNotification currentUsername=validEmailMock updateEmailRequest
      />->render

    let inputEmail = screen->getByLabelTextExn("Email")
    expect(inputEmail)->toBeVisible
    expect(inputEmail)->toHaveValue(validEmailMock)
  })

  itPromise("should notify an error if email is not valid", async () => {
    let userEvent = TestingLibraryEvent.setup()

    let onNotification = fn1(ignore)
    let onRequestClose = fn1(ignore)

    let requestResult = fn1(() => Error(None))
    let updateEmailRequest = mockUpdateEmailRequest(~futureResult=requestResult->fn)

    let _ =
      <TestableEditEmailModal
        onRequestClose={onRequestClose->fn}
        onNotification
        currentUsername=validEmailMock
        updateEmailRequest
      />->render

    let inputEmail = screen->getByLabelTextExn("Email")

    await userEvent->TestingLibraryEvent.typeWithOptions(
      inputEmail,
      invalidEmailMock,
      {initialSelectionStart: 0, initialSelectionEnd: validEmailMock->String.length},
    )

    let inputCurrentPassword = screen->getByLabelTextExn("Current password")
    expect(inputCurrentPassword)->toBeVisible

    await userEvent->TestingLibraryEvent.typeWithOptions(
      inputCurrentPassword,
      passwordMock,
      {initialSelectionStart: 0, initialSelectionEnd: passwordMock->String.length},
    )

    let button = screen->getByRoleWithOptionsExn(#button, {name: "Save"})
    expect(button)->toBeVisible
    expect(button)->toHaveTextContent("Save")

    await userEvent->TestingLibraryEvent.click(button)

    expect(
      screen->getByTextExn(
        "There are some errors in the form, please correct them before trying to send it again.",
      ),
    )->toBeVisible
  })

  itPromise("should close modal and notify success when server success", async () => {
    let userEvent = TestingLibraryEvent.setup()

    let onNotification = fn1(ignore)
    let onRequestClose = fn1(ignore)

    let requestResult = fn1(() => Ok(Js.Dict.empty()->Js.Json.object_))
    let updateEmailRequest = mockUpdateEmailRequest(~futureResult=requestResult->fn)

    let _ =
      <TestableEditEmailModal
        onRequestClose={onRequestClose->fn}
        onNotification
        currentUsername=validEmailMock
        updateEmailRequest
      />->render

    let inputEmail = screen->getByLabelTextExn("Email")

    await userEvent->TestingLibraryEvent.typeWithOptions(
      inputEmail,
      validEmailMock,
      {initialSelectionStart: 0, initialSelectionEnd: validEmailMock->String.length},
    )

    let inputCurrentPassword = screen->getByLabelTextExn("Current password")

    await userEvent->TestingLibraryEvent.typeWithOptions(
      inputCurrentPassword,
      passwordMock,
      {initialSelectionStart: 0, initialSelectionEnd: passwordMock->String.length},
    )

    let button = screen->getByRoleWithOptionsExn(#button, {name: "Save"})

    await userEvent->TestingLibraryEvent.click(button)

    await waitFor(() => expect(onNotification)->toHaveBeenCalledTimes(1))

    expect(onNotification)->toHaveBeenCalledWith1(
      Some(
        Banner.Warning(
          "You will receive a link by email to confirm the email address change within a few minutes.",
        ),
      ),
    )
  })
})

let mockUpdateUserNamesRequest = (~futureResult) => {
  (~name as _, ~organizationName as _) => Future.makePure(resolve => resolve(futureResult()))
}

describe("EditUserNamesFieldset", () => {
  module TestableEditUserNamesFieldset = {
    @react.component
    let make = (~name, ~organizationName, ~onNotification, ~updateUserNamesRequest) =>
      <Providers>
        <SettingsUserPage.EditUserNamesFieldset
          name organizationName onNotification={onNotification->fn} updateUserNamesRequest
        />
      </Providers>
  }

  let nameMock = "dummy name"
  let organizationNameMock = "dummy organization name"

  it("should display current account name and username", () => {
    let onNotification = fn1(ignore)

    let requestResult = fn1(() => Error(Request.UnexpectedServerError))
    let updateUserNamesRequest = mockUpdateUserNamesRequest(~futureResult=requestResult->fn)

    let _ =
      <TestableEditUserNamesFieldset
        name=nameMock organizationName=organizationNameMock onNotification updateUserNamesRequest
      />->render

    let input = screen->getByLabelTextExn("Account name")
    expect(input)->toBeVisible
    expect(input)->toHaveValue(organizationNameMock)

    let input = screen->getByLabelTextExn("Username")
    expect(input)->toBeVisible
    expect(input)->toHaveValue(nameMock)
  })

  itPromise("should reset form to its inital state on cancel", async () => {
    let userEvent = TestingLibraryEvent.setup()
    let onNotification = fn1(ignore)

    let requestResult = fn1(() => Error(Request.UnexpectedServerError))
    let updateUserNamesRequest = mockUpdateUserNamesRequest(~futureResult=requestResult->fn)

    let _ =
      <TestableEditUserNamesFieldset
        name=nameMock organizationName=organizationNameMock onNotification updateUserNamesRequest
      />->render

    let inputAccountName = screen->getByLabelTextExn("Username")
    let newAccountName = "My new account name"

    await userEvent->TestingLibraryEvent.typeWithOptions(
      inputAccountName,
      newAccountName,
      {initialSelectionStart: 0, initialSelectionEnd: newAccountName->Js.String2.length},
    )

    let inputOrganizationName = screen->getByLabelTextExn("Account name")
    let newOrganizationName = "My new organization name"

    await userEvent->TestingLibraryEvent.typeWithOptions(
      inputOrganizationName,
      newOrganizationName,
      {initialSelectionStart: 0, initialSelectionEnd: newOrganizationName->Js.String2.length},
    )
  })
})

describe("SettingsUserPage", () => {
  module TestableSettingsUserPage = {
    @react.component
    let make = (~updateEmailRequest, ~updatePasswordRequest, ~updateUserNamesRequest) =>
      <Providers>
        <SettingsUserPage updateEmailRequest updatePasswordRequest updateUserNamesRequest />
      </Providers>
  }

  let fillAnSubmitForm = async () => {
    let userEvent = TestingLibraryEvent.setup()

    let button = screen->getByRoleWithOptionsExn(#button, {name: "Save"})
    let inputAccountName = screen->getByLabelTextExn("Account name")
    let inputOrganizationName = screen->getByLabelTextExn("Username")

    let newAccountName = "My new account name"
    let newOrganizationName = "My new organization name"

    await userEvent->TestingLibraryEvent.typeWithOptions(
      inputAccountName,
      newAccountName,
      {initialSelectionStart: 0, initialSelectionEnd: newAccountName->Js.String2.length},
    )

    await userEvent->TestingLibraryEvent.typeWithOptions(
      inputOrganizationName,
      newOrganizationName,
      {initialSelectionStart: 0, initialSelectionEnd: newOrganizationName->Js.String2.length},
    )

    await userEvent->TestingLibraryEvent.click(button)
  }

  itPromise("should display error when request fails", async () => {
    let requestResult = fn1(() => Error(None))
    let updatePasswordRequest = mockUpdatePasswordRequest(~futureResult=requestResult->fn)

    let requestResult = fn1(() => Error(None))
    let updateEmailRequest = mockUpdateEmailRequest(~futureResult=requestResult->fn)

    let requestResult = fn1(() => Error(Request.UnexpectedServerError))
    let updateUserNamesRequest = mockUpdateUserNamesRequest(~futureResult=requestResult->fn)

    let _ =
      <TestableSettingsUserPage
        updateEmailRequest updatePasswordRequest updateUserNamesRequest
      />->render

    await fillAnSubmitForm()

    await waitFor(
      () =>
        expect(
          screen->getByTextExn(
            "An unexpected error occured. Please try again or contact the support.",
          ),
        )->toBeVisible,
    )
  })

  itPromise("should display success message when request succeeds", async () => {
    let mockedResponse = Json.stringifyAny({"data": Js.Nullable.null})->Option.getExn->Json.parseExn

    let requestResult = fn1(() => Ok(mockedResponse))
    let updatePasswordRequest = mockUpdatePasswordRequest(~futureResult=requestResult->fn)

    let requestResult = fn1(() => Ok(mockedResponse))
    let updateEmailRequest = mockUpdateEmailRequest(~futureResult=requestResult->fn)

    let requestResult = fn1(() => Ok(mockedResponse))
    let updateUserNamesRequest = mockUpdateUserNamesRequest(~futureResult=requestResult->fn)

    let _ =
      <TestableSettingsUserPage
        updateEmailRequest updatePasswordRequest updateUserNamesRequest
      />->render

    await fillAnSubmitForm()

    await waitFor(
      () => {
        expect(
          screen->getByTextExn("Your user and account information has been successfully updated."),
        )->toBeVisible
      },
    )
  })
})
