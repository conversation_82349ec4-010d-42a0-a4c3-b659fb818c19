open Vitest

test("baseRoute", () => {
  let {baseRoute} = module(SupplierRoutes)
  expect(baseRoute)->toBe("/suppliers")
})

test("newRoute", () => {
  let {newRoute} = module(SupplierRoutes)
  expect(newRoute())->toBe("/suppliers/new")
  expect(newRoute(~shopId="mock-shop-id", ()))->toBe("/suppliers/new?shopId=%22mock-shop-id%22")
})

test("showRoute", () => {
  let {showRoute} = module(SupplierRoutes)
  expect(showRoute(~id="mock-id"))->toBe("/suppliers/mock-id")
})

test("editRoute", () => {
  let {editRoute} = module(SupplierRoutes)
  expect(editRoute(~id="mock-id"))->toBe("/suppliers/mock-id/edit")
})

test("editLocationRoute", () => {
  let {editLocationRoute} = module(SupplierRoutes)
  expect(editLocationRoute(~id="mock-id"))->toBe("/suppliers/mock-id/edit/location")
})

test("editContactRoute", () => {
  let {editContactRoute} = module(SupplierRoutes)
  expect(editContactRoute(~id="mock-id"))->toBe("/suppliers/mock-id/edit/contact")
})

describe("CreateSupplierQueryStringCodecs", () => {
  let {encodeCreateSupplierQueryString, decodeCreateSupplierQueryString} = module(SupplierRoutes)

  it("should encode and decode in a query string correctly", () => {
    let jsonQueryString = encodeCreateSupplierQueryString({
      shopId: "mocked-shop-id",
    })

    expect(jsonQueryString)->toUnsafeStrictEqual("shopId=%22mocked-shop-id%22")
    expect(decodeCreateSupplierQueryString(jsonQueryString))->toStrictEqual(Some("mocked-shop-id"))
  })
})
