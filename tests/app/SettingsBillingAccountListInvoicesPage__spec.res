open Vitest

describe("InvoicesRequest", () => {
  open BillingAccount
  let {decodeResult} = module(InvoicesRequest)

  test("decodeResult", () => {
    let now = Js.Date.now()
    expect(
      Json.fromObjExn([
        {
          "id": "mock-invoice-id",
          "number": "mock-invoice-number",
          "total": 10.5,
          "status": "open",
          "pdfLink": "mock-invoice-pdf-link",
          "date": now,
        },
      ])->decodeResult,
    )->toStrictEqual([
      {
        id: "mock-invoice-id",
        number: "mock-invoice-number",
        total: 10.5,
        status: Some(Open),
        pdfLink: Some("mock-invoice-pdf-link"),
        paymentLink: None,
        date: now->Js.Date.fromFloat,
      },
    ])
  })
})
