open Vitest

test("stripEmptyString", () => {
  let {stripEmptyString} = module(SupplierEditLocationPage)

  expect(stripEmptyString(""))->toBe(None)
  expect(stripEmptyString(" "))->toBe(None)
  expect(stripEmptyString("1"))->toBe(Some("1"))
  expect(stripEmptyString("abc"))->toBe(Some("abc"))
})

test("flatMapOrEmptyString", () => {
  let {flatMapOrEmptyString} = module(SupplierEditLocationPage)

  expect(None->flatMapOrEmptyString(a => Some(a)))->toBe("")
  expect(Some("hola")->flatMapOrEmptyString(a => Some(a)))->toBe("hola")
  expect(Some("hola")->flatMapOrEmptyString(_ => None))->toBe("")
})

module SupplierEditForm = SupplierEditLocationPage.SupplierEditForm
module SupplierEditFormLenses = SupplierEditLocationPage.SupplierEditFormLenses

let mockFormValues = (~locationId="", ~locationLabel="", ~locationRecipient="", ()) => {
  SupplierEditFormLenses.locationId,
  locationLabel,
  locationRecipient,
  locationAddress: "",
  locationPostalCode: "",
  locationCity: "",
  locationCountry: "",
}

test("supplierEditFormSchema", () => {
  let {supplierEditFormSchema: schema} = module(SupplierEditLocationPage)

  let values = mockFormValues()
  expect(SupplierEditForm.validate(~schema, ~values))->toStrictEqual(
    Error([(Field(LocationLabel), "Please fulfill this field.")]),
  )

  let values = mockFormValues(~locationLabel="mock", ())
  expect(SupplierEditForm.validate(~schema, ~values))->toStrictEqual(Ok())

  let values = mockFormValues(~locationRecipient="mock", ())
  expect(SupplierEditForm.validate(~schema, ~values))->toStrictEqual(
    Error([(Field(LocationLabel), "Please fulfill this field.")]),
  )

  let values = mockFormValues(~locationRecipient="mock", ~locationLabel="mock", ())
  expect(SupplierEditForm.validate(~schema, ~values))->toStrictEqual(Ok())
})

module SupplierFragment = SupplierEditLocationPage.SupplierFragment

let mockTypename = () => ""

let mockQueryResult = (~email=?, ~locationsEdges=[], ()): SupplierFragment.t => {
  SupplierFragment.id: "",
  companyName: "",
  updatedAt: Js.Date.make(),
  archivedAt: None,
  intraCommunityVat: None,
  internalCode: None,
  phoneNumber: None,
  mobileNumber: None,
  email,
  note: None,
  siretNumber: None,
  shop: {id: "mock-shop-id", __typename: mockTypename()},
  locations: {
    edges: locationsEdges,
    __typename: mockTypename(),
  },
  __typename: mockTypename(),
}

let mockQueryResultLocationEdge = (
  ~id="mock-id",
  ~label="mock-label",
  ~recipient=?,
  ~address=?,
  ~postalCode=?,
  ~city=?,
  ~country=?,
  (),
) => {
  SupplierFragment.node: {
    id,
    label,
    recipient,
    address,
    postalCode,
    city,
    country,
    __typename: mockTypename(),
  },
  __typename: mockTypename(),
}

test("supplierEditFormInitialValuesFromQueryResult", () => {
  let {supplierEditFormInitialValuesFromQueryResult} = module(SupplierEditLocationPage)

  let queryResult = mockQueryResult()
  expect(supplierEditFormInitialValuesFromQueryResult(queryResult))->toStrictEqual({
    SupplierEditFormLenses.locationId: "",
    locationLabel: "",
    locationRecipient: "",
    locationAddress: "",
    locationPostalCode: "",
    locationCity: "",
    locationCountry: "",
  })

  let queryResult = mockQueryResult(~locationsEdges=[mockQueryResultLocationEdge()], ())
  expect(supplierEditFormInitialValuesFromQueryResult(queryResult))->toStrictEqual({
    SupplierEditFormLenses.locationId: "mock-id",
    locationLabel: "mock-label",
    locationRecipient: "",
    locationAddress: "",
    locationPostalCode: "",
    locationCity: "",
    locationCountry: "",
  })

  let queryResult = mockQueryResult(
    ~locationsEdges=[mockQueryResultLocationEdge(~id="", ~label="", ~country="France", ())],
    (),
  )
  expect(supplierEditFormInitialValuesFromQueryResult(queryResult))->toStrictEqual({
    SupplierEditFormLenses.locationId: "",
    locationLabel: "",
    locationRecipient: "",
    locationAddress: "",
    locationPostalCode: "",
    locationCity: "",
    locationCountry: "France",
  })

  let queryResult = mockQueryResult(
    ~locationsEdges=[
      mockQueryResultLocationEdge(
        ~id="mock-id",
        ~label="mock-label",
        ~recipient="mock-recipient",
        ~address="mock-address",
        ~postalCode="mock-postalCode",
        ~city="mock-city",
        ~country="mock-country",
        (),
      ),
    ],
    (),
  )
  expect(supplierEditFormInitialValuesFromQueryResult(queryResult))->toStrictEqual({
    SupplierEditFormLenses.locationId: "mock-id",
    locationLabel: "mock-label",
    locationRecipient: "mock-recipient",
    locationAddress: "mock-address",
    locationPostalCode: "mock-postalCode",
    locationCity: "mock-city",
    locationCountry: "mock-country",
  })
})

module SupplierUpdateMutation = SupplierEditLocationPage.SupplierUpdateMutation

test("supplierUpdateMutationVariablesFromFormValuesAndQueryResult", () => {
  let {supplierUpdateMutationVariablesFromFormValuesAndQueryResult} = module(
    SupplierEditLocationPage
  )

  let formValues = mockFormValues()
  let queryResult = mockQueryResult()
  expect(
    supplierUpdateMutationVariablesFromFormValuesAndQueryResult(formValues, queryResult),
  )->toStrictEqual({
    SupplierUpdateMutation.id: "",
    supplierInput: {
      companyName: Some(""),
      intraCommunityVat: None,
      siretNumber: None,
      phoneNumber: None,
      mobileNumber: None,
      email: None,
      note: None,
      internalCode: None,
    },
    locationsInput: Some([
      {
        id: None,
        label: %raw(`null`),
        recipient: %raw(`null`),
        postalCode: %raw(`null`),
        city: %raw(`null`),
        country: %raw(`null`),
        address: %raw(`null`),
        defaults: [#DELIVERY, #BILLING],
      },
    ]),
  })

  let formValues = mockFormValues(
    ~locationLabel="mock-location-label",
    ~locationRecipient="mock-location-recipient",
    (),
  )
  let queryResult = mockQueryResult(~email="mock-email", ())
  expect(
    supplierUpdateMutationVariablesFromFormValuesAndQueryResult(formValues, queryResult),
  )->toStrictEqual({
    SupplierUpdateMutation.id: "",
    supplierInput: {
      companyName: Some(""),
      intraCommunityVat: None,
      siretNumber: None,
      phoneNumber: None,
      mobileNumber: None,
      email: Some("mock-email"),
      note: None,
      internalCode: None,
    },
    locationsInput: Some([
      {
        id: None,
        label: "mock-location-label",
        recipient: Some("mock-location-recipient"),
        postalCode: %raw(`null`),
        city: %raw(`null`),
        country: %raw(`null`),
        address: %raw(`null`),
        defaults: [#DELIVERY, #BILLING],
      },
    ]),
  })

  let formValues = mockFormValues(
    ~locationId="mock-location-id",
    ~locationLabel="mock-location-label",
    ~locationRecipient="mock-location-recipient",
    (),
  )
  let queryResult = mockQueryResult(~email="mock-email", ())
  expect(
    supplierUpdateMutationVariablesFromFormValuesAndQueryResult(formValues, queryResult),
  )->toStrictEqual({
    SupplierUpdateMutation.id: "",
    supplierInput: {
      companyName: Some(""),
      intraCommunityVat: None,
      siretNumber: None,
      phoneNumber: None,
      mobileNumber: None,
      email: Some("mock-email"),
      note: None,
      internalCode: None,
    },
    locationsInput: Some([
      {
        id: Some("mock-location-id"),
        label: "mock-location-label",
        recipient: Some("mock-location-recipient"),
        postalCode: %raw(`null`),
        city: %raw(`null`),
        country: %raw(`null`),
        address: %raw(`null`),
        defaults: [#DELIVERY, #BILLING],
      },
    ]),
  })

  let formValues = mockFormValues(
    ~locationId="mock-location-id",
    ~locationLabel="mock-location-label",
    ~locationRecipient="mock-location-recipient",
    (),
  )
  let queryResult = mockQueryResult(
    ~email="mock-email",
    ~locationsEdges=[mockQueryResultLocationEdge(~id="id", ~country="country", ())],
    (),
  )
  expect(
    supplierUpdateMutationVariablesFromFormValuesAndQueryResult(formValues, queryResult),
  )->toStrictEqual({
    SupplierUpdateMutation.id: "",
    supplierInput: {
      companyName: Some(""),
      intraCommunityVat: None,
      siretNumber: None,
      phoneNumber: None,
      mobileNumber: None,
      email: Some("mock-email"),
      note: None,
      internalCode: None,
    },
    locationsInput: Some([
      {
        id: Some("mock-location-id"),
        label: "mock-location-label",
        recipient: Some("mock-location-recipient"),
        postalCode: %raw(`null`),
        city: %raw(`null`),
        country: %raw(`null`),
        address: %raw(`null`),
        defaults: [#DELIVERY, #BILLING],
      },
    ]),
  })
})

todo("SupplierUpdateFormLocationFieldset")
todo("SuppplierUpdateFormActionsBar")

// Some integration tests to ensure basic interactions
// with the other components and GraphQL might be valuable
// at some point.
todo("Integration test")
