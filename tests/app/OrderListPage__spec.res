open Vitest

describe("OrderExportMenuItem", () => {
  test("makeRequestBodyJson", () => {
    let {makeRequestBodyJson} = module(OrderListPage.OrderExportMenuItem)

    expect(makeRequestBodyJson(~shopIds=["shop-a-id", "shop-b-id"], ()))->toUnsafeStrictEqual({
      "shopIds": ["shop-a-id", "shop-b-id"],
      "timeZone": "UTC",
    })
    expect(makeRequestBodyJson(~shopIds=["shop-a-id"], ()))->toUnsafeStrictEqual({
      "shopIds": ["shop-a-id"],
      "timeZone": "UTC",
    })
    expect(makeRequestBodyJson(~shopIds=[], ()))->toUnsafeStrictEqual({
      "shopIds": [],
      "timeZone": "UTC",
    })

    expect(
      makeRequestBodyJson(
        ~shopIds=[],
        ~orderIssueStartDate=Js.Date.fromString("Tue Nov 02 06:32:34 2021"),
        ~orderIssueEndDate=Js.Date.fromString("Tue Nov 03 06:32:34 2021"),
        (),
      ),
    )->toUnsafeStrictEqual({
      "shopIds": [],
      "timeZone": "UTC",
      "orderIssueStartDate": "2021-11-02T06:32:34.000Z",
      "orderIssueEndDate": "2021-11-03T06:32:34.000Z",
    })

    expect(
      makeRequestBodyJson(
        ~shopIds=[],
        ~orderReceptionFinishedStartDate=Js.Date.fromString("Tue Nov 02 06:32:34 2021"),
        ~orderReceptionFinishedEndDate=Js.Date.fromString("Tue Nov 03 06:32:34 2021"),
        (),
      ),
    )->toUnsafeStrictEqual({
      "shopIds": [],
      "timeZone": "UTC",
      "orderReceptionFinishedStartDate": "2021-11-02T06:32:34.000Z",
      "orderReceptionFinishedEndDate": "2021-11-03T06:32:34.000Z",
    })
  })
})

describe("OrderProductExportMenuItem", () => {
  test("makeRequestBodyJson", () => {
    let {makeRequestBodyJson} = module(OrderListPage.OrderProductExportMenuItem)

    expect(makeRequestBodyJson(~shopIds=["shop-a-id", "shop-b-id"], ()))->toUnsafeStrictEqual({
      "shopIds": ["shop-a-id", "shop-b-id"],
      "timeZone": "UTC",
    })
    expect(makeRequestBodyJson(~shopIds=["shop-a-id"], ()))->toUnsafeStrictEqual({
      "shopIds": ["shop-a-id"],
      "timeZone": "UTC",
    })
    expect(makeRequestBodyJson(~shopIds=[], ()))->toUnsafeStrictEqual({
      "shopIds": [],
      "timeZone": "UTC",
    })

    expect(
      makeRequestBodyJson(
        ~shopIds=[],
        ~orderIssueStartDate=Js.Date.fromString("Tue Nov 02 06:32:34 2021"),
        ~orderIssueEndDate=Js.Date.fromString("Tue Nov 03 06:32:34 2021"),
        (),
      ),
    )->toUnsafeStrictEqual({
      "shopIds": [],
      "timeZone": "UTC",
      "orderIssueStartDate": "2021-11-02T06:32:34.000Z",
      "orderIssueEndDate": "2021-11-03T06:32:34.000Z",
    })

    expect(
      makeRequestBodyJson(
        ~shopIds=[],
        ~orderReceptionFinishedStartDate=Js.Date.fromString("Tue Nov 02 06:32:34 2021"),
        ~orderReceptionFinishedEndDate=Js.Date.fromString("Tue Nov 03 06:32:34 2021"),
        (),
      ),
    )->toUnsafeStrictEqual({
      "shopIds": [],
      "timeZone": "UTC",
      "orderReceptionFinishedStartDate": "2021-11-02T06:32:34.000Z",
      "orderReceptionFinishedEndDate": "2021-11-03T06:32:34.000Z",
    })
  })
})

let {mockShop} = module(Auth__Mock)

let mockQueryVariablesFilterBy = () => OrderListPage.Query.makeInputObjectInputOrdersQueryFilter()

let mockTypename = () => ""

let mockQueryDataPageInfo = (~startCursor=?, ~endCursor=?, ()) => {
  OrderListPage.Query.startCursor,
  endCursor,
  __typename: mockTypename(),
}

let mockQueryData = (~edges=[], ~pageInfo=mockQueryDataPageInfo(), ~totalCount=0, ()) => {
  OrderListPage.Query.orders: {
    edges,
    pageInfo,
    totalCount,
    __typename: mockTypename(),
  },
}

let mockRow = (~id="", ()) => {
  OrderListPage.Row.id,
  formattedName: "",
  shopName: "",
  supplierCompanyName: "",
  supplierId: None,
  issueDate: Js.Date.fromFloat(0.),
  receptionFinishedAt: None,
  estimatedReceptionDate: Js.Date.fromFloat(0.),
  statuses: [],
  totalAmountExcludingTaxes: 0.,
  totalAmountIncludingTaxes: 0.,
  totalProductsQuantity: 0,
  totalProductsExpectedQuantity: 0,
}

let mockQueryDataEdge = (
  ~id="",
  ~formattedName="",
  ~shopName="",
  ~supplierCompanyName="",
  ~issueDate=Js.Date.fromFloat(0.),
  ~receptionFinishedAt=None,
  ~estimatedReceptionDate=Js.Date.fromFloat(0.),
  ~formattedStatus=[],
  ~totalAmountExcludingTaxes=0.,
  ~totalAmountIncludingTaxes=0.,
  ~totalProductsQuantity=0,
  ~totalProductsExpectedQuantity=0,
  ~supplierId="",
  (),
) => {
  OrderListPage.Query.node: {
    id,
    formattedName,
    shopName,
    supplierCompanyName,
    issueDate,
    receptionFinishedAt,
    estimatedReceptionDate,
    formattedStatus,
    totalAmountExcludingTaxes,
    totalAmountIncludingTaxes,
    totalProductsQuantity,
    totalProductsExpectedQuantity,
    supplier: Some({
      id: supplierId,
      __typename: mockTypename(),
    }),
    __typename: mockTypename(),
  },
  __typename: mockTypename(),
}

let mockQueryVariables = () => {
  OrderListPage.Query.first: None,
  last: None,
  before: None,
  after: None,
  filterBy: None,
  search: None,
}

let mockStateFilters = (~shop=?, ~status=?, ~issueDateRange=?, ~receptionDateRange=?, ()) => {
  OrderListPage.Filters.shop,
  status,
  issueDateRange,
  receptionDateRange,
}

let mockState = (
  ~filters=mockStateFilters(),
  ~currentPage=1,
  ~previousPage=-1,
  ~searchQuery=?,
  (),
) => {
  Scaffold.filters,
  connectionArguments: {},
  currentPage,
  previousPage,
  searchQuery,
}

describe("Scaffolded", () => {
  todo("useFiltersJsonCodec")

  test("makeQueryVariables", () => {
    let {makeQueryVariables} = module(OrderListPage.Scaffolded)

    expect(makeQueryVariables(mockQueryVariables(), ~connectionArguments={}, ()))->toStrictEqual({
      OrderListPage.Query.first: None,
      last: None,
      before: None,
      after: None,
      filterBy: None,
      search: None,
    })

    expect(
      makeQueryVariables(
        mockQueryVariables(),
        ~connectionArguments={
          first: 1,
          last: 2,
          before: "before",
          after: "after",
        },
        ~search="search",
        ~filterBy=mockQueryVariablesFilterBy(),
        (),
      ),
    )->toStrictEqual({
      OrderListPage.Query.first: Some(1),
      last: Some(2),
      before: Some("before"),
      after: Some("after"),
      search: Some("search"),
      filterBy: Some(OrderListPage.Query.makeInputObjectInputOrdersQueryFilter()),
    })
  })

  test("makeQueryVariablesFilterBy", () => {
    let {makeQueryVariablesFilterBy} = module(OrderListPage.Scaffolded)

    let filters = {
      OrderListPage.Filters.shop: None,
      status: None,
      issueDateRange: None,
      receptionDateRange: None,
    }

    expect(makeQueryVariablesFilterBy(filters))->toStrictEqual({
      OrderListPage.Query.shopIds: None,
      status: None,
      createdAt: None,
      issueDate: None,
      receptionFinishedAt: None,
    })

    let filters = {
      OrderListPage.Filters.shop: Some(mockShop(~id="mock-shop-id", ())),
      status: Some(#FINALIZED),
      issueDateRange: Some((Js.Date.fromFloat(0.), Js.Date.fromFloat(1.))),
      receptionDateRange: Some((Js.Date.fromFloat(1.), Js.Date.fromFloat(0.))),
    }

    expect(makeQueryVariablesFilterBy(filters))->toStrictEqual({
      OrderListPage.Query.shopIds: Some({_in: ["mock-shop-id"]}),
      status: Some({_in: ["FINALIZED"]}),
      createdAt: None,
      issueDate: Some({
        _before: None,
        _after: None,
        _between: Some([
          Js.Date.fromFloat(0.)->Scalar.Datetime.serialize,
          Js.Date.fromFloat(1.)->Scalar.Datetime.serialize,
        ]),
      }),
      receptionFinishedAt: Some({
        _before: None,
        _after: None,
        _between: Some([
          Js.Date.fromFloat(1.)->Scalar.Datetime.serialize,
          Js.Date.fromFloat(0.)->Scalar.Datetime.serialize,
        ]),
      }),
    })
  })

  test("totalCountFromQueryData", () => {
    let {totalCountFromQueryData} = module(OrderListPage.Scaffolded)

    let queryData = mockQueryData()
    expect(totalCountFromQueryData(queryData))->toBe(0)

    let queryData = mockQueryData(~totalCount=1, ())
    expect(totalCountFromQueryData(queryData))->toBe(1)

    let queryData = mockQueryData(~totalCount=100, ())
    expect(totalCountFromQueryData(queryData))->toBe(100)

    let queryData = mockQueryData(~totalCount=-1, ())
    expect(totalCountFromQueryData(queryData))->toBe(-1)
  })

  test("cursorsFromQueryData", () => {
    let {cursorsFromQueryData} = module(OrderListPage.Scaffolded)

    let queryData = mockQueryData()
    expect(cursorsFromQueryData(queryData))->toStrictEqual((None, None))

    let queryData = mockQueryData(~pageInfo=mockQueryDataPageInfo(~startCursor="start", ()), ())
    expect(cursorsFromQueryData(queryData))->toStrictEqual((Some("start"), None))

    let queryData = mockQueryData(
      ~pageInfo=mockQueryDataPageInfo(~startCursor="start", ~endCursor="end", ()),
      (),
    )
    expect(cursorsFromQueryData(queryData))->toStrictEqual((Some("start"), Some("end")))

    let queryData = mockQueryData(~pageInfo=mockQueryDataPageInfo(~endCursor="end", ()), ())
    expect(cursorsFromQueryData(queryData))->toStrictEqual((None, Some("end")))
  })

  test("rowsFromQueryDataAndState", () => {
    let {rowsFromQueryDataAndState} = module(OrderListPage.Scaffolded)

    let queryData = mockQueryData()
    expect(rowsFromQueryDataAndState(queryData, mockState()))->toStrictEqual([])

    let queryData = mockQueryData(~edges=[mockQueryDataEdge()], ())
    expect(rowsFromQueryDataAndState(queryData, mockState()))->toStrictEqual([
      {
        OrderListPage.Row.id: "",
        formattedName: "",
        shopName: "",
        supplierCompanyName: "",
        supplierId: Some(""),
        issueDate: Js.Date.fromFloat(0.),
        receptionFinishedAt: None,
        estimatedReceptionDate: Js.Date.fromFloat(0.),
        statuses: [],
        totalAmountExcludingTaxes: 0.,
        totalAmountIncludingTaxes: 0.,
        totalProductsQuantity: 0,
        totalProductsExpectedQuantity: 0,
      },
    ])

    let queryData = mockQueryData(
      ~edges=[
        mockQueryDataEdge(
          ~id="mock-id",
          ~formattedName="mock-formatted-name",
          ~shopName="mock-shop-name",
          ~supplierCompanyName="mock-supplier-company-name",
          ~issueDate=Js.Date.fromFloat(1.),
          ~receptionFinishedAt=Some(Js.Date.fromFloat(1.)),
          ~estimatedReceptionDate=Js.Date.fromFloat(1.),
          ~formattedStatus=[#FINALIZED, #ACCEPTED],
          ~totalAmountExcludingTaxes=0.,
          ~totalAmountIncludingTaxes=0.,
          ~totalProductsQuantity=1,
          ~totalProductsExpectedQuantity=2,
          ~supplierId="mock-supplier-id",
          (),
        ),
      ],
      (),
    )
    expect(rowsFromQueryDataAndState(queryData, mockState()))->toStrictEqual([
      {
        OrderListPage.Row.id: "mock-id",
        formattedName: "mock-formatted-name",
        shopName: "mock-shop-name",
        supplierCompanyName: "mock-supplier-company-name",
        supplierId: Some("mock-supplier-id"),
        issueDate: Js.Date.fromFloat(1.),
        receptionFinishedAt: Some(Js.Date.fromFloat(1.)),
        estimatedReceptionDate: Js.Date.fromFloat(1.),
        statuses: [#FINALIZED, #ACCEPTED],
        totalAmountExcludingTaxes: 0.,
        totalAmountIncludingTaxes: 0.,
        totalProductsQuantity: 1,
        totalProductsExpectedQuantity: 2,
      },
    ])
  })

  test("keyExtractor", () => {
    let {keyExtractor} = module(OrderListPage.Scaffolded)

    let row = mockRow()
    expect(keyExtractor(row))->toBe("")

    let row = mockRow(~id="mock-id", ())
    expect(keyExtractor(row))->toBe("mock-id")
  })
})

describe("SheetExportInput", () => {
  test("makeFromState", () => {
    let {makeFromState} = module(OrderListPage.SheetExportInput)

    let state = mockState()
    expect(makeFromState(state))->toStrictEqual({
      shopId: None,
      status: None,
      issueDateRange: None,
      receptionDateRange: None,
    })

    let state = mockState(
      ~filters=mockStateFilters(
        ~shop=mockShop(~id="mock-shop-id", ()),
        ~status=#FINALIZED,
        ~issueDateRange=(Js.Date.fromFloat(0.), Js.Date.fromFloat(1.)),
        ~receptionDateRange=(Js.Date.fromFloat(1.), Js.Date.fromFloat(0.)),
        (),
      ),
      (),
    )
    expect(makeFromState(state))->toStrictEqual({
      shopId: Some("mock-shop-id"),
      status: Some(#FINALIZED),
      issueDateRange: Some((Js.Date.fromFloat(0.), Js.Date.fromFloat(1.))),
      receptionDateRange: Some((Js.Date.fromFloat(1.), Js.Date.fromFloat(0.))),
    })
  })
})

// Some integration tests to ensure basic interactions
// with the other components and GraphQL might be valuable
// at some point.
todo("Integration test")
