open Vitest

describe("CreateOrUpdateShopAccountingConfigurationRequest", () => {
  module CreateOrUpdateShopAccountingConfigurationRequest = AccountingConfigurationPage.CreateOrUpdateShopAccountingConfigurationRequest

  test("createEndpoint", () => {
    let {createEndpoint} = module(CreateOrUpdateShopAccountingConfigurationRequest)
    expect(createEndpoint())->toMatch("/accounting-export-configurations/")
  })

  test("updateEndpoint", () => {
    let {updateEndpoint} = module(CreateOrUpdateShopAccountingConfigurationRequest)
    expect(updateEndpoint(~shopId="mock-shop-id"))->toMatch(
      "/accounting-export-configurations/mock-shop-id",
    )
  })

  test("encodeBodyJson", () => {
    let {encodeBodyJson} = module(CreateOrUpdateShopAccountingConfigurationRequest)
    expect(
      encodeBodyJson({
        AccountingConfiguration.fiscalYearOpeningMonth: January,
        shopId: "mock-shop-id",
        isaComptaAccountNumber: None,
        taxesAccounts: [],
        breakdownOfConsumerSalesByCashRegisterDailyReport: false,
        includeCashFromIndividualsInPaymentJournal: false,
        maybePaymentsAccounts: None,
      }),
    )->toStrictEqual(
      Json.fromObjExn({
        "accountingExportTaxAccounts": [],
        "breakdownOfConsumerSalesByCashRegisterDailyReport": false,
        "includeCashFromIndividualsInPaymentJournal": false,
        "fiscalYearOpeningMonth": 0,
        "isaComptaAccountNumber": Js.null,
        "shopId": "mock-shop-id",
      }),
    )
    let {encodeBodyJson} = module(CreateOrUpdateShopAccountingConfigurationRequest)
    expect(
      encodeBodyJson({
        AccountingConfiguration.fiscalYearOpeningMonth: January,
        shopId: "mock-shop-id",
        isaComptaAccountNumber: None,
        taxesAccounts: [
          {
            taxId: "mock-a-tax-id",
            isaComptaCode: Some("mock-isacompta-code"),
            deductibleTax: Some(("mock-a-account-number", "mock-a-label")),
            productsSold: ("mock-a-account-number", "mock-a-label"),
          },
          {
            taxId: "mock-b-tax-id",
            isaComptaCode: None,
            deductibleTax: None,
            productsSold: ("mock-b-account-number", "mock-b-label"),
          },
        ],
        breakdownOfConsumerSalesByCashRegisterDailyReport: false,
        includeCashFromIndividualsInPaymentJournal: false,
        maybePaymentsAccounts: Some([
          {
            paymentMethod: Cash,
            accountNumber: "123",
            accountLabel: "my-label",
            isaComptaJournalCode: None,
          },
          {
            paymentMethod: DebitCard,
            accountNumber: "0",
            accountLabel: "",
            isaComptaJournalCode: Some("abc-123"),
          },
        ]),
      }),
    )->toStrictEqual(
      Json.fromObjExn({
        "accountingExportTaxAccounts": [
          {
            "taxId": "mock-a-tax-id",
            "isaComptaCode": Js.Nullable.return("mock-isacompta-code"),
            "productsSold": Js.Nullable.return({
              "accountNumber": "mock-a-account-number",
              "label": "mock-a-label",
            }),
            "deductibleTax": Js.Nullable.return({
              "accountNumber": "mock-a-account-number",
              "label": "mock-a-label",
            }),
          },
          {
            "taxId": "mock-b-tax-id",
            "isaComptaCode": Js.Nullable.null,
            "productsSold": Js.Nullable.return({
              "accountNumber": "mock-b-account-number",
              "label": "mock-b-label",
            }),
            "deductibleTax": Js.Nullable.null,
          },
        ],
        "breakdownOfConsumerSalesByCashRegisterDailyReport": false,
        "includeCashFromIndividualsInPaymentJournal": false,
        "accountingExportPaymentAccounts": [
          {
            "accountNumber": "123",
            "isaComptaJournalCode": Js.Nullable.null,
            "label": "my-label",
            "paymentMethod": "CASH",
          },
          {
            "accountNumber": "0",
            "isaComptaJournalCode": Js.Nullable.return("abc-123"),
            "label": "",
            "paymentMethod": "DEBIT_CARD",
          },
        ],
        "fiscalYearOpeningMonth": 0,
        "isaComptaAccountNumber": Js.null,
        "shopId": "mock-shop-id",
      }),
    )
  })

  todo("make")
})

describe("GetAllShopTaxesRequest", () => {
  module GetAllShopTaxesRequest = AccountingConfigurationPage.GetAllShopTaxesRequest

  let mockTypename = () => ""

  let mockQueryShopTaxEdge = (~id="", ~value=0., ()) => {
    GetAllShopTaxesRequest.Query.node: {
      id,
      value,
      __typename: mockTypename(),
    },
    __typename: mockTypename(),
  }

  test("queryEdgesToItems", () => {
    let {queryEdgesToItems} = module(GetAllShopTaxesRequest)
    expect(queryEdgesToItems([mockQueryShopTaxEdge()]))->toStrictEqual([])
    let idA = Uuid.toString(Uuid.make())
    let idB = Uuid.toString(Uuid.make())
    expect(
      queryEdgesToItems([
        mockQueryShopTaxEdge(~id=idA, ~value=1., ()),
        mockQueryShopTaxEdge(~id=idB, ~value=2., ()),
        mockQueryShopTaxEdge(~id=idB, ~value=5.5, ()),
        mockQueryShopTaxEdge(~value=3., ()),
      ]),
    )->toStrictEqual([
      {id: Uuid.unsafeFromString(idB), label: "VAT rate 5.5%", rate: 5.5},
      {id: Uuid.unsafeFromString(idB), label: "VAT rate 2%", rate: 2.},
      {id: Uuid.unsafeFromString(idA), label: "VAT rate 1%", rate: 1.},
    ])
  })

  todo("make")
})

describe("FormField", () => {
  module FormField = AccountingConfigurationPage.FormField

  test("make", () => {
    let {make} = module(FormField)
    expect(make(~value=""))->toStrictEqual({
      value: "",
      touched: false,
      errorMessage: None,
    })
    expect(make(~value="abc"))->toStrictEqual({
      value: "abc",
      touched: false,
      errorMessage: None,
    })
  })

  test("validateNonEmptyStringValue", () => {
    let {validateNonEmptyStringValue} = module(AccountingConfigurationPage.FormField)
    expect(validateNonEmptyStringValue(""))->toStrictEqual(Some("Please fulfill this field."))
    expect(validateNonEmptyStringValue("abc"))->toStrictEqual(None)
    expect(validateNonEmptyStringValue("0"))->toStrictEqual(None)
    expect(validateNonEmptyStringValue(" "))->toStrictEqual(None)
  })

  test("validateNoDuplicate", () => {
    let {validateNoDuplicate} = module(AccountingConfigurationPage.FormField)
    expect(validateNoDuplicate("ok", []))->toStrictEqual(None)
    expect(validateNoDuplicate("ok", ["ok", "ok"]))->toStrictEqual(
      Some("This field must be unique; the value ok has already been entered."),
    )
    expect(validateNoDuplicate("ok", ["no"]))->toStrictEqual(None)
  })

  test("valideNonEmptyStringLength", () => {
    let {valideNonEmptyStringLength} = module(AccountingConfigurationPage.FormField)
    expect(valideNonEmptyStringLength(1, "1"))->toStrictEqual(None)
    expect(valideNonEmptyStringLength(1, "ok"))->toStrictEqual(
      Some("This field must contain 1 characters."),
    )
    expect(valideNonEmptyStringLength(2, ""))->toStrictEqual(
      Some("This field must contain 2 characters."),
    )
  })

  test("validateLengthNonEmptyString", () => {
    let {validateStringLength} = module(AccountingConfigurationPage.FormField)
    expect(validateStringLength(1, 2, "a"))->toStrictEqual(None)
    expect(validateStringLength(1, 2, "ab"))->toStrictEqual(None)
    expect(validateStringLength(1, 2, ""))->toStrictEqual(
      Some("This field must contain between 1 and 2 characters."),
    )
    expect(validateStringLength(1, 2, "abcd"))->toStrictEqual(
      Some("This field must contain between 1 and 2 characters."),
    )
  })
})

describe("TaxAccountFormFieldset", () => {
  module TaxAccountFormFieldset = AccountingConfigurationPage.TaxAccountFormFieldset

  describe("ID", () => {
    todo("fromUuid")
    todo("unsafeFromString")
    todo("toString")
    todo("equal")
  })

  test("isDeductibleTax", () => {
    let {isDeductibleTax} = module(TaxAccountFormFieldset)
    expect(isDeductibleTax(~taxRate=-1.))->toBe(true)
    expect(isDeductibleTax(~taxRate=0.))->toBe(false)
    expect(isDeductibleTax(~taxRate=1.))->toBe(true)
  })

  let mockId = () => TaxAccountFormFieldset.ID.unsafeFromString("")

  test("isErrored", () => {
    let {isErrored} = module(TaxAccountFormFieldset)
    expect(
      isErrored({
        id: mockId(),
        rate: 0.,
        label: "",
        isaComptaCodeFormField: None,
        productsSoldFormFields: {
          accountNumberFormField: {value: "", errorMessage: None, touched: false},
          labelFormField: {value: "", errorMessage: None, touched: false},
        },
        deductibleTaxFormFields: Some({
          accountNumberFormField: {value: "", errorMessage: None, touched: false},
          labelFormField: {value: "", errorMessage: None, touched: false},
        }),
      }),
    )->toBe(false)
    expect(
      isErrored({
        id: mockId(),
        rate: 0.,
        label: "",
        isaComptaCodeFormField: None,
        productsSoldFormFields: {
          accountNumberFormField: {value: "", errorMessage: None, touched: false},
          labelFormField: {value: "", errorMessage: None, touched: false},
        },
        deductibleTaxFormFields: None,
      }),
    )->toBe(false)
    expect(
      isErrored({
        id: mockId(),
        rate: 0.,
        label: "",
        isaComptaCodeFormField: Some({
          value: "",
          errorMessage: None,
          touched: false,
        }),
        productsSoldFormFields: {
          accountNumberFormField: {value: "", errorMessage: None, touched: false},
          labelFormField: {value: "", errorMessage: None, touched: false},
        },
        deductibleTaxFormFields: None,
      }),
    )->toBe(false)
    expect(
      isErrored({
        id: mockId(),
        rate: 0.,
        label: "",
        isaComptaCodeFormField: None,
        productsSoldFormFields: {
          accountNumberFormField: {
            value: "",
            errorMessage: Some(""),
            touched: false,
          },
          labelFormField: {value: "", errorMessage: None, touched: false},
        },
        deductibleTaxFormFields: None,
      }),
    )->toBe(true)
    expect(
      isErrored({
        id: mockId(),
        rate: 0.,
        label: "",
        isaComptaCodeFormField: None,
        productsSoldFormFields: {
          accountNumberFormField: {value: "", errorMessage: None, touched: false},
          labelFormField: {value: "", errorMessage: Some(""), touched: false},
        },
        deductibleTaxFormFields: None,
      }),
    )->toBe(true)
    expect(
      isErrored({
        id: mockId(),
        rate: 0.,
        label: "",
        isaComptaCodeFormField: Some({
          value: "",
          errorMessage: Some(""),
          touched: false,
        }),
        productsSoldFormFields: {
          accountNumberFormField: {value: "", errorMessage: None, touched: false},
          labelFormField: {value: "", errorMessage: None, touched: false},
        },
        deductibleTaxFormFields: None,
      }),
    )->toBe(true)
    expect(
      isErrored({
        id: mockId(),
        rate: 0.,
        label: "",
        isaComptaCodeFormField: None,
        productsSoldFormFields: {
          accountNumberFormField: {value: "", errorMessage: None, touched: false},
          labelFormField: {value: "", errorMessage: None, touched: false},
        },
        deductibleTaxFormFields: Some({
          accountNumberFormField: {
            value: "",
            errorMessage: Some(""),
            touched: false,
          },
          labelFormField: {value: "", errorMessage: None, touched: false},
        }),
      }),
    )->toBe(true)
    expect(
      isErrored({
        id: mockId(),
        rate: 0.,
        label: "",
        isaComptaCodeFormField: None,
        productsSoldFormFields: {
          accountNumberFormField: {value: "", errorMessage: None, touched: false},
          labelFormField: {value: "", errorMessage: None, touched: false},
        },
        deductibleTaxFormFields: Some({
          accountNumberFormField: {value: "", errorMessage: None, touched: false},
          labelFormField: {value: "", errorMessage: Some(""), touched: false},
        }),
      }),
    )->toBe(true)
    expect(
      isErrored({
        id: mockId(),
        rate: 0.,
        label: "",
        isaComptaCodeFormField: Some({
          value: "",
          errorMessage: Some(""),
          touched: false,
        }),
        productsSoldFormFields: {
          accountNumberFormField: {
            value: "",
            errorMessage: Some(""),
            touched: false,
          },
          labelFormField: {value: "", errorMessage: Some(""), touched: false},
        },
        deductibleTaxFormFields: Some({
          accountNumberFormField: {
            value: "",
            errorMessage: Some(""),
            touched: false,
          },
          labelFormField: {value: "", errorMessage: Some(""), touched: false},
        }),
      }),
    )->toBe(true)
  })

  todo("isPristine")
  todo("fillWithStandard")
  todo("changeFormFieldValue")
  todo("blurFormField")
  todo("mapFormFieldValiation")
})

describe("PaymentAccountFormFieldset", () => {
  module PaymentAccountFormFieldset = AccountingConfigurationPage.PaymentAccountFormFieldset

  test("make", () => {
    let {make} = module(PaymentAccountFormFieldset)
    expect(make(~paymentMethod=DebitCard, ~vendor=Excel))->toStrictEqual({
      paymentMethod: DebitCard,
      accountNumberFormField: {
        value: "",
        touched: false,
        errorMessage: None,
      },
      accountLabelFormField: {
        value: "Payment Debit Card",
        touched: false,
        errorMessage: None,
      },
      isaComptaJournalCodeFormField: None,
    })
    expect(make(~paymentMethod=Cash, ~vendor=IsaCompta))->toStrictEqual({
      paymentMethod: Cash,
      accountNumberFormField: {
        value: "",
        touched: false,
        errorMessage: None,
      },
      accountLabelFormField: {
        value: "Payment Cash",
        touched: false,
        errorMessage: None,
      },
      isaComptaJournalCodeFormField: Some({
        value: "R1",
        touched: false,
        errorMessage: None,
      }),
    })
  })

  test("fromAccountingConfigurationPaymentAccount", () => {
    let {fromAccountingConfigurationPaymentAccount} = module(PaymentAccountFormFieldset)
    expect(
      fromAccountingConfigurationPaymentAccount(
        {
          paymentMethod: ContactlessAmex,
          accountLabel: "contactless amex",
          accountNumber: "580",
          isaComptaJournalCode: None,
        },
        ~vendor=Excel,
      ),
    )->toStrictEqual({
      paymentMethod: ContactlessAmex,
      accountNumberFormField: {
        value: "580",
        touched: false,
        errorMessage: None,
      },
      accountLabelFormField: {
        value: "contactless amex",
        touched: false,
        errorMessage: None,
      },
      isaComptaJournalCodeFormField: None,
    })
    expect(
      fromAccountingConfigurationPaymentAccount(
        {
          paymentMethod: ContactlessAmex,
          accountLabel: "contactless amex",
          accountNumber: "580",
          isaComptaJournalCode: None,
        },
        ~vendor=IsaCompta,
      ),
    )->toStrictEqual({
      paymentMethod: ContactlessAmex,
      accountNumberFormField: {
        value: "580",
        touched: false,
        errorMessage: None,
      },
      accountLabelFormField: {
        value: "contactless amex",
        touched: false,
        errorMessage: None,
      },
      isaComptaJournalCodeFormField: Some({
        value: "",
        touched: false,
        errorMessage: None,
      }),
    })
    expect(
      fromAccountingConfigurationPaymentAccount(
        {
          paymentMethod: BankTransfer,
          accountLabel: "bank transfer",
          accountNumber: "",
          isaComptaJournalCode: Some("RE"),
        },
        ~vendor=IsaCompta,
      ),
    )->toStrictEqual({
      paymentMethod: BankTransfer,
      accountNumberFormField: {
        value: "",
        touched: false,
        errorMessage: None,
      },
      accountLabelFormField: {
        value: "bank transfer",
        touched: false,
        errorMessage: None,
      },
      isaComptaJournalCodeFormField: Some({
        value: "RE",
        touched: false,
        errorMessage: None,
      }),
    })
    expect(
      fromAccountingConfigurationPaymentAccount(
        {
          paymentMethod: BankTransfer,
          accountLabel: "bank transfer",
          accountNumber: "",
          isaComptaJournalCode: Some("RE"),
        },
        ~vendor=Excel,
      ),
    )->toStrictEqual({
      paymentMethod: BankTransfer,
      accountNumberFormField: {
        value: "",
        touched: false,
        errorMessage: None,
      },
      accountLabelFormField: {
        value: "bank transfer",
        touched: false,
        errorMessage: None,
      },
      isaComptaJournalCodeFormField: None,
    })
  })

  todo("isPristine")
  todo("isErrored")
  todo("changeFormFieldValue")
  todo("blurFormField")
  todo("mapFormFieldValiation")
})

describe("Reducer", () => {
  test("isDataErrored", () => {
    let {isDataErrored} = module(AccountingConfigurationPage.Reducer)
    expect(
      isDataErrored({
        {
          submission: NotAsked,
          initialAccountingConfiguration: None,
          vendor: Excel,
          fiscalYearOpeningMonth: January,
          breakdownOfConsumerSalesByCashRegisterDailyReport: false,
          includeCashFromIndividualsInPaymentJournal: false,
          isaComptaAccountNumberFormField: None,
          taxesAccountsFormArrayFieldset: [],
          maybePaymentsAccountsFormArrayFieldset: None,
        }
      }),
    )->toBe(false)
    expect(
      isDataErrored({
        submission: NotAsked,
        initialAccountingConfiguration: None,
        vendor: Excel,
        fiscalYearOpeningMonth: January,
        breakdownOfConsumerSalesByCashRegisterDailyReport: false,
        includeCashFromIndividualsInPaymentJournal: false,
        isaComptaAccountNumberFormField: None,
        taxesAccountsFormArrayFieldset: [],
        maybePaymentsAccountsFormArrayFieldset: None,
      }),
    )->toBe(false)
    expect(
      isDataErrored({
        submission: NotAsked,
        initialAccountingConfiguration: None,
        vendor: Excel,
        fiscalYearOpeningMonth: January,
        breakdownOfConsumerSalesByCashRegisterDailyReport: false,
        includeCashFromIndividualsInPaymentJournal: false,
        isaComptaAccountNumberFormField: Some({
          value: "",
          errorMessage: None,
          touched: true,
        }),
        taxesAccountsFormArrayFieldset: [],
        maybePaymentsAccountsFormArrayFieldset: None,
      }),
    )->toBe(false)
    expect(
      isDataErrored({
        submission: NotAsked,
        initialAccountingConfiguration: None,
        vendor: Excel,
        fiscalYearOpeningMonth: January,
        breakdownOfConsumerSalesByCashRegisterDailyReport: false,
        includeCashFromIndividualsInPaymentJournal: false,
        isaComptaAccountNumberFormField: Some({
          value: "abc",
          errorMessage: Some(""),
          touched: false,
        }),
        taxesAccountsFormArrayFieldset: [],
        maybePaymentsAccountsFormArrayFieldset: None,
      }),
    )->toBe(true)
  })

  test("findDuplicates", () => {
    let {findDuplicates} = module(AccountingConfigurationPage.Reducer)

    expect(findDuplicates([]))->toStrictEqual([])
    expect(findDuplicates(["abc", "abd", "a"]))->toStrictEqual([])
    expect(findDuplicates(["abc", "abd", "abc"]))->toStrictEqual(["abc"])
    expect(findDuplicates(["abc", "abd", "abc", "abd", "abc"]))->toStrictEqual(["abc", "abd"])
  })

  describe("mapDataFormFieldValiation", () => {
    let {mapDataFormFieldValiation} = module(AccountingConfigurationPage.Reducer)
    module TaxAccountFormFieldset = AccountingConfigurationPage.TaxAccountFormFieldset
    let {unsafeFromString: unsafeTaxAccountIdFromString} = module(TaxAccountFormFieldset.ID)

    it(
      "should give error when field is missing",
      () => {
        expect(
          mapDataFormFieldValiation({
            submission: NotAsked,
            initialAccountingConfiguration: None,
            vendor: Excel,
            fiscalYearOpeningMonth: January,
            breakdownOfConsumerSalesByCashRegisterDailyReport: false,
            isaComptaAccountNumberFormField: None,
            taxesAccountsFormArrayFieldset: [],
            maybePaymentsAccountsFormArrayFieldset: None,
            includeCashFromIndividualsInPaymentJournal: false,
          }),
        )->toStrictEqual({
          submission: NotAsked,
          initialAccountingConfiguration: None,
          vendor: Excel,
          fiscalYearOpeningMonth: January,
          breakdownOfConsumerSalesByCashRegisterDailyReport: false,
          isaComptaAccountNumberFormField: None,
          taxesAccountsFormArrayFieldset: [],
          maybePaymentsAccountsFormArrayFieldset: None,
          includeCashFromIndividualsInPaymentJournal: false,
        })
        expect(
          mapDataFormFieldValiation({
            submission: NotAsked,
            initialAccountingConfiguration: None,
            vendor: IsaCompta,
            fiscalYearOpeningMonth: January,
            breakdownOfConsumerSalesByCashRegisterDailyReport: false,
            isaComptaAccountNumberFormField: Some({
              value: "",
              errorMessage: None,
              touched: false,
            }),
            taxesAccountsFormArrayFieldset: [],
            maybePaymentsAccountsFormArrayFieldset: None,
            includeCashFromIndividualsInPaymentJournal: false,
          }),
        )->toStrictEqual({
          submission: NotAsked,
          initialAccountingConfiguration: None,
          vendor: IsaCompta,
          fiscalYearOpeningMonth: January,
          breakdownOfConsumerSalesByCashRegisterDailyReport: false,
          isaComptaAccountNumberFormField: Some({
            value: "",
            errorMessage: Some("Please fulfill this field."),
            touched: false,
          }),
          taxesAccountsFormArrayFieldset: [],
          maybePaymentsAccountsFormArrayFieldset: None,
          includeCashFromIndividualsInPaymentJournal: false,
        })
      },
    )

    it(
      "should remove error when value is correct",
      () => {
        expect(
          mapDataFormFieldValiation({
            submission: NotAsked,
            initialAccountingConfiguration: None,
            vendor: IsaCompta,
            fiscalYearOpeningMonth: January,
            breakdownOfConsumerSalesByCashRegisterDailyReport: false,
            isaComptaAccountNumberFormField: Some({
              value: "abc",
              errorMessage: Some("Please fulfill this field."),
              touched: false,
            }),
            taxesAccountsFormArrayFieldset: [],
            maybePaymentsAccountsFormArrayFieldset: None,
            includeCashFromIndividualsInPaymentJournal: false,
          }),
        )->toStrictEqual({
          submission: NotAsked,
          initialAccountingConfiguration: None,
          vendor: IsaCompta,
          fiscalYearOpeningMonth: January,
          breakdownOfConsumerSalesByCashRegisterDailyReport: false,
          isaComptaAccountNumberFormField: Some({
            value: "abc",
            errorMessage: None,
            touched: false,
          }),
          taxesAccountsFormArrayFieldset: [],
          maybePaymentsAccountsFormArrayFieldset: None,
          includeCashFromIndividualsInPaymentJournal: false,
        })
      },
    )

    it(
      "should give an error when value is too long",
      () => {
        expect(
          mapDataFormFieldValiation({
            submission: NotAsked,
            initialAccountingConfiguration: None,
            vendor: IsaCompta,
            fiscalYearOpeningMonth: January,
            breakdownOfConsumerSalesByCashRegisterDailyReport: false,
            isaComptaAccountNumberFormField: Some({
              value: "abc",
              errorMessage: Some("Please fulfill this field."),
              touched: false,
            }),
            taxesAccountsFormArrayFieldset: [
              {
                id: unsafeTaxAccountIdFromString("********-53dd-417f-87e4-35c8429f72c2"),
                rate: 10.,
                label: "10",
                isaComptaCodeFormField: Some({
                  value: "code-1",
                  touched: false,
                  errorMessage: None,
                }),
                productsSoldFormFields: {
                  accountNumberFormField: {
                    value: "mock-number-1",
                    touched: false,
                    errorMessage: None,
                  },
                  labelFormField: {
                    value: "number-label-1",
                    touched: false,
                    errorMessage: None,
                  },
                },
                deductibleTaxFormFields: None,
              },
              {
                id: unsafeTaxAccountIdFromString("********-53dd-417f-87e4-35c8429f72c2"),
                rate: 20.,
                label: "20",
                isaComptaCodeFormField: Some({
                  value: "code-1",
                  touched: false,
                  errorMessage: None,
                }),
                productsSoldFormFields: {
                  accountNumberFormField: {
                    value: "mock-number",
                    touched: false,
                    errorMessage: None,
                  },
                  labelFormField: {
                    value: "number-label",
                    touched: false,
                    errorMessage: None,
                  },
                },
                deductibleTaxFormFields: None,
              },
            ],
            maybePaymentsAccountsFormArrayFieldset: None,
            includeCashFromIndividualsInPaymentJournal: false,
          }),
        )->toStrictEqual({
          submission: NotAsked,
          initialAccountingConfiguration: None,
          vendor: IsaCompta,
          fiscalYearOpeningMonth: January,
          breakdownOfConsumerSalesByCashRegisterDailyReport: false,
          isaComptaAccountNumberFormField: Some({
            value: "abc",
            errorMessage: None,
            touched: false,
          }),
          taxesAccountsFormArrayFieldset: [
            {
              id: unsafeTaxAccountIdFromString("********-53dd-417f-87e4-35c8429f72c2"),
              rate: 10.,
              label: "10",
              isaComptaCodeFormField: Some({
                value: "code-1",
                touched: false,
                errorMessage: Some("This field must contain between 1 and 2 characters."),
              }),
              productsSoldFormFields: {
                accountNumberFormField: {
                  value: "mock-number-1",
                  touched: false,
                  errorMessage: None,
                },
                labelFormField: {
                  value: "number-label-1",
                  touched: false,
                  errorMessage: None,
                },
              },
              deductibleTaxFormFields: None,
            },
            {
              id: unsafeTaxAccountIdFromString("********-53dd-417f-87e4-35c8429f72c2"),
              rate: 20.,
              label: "20",
              isaComptaCodeFormField: Some({
                value: "code-1",
                touched: false,
                errorMessage: Some("This field must contain between 1 and 2 characters."),
              }),
              productsSoldFormFields: {
                accountNumberFormField: {
                  value: "mock-number",
                  touched: false,
                  errorMessage: None,
                },
                labelFormField: {
                  value: "number-label",
                  touched: false,
                  errorMessage: None,
                },
              },
              deductibleTaxFormFields: None,
            },
          ],
          maybePaymentsAccountsFormArrayFieldset: None,
          includeCashFromIndividualsInPaymentJournal: false,
        })
      },
    )

    it(
      "should give an error on duplication",
      () => {
        expect(
          mapDataFormFieldValiation({
            submission: NotAsked,
            initialAccountingConfiguration: None,
            vendor: IsaCompta,
            fiscalYearOpeningMonth: January,
            breakdownOfConsumerSalesByCashRegisterDailyReport: false,
            isaComptaAccountNumberFormField: Some({
              value: "abc",
              errorMessage: Some("Please fulfill this field."),
              touched: false,
            }),
            includeCashFromIndividualsInPaymentJournal: false,
            taxesAccountsFormArrayFieldset: [
              {
                id: unsafeTaxAccountIdFromString("********-53dd-417f-87e4-35c8429f72c2"),
                rate: 10.,
                label: "10",
                isaComptaCodeFormField: Some({
                  value: "1",
                  touched: false,
                  errorMessage: None,
                }),
                productsSoldFormFields: {
                  accountNumberFormField: {
                    value: "mock-number-1",
                    touched: false,
                    errorMessage: None,
                  },
                  labelFormField: {
                    value: "number-label",
                    touched: false,
                    errorMessage: None,
                  },
                },
                deductibleTaxFormFields: None,
              },
              {
                id: unsafeTaxAccountIdFromString("********-53dd-417f-87e4-35c8429f72c3"),
                rate: 20.,
                label: "20",
                isaComptaCodeFormField: Some({
                  value: "1",
                  touched: false,
                  errorMessage: None,
                }),
                productsSoldFormFields: {
                  accountNumberFormField: {
                    value: "mock-number-2",
                    touched: false,
                    errorMessage: None,
                  },
                  labelFormField: {
                    value: "number-label",
                    touched: false,
                    errorMessage: None,
                  },
                },
                deductibleTaxFormFields: None,
              },
            ],
            maybePaymentsAccountsFormArrayFieldset: Some([
              {
                paymentMethod: PaymentMethod.Amex,
                accountNumberFormField: {
                  value: "mock-account-number-1",
                  touched: true,
                  errorMessage: None,
                },
                accountLabelFormField: {
                  value: "",
                  touched: false,
                  errorMessage: Some("Please fulfill this field."),
                },
                isaComptaJournalCodeFormField: Some({
                  value: "R1",
                  touched: false,
                  errorMessage: None,
                }),
              },
              {
                paymentMethod: PaymentMethod.Paypal,
                accountNumberFormField: {
                  value: "mock-account-number-2",
                  touched: true,
                  errorMessage: None,
                },
                accountLabelFormField: {
                  value: "",
                  touched: false,
                  errorMessage: Some("Please fulfill this field."),
                },
                isaComptaJournalCodeFormField: Some({
                  value: "R1",
                  touched: false,
                  errorMessage: None,
                }),
              },
            ]),
          }),
        )->toStrictEqual({
          submission: NotAsked,
          initialAccountingConfiguration: None,
          vendor: IsaCompta,
          fiscalYearOpeningMonth: January,
          breakdownOfConsumerSalesByCashRegisterDailyReport: false,
          isaComptaAccountNumberFormField: Some({
            value: "abc",
            errorMessage: None,
            touched: false,
          }),
          taxesAccountsFormArrayFieldset: [
            {
              id: unsafeTaxAccountIdFromString("********-53dd-417f-87e4-35c8429f72c2"),
              rate: 10.,
              label: "10",
              isaComptaCodeFormField: Some({
                value: "1",
                touched: false,
                errorMessage: Some(
                  "This field must be unique; the value 1 has already been entered.",
                ),
              }),
              productsSoldFormFields: {
                accountNumberFormField: {
                  value: "mock-number-1",
                  touched: false,
                  errorMessage: None,
                },
                labelFormField: {
                  value: "number-label",
                  touched: false,
                  errorMessage: None,
                },
              },
              deductibleTaxFormFields: None,
            },
            {
              id: unsafeTaxAccountIdFromString("********-53dd-417f-87e4-35c8429f72c3"),
              rate: 20.,
              label: "20",
              isaComptaCodeFormField: Some({
                value: "1",
                touched: false,
                errorMessage: Some(
                  "This field must be unique; the value 1 has already been entered.",
                ),
              }),
              productsSoldFormFields: {
                accountNumberFormField: {
                  value: "mock-number-2",
                  touched: false,
                  errorMessage: None,
                },
                labelFormField: {
                  value: "number-label",
                  touched: false,
                  errorMessage: None,
                },
              },
              deductibleTaxFormFields: None,
            },
          ],
          maybePaymentsAccountsFormArrayFieldset: Some([
            {
              paymentMethod: PaymentMethod.Amex,
              accountNumberFormField: {
                value: "mock-account-number-1",
                touched: true,
                errorMessage: None,
              },
              accountLabelFormField: {
                value: "",
                touched: false,
                errorMessage: Some("Please fulfill this field."),
              },
              isaComptaJournalCodeFormField: Some({
                value: "R1",
                touched: false,
                errorMessage: Some(
                  "This field must be unique; the value R1 has already been entered.",
                ),
              }),
            },
            {
              paymentMethod: PaymentMethod.Paypal,
              accountNumberFormField: {
                value: "mock-account-number-2",
                touched: true,
                errorMessage: None,
              },
              accountLabelFormField: {
                value: "",
                touched: false,
                errorMessage: Some("Please fulfill this field."),
              },
              isaComptaJournalCodeFormField: Some({
                value: "R1",
                touched: false,
                errorMessage: Some(
                  "This field must be unique; the value R1 has already been entered.",
                ),
              }),
            },
          ]),
          includeCashFromIndividualsInPaymentJournal: false,
        })
      },
    )

    it(
      "should give no error on duplication on different journal",
      () => {
        expect(
          mapDataFormFieldValiation({
            submission: NotAsked,
            initialAccountingConfiguration: None,
            vendor: IsaCompta,
            fiscalYearOpeningMonth: January,
            breakdownOfConsumerSalesByCashRegisterDailyReport: false,
            isaComptaAccountNumberFormField: Some({
              value: "abc",
              errorMessage: Some("Please fulfill this field."),
              touched: false,
            }),
            includeCashFromIndividualsInPaymentJournal: false,
            taxesAccountsFormArrayFieldset: [
              {
                id: unsafeTaxAccountIdFromString("********-53dd-417f-87e4-35c8429f72c2"),
                rate: 10.,
                label: "10",
                isaComptaCodeFormField: Some({
                  value: "R1",
                  touched: false,
                  errorMessage: None,
                }),
                productsSoldFormFields: {
                  accountNumberFormField: {
                    value: "mock-number",
                    touched: false,
                    errorMessage: None,
                  },
                  labelFormField: {
                    value: "number-label",
                    touched: false,
                    errorMessage: None,
                  },
                },
                deductibleTaxFormFields: None,
              },
            ],
            maybePaymentsAccountsFormArrayFieldset: Some([
              {
                paymentMethod: PaymentMethod.Amex,
                accountNumberFormField: {
                  value: "mock-account-number",
                  touched: true,
                  errorMessage: None,
                },
                accountLabelFormField: {
                  value: "",
                  touched: false,
                  errorMessage: Some("Please fulfill this field."),
                },
                isaComptaJournalCodeFormField: Some({
                  value: "R1",
                  touched: false,
                  errorMessage: None,
                }),
              },
            ]),
          }),
        )->toStrictEqual({
          submission: NotAsked,
          initialAccountingConfiguration: None,
          vendor: IsaCompta,
          fiscalYearOpeningMonth: January,
          breakdownOfConsumerSalesByCashRegisterDailyReport: false,
          isaComptaAccountNumberFormField: Some({
            value: "abc",
            errorMessage: None,
            touched: false,
          }),
          taxesAccountsFormArrayFieldset: [
            {
              id: unsafeTaxAccountIdFromString("********-53dd-417f-87e4-35c8429f72c2"),
              rate: 10.,
              label: "10",
              isaComptaCodeFormField: Some({
                value: "R1",
                touched: false,
                errorMessage: None,
              }),
              productsSoldFormFields: {
                accountNumberFormField: {
                  value: "mock-number",
                  touched: false,
                  errorMessage: None,
                },
                labelFormField: {
                  value: "number-label",
                  touched: false,
                  errorMessage: None,
                },
              },
              deductibleTaxFormFields: None,
            },
          ],
          maybePaymentsAccountsFormArrayFieldset: Some([
            {
              paymentMethod: PaymentMethod.Amex,
              accountNumberFormField: {
                value: "mock-account-number",
                touched: true,
                errorMessage: None,
              },
              accountLabelFormField: {
                value: "",
                touched: false,
                errorMessage: Some("Please fulfill this field."),
              },
              isaComptaJournalCodeFormField: Some({
                value: "R1",
                touched: false,
                errorMessage: None,
              }),
            },
          ]),
          includeCashFromIndividualsInPaymentJournal: false,
        })
      },
    )

    expect(
      mapDataFormFieldValiation({
        submission: NotAsked,
        initialAccountingConfiguration: None,
        vendor: Excel,
        fiscalYearOpeningMonth: January,
        breakdownOfConsumerSalesByCashRegisterDailyReport: false,
        includeCashFromIndividualsInPaymentJournal: false,
        isaComptaAccountNumberFormField: None,
        taxesAccountsFormArrayFieldset: [],
        maybePaymentsAccountsFormArrayFieldset: None,
      }),
    )->toStrictEqual({
      submission: NotAsked,
      initialAccountingConfiguration: None,
      vendor: Excel,
      fiscalYearOpeningMonth: January,
      breakdownOfConsumerSalesByCashRegisterDailyReport: false,
      includeCashFromIndividualsInPaymentJournal: false,
      isaComptaAccountNumberFormField: None,
      taxesAccountsFormArrayFieldset: [],
      maybePaymentsAccountsFormArrayFieldset: None,
    })
    expect(
      mapDataFormFieldValiation({
        submission: NotAsked,
        initialAccountingConfiguration: None,
        vendor: IsaCompta,
        fiscalYearOpeningMonth: January,
        breakdownOfConsumerSalesByCashRegisterDailyReport: false,
        includeCashFromIndividualsInPaymentJournal: false,
        isaComptaAccountNumberFormField: Some({
          value: "",
          errorMessage: None,
          touched: false,
        }),
        taxesAccountsFormArrayFieldset: [],
        maybePaymentsAccountsFormArrayFieldset: None,
      }),
    )->toStrictEqual({
      submission: NotAsked,
      initialAccountingConfiguration: None,
      vendor: IsaCompta,
      fiscalYearOpeningMonth: January,
      breakdownOfConsumerSalesByCashRegisterDailyReport: false,
      includeCashFromIndividualsInPaymentJournal: false,
      isaComptaAccountNumberFormField: Some({
        value: "",
        errorMessage: Some("Please fulfill this field."),
        touched: false,
      }),
      taxesAccountsFormArrayFieldset: [],
      maybePaymentsAccountsFormArrayFieldset: None,
    })
    expect(
      mapDataFormFieldValiation({
        submission: NotAsked,
        initialAccountingConfiguration: None,
        vendor: IsaCompta,
        fiscalYearOpeningMonth: January,
        breakdownOfConsumerSalesByCashRegisterDailyReport: false,
        includeCashFromIndividualsInPaymentJournal: false,
        isaComptaAccountNumberFormField: Some({
          value: "abc",
          errorMessage: Some("Please fulfill this field."),
          touched: false,
        }),
        taxesAccountsFormArrayFieldset: [],
        maybePaymentsAccountsFormArrayFieldset: None,
      }),
    )->toStrictEqual({
      submission: NotAsked,
      initialAccountingConfiguration: None,
      vendor: IsaCompta,
      fiscalYearOpeningMonth: January,
      breakdownOfConsumerSalesByCashRegisterDailyReport: false,
      includeCashFromIndividualsInPaymentJournal: false,
      isaComptaAccountNumberFormField: Some({
        value: "abc",
        errorMessage: None,
        touched: false,
      }),
      taxesAccountsFormArrayFieldset: [],
      maybePaymentsAccountsFormArrayFieldset: None,
    })
  })

  todo("isSubmitting")
  todo("isPristine")

  test("initialState", () => {
    let {initialState} = module(AccountingConfigurationPage.Reducer)
    expect(initialState())->toStrictEqual(NotAsked)
  })

  describe("initialStateFromOkResult", () => {
    module TaxAccountFormFieldset = AccountingConfigurationPage.TaxAccountFormFieldset

    let {initialStateFromOkResult} = module(AccountingConfigurationPage.Reducer)
    let {unsafeFromString: unsafeTaxAccountIdFromString} = module(TaxAccountFormFieldset.ID)

    todo("with initial accounting configuration")

    test(
      "without initial accountingConfiguration",
      () => {
        expect(initialStateFromOkResult(~allShopTaxes=[], ~vendor=Excel, ()))->toStrictEqual(
          Done(
            Ok({
              submission: NotAsked,
              initialAccountingConfiguration: None,
              vendor: Excel,
              fiscalYearOpeningMonth: January,
              breakdownOfConsumerSalesByCashRegisterDailyReport: true,
              includeCashFromIndividualsInPaymentJournal: false,
              isaComptaAccountNumberFormField: None,
              taxesAccountsFormArrayFieldset: [],
              maybePaymentsAccountsFormArrayFieldset: None,
            }),
          ),
        )
        expect(
          initialStateFromOkResult(
            ~allShopTaxes=[
              {
                id: Uuid.unsafeFromString("********-53dd-417f-87e4-35c8429f72c2"),
                label: "",
                rate: 0.,
              },
              {
                id: Uuid.unsafeFromString("13bcadec-4c3a-41ad-baa3-095d05978eda"),
                label: "1",
                rate: 1.,
              },
            ],
            ~vendor=IsaCompta,
            (),
          ),
        )->toStrictEqual(
          Done(
            Ok({
              submission: NotAsked,
              initialAccountingConfiguration: None,
              vendor: IsaCompta,
              fiscalYearOpeningMonth: January,
              breakdownOfConsumerSalesByCashRegisterDailyReport: true,
              includeCashFromIndividualsInPaymentJournal: false,
              isaComptaAccountNumberFormField: Some({
                value: "",
                touched: false,
                errorMessage: None,
              }),
              taxesAccountsFormArrayFieldset: [
                {
                  id: unsafeTaxAccountIdFromString("********-53dd-417f-87e4-35c8429f72c2"),
                  rate: 0.,
                  label: "",
                  isaComptaCodeFormField: Some({
                    value: "",
                    touched: false,
                    errorMessage: None,
                  }),
                  deductibleTaxFormFields: None,
                  productsSoldFormFields: {
                    accountNumberFormField: {
                      value: "",
                      touched: false,
                      errorMessage: None,
                    },
                    labelFormField: {
                      value: "",
                      touched: false,
                      errorMessage: None,
                    },
                  },
                },
                {
                  id: unsafeTaxAccountIdFromString("13bcadec-4c3a-41ad-baa3-095d05978eda"),
                  rate: 1.,
                  label: "1",
                  isaComptaCodeFormField: Some({
                    value: "",
                    touched: false,
                    errorMessage: None,
                  }),
                  deductibleTaxFormFields: Some({
                    accountNumberFormField: {
                      value: "",
                      touched: false,
                      errorMessage: None,
                    },
                    labelFormField: {
                      value: "",
                      touched: false,
                      errorMessage: None,
                    },
                  }),
                  productsSoldFormFields: {
                    accountNumberFormField: {
                      value: "",
                      touched: false,
                      errorMessage: None,
                    },
                    labelFormField: {
                      value: "",
                      touched: false,
                      errorMessage: None,
                    },
                  },
                },
              ],
              maybePaymentsAccountsFormArrayFieldset: None,
            }),
          ),
        )
        expect(
          initialStateFromOkResult(
            ~allShopTaxes=[
              {
                id: Uuid.unsafeFromString("ac2be136-83f3-4686-ab17-f970ba76374e"),
                label: "2",
                rate: 2.,
              },
            ],
            ~vendor=Excel,
            (),
          ),
        )->toStrictEqual(
          Done(
            Ok({
              submission: NotAsked,
              initialAccountingConfiguration: None,
              vendor: Excel,
              fiscalYearOpeningMonth: January,
              breakdownOfConsumerSalesByCashRegisterDailyReport: true,
              includeCashFromIndividualsInPaymentJournal: false,
              isaComptaAccountNumberFormField: None,
              taxesAccountsFormArrayFieldset: [
                {
                  id: unsafeTaxAccountIdFromString("ac2be136-83f3-4686-ab17-f970ba76374e"),
                  rate: 2.,
                  label: "2",
                  isaComptaCodeFormField: None,
                  deductibleTaxFormFields: Some({
                    accountNumberFormField: {
                      value: "",
                      touched: false,
                      errorMessage: None,
                    },
                    labelFormField: {
                      value: "",
                      touched: false,
                      errorMessage: None,
                    },
                  }),
                  productsSoldFormFields: {
                    accountNumberFormField: {
                      value: "",
                      touched: false,
                      errorMessage: None,
                    },
                    labelFormField: {
                      value: "",
                      touched: false,
                      errorMessage: None,
                    },
                  },
                },
              ],
              maybePaymentsAccountsFormArrayFieldset: None,
            }),
          ),
        )
      },
    )
  })

  todo("initialStateDataFromOkResult")

  describe("make", () => {
    module TaxAccountFormFieldset = AccountingConfigurationPage.TaxAccountFormFieldset

    let {unsafeFromString: unsafeTaxAccountIdFromString} = module(TaxAccountFormFieldset.ID)
    let {make, initialStateFromOkResult} = module(AccountingConfigurationPage.Reducer)

    let initialDoneOkStateWithExcelVendor = initialStateFromOkResult(
      ~allShopTaxes=[
        {
          id: Uuid.unsafeFromString("0fb7d86e-1999-4144-aec9-2aa344d898f1"),
          label: "",
          rate: 1.,
        },
      ],
      ~vendor=Excel,
      (),
    )
    let initialDoneOkStateWithIsaComptaVendor = initialStateFromOkResult(
      ~allShopTaxes=[
        {
          id: Uuid.unsafeFromString("50345dcb-3be5-4ebb-89f4-fd8dde54cfe3"),
          label: "",
          rate: 1.,
        },
      ],
      ~vendor=IsaCompta,
      (),
    )

    todo("AsyncDataDoneOk(_)")
    todo("AsyncDataDoneError")
    todo("SubmitButtonClicked")
    todo("SubmissionSucceeded")
    todo("SubmissionFailed(_)")
    todo("NotificationBannerCloseButtonClicked")
    todo("AutoCompleteWithStandardTaxesAccounts")
    todo("FiscalYearOpeningMonthChanged(_)")
    todo("IsaComptaAccountNumberFormFieldChanged(_)")
    todo("IsaComptaAccountNumberFormFieldFocused")
    todo("BreakdownOfConsumerSalesByCashRegisterDailyReportChanged(_)")
    todo("TaxAccountFormFieldChanged(_,_,_)")
    todo("TaxAccountFormFieldBlured(_,_)")
    todo("PaymentsAccountsFormArrayFieldsetToggled")
    todo("AutocompleteBasicPaymentsAccounts")
    todo("PaymentsAccountsFormFieldsetRemoved")
    todo("PaymentAccountFormFieldChanged")
    todo("PaymentAccountFormFieldBlured")
    todo("PaymentAccountPaymentMethodChanged")

    test(
      "PaymentsAccountsFormFieldsetAdded",
      () => {
        module PaymentAccountFormFieldset = AccountingConfigurationPage.PaymentAccountFormFieldset
        let intialState = initialDoneOkStateWithExcelVendor->AsyncResult.mapOk(
          result => {
            ...result,
            maybePaymentsAccountsFormArrayFieldset: Some([
              {
                paymentMethod: PaymentMethod.Amex,
                accountNumberFormField: {
                  value: "the value",
                  touched: true,
                  errorMessage: Some("Please fulfill this field."),
                },
                accountLabelFormField: {
                  value: "",
                  touched: false,
                  errorMessage: Some("Please fulfill this field."),
                },
                isaComptaJournalCodeFormField: None,
              },
            ]),
          },
        )
        expect(make(intialState, PaymentsAccountsFormFieldsetAdded))->toStrictEqual(
          Done(
            Ok({
              submission: NotAsked,
              initialAccountingConfiguration: None,
              vendor: Excel,
              fiscalYearOpeningMonth: January,
              breakdownOfConsumerSalesByCashRegisterDailyReport: true,
              includeCashFromIndividualsInPaymentJournal: false,
              isaComptaAccountNumberFormField: None,
              taxesAccountsFormArrayFieldset: [
                {
                  id: unsafeTaxAccountIdFromString("0fb7d86e-1999-4144-aec9-2aa344d898f1"),
                  rate: 1.,
                  label: "",
                  isaComptaCodeFormField: None,
                  deductibleTaxFormFields: Some({
                    accountNumberFormField: {
                      value: "",
                      touched: false,
                      errorMessage: Some("Please fulfill this field."),
                    },
                    labelFormField: {
                      value: "",
                      touched: false,
                      errorMessage: Some("Please fulfill this field."),
                    },
                  }),
                  productsSoldFormFields: {
                    accountNumberFormField: {
                      value: "",
                      touched: false,
                      errorMessage: Some("Please fulfill this field."),
                    },
                    labelFormField: {
                      value: "",
                      touched: false,
                      errorMessage: Some("Please fulfill this field."),
                    },
                  },
                },
              ],
              maybePaymentsAccountsFormArrayFieldset: Some([
                {
                  paymentMethod: PaymentMethod.Amex,
                  accountNumberFormField: {
                    value: "the value",
                    touched: true,
                    errorMessage: None,
                  },
                  accountLabelFormField: {
                    value: "",
                    touched: false,
                    errorMessage: Some("Please fulfill this field."),
                  },
                  isaComptaJournalCodeFormField: None,
                },
                {
                  paymentMethod: PaymentMethod.ContactlessAmex,
                  accountNumberFormField: {
                    value: "",
                    touched: false,
                    errorMessage: Some("Please fulfill this field."),
                  },
                  accountLabelFormField: {
                    value: "Payment Amex contactless",
                    touched: false,
                    errorMessage: None,
                  },
                  isaComptaJournalCodeFormField: None,
                },
              ]),
            }),
          ),
        )
        let intialState = initialDoneOkStateWithIsaComptaVendor->AsyncResult.mapOk(
          result => {
            ...result,
            maybePaymentsAccountsFormArrayFieldset: Some(
              PaymentMethod.values->Array.map(
                value => {
                  PaymentAccountFormFieldset.paymentMethod: value,
                  accountNumberFormField: {
                    value: "",
                    touched: false,
                    errorMessage: None,
                  },
                  accountLabelFormField: {
                    value: "",
                    touched: false,
                    errorMessage: None,
                  },
                  isaComptaJournalCodeFormField: Some({
                    value: "",
                    touched: false,
                    errorMessage: None,
                  }),
                },
              ),
            ),
          },
        )
        expect(make(intialState, PaymentsAccountsFormFieldsetAdded))->toStrictEqual(
          Done(
            Ok({
              submission: NotAsked,
              initialAccountingConfiguration: None,
              vendor: IsaCompta,
              fiscalYearOpeningMonth: January,
              breakdownOfConsumerSalesByCashRegisterDailyReport: true,
              includeCashFromIndividualsInPaymentJournal: false,
              isaComptaAccountNumberFormField: Some({
                value: "",
                touched: false,
                errorMessage: Some("Please fulfill this field."),
              }),
              taxesAccountsFormArrayFieldset: [
                {
                  id: unsafeTaxAccountIdFromString("50345dcb-3be5-4ebb-89f4-fd8dde54cfe3"),
                  rate: 1.,
                  label: "",
                  isaComptaCodeFormField: Some({
                    value: "",
                    touched: false,
                    errorMessage: Some("Please fulfill this field."),
                  }),
                  deductibleTaxFormFields: Some({
                    accountNumberFormField: {
                      value: "",
                      touched: false,
                      errorMessage: Some("Please fulfill this field."),
                    },
                    labelFormField: {
                      value: "",
                      touched: false,
                      errorMessage: Some("Please fulfill this field."),
                    },
                  }),
                  productsSoldFormFields: {
                    accountNumberFormField: {
                      value: "",
                      touched: false,
                      errorMessage: Some("Please fulfill this field."),
                    },
                    labelFormField: {
                      value: "",
                      touched: false,
                      errorMessage: Some("Please fulfill this field."),
                    },
                  },
                },
              ],
              maybePaymentsAccountsFormArrayFieldset: Some(
                PaymentMethod.values->Array.map(
                  value => {
                    PaymentAccountFormFieldset.paymentMethod: value,
                    accountNumberFormField: {
                      value: "",
                      touched: false,
                      errorMessage: Some("Please fulfill this field."),
                    },
                    accountLabelFormField: {
                      value: "",
                      touched: false,
                      errorMessage: Some("Please fulfill this field."),
                    },
                    isaComptaJournalCodeFormField: Some({
                      value: "",
                      touched: false,
                      errorMessage: Some("Please fulfill this field."),
                    }),
                  },
                ),
              ),
            }),
          ),
        )
      },
    )

    test(
      "VendorChanged",
      () => {
        expect(make(initialDoneOkStateWithExcelVendor, VendorChanged(Excel)))->toStrictEqual(
          Done(
            Ok({
              submission: NotAsked,
              initialAccountingConfiguration: None,
              vendor: Excel,
              fiscalYearOpeningMonth: January,
              breakdownOfConsumerSalesByCashRegisterDailyReport: true,
              includeCashFromIndividualsInPaymentJournal: false,
              isaComptaAccountNumberFormField: None,
              taxesAccountsFormArrayFieldset: [
                {
                  id: unsafeTaxAccountIdFromString("0fb7d86e-1999-4144-aec9-2aa344d898f1"),
                  rate: 1.,
                  label: "",
                  isaComptaCodeFormField: None,
                  deductibleTaxFormFields: Some({
                    accountNumberFormField: {
                      value: "",
                      touched: false,
                      errorMessage: Some("Please fulfill this field."),
                    },
                    labelFormField: {
                      value: "",
                      touched: false,
                      errorMessage: Some("Please fulfill this field."),
                    },
                  }),
                  productsSoldFormFields: {
                    accountNumberFormField: {
                      value: "",
                      touched: false,
                      errorMessage: Some("Please fulfill this field."),
                    },
                    labelFormField: {
                      value: "",
                      touched: false,
                      errorMessage: Some("Please fulfill this field."),
                    },
                  },
                },
              ],
              maybePaymentsAccountsFormArrayFieldset: None,
            }),
          ),
        )
        expect(make(initialDoneOkStateWithExcelVendor, VendorChanged(IsaCompta)))->toStrictEqual(
          Done(
            Ok({
              submission: NotAsked,
              initialAccountingConfiguration: None,
              vendor: IsaCompta,
              fiscalYearOpeningMonth: January,
              breakdownOfConsumerSalesByCashRegisterDailyReport: true,
              includeCashFromIndividualsInPaymentJournal: false,
              isaComptaAccountNumberFormField: Some({
                value: "",
                touched: false,
                errorMessage: Some("Please fulfill this field."),
              }),
              taxesAccountsFormArrayFieldset: [
                {
                  id: unsafeTaxAccountIdFromString("0fb7d86e-1999-4144-aec9-2aa344d898f1"),
                  rate: 1.,
                  label: "",
                  isaComptaCodeFormField: Some({
                    value: "",
                    touched: false,
                    errorMessage: Some("Please fulfill this field."),
                  }),
                  deductibleTaxFormFields: Some({
                    accountNumberFormField: {
                      value: "",
                      touched: false,
                      errorMessage: Some("Please fulfill this field."),
                    },
                    labelFormField: {
                      value: "",
                      touched: false,
                      errorMessage: Some("Please fulfill this field."),
                    },
                  }),
                  productsSoldFormFields: {
                    accountNumberFormField: {
                      value: "",
                      touched: false,
                      errorMessage: Some("Please fulfill this field."),
                    },
                    labelFormField: {
                      value: "",
                      touched: false,
                      errorMessage: Some("Please fulfill this field."),
                    },
                  },
                },
              ],
              maybePaymentsAccountsFormArrayFieldset: None,
            }),
          ),
        )
        expect(make(initialDoneOkStateWithIsaComptaVendor, VendorChanged(Excel)))->toStrictEqual(
          Done(
            Ok({
              submission: NotAsked,
              initialAccountingConfiguration: None,
              vendor: Excel,
              fiscalYearOpeningMonth: January,
              breakdownOfConsumerSalesByCashRegisterDailyReport: true,
              includeCashFromIndividualsInPaymentJournal: false,
              isaComptaAccountNumberFormField: None,
              taxesAccountsFormArrayFieldset: [
                {
                  id: unsafeTaxAccountIdFromString("50345dcb-3be5-4ebb-89f4-fd8dde54cfe3"),
                  rate: 1.,
                  label: "",
                  isaComptaCodeFormField: None,
                  deductibleTaxFormFields: Some({
                    accountNumberFormField: {
                      value: "",
                      touched: false,
                      errorMessage: Some("Please fulfill this field."),
                    },
                    labelFormField: {
                      value: "",
                      touched: false,
                      errorMessage: Some("Please fulfill this field."),
                    },
                  }),
                  productsSoldFormFields: {
                    accountNumberFormField: {
                      value: "",
                      touched: false,
                      errorMessage: Some("Please fulfill this field."),
                    },
                    labelFormField: {
                      value: "",
                      touched: false,
                      errorMessage: Some("Please fulfill this field."),
                    },
                  },
                },
              ],
              maybePaymentsAccountsFormArrayFieldset: None,
            }),
          ),
        )
      },
    )
  })
})

todo("mapReducerValuesAndShopIdToAccountingConfiguration")

// Some integration tests to ensure basic interactions
// with the other components and gateway api be valuable
// at some point.
todo("Integration test")
