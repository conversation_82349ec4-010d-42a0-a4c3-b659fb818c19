open Vitest
open TestingLibraryReact

module Schema = AuthSignupPage.SignupForm.Core.Schema

describe("SignupRequest", () => {
  let {createUserRegistrationPayloadDict, decodeInvalidRequestFailure} = module(
    AuthSignupPage.SignupRequest
  )

  describe("createUserRegistrationPayloadDict", () => {
    test(
      "same shippings address than billing",
      () =>
        expect(
          createUserRegistrationPayloadDict(
            ~username="mock-username",
            ~password="mock-password",
            ~name="mock-name",
            ~email="mock-email",
            ~phoneNumber="**********",
            ~corporateName="mock-corporateName",
            ~legalRepresentative="mock-legalRepresentative",
            ~legalForm="SA",
            ~siretNumber=Some("mock-siretNumber"),
            ~rcsNumber=Some("mock-rcsNumber"),
            ~apeNafCode=Some("mock-apeNafCode"),
            ~vatNumber=Some("mock-vatNumber"),
            ~address="mock-address",
            ~city="mock-city",
            ~postalCode="mock-postalCode",
            ~country=CountryCode.FR,
            ~ibanNumber="*********************",
            ~shippingAddress="",
            ~shippingPostalCode="",
            ~shippingCity="",
            ~shippingCountry=CountryCode.FR,
            ~sameShippingAddressThanBilling=true,
            ~ipAddress="mock-ipAddress",
            ~userAgent="mock-userAgent",
            ~acceptedAt=0.,
          ),
        )->toUnsafeStrictEqual({
          "username": "mock-username",
          "password": "mock-password",
          "shop": {
            "name": "mock-name",
            "email": "mock-email",
            "phoneNumber": "**********",
            "corporateName": "mock-corporateName",
            "legalRepresentative": "mock-legalRepresentative",
            "legalForm": "SA",
            "taxCountry": "FR",
            "address": "mock-address",
            "city": "mock-city",
            "country": "France",
            "postalCode": "mock-postalCode",
            "billingAccount": {
              "corporateName": "mock-corporateName",
              "shopName": "mock-name",
              "phone": "**********",
              "email": "mock-email",
              "vatNumber": "mock-vatNumber",
              "ibanNumber": "*********************",
              "shippingAddress": {
                "address": "mock-address",
                "city": "mock-city",
                "country": "FR",
                "postalCode": "mock-postalCode",
              },
              "billingAddress": {
                "address": "mock-address",
                "city": "mock-city",
                "country": "FR",
                "postalCode": "mock-postalCode",
              },
              "sepaMandateAcceptanceDetails": {
                "ipAddress": "mock-ipAddress",
                "userAgent": "mock-userAgent",
                "acceptedAt": 0.,
              },
            },
            "siretNumber": "mock-siretNumber",
            "rcsNumber": "mock-rcsNumber",
            "apeNafCode": "mock-apeNafCode",
            "vatNumber": "mock-vatNumber",
          },
        }),
    )
    test(
      "different shipping address than billing",
      () =>
        expect(
          createUserRegistrationPayloadDict(
            ~username="mock-username",
            ~password="mock-password",
            ~name="mock-name",
            ~email="mock-email",
            ~phoneNumber="**********",
            ~corporateName="mock-corporateName",
            ~legalRepresentative="mock-legalRepresentative",
            ~legalForm="SA",
            ~siretNumber=Some("mock-siretNumber"),
            ~rcsNumber=Some("mock-rcsNumber"),
            ~apeNafCode=Some("mock-apeNafCode"),
            ~vatNumber=Some("mock-vatNumber"),
            ~address="mock-address",
            ~city="mock-city",
            ~postalCode="mock-postalCode",
            ~country=CountryCode.FR,
            ~ibanNumber="*********************",
            ~shippingAddress="mock-shipping-address",
            ~shippingPostalCode="mock-shipping-postalCode",
            ~shippingCity="mock-shipping-city",
            ~shippingCountry=CountryCode.FR,
            ~sameShippingAddressThanBilling=false,
            ~ipAddress="mock-ipAddress",
            ~userAgent="mock-userAgent",
            ~acceptedAt=0.,
          ),
        )->toUnsafeStrictEqual({
          "username": "mock-username",
          "password": "mock-password",
          "shop": {
            "name": "mock-name",
            "email": "mock-email",
            "phoneNumber": "**********",
            "corporateName": "mock-corporateName",
            "legalRepresentative": "mock-legalRepresentative",
            "legalForm": "SA",
            "taxCountry": "FR",
            "address": "mock-address",
            "city": "mock-city",
            "country": "France",
            "postalCode": "mock-postalCode",
            "billingAccount": {
              "corporateName": "mock-corporateName",
              "shopName": "mock-name",
              "phone": "**********",
              "email": "mock-email",
              "vatNumber": "mock-vatNumber",
              "ibanNumber": "*********************",
              "shippingAddress": {
                "address": "mock-shipping-address",
                "city": "mock-shipping-city",
                "country": "FR",
                "postalCode": "mock-shipping-postalCode",
              },
              "billingAddress": {
                "address": "mock-address",
                "city": "mock-city",
                "country": "FR",
                "postalCode": "mock-postalCode",
              },
              "sepaMandateAcceptanceDetails": {
                "ipAddress": "mock-ipAddress",
                "userAgent": "mock-userAgent",
                "acceptedAt": 0.,
              },
            },
            "siretNumber": "mock-siretNumber",
            "rcsNumber": "mock-rcsNumber",
            "apeNafCode": "mock-apeNafCode",
            "vatNumber": "mock-vatNumber",
          },
        }),
    )

    test(
      "strip empty siret, rcs, ape and tva numbers",
      () =>
        expect(
          createUserRegistrationPayloadDict(
            ~username="mock-username",
            ~password="mock-password",
            ~name="mock-name",
            ~email="mock-email",
            ~phoneNumber="**********",
            ~corporateName="mock-corporateName",
            ~legalRepresentative="mock-legalRepresentative",
            ~legalForm="SA",
            ~siretNumber=None,
            ~rcsNumber=None,
            ~apeNafCode=None,
            ~vatNumber=None,
            ~address="mock-address",
            ~city="mock-city",
            ~postalCode="mock-postalCode",
            ~country=CountryCode.FR,
            ~ibanNumber="*********************",
            ~shippingAddress="mock-shipping-address",
            ~shippingPostalCode="mock-shipping-postalCode",
            ~shippingCity="mock-shipping-city",
            ~shippingCountry=CountryCode.FR,
            ~sameShippingAddressThanBilling=false,
            ~ipAddress="mock-ipAddress",
            ~userAgent="mock-userAgent",
            ~acceptedAt=0.,
          ),
        )->toUnsafeStrictEqual({
          "username": "mock-username",
          "password": "mock-password",
          "shop": {
            "name": "mock-name",
            "email": "mock-email",
            "phoneNumber": "**********",
            "corporateName": "mock-corporateName",
            "legalRepresentative": "mock-legalRepresentative",
            "legalForm": "SA",
            "taxCountry": "FR",
            "address": "mock-address",
            "city": "mock-city",
            "country": "France",
            "postalCode": "mock-postalCode",
            "billingAccount": {
              "corporateName": "mock-corporateName",
              "shopName": "mock-name",
              "phone": "**********",
              "email": "mock-email",
              "vatNumber": "",
              "ibanNumber": "*********************",
              "shippingAddress": {
                "address": "mock-shipping-address",
                "city": "mock-shipping-city",
                "country": "FR",
                "postalCode": "mock-shipping-postalCode",
              },
              "billingAddress": {
                "address": "mock-address",
                "city": "mock-city",
                "country": "FR",
                "postalCode": "mock-postalCode",
              },
              "sepaMandateAcceptanceDetails": {
                "ipAddress": "mock-ipAddress",
                "userAgent": "mock-userAgent",
                "acceptedAt": 0.,
              },
            },
          },
        }),
    )

    test(
      "decodeInvalidRequestFailure",
      () => {
        let serverFailure = {
          Request.kind: "DuplicateUserUsername",
          message: "",
          data: None,
        }
        expect(serverFailure->decodeInvalidRequestFailure)->toStrictEqual(DuplicateUserUsername)

        let serverFailure = {
          Request.kind: "InvalidVatNumber",
          message: "",
          data: None,
        }
        expect(serverFailure->decodeInvalidRequestFailure)->toStrictEqual(InvalidVatNumber)

        let serverFailure = {
          Request.kind: "InvalidIban",
          message: "",
          data: None,
        }
        expect(serverFailure->decodeInvalidRequestFailure)->toStrictEqual(InvalidIban)

        let serverFailure = {
          Request.kind: "Unknown",
          message: "",
          data: None,
        }
        expect(serverFailure->decodeInvalidRequestFailure)->toStrictEqual(Unknown)
      },
    )
  })
})

test("stripEmptyString", () => {
  expect(AuthSignupPage.stripEmptyString(""))->toStrictEqual(None)
  expect(AuthSignupPage.stripEmptyString("Something"))->toStrictEqual(Some("Something"))
})

describe("AuthSignupForm", () => {
  let schema = AuthSignupPage.schema
  let mockState = (
    ~username="<EMAIL>",
    ~password="MockStrongp1SSword",
    ~passwordConfirmation="MockStrongp1SSword",
    ~name="mock-name",
    ~email="<EMAIL>",
    ~phoneNumber="0711111111",
    ~corporateName="mock-corporateName",
    ~legalRepresentative="mock-legalRepresentative",
    ~legalForm="SAS",
    ~siretNumber="mock-siretNumber",
    ~rcsNumber="mock-rcsNumber",
    ~apeNafCode="mock-apeNafCode",
    ~vatNumber="mock-vatNumber",
    ~address="mock-address",
    ~city="mock-city",
    ~postalCode="mock-postalCode",
    ~country=CountryCode.BE,
    ~ibanNumber="*********************",
    ~shippingAddress="",
    ~shippingPostalCode="",
    ~shippingCity="",
    ~shippingCountry=CountryCode.BE,
    ~sameShippingAddressThanBilling=true,
    (),
  ) => {
    AuthSignupPage.SignupFormLenses.username,
    password,
    passwordConfirmation,
    name,
    email,
    phoneNumber,
    corporateName,
    legalRepresentative,
    legalForm,
    siretNumber,
    rcsNumber,
    apeNafCode,
    vatNumber,
    address,
    city,
    postalCode,
    country,
    ibanNumber,
    shippingAddress,
    shippingPostalCode,
    shippingCity,
    shippingCountry,
    sameShippingAddressThanBilling,
  }

  it("should return no errors when all mandatory fields are well filled", () => {
    let values = mockState()
    expect(Schema.validate(~schema, ~values))->toStrictEqual(Ok())
  })

  it("should return an error on all noncorrect fields", () => {
    let values = mockState(
      ~username="",
      ~password="",
      ~passwordConfirmation="",
      ~name="",
      ~email="",
      ~phoneNumber="",
      ~corporateName="",
      ~legalRepresentative="",
      ~legalForm="SAS",
      ~siretNumber="mock-siretNumber",
      ~rcsNumber="mock-rcsNumber",
      ~apeNafCode="mock-apeNafCode",
      ~vatNumber="mock-vatNumber",
      ~address="",
      ~city="",
      ~postalCode="",
      ~country=CountryCode.BE,
      ~ibanNumber="",
      ~shippingAddress="",
      ~shippingPostalCode="",
      ~shippingCity="",
      ~shippingCountry=CountryCode.BE,
      (),
    )

    expect(Schema.validate(~schema, ~values))->toStrictEqual(
      Error([
        (Field(Username), "Invalid email address."),
        (Field(Password), "Invalid password."),
        (Field(PasswordConfirmation), "Please fulfill this field."),
        (Field(Name), "Please fulfill this field."),
        (Field(Email), "Invalid email address."),
        (Field(PhoneNumber), "This value is not a valid phone number."),
        (
          Field(CorporateName),
          "The corporate name is invalid. Please provide the full name of the company.",
        ),
        (Field(Address), "Please fulfill this field."),
        (Field(PostalCode), "Please fulfill this field."),
        (Field(City), "Please fulfill this field."),
        (Field(IbanNumber), "Please fulfill this field."),
        (Field(IbanNumber), "The IBAN number is not valid."),
        (
          Field(LegalRepresentative),
          "The legal representative is not valid, full name must be given.",
        ),
      ]),
    )
  })

  it("should return an error when password confirmation does not match password", () => {
    let values = mockState(
      ~username="<EMAIL>",
      ~password="OIJoijAZ233",
      ~passwordConfirmation="OIJoijAadzodjaoij",
      ~name="mock-name",
      ~email="<EMAIL>",
      ~phoneNumber="**********",
      ~corporateName="mock-corporateName",
      ~legalRepresentative="mock-legalRepresentative",
      ~legalForm="SAS",
      ~siretNumber="mock-siretNumber",
      ~rcsNumber="mock-rcsNumber",
      ~apeNafCode="mock-apeNafCode",
      ~vatNumber="mock-vatNumber",
      ~address="mock-address",
      ~city="mock-city",
      ~postalCode="mock-postalCode",
      ~country=CountryCode.BE,
      ~ibanNumber="*********************",
      ~shippingAddress="",
      ~shippingPostalCode="",
      ~shippingCity="",
      ~shippingCountry=CountryCode.BE,
      (),
    )

    expect(Schema.validate(~schema, ~values))->toStrictEqual(
      Error([(Field(PasswordConfirmation), "Password and its confirmation must be identical")]),
    )
  })

  it("should fail when corporate name is not valid", () => {
    let values = mockState(
      ~username="<EMAIL>",
      ~password="OIJoijAZ233",
      ~passwordConfirmation="OIJoijAZ233",
      ~name="mock-name",
      ~email="<EMAIL>",
      ~phoneNumber="**********",
      ~corporateName="SARL ab",
      ~legalRepresentative="mock-legalRepresentative",
      ~legalForm="SAS",
      ~siretNumber="mock-siretNumber",
      ~rcsNumber="mock-rcsNumber",
      ~apeNafCode="mock-apeNafCode",
      ~vatNumber="mock-vatNumber",
      ~address="mock-address",
      ~city="mock-city",
      ~postalCode="mock-postalCode",
      ~country=CountryCode.BE,
      ~ibanNumber="*********************",
      ~shippingAddress="",
      ~shippingPostalCode="",
      ~shippingCity="",
      ~shippingCountry=CountryCode.BE,
      (),
    )

    expect(Schema.validate(~schema, ~values))->toStrictEqual(
      Error([
        (
          Field(CorporateName),
          "The corporate name is invalid. Please provide the full name of the company.",
        ),
      ]),
    )
  })

  it(
    "should return an error when sameShippingAddressThanBilling is false and shipping address is empty",
    () => {
      let values = mockState(
        ~shippingAddress="",
        ~shippingPostalCode="",
        ~shippingCity="",
        ~shippingCountry=CountryCode.BE,
        ~sameShippingAddressThanBilling=false,
        (),
      )

      expect(Schema.validate(~schema, ~values))->toStrictEqual(
        Error([
          (Field(ShippingAddress), "Please fulfill this field."),
          (Field(ShippingPostalCode), "Please fulfill this field."),
          (Field(ShippingCity), "Please fulfill this field."),
        ]),
      )
    },
  )
})

let fillRequiredFieldsAndSubmitForm = async () => {
  let userEvent = TestingLibraryEvent.setup()

  let inputEmail = screen->getByLabelTextExn("Account email")
  await userEvent->TestingLibraryEvent.type_(inputEmail, "<EMAIL>")

  let inputPassword = screen->getByLabelTextExn("Password")
  await userEvent->TestingLibraryEvent.type_(inputPassword, "OIAKZOJAZoijazhjzbdf9829832")

  let inputPassword = screen->getByLabelTextExn("Password confirmation")
  await userEvent->TestingLibraryEvent.type_(inputPassword, "OIAKZOJAZoijazhjzbdf9829832")

  let shopName = screen->getByLabelTextExn("Shop name")
  await userEvent->TestingLibraryEvent.type_(shopName, "Shop name")

  let shopEmail = screen->getByLabelTextExn("Shop email")
  await userEvent->TestingLibraryEvent.type_(shopEmail, "<EMAIL>")

  let inputPhone = screen->getByLabelTextExn("Shop phone")
  await userEvent->TestingLibraryEvent.type_(inputPhone, "**********")

  let corporateName = screen->getByLabelTextExn("Corporate name")
  await userEvent->TestingLibraryEvent.type_(corporateName, "My shop")

  let legalRepresentative = screen->getByLabelTextExn("Legal representative")
  await userEvent->TestingLibraryEvent.type_(legalRepresentative, "John Doe")

  let address = screen->getByLabelTextExn("Address")
  await userEvent->TestingLibraryEvent.type_(address, "mock billing address")

  let postalCode = screen->getByLabelTextExn("Postal code")
  await userEvent->TestingLibraryEvent.type_(postalCode, "mock billing postal code")

  let city = screen->getByLabelTextExn("City")
  await userEvent->TestingLibraryEvent.type_(city, "mock billing city")

  let inputIBAN = screen->getByLabelTextExn("IBAN number")
  await userEvent->TestingLibraryEvent.type_(inputIBAN, "*********************")

  let vatNumber = screen->getByLabelTextExn("VAT number")
  await userEvent->TestingLibraryEvent.type_(vatNumber, "FR11111111111")

  let button =
    screen->getAllByRoleWithOptionsExn(#button, {name: "Create your account"})->Array.getExn(0)
  expect(button)->toBeVisible

  await userEvent->TestingLibraryEvent.click(button)
}

describe("AuthSignupPage", () => {
  module TestableAuthSignupPage = {
    @react.component
    let make = (
      ~signupRequest: AuthSignupPage.SignupRequest.makeT,
      ~ipAddressRequest,
      ~history=?,
    ) =>
      <Providers ?history>
        <AuthSignupPage signupRequest ipAddressRequest />
      </Providers>
  }

  let mockUpdateRequest = (~futureResult) => {
    (
      ~username as _,
      ~password as _,
      ~name as _,
      ~email as _,
      ~phoneNumber as _,
      ~corporateName as _,
      ~legalRepresentative as _,
      ~legalForm as _,
      ~siretNumber as _,
      ~rcsNumber as _,
      ~apeNafCode as _,
      ~vatNumber as _,
      ~address as _,
      ~postalCode as _,
      ~city as _,
      ~country as _,
      ~ibanNumber as _,
      ~shippingAddress as _,
      ~shippingPostalCode as _,
      ~shippingCity as _,
      ~shippingCountry as _,
      ~sameShippingAddressThanBilling as _,
      ~ipAddress as _,
      ~userAgent as _,
      ~acceptedAt as _,
    ) => Future.makePure(resolve => resolve(futureResult()))
  }

  let mockIpRequest = (~futureResult) => {
    () => Future.makePure(resolve => resolve(futureResult()))
  }

  itPromise("should display BCE number for Belgium, RCS otherwise", async () => {
    let userEvent = TestingLibraryEvent.setup()
    let signupRequest = mockUpdateRequest(~futureResult=() => Ok())
    let ipAddressRequest = () => Future.makePure(resolve => resolve(Ok("0.0.0.0")))

    let _ = <TestableAuthSignupPage signupRequest ipAddressRequest />->render

    expect(screen->getByTextExn("RCS number"))->toBeVisible

    let rcsNumber = screen->getByTextExn("RCS number")
    expect(rcsNumber)->toBeVisible

    let siretNumber = screen->getByTextExn("SIRET number")
    expect(siretNumber)->toBeVisible

    let countrySelect = screen->getByLabelTextExn("Country")
    expect(countrySelect)->toBeVisible

    await userEvent->TestingLibraryEvent.click(countrySelect)
    await waitFor(() => expect(screen->getByTextExn("Belgium"))->toBeVisible)
    let belgium = screen->getByTextExn("Belgium")
    await userEvent->TestingLibraryEvent.click(belgium)

    expect(rcsNumber)->Vitest.not->toBeVisible
    expect(siretNumber)->Vitest.not->toBeVisible
    expect(screen->getByTextExn("BCE number"))->toBeVisible

    await userEvent->TestingLibraryEvent.click(countrySelect)
    await waitFor(() => expect(screen->getByTextExn("Luxembourg"))->toBeVisible)
    let belgium = screen->getByTextExn("Luxembourg")
    await userEvent->TestingLibraryEvent.click(belgium)

    expect(rcsNumber)->Vitest.not->toBeVisible
    expect(siretNumber)->Vitest.not->toBeVisible
    expect(screen->getByTextExn("RCSL number"))->toBeVisible
  })

  itPromiseTimeout(
    "should display an error when form validation fails",
    async () => {
      let userEvent = TestingLibraryEvent.setup()

      let requestResult = fn1(() => Ok())
      let signupRequest = mockUpdateRequest(~futureResult=requestResult->fn)

      let ipAddressRequest = () => Future.makePure(resolve => resolve(Ok("0.0.0.0")))

      let _ = <TestableAuthSignupPage signupRequest ipAddressRequest />->render
      let nonValidPassword = "password"

      let inputPassword = screen->getByLabelTextExn("Password")
      expect(inputPassword)->toBeVisible

      await userEvent->TestingLibraryEvent.type_(inputPassword, nonValidPassword)

      let button =
        screen->getAllByRoleWithOptionsExn(#button, {name: "Create your account"})->Array.getExn(0)
      expect(button)->toBeVisible

      await userEvent->TestingLibraryEvent.click(button)

      await waitFor(() => {
        expect(
          screen->getByTextExn(
            "There are some errors in the form, please correct them before trying to send it again.",
          ),
        )->toBeVisible
      })
    },
    30000,
  )

  itPromiseTimeout(
    "should fail if ip address fetch failure",
    async () => {
      let history = History.createMemoryHistory()
      let requestResult = fn1(() => Ok())
      let signupRequest = mockUpdateRequest(~futureResult=requestResult->fn)

      let ipRequestResult = fn1(() => Error())
      let ipAddressRequest = mockIpRequest(~futureResult=ipRequestResult->fn)

      let _ = <TestableAuthSignupPage history signupRequest ipAddressRequest />->render

      await fillRequiredFieldsAndSubmitForm()

      await waitFor(() => {
        expect(
          screen->getByTextExn(
            "An unexpected error occured. Please try again or contact the support.",
          ),
        )->toBeVisible
      })
    },
    30000,
  )

  itPromiseTimeout(
    "should display error when the request fails",
    async () => {
      let signupError = AuthSignupPage.SignupRequest.Unknown
      let requestResult = fn1(() => Error(Some(signupError)))
      let signupRequest = mockUpdateRequest(~futureResult=requestResult->fn)

      let ipAddressRequest = () => Future.makePure(resolve => resolve(Ok("0.0.0.0")))

      let _ = <TestableAuthSignupPage signupRequest ipAddressRequest />->render

      await fillRequiredFieldsAndSubmitForm()

      await waitFor(() => {
        expect(
          screen->getByTextExn(
            "An unexpected error occured. Please try again or contact the support.",
          ),
        )->toBeVisible
      })
    },
    30000,
  )

  itPromiseTimeout(
    "should display success when request succeeeds",
    async () => {
      let history = History.createMemoryHistory()
      let requestResult = fn1(() => Ok())
      let signupRequest = mockUpdateRequest(~futureResult=requestResult->fn)

      let ipRequestResult = fn1(() => Ok("0.0.0.0"))
      let ipAddressRequest = mockIpRequest(~futureResult=ipRequestResult->fn)

      let _ = <TestableAuthSignupPage history signupRequest ipAddressRequest />->render

      await fillRequiredFieldsAndSubmitForm()

      await waitFor(() => {
        expect(history.location.pathname)->toBe(AuthRoutes.signupSuccess)
      })
    },
    30000,
  )
})
