open Vitest
open TestingLibraryReact

describe("CustomersExportRequest", () => {
  test("encodeRequestBodyJson", () => {
    let {encodeRequestBodyJson} = module(CustomerListPage.CustomersExportRequest)

    expect(encodeRequestBodyJson(~shopIds=["mock-shop-id"], ()))->toUnsafeStrictEqual({
      "shopIds": ["mock-shop-id"],
    })
    expect(encodeRequestBodyJson(~shopIds=[], ()))->toUnsafeStrictEqual({
      "shopIds": [],
    })
  })
})

describe("CustomerListPage", () => {
  module TestableCustomerListPage = {
    @react.component
    let make = (~request, ~history=?) =>
      <Providers ?history>
        <CustomerListPage request />
      </Providers>
  }

  itPromise("should display error on request failure", async () => {
    let userEvent = TestingLibraryEvent.setup()

    let requestResult = fn1(() => Error(Request.UnexpectedServerError))
    let request = (~shopIds as _) => Future.makePure(resolve => resolve(requestResult->fn()))

    let _ = <TestableCustomerListPage request />->render

    let button =
      screen->getAllByRoleWithOptionsExn(#button, {name: "Export customer file"})->Array.getExn(0)
    expect(button)->toBeVisible

    await userEvent->TestingLibraryEvent.click(button)

    await waitFor(
      () => {
        expect(
          screen->getByTextExn(
            "An issue occurred when attempting to download the customers file. Wino did not respond, please reiterate your request.",
          ),
        )->toBeVisible
      },
    )
  })
})
