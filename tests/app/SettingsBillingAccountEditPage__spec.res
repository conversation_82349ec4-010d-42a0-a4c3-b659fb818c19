open Vitest
open TestingLibraryReact

describe("BillingAccountEditRequest", () => {
  module BillingAccountEditRequest = SettingsBillingAccountEditPage.BillingAccountEditRequest

  let {encodeBody, decodeInvalidRequestFailure} = module(BillingAccountEditRequest)

  describe("createUserRegistrationPayloadDict", () => {
    test(
      "same shipping address than billing",
      () =>
        expect(
          encodeBody(
            ~shopId="mock-shopId",
            ~corporateName="mock-name",
            ~shopName="",
            ~email="mock-email",
            ~phone="**********",
            ~vatNumber="mock-tvaNumber",
            ~address="mock-address",
            ~city="mock-city",
            ~postalCode="mock-postalCode",
            ~country=CountryCode.FR,
            ~shippingAddress="",
            ~shippingPostalCode="",
            ~shippingCity="",
            ~shippingCountry="",
            ~sameShippingAddressThanBilling=true,
          )->Json.encodeDict,
        )->toUnsafeStrictEqual({
          "shopId": "mock-shopId",
          "corporateName": "mock-name",
          "shopName": "",
          "email": "mock-email",
          "phone": "**********",
          "vatNumber": "mock-tvaNumber",
          "billingAddress": {
            "address": "mock-address",
            "city": "mock-city",
            "country": "FR",
            "postalCode": "mock-postalCode",
          },
          "shippingAddress": {
            "address": "mock-address",
            "city": "mock-city",
            "country": "FR",
            "postalCode": "mock-postalCode",
          },
        }),
    )

    test(
      "different shipping address than billing",
      () =>
        expect(
          encodeBody(
            ~shopId="mock-shopId",
            ~corporateName="mock-name",
            ~shopName="",
            ~email="mock-email",
            ~phone="**********",
            ~vatNumber="mock-tvaNumber",
            ~address="mock-address",
            ~city="mock-city",
            ~postalCode="mock-postalCode",
            ~country=CountryCode.FR,
            ~shippingAddress="mock-shipping",
            ~shippingPostalCode="",
            ~shippingCity="",
            ~shippingCountry="",
            ~sameShippingAddressThanBilling=false,
          )->Json.encodeDict,
        )->toUnsafeStrictEqual({
          "shopId": "mock-shopId",
          "corporateName": "mock-name",
          "shopName": "",
          "email": "mock-email",
          "phone": "**********",
          "vatNumber": "mock-tvaNumber",
          "billingAddress": {
            "address": "mock-address",
            "city": "mock-city",
            "country": "FR",
            "postalCode": "mock-postalCode",
          },
          "shippingAddress": {
            "address": "mock-shipping",
            "city": "",
            "country": "",
            "postalCode": "",
          },
        }),
    )

    test(
      "decodeInvalidRequestFailure",
      () => {
        let serverFailure = {
          Request.kind: "DuplicateUserUsername",
          message: "",
          data: None,
        }
        expect(serverFailure->decodeInvalidRequestFailure)->toStrictEqual(DuplicateUserUsername)

        let serverFailure = {
          Request.kind: "InvalidVatNumber",
          message: "",
          data: None,
        }
        expect(serverFailure->decodeInvalidRequestFailure)->toStrictEqual(InvalidVatNumber)

        let serverFailure = {
          Request.kind: "NotFoundBillingAccount",
          message: "",
          data: None,
        }
        expect(serverFailure->decodeInvalidRequestFailure)->toStrictEqual(NotFoundBillingAccount)

        let serverFailure = {
          Request.kind: "Unknown",
          message: "",
          data: None,
        }
        expect(serverFailure->decodeInvalidRequestFailure)->toStrictEqual(Unknown)
      },
    )
  })
})

describe("BillingAccountForm", () => {
  module BillingAccountForm = SettingsBillingAccountEditPage.BillingAccountForm

  let schema = SettingsBillingAccountEditPage.schema

  let mockState = (
    ~corporateName="mock-corporate-name",
    ~shopName="mock-shop-name",
    ~email="<EMAIL>",
    ~phone="**********",
    ~vatNumber="mock-tvaNumber",
    ~address="mock-address",
    ~city="mock-city",
    ~postalCode="mock-postalCode",
    ~country=CountryCode.BE,
    ~shippingAddress="",
    ~shippingPostalCode="",
    ~shippingCity="",
    ~shippingCountry="BE",
    ~sameShippingAddressThanBilling=true,
    (),
  ) => {
    SettingsBillingAccountEditPage.BillingAccountFormLenses.corporateName,
    shopName,
    email,
    phone,
    vatNumber,
    address,
    city,
    postalCode,
    country,
    shippingAddress,
    shippingPostalCode,
    shippingCity,
    shippingCountry,
    sameShippingAddressThanBilling,
  }

  it("should return no errors when all mandatory fields are well filled", () => {
    let values = mockState()
    expect(BillingAccountForm.validate(~schema, ~values))->toStrictEqual(Ok())
  })

  it("should return an error on all noncorrect fields", () => {
    let values = mockState(
      ~corporateName="",
      ~shopName="",
      ~email="",
      ~phone="",
      ~vatNumber="",
      ~address="",
      ~city="",
      ~postalCode="",
      ~country=CountryCode.BE,
      ~shippingAddress="",
      ~shippingPostalCode="",
      ~shippingCity="",
      ~shippingCountry="",
      (),
    )

    expect(BillingAccountForm.validate(~schema, ~values))->toStrictEqual(
      Error([
        (
          Field(CorporateName),
          "The corporate name is invalid. Please provide the full name of the company.",
        ),
        (Field(ShopName), "Please fulfill this field."),
        (Field(Email), "Invalid email address."),
        (Field(Phone), "This value is not a valid phone number."),
        (Field(Address), "Please fulfill this field."),
        (Field(PostalCode), "Please fulfill this field."),
        (Field(City), "Please fulfill this field."),
      ]),
    )
  })

  it(
    "should return an error when sameShippingAddressThanBilling is false and shipping address is empty",
    () => {
      let values = mockState(
        ~corporateName="",
        ~shopName="",
        ~email="",
        ~phone="",
        ~vatNumber="",
        ~address="",
        ~city="",
        ~postalCode="",
        ~country=CountryCode.BE,
        ~shippingAddress="",
        ~shippingPostalCode="",
        ~shippingCity="",
        ~shippingCountry="",
        ~sameShippingAddressThanBilling=false,
        (),
      )

      expect(BillingAccountForm.validate(~schema, ~values))->toStrictEqual(
        Error([
          (
            Field(CorporateName),
            "The corporate name is invalid. Please provide the full name of the company.",
          ),
          (Field(ShopName), "Please fulfill this field."),
          (Field(Email), "Invalid email address."),
          (Field(Phone), "This value is not a valid phone number."),
          (Field(Address), "Please fulfill this field."),
          (Field(PostalCode), "Please fulfill this field."),
          (Field(City), "Please fulfill this field."),
          (Field(ShippingAddress), "Please fulfill this field."),
          (Field(ShippingPostalCode), "Please fulfill this field."),
          (Field(ShippingCity), "Please fulfill this field."),
          (Field(ShippingCountry), "Please fulfill this field."),
        ]),
      )
    },
  )
})

let fillRequiredFieldsAndSubmitForm = async () => {
  let userEvent = TestingLibraryEvent.setup()

  let corporateName = screen->getByLabelTextExn("Corporate name")
  await userEvent->TestingLibraryEvent.type_(corporateName, "Corporate name")

  let shopName = screen->getByLabelTextExn("Shop name")
  await userEvent->TestingLibraryEvent.type_(shopName, "Shop name")

  let billingEmail = screen->getByLabelTextExn("Billing email")
  await userEvent->TestingLibraryEvent.type_(billingEmail, "<EMAIL>")

  let inputPhone = screen->getByLabelTextExn("Shop phone")
  await userEvent->TestingLibraryEvent.type_(inputPhone, "0683500056")

  let tvaNumber = screen->getByLabelTextExn("VAT number")
  await userEvent->TestingLibraryEvent.type_(tvaNumber, "FR11111111111")

  let address = screen->getByLabelTextExn("Address")
  await userEvent->TestingLibraryEvent.type_(address, "mock billing address")

  let postalCode = screen->getByLabelTextExn("Postal code")
  await userEvent->TestingLibraryEvent.type_(postalCode, "mock billing postal code")

  let city = screen->getByLabelTextExn("City")
  await userEvent->TestingLibraryEvent.type_(city, "mock billing city")

  let button = screen->getAllByRoleWithOptionsExn(#button, {name: "Save"})->Array.getExn(0)
  expect(button)->toBeVisible

  await userEvent->TestingLibraryEvent.click(button)
}

describe("SettingsBillingAccountEditPage", () => {
  module TestableSettingsBillingAccountEditPage = {
    @react.component
    let make = (
      ~updateRequest: (
        ~shopId: string,
        ~corporateName: string,
        ~shopName: string,
        ~email: string,
        ~phone: string,
        ~vatNumber: string,
        ~address: string,
        ~postalCode: string,
        ~city: string,
        ~country: CountryCode.t,
        ~shippingAddress: string,
        ~shippingPostalCode: string,
        ~shippingCity: string,
        ~shippingCountry: string,
        ~sameShippingAddressThanBilling: bool,
      ) => Future.t<
        result<unit, option<SettingsBillingAccountEditPage.BillingAccountEditRequest.failureKind>>,
      >,
    ) =>
      <Providers>
        <SettingsBillingAccountEditPage
          activeShopId=""
          requestBillingAccountUpdate=updateRequest
          corporateName=""
          shopName=""
          email=""
          phone=""
          vatNumber=None
          billingAddress=None
          shippingAddress=None
        />
      </Providers>
  }

  let mockUpdateRequest = (~futureResult) => {
    (
      ~shopId as _,
      ~corporateName as _,
      ~shopName as _,
      ~email as _,
      ~phone as _,
      ~vatNumber as _,
      ~address as _,
      ~postalCode as _,
      ~city as _,
      ~country as _,
      ~shippingAddress as _,
      ~shippingPostalCode as _,
      ~shippingCity as _,
      ~shippingCountry as _,
      ~sameShippingAddressThanBilling as _,
    ) => Future.makePure(resolve => resolve(futureResult()))
  }

  itPromise("should display an error when form validation fails", async () => {
    let updateRequestResultError = SettingsBillingAccountEditPage.BillingAccountEditRequest.Unknown
    let updateRequestResult = fn1(() => Error(Some(updateRequestResultError)))
    let updateRequest = mockUpdateRequest(~futureResult=updateRequestResult->fn)

    let userEvent = TestingLibraryEvent.setup()

    let _ = <TestableSettingsBillingAccountEditPage updateRequest />->render

    expect(updateRequestResult)->toHaveBeenCalledTimes(0)
    let shopNameInput = screen->getByLabelTextExn("Billing email")
    expect(shopNameInput)->toBeVisible

    await userEvent->TestingLibraryEvent.type_(shopNameInput, "wrong-email")

    let button = screen->getAllByRoleWithOptionsExn(#button, {name: "Save"})->Array.getExn(0)
    expect(button)->toBeVisible

    await userEvent->TestingLibraryEvent.click(button)

    let expectedText = "There are some errors in the form, please correct them before trying to send it again."
    await waitFor(() => expect(screen->getByTextExn(expectedText))->toBeVisible)
    expect(updateRequestResult)->toHaveBeenCalledTimes(0)
  })

  itPromise("should display error when the request fails", async () => {
    let updateRequestResultError = SettingsBillingAccountEditPage.BillingAccountEditRequest.Unknown
    let updateRequestResult = fn1(() => Error(Some(updateRequestResultError)))
    let updateRequest = mockUpdateRequest(~futureResult=updateRequestResult->fn)

    let _ = <TestableSettingsBillingAccountEditPage updateRequest />->render

    expect(updateRequestResult)->toHaveBeenCalledTimes(0)

    await fillRequiredFieldsAndSubmitForm()

    let expectedText = "An unexpected error occured. Please try again or contact the support."
    await waitFor(() => expect(screen->getByTextExn(expectedText))->toBeVisible)
    expect(updateRequestResult)->toHaveBeenCalledTimes(1)
  })

  itPromise("should display success when request succeeeds", async () => {
    let updateRequestResult = fn1(() => Ok())
    let updateRequest = mockUpdateRequest(~futureResult=updateRequestResult->fn)

    let _ = <TestableSettingsBillingAccountEditPage updateRequest />->render

    expect(updateRequestResult)->toHaveBeenCalledTimes(0)

    await fillRequiredFieldsAndSubmitForm()

    let expectedText = "You have made changes to your billing information, please now verify your details in Shop settings."
    await waitFor(() => expect(screen->getByTextExn(expectedText))->toBeVisible)
    expect(updateRequestResult)->toHaveBeenCalledTimes(1)
  })
})
