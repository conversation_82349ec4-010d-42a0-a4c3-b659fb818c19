open Vitest

let {mockShop} = module(Auth__Mock)

let mockQueryVariablesFilterBy = () =>
  SupplierOrderListPage.Query.makeInputObjectInputSupplierOrdersFilter()

let mockTypename = () => ""

let mockQueryDataPageInfo = (~startCursor=?, ~endCursor=?, ()) => {
  SupplierOrderListPage.Query.startCursor,
  endCursor,
  __typename: mockTypename(),
}

let mockQueryData = (
  ~id="",
  ~ordersEdges=[],
  ~ordersPageInfo=mockQueryDataPageInfo(),
  ~ordersTotalCount=0,
  (),
) => {
  SupplierOrderListPage.Query.supplier: Some({
    id,
    orders: {
      pageInfo: ordersPageInfo,
      totalCount: ordersTotalCount,
      edges: ordersEdges,
      __typename: mockTypename(),
    },
    __typename: mockTypename(),
  }),
}

let mockRow = (~id="", ()) => {
  SupplierOrderListPage.Row.id,
  supplierId: "",
  formattedName: "",
  shopName: "",
  issueDate: Js.Date.fromFloat(0.),
  receptionDate: None,
  statuses: [],
  totalAmountExcludingTaxes: 0.,
  totalAmountIncludingTaxes: 0.,
  totalProductsQuantity: 0,
  totalProductsExpectedQuantity: 0,
}

let mockQueryDataOrdersEdge = (
  ~id="",
  ~formattedName="",
  ~shopName="",
  ~supplierCompanyName="",
  ~issueDate=Js.Date.fromFloat(0.),
  ~receptionFinishedAt=None,
  ~formattedStatus=[],
  ~totalAmountExcludingTaxes=0.,
  ~totalAmountIncludingTaxes=0.,
  ~totalProductsQuantity=0,
  ~totalProductsExpectedQuantity=0,
  (),
) => {
  SupplierOrderListPage.Query.node: {
    id,
    formattedName,
    shopName,
    supplierCompanyName,
    issueDate,
    receptionFinishedAt,
    formattedStatus,
    totalAmountExcludingTaxes,
    totalAmountIncludingTaxes,
    totalProductsQuantity,
    totalProductsExpectedQuantity,
    __typename: mockTypename(),
  },
  __typename: mockTypename(),
}

let mockQueryVariables = (~supplierId="", ()) => {
  SupplierOrderListPage.Query.supplierId,
  first: None,
  last: None,
  before: None,
  after: None,
  filterBy: None,
  search: None,
}

let mockStateFilters = (~status=?, ~issueDateRange=?, ~receptionDateRange=?, ()) => {
  SupplierOrderListPage.Filters.status,
  issueDateRange,
  receptionDateRange,
}

let mockState = (
  ~filters=mockStateFilters(),
  ~currentPage=1,
  ~previousPage=-1,
  ~searchQuery=?,
  (),
) => {
  Scaffold.filters,
  currentPage,
  previousPage,
  searchQuery,
  connectionArguments: {first: 10},
}

describe("Scaffolded", () => {
  todo("useFiltersJsonCodec")

  test("makeQueryVariables", () => {
    let {makeQueryVariables} = module(SupplierOrderListPage.Scaffolded)

    expect(makeQueryVariables(mockQueryVariables(), ~connectionArguments={}, ()))->toStrictEqual({
      SupplierOrderListPage.Query.supplierId: "",
      first: None,
      last: None,
      before: None,
      after: None,
      filterBy: None,
      search: None,
    })

    expect(
      makeQueryVariables(
        mockQueryVariables(~supplierId="supplierId", ()),
        ~connectionArguments={
          first: 1,
          last: 2,
          before: "before",
          after: "after",
        },
        ~search="search",
        ~filterBy=mockQueryVariablesFilterBy(),
        (),
      ),
    )->toStrictEqual({
      SupplierOrderListPage.Query.supplierId: "supplierId",
      first: Some(1),
      last: Some(2),
      before: Some("before"),
      after: Some("after"),
      search: Some("search"),
      filterBy: Some(SupplierOrderListPage.Query.makeInputObjectInputSupplierOrdersFilter()),
    })
  })

  test("makeQueryVariablesFilterBy", () => {
    let {makeQueryVariablesFilterBy} = module(SupplierOrderListPage.Scaffolded)

    let filters = {
      SupplierOrderListPage.Filters.status: None,
      issueDateRange: None,
      receptionDateRange: None,
    }

    expect(makeQueryVariablesFilterBy(filters))->toStrictEqual({
      SupplierOrderListPage.Query.status: None,
      shopIds: None,
      createdAt: None,
      issueDate: None,
      receptionFinishedAt: None,
    })

    let filters = {
      SupplierOrderListPage.Filters.status: Some(#FINALIZED),
      issueDateRange: Some((Js.Date.fromFloat(0.), Js.Date.fromFloat(1.))),
      receptionDateRange: Some((Js.Date.fromFloat(1.), Js.Date.fromFloat(0.))),
    }

    expect(makeQueryVariablesFilterBy(filters))->toStrictEqual({
      SupplierOrderListPage.Query.status: Some({_in: ["FINALIZED"]}),
      shopIds: None,
      createdAt: None,
      issueDate: Some({
        _before: None,
        _after: None,
        _between: Some([
          Js.Date.fromFloat(0.)->Scalar.Datetime.serialize,
          Js.Date.fromFloat(1.)->Scalar.Datetime.serialize,
        ]),
      }),
      receptionFinishedAt: Some({
        _before: None,
        _after: None,
        _between: Some([
          Js.Date.fromFloat(1.)->Scalar.Datetime.serialize,
          Js.Date.fromFloat(0.)->Scalar.Datetime.serialize,
        ]),
      }),
    })
  })

  test("totalCountFromQueryData", () => {
    let {totalCountFromQueryData} = module(SupplierOrderListPage.Scaffolded)

    let queryData = mockQueryData()
    expect(totalCountFromQueryData(queryData))->toBe(0)

    let queryData = mockQueryData(~ordersTotalCount=1, ())
    expect(totalCountFromQueryData(queryData))->toBe(1)

    let queryData = mockQueryData(~ordersTotalCount=100, ())
    expect(totalCountFromQueryData(queryData))->toBe(100)

    let queryData = mockQueryData(~ordersTotalCount=-1, ())
    expect(totalCountFromQueryData(queryData))->toBe(-1)
  })

  test("cursorsFromQueryData", () => {
    let {cursorsFromQueryData} = module(SupplierOrderListPage.Scaffolded)

    let queryData = mockQueryData()
    expect(cursorsFromQueryData(queryData))->toStrictEqual((None, None))

    let queryData = mockQueryData(
      ~ordersPageInfo=mockQueryDataPageInfo(~startCursor="start", ()),
      (),
    )
    expect(cursorsFromQueryData(queryData))->toStrictEqual((Some("start"), None))

    let queryData = mockQueryData(
      ~ordersPageInfo=mockQueryDataPageInfo(~startCursor="start", ~endCursor="end", ()),
      (),
    )
    expect(cursorsFromQueryData(queryData))->toStrictEqual((Some("start"), Some("end")))

    let queryData = mockQueryData(~ordersPageInfo=mockQueryDataPageInfo(~endCursor="end", ()), ())
    expect(cursorsFromQueryData(queryData))->toStrictEqual((None, Some("end")))
  })

  test("rowsFromQueryDataAndState", () => {
    let {rowsFromQueryDataAndState} = module(SupplierOrderListPage.Scaffolded)

    let queryData = mockQueryData()
    expect(rowsFromQueryDataAndState(queryData, mockState()))->toStrictEqual([])

    let queryData = mockQueryData(~ordersEdges=[mockQueryDataOrdersEdge()], ())
    expect(rowsFromQueryDataAndState(queryData, mockState()))->toStrictEqual([
      {
        SupplierOrderListPage.Row.id: "",
        supplierId: "",
        formattedName: "",
        shopName: "",
        issueDate: Js.Date.fromFloat(0.),
        receptionDate: None,
        statuses: [],
        totalAmountExcludingTaxes: 0.,
        totalAmountIncludingTaxes: 0.,
        totalProductsQuantity: 0,
        totalProductsExpectedQuantity: 0,
      },
    ])

    let queryData = mockQueryData(
      ~id="mock-supplier-id",
      ~ordersEdges=[
        mockQueryDataOrdersEdge(
          ~id="mock-order-id",
          ~formattedName="mock-formatted-name",
          ~shopName="mock-shop-name",
          ~supplierCompanyName="mock-supplier-company-name",
          ~issueDate=Js.Date.fromFloat(1.),
          ~receptionFinishedAt=Some(Js.Date.fromFloat(1.)),
          ~formattedStatus=[#FINALIZED, #ACCEPTED],
          ~totalAmountExcludingTaxes=0.,
          ~totalAmountIncludingTaxes=0.,
          ~totalProductsQuantity=1,
          ~totalProductsExpectedQuantity=2,
          (),
        ),
      ],
      (),
    )
    expect(rowsFromQueryDataAndState(queryData, mockState()))->toStrictEqual([
      {
        SupplierOrderListPage.Row.id: "mock-order-id",
        supplierId: "mock-supplier-id",
        formattedName: "mock-formatted-name",
        shopName: "mock-shop-name",
        issueDate: Js.Date.fromFloat(1.),
        receptionDate: Some(Js.Date.fromFloat(1.)),
        statuses: [#FINALIZED, #ACCEPTED],
        totalAmountExcludingTaxes: 0.,
        totalAmountIncludingTaxes: 0.,
        totalProductsQuantity: 1,
        totalProductsExpectedQuantity: 2,
      },
    ])
  })

  test("keyExtractor", () => {
    let {keyExtractor} = module(SupplierOrderListPage.Scaffolded)

    let row = mockRow()
    expect(keyExtractor(row))->toBe("")

    let row = mockRow(~id="mock-id", ())
    expect(keyExtractor(row))->toBe("mock-id")
  })
})

// Some integration tests to ensure basic interactions
// with the other components and GraphQL might be valuable
// at some point.
todo("Integration test")
