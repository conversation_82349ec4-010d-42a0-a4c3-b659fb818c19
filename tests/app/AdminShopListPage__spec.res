open Vitest

let mockTableRow = (
  ~id,
  ~name="",
  ~corporateName="",
  ~legalRepresentative="",
  ~email="",
  ~kind=#INDEPENDENT,
  (),
) => {
  AdminShopListPage.TableRow.id,
  name,
  corporateName,
  legalRepresentative,
  email,
  kind,
}

let mockQueryAllShopsRequestItem = (
  ~id="",
  ~name="",
  ~corporateName="",
  ~legalRepresentative="",
  ~email="",
  ~type_=?,
  (),
) => {
  AdminShopListPage.QueryAllShopsRequest.id,
  name,
  corporateName,
  legalRepresentative,
  email,
  type_,
}

describe("TableRow", () => {
  test("keyExtractor", () => {
    let {keyExtractor} = module(AdminShopListPage.TableRow)

    expect(keyExtractor(mockTableRow(~id="", ())))->toBe("")
    expect(keyExtractor(mockTableRow(~id="row-id", ())))->toBe("row-id")
  })

  test("sanitize", () => {
    let {sanitize} = module(AdminShopListPage.TableRow)

    expect(sanitize("abc"))->toStrictEqual("abc")
    expect(sanitize("Àbç!"))->toStrictEqual("abc")
  })

  test("match", () => {
    let {match} = module(AdminShopListPage.TableRow)

    let row = mockTableRow(
      ~id="id",
      ~name="name",
      ~corporateName="corporate-name",
      ~legalRepresentative="legal-representative",
      ~email="email",
      (),
    )

    expect(match(row, ""))->toBe(true)
    expect(match(row, "id"))->toBe(true)
    expect(match(row, "dummy"))->toBe(false)
    expect(match(row, "maiK"))->toBe(false)
    expect(match(row, "email userName"))->toBe(false)
    expect(match(row, "corporate"))->toBe(true)
    expect(match(row, "presentative"))->toBe(true)
    expect(match(row, "presentaTIve"))->toBe(true)
    expect(match(row, "corpo"))->toBe(true)
    expect(match(row, "cÔrpô"))->toBe(true)
    expect(match(row, "mail"))->toBe(true)
  })

  test("fromQueryItem", () => {
    let {fromQueryItem} = module(AdminShopListPage.TableRow)

    let queryItem = mockQueryAllShopsRequestItem(
      ~id="item-id",
      ~name="item-name",
      ~corporateName="item-corporate-name",
      ~legalRepresentative="item-legal-representative",
      ~email="item-email",
      (),
    )
    expect(fromQueryItem(queryItem))->toStrictEqual({
      AdminShopListPage.TableRow.id: "item-id",
      name: "item-name",
      corporateName: "item-corporate-name",
      legalRepresentative: "item-legal-representative",
      email: "item-email",
      kind: #INDEPENDENT,
    })

    let queryItem = mockQueryAllShopsRequestItem(
      ~id="item-id",
      ~name="item-name",
      ~corporateName="item-corporate-name",
      ~legalRepresentative="item-legal-representative",
      ~email="item-email",
      ~type_="INTEGRATED",
      (),
    )
    expect(fromQueryItem(queryItem))->toStrictEqual({
      AdminShopListPage.TableRow.id: "item-id",
      name: "item-name",
      corporateName: "item-corporate-name",
      legalRepresentative: "item-legal-representative",
      email: "item-email",
      kind: #INTEGRATED,
    })

    let queryItem = mockQueryAllShopsRequestItem(
      ~id="item-id",
      ~name="item-name",
      ~corporateName="item-corporate-name",
      ~legalRepresentative="item-legal-representative",
      ~email="item-email",
      ~type_="AFFILIATED",
      (),
    )
    expect(fromQueryItem(queryItem))->toStrictEqual({
      AdminShopListPage.TableRow.id: "item-id",
      name: "item-name",
      corporateName: "item-corporate-name",
      legalRepresentative: "item-legal-representative",
      email: "item-email",
      kind: #AFFILIATED,
    })

    let queryItem = mockQueryAllShopsRequestItem(
      ~id="item-id",
      ~name="item-name",
      ~corporateName="item-corporate-name",
      ~legalRepresentative="item-legal-representative",
      ~email="item-email",
      ~type_="FRANCHISED",
      (),
    )
    expect(fromQueryItem(queryItem))->toStrictEqual({
      AdminShopListPage.TableRow.id: "item-id",
      name: "item-name",
      corporateName: "item-corporate-name",
      legalRepresentative: "item-legal-representative",
      email: "item-email",
      kind: #FRANCHISED,
    })

    let queryItem = mockQueryAllShopsRequestItem(
      ~id="item-id",
      ~name="item-name",
      ~corporateName="item-corporate-name",
      ~legalRepresentative="item-legal-representative",
      ~email="item-email",
      ~type_="WAREHOUSE",
      (),
    )
    expect(fromQueryItem(queryItem))->toStrictEqual({
      AdminShopListPage.TableRow.id: "item-id",
      name: "item-name",
      corporateName: "item-corporate-name",
      legalRepresentative: "item-legal-representative",
      email: "item-email",
      kind: #WAREHOUSE,
    })

    let queryItem = mockQueryAllShopsRequestItem(
      ~id="item-id",
      ~name="item-name",
      ~corporateName="item-corporate-name",
      ~legalRepresentative="item-legal-representative",
      ~email="item-email",
      ~type_="UNKNOWN",
      (),
    )
    expect(fromQueryItem(queryItem))->toStrictEqual({
      AdminShopListPage.TableRow.id: "item-id",
      name: "item-name",
      corporateName: "item-corporate-name",
      legalRepresentative: "item-legal-representative",
      email: "item-email",
      kind: #INDEPENDENT,
    })
  })
})

describe("TableRows", () => {
  let {totalPages, search, paginate, fromQueryAllShops} = module(AdminShopListPage.TableRows)

  test("totalPages", () => {
    expect(totalPages([]))->toBe(0)
    expect(totalPages([mockTableRow(~id="1")]))->toBe(1)

    let tableRowFromIndex = index => mockTableRow(~id=index->Int.toString, ())

    expect(totalPages(Array.makeBy(2, tableRowFromIndex)))->toBe(1)
    expect(totalPages(Array.makeBy(5, tableRowFromIndex)))->toBe(1)
    expect(totalPages(Array.makeBy(9, tableRowFromIndex)))->toBe(1)
    expect(totalPages(Array.makeBy(10, tableRowFromIndex)))->toBe(1)
    expect(totalPages(Array.makeBy(11, tableRowFromIndex)))->toBe(2)
    expect(totalPages(Array.makeBy(19, tableRowFromIndex)))->toBe(2)
    expect(totalPages(Array.makeBy(20, tableRowFromIndex)))->toBe(2)
    expect(totalPages(Array.makeBy(21, tableRowFromIndex)))->toBe(3)
    expect(totalPages(Array.makeBy(99, tableRowFromIndex)))->toBe(10)
  })

  test("search", () => {
    let tableRowA = mockTableRow(
      ~id="row-id-a",
      ~name="row-name-a",
      ~corporateName="row-corporate-name-a",
      ~legalRepresentative="row-legal-representative-a",
      ~email="row-email-a",
      (),
    )
    let tableRowB = mockTableRow(
      ~id="row-id-b",
      ~name="row-name-b",
      ~corporateName="row-corporate-name-b",
      ~legalRepresentative="row-legal-representative-b",
      ~email="row-email-b",
      (),
    )

    let tableRows = [tableRowA, tableRowB]

    expect(search(tableRows, ""))->toStrictEqual([tableRowA, tableRowB])
    expect(search(tableRows, "id"))->toStrictEqual([])
    expect(search(tableRows, "row-id-a"))->toStrictEqual([tableRowA])
    expect(search(tableRows, "corporate-name"))->toStrictEqual([tableRowA, tableRowB])
    expect(search(tableRows, "corporate-name-a"))->toStrictEqual([tableRowA])
    expect(search(tableRows, "corporate-name-b"))->toStrictEqual([tableRowB])
  })

  test("paginate", () => {
    let tableRowFromIndex = index => mockTableRow(~id=index->Int.toString, ())

    expect(paginate(Array.makeBy(2, tableRowFromIndex), 1))->toStrictEqual(
      Array.makeBy(2, tableRowFromIndex),
    )
    expect(paginate(Array.makeBy(2, tableRowFromIndex), 2))->toStrictEqual([])
    expect(paginate(Array.makeBy(2, tableRowFromIndex), 3))->toStrictEqual([])
    expect(paginate(Array.makeBy(11, tableRowFromIndex), 1))->toStrictEqual(
      Array.makeBy(10, tableRowFromIndex),
    )
    expect(paginate(Array.makeBy(11, tableRowFromIndex), 2))->toStrictEqual(
      Array.makeBy(1, index => tableRowFromIndex(index + 10)),
    )
    expect(paginate(Array.makeBy(11, tableRowFromIndex), 3))->toStrictEqual([])
  })

  test("fromQueryAllShops", () => {
    let queryItemA = mockQueryAllShopsRequestItem(
      ~id="item-id-a",
      ~name="item-name-a",
      ~corporateName="item-corporate-name-a",
      ~legalRepresentative="item-legal-representative-a",
      ~email="item-email-a",
      (),
    )
    let queryItemB = mockQueryAllShopsRequestItem(
      ~id="item-id-b",
      ~name="item-name-b",
      ~corporateName="item-corporate-name-b",
      ~legalRepresentative="item-legal-representative-b",
      ~email="item-email-b",
      ~type_="WAREHOUSE",
      (),
    )

    expect(fromQueryAllShops([]))->toStrictEqual([])
    expect(fromQueryAllShops([queryItemA]))->toStrictEqual([
      {
        AdminShopListPage.TableRow.id: "item-id-a",
        name: "item-name-a",
        corporateName: "item-corporate-name-a",
        legalRepresentative: "item-legal-representative-a",
        email: "item-email-a",
        kind: #INDEPENDENT,
      },
    ])
    expect(fromQueryAllShops([queryItemA, queryItemB]))->toStrictEqual([
      {
        AdminShopListPage.TableRow.id: "item-id-a",
        name: "item-name-a",
        corporateName: "item-corporate-name-a",
        legalRepresentative: "item-legal-representative-a",
        email: "item-email-a",
        kind: #INDEPENDENT,
      },
      {
        AdminShopListPage.TableRow.id: "item-id-b",
        name: "item-name-b",
        corporateName: "item-corporate-name-b",
        legalRepresentative: "item-legal-representative-b",
        email: "item-email-b",
        kind: #WAREHOUSE,
      },
    ])
  })
})

describe("QueryAllShopsRequest", () => {
  let {decodeResult, decodeResultItem} = module(AdminShopListPage.QueryAllShopsRequest)

  let mockJsonShop = (~id, ~name, ~corporateName, ~legalRepresentative, ~email) =>
    Js.Dict.fromArray([
      ("id", id->Json.encodeString),
      ("name", name->Json.encodeString),
      ("corporateName", corporateName->Json.encodeString),
      ("legalRepresentative", legalRepresentative->Json.encodeString),
      ("email", email->Json.encodeString),
    ])->Json.encodeDict

  test("decodeResultItem", () => {
    expect(decodeResultItem(""->Json.encodeString))->toStrictEqual(None)

    let dummyErrors = Js.Dict.empty()
    dummyErrors->Js.Dict.set("errors", "dummy-error"->Json.encodeString)
    expect(decodeResultItem(dummyErrors->Json.encodeDict))->toStrictEqual(None)

    let dummyJsonShop =
      Js.Dict.fromArray([
        ("identifier", ""->Json.encodeString),
        ("shop-name", ""->Json.encodeString),
      ])->Json.encodeDict
    expect(decodeResultItem(dummyJsonShop))->toStrictEqual(None)

    let jsonShop = mockJsonShop(
      ~id="shop-id",
      ~name="shop-name",
      ~corporateName="shop-corporate-name",
      ~legalRepresentative="shop-legal-representative",
      ~email="shop-email",
    )
    expect(decodeResultItem(jsonShop))->toStrictEqual(
      Some({
        id: "shop-id",
        name: "shop-name",
        corporateName: "shop-corporate-name",
        legalRepresentative: "shop-legal-representative",
        email: "shop-email",
        type_: None,
      }),
    )
  })

  test("decodeResult", () => {
    expect(decodeResult("data"->Json.encodeString))->toStrictEqual([])

    let dummyErrors = Js.Dict.empty()
    dummyErrors->Js.Dict.set("errors", "dummy-error"->Json.encodeString)
    expect(decodeResult(dummyErrors->Json.encodeDict))->toStrictEqual([])

    let dummyData = Js.Dict.empty()
    dummyData->Js.Dict.set("a", "b"->Json.encodeString)
    expect(decodeResult(dummyData->Json.encodeDict))->toStrictEqual([])

    let dummyItem = Js.Dict.empty()
    dummyItem->Js.Dict.set("foo", "bar"->Json.encodeString)
    expect(decodeResult([dummyItem->Json.encodeDict]->Json.encodeArray))->toStrictEqual([])

    let jsonShopA = mockJsonShop(
      ~id="shop-a-id",
      ~name="shop-a-name",
      ~corporateName="shop-a-corporate-name",
      ~legalRepresentative="shop-a-legal-representative",
      ~email="shop-a-email",
    )

    let json = Json.fromObjExn([jsonShopA])
    expect(decodeResult(json))->toStrictEqual([
      {
        id: "shop-a-id",
        name: "shop-a-name",
        corporateName: "shop-a-corporate-name",
        legalRepresentative: "shop-a-legal-representative",
        email: "shop-a-email",
        type_: None,
      },
    ])

    let json = Json.fromObjExn([dummyItem->Json.encodeDict, jsonShopA])
    expect(decodeResult(json))->toStrictEqual([
      {
        id: "shop-a-id",
        name: "shop-a-name",
        corporateName: "shop-a-corporate-name",
        legalRepresentative: "shop-a-legal-representative",
        email: "shop-a-email",
        type_: None,
      },
    ])

    let jsonShopB = mockJsonShop(
      ~id="shop-b-id",
      ~name="shop-b-name",
      ~corporateName="shop-b-corporate-name",
      ~legalRepresentative="shop-b-legal-representative",
      ~email="shop-b-email",
    )

    let json = Json.fromObjExn([jsonShopB, jsonShopA])
    expect(decodeResult(json))->toStrictEqual([
      {
        id: "shop-b-id",
        name: "shop-b-name",
        corporateName: "shop-b-corporate-name",
        legalRepresentative: "shop-b-legal-representative",
        email: "shop-b-email",
        type_: None,
      },
      {
        id: "shop-a-id",
        name: "shop-a-name",
        corporateName: "shop-a-corporate-name",
        legalRepresentative: "shop-a-legal-representative",
        email: "shop-a-email",
        type_: None,
      },
    ])
  })
})

describe("Reducer", () => {
  open AdminShopListPage.Reducer

  let fulfilledState = {
    searchQuery: "dummy-search-query",
    currentPage: 2,
    asyncResult: AsyncData.loading(),
  }

  test("make with SearchQueryChanged(_)", () => {
    expect(make(initialState, SearchQueryChanged("any-search-query")))->toStrictEqual({
      searchQuery: "any-search-query",
      currentPage: 1,
      asyncResult: AsyncResult.notAsked(),
    })
    expect(make(fulfilledState, SearchQueryChanged("any-search-query")))->toStrictEqual({
      searchQuery: "any-search-query",
      currentPage: 1,
      asyncResult: AsyncData.loading(),
    })
  })

  test("make with AsyncResultGet(_)", () => {
    expect(make(initialState, AsyncResultGet(AsyncResult.done(Ok([])))))->toStrictEqual({
      searchQuery: "",
      currentPage: 1,
      asyncResult: AsyncResult.done(Ok([])),
    })
    expect(make(fulfilledState, AsyncResultGet(AsyncResult.done(Ok([])))))->toStrictEqual({
      searchQuery: "dummy-search-query",
      currentPage: 1,
      asyncResult: AsyncResult.done(Ok([])),
    })
  })

  test("make with Paginated(_)", () => {
    expect(make(initialState, Paginated(LegacyPagination.First, 1)))->toStrictEqual({
      searchQuery: "",
      currentPage: 1,
      asyncResult: AsyncResult.notAsked(),
    })
    expect(make(fulfilledState, Paginated(LegacyPagination.First, 1)))->toStrictEqual({
      searchQuery: "dummy-search-query",
      currentPage: 1,
      asyncResult: AsyncResult.loading(),
    })

    expect(make(initialState, Paginated(LegacyPagination.Prev, 1)))->toStrictEqual({
      searchQuery: "",
      currentPage: 1,
      asyncResult: AsyncResult.notAsked(),
    })
    expect(make(fulfilledState, Paginated(LegacyPagination.Prev, 1)))->toStrictEqual({
      searchQuery: "dummy-search-query",
      currentPage: 1,
      asyncResult: AsyncResult.loading(),
    })

    expect(make(initialState, Paginated(LegacyPagination.Next, 3)))->toStrictEqual({
      searchQuery: "",
      currentPage: 2,
      asyncResult: AsyncResult.notAsked(),
    })
    expect(make(fulfilledState, Paginated(LegacyPagination.Next, 2)))->toStrictEqual({
      searchQuery: "dummy-search-query",
      currentPage: 2,
      asyncResult: AsyncResult.loading(),
    })

    expect(make(initialState, Paginated(LegacyPagination.Last, 1)))->toStrictEqual({
      searchQuery: "",
      currentPage: 1,
      asyncResult: AsyncResult.notAsked(),
    })
    expect(make(fulfilledState, Paginated(LegacyPagination.Last, 3)))->toStrictEqual({
      searchQuery: "dummy-search-query",
      currentPage: 3,
      asyncResult: AsyncResult.loading(),
    })
  })
})

// Some integration tests to ensure basic interactions
// with the other components and gateway api be valuable
// at some point.
todo("Integration test")
