open Vitest

describe("CatalogInventoryAuditRequest", () => {
  module CatalogInventoryAuditRequest = CatalogInventoryImportPage.CatalogInventoryAuditRequest

  describe("encodeBodyJson", () => {
    let {encodeBodyJson} = module(CatalogInventoryAuditRequest)

    it(
      "should encode inventory items",
      () => {
        let encodedBodyJson = encodeBodyJson([])
        let expectedBodyJson = {"inventoryItems": []}
        expect(encodedBodyJson)->toUnsafeStrictEqual(expectedBodyJson)

        let encodedBodyJson = encodeBodyJson([
          {variantId: "", stock: -1.},
          {variantId: "variant-id", stock: 12.67},
        ])
        let expectedBodyJson = {
          "inventoryItems": [
            {"variantId": "", "stock": -1.},
            {"variantId": "variant-id", "stock": 12.67},
          ],
        }
        expect(encodedBodyJson)->toUnsafeStrictEqual(expectedBodyJson)
      },
    )
  })

  test("decodeInvalidRequestFailureDataVariantId", () => {
    let {decodeInvalidRequestFailureDataVariantId} = module(CatalogInventoryAuditRequest)

    let json = Json.fromObjExn(Uuid.make())
    expect(decodeInvalidRequestFailureDataVariantId(json))->toStrictEqual(None)

    let json = Json.fromObjExn({"id": ""})
    expect(decodeInvalidRequestFailureDataVariantId(json))->toStrictEqual(None)

    let json = Json.fromObjExn({"id": 1})
    expect(decodeInvalidRequestFailureDataVariantId(json))->toStrictEqual(None)

    let uuid = Uuid.make()
    let json = Json.fromObjExn({"uuid": uuid})
    expect(decodeInvalidRequestFailureDataVariantId(json))->toStrictEqual(None)

    let id = Uuid.make()
    let json = Json.fromObjExn({"id": id})
    expect(decodeInvalidRequestFailureDataVariantId(json))->toStrictEqual(Some(id))

    let id = Uuid.make()
    let json = Json.fromObjExn({"variantId": id})
    expect(decodeInvalidRequestFailureDataVariantId(json))->toStrictEqual(Some(id))
  })

  test("decodeInvalidRequestFailure", () => {
    let {decodeInvalidRequestFailure} = module(CatalogInventoryAuditRequest)

    let serverFailure = {Request.kind: "Error", data: None, message: ""}
    expect(decodeInvalidRequestFailure(serverFailure))->toStrictEqual(Unknown)

    let id = Uuid.make()
    let serverFailure = {
      Request.kind: "Error",
      data: Some(Json.fromObjExn({"id": id})),
      message: "",
    }
    expect(decodeInvalidRequestFailure(serverFailure))->toStrictEqual(Unknown)

    let serverFailure = {Request.kind: "NotFoundVariant", data: None, message: ""}
    expect(decodeInvalidRequestFailure(serverFailure))->toStrictEqual(Unknown)

    let id = Uuid.make()
    let serverFailure = {
      Request.kind: "NotFoundVariant",
      data: Some(Json.fromObjExn({"id": id})),
      message: "",
    }
    expect(decodeInvalidRequestFailure(serverFailure))->toStrictEqual(NotFoundVariant(id))

    let id = Uuid.make()
    let serverFailure = {
      Request.kind: "VariantBulkStockValue",
      data: Some(Json.fromObjExn({"id": id})),
      message: "",
    }
    expect(decodeInvalidRequestFailure(serverFailure))->toStrictEqual(NotBulkProduct(id))

    let id = Uuid.make()
    let serverFailure = {
      Request.kind: "InsufficientCapacityPrecision",
      data: Some(Json.fromObjExn({"variantId": id})),
      message: "",
    }
    expect(decodeInvalidRequestFailure(serverFailure))->toStrictEqual(InvalidCapacityPrecision(id))

    let id = Uuid.make()
    let serverFailure = {
      Request.kind: "DuplicateVariantIdFailure",
      data: Some(Json.fromObjExn({"id": id})),
      message: "",
    }
    expect(decodeInvalidRequestFailure(serverFailure))->toStrictEqual(DuplicateVariantId(id))

    let id = Uuid.make()
    let serverFailure = {
      Request.kind: "OutOfRangeStockValue",
      data: Some(Json.fromObjExn({"id": id})),
      message: "",
    }
    expect(decodeInvalidRequestFailure(serverFailure))->toStrictEqual(OutOfRangeStockValue(id))
  })

  test("decodeError", () => {
    let {decodeError} = module(CatalogInventoryAuditRequest)

    expect(decodeError(Request.UnexpectedServerError))->toStrictEqual([Unknown])
    expect(decodeError(Request.MalformedResponse))->toStrictEqual([Unknown])
    expect(decodeError(Request.InvalidRequestFailures([])))->toStrictEqual([Unknown])

    expect(
      decodeError(
        Request.InvalidRequestFailures([
          {
            kind: "NotFoundVariant",
            data: None,
            message: "",
          },
        ]),
      ),
    )->toStrictEqual([Unknown])

    let id = Uuid.make()
    expect(
      decodeError(
        Request.InvalidRequestFailures([
          {
            kind: "NotFoundVariant",
            data: Some(Json.fromObjExn({"id": id})),
            message: "",
          },
        ]),
      ),
    )->toStrictEqual([NotFoundVariant(id)])

    let idA = Uuid.make()
    let idB = Uuid.make()
    expect(
      decodeError(
        Request.InvalidRequestFailures([
          {
            kind: "NotFoundVariant",
            data: Some(Json.fromObjExn({"id": idA})),
            message: "",
          },
          {
            kind: "OtherFailureKind",
            data: Some(Json.fromObjExn({"id": Uuid.make()})),
            message: "",
          },
          {
            kind: "NotFoundVariant",
            data: Some(Json.fromObjExn({"id": idB})),
            message: "",
          },
        ]),
      ),
    )->toStrictEqual([NotFoundVariant(idA), Unknown, NotFoundVariant(idB)])
  })
})

describe("CatalogInventoryImportRequest", () => {
  describe("encodeBodyJson", () => {
    let {encodeBodyJson} = module(CatalogInventoryImportPage.CatalogInventoryImportRequest)

    it(
      "should encode inventory items",
      () => {
        let encodedBodyJson = encodeBodyJson(~inventoryItems=[], ())
        let expectedBodyJson = {"inventoryItems": []}
        expect(encodedBodyJson)->toUnsafeStrictEqual(expectedBodyJson)

        let encodedBodyJson = encodeBodyJson(
          ~inventoryItems=[{variantId: "", stock: -1.}, {variantId: "variant-id", stock: 12.67}],
          (),
        )
        let expectedBodyJson = {
          "inventoryItems": [
            {"variantId": "", "stock": -1.},
            {"variantId": "variant-id", "stock": 12.67},
          ],
        }
        expect(encodedBodyJson)->toUnsafeStrictEqual(expectedBodyJson)
      },
    )

    it(
      "should encode a json with empty inventory items and inventory datetime",
      () => {
        let inventoryDatetime = Js.Date.makeWithYMD(~year=2022., ~month=9., ~date=13., ())
        let encodedBodyJson = encodeBodyJson(~inventoryItems=[], ~inventoryDatetime, ())
        let expectedBodyJson = {
          "inventoryItems": [],
          "inventoryDate": 1665619200000.,
        }
        expect(encodedBodyJson)->toUnsafeStrictEqual(expectedBodyJson)

        let inventoryDatetime = Js.Date.makeWithYMDHM(
          ~year=2022.,
          ~month=9.,
          ~date=13.,
          ~hours=9.,
          ~minutes=30.,
          (),
        )
        let encodedBodyJson = encodeBodyJson(
          ~inventoryItems=[{variantId: "", stock: -1.}, {variantId: "variant-id", stock: 12.}],
          ~inventoryDatetime,
          (),
        )

        expect(encodedBodyJson)->toUnsafeStrictEqual({
          "inventoryItems": [
            {"variantId": "", "stock": -1.},
            {"variantId": "variant-id", "stock": 12.},
          ],
          "inventoryDate": 1665653400000.,
        })
      },
    )
  })
})

todo("CatalogInventoryImportFileState")
todo("CatalogInventoryImportDatetimeState")

todo("CatalogInventoryImportPickFileView")
todo("CatalogInventoryImportLaunchImportView")
todo("CatalogInventoryImportSuccessImportView")
todo("CatalogInventoryImportFileCard")
todo("CatalogInventoryImportDatetimeCard")
todo("CatalogInventoryImportPrecautionCard")

test("arrayChunks", () => {
  let {arrayChunks} = module(CatalogInventoryImportPage)

  expect(arrayChunks([], ~size=0))->toStrictEqual([])
  expect(arrayChunks([], ~size=1))->toStrictEqual([])
  expect(arrayChunks([1], ~size=1))->toStrictEqual([[1]])
  expect(arrayChunks([1, 2], ~size=1))->toStrictEqual([[1], [2]])
  expect(arrayChunks([1, 2, 3], ~size=1))->toStrictEqual([[1], [2], [3]])
  expect(arrayChunks([1, 2, 3, 4], ~size=1))->toStrictEqual([[1], [2], [3], [4]])
  expect(arrayChunks([], ~size=2))->toStrictEqual([])
  expect(arrayChunks([1], ~size=2))->toStrictEqual([[1]])
  expect(arrayChunks([1, 2], ~size=2))->toStrictEqual([[1, 2]])
  expect(arrayChunks([1, 2, 3], ~size=2))->toStrictEqual([[1, 2], [3]])
  expect(arrayChunks([1, 2, 3, 4], ~size=2))->toStrictEqual([[1, 2], [3, 4]])
  expect(arrayChunks([], ~size=3))->toStrictEqual([])
  expect(arrayChunks([1], ~size=3))->toStrictEqual([[1]])
  expect(arrayChunks([1, 2, 3, 4, 5], ~size=3))->toStrictEqual([[1, 2, 3], [4, 5]])
  expect(arrayChunks([1, 2, 3, 4, 5, 6], ~size=3))->toStrictEqual([[1, 2, 3], [4, 5, 6]])
  expect(arrayChunks([1, 2, 3, 4, 5, 6, 7], ~size=3))->toStrictEqual([[1, 2, 3], [4, 5, 6], [7]])
})

test("catalogInventoryQueryChunkSize", () => {
  let {catalogInventoryQueryChunkSize} = module(CatalogInventoryImportPage)
  expect(catalogInventoryQueryChunkSize)->toBe(2500)
})

describe("queryCatalogInventoryImport", () => {
  let {queryCatalogInventoryImport} = module(CatalogInventoryImportPage)

  itFuture("should return Ok() with no inventory items and no inventory datetime", () => {
    let catalogInventoryImportRequest = fn1((_, _) => Future.value(Ok()))
    let catalogInventoryQueryChunkSize = 2
    let inventoryItems = []

    queryCatalogInventoryImport(
      ~catalogInventoryImportRequest=catalogInventoryImportRequest->fn,
      ~catalogInventoryQueryChunkSize,
      ~inventoryItems,
      (),
    )->Future.tap(
      result => {
        expect(catalogInventoryImportRequest)->toHaveBeenCalledTimes(0)
        expect(result)->toStrictEqual(Ok())
      },
    )
  })

  itFuture("should return Ok() with multiple inventory items and no inventory datetime", () => {
    let catalogInventoryImportRequest = fn2((_, _) => Future.value(Ok()))
    let catalogInventoryQueryChunkSize = 2
    let inventoryItems = [
      {
        CatalogInventoryImportSheet.variantId: "",
        stock: 0.,
      },
      {
        variantId: "variant-id",
        stock: 1.234,
      },
    ]

    queryCatalogInventoryImport(
      ~catalogInventoryImportRequest=catalogInventoryImportRequest->fn,
      ~catalogInventoryQueryChunkSize,
      ~inventoryItems,
      (),
    )->Future.tap(
      result => {
        expect(catalogInventoryImportRequest)->toHaveBeenCalledTimes(1)
        expect(catalogInventoryImportRequest)->toHaveBeenCalledWith2(
          [
            {
              CatalogInventoryImportSheet.variantId: "",
              stock: 0.,
            },
            {
              variantId: "variant-id",
              stock: 1.234,
            },
          ],
          None,
        )
        expect(result)->toStrictEqual(Ok())
      },
    )
  })

  itFuture("should return Ok() with inventory datetime defined and inventory items chunks", () => {
    let catalogInventoryImportRequest = fn2((_, _) => Future.value(Ok()))
    let catalogInventoryQueryChunkSize = 2
    let inventoryDatetime = Js.Date.makeWithYMDHMS(
      ~year=2024.,
      ~month=0.,
      ~date=1.,
      ~hours=18.,
      ~minutes=42.,
      ~seconds=34.,
      (),
    )
    let inventoryItems = [
      {
        CatalogInventoryImportSheet.variantId: "",
        stock: 0.,
      },
      {
        variantId: "variant-id",
        stock: 1.234,
      },
      {
        variantId: "",
        stock: -89.,
      },
      {
        variantId: "",
        stock: -1.,
      },
    ]

    queryCatalogInventoryImport(
      ~catalogInventoryImportRequest=catalogInventoryImportRequest->fn,
      ~catalogInventoryQueryChunkSize,
      ~inventoryItems,
      ~inventoryDatetime,
      (),
    )->Future.tap(
      result => {
        expect(catalogInventoryImportRequest)->toHaveBeenCalledTimes(2)
        expect(catalogInventoryImportRequest)->toHaveBeenCalledWith2(
          [
            {
              variantId: "",
              stock: -89.,
            },
            {
              variantId: "",
              stock: -1.,
            },
          ],
          Some(inventoryDatetime),
        )
        expect(catalogInventoryImportRequest)->toHaveBeenCalledWith2(
          [
            {
              variantId: "",
              stock: 0.,
            },
            {
              variantId: "variant-id",
              stock: 1.234,
            },
          ],
          Some(inventoryDatetime),
        )
        expect(result)->toStrictEqual(Ok())
      },
    )
  })

  itFuture("should return Error() with some backend errors", () => {
    let catalogInventoryImportRequest = fn2(
      (inventoryItems, _) => {
        if inventoryItems->Array.length === 1 {
          Future.value(Ok())
        } else {
          Future.value(Error([]))
        }
      },
    )
    let catalogInventoryQueryChunkSize = 2
    let inventoryDatetime = Js.Date.makeWithYMDHMS(
      ~year=2024.,
      ~month=0.,
      ~date=1.,
      ~hours=18.,
      ~minutes=42.,
      ~seconds=34.,
      (),
    )
    let inventoryItems = [
      {
        CatalogInventoryImportSheet.variantId: "",
        stock: 0.,
      },
      {
        variantId: "variant-id",
        stock: 1.234,
      },
      {
        variantId: "",
        stock: -89.,
      },
      {
        variantId: "",
        stock: -1.,
      },
      {
        variantId: "",
        stock: 4.,
      },
    ]

    queryCatalogInventoryImport(
      ~catalogInventoryImportRequest=catalogInventoryImportRequest->fn,
      ~catalogInventoryQueryChunkSize,
      ~inventoryItems,
      ~inventoryDatetime,
      (),
    )->Future.tap(
      result => {
        expect(catalogInventoryImportRequest)->toHaveBeenCalledTimes(3)
        expect(catalogInventoryImportRequest)->toHaveBeenCalledWith2(
          [
            {
              variantId: "",
              stock: 0.,
            },
            {
              variantId: "variant-id",
              stock: 1.234,
            },
          ],
          Some(inventoryDatetime),
        )
        expect(catalogInventoryImportRequest)->toHaveBeenCalledWith2(
          [
            {
              variantId: "",
              stock: -89.,
            },
            {
              variantId: "",
              stock: -1.,
            },
          ],
          Some(inventoryDatetime),
        )
        expect(catalogInventoryImportRequest)->toHaveBeenCalledWith2(
          [
            {
              variantId: "",
              stock: 4.,
            },
          ],
          Some(inventoryDatetime),
        )
        expect(result)->toStrictEqual(Error())
      },
    )
  })

  itFuture("should return Error() with only backend errors", () => {
    let catalogInventoryImportRequest = fn2((_, _) => Future.value(Error([])))
    let catalogInventoryQueryChunkSize = 9
    let inventoryItems = [
      {
        CatalogInventoryImportSheet.variantId: "",
        stock: 0.,
      },
    ]

    queryCatalogInventoryImport(
      ~catalogInventoryImportRequest=catalogInventoryImportRequest->fn,
      ~catalogInventoryQueryChunkSize,
      ~inventoryItems,
      (),
    )->Future.tap(
      result => {
        expect(catalogInventoryImportRequest)->toHaveBeenCalledTimes(1)
        expect(catalogInventoryImportRequest)->toHaveBeenCalledWith2(
          [
            {
              variantId: "",
              stock: 0.,
            },
          ],
          None,
        )
        expect(result)->toStrictEqual(Error())
      },
    )
  })
})

describe("queryCatalogInventoryAudit", () => {
  let {queryCatalogInventoryAudit} = module(CatalogInventoryImportPage)

  itFuture("should return Ok() with no inventory items", () => {
    let catalogInventoryAuditRequest = fn1(_ => Future.value(Ok()))
    let catalogInventoryQueryChunkSize = 2
    let inventoryItems = []

    queryCatalogInventoryAudit(
      ~catalogInventoryAuditRequest=catalogInventoryAuditRequest->fn,
      ~catalogInventoryQueryChunkSize,
      ~inventoryItems,
    )->Future.tap(
      result => {
        expect(catalogInventoryAuditRequest)->toHaveBeenCalledTimes(0)
        expect(result)->toStrictEqual(Ok())
      },
    )
  })

  itFuture("should return Ok() with multiple inventory items", () => {
    let catalogInventoryAuditRequest = fn1(_ => Future.value(Ok()))
    let catalogInventoryQueryChunkSize = 3
    let inventoryItems = [
      {
        CatalogInventoryImportSheet.variantId: "",
        stock: 0.,
      },
      {
        variantId: "variant-id",
        stock: 1.234,
      },
    ]

    queryCatalogInventoryAudit(
      ~catalogInventoryAuditRequest=catalogInventoryAuditRequest->fn,
      ~catalogInventoryQueryChunkSize,
      ~inventoryItems,
    )->Future.tap(
      result => {
        expect(catalogInventoryAuditRequest)->toHaveBeenCalledTimes(1)
        expect(catalogInventoryAuditRequest)->toHaveBeenCalledWith1([
          {
            variantId: "",
            stock: 0.,
          },
          {
            variantId: "variant-id",
            stock: 1.234,
          },
        ])
        expect(result)->toStrictEqual(Ok())
      },
    )
  })

  itFuture("should return Ok() with multiple inventory items chunks", () => {
    let catalogInventoryAuditRequest = fn1(_ => Future.value(Ok()))
    let catalogInventoryQueryChunkSize = 3
    let inventoryItems = [
      {
        CatalogInventoryImportSheet.variantId: "",
        stock: 0.,
      },
      {
        variantId: "variant-id",
        stock: 1.234,
      },
      {
        variantId: "",
        stock: -89.,
      },
      {
        variantId: "",
        stock: -1.,
      },
    ]

    queryCatalogInventoryAudit(
      ~catalogInventoryAuditRequest=catalogInventoryAuditRequest->fn,
      ~catalogInventoryQueryChunkSize,
      ~inventoryItems,
    )->Future.tap(
      result => {
        expect(catalogInventoryAuditRequest)->toHaveBeenCalledTimes(2)
        expect(catalogInventoryAuditRequest)->toHaveBeenCalledWith1([
          {
            variantId: "",
            stock: -1.,
          },
        ])
        expect(catalogInventoryAuditRequest)->toHaveBeenCalledWith1([
          {
            variantId: "",
            stock: 0.,
          },
          {
            variantId: "variant-id",
            stock: 1.234,
          },
          {
            variantId: "",
            stock: -89.,
          },
        ])
        expect(result)->toStrictEqual(Ok())
      },
    )
  })

  itFuture(
    "should return Error() with multiple inventory items chunks and some backend errors",
    () => {
      let erroredVariantId = Uuid.make()
      let catalogInventoryAuditRequest = fn1(
        input => {
          if input->Array.length === 1 {
            Future.value(
              Error([
                CatalogInventoryImportPage.CatalogInventoryAuditRequest.Unknown,
                NotFoundVariant(erroredVariantId),
              ]),
            )
          } else {
            Future.value(Ok())
          }
        },
      )
      let catalogInventoryQueryChunkSize = 3
      let inventoryItems = [
        {
          CatalogInventoryImportSheet.variantId: "",
          stock: 0.,
        },
        {
          variantId: "variant-id",
          stock: 1.234,
        },
        {
          variantId: "",
          stock: -89.,
        },
        {
          variantId: "",
          stock: -1.,
        },
      ]

      queryCatalogInventoryAudit(
        ~catalogInventoryAuditRequest=catalogInventoryAuditRequest->fn,
        ~catalogInventoryQueryChunkSize,
        ~inventoryItems,
      )->Future.tap(
        result => {
          expect(catalogInventoryAuditRequest)->toHaveBeenCalledTimes(2)
          expect(catalogInventoryAuditRequest)->toHaveBeenCalledWith1([
            {
              variantId: "",
              stock: -1.,
            },
          ])
          expect(catalogInventoryAuditRequest)->toHaveBeenCalledWith1([
            {
              variantId: "",
              stock: 0.,
            },
            {
              variantId: "variant-id",
              stock: 1.234,
            },
            {
              variantId: "",
              stock: -89.,
            },
          ])
          expect(result)->toStrictEqual(Error([Unknown, NotFoundVariant(erroredVariantId)]))
        },
      )
    },
  )

  itFuture(
    "should return Error() with multiple inventory items chunks and only backend errors",
    () => {
      let erroredVariantId = Uuid.make()
      let catalogInventoryAuditRequest = fn1(
        input => {
          if input->Array.length === 1 {
            let error = CatalogInventoryImportPage.CatalogInventoryAuditRequest.NotFoundVariant(
              erroredVariantId,
            )
            Future.value(Error([error]))
          } else {
            Future.value(Error([CatalogInventoryImportPage.CatalogInventoryAuditRequest.Unknown]))
          }
        },
      )
      let catalogInventoryQueryChunkSize = 3
      let inventoryItems = [
        {
          CatalogInventoryImportSheet.variantId: "",
          stock: 0.,
        },
        {
          variantId: "variant-id",
          stock: 1.234,
        },
        {
          variantId: "",
          stock: -89.,
        },
        {
          variantId: "",
          stock: -1.,
        },
      ]

      queryCatalogInventoryAudit(
        ~catalogInventoryAuditRequest=catalogInventoryAuditRequest->fn,
        ~catalogInventoryQueryChunkSize,
        ~inventoryItems,
      )->Future.tap(
        result => {
          expect(catalogInventoryAuditRequest)->toHaveBeenCalledTimes(2)
          expect(catalogInventoryAuditRequest)->toHaveBeenCalledWith1([
            {
              variantId: "",
              stock: -1.,
            },
          ])
          expect(catalogInventoryAuditRequest)->toHaveBeenCalledWith1([
            {
              variantId: "",
              stock: 0.,
            },
            {
              variantId: "variant-id",
              stock: 1.234,
            },
            {
              variantId: "",
              stock: -89.,
            },
          ])
          expect(result)->toStrictEqual(Error([Unknown, NotFoundVariant(erroredVariantId)]))
        },
      )
    },
  )
})

describe("mapQueryCatalogInventorySheetDecodeRowError", () => {
  let {mapQueryCatalogInventorySheetDecodeRowError} = module(CatalogInventoryImportPage)

  test("with NegativeCellStock(_)", () => {
    expect(
      mapQueryCatalogInventorySheetDecodeRowError([
        NegativeCellStock({
          index: 0,
          json: Json.fromObjExn("-1"),
        }),
        NegativeCellStock({
          index: 99,
          json: Json.fromObjExn(""),
        }),
      ]),
    )->toStrictEqual([
      `Negative inventory quantity "-1" at line 1`,
      `Negative inventory quantity "" at line 100`,
    ])
    expect(
      mapQueryCatalogInventorySheetDecodeRowError([
        NegativeCellStock({
          index: 0,
          json: Json.fromObjExn("-1"),
        }),
        NegativeCellStock({
          index: 99,
          json: Json.fromObjExn(""),
        }),
      ]),
    )->toStrictEqual([
      `Negative inventory quantity "-1" at line 1`,
      `Negative inventory quantity "" at line 100`,
    ])
  })

  todo("CannotDecodeHeaderRow")
  todo("CannotDecodeCellVariantId")
  todo("CannotDecodeCellStock")
  todo("EmptyCellStock")
  todo("EmptyCellVariantId")
})

todo("mapQueryCatalogInventorySheetParseAndDecodeError")

test("mapQueryCatalogInventoryAuditError", () => {
  let {mapQueryCatalogInventoryAuditError} = module(CatalogInventoryImportPage)

  expect(
    mapQueryCatalogInventoryAuditError([], ~headerOffset=0, ~inventoryItems=[]),
  )->toStrictEqual(None)

  expect(
    mapQueryCatalogInventoryAuditError(
      [CatalogInventoryImportPage.CatalogInventoryAuditRequest.NotFoundVariant(Uuid.make())],
      ~headerOffset=0,
      ~inventoryItems=[],
    ),
  )->toStrictEqual(None)

  expect(
    mapQueryCatalogInventoryAuditError(
      [],
      ~headerOffset=0,
      ~inventoryItems=[{variantId: "", stock: 0.}],
    ),
  )->toStrictEqual(None)

  expect(
    mapQueryCatalogInventoryAuditError(
      [CatalogInventoryImportPage.CatalogInventoryAuditRequest.NotFoundVariant(Uuid.make())],
      ~headerOffset=0,
      ~inventoryItems=[{variantId: "", stock: 0.}],
    ),
  )->toStrictEqual(None)

  let uuid = Uuid.make()
  expect(
    mapQueryCatalogInventoryAuditError(
      [
        CatalogInventoryImportPage.CatalogInventoryAuditRequest.NotFoundVariant(Uuid.make()),
        NotFoundVariant(uuid),
      ],
      ~headerOffset=0,
      ~inventoryItems=[{variantId: uuid->Uuid.toString, stock: 0.}],
    ),
  )->toStrictEqual(Some([`Incorrect reference ID "` ++ uuid->Uuid.toString ++ `" at line 1`]))

  let uuidA = Uuid.make()
  let uuidB = Uuid.make()
  expect(
    mapQueryCatalogInventoryAuditError(
      [
        CatalogInventoryImportPage.CatalogInventoryAuditRequest.NotFoundVariant(uuidA),
        NotFoundVariant(uuidB),
      ],
      ~headerOffset=0,
      ~inventoryItems=[
        {variantId: uuidA->Uuid.toString, stock: 0.},
        {variantId: uuidB->Uuid.toString, stock: 0.},
      ],
    ),
  )->toStrictEqual(
    Some([
      `Incorrect reference ID "` ++ uuidA->Uuid.toString ++ `" at line 1`,
      `Incorrect reference ID "` ++ uuidB->Uuid.toString ++ `" at line 2`,
    ]),
  )
})

todo("catalogInventoryImportRequest")
todo("catalogInventoryAuditRequest")

todo("catalogInventoryProcessAudit")

// Some integration tests to ensure basic interactions
// with the other components and gateway api be valuable
// at some point.
todo("Component")
