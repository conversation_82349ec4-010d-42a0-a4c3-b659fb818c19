open Vitest
open TestingLibraryReact

let mockTypename = () => ""

module MockDailyCashFlowsQuery = {
  open! AnalyticsCashFlowPage.DailyCashFlowsQuery

  let mockCashFlowStaff = (~id="", ~name="", ()): t_dailyShopCashFlows_staff => {
    __typename: mockTypename(),
    id,
    name,
  }

  let mockCashFlowDevice = (~id="", ~name="", ~slug="slug", ()): t_dailyShopCashFlows_device => {
    __typename: mockTypename(),
    id,
    name,
    slug,
  }

  let mockDailyShopCashFlow = (
    ~id="",
    ~createdAt=Js.Date.fromFloat(-1.),
    ~amount=0.,
    ~kind=#INFLOW,
    ~reason="",
    ~staff=?,
    ~device=mockCashFlowDevice(),
    (),
  ): t_dailyShopCashFlows => {
    __typename: mockTypename(),
    id,
    createdAt,
    amount,
    kind,
    reason,
    staff,
    device,
  }

  let mockDailyShopCashFlows = (~dailyShopCashFlows=[], ()): t => {
    dailyShopCashFlows: dailyShopCashFlows,
  }
}

module MockDailyPaymentsQuery = {
  open! AnalyticsCashFlowPage.DailyPaymentsQuery

  let mockInvoiceStaff = (
    ~id="",
    ~name="",
    (),
  ): t_dailyShopPayments_InvoicePayment_invoice_staff => {
    __typename: mockTypename(),
    id,
    name,
  }

  let mockInvoiceDevice = (
    ~id="",
    ~name="",
    ~slug="slug",
    (),
  ): t_dailyShopPayments_InvoicePayment_device => {
    __typename: mockTypename(),
    id,
    name,
    slug,
  }

  let mockTransformedSalesReceipt = (
    ~id="",
    ~name="",
    ~createdAt=Js.Date.fromFloat(-1.),
    (),
  ): t_dailyShopPayments_InvoicePayment_invoice_salesReceipt => {
    __typename: mockTypename(),
    id,
    name,
    createdAt,
  }

  let mockInvoice = (
    ~id="",
    ~name="",
    ~customerName="",
    ~staff=mockInvoiceStaff(),
    ~salesReceipt=?,
    (),
  ): t_dailyShopPayments_InvoicePayment_invoice => {
    __typename: mockTypename(),
    id,
    name,
    customerName,
    staff,
    salesReceipt,
  }

  let mockInvoicePayment = (
    ~invoiceId="",
    ~invoiceCreatedAt=Js.Date.fromFloat(-1.),
    ~invoiceMethod=#CASH,
    ~invoiceAmountReceived=0.,
    ~invoiceAmountReturned=0.,
    ~device=mockInvoiceDevice(),
    ~invoice=mockInvoice(),
    (),
  ): t_dailyShopPayments =>
    #InvoicePayment({
      __typename: mockTypename(),
      invoiceId,
      invoiceCreatedAt,
      invoiceMethod,
      invoiceAmountReceived,
      invoiceAmountReturned,
      device,
      invoice,
    })

  let mockSalesReceiptCustomer = (
    ~id="",
    ~lastName=?,
    ~firstName=?,
    ~companyName=?,
    (),
  ): t_dailyShopPayments_SalesReceiptPayment_salesReceipt_customer => {
    __typename: mockTypename(),
    id,
    lastName,
    firstName,
    companyName,
  }

  let mockSalesReceiptStaff = (
    ~id="",
    ~name="",
    (),
  ): t_dailyShopPayments_SalesReceiptPayment_salesReceipt_staff => {
    __typename: mockTypename(),
    id,
    name,
  }

  let mockSalesReceiptDevice = (
    ~id="",
    ~name="",
    ~slug="slug",
    (),
  ): t_dailyShopPayments_SalesReceiptPayment_salesReceipt_device => {
    __typename: mockTypename(),
    id,
    name,
    slug,
  }

  let mockTransformedInvoice = (
    ~id="",
    ~name="",
    ~createdAt=Js.Date.fromFloat(-1.),
    (),
  ): t_dailyShopPayments_SalesReceiptPayment_salesReceipt_invoice => {
    __typename: mockTypename(),
    id,
    name,
    createdAt,
  }

  let mockSalesReceipt = (
    ~id="",
    ~name="",
    ~customer=?,
    ~staff=mockSalesReceiptStaff(),
    ~device=mockSalesReceiptDevice(),
    ~invoice=?,
    (),
  ): t_dailyShopPayments_SalesReceiptPayment_salesReceipt => {
    __typename: mockTypename(),
    id,
    name,
    customer,
    staff,
    device,
    invoice,
  }

  let mockSalesReceiptPayment = (
    ~salesReceiptId="",
    ~salesReceiptCreatedAt=Js.Date.fromFloat(-1.),
    ~salesReceiptMethod=#CASH,
    ~salesReceiptAmountReceived=0.,
    ~salesReceiptAmountReturned=0.,
    ~salesReceipt=mockSalesReceipt(),
    (),
  ): t_dailyShopPayments =>
    #SalesReceiptPayment({
      __typename: mockTypename(),
      salesReceiptId,
      salesReceiptCreatedAt,
      salesReceiptMethod,
      salesReceiptAmountReceived,
      salesReceiptAmountReturned,
      salesReceipt,
    })

  let mockRefundReceiptCustomer = (
    ~id="",
    ~lastName=?,
    ~firstName=?,
    ~companyName=?,
    (),
  ): t_dailyShopPayments_RefundReceiptPayment_refundReceipt_customer => {
    __typename: mockTypename(),
    id,
    lastName,
    firstName,
    companyName,
  }

  let mockRefundReceiptStaff = (
    ~id="",
    ~name="",
    (),
  ): t_dailyShopPayments_RefundReceiptPayment_refundReceipt_staff => {
    __typename: mockTypename(),
    id,
    name,
  }

  let mockRefundReceiptDevice = (
    ~id="",
    ~name="",
    ~slug="slug",
    (),
  ): t_dailyShopPayments_RefundReceiptPayment_refundReceipt_device => {
    __typename: mockTypename(),
    id,
    name,
    slug,
  }

  let mockRefundReceipt = (
    ~id="",
    ~name="",
    ~customer=?,
    ~staff=mockRefundReceiptStaff(),
    ~device=mockRefundReceiptDevice(),
    (),
  ): t_dailyShopPayments_RefundReceiptPayment_refundReceipt => {
    __typename: mockTypename(),
    id,
    name,
    staff,
    customer,
    device,
  }

  let mockRefundReceiptPayment = (
    ~refundReceiptId="",
    ~refundReceiptCreatedAt=Js.Date.fromFloat(-1.),
    ~refundReceiptMethod=#CASH,
    ~refundReceiptAmountReceived=0.,
    ~refundReceiptAmountReturned=0.,
    ~refundReceipt=mockRefundReceipt(),
    (),
  ): t_dailyShopPayments =>
    #RefundReceiptPayment({
      __typename: mockTypename(),
      refundReceiptId,
      refundReceiptCreatedAt,
      refundReceiptMethod,
      refundReceiptAmountReceived,
      refundReceiptAmountReturned,
      refundReceipt,
    })

  let mockDailyShopPayments = (~dailyShopPayments=[], ()): t => {
    dailyShopPayments: dailyShopPayments,
  }
}

let mockTableRow = (
  ~id,
  ~name={AnalyticsCashFlowPage.TableRow.value: ""},
  ~flowType=AnalyticsCashFlowPage.FlowType.Invoice,
  ~createdAt=Js.Date.fromFloat(-1.),
  ~amount=0.,
  ~paymentMethod=PaymentMethod.Cash,
  ~customerNameOrReason="",
  ~staffName="",
  ~device={AnalyticsCashFlowPage.TableRow.id: "", label: ""},
  (),
) => {
  AnalyticsCashFlowPage.TableRow.id,
  flowType,
  name,
  createdAt,
  amount,
  paymentMethod,
  customerNameOrReason,
  staffName,
  device,
}

describe("FlowType", () => {
  open AnalyticsCashFlowPage.FlowType

  testEach2([
    (Receipt, "Receipts & refunds"),
    (Invoice, "Invoices & credit notes"),
    (Cashflow, "Cash activities"),
  ])(."toLabel", (input, output) => expect(toLabel(input))->toBe(output))
})

describe("DeviceSelect", () => {
  open AnalyticsCashFlowPage
  open! AnalyticsCashFlowPage.DeviceSelect

  let mockDevice = (~id, ~name, ~slug, ~shopId) => {
    Query.__typename: mockTypename(),
    id,
    name,
    slug,
    shop: {
      __typename: mockTypename(),
      id: shopId,
    },
  }
  let mockDevices = devices => {
    Query.devices: {
      __typename: mockTypename(),
      edges: devices->Array.map(device => {
        Query.__typename: mockTypename(),
        node: device,
      }),
    },
  }

  let devices = mockDevices([
    mockDevice(
      ~shopId="shop-id-b",
      ~id="device-id-c",
      ~name="device-name-c",
      ~slug="device-slug-c",
    ),
    mockDevice(
      ~shopId="shop-id-a",
      ~id="device-id-b",
      ~name="device-name-b",
      ~slug="device-slug-b",
    ),
    mockDevice(~shopId="shop-id-a", ~id="device-id-e", ~name="", ~slug="W"),
    mockDevice(~shopId="shop-id-a", ~id="device-id-d", ~name="", ~slug="d"),
    mockDevice(~shopId="shop-id-a", ~id="device-id-f", ~name="", ~slug="winopay"),
    mockDevice(
      ~shopId="shop-id-a",
      ~id="device-id-a",
      ~name="device-name-a",
      ~slug="device-slug-a",
    ),
  ]).devices

  test("sortedCashRegistersSelectItemsFromDevicesEdges", () => {
    let {sortedCashRegistersSelectItemsFromDevicesEdges} = module(
      AnalyticsCashFlowPage.DeviceSelect
    )

    expect(
      sortedCashRegistersSelectItemsFromDevicesEdges(devices.edges, ~shopId="shop-id-a"),
    )->toStrictEqual([
      {
        key: "device-id-a",
        label: "device-slug-a - device-name-a",
        value: Some({
          id: "device-id-a",
          label: "device-slug-a - device-name-a",
        }),
      },
      {
        key: "device-id-b",
        label: "device-slug-b - device-name-b",
        value: Some({
          id: "device-id-b",
          label: "device-slug-b - device-name-b",
        }),
      },
    ])
  })

  test("servicesSelectItemsFromDevicesEdges", () => {
    let {servicesSelectItemsFromDevicesEdges} = module(AnalyticsCashFlowPage.DeviceSelect)

    expect(servicesSelectItemsFromDevicesEdges(devices.edges, ~shopId="shop-id-a"))->toStrictEqual([
      {
        key: "device-id-d",
        label: "dashboard.wino.fr",
        value: Some({
          id: "device-id-d",
          label: "dashboard.wino.fr",
        }),
      },
      {
        key: "device-id-f",
        label: "WinoPay",
        value: Some({
          id: "device-id-f",
          label: "WinoPay",
        }),
      },
    ])
  })

  let userEvent = TestingLibraryEvent.setup()

  testPromise("component", async () => {
    let onChange = fn1(ignore)

    let {rerender} = render(
      <DeviceSelect
        shopId="mock-shop-id"
        value=None
        devicesAsyncResult=AsyncData.Loading
        onChange={onChange->fn}
      />,
    )

    let triggerElement = screen->getByRoleExn(#button)

    expect(triggerElement)->toBeVisible
    expect(triggerElement)->toHaveTextContent("All")
    expect(onChange)->toHaveBeenCalledTimes(0)

    await userEvent->TestingLibraryEvent.click(triggerElement)

    let listboxElement = screen->getByRoleExn(#listbox)

    expect(listboxElement)->toBeVisible

    let (sectionAll, sectionCashRegisters, sectionServices) =
      within(listboxElement)->getAllByRoleExn3(#presentation)

    expect(within(sectionAll)->getByTextExn("All"))->toBeVisible
    expect(within(sectionAll)->queryByRole(#option))->toBeDefined
    expect(within(sectionCashRegisters)->queryByRole(#option))->Vitest.not->toBeDefined
    expect(within(sectionServices)->queryByRole(#option))->Vitest.not->toBeDefined

    let devicesAsyncResult = AsyncData.Done(
      Ok(
        mockDevices([
          mockDevice(~shopId="", ~id="device-id-a", ~name="device-name-a", ~slug="device-slug-a"),
        ]),
      ),
    )

    rerender(
      <DeviceSelect shopId="mock-shop-id" value=None devicesAsyncResult onChange={onChange->fn} />,
    )->ignore

    let sections = within(listboxElement)->getAllByRoleExn(#presentation)

    expect(sections->Array.length)->toBe(3)

    let (sectionAll, sectionCashRegisters, sectionServices) =
      within(listboxElement)->getAllByRoleExn3(#presentation)

    expect(within(sectionAll)->getByTextExn("All"))->toBeVisible
    expect(within(sectionAll)->queryByRole(#option))->toBeDefined
    expect(within(sectionCashRegisters)->queryByRole(#option))->Vitest.not->toBeDefined
    expect(within(sectionServices)->queryByRole(#option))->Vitest.not->toBeDefined

    let devicesAsyncResult = AsyncData.Done(
      Ok(
        mockDevices([
          mockDevice(~shopId="mock-shop-id", ~id="device-id-d", ~name="", ~slug="W"),
          mockDevice(
            ~shopId="mock-shop-id",
            ~id="device-id-b",
            ~name="device-name-a",
            ~slug="device-slug-ab",
          ),
          mockDevice(~shopId="mock-shop-id", ~id="device-id-e", ~name="", ~slug="d"),
          mockDevice(
            ~shopId="mock-shop-id",
            ~id="device-id-c",
            ~name="device-name-b",
            ~slug="device-slug-ba",
          ),
          mockDevice(
            ~shopId="mock-shop-id",
            ~id="device-id-a",
            ~name="device-name-a",
            ~slug="device-slug-aa",
          ),
          mockDevice(~shopId="mock-shop-id", ~id="device-id-f", ~name="", ~slug="winopay"),
        ]),
      ),
    )

    rerender(
      <DeviceSelect
        shopId="mock-shop-id"
        value=Some({
          AnalyticsCashFlowPage.TableRow.id: "device-id-b",
          label: "device-slug-aa - device-name-a",
        })
        devicesAsyncResult
        onChange={onChange->fn}
      />,
    )->ignore

    let (sectionAll, sectionCashRegisters, _, sectionServices) =
      within(listboxElement)->getAllByRoleExn4(#presentation)

    expect(within(sectionAll)->queryByRole(#option))->toBeDefined
    expect(within(sectionAll)->getByTextExn("All"))->toBeVisible

    expect(within(sectionCashRegisters)->getAllByRoleExn(#option)->Array.length)->toBe(3)
    expect(within(sectionCashRegisters)->getByTextExn("Cash registers"))->toBeVisible
    expect(
      within(sectionCashRegisters)->getByTextExn("device-slug-aa - device-name-a"),
    )->toBeVisible
    expect(within(sectionCashRegisters)->getByRoleExn(#group))->toHaveTextContent(
      "device-slug-ab - device-name-a",
    )
    expect(within(sectionCashRegisters)->getByRoleExn(#group))->toHaveTextContent(
      "device-slug-ba - device-name-b",
    )

    // NOTE - temporary: filtered until duplicates are cleared (backend operation)
    expect(within(sectionServices)->getAllByRoleExn(#option)->Array.length)->toBe(1)
    // expect(within(sectionServices)->getAllByRoleExn(#option)->Array.length)->toBe(2)
    expect(within(sectionServices)->getByTextExn("Services"))->toBeVisible
    // expect(within(sectionServices)->getByTextExn("dashboard.wino.fr"))->toBeVisible
    expect(within(sectionServices)->getByTextExn("WinoPay"))->toBeVisible

    // let (_option1, option2) = within(sectionServices)->getAllByRoleExn2(#option)
    let option = within(sectionServices)->getByRoleExn(#option)

    await userEvent->TestingLibraryEvent.click(option)

    expect(onChange)->toHaveBeenCalledWith1(
      Some({AnalyticsCashFlowPage.TableRow.id: "device-id-f", label: "WinoPay"}),
    )

    rerender(
      <DeviceSelect
        shopId="mock-shop-another-id"
        value=Some({AnalyticsCashFlowPage.TableRow.id: "device-id-f", label: "WinoPay"})
        devicesAsyncResult
        onChange={onChange->fn}
      />,
    )->ignore

    expect(onChange)->toHaveBeenCalledWith1(None)
  })
})

describe("TableRow", () => {
  test("safeSubstract", () => {
    let {safeSubstract} = module(AnalyticsCashFlowPage.TableRow)

    expect(safeSubstract(12., 12.))->toBe(0.)
    expect(safeSubstract(-5.5, -5.5))->toBe(0.)
    expect(safeSubstract(10., 5.5))->toBe(4.5)
    expect(safeSubstract(0.3, 0.1))->toBe(0.2)
    expect(safeSubstract(0.32, 0.99))->toBe(-0.67)
    expect(safeSubstract(100.02, 100.01))->toBe(0.01)
    expect(safeSubstract(5.5, -5.5))->toBe(11.)
    expect(safeSubstract(5.5, 0.))->toBe(5.5)
  })

  test("keyExtractor", () => {
    let {keyExtractor} = module(AnalyticsCashFlowPage.TableRow)

    expect(keyExtractor(mockTableRow(~id="", ())))->toBe("")
    expect(keyExtractor(mockTableRow(~id="row-id", ())))->toBe("row-id")
  })

  describe("customerName", () => {
    let {customerName} = module(AnalyticsCashFlowPage.TableRow)
    it(
      "should formate companyName provided alone",
      () => {
        let inputResult = customerName(
          ~firstName=None,
          ~lastName=None,
          ~companyName=Some("Cave Spirituelle"),
        )
        expect(inputResult)->toBe("Cave Spirituelle")
      },
    )

    it(
      "should formate with first then last name combined if both are provided",
      () => {
        let inputResult = customerName(
          ~firstName=Some("paul"),
          ~lastName=Some("Durand"),
          ~companyName=None,
        )
        expect(inputResult)->toBe("paul DURAND")
      },
    )

    it(
      "should formate with only lastName in uppercase if firstName is not provided",
      () => {
        let inputResult = customerName(~firstName=None, ~lastName=Some("Durand"), ~companyName=None)
        expect(inputResult)->toBe("DURAND")
      },
    )

    it(
      "should formate with a N/A string if no names or companyName are provided",
      () => {
        let inputResult = customerName(~firstName=None, ~lastName=None, ~companyName=None)
        expect(inputResult)->toBe("—")
        expect(inputResult)->toBe(AnalyticsCashFlowPage.nonApplicableStringLiteral)
      },
    )

    it(
      "should formate with companyName even if firstName and lastName are provided",
      () => {
        let inputResult = customerName(
          ~firstName=Some("Paul"),
          ~lastName=Some("DURAND"),
          ~companyName=Some("Cave Spirituelle"),
        )
        expect(inputResult)->toBe("Cave Spirituelle")
      },
    )
  })

  test("mergeRowsByCreatedAt", () => {
    let {mergeRowsByCreatedAt} = module(AnalyticsCashFlowPage.TableRow)

    let cashFlowsRows = [
      {
        AnalyticsCashFlowPage.TableRow.id: "mock-inflow-id",
        flowType: Cashflow,
        name: {value: "Inflow"},
        createdAt: Js.Date.fromString("2024-01-01T22:00:00.000Z"),
        amount: 100.,
        paymentMethod: Cash,
        customerNameOrReason: "Reason 1",
        staffName: "Paul DURAND",
        device: {id: "device-a-id", label: "slug - Device A"},
      },
    ]
    let paymentsRows = [
      {
        AnalyticsCashFlowPage.TableRow.id: "mock-invoice-id",
        name: {value: "My Invoice"},
        flowType: Invoice,
        createdAt: Js.Date.fromString("2024-01-02T02:00:00.000Z"),
        amount: 8.12,
        paymentMethod: Amex,
        customerNameOrReason: "—",
        staffName: "",
        device: {id: "device-id", label: "slug - My Device"},
      },
      {
        id: "mock-sales-receipt-id",
        flowType: Receipt,
        name: {value: "My Sales Receipt"},
        createdAt: Js.Date.fromString("2024-01-02T04:00:00.000Z"),
        amount: -23.98,
        paymentMethod: ContactlessDebitCard,
        customerNameOrReason: "Paul DURAND",
        staffName: "Mike",
        device: {id: "device-id", label: "slug - My Device"},
      },
      {
        id: "mock-refund-receipt-id",
        flowType: Receipt,
        name: {value: "My Refund Receipt"},
        createdAt: Js.Date.fromString("2024-01-01T23:00:00.000Z"),
        amount: 80.99,
        paymentMethod: Lydia,
        customerNameOrReason: "Paul DURAND",
        staffName: "",
        device: {id: "device-id", label: "slug - My Device"},
      },
    ]

    expect(mergeRowsByCreatedAt(~cashFlowsRows, ~paymentsRows))->toStrictEqual([
      {
        id: "mock-sales-receipt-id",
        flowType: Receipt,
        name: {value: "My Sales Receipt"},
        createdAt: Js.Date.fromString("2024-01-02T04:00:00.000Z"),
        amount: -23.98,
        paymentMethod: ContactlessDebitCard,
        customerNameOrReason: "Paul DURAND",
        staffName: "Mike",
        device: {id: "device-id", label: "slug - My Device"},
      },
      {
        id: "mock-invoice-id",
        flowType: Invoice,
        name: {value: "My Invoice"},
        createdAt: Js.Date.fromString("2024-01-02T02:00:00.000Z"),
        amount: 8.12,
        paymentMethod: Amex,
        customerNameOrReason: "—",
        staffName: "",
        device: {id: "device-id", label: "slug - My Device"},
      },
      {
        id: "mock-refund-receipt-id",
        flowType: Receipt,
        name: {value: "My Refund Receipt"},
        createdAt: Js.Date.fromString("2024-01-01T23:00:00.000Z"),
        amount: 80.99,
        paymentMethod: Lydia,
        customerNameOrReason: "Paul DURAND",
        staffName: "",
        device: {id: "device-id", label: "slug - My Device"},
      },
      {
        id: "mock-inflow-id",
        flowType: Cashflow,
        name: {value: "Inflow"},
        createdAt: Js.Date.fromString("2024-01-01T22:00:00.000Z"),
        amount: 100.,
        paymentMethod: Cash,
        customerNameOrReason: "Reason 1",
        staffName: "Paul DURAND",
        device: {id: "device-a-id", label: "slug - Device A"},
      },
    ])

    let cashFlowsRows = [
      {
        AnalyticsCashFlowPage.TableRow.id: "mock-inflow-id",
        flowType: Cashflow,
        name: {value: "Inflow"},
        createdAt: Js.Date.fromString("2024-01-01T22:00:00.000Z"),
        amount: 100.,
        paymentMethod: Cash,
        customerNameOrReason: "Reason 1",
        staffName: "Paul DURAND",
        device: {id: "device-a-id", label: "slug - Device A"},
      },
    ]
    let paymentsRows = [
      {
        AnalyticsCashFlowPage.TableRow.id: "",
        name: {value: "My Invoice"},
        flowType: InvoiceTransformed({
          initialSalesReceiptId: "mock-initial-sales-receipt-id",
          initialSalesReceiptName: "last day payment",
          initialSalesReceiptCreatedAt: Js.Date.fromString("2024-01-01T04:00:00.000Z"),
        }),
        createdAt: Js.Date.fromString("2024-01-02T02:00:00.000Z"),
        amount: 0.,
        paymentMethod: Amex,
        customerNameOrReason: "—",
        staffName: "",
        device: {id: "", label: ""},
      },
      {
        id: "",
        name: {value: ""},
        flowType: InvoiceTransformed({
          initialSalesReceiptId: "mock-initial-sales-receipt-id",
          initialSalesReceiptName: "",
          initialSalesReceiptCreatedAt: Js.Date.fromString("2024-01-03T04:00:00.000Z"),
        }),
        createdAt: Js.Date.fromString("2024-01-02T02:00:00.000Z"),
        amount: 0.,
        paymentMethod: Amex,
        customerNameOrReason: "—",
        staffName: "",
        device: {id: "", label: ""},
      },
      {
        id: "",
        name: {value: ""},
        flowType: InvoiceTransformed({
          initialSalesReceiptId: "mock-initial-sales-receipt-id",
          initialSalesReceiptName: "",
          initialSalesReceiptCreatedAt: Js.Date.fromString("2023-01-02T02:00:00.000Z"),
        }),
        createdAt: Js.Date.fromString("2024-01-02T02:00:00.000Z"),
        amount: 0.,
        paymentMethod: Amex,
        customerNameOrReason: "—",
        staffName: "",
        device: {id: "", label: ""},
      },
      {
        id: "",
        name: {value: ""},
        flowType: InvoiceTransformed({
          initialSalesReceiptId: "mock-initial-sales-receipt-id",
          initialSalesReceiptName: "",
          initialSalesReceiptCreatedAt: Js.Date.fromString("2024-02-02T02:00:00.000Z"),
        }),
        createdAt: Js.Date.fromString("2024-01-02T02:00:00.000Z"),
        amount: 0.,
        paymentMethod: Amex,
        customerNameOrReason: "—",
        staffName: "",
        device: {id: "", label: ""},
      },
      {
        id: "",
        name: {value: ""},
        flowType: InvoiceTransformed({
          initialSalesReceiptId: "mock-initial-sales-receipt-id",
          initialSalesReceiptName: "",
          initialSalesReceiptCreatedAt: Js.Date.fromString("2024-01-01T23:59:59.999Z"),
        }),
        createdAt: Js.Date.fromString("2024-01-02T02:00:00.000Z"),
        amount: 0.,
        paymentMethod: Amex,
        customerNameOrReason: "—",
        staffName: "",
        device: {id: "", label: ""},
      },
      {
        id: "",
        name: {value: ""},
        flowType: InvoiceTransformed({
          initialSalesReceiptId: "mock-initial-sales-receipt-id",
          initialSalesReceiptName: "",
          initialSalesReceiptCreatedAt: Js.Date.fromString("2024-01-01T23:59:59.999Z"),
        }),
        createdAt: Js.Date.fromString("2024-01-02T00:00:00.000Z"),
        amount: 0.,
        paymentMethod: Amex,
        customerNameOrReason: "—",
        staffName: "",
        device: {id: "", label: ""},
      },
      {
        id: "mock-invoice-id",
        name: {value: "My invoice"},
        flowType: InvoiceTransformed({
          initialSalesReceiptId: "mock-initial-sales-receipt-id",
          initialSalesReceiptName: "",
          initialSalesReceiptCreatedAt: Js.Date.fromString("2024-01-02T00:00:00.001Z"),
        }),
        createdAt: Js.Date.fromString("2024-01-02T00:00:00.000Z"),
        amount: -23.98,
        paymentMethod: ContactlessDebitCard,
        customerNameOrReason: "Paul DURAND",
        staffName: "Mike",
        device: {id: "device-id", label: "slug - My Device"},
      },
      {
        id: "mock-sales-receipt-id",
        name: {value: "My sales receipt"},
        flowType: ReceiptTransformed({
          paymentId: "",
          transformedInvoiceName: "",
          transformedInvoiceCreatedAt: Js.Date.fromString("2024-01-01T23:59:59.999Z"),
        }),
        createdAt: Js.Date.fromString("2024-01-02T00:00:00.000Z"),
        amount: 10.,
        paymentMethod: Amex,
        customerNameOrReason: "—",
        staffName: "Lucas",
        device: {id: "device-id", label: "slug - My Device"},
      },
    ]

    expect(mergeRowsByCreatedAt(~cashFlowsRows, ~paymentsRows))->toStrictEqual([
      {
        id: "mock-invoice-id",
        name: {value: "My invoice"},
        flowType: InvoiceTransformed({
          initialSalesReceiptId: "mock-initial-sales-receipt-id",
          initialSalesReceiptName: "",
          initialSalesReceiptCreatedAt: Js.Date.fromString("2024-01-02T00:00:00.001Z"),
        }),
        createdAt: Js.Date.fromString("2024-01-02T00:00:00.000Z"),
        amount: -23.98,
        paymentMethod: ContactlessDebitCard,
        customerNameOrReason: "Paul DURAND",
        staffName: "Mike",
        device: {id: "device-id", label: "slug - My Device"},
      },
      {
        id: "mock-sales-receipt-id",
        name: {value: "My sales receipt"},
        flowType: ReceiptTransformed({
          paymentId: "",
          transformedInvoiceName: "",
          transformedInvoiceCreatedAt: Js.Date.fromString("2024-01-01T23:59:59.999Z"),
        }),
        createdAt: Js.Date.fromString("2024-01-02T00:00:00.000Z"),
        amount: 10.,
        paymentMethod: Amex,
        customerNameOrReason: "—",
        staffName: "Lucas",
        device: {id: "device-id", label: "slug - My Device"},
      },
      {
        id: "mock-inflow-id",
        flowType: Cashflow,
        name: {value: "Inflow"},
        createdAt: Js.Date.fromString("2024-01-01T22:00:00.000Z"),
        amount: 100.,
        paymentMethod: Cash,
        customerNameOrReason: "Reason 1",
        staffName: "Paul DURAND",
        device: {id: "device-a-id", label: "slug - Device A"},
      },
    ])
  })

  test("cashFlowsRowsFromQueryData", () => {
    open MockDailyCashFlowsQuery
    let {cashFlowsRowsFromQueryData} = module(AnalyticsCashFlowPage.TableRow)

    let mockData = mockDailyShopCashFlows(
      ~dailyShopCashFlows=[
        mockDailyShopCashFlow(
          ~id="mock-inflow-id",
          ~createdAt=Js.Date.fromString("2024-01-01T00:00:00.000Z"),
          ~amount=100.,
          ~kind=#INFLOW,
          ~reason="Reason 1",
          ~staff=mockCashFlowStaff(~name="Mike", ()),
          ~device=mockCashFlowDevice(~name="Device A", ()),
          (),
        ),
        mockDailyShopCashFlow(
          ~id="mock-outflow-id",
          ~createdAt=Js.Date.fromString("2024-01-02T00:00:00.000Z"),
          ~amount=50.,
          ~kind=#OUTFLOW,
          ~reason="Reason 2",
          ~device=mockCashFlowDevice(~name="Device B", ()),
          (),
        ),
      ],
      (),
    )

    expect(cashFlowsRowsFromQueryData(mockData))->toStrictEqual([
      {
        id: "mock-inflow-id",
        flowType: Cashflow,
        name: {value: "Inflow"},
        createdAt: Js.Date.fromString("2024-01-01T00:00:00.000Z"),
        amount: 100.,
        paymentMethod: Cash,
        customerNameOrReason: "Reason 1",
        staffName: "Mike",
        device: {id: "", label: "slug - Device A"},
      },
      {
        id: "mock-outflow-id",
        flowType: Cashflow,
        name: {value: "Outflow"},
        createdAt: Js.Date.fromString("2024-01-02T00:00:00.000Z"),
        amount: -50.,
        paymentMethod: Cash,
        customerNameOrReason: "Reason 2",
        staffName: "—",
        device: {id: "", label: "slug - Device B"},
      },
    ])
  })

  test("paymentRowsFromQueryData", () => {
    open MockDailyPaymentsQuery
    let {paymentRowsFromQueryData} = module(AnalyticsCashFlowPage.TableRow)

    let mockData = mockDailyShopPayments(
      ~dailyShopPayments=[
        mockInvoicePayment(
          ~invoiceId="mock-invoice-id",
          ~invoiceCreatedAt=Js.Date.fromString("2024-01-01T00:00:00.000Z"),
          ~invoiceMethod=#AMEX,
          ~invoiceAmountReceived=8.12,
          ~invoiceAmountReturned=0.,
          ~invoice=mockInvoice(
            ~id="mock-invoice-id",
            ~name="My Invoice",
            ~customerName="My customer",
            (),
          ),
          ~device=mockInvoiceDevice(~name="My Device", ()),
          (),
        ),
        mockSalesReceiptPayment(
          ~salesReceiptId="mock-sales-receipt-id",
          ~salesReceiptCreatedAt=Js.Date.fromString("2024-01-02T00:00:00.000Z"),
          ~salesReceiptMethod=#CONTACTLESS_DEBIT_CARD,
          ~salesReceiptAmountReceived=0.,
          ~salesReceiptAmountReturned=23.98,
          ~salesReceipt=mockSalesReceipt(
            ~id="mock-sales-receipt-id",
            ~name="My Sales Receipt",
            ~staff=mockSalesReceiptStaff(~name="Mike", ()),
            ~customer=mockSalesReceiptCustomer(~lastName="DURAND", ~firstName="Paul", ()),
            ~device=mockSalesReceiptDevice(~name="My Device", ()),
            (),
          ),
          (),
        ),
        mockRefundReceiptPayment(
          ~refundReceiptId="mock-refund-receipt-id",
          ~refundReceiptCreatedAt=Js.Date.fromString("2024-01-03T00:00:00.000Z"),
          ~refundReceiptMethod=#LYDIA,
          ~refundReceiptAmountReceived=104.02,
          ~refundReceiptAmountReturned=23.03,
          ~refundReceipt=mockRefundReceipt(
            ~id="mock-refund-receipt-id",
            ~name="My Refund Receipt",
            ~customer=mockRefundReceiptCustomer(~lastName="DURAND", ~firstName="Paul", ()),
            ~device=mockRefundReceiptDevice(~name="My Device", ()),
            (),
          ),
          (),
        ),
      ],
      (),
    )

    expect(paymentRowsFromQueryData(mockData))->toStrictEqual([
      {
        id: "mock-invoice-id",
        flowType: Invoice,
        name: {value: "My Invoice"},
        createdAt: Js.Date.fromString("2024-01-01T00:00:00.000Z"),
        amount: 8.12,
        paymentMethod: Amex,
        customerNameOrReason: "My customer",
        staffName: "",
        device: {id: "", label: "slug - My Device"},
      },
      {
        id: "mock-sales-receipt-id",
        flowType: Receipt,
        name: {value: "My Sales Receipt"},
        createdAt: Js.Date.fromString("2024-01-02T00:00:00.000Z"),
        amount: -23.98,
        paymentMethod: ContactlessDebitCard,
        customerNameOrReason: "Paul DURAND",
        staffName: "Mike",
        device: {id: "", label: "slug - My Device"},
      },
      {
        id: "mock-refund-receipt-id",
        flowType: Receipt,
        name: {value: "My Refund Receipt"},
        createdAt: Js.Date.fromString("2024-01-03T00:00:00.000Z"),
        amount: 80.99,
        paymentMethod: Lydia,
        customerNameOrReason: "Paul DURAND",
        staffName: "",
        device: {id: "", label: "slug - My Device"},
      },
    ])
  })

  test("addCountersToRows", () => {
    let {addCountersToRows} = module(AnalyticsCashFlowPage.TableRow)

    let rows = [
      {
        AnalyticsCashFlowPage.TableRow.id: "mock-invoice-id",
        flowType: Invoice,
        name: {value: "My Invoice"},
        createdAt: Js.Date.fromString("2024-01-01T00:00:00.000Z"),
        amount: 8.12,
        paymentMethod: Amex,
        customerNameOrReason: "—",
        staffName: "",
        device: {id: "device-id", label: "slug - My Device"},
      },
      {
        id: "mock-sales-receipt-id-01",
        flowType: Receipt,
        name: {value: "My Receipt"},
        createdAt: Js.Date.fromString("2024-01-02T00:00:00.000Z"),
        amount: 50.00,
        paymentMethod: ContactlessDebitCard,
        customerNameOrReason: "Paul DURAND",
        staffName: "Mike",
        device: {id: "device-id", label: "slug - My Device"},
      },
      {
        id: "mock-sales-receipt-id-02",
        flowType: Receipt,
        name: {value: "My Receipt"},
        createdAt: Js.Date.fromString("2024-01-03T00:00:00.000Z"),
        amount: 2.00,
        paymentMethod: Cash,
        customerNameOrReason: "Paul DURAND",
        staffName: "",
        device: {id: "device-id", label: "slug - My Device"},
      },
    ]

    expect(addCountersToRows([]))->toStrictEqual([])
    expect(addCountersToRows(rows))->toStrictEqual([
      {
        id: "mock-invoice-id",
        flowType: Invoice,
        name: {value: "My Invoice", count: Obj.magic(None)},
        createdAt: Js.Date.fromString("2024-01-01T00:00:00.000Z"),
        amount: 8.12,
        paymentMethod: Amex,
        customerNameOrReason: "—",
        staffName: "",
        device: {id: "device-id", label: "slug - My Device"},
      },
      {
        id: "mock-sales-receipt-id-01",
        flowType: Receipt,
        name: {value: "My Receipt", count: (1, 2)},
        createdAt: Js.Date.fromString("2024-01-02T00:00:00.000Z"),
        amount: 50.00,
        paymentMethod: ContactlessDebitCard,
        customerNameOrReason: "Paul DURAND",
        staffName: "Mike",
        device: {id: "device-id", label: "slug - My Device"},
      },
      {
        id: "mock-sales-receipt-id-02",
        flowType: Receipt,
        name: {value: "My Receipt", count: (2, 2)},
        createdAt: Js.Date.fromString("2024-01-03T00:00:00.000Z"),
        amount: 2.00,
        paymentMethod: Cash,
        customerNameOrReason: "Paul DURAND",
        staffName: "",
        device: {id: "device-id", label: "slug - My Device"},
      },
    ])
  })

  test("sortRowsByTime", () => {
    let {sortRowsByTime} = module(AnalyticsCashFlowPage.TableRow)

    let rows = [
      {
        AnalyticsCashFlowPage.TableRow.id: "mock-invoice-id",
        flowType: Invoice,
        name: {value: "My Invoice"},
        createdAt: Js.Date.fromString("2024-01-01T00:00:00.000Z"),
        amount: 8.12,
        paymentMethod: Amex,
        customerNameOrReason: "—",
        staffName: "",
        device: {id: "device-id", label: "slug - My Device"},
      },
      {
        id: "mock-refund-receipt-id",
        flowType: Receipt,
        name: {value: "My Refund Receipt"},
        createdAt: Js.Date.fromString("2024-01-03T00:00:00.000Z"),
        amount: 80.99,
        paymentMethod: Lydia,
        customerNameOrReason: "Paul DURAND",
        staffName: "",
        device: {id: "device-id", label: "slug - My Device"},
      },
      {
        id: "mock-sales-receipt-id",
        flowType: Receipt,
        name: {value: "My Sales Receipt"},
        createdAt: Js.Date.fromString("2024-01-02T00:00:00.000Z"),
        amount: -23.98,
        paymentMethod: ContactlessDebitCard,
        customerNameOrReason: "Paul DURAND",
        staffName: "Mike",
        device: {id: "device-id", label: "slug - My Device"},
      },
    ]

    expect(sortRowsByTime(~rows, ~direction=#ascending))->toStrictEqual([
      {
        id: "mock-invoice-id",
        flowType: Invoice,
        name: {value: "My Invoice"},
        createdAt: Js.Date.fromString("2024-01-01T00:00:00.000Z"),
        amount: 8.12,
        paymentMethod: Amex,
        customerNameOrReason: "—",
        staffName: "",
        device: {id: "device-id", label: "slug - My Device"},
      },
      {
        id: "mock-sales-receipt-id",
        flowType: Receipt,
        name: {value: "My Sales Receipt"},
        createdAt: Js.Date.fromString("2024-01-02T00:00:00.000Z"),
        amount: -23.98,
        paymentMethod: ContactlessDebitCard,
        customerNameOrReason: "Paul DURAND",
        staffName: "Mike",
        device: {id: "device-id", label: "slug - My Device"},
      },
      {
        id: "mock-refund-receipt-id",
        flowType: Receipt,
        name: {value: "My Refund Receipt"},
        createdAt: Js.Date.fromString("2024-01-03T00:00:00.000Z"),
        amount: 80.99,
        paymentMethod: Lydia,
        customerNameOrReason: "Paul DURAND",
        staffName: "",
        device: {id: "device-id", label: "slug - My Device"},
      },
    ])

    expect(sortRowsByTime(~rows, ~direction=#descending))->toStrictEqual([
      {
        id: "mock-refund-receipt-id",
        flowType: Receipt,
        name: {value: "My Refund Receipt"},
        createdAt: Js.Date.fromString("2024-01-03T00:00:00.000Z"),
        amount: 80.99,
        paymentMethod: Lydia,
        customerNameOrReason: "Paul DURAND",
        staffName: "",
        device: {id: "device-id", label: "slug - My Device"},
      },
      {
        id: "mock-sales-receipt-id",
        flowType: Receipt,
        name: {value: "My Sales Receipt"},
        createdAt: Js.Date.fromString("2024-01-02T00:00:00.000Z"),
        amount: -23.98,
        paymentMethod: ContactlessDebitCard,
        customerNameOrReason: "Paul DURAND",
        staffName: "Mike",
        device: {id: "device-id", label: "slug - My Device"},
      },
      {
        id: "mock-invoice-id",
        flowType: Invoice,
        name: {value: "My Invoice"},
        createdAt: Js.Date.fromString("2024-01-01T00:00:00.000Z"),
        amount: 8.12,
        paymentMethod: Amex,
        customerNameOrReason: "—",
        staffName: "",
        device: {id: "device-id", label: "slug - My Device"},
      },
    ])
  })

  test("sortRowsByAmount", () => {
    let {sortRowsByAmount} = module(AnalyticsCashFlowPage.TableRow)

    let rows = [
      {
        AnalyticsCashFlowPage.TableRow.id: "mock-invoice-id",
        flowType: Invoice,
        name: {value: "My Invoice"},
        createdAt: Js.Date.fromString("2024-01-01T00:00:00.000Z"),
        amount: 8.12,
        paymentMethod: Amex,
        customerNameOrReason: "—",
        staffName: "",
        device: {id: "device-id", label: "slug - My Device"},
      },
      {
        id: "mock-sales-receipt-id",
        flowType: Receipt,
        name: {value: "My Sales Receipt"},
        createdAt: Js.Date.fromString("2024-01-02T00:00:00.000Z"),
        amount: -23.98,
        paymentMethod: ContactlessDebitCard,
        customerNameOrReason: "Paul DURAND",
        staffName: "Mike",
        device: {id: "device-id", label: "slug - My Device"},
      },
      {
        id: "mock-refund-receipt-id",
        flowType: Receipt,
        name: {value: "My Refund Receipt"},
        createdAt: Js.Date.fromString("2024-01-03T00:00:00.000Z"),
        amount: 80.99,
        paymentMethod: Lydia,
        customerNameOrReason: "Paul DURAND",
        staffName: "",
        device: {id: "device-id", label: "slug - My Device"},
      },
    ]

    expect(sortRowsByAmount(~rows, ~direction=#ascending))->toStrictEqual([
      {
        id: "mock-sales-receipt-id",
        flowType: Receipt,
        name: {value: "My Sales Receipt"},
        createdAt: Js.Date.fromString("2024-01-02T00:00:00.000Z"),
        amount: -23.98,
        paymentMethod: ContactlessDebitCard,
        customerNameOrReason: "Paul DURAND",
        staffName: "Mike",
        device: {id: "device-id", label: "slug - My Device"},
      },
      {
        id: "mock-invoice-id",
        flowType: Invoice,
        name: {value: "My Invoice"},
        createdAt: Js.Date.fromString("2024-01-01T00:00:00.000Z"),
        amount: 8.12,
        paymentMethod: Amex,
        customerNameOrReason: "—",
        staffName: "",
        device: {id: "device-id", label: "slug - My Device"},
      },
      {
        id: "mock-refund-receipt-id",
        flowType: Receipt,
        name: {value: "My Refund Receipt"},
        createdAt: Js.Date.fromString("2024-01-03T00:00:00.000Z"),
        amount: 80.99,
        paymentMethod: Lydia,
        customerNameOrReason: "Paul DURAND",
        staffName: "",
        device: {id: "device-id", label: "slug - My Device"},
      },
    ])

    expect(sortRowsByAmount(~rows, ~direction=#descending))->toStrictEqual([
      {
        id: "mock-refund-receipt-id",
        flowType: Receipt,
        name: {value: "My Refund Receipt"},
        createdAt: Js.Date.fromString("2024-01-03T00:00:00.000Z"),
        amount: 80.99,
        paymentMethod: Lydia,
        customerNameOrReason: "Paul DURAND",
        staffName: "",
        device: {id: "device-id", label: "slug - My Device"},
      },
      {
        id: "mock-invoice-id",
        flowType: Invoice,
        name: {value: "My Invoice"},
        createdAt: Js.Date.fromString("2024-01-01T00:00:00.000Z"),
        amount: 8.12,
        paymentMethod: Amex,
        customerNameOrReason: "—",
        staffName: "",
        device: {id: "device-id", label: "slug - My Device"},
      },
      {
        id: "mock-sales-receipt-id",
        flowType: Receipt,
        name: {value: "My Sales Receipt"},
        createdAt: Js.Date.fromString("2024-01-02T00:00:00.000Z"),
        amount: -23.98,
        paymentMethod: ContactlessDebitCard,
        customerNameOrReason: "Paul DURAND",
        staffName: "Mike",
        device: {id: "device-id", label: "slug - My Device"},
      },
    ])
  })

  describe("filterPaymentsTransformed", () => {
    let {filterPaymentsTransformed} = module(AnalyticsCashFlowPage.TableRow)

    it(
      "should filter out transformed invoice payments when an associated sale receipt payment ID is found in the initialRows",
      () => {
        let rows = [
          {
            AnalyticsCashFlowPage.TableRow.id: "mock-receipt-id-0",
            flowType: ReceiptTransformed({
              paymentId: "mock-receipt-payment-id-0",
              transformedInvoiceName: "My Invoice A",
              transformedInvoiceCreatedAt: Js.Date.fromString("2024-01-01T00:00:00.000Z"),
            }),
            name: {value: "My Receipt A"},
            createdAt: Js.Date.fromString("2024-01-01T00:00:00.000Z"),
            amount: 0.,
            paymentMethod: Amex,
            customerNameOrReason: "—",
            staffName: "",
            device: {id: "device-id", label: "My Device"},
          },
          {
            id: "mock-invoice-id-0",
            flowType: InvoiceTransformed({
              initialSalesReceiptId: "mock-receipt-payment-id-0",
              initialSalesReceiptName: "My Receipt A",
              initialSalesReceiptCreatedAt: Js.Date.fromString("2024-01-01T00:00:00.000Z"),
            }),
            name: {value: "My Invoice A"},
            createdAt: Js.Date.fromString("2024-01-01T00:00:00.000Z"),
            amount: 0.,
            paymentMethod: Amex,
            customerNameOrReason: "—",
            staffName: "",
            device: {id: "device-id", label: "My Device"},
          },
          {
            id: "mock-invoice-id-1",
            flowType: Invoice,
            name: {value: "My Invoice B"},
            createdAt: Js.Date.fromString("2024-01-01T00:00:00.000Z"),
            amount: 0.,
            paymentMethod: Amex,
            customerNameOrReason: "—",
            staffName: "",
            device: {id: "device-id", label: "My Device"},
          },
        ]

        expect(filterPaymentsTransformed(~initialRows=rows, ~rows))->toStrictEqual([
          {
            id: "mock-receipt-id-0",
            flowType: ReceiptTransformed({
              paymentId: "mock-receipt-payment-id-0",
              transformedInvoiceName: "My Invoice A",
              transformedInvoiceCreatedAt: Js.Date.fromString("2024-01-01T00:00:00.000Z"),
            }),
            name: {value: "My Receipt A"},
            createdAt: Js.Date.fromString("2024-01-01T00:00:00.000Z"),
            amount: 0.,
            paymentMethod: Amex,
            customerNameOrReason: "—",
            staffName: "",
            device: {id: "device-id", label: "My Device"},
          },
          {
            id: "mock-invoice-id-1",
            flowType: Invoice,
            name: {value: "My Invoice B"},
            createdAt: Js.Date.fromString("2024-01-01T00:00:00.000Z"),
            amount: 0.,
            paymentMethod: Amex,
            customerNameOrReason: "—",
            staffName: "",
            device: {id: "device-id", label: "My Device"},
          },
        ])

        expect(filterPaymentsTransformed(~initialRows=[], ~rows))->toStrictEqual(rows)
      },
    )

    it(
      "should not filter out if the name is equal but the id different",
      () => {
        let rows = [
          {
            AnalyticsCashFlowPage.TableRow.id: "mock-receipt-id-0",
            flowType: Receipt,
            name: {value: "My Receipt A"},
            createdAt: Js.Date.fromString("2024-01-01T00:00:00.000Z"),
            amount: 0.,
            paymentMethod: Amex,
            customerNameOrReason: "—",
            staffName: "",
            device: {id: "device-id", label: "My Device"},
          },
          {
            id: "mock-invoice-id-0",
            flowType: InvoiceTransformed({
              initialSalesReceiptId: "mock-receipt-id-1",
              initialSalesReceiptName: "My Receipt A",
              initialSalesReceiptCreatedAt: Js.Date.fromString("2024-01-01T00:00:00.000Z"),
            }),
            name: {value: "My Invoice A"},
            createdAt: Js.Date.fromString("2024-01-01T00:00:00.000Z"),
            amount: 0.,
            paymentMethod: Amex,
            customerNameOrReason: "—",
            staffName: "",
            device: {id: "device-id", label: "My Device"},
          },
        ]

        expect(filterPaymentsTransformed(~initialRows=rows, ~rows))->toStrictEqual([
          {
            id: "mock-receipt-id-0",
            flowType: Receipt,
            name: {value: "My Receipt A"},
            createdAt: Js.Date.fromString("2024-01-01T00:00:00.000Z"),
            amount: 0.,
            paymentMethod: Amex,
            customerNameOrReason: "—",
            staffName: "",
            device: {id: "device-id", label: "My Device"},
          },
          {
            id: "mock-invoice-id-0",
            flowType: InvoiceTransformed({
              initialSalesReceiptId: "mock-receipt-id-1",
              initialSalesReceiptName: "My Receipt A",
              initialSalesReceiptCreatedAt: Js.Date.fromString("2024-01-01T00:00:00.000Z"),
            }),
            name: {value: "My Invoice A"},
            createdAt: Js.Date.fromString("2024-01-01T00:00:00.000Z"),
            amount: 0.,
            paymentMethod: Amex,
            customerNameOrReason: "—",
            staffName: "",
            device: {id: "device-id", label: "My Device"},
          },
        ])
      },
    )
  })

  test("filterRowsByFlowType", () => {
    let {filterRowsByFlowType} = module(AnalyticsCashFlowPage.TableRow)

    let rows = [
      {
        AnalyticsCashFlowPage.TableRow.id: "mock-receipt-id-0",
        flowType: ReceiptTransformed({
          paymentId: "mock-receipt-payment-id-0",
          transformedInvoiceName: "My Invoice A",
          transformedInvoiceCreatedAt: Js.Date.fromString("2024-01-01T00:00:00.000Z"),
        }),
        name: {value: "My Receipt A"},
        createdAt: Js.Date.fromString("2024-01-01T00:00:00.000Z"),
        amount: 0.,
        paymentMethod: Amex,
        customerNameOrReason: "—",
        staffName: "",
        device: {id: "device-id", label: "My Device"},
      },
      {
        id: "mock-invoice-id-0",
        flowType: InvoiceTransformed({
          initialSalesReceiptId: "mock-receipt-payment-id-0",
          initialSalesReceiptName: "My Receipt A",
          initialSalesReceiptCreatedAt: Js.Date.fromString("2024-01-01T00:00:00.000Z"),
        }),
        name: {value: "My Invoice A"},
        createdAt: Js.Date.fromString("2024-01-01T00:00:00.000Z"),
        amount: 0.,
        paymentMethod: Amex,
        customerNameOrReason: "—",
        staffName: "",
        device: {id: "device-id", label: "My Device"},
      },
      {
        id: "mock-invoice-id-1",
        flowType: Invoice,
        name: {value: "My Invoice B"},
        createdAt: Js.Date.fromString("2024-01-01T00:00:00.000Z"),
        amount: 0.,
        paymentMethod: Amex,
        customerNameOrReason: "—",
        staffName: "",
        device: {id: "device-id", label: "My Device"},
      },
      {
        id: "mock-receipt-id-1",
        flowType: Receipt,
        name: {value: "My Receipt B"},
        createdAt: Js.Date.fromString("2024-01-01T00:00:00.000Z"),
        amount: 0.,
        paymentMethod: Amex,
        customerNameOrReason: "—",
        staffName: "",
        device: {id: "device-id", label: "My Device"},
      },
      {
        id: "mock-cashflow-id-0",
        flowType: Cashflow,
        name: {value: "My Cashflow B"},
        createdAt: Js.Date.fromString("2024-01-01T00:00:00.000Z"),
        amount: 0.,
        paymentMethod: Amex,
        customerNameOrReason: "—",
        staffName: "",
        device: {id: "device-id", label: "My Device"},
      },
    ]

    expect(filterRowsByFlowType(rows, ~flowTypeFilter=Some(Cashflow)))->toStrictEqual([
      {
        id: "mock-cashflow-id-0",
        flowType: Cashflow,
        name: {value: "My Cashflow B"},
        createdAt: Js.Date.fromString("2024-01-01T00:00:00.000Z"),
        amount: 0.,
        paymentMethod: Amex,
        customerNameOrReason: "—",
        staffName: "",
        device: {id: "device-id", label: "My Device"},
      },
    ])

    expect(filterRowsByFlowType(rows, ~flowTypeFilter=Some(Receipt)))->toStrictEqual([
      {
        AnalyticsCashFlowPage.TableRow.id: "mock-receipt-id-0",
        flowType: ReceiptTransformed({
          paymentId: "mock-receipt-payment-id-0",
          transformedInvoiceName: "My Invoice A",
          transformedInvoiceCreatedAt: Js.Date.fromString("2024-01-01T00:00:00.000Z"),
        }),
        name: {value: "My Receipt A"},
        createdAt: Js.Date.fromString("2024-01-01T00:00:00.000Z"),
        amount: 0.,
        paymentMethod: Amex,
        customerNameOrReason: "—",
        staffName: "",
        device: {id: "device-id", label: "My Device"},
      },
      {
        id: "mock-receipt-id-1",
        flowType: Receipt,
        name: {value: "My Receipt B"},
        createdAt: Js.Date.fromString("2024-01-01T00:00:00.000Z"),
        amount: 0.,
        paymentMethod: Amex,
        customerNameOrReason: "—",
        staffName: "",
        device: {id: "device-id", label: "My Device"},
      },
    ])

    expect(filterRowsByFlowType(rows, ~flowTypeFilter=Some(Invoice)))->toStrictEqual([
      {
        id: "mock-invoice-id-0",
        flowType: InvoiceTransformed({
          initialSalesReceiptId: "mock-receipt-payment-id-0",
          initialSalesReceiptName: "My Receipt A",
          initialSalesReceiptCreatedAt: Js.Date.fromString("2024-01-01T00:00:00.000Z"),
        }),
        name: {value: "My Invoice A"},
        createdAt: Js.Date.fromString("2024-01-01T00:00:00.000Z"),
        amount: 0.,
        paymentMethod: Amex,
        customerNameOrReason: "—",
        staffName: "",
        device: {id: "device-id", label: "My Device"},
      },
      {
        id: "mock-invoice-id-1",
        flowType: Invoice,
        name: {value: "My Invoice B"},
        createdAt: Js.Date.fromString("2024-01-01T00:00:00.000Z"),
        amount: 0.,
        paymentMethod: Amex,
        customerNameOrReason: "—",
        staffName: "",
        device: {id: "device-id", label: "My Device"},
      },
    ])

    expect(
      filterRowsByFlowType(
        rows,
        ~flowTypeFilter=Some(
          ReceiptTransformed({
            paymentId: "mock-receipt-payment-id-0",
            transformedInvoiceName: "My Invoice A",
            transformedInvoiceCreatedAt: Js.Date.fromString("2024-01-01T00:00:00.000Z"),
          }),
        ),
      ),
    )->toStrictEqual([])

    expect(
      filterRowsByFlowType(
        rows,
        ~flowTypeFilter=Some(
          InvoiceTransformed({
            initialSalesReceiptId: "mock-receipt-payment-id-0",
            initialSalesReceiptName: "My Receipt A",
            initialSalesReceiptCreatedAt: Js.Date.fromString("2024-01-01T00:00:00.000Z"),
          }),
        ),
      ),
    )->toStrictEqual([])

    expect(filterRowsByFlowType(rows, ~flowTypeFilter=None))->toStrictEqual(rows)
  })

  test("filterRowsByDevice", () => {
    let {filterRowsByDevice} = module(AnalyticsCashFlowPage.TableRow)

    let rows = [
      {
        AnalyticsCashFlowPage.TableRow.id: "mock-invoice-id-0",
        flowType: Invoice,
        name: {value: "My Invoice"},
        createdAt: Js.Date.fromString("2024-01-01T00:00:00.000Z"),
        amount: 0.,
        paymentMethod: Amex,
        customerNameOrReason: "—",
        staffName: "",
        device: {id: "device-id-1", label: "My Device B"},
      },
      {
        id: "mock-receipt-id-0",
        flowType: Receipt,
        name: {value: "My Receipt"},
        createdAt: Js.Date.fromString("2024-01-01T00:00:00.000Z"),
        amount: 0.,
        paymentMethod: Amex,
        customerNameOrReason: "—",
        staffName: "",
        device: {id: "device-id-0", label: "My Device A"},
      },
      {
        id: "mock-cashflow-id-0",
        flowType: Cashflow,
        name: {value: "My Cashflow"},
        createdAt: Js.Date.fromString("2024-01-01T00:00:00.000Z"),
        amount: 0.,
        paymentMethod: Amex,
        customerNameOrReason: "—",
        staffName: "",
        device: {id: "device-id-0", label: "My Device A"},
      },
    ]

    expect(
      filterRowsByDevice(rows, ~deviceFilter=Some({id: "device-id-0", label: "some random label"})),
    )->toStrictEqual([
      {
        id: "mock-receipt-id-0",
        flowType: Receipt,
        name: {value: "My Receipt"},
        createdAt: Js.Date.fromString("2024-01-01T00:00:00.000Z"),
        amount: 0.,
        paymentMethod: Amex,
        customerNameOrReason: "—",
        staffName: "",
        device: {id: "device-id-0", label: "My Device A"},
      },
      {
        id: "mock-cashflow-id-0",
        flowType: Cashflow,
        name: {value: "My Cashflow"},
        createdAt: Js.Date.fromString("2024-01-01T00:00:00.000Z"),
        amount: 0.,
        paymentMethod: Amex,
        customerNameOrReason: "—",
        staffName: "",
        device: {id: "device-id-0", label: "My Device A"},
      },
    ])

    expect(
      filterRowsByDevice(rows, ~deviceFilter=Some({id: "device-id-99", label: "My Device A"})),
    )->toStrictEqual([])
    expect(filterRowsByDevice(rows, ~deviceFilter=None))->toStrictEqual(rows)
  })
})

describe("Reducer", () => {
  open MockDailyCashFlowsQuery
  open MockDailyPaymentsQuery
  open AnalyticsCashFlowPage.Reducer

  test("FiltersChanged(_)", () => {
    let state = make(initialState, FiltersChanged({paymentMethod: DebitCard}))
    expect(state)->toStrictEqual({
      filters: {paymentMethod: DebitCard},
      searchQuery: "",
      sortDescriptor: {column: "time", direction: #descending},
      asyncResult: NotAsked,
    })

    let fulfilledState = {
      filters: {flowType: Receipt, paymentMethod: Cash},
      searchQuery: "my search query",
      sortDescriptor: {column: "time", direction: #descending},
      asyncResult: Loading,
    }
    expect(make(fulfilledState, FiltersChanged({flowType: Cashflow})))->toStrictEqual({
      filters: {flowType: Cashflow},
      searchQuery: "my search query",
      sortDescriptor: {column: "time", direction: #descending},
      asyncResult: Loading,
    })
  })

  test("SearchQueryChanged(_)", () => {
    let state = make(initialState, SearchQueryChanged("my search query"))
    expect(state)->toStrictEqual({
      filters: {},
      searchQuery: "my search query",
      sortDescriptor: {column: "time", direction: #descending},
      asyncResult: NotAsked,
    })

    let fulfilledState = {
      filters: {},
      searchQuery: "my ",
      sortDescriptor: {column: "time", direction: #descending},
      asyncResult: Loading,
    }
    expect(make(fulfilledState, SearchQueryChanged("my search query")))->toStrictEqual({
      filters: {},
      searchQuery: "my search query",
      sortDescriptor: {column: "time", direction: #descending},
      asyncResult: Loading,
    })
  })

  test("MergeAsyncResultsChanged(_, _)", () => {
    let state = make(initialState, MergeAsyncResultsChanged(Loading, NotAsked))
    expect(state)->toStrictEqual({
      filters: {},
      searchQuery: "",
      sortDescriptor: {column: "time", direction: #descending},
      asyncResult: Loading,
    })

    let state = make(
      initialState,
      MergeAsyncResultsChanged(Done(Ok(mockDailyShopCashFlows())), Loading),
    )
    expect(state)->toStrictEqual({
      filters: {},
      searchQuery: "",
      sortDescriptor: {column: "time", direction: #descending},
      asyncResult: Loading,
    })

    let state = make(
      initialState,
      MergeAsyncResultsChanged(
        Done(Ok(mockDailyShopCashFlows())),
        Done(Ok(mockDailyShopPayments())),
      ),
    )
    expect(state)->toStrictEqual({
      filters: {},
      searchQuery: "",
      sortDescriptor: {column: "time", direction: #descending},
      asyncResult: Done(Ok([])),
    })

    let state = make(
      initialState,
      MergeAsyncResultsChanged(
        Done(
          Ok(
            mockDailyShopCashFlows(
              ~dailyShopCashFlows=[
                mockDailyShopCashFlow(
                  ~id="mock-inflow-id",
                  ~createdAt=Js.Date.fromString("2024-01-01T05:01:01.000Z"),
                  ~amount=100.,
                  ~kind=#INFLOW,
                  ~reason="My Reason",
                  ~staff=mockCashFlowStaff(~name="Mike", ()),
                  ~device=mockCashFlowDevice(~name="My Device", ()),
                  (),
                ),
                mockDailyShopCashFlow(
                  ~id="mock-outflow-id",
                  ~createdAt=Js.Date.fromString("2024-01-01T05:03:03.000Z"),
                  ~amount=50.,
                  ~kind=#OUTFLOW,
                  ~reason="My Reason",
                  ~device=mockCashFlowDevice(~name="My Device", ()),
                  (),
                ),
              ],
              (),
            ),
          ),
        ),
        Done(
          Ok(
            mockDailyShopPayments(
              ~dailyShopPayments=[
                mockInvoicePayment(
                  ~invoiceId="mock-invoice-id",
                  ~invoiceCreatedAt=Js.Date.fromString("2024-01-01T05:02:02.000Z"),
                  ~invoiceMethod=#AMEX,
                  ~invoiceAmountReceived=8.12,
                  ~invoiceAmountReturned=0.,
                  ~invoice=mockInvoice(~id="mock-invoice-id", ~name="My Invoice", ()),
                  ~device=mockInvoiceDevice(~name="My Device", ()),
                  (),
                ),
              ],
              (),
            ),
          ),
        ),
      ),
    )
    expect(state)->toStrictEqual({
      filters: {},
      searchQuery: "",
      sortDescriptor: {column: "time", direction: #descending},
      asyncResult: Done(
        Ok([
          {
            id: "mock-outflow-id",
            flowType: Cashflow,
            name: {value: "Outflow", count: Obj.magic(None)},
            createdAt: Js.Date.fromString("2024-01-01T05:03:03.000Z"),
            amount: -50.,
            paymentMethod: Cash,
            customerNameOrReason: "My Reason",
            staffName: "—",
            device: {id: "", label: "slug - My Device"},
          },
          {
            id: "mock-invoice-id",
            flowType: Invoice,
            name: {value: "My Invoice", count: Obj.magic(None)},
            createdAt: Js.Date.fromString("2024-01-01T05:02:02.000Z"),
            amount: 8.12,
            paymentMethod: Amex,
            customerNameOrReason: "",
            staffName: "",
            device: {id: "", label: "slug - My Device"},
          },
          {
            id: "mock-inflow-id",
            flowType: Cashflow,
            name: {value: "Inflow", count: Obj.magic(None)},
            createdAt: Js.Date.fromString("2024-01-01T05:01:01.000Z"),
            amount: 100.,
            paymentMethod: Cash,
            customerNameOrReason: "My Reason",
            staffName: "Mike",
            device: {id: "", label: "slug - My Device"},
          },
        ]),
      ),
    })
  })

  test("SortingChanged(_)", () => {
    let state = make(
      {
        ...initialState,
        asyncResult: Done(
          Ok([
            {
              id: "mock-invoice-id",
              flowType: Invoice,
              name: {value: "My Invoice", count: Obj.magic(None)},
              createdAt: Js.Date.fromString("2024-01-01T05:02:02.000Z"),
              amount: 8.12,
              paymentMethod: Amex,
              customerNameOrReason: "—",
              staffName: "",
              device: {id: "device-id", label: "slug - My Device"},
            },
            {
              id: "mock-outflow-id",
              flowType: Cashflow,
              name: {value: "Outflow", count: Obj.magic(None)},
              createdAt: Js.Date.fromString("2024-01-01T05:03:03.000Z"),
              amount: -50.,
              paymentMethod: Cash,
              customerNameOrReason: "My Reason",
              staffName: "—",
              device: {id: "device-id", label: "slug - My Device"},
            },
            {
              id: "mock-inflow-id",
              flowType: Cashflow,
              name: {value: "Inflow", count: Obj.magic(None)},
              createdAt: Js.Date.fromString("2024-01-01T05:01:01.000Z"),
              amount: 100.,
              paymentMethod: Cash,
              customerNameOrReason: "My Reason",
              staffName: "Mike",
              device: {id: "device-id", label: "slug - My Device"},
            },
          ]),
        ),
      },
      SortingChanged({column: "amount", direction: #ascending}),
    )

    expect(state)->toStrictEqual({
      filters: {},
      searchQuery: "",
      sortDescriptor: {column: "amount", direction: #ascending},
      asyncResult: Done(
        Ok([
          {
            id: "mock-outflow-id",
            flowType: Cashflow,
            name: {value: "Outflow", count: Obj.magic(None)},
            createdAt: Js.Date.fromString("2024-01-01T05:03:03.000Z"),
            amount: -50.,
            paymentMethod: Cash,
            customerNameOrReason: "My Reason",
            staffName: "—",
            device: {id: "device-id", label: "slug - My Device"},
          },
          {
            id: "mock-invoice-id",
            flowType: Invoice,
            name: {value: "My Invoice", count: Obj.magic(None)},
            createdAt: Js.Date.fromString("2024-01-01T05:02:02.000Z"),
            amount: 8.12,
            paymentMethod: Amex,
            customerNameOrReason: "—",
            staffName: "",
            device: {id: "device-id", label: "slug - My Device"},
          },
          {
            id: "mock-inflow-id",
            flowType: Cashflow,
            name: {value: "Inflow", count: Obj.magic(None)},
            createdAt: Js.Date.fromString("2024-01-01T05:01:01.000Z"),
            amount: 100.,
            paymentMethod: Cash,
            customerNameOrReason: "My Reason",
            staffName: "Mike",
            device: {id: "device-id", label: "slug - My Device"},
          },
        ]),
      ),
    })
  })
})

describe("useSearchFiltering", () => {
  let notFoundAsyncResult = AsyncData.Done(Ok([]))
  let asyncResult = AsyncData.Done(
    Ok([
      {
        AnalyticsCashFlowPage.TableRow.id: "mock-invoice-id",
        flowType: Invoice,
        name: {value: "My Invoice"},
        createdAt: Js.Date.fromString("2024-01-01T02:02:02.000Z"),
        amount: 48.12,
        paymentMethod: ContactlessAmex,
        customerNameOrReason: "Paul DURAND",
        staffName: "Mike",
        device: {id: "device-id", label: "slug - My Device"},
      },
    ]),
  )

  testEachPromise2([
    ("", asyncResult),
    (" ", asyncResult),
    ("     ", notFoundAsyncResult),
    // reference
    ("My Invoice", asyncResult),
    ("My Invoic", notFoundAsyncResult),
    ("nvoice", asyncResult),
    // time
    ("2:02 AM", asyncResult),
    ("2:02AM", asyncResult),
    ("2:", asyncResult),
    ("02 AM", notFoundAsyncResult),
    // amount
    ("€48.12", asyncResult),
    ("€48,12", asyncResult),
    ("€ 48.12 ", asyncResult),
    ("€48", asyncResult),
    ("8.1", notFoundAsyncResult),
    // payment method
    ("amex ctls", asyncResult),
    ("Amex-CTLS", asyncResult),
    ("Amex - CTLS", notFoundAsyncResult),
    ("aMeX", asyncResult),
    ("ctls", asyncResult),
    ("contactless", asyncResult),
    // customer
    ("paul durand", asyncResult),
    ("paul-durand", asyncResult),
    ("Paul DURAND", asyncResult),
    ("pauL DU", asyncResult),
    ("PaulDurand", notFoundAsyncResult),
    ("durand paul", notFoundAsyncResult),
    // staff
    ("mîke", asyncResult),
    ("Mik", asyncResult),
    ("IKE", asyncResult),
    // source
    ("My Device", notFoundAsyncResult),
    ("Device", notFoundAsyncResult),
  ])(."it should return the row when matching data with the query", async (input, expected) => {
    let {useSearchFiltering} = module(AnalyticsCashFlowPage)
    let {result} = renderHook(() => useSearchFiltering(~asyncResult, ~searchQuery=input))
    expect(result.current)->toStrictEqual(expected)
  })
})

let userEvent = TestingLibraryEvent.setup()

describe("OperationMetrics", () => {
  test("safeAdd", () => {
    let {safeAdd} = module(AnalyticsCashFlowPage.OperationMetrics)

    expect(safeAdd(12.5, 12.5))->toBe(25.)
    expect(safeAdd(-5.5, -5.5))->toBe(-11.)
    expect(safeAdd(10., 5.5))->toBe(15.5)
    expect(safeAdd(0.3, 0.1))->toBe(0.4)
    expect(safeAdd(0.32, 0.99))->toBe(1.31)
    expect(safeAdd(99.98, 100.02))->toBe(200.)
    expect(safeAdd(5.5, -5.99))->toBe(-0.49)
    expect(safeAdd(5.5, 0.))->toBe(5.5)
  })

  todo("integration: sorting, monetary total value, operation quantity")
})

module TestableAnalyticsCashFlowLoaded = {
  let {mockShop, mockAuthState} = module(Auth__Mock)

  let shopA = mockShop(~id="shop-id-a", ~name="shop-name-a", ())
  let shopB = mockShop(~id="shop-id-b", ~name="shop-name-b", ())
  let auth = mockAuthState(~activeShop=Some(shopA), ~shops=[shopA, shopB], ())

  @react.component
  let make = (
    ~dailyShopCashFlowsAsyncResult,
    ~dailyShopPaymentsAsyncResult,
    ~date=Js.Date.fromString("2024-01-01T00:00:00.000Z"),
  ) => {
    let (date, setDate) = React.useState(() => date)

    <Providers auth=Logged(auth)>
      <AnalyticsCashFlowPage.AnalyticsCashFlowLoaded
        dailyShopCashFlowsAsyncResult
        dailyShopPaymentsAsyncResult
        shop=Some(shopA)
        date
        onDateChange={value => setDate(_ => value)}
      />
    </Providers>
  }
}

describe("PageLoaded", () => {
  let dailyShopCashFlowsAsyncResult = AsyncData.Done(
    Ok(
      MockDailyCashFlowsQuery.mockDailyShopCashFlows(
        ~dailyShopCashFlows=[
          MockDailyCashFlowsQuery.mockDailyShopCashFlow(
            ~id="mock-inflow-id",
            ~createdAt=Js.Date.fromString("2024-01-01T01:01:01.000Z"),
            ~amount=100.,
            ~kind=#INFLOW,
            ~reason="My Reason",
            ~staff=MockDailyCashFlowsQuery.mockCashFlowStaff(~name="Mike", ()),
            ~device=MockDailyCashFlowsQuery.mockCashFlowDevice(~name="My Device", ()),
            (),
          ),
        ],
        (),
      ),
    ),
  )
  let dailyShopPaymentsAsyncResult = AsyncData.Done(
    Ok(
      MockDailyPaymentsQuery.mockDailyShopPayments(
        ~dailyShopPayments=[
          MockDailyPaymentsQuery.mockInvoicePayment(
            ~invoiceId="mock-invoice-id",
            ~invoiceCreatedAt=Js.Date.fromString("2024-01-01T02:02:02.000Z"),
            ~invoiceMethod=#AMEX,
            ~invoiceAmountReceived=8.12,
            ~invoiceAmountReturned=0.,
            ~invoice=MockDailyPaymentsQuery.mockInvoice(
              ~id="mock-invoice-id",
              ~name="My Invoice",
              ~staff=MockDailyPaymentsQuery.mockInvoiceStaff(~name="Mike", ()),
              ~customerName="My customer",
              (),
            ),
            ~device=MockDailyPaymentsQuery.mockInvoiceDevice(~name="My Device", ()),
            (),
          ),
          MockDailyPaymentsQuery.mockSalesReceiptPayment(
            ~salesReceiptId="mock-receipt-id",
            ~salesReceiptCreatedAt=Js.Date.fromString("2024-01-01T00:00:00.000Z"),
            ~salesReceiptMethod=#DEBIT_CARD,
            ~salesReceiptAmountReceived=14.98,
            ~salesReceiptAmountReturned=0.,
            ~salesReceipt=MockDailyPaymentsQuery.mockSalesReceipt(
              ~id="mock-receipt-id",
              ~name="My Receipt",
              ~customer=MockDailyPaymentsQuery.mockSalesReceiptCustomer(
                ~firstName="Paul",
                ~lastName="DURAND",
                (),
              ),
              ~staff=MockDailyPaymentsQuery.mockSalesReceiptStaff(~name="Chris", ()),
              ~device=MockDailyPaymentsQuery.mockSalesReceiptDevice(~name="My Device", ()),
              (),
            ),
            (),
          ),
        ],
        (),
      ),
    ),
  )

  itPromise("should render the page and interact with filters/search/metric cards", async () => {
    let {unmount} = render(
      <TestableAnalyticsCashFlowLoaded
        dailyShopCashFlowsAsyncResult=AsyncData.Loading dailyShopPaymentsAsyncResult
      />,
    )

    let (shopSelectTrigger, dateSelectTrigger) = screen->getAllByRoleExn2(#button)
    let table = screen->getByRoleWithOptionsExn(#grid, {name: "table"})

    expect(shopSelectTrigger)->toHaveTextContent("Shop: shop-name-a")
    expect(dateSelectTrigger)->toHaveTextContent("Date: January 1, 2024")
    expect(table)->toBeVisible
    expect(screen->getByTextExn("Loading..."))->toBeVisible

    unmount()

    let {unmount} = render(
      <TestableAnalyticsCashFlowLoaded
        dailyShopCashFlowsAsyncResult=AsyncData.Done(
          Ok(MockDailyCashFlowsQuery.mockDailyShopCashFlows()),
        )
        dailyShopPaymentsAsyncResult=AsyncData.Done(
          Ok(MockDailyPaymentsQuery.mockDailyShopPayments()),
        )
        date={Js.Date.fromString("1996-01-01T00:00:00.000Z")}
      />,
    )

    let (shopSelectTrigger, dateSelectTrigger) = screen->getAllByRoleExn2(#button)
    let placeholderNoRow = screen->getByTextExn("No result were found.")
    let table = screen->getByRoleWithOptionsExn(#grid, {name: "table"})

    expect(shopSelectTrigger)->toHaveTextContent("Shop: shop-name-a")
    expect(dateSelectTrigger)->toHaveTextContent("Date: January 1, 1996")
    expect(table)->toBeVisible
    expect(placeholderNoRow)->toBeVisible

    unmount()

    render(
      <TestableAnalyticsCashFlowLoaded
        dailyShopCashFlowsAsyncResult dailyShopPaymentsAsyncResult
      />,
    )->ignore

    let (shopSelectTrigger, dateSelectTrigger, flowTypeSelectTrigger, paymentMethodSelectTrigger) =
      screen->getAllByRoleExn4(#button)
    let table = screen->getByRoleWithOptionsExn(#grid, {name: "table"})

    expect(shopSelectTrigger)->toHaveTextContent("Shop: shop-name-a")
    expect(dateSelectTrigger)->toHaveTextContent("Date: January 1, 2024")
    expect(flowTypeSelectTrigger)->toHaveTextContent("Flow type: All")
    expect(paymentMethodSelectTrigger)->toHaveTextContent("Payment method: All")
    expect(table)->toBeVisible

    let (thead, tbody) = within(table)->getAllByRoleExn2(#rowgroup)

    let headersRow = within(thead)->getByRoleExn(#row)
    let (col1, col2, col3, col4, col5, col6, col7) =
      within(headersRow)->getAllByRoleExn7(#columnheader)

    expect(col1)->toHaveTextContent("No. and type of flow")
    expect(col2)->toHaveTextContent("Time")
    expect(col3)->toHaveTextContent("Amount")
    expect(col4)->toHaveTextContent("Payment method")
    expect(col5)->toHaveTextContent("Customer / Comment")
    expect(col6)->toHaveTextContent("Seller")
    expect(col7)->toHaveTextContent("Source")

    let rowsLength = within(tbody)->getAllByRoleExn(#row)->Array.length
    let (rowA, rowB, rowC) = within(tbody)->getAllByRoleExn3(#row)

    let rowAcol1 = within(rowA)->getByRoleExn(#rowheader)
    let (rowAcol2, rowAcol3, rowAcol4, rowAcol5, rowAcol6, rowAcol7) =
      within(rowA)->getAllByRoleExn6(#gridcell)

    let rowBcol1 = within(rowB)->getByRoleExn(#rowheader)
    let (rowBcol2, rowBcol3, rowBcol4, rowBcol5, rowBcol6, rowBcol7) =
      within(rowB)->getAllByRoleExn6(#gridcell)

    let rowCcol1 = within(rowC)->getByRoleExn(#rowheader)
    let (rowCcol2, rowCcol3, rowCcol4, rowCcol5, rowCcol6, rowCcol7) =
      within(rowC)->getAllByRoleExn6(#gridcell)

    expect(rowA)->toBeVisible
    expect(rowB)->toBeVisible
    expect(rowC)->toBeVisible
    expect(rowsLength)->toBe(3)

    expect(rowAcol1)->toHaveTextContent("My Invoice")
    expect(rowAcol2)->toHaveTextContent("2:02 AM")
    expect(rowAcol3)->toHaveTextContent("€8.12")
    expect(rowAcol4)->toHaveTextContent("Amex")
    expect(rowAcol5)->toHaveTextContent("My customer")
    expect(rowAcol6)->toHaveTextContent("Mike")
    expect(rowAcol7)->toHaveTextContent("My Device")

    expect(rowBcol1)->toHaveTextContent("Inflow")
    expect(rowBcol2)->toHaveTextContent("1:01 AM")
    expect(rowBcol3)->toHaveTextContent("€100.00")
    expect(rowBcol4)->toHaveTextContent("Cash")
    expect(rowBcol5)->toHaveTextContent("My Reason")
    expect(rowBcol6)->toHaveTextContent("Mike")
    expect(rowBcol7)->toHaveTextContent("My Device")

    expect(rowCcol1)->toHaveTextContent("My Receipt")
    expect(rowCcol2)->toHaveTextContent("12:00 AM")
    expect(rowCcol3)->toHaveTextContent("€14.98")
    expect(rowCcol4)->toHaveTextContent("DC")
    expect(rowCcol5)->toHaveTextContent("Paul DURAND")
    expect(rowCcol6)->toHaveTextContent("Chris")
    expect(rowCcol7)->toHaveTextContent("My Device")

    let searchInput = screen->getByRoleExn(#textbox)

    expect(searchInput)->toBeVisible
    await waitFor(() => expect(searchInput)->toHaveFocus)

    await userEvent->TestingLibraryEvent.type_(searchInput, "Inflow")

    let row = await waitForReturn(() => within(tbody)->getByRoleExn(#row))
    let rowsLength = within(tbody)->getAllByRoleExn(#row)->Array.length
    let rowAcol1 = within(row)->getByRoleExn(#rowheader)
    let (rowAcol2, _, _, _, _, _) = within(row)->getAllByRoleExn6(#gridcell)

    expect(rowsLength)->toBe(1)
    expect(rowAcol1)->toHaveTextContent("Inflow")
    expect(rowAcol2)->toHaveTextContent("1:01 AM")

    let clearButton = screen->getByLabelTextExn("close_medium")

    await userEvent->TestingLibraryEvent.click(clearButton)

    await waitFor(
      () => {
        let (rowA, rowB, rowC) = within(tbody)->getAllByRoleExn3(#row)
        let rowsLength = within(tbody)->getAllByRoleExn(#row)->Array.length
        let rowAcol1 = within(rowA)->getByRoleExn(#rowheader)
        let rowBcol1 = within(rowB)->getByRoleExn(#rowheader)
        let rowCcol1 = within(rowC)->getByRoleExn(#rowheader)

        expect(rowsLength)->toBe(3)
        expect(rowAcol1)->toHaveTextContent("My Invoice")
        expect(rowBcol1)->toHaveTextContent("Inflow")
        expect(rowCcol1)->toHaveTextContent("My Receipt")
      },
    )

    await userEvent->TestingLibraryEvent.click(flowTypeSelectTrigger)

    screen->getByRoleWithOptionsExn(#option, {name: "All 3"})->ignore

    await userEvent->TestingLibraryEvent.click(
      screen->getByRoleWithOptionsExn(#option, {name: "Cash activities 1"}),
    )

    expect(flowTypeSelectTrigger)->toHaveTextContent("Flow type: Cash activities")

    let row = await waitForReturn(() => within(tbody)->getByRoleExn(#row))
    let rowsLength = within(tbody)->getAllByRoleExn(#row)->Array.length
    let rowAcol1 = within(row)->getByRoleExn(#rowheader)

    expect(rowsLength)->toBe(1)
    expect(rowAcol1)->toHaveTextContent("Inflow")

    await userEvent->TestingLibraryEvent.click(paymentMethodSelectTrigger)
    await userEvent->TestingLibraryEvent.click(
      screen->getByRoleWithOptionsExn(#option, {name: "Cash"}),
    )

    expect(paymentMethodSelectTrigger)->toHaveTextContent("Payment method: Cash")

    let row = await waitForReturn(() => within(tbody)->getByRoleExn(#row))
    let rowsLength = within(tbody)->getAllByRoleExn(#row)->Array.length
    let rowAcol1 = within(row)->getByRoleExn(#rowheader)
    let (_, _, rowAcol4, _, _, _) = within(row)->getAllByRoleExn6(#gridcell)

    expect(rowsLength)->toBe(1)
    expect(rowAcol1)->toHaveTextContent("Inflow")
    expect(rowAcol4)->toHaveTextContent("Cash")

    await userEvent->TestingLibraryEvent.click(paymentMethodSelectTrigger)
    await userEvent->TestingLibraryEvent.click(
      screen->getByRoleWithOptionsExn(#option, {name: "Paypal"}),
    )

    expect(paymentMethodSelectTrigger)->toHaveTextContent("Payment method: Paypal")

    let table = await waitForReturn(() => screen->getByRoleWithOptionsExn(#grid, {name: "table"}))
    let placeholderNoRow = screen->getByTextExn("No result were found.")
    let clearButton =
      screen->getByRoleWithOptionsExn(#button, {name: "Clear search query and filters"})

    expect(table)->toBeVisible
    expect(placeholderNoRow)->toBeVisible
    expect(clearButton)->toBeVisible

    await userEvent->TestingLibraryEvent.click(
      screen->getByRoleWithOptionsExn(#button, {name: "Reset"}),
    )

    let table = screen->getByRoleWithOptionsExn(#grid, {name: "table"})
    let (_, tbody) = within(table)->getAllByRoleExn2(#rowgroup)
    let rowsLength = await waitForReturn(() => within(tbody)->getAllByRoleExn(#row)->Array.length)

    expect(rowsLength)->toBe(3)

    let metricCardsTrack = screen->getByLabelTextExn("track")
    let cashMetricCard = within(metricCardsTrack)->getByLabelTextExn("Cash MetricCard")
    let amexMetricCard = within(metricCardsTrack)->getByLabelTextExn("Amex MetricCard")
    let debitCardMetricCard = within(metricCardsTrack)->getByLabelTextExn("Debit Card MetricCard")

    expect(metricCardsTrack)->toBeVisible
    expect(cashMetricCard)->toBeVisible
    expect(amexMetricCard)->toBeVisible
    expect(debitCardMetricCard)->toBeVisible

    await userEvent->TestingLibraryEvent.click(amexMetricCard)

    let rowsLength = await waitForReturn(() => within(tbody)->getAllByRoleExn(#row)->Array.length)
    let (_, rowColAmount, rowColMethod, _, _, _) = within(rowA)->getAllByRoleExn6(#gridcell)

    expect(rowsLength)->toBe(1)

    expect(rowColAmount)->toHaveTextContent("€8.12")
    expect(rowColMethod)->toHaveTextContent("Amex")
    expect(amexMetricCard)->toHaveTextContent("Amex€8.12Operations1")

    await userEvent->TestingLibraryEvent.click(amexMetricCard)

    let rowsLength = await waitForReturn(() => within(tbody)->getAllByRoleExn(#row)->Array.length)

    expect(rowsLength)->toBe(3)
  })
})

let dailyShopPaymentsAsyncResult = AsyncData.Done(
  Ok(
    MockDailyPaymentsQuery.mockDailyShopPayments(
      ~dailyShopPayments=[
        MockDailyPaymentsQuery.mockInvoicePayment(
          ~invoiceId="mock-invoice-id",
          ~invoiceCreatedAt=Js.Date.fromString("2024-01-01T02:02:02.000Z"),
          ~invoiceMethod=#BANK_TRANSFER,
          ~invoiceAmountReceived=300.,
          ~invoiceAmountReturned=0.,
          ~invoice=MockDailyPaymentsQuery.mockInvoice(
            ~id="mock-invoice-id",
            ~name="My Invoice",
            ~staff=MockDailyPaymentsQuery.mockInvoiceStaff(~name="Chris", ()),
            ~salesReceipt=MockDailyPaymentsQuery.mockTransformedSalesReceipt(
              ~id="mock-receipt-id",
              ~name="My Receipt",
              ~createdAt=Js.Date.fromString("2024-01-01T00:00:00.000Z"),
              (),
            ),
            (),
          ),
          ~device=MockDailyPaymentsQuery.mockInvoiceDevice(~name="My Device", ()),
          (),
        ),
        MockDailyPaymentsQuery.mockSalesReceiptPayment(
          ~salesReceiptId="mock-receipt-id",
          ~salesReceiptCreatedAt=Js.Date.fromString("2024-01-01T00:00:00.000Z"),
          ~salesReceiptMethod=#BANK_TRANSFER,
          ~salesReceiptAmountReceived=300.,
          ~salesReceiptAmountReturned=0.,
          ~salesReceipt=MockDailyPaymentsQuery.mockSalesReceipt(
            ~id="mock-receipt-id",
            ~name="My Receipt",
            ~customer=MockDailyPaymentsQuery.mockSalesReceiptCustomer(
              ~firstName="Paul",
              ~lastName="DURAND",
              (),
            ),
            ~staff=MockDailyPaymentsQuery.mockSalesReceiptStaff(~name="Chris", ()),
            ~device=MockDailyPaymentsQuery.mockSalesReceiptDevice(~name="My Device", ()),
            ~invoice=MockDailyPaymentsQuery.mockTransformedInvoice(
              ~id="mock-invoice-id",
              ~name="My Invoice",
              ~createdAt=Js.Date.fromString("2024-01-01T03:00:00.000Z"),
              (),
            ),
            (),
          ),
          (),
        ),
      ],
      (),
    ),
  ),
)

itPromise("should merge the rows with transformed payments", async () => {
  render(
    <TestableAnalyticsCashFlowLoaded
      dailyShopCashFlowsAsyncResult=AsyncData.Done(
        Ok(MockDailyCashFlowsQuery.mockDailyShopCashFlows()),
      )
      dailyShopPaymentsAsyncResult
    />,
  )->ignore

  let table = screen->getByRoleWithOptionsExn(#grid, {name: "table"})
  let (_, tbody) = within(table)->getAllByRoleExn2(#rowgroup)

  let rowsLength = within(tbody)->getAllByRoleExn(#row)->Array.length
  let row = within(tbody)->getByRoleExn(#row)
  let rowColReference = within(row)->getByRoleExn(#rowheader)
  let (rowColTime, rowColAmount, rowColMethod, _, _, _) = within(row)->getAllByRoleExn6(#gridcell)

  expect(rowsLength)->toBe(1)
  expect(rowColReference)->toBeVisible
  expect(rowColTime)->toBeVisible
  expect(rowColAmount)->toBeVisible
  expect(rowColMethod)->toBeVisible
  expect(rowColReference)->toHaveTextContent("My ReceiptMy Invoice")
  expect(rowColTime)->toHaveTextContent("12:00 AM")
  expect(rowColAmount)->toHaveTextContent("€300.00")
  expect(rowColMethod)->toHaveTextContent("Bank transfer")

  let rowColTimeTooltipTrigger = rowColTime->querySelectorUnsafe("svg")

  await userEvent->TestingLibraryEvent.hover(rowColTimeTooltipTrigger)

  let rowColTimeTooltip = await waitForReturn(() => screen->getByRoleExn(#tooltip))
  expect(rowColTimeTooltip)->toHaveTextContent("Transformed into invoice at 3:00 AM")

  let metricCardsTrack = screen->getByLabelTextExn("track")
  let bankTransferMetricCard =
    within(metricCardsTrack)->getByLabelTextExn("Bank Transfer MetricCard")

  expect(metricCardsTrack)->toBeVisible
  expect(bankTransferMetricCard)->toBeVisible
  expect(bankTransferMetricCard)->toHaveTextContent("Bank Transfer€300.00Operations1")
})

let dailyShopPaymentsAsyncResult = AsyncData.Done(
  Ok(
    MockDailyPaymentsQuery.mockDailyShopPayments(
      ~dailyShopPayments=[
        MockDailyPaymentsQuery.mockSalesReceiptPayment(
          ~salesReceiptId="mock-receipt-id",
          ~salesReceiptCreatedAt=Js.Date.fromString("2024-01-01T00:02:00.000Z"),
          ~salesReceiptMethod=#CASH,
          ~salesReceiptAmountReceived=15.40,
          ~salesReceiptAmountReturned=0.,
          ~salesReceipt=MockDailyPaymentsQuery.mockSalesReceipt(
            ~name="My Receipt",
            ~customer=MockDailyPaymentsQuery.mockSalesReceiptCustomer(
              ~firstName="Paul",
              ~lastName="DURAND",
              (),
            ),
            ~staff=MockDailyPaymentsQuery.mockSalesReceiptStaff(~name="Chris", ()),
            ~device=MockDailyPaymentsQuery.mockSalesReceiptDevice(~name="My Device", ()),
            (),
          ),
          (),
        ),
        MockDailyPaymentsQuery.mockSalesReceiptPayment(
          ~salesReceiptId="mock-receipt-id",
          ~salesReceiptCreatedAt=Js.Date.fromString("2024-01-01T00:00:00.000Z"),
          ~salesReceiptMethod=#DEBIT_CARD,
          ~salesReceiptAmountReceived=15.,
          ~salesReceiptAmountReturned=0.,
          ~salesReceipt=MockDailyPaymentsQuery.mockSalesReceipt(
            ~name="My Receipt",
            ~customer=MockDailyPaymentsQuery.mockSalesReceiptCustomer(
              ~firstName="Paul",
              ~lastName="DURAND",
              (),
            ),
            ~staff=MockDailyPaymentsQuery.mockSalesReceiptStaff(~name="Chris", ()),
            ~device=MockDailyPaymentsQuery.mockSalesReceiptDevice(~name="My Device", ()),
            (),
          ),
          (),
        ),
      ],
      (),
    ),
  ),
)

itPromise(
  "should associate the rows with mixed payments (multiple payments in the same receipt)",
  async () => {
    render(
      <TestableAnalyticsCashFlowLoaded
        dailyShopCashFlowsAsyncResult=AsyncData.Done(
          Ok(MockDailyCashFlowsQuery.mockDailyShopCashFlows()),
        )
        dailyShopPaymentsAsyncResult
      />,
    )->ignore

    let table = screen->getByRoleWithOptionsExn(#grid, {name: "table"})
    let (_, tbody) = within(table)->getAllByRoleExn2(#rowgroup)

    let rowsLength = within(tbody)->getAllByRoleExn(#row)->Array.length
    let (rowA, rowB) = within(tbody)->getAllByRoleExn2(#row)

    let rowAcolReference = within(rowA)->getByRoleExn(#rowheader)
    let (_, rowAcolAmount, rowAcolMethod, _, _, _) = within(rowA)->getAllByRoleExn6(#gridcell)
    let rowBcolReference = within(rowB)->getByRoleExn(#rowheader)
    let (_, rowBcolAmount, rowBcolMethod, _, _, _) = within(rowB)->getAllByRoleExn6(#gridcell)

    expect(rowsLength)->toBe(2)

    expect(rowAcolReference)->toBeVisible
    expect(rowAcolAmount)->toBeVisible
    expect(rowAcolMethod)->toBeVisible
    expect(rowBcolReference)->toBeVisible
    expect(rowBcolAmount)->toBeVisible
    expect(rowBcolMethod)->toBeVisible

    expect(rowAcolReference)->toHaveTextContent("My Receipt1 / 2")
    expect(rowAcolAmount)->toHaveTextContent("15.40")
    expect(rowAcolMethod)->toHaveTextContent("Cash")

    expect(rowBcolReference)->toHaveTextContent("My Receipt2 / 2")
    expect(rowBcolAmount)->toHaveTextContent("€15.00")
    expect(rowBcolMethod)->toHaveTextContent("DC")
  },
)
