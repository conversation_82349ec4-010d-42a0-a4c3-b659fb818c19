open Vitest

describe("UserImpersonationSignInRequest", () => {
  let {encodeBodyJson, decodeResult} = module(AuthImpersonationPage.UserImpersonationSignInRequest)

  test("encodeBodyJson", () => {
    let dict = Js.Dict.empty()
    dict->Js.Dict.set("userId", "dummy-userId"->Json.encodeString)
    let encodedBodyJson = dict->Json.encodeDict

    expect(encodeBodyJson(~userId="dummy-userId"))->toStrictEqual(encodedBodyJson)
  })

  test("decodeResult", () => {
    expect(decodeResult("dummy"->Json.encodeString))->toStrictEqual(Error())

    let dummyDict = Js.Dict.empty()
    dummyDict->Js.Dict.set("foo", "bar"->Json.encodeString)
    expect(decodeResult(dummyDict->Json.encodeDict))->toStrictEqual(Error())

    let dummyDict = Js.Dict.empty()
    dummyDict->Js.Dict.set("access_token", 1.->Json.encodeNumber)
    expect(decodeResult(dummyDict->Json.encodeDict))->toStrictEqual(Error())

    let dict = Js.Dict.empty()
    dict->Js.Dict.set("access_token", "dummy-bearer-token"->Json.encodeString)
    expect(decodeResult(dict->Json.encodeDict))->toStrictEqual(Ok("dummy-bearer-token"))
  })
})

// Some integration tests to ensure basic interactions
// with the other components and gateway might be valuable
// at some point.
todo("Integration test")
