open Vitest
open TestingLibraryReact

module Lenses = SettingsShopsPage.ShopEditFormLenses
module Schema = SettingsShopsPage.ShopEditForm.Schema

let {mockShop, mockUser} = module(Auth__Mock)

let mockAuthState = (~shops, ~activeShop) => {
  Auth__Types.user: mockUser(),
  shops,
  activeShop,
}

describe("UpdateShopRequest", () => {
  let {encodeBody, decodeInvalidRequestFailure} = module(SettingsShopsPage.UpdateShopRequest)

  test("encodeBody", () =>
    expect(
      encodeBody(
        ~id="mock-id",
        ~name="mock-name",
        ~address="mock-address",
        ~postalCode="mock-postalCode",
        ~city="mock-city",
        ~cityOfRegistryOffice="mock-cityOfRegistryOffice",
        ~country="mock-country",
        ~phoneNumber="mock-phoneNumber",
        ~email="mock-email",
        ~website="mock-website",
        ~fiscalYearEndClosingMonth="mock-fiscalYearEndClosingMonth",
        ~corporateName="mock-corporateName",
        ~legalRepresentative="mock-legalRepresentative",
        ~legalForm="mock-legalForm",
        ~amountOfShareCapital="mock-amountOfShareCapital",
        ~tvaNumber="mock-tvaNumber",
        ~siretNumber="mock-siretNumber",
        ~rcsNumber="mock-rcsNumber",
        ~apeNafCode="mock-apeNafCode",
        ~bankName="mock-bankName",
        ~bankCode="mock-bankCode",
        ~bankAccountHolder="mock-bankAccountHolder",
        ~bankAccountNumber="mock-bankAccountNumber",
        ~bicCode="mock-bicCode",
        ~ibanNumber="mock-ibanNumber",
      )->Json.stringify,
    )->toStrictEqual(`{"id":"mock-id","name":"mock-name","address":"mock-address","postalCode":"mock-postalCode","city":"mock-city","cityOfRegistryOffice":"mock-cityOfRegistryOffice","country":"mock-country","phoneNumber":"mock-phoneNumber","email":"mock-email","website":"mock-website","fiscalYearEndClosingMonth":"mock-fiscalYearEndClosingMonth","corporateName":"mock-corporateName","legalRepresentative":"mock-legalRepresentative","legalForm":"mock-legalForm","amountOfShareCapital":"mock-amountOfShareCapital","tvaNumber":"mock-tvaNumber","siretNumber":"mock-siretNumber","rcsNumber":"mock-rcsNumber","apeNafCode":"mock-apeNafCode","bankName":"mock-bankName","bankCode":"mock-bankCode","bankAccountHolder":"mock-bankAccountHolder","bankAccountNumber":"mock-bankAccountNumber","bicCode":"mock-bicCode","ibanNumber":"mock-ibanNumber"}`)
  )

  test("decodeInvalidRequestFailure", () => {
    let serverFailure = {
      Request.kind: "ShopArchivedFailure",
      message: "",
      data: None,
    }
    expect(serverFailure->decodeInvalidRequestFailure)->toStrictEqual(ShopArchivedFailure)

    let serverFailure = {
      Request.kind: "NotFoundShopFailure",
      message: "",
      data: None,
    }
    expect(serverFailure->decodeInvalidRequestFailure)->toStrictEqual(NotFoundShopFailure)

    let serverFailure = {
      Request.kind: "MockFailure",
      message: "",
      data: None,
    }
    expect(serverFailure->decodeInvalidRequestFailure)->toStrictEqual(UnknownFailure)
  })
})

describe("ShopEditForm", () => {
  let schema = SettingsShopsPage.schema
  let mockState = (
    ~name="",
    ~address="",
    ~postalCode="",
    ~city="",
    ~phoneNumber="",
    ~email="",
    (),
  ) => {
    SettingsShopsPage.ShopEditFormLenses.name,
    address,
    postalCode,
    city,
    phoneNumber,
    email,
    cityOfRegistryOffice: "",
    country: "",
    website: "",
    fiscalYearEndClosingMonth: "",
    corporateName: "",
    legalRepresentative: "",
    legalForm: "",
    amountOfShareCapital: "",
    tvaNumber: "",
    siretNumber: "",
    rcsNumber: "",
    apeNafCode: "",
    bankName: "",
    bankCode: "",
    bankAccountHolder: "",
    bankAccountNumber: "",
    bicCode: "",
    ibanNumber: "",
  }

  it("should return no errors when all mandatory fields are well filled", () => {
    let values = mockState(
      ~name="mock-name",
      ~address="mock-address",
      ~postalCode="mock-postalcode",
      ~city="mock-city",
      ~phoneNumber="**********",
      ~email="<EMAIL>",
      (),
    )

    expect(Schema.validate(~schema, ~values))->toStrictEqual(Ok())
  })

  it("should return an error when a mandatory field is missing", () => {
    let values = mockState()

    expect(Schema.validate(~schema, ~values))->toStrictEqual(
      Error([
        (Schema.Field(Name), "Please fulfill this field."),
        (Schema.Field(Address), "Please fulfill this field."),
        (Schema.Field(PostalCode), "Please fulfill this field."),
        (Schema.Field(City), "Please fulfill this field."),
        (Schema.Field(PhoneNumber), "This value is not a valid phone number."),
        (Schema.Field(Email), "Invalid email address."),
      ]),
    )
  })
})

describe("SettingsShopsPage", () => {
  module TestableSettingsShopsPage = {
    @react.component
    let make = (~auth, ~updateShopRequest) =>
      <Providers auth={Logged(auth)}>
        <SettingsShopsPage updateShopRequest />
      </Providers>
  }

  let mockUpdateShopRequest = (~futureResult) => {
    (
      ~id as _,
      ~name as _,
      ~address as _,
      ~postalCode as _,
      ~city as _,
      ~cityOfRegistryOffice as _,
      ~country as _,
      ~phoneNumber as _,
      ~email as _,
      ~website as _,
      ~fiscalYearEndClosingMonth as _,
      ~corporateName as _,
      ~legalRepresentative as _,
      ~legalForm as _,
      ~amountOfShareCapital as _,
      ~tvaNumber as _,
      ~siretNumber as _,
      ~rcsNumber as _,
      ~apeNafCode as _,
      ~bankName as _,
      ~bankCode as _,
      ~bankAccountHolder as _,
      ~bankAccountNumber as _,
      ~bicCode as _,
      ~ibanNumber as _,
    ) => Future.makePure(resolve => resolve(futureResult()))
  }

  itPromise("should display select input to select shop on multishop account", async () => {
    let userEvent = TestingLibraryEvent.setup()

    let shopA = mockShop(~id="shop-id-a", ~name="shop-name-a", ())
    let shopB = mockShop(~id="shop-id-b", ~name="shop-name-b", ())
    let shopC = mockShop(~id="shop-id-c", ~name="shop-name-c", ())
    let auth = mockAuthState(~activeShop=Some(shopA), ~shops=[shopA, shopB, shopC])

    let requestResult = fn1(() => Error(None))
    let updateShopRequest = mockUpdateShopRequest(~futureResult=requestResult->fn)

    let _ = <TestableSettingsShopsPage auth updateShopRequest />->render

    let selectShopField = screen->getByRoleWithOptionsExn(#button, {name: "shop-name-a"})

    expect(selectShopField)->toBeVisible

    await userEvent->TestingLibraryEvent.click(selectShopField)

    let optionA = screen->getByRoleWithOptionsExn(#option, {name: "shop-name-a"})
    let optionB = screen->getByRoleWithOptionsExn(#option, {name: "shop-name-b"})
    let optionC = screen->getByRoleWithOptionsExn(#option, {name: "shop-name-c"})

    expect(optionA)->toBeVisible
    expect(optionB)->toBeVisible
    expect(optionC)->toBeVisible
  })

  itPromise("should initialy populate the input fields with the data from first shop", async () => {
    let shopA = mockShop(~id="shop-id-a", ~name="shop-name-a", ~email="shop-email-a", ())
    let shopB = mockShop(~id="shop-id-b", ~name="shop-name-b", ())
    let shopC = mockShop(~id="shop-id-c", ~name="shop-name-c", ())
    let auth = mockAuthState(~activeShop=Some(shopA), ~shops=[shopA, shopB, shopC])

    let requestResult = fn1(() => Error(None))
    let updateShopRequest = mockUpdateShopRequest(~futureResult=requestResult->fn)

    let _ = <TestableSettingsShopsPage auth updateShopRequest />->render

    let inputEmail = screen->getByLabelTextExn("Email")
    let inputPhone = screen->getByLabelTextExn("Phone")
    let inputAddress = screen->getByLabelTextExn("Address")
    let inputPostalCode = screen->getByLabelTextExn("Postal code")
    let inputCity = screen->getByLabelTextExn("City")
    let inputCountry = screen->getByLabelTextExn("Country")

    let inputCorporateName = screen->getByLabelTextExn("Corporate name")
    let inputLegalForm = screen->getByLabelTextExn("Legal form")
    let inputLegalRepresentative = screen->getByLabelTextExn("Legal representative")
    let inputAmountOfShareCapital = screen->getByLabelTextExn("Amount of share capital")
    let inputCityOfRegistrationOffice = screen->getByLabelTextExn("City of registry office")
    let inputRCS = screen->getByLabelTextExn("Company Registration Number")
    let inputSiretNumber = screen->getByLabelTextExn("SIRET number")
    let inputApeNafCode = screen->getByLabelTextExn("APE/NAF code")
    let inputVatNumber = screen->getByLabelTextExn("VAT number")

    let inputBankName = screen->getByLabelTextExn("Bank name")
    let inputBankCode = screen->getByLabelTextExn("Bank code")
    let inputBankAccountHolder = screen->getByLabelTextExn("Bank account holder")
    let inputBankAccountNumber = screen->getByLabelTextExn("Bank account number")
    let inputBicCode = screen->getByLabelTextExn("BIC code")
    let inputIbanNumber = screen->getByLabelTextExn("IBAN number")

    expect(inputEmail)->toHaveValue("shop-email-a")
    expect(inputPhone)->toHaveValue(shopA.phoneNumber)
    expect(inputAddress)->toHaveValue(shopA.address)
    expect(inputPostalCode)->toHaveValue(shopA.postalCode)
    expect(inputCity)->toHaveValue(shopA.city)
    expect(inputCountry)->toHaveTextContent("Select a country")

    expect(inputCorporateName)->toHaveValue(shopA.corporateName)
    expect(inputLegalForm)->toHaveValue(shopA.legalForm->Option.getUnsafe)
    expect(inputLegalRepresentative)->toHaveValue(shopA.legalRepresentative->Option.getUnsafe)
    expect(inputAmountOfShareCapital)->toHaveValue(shopA.amountOfShareCapital->Option.getUnsafe)
    expect(inputCityOfRegistrationOffice)->toHaveValue(shopA.cityOfRegistryOffice->Option.getUnsafe)
    expect(inputRCS)->toHaveValue(shopA.rcsNumber->Option.getUnsafe)
    expect(inputSiretNumber)->toHaveValue(shopA.siretNumber->Option.getUnsafe)
    expect(inputApeNafCode)->toHaveValue(shopA.apeNafCode->Option.getUnsafe)
    expect(inputVatNumber)->toHaveValue(shopA.tvaNumber->Option.getUnsafe)

    expect(inputBankName)->toHaveValue(shopA.bankName->Option.getUnsafe)
    expect(inputBankCode)->toHaveValue(shopA.bankCode->Option.getUnsafe)
    expect(inputBankAccountHolder)->toHaveValue(shopA.bankAccountHolder->Option.getUnsafe)
    expect(inputBankAccountNumber)->toHaveValue(shopA.bankAccountNumber->Option.getUnsafe)
    expect(inputBicCode)->toHaveValue(shopA.bicCode->Option.getUnsafe)
    expect(inputIbanNumber)->toHaveValue(shopA.ibanNumber->Option.getUnsafe)
  })

  itPromise("should reset the form to its initial state on cancel", async () => {
    let userEvent = TestingLibraryEvent.setup()

    let shopA = mockShop()
    let auth = mockAuthState(~activeShop=Some(shopA), ~shops=[shopA])

    let requestResult = fn1(() => Error(None))
    let updateShopRequest = mockUpdateShopRequest(~futureResult=requestResult->fn)

    let _ = <TestableSettingsShopsPage auth updateShopRequest />->render

    let inputEmail = screen->getByLabelTextExn("Email")
    let updatedEmail = "updated-email"

    await userEvent->TestingLibraryEvent.typeWithOptions(
      inputEmail,
      updatedEmail,
      {initialSelectionStart: 0, initialSelectionEnd: 20},
    )

    expect(inputEmail)->toHaveValue(updatedEmail)

    let cancelButton =
      screen->getAllByRoleWithOptionsExn(#button, {name: "Cancel"})->Array.getUnsafe(0)

    await userEvent->TestingLibraryEvent.click(cancelButton)

    expect(inputEmail)->toHaveValue(shopA.email)
  })

  itPromise(
    "should initially populate the input fields with the corresponding values from the selected shop",
    async () => {
      let userEvent = TestingLibraryEvent.setup()

      let shopA = mockShop(~id="shop-id-a", ~name="shop-name-a", ())
      let shopB = mockShop(~id="shop-id-b", ~name="shop-name-b", ~email="shop-email-b", ())
      let shopC = mockShop(~id="shop-id-c", ~name="shop-name-c", ())
      let auth = mockAuthState(~activeShop=Some(shopA), ~shops=[shopA, shopB, shopC])

      let requestResult = fn1(() => Error(None))
      let updateShopRequest = mockUpdateShopRequest(~futureResult=requestResult->fn)

      let _ = <TestableSettingsShopsPage auth updateShopRequest />->render

      let selectShopField = screen->getByRoleWithOptionsExn(#button, {name: "shop-name-a"})

      expect(selectShopField)->toBeVisible

      await userEvent->TestingLibraryEvent.click(selectShopField)

      let optionB = screen->getByRoleWithOptionsExn(#option, {name: "shop-name-b"})

      expect(optionB)->toBeVisible

      await userEvent->TestingLibraryEvent.click(optionB)

      let inputEmail = screen->getByLabelTextExn("Email")
      expect(inputEmail)->toHaveValue("shop-email-b")
    },
  )

  itPromise("should display error when request fails", async () => {
    let userEvent = TestingLibraryEvent.setup()

    let shopA = mockShop(~email="<EMAIL>", ())
    let auth = mockAuthState(~activeShop=Some(shopA), ~shops=[shopA])

    let requestResult = fn1(() => Error(None))
    let updateShopRequest = mockUpdateShopRequest(~futureResult=requestResult->fn)

    let _ = <TestableSettingsShopsPage auth updateShopRequest />->render

    let button = screen->getAllByRoleWithOptionsExn(#button, {name: "Save"})->Array.getUnsafe(0)

    await userEvent->TestingLibraryEvent.click(button)

    expect(
      screen->getByTextExn("An unexpected error occured. Please try again or contact the support."),
    )->toBeVisible
  })

  itPromise("should display form error when email is in wrong format", async () => {
    let userEvent = TestingLibraryEvent.setup()

    let shopA = mockShop(~id="shop-id-a", ~name="shop-name-a", ~email="my-emailwino.fr", ())
    let auth = mockAuthState(~activeShop=Some(shopA), ~shops=[shopA])

    let requestResult = fn1(() => Error(None))
    let updateShopRequest = mockUpdateShopRequest(~futureResult=requestResult->fn)

    let _ = <TestableSettingsShopsPage auth updateShopRequest />->render

    let saveButton = screen->getAllByRoleWithOptionsExn(#button, {name: "Save"})->Array.getUnsafe(0)

    await userEvent->TestingLibraryEvent.click(saveButton)

    expect(
      screen->getByTextExn(
        "There are some errors in the form, please correct them before trying to send it again.",
      ),
    )->toBeVisible
  })

  itPromise("should display success message when request succeeds", async () => {
    let userEvent = TestingLibraryEvent.setup()

    let shopA = mockShop(~id="shop-id-a", ~name="shop-name-a", ~email="<EMAIL>", ())
    let auth = mockAuthState(~activeShop=Some(shopA), ~shops=[shopA])

    let requestResult = fn1(() => Ok())
    let updateShopRequest = mockUpdateShopRequest(~futureResult=requestResult->fn)

    let _ = <TestableSettingsShopsPage auth updateShopRequest />->render

    let saveButton = screen->getAllByRoleWithOptionsExn(#button, {name: "Save"})->Array.getUnsafe(0)

    await userEvent->TestingLibraryEvent.click(saveButton)

    await waitFor(
      () => {
        expect(
          screen->getByTextExn(
            "You have made changes to your store information; please now verify your billing information in Subscription and Billing.",
          ),
        )->toBeVisible
      },
    )
  })
})
