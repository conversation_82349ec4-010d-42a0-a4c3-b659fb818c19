open Vitest

describe("DateRangePreset", () => {
  module DateRangePreset = AccountingPage.DateRangePreset

  let {isCustomOrNotDefined, toString, toDateRange, getCurrentFiscalYearStart, isEqual} = module(
    DateRangePreset
  )

  let now = Js.Date.fromFloat(Js.Date.now())

  test("isCustomOrNotDefined", () => {
    expect(isCustomOrNotDefined(Yesterday))->toStrictEqual(false)
    expect(isCustomOrNotDefined(Yesterday))->toStrictEqual(false)
    expect(isCustomOrNotDefined(LastFiscalYear({fiscalYearOpeningMonth: November})))->toStrictEqual(
      false,
    )
    expect(isCustomOrNotDefined(NotDefined))->toStrictEqual(true)
    expect(isCustomOrNotDefined(Custom(Some(now), None)))->toStrictEqual(true)
  })

  test("isEqual", () => {
    expect(Yesterday->isEqual(Yesterday))->toStrictEqual(true)
    expect(LastWeek->isEqual(LastWeek))->toStrictEqual(true)
    expect(LastMonth->isEqual(LastMonth))->toStrictEqual(true)
    expect(NotDefined->isEqual(NotDefined))->toStrictEqual(true)
    expect(
      CurrentFiscalYear({fiscalYearOpeningMonth: November})->isEqual(
        CurrentFiscalYear({fiscalYearOpeningMonth: November}),
      ),
    )->toStrictEqual(true)
    expect(
      LastFiscalYear({fiscalYearOpeningMonth: November})->isEqual(
        LastFiscalYear({fiscalYearOpeningMonth: November}),
      ),
    )->toStrictEqual(true)
    expect(LastMonth->isEqual(LastWeek))->toStrictEqual(false)
    expect(
      CurrentFiscalYear({fiscalYearOpeningMonth: November})->isEqual(
        CurrentFiscalYear({fiscalYearOpeningMonth: March}),
      ),
    )->toStrictEqual(false)
  })

  test("toString", () => {
    let someDate = Some(Js.Date.makeWithYMD(~year=2024., ~month=10., ~date=1., ()))
    let someOtherDate = Some(Js.Date.makeWithYMD(~year=2024., ~month=11., ~date=4., ()))

    expect(toString(Custom(someDate, None), ~notDefinedPlaceholder=""))->toStrictEqual(
      "Nov 1, 2024 →  …",
    )
    expect(toString(Custom(someDate, someDate), ~notDefinedPlaceholder=""))->toStrictEqual(
      "Nov 1, 2024",
    )
    expect(toString(Custom(someDate, someOtherDate), ~notDefinedPlaceholder=""))->toStrictEqual(
      "Nov 1, 2024 → Dec 4, 2024",
    )
    expect(toString(NotDefined, ~notDefinedPlaceholder="Pick a date range"))->toStrictEqual(
      "Pick a date range",
    )
    expect(toString(NotDefined, ~notDefinedPlaceholder=""))->toStrictEqual("")
    expect(toString(Yesterday, ~notDefinedPlaceholder=""))->toStrictEqual("Yesterday")
    expect(toString(LastWeek, ~notDefinedPlaceholder=""))->toStrictEqual("Last week")
    expect(toString(LastMonth, ~notDefinedPlaceholder=""))->toStrictEqual("Last month")
    expect(
      toString(CurrentFiscalYear({fiscalYearOpeningMonth: April}), ~notDefinedPlaceholder=""),
    )->toStrictEqual("Current fiscal year")
    expect(
      toString(LastFiscalYear({fiscalYearOpeningMonth: April}), ~notDefinedPlaceholder=""),
    )->toStrictEqual("Last fiscal year")
    expect(toString(Custom(None, None), ~notDefinedPlaceholder=""))->toStrictEqual("Custom")
  })

  test("toDateRange", () => {
    let {
      startOfDay,
      endOfDay,
      subDays,
      startOfWeek,
      subWeeks,
      endOfWeek,
      startOfMonth,
      subMonths,
      endOfMonth,
      addMonths,
    } = module(DateHelpers)

    let now = Js.Date.fromFloat(Js.Date.now())

    expect(toDateRange(Yesterday))->toEqual(
      Some((now->startOfDay->subDays(1.), now->subDays(1.)->endOfDay)),
    )

    let expectedStartOfLastWeek = now->subWeeks(1.)->startOfWeek
    let expectedEndOfLastWeek = now->subWeeks(1.)->endOfWeek
    expect(toDateRange(LastWeek))->toEqual(Some((expectedStartOfLastWeek, expectedEndOfLastWeek)))

    let lastMonthRange = toDateRange(LastMonth)
    let expectedStartOfLastMonth = now->subMonths(1.)->startOfMonth
    let expectedEndOfLastMonth = now->subMonths(1.)->endOfMonth
    expect(lastMonthRange)->toEqual(Some((expectedStartOfLastMonth, expectedEndOfLastMonth)))

    let fiscalYearRange = toDateRange(CurrentFiscalYear({fiscalYearOpeningMonth: April}))
    let adjustedYear =
      Js.Date.make() < Js.Date.makeWithYMD(~year=Js.Date.getFullYear(now), ~month=3., ~date=1., ())
        ? Js.Date.getFullYear(now) -. 1.
        : Js.Date.getFullYear(now)

    let startOfFiscalYear = Js.Date.makeWithYMD(~year=adjustedYear, ~month=3., ~date=1., ())
    let endOfFiscalYear = startOfFiscalYear->addMonths(11.)->endOfMonth
    expect(fiscalYearRange)->toEqual(Some((startOfFiscalYear, endOfFiscalYear)))

    let lastFiscalYearRange = toDateRange(LastFiscalYear({fiscalYearOpeningMonth: April}))
    let adjustedYear =
      Js.Date.make() < Js.Date.makeWithYMD(~year=Js.Date.getFullYear(now), ~month=3., ~date=1., ())
        ? Js.Date.getFullYear(now) -. 2.
        : Js.Date.getFullYear(now) -. 1.

    let startOfLastFiscalYear = Js.Date.makeWithYMD(~year=adjustedYear, ~month=3., ~date=1., ())
    let endOfLastFiscalYear = startOfLastFiscalYear->addMonths(11.)->endOfMonth
    expect(lastFiscalYearRange)->toEqual(Some((startOfLastFiscalYear, endOfLastFiscalYear)))

    let startDate = Js.Date.makeWithYMD(~year=2024., ~month=10., ~date=1., ())
    let endDate = Js.Date.makeWithYMD(~year=2024., ~month=11., ~date=4., ())
    let customRange = toDateRange(Custom((Some(startDate), Some(endDate))))
    expect(customRange)->toEqual(Some((startDate, endDate)))

    let customRangeStartOnly = toDateRange(Custom((Some(startDate), None)))
    expect(customRangeStartOnly)->toEqual(None)

    let customRangeNone = toDateRange(Custom((None, None)))
    expect(customRangeNone)->toEqual(None)

    let notDefinedRange = toDateRange(NotDefined)
    expect(notDefinedRange)->toEqual(None)
  })

  test("fromPartialDateRange", () => {
    let {fromPartialDateRange} = module(DateRangePreset)
    let {subWeeks, startOfWeek, endOfWeek, startOfMonth, subMonths, endOfMonth} = module(
      DateHelpers
    )

    let presets = [
      DateRangePreset.Yesterday,
      LastWeek,
      LastMonth,
      CurrentFiscalYear({fiscalYearOpeningMonth: January}),
      LastFiscalYear({fiscalYearOpeningMonth: January}),
    ]

    let yesterday = Js.Date.makeWithYMD(
      ~year=now->Js.Date.getFullYear,
      ~month=now->Js.Date.getMonth,
      ~date=now->Js.Date.getDate -. 1.,
      (),
    )
    expect(fromPartialDateRange((Some(yesterday), Some(yesterday)), presets))->toStrictEqual(
      Yesterday,
    )

    let firstDay = now->subWeeks(1.)->startOfWeek
    let lastDay = now->subWeeks(1.)->endOfWeek
    expect(fromPartialDateRange((Some(firstDay), Some(lastDay)), presets))->toStrictEqual(LastWeek)

    let firstDayOfLastMonth = now->subMonths(1.)->startOfMonth
    let lastDayOfLastMonth = now->subMonths(1.)->endOfMonth
    expect(
      fromPartialDateRange((Some(firstDayOfLastMonth), Some(lastDayOfLastMonth)), presets),
    )->toStrictEqual(LastMonth)

    let customStart = Js.Date.makeWithYMD(~year=2024., ~month=11., ~date=1., ())
    let customEnd = Js.Date.makeWithYMD(~year=2024., ~month=11., ~date=4., ())
    expect(fromPartialDateRange((Some(customStart), Some(customEnd)), presets))->toStrictEqual(
      Custom((Some(customStart), Some(customEnd))),
    )

    let rangeWithNoStart = (None, Some(customEnd))
    expect(fromPartialDateRange(rangeWithNoStart, presets))->toStrictEqual(NotDefined)

    let rangeWithNoEnd = (Some(customStart), None)
    expect(fromPartialDateRange(rangeWithNoEnd, presets))->toStrictEqual(
      Custom(Some(customStart), None),
    )

    let rangeWithNoStartOrEnd = (None, None)
    expect(fromPartialDateRange(rangeWithNoStartOrEnd, presets))->toStrictEqual(NotDefined)
  })

  test("getCurrentFiscalYearStart", () => {
    let now = Js.Date.makeWithYMD(~year=2024., ~month=8., ~date=1., ())

    expect(getCurrentFiscalYearStart(~now, ~fiscalYearOpeningMonth=November))->toStrictEqual(2023.)
    expect(getCurrentFiscalYearStart(~now, ~fiscalYearOpeningMonth=April))->toStrictEqual(2024.)
  })
})

describe("GetShopAccountingExportRequest", () => {
  module GetShopAccountingExportRequest = AccountingPage.GetShopAccountingExportRequest

  test("endpoint", () => {
    let {endpoint} = module(GetShopAccountingExportRequest)
    expect(endpoint(~shopId="mock-shop-id", ~vendor=Excel))->toMatch(
      "accounting-export/mock-shop-id/xlsx-format",
    )
    expect(endpoint(~shopId="mock-shop-id", ~vendor=IsaCompta))->toMatch(
      "accounting-export/mock-shop-id/ecr-format",
    )
  })

  test("encodeBodyJson", () => {
    let {encodeBodyJson} = module(GetShopAccountingExportRequest)
    expect(
      encodeBodyJson(
        ~shopId="mock-shop-id",
        ~dateRange=(
          Js.Date.makeWithYMD(~year=2024., ~month=0., ~date=1., ()),
          Js.Date.makeWithYMD(~year=2024., ~month=0., ~date=1., ()),
        ),
      ),
    )->toStrictEqual(
      Json.fromObjExn({
        "accountingEntriesView": "sales",
        "endDate": *************.,
        "shopId": "mock-shop-id",
        "startDate": *************.,
        "timeZone": "UTC",
      }),
    )
  })

  test("decodeInvalidRequestFailure", () => {
    let {decodeInvalidRequestFailure} = module(GetShopAccountingExportRequest)
    expect(decodeInvalidRequestFailure({kind: "abc", message: "", data: None}))->toStrictEqual(
      UnknownFailure,
    )
    expect(
      decodeInvalidRequestFailure({
        kind: "NotFoundAccountingExportConfiguration",
        message: "",
        data: None,
      }),
    )->toStrictEqual(NotFoundAccountingConfigurationFailure)
    expect(
      decodeInvalidRequestFailure({
        kind: "IsaComptaTaxCodeNotDefined",
        message: "",
        data: None,
      }),
    )->toStrictEqual(MissingConfigurationForIsacomptaVendorFailure)
    expect(
      decodeInvalidRequestFailure({
        kind: "InvalidFiscalExportDateRange",
        message: "",
        data: None,
      }),
    )->toStrictEqual(InvalidFiscalExportDateRangeFailure)
    expect(
      decodeInvalidRequestFailure({
        kind: "InvalidInvoiceTax",
        message: "",
        data: None,
      }),
    )->toStrictEqual(UnknownFailure)
    expect(
      decodeInvalidRequestFailure({
        kind: "InvalidCreditNoteTax",
        message: "",
        data: Some(Json.fromObjExn({"foo": "bar"})),
      }),
    )->toStrictEqual(UnknownFailure)
    expect(
      decodeInvalidRequestFailure({
        kind: "InvalidRefundReceiptTax",
        message: "",
        data: Some(Json.fromObjExn("json")),
      }),
    )->toStrictEqual(UnknownFailure)
    expect(
      decodeInvalidRequestFailure({
        kind: "InvalidInvoiceTax",
        message: "",
        data: Some(
          Json.fromObjExn({
            "name": "mock-name",
            "date": Js.Date.makeWithYMD(~year=2024., ~month=0., ~date=1., ())->Js.Date.getTime,
          }),
        ),
      }),
    )->toStrictEqual(
      InvalidSaleTaxFailure({
        name: "mock-name",
        issueDate: Js.Date.makeWithYMD(~year=2024., ~month=0., ~date=1., ()),
      }),
    )
    expect(
      decodeInvalidRequestFailure({
        kind: "InvalidCreditNoteTax",
        message: "",
        data: Some(
          Json.fromObjExn({
            "name": "mock-name",
            "date": Js.Date.makeWithYMD(~year=2012., ~month=11., ~date=10., ())->Js.Date.getTime,
          }),
        ),
      }),
    )->toStrictEqual(
      InvalidSaleTaxFailure({
        name: "mock-name",
        issueDate: Js.Date.makeWithYMD(~year=2012., ~month=11., ~date=10., ()),
      }),
    )
    expect(
      decodeInvalidRequestFailure({
        kind: "InvalidRefundReceiptTax",
        message: "",
        data: Some(
          Json.fromObjExn({
            "name": "mock-name",
            "date": Js.Date.makeWithYMD(~year=2024., ~month=0., ~date=30., ())->Js.Date.getTime,
          }),
        ),
      }),
    )->toStrictEqual(
      InvalidSaleTaxFailure({
        name: "mock-name",
        issueDate: Js.Date.makeWithYMD(~year=2024., ~month=0., ~date=30., ()),
      }),
    )
  })

  test("decodeRequestError", () => {
    let {decodeRequestError} = module(GetShopAccountingExportRequest)

    expect(decodeRequestError(UnexpectedServerError))->toStrictEqual(UnknownFailure)
    expect(decodeRequestError(InvalidRequestFailures([])))->toStrictEqual(UnknownFailure)
    expect(
      decodeRequestError(InvalidRequestFailures([{kind: "", message: "", data: None}])),
    )->toStrictEqual(UnknownFailure)
    expect(
      decodeRequestError(
        InvalidRequestFailures([
          {
            kind: "IsaComptaTaxCodeNotDefined",
            message: "",
            data: None,
          },
        ]),
      ),
    )->toStrictEqual(MissingConfigurationForIsacomptaVendorFailure)
    expect(
      decodeRequestError(
        InvalidRequestFailures([
          {
            kind: "InvalidInvoiceTax",
            message: "",
            data: Some(
              Json.fromObjExn({
                "name": "mock-name",
                "date": Js.Date.makeWithYMD(~year=2024., ~month=0., ~date=1., ())->Js.Date.getTime,
              }),
            ),
          },
        ]),
      ),
    )->toStrictEqual(
      InvalidSaleTaxFailure({
        name: "mock-name",
        issueDate: Js.Date.makeWithYMD(~year=2024., ~month=0., ~date=1., ()),
      }),
    )
    expect(
      decodeRequestError(
        InvalidRequestFailures([
          {
            kind: "AccountingPaymentExportItemMissingPaymentMethodConfiguration",
            message: "",
            data: Some(
              Json.fromObjExn({
                "paymentMethod": "CASH",
              }),
            ),
          },
        ]),
      ),
    )->toStrictEqual(MissingPaymentMethodAccountFailure(PaymentMethod.Cash))
    expect(
      decodeRequestError(
        InvalidRequestFailures([
          {
            kind: "AccountingPaymentExportItemMissingPaymentMethodConfiguration",
            message: "",
            data: Some(
              Json.fromObjExn({
                "paymentMethod": "Cash",
              }),
            ),
          },
        ]),
      ),
    )->toStrictEqual(UnknownFailure)
  })

  test("decodeResponse", () => {
    let {decodeResponse} = module(GetShopAccountingExportRequest)
    expect(decodeResponse(Json.fromObjExn({"foo": "bar"})))->toStrictEqual(None)
    expect(decodeResponse(Json.fromObjExn({"url": "https://wino.fr"})))->toStrictEqual(
      Some(Url.make("https://wino.fr")),
    )
    expect(decodeResponse(Json.fromObjExn({"url": 0.})))->toStrictEqual(None)
  })

  todo("make")
})

describe("State", () => {
  todo("make")
})

// Some integration tests to ensure basic interactions
// with the other components and gateway api be valuable
// at some point.
todo("Integration test")
