open Vitest
open TestingLibraryReact

describe("SignInRequest", () => {
  let {encodeBody, decodeResult} = module(AuthLoginPage.SignInRequest)

  test("encodeBody", () => {
    expect(encodeBody(~username="dummy-username", ~password="dummy-password"))->toStrictEqual(
      `{"username":"dummy-username","password":"dummy-password"}`->Fetch.Body.string,
    )
  })

  test("decodeResult", () => {
    let json = `{"access_token": "jwt"}`->Json.parseExn
    expect(decodeResult(json))->toStrictEqual(Some("jwt"))

    let json = `{"foo": "bar"}`->Json.parseExn
    expect(decodeResult(json))->toStrictEqual(None)

    let json = "dummy"->Json.encodeString
    expect(decodeResult(json))->toStrictEqual(None)
  })
})

module TestableAuthLoginPage = {
  @react.component
  let make = (~signInRequest, ~history=?) =>
    <Providers auth=Unlogged ?history>
      <AuthLoginPage signInRequest recoveryRoute="" />
    </Providers>
}

let mockSignInRequest = (~futureResult) => {
  (~username as _, ~password as _) => {
    Future.makePure(resolve => resolve(futureResult()))
  }
}

itPromise("should display an error when form validation fails", async () => {
  let userEvent = TestingLibraryEvent.setup()

  let requestResult = fn1(() => Ok("mock-jwt"))
  let signInRequest = mockSignInRequest(~futureResult=requestResult->fn)

  let _ = <TestableAuthLoginPage signInRequest />->render
  let nonValidPassword = "password"

  let inputPassword = screen->getByLabelTextExn("Password")
  expect(inputPassword)->toBeVisible

  await userEvent->TestingLibraryEvent.type_(inputPassword, nonValidPassword)

  let button = screen->getAllByRoleWithOptionsExn(#button, {name: "Sign in"})->Array.getExn(0)
  expect(button)->toBeVisible

  await userEvent->TestingLibraryEvent.click(button)

  expect(
    screen->getByTextExn(
      "There are some errors in the form, please correct them before trying to send it again.",
    ),
  )->toBeVisible
})

itPromise("should display error when the request fails", async () => {
  let userEvent = TestingLibraryEvent.setup()

  let requestResult = fn1(() => Error())
  let signInRequest = mockSignInRequest(~futureResult=requestResult->fn)

  let _ = <TestableAuthLoginPage signInRequest />->render

  let inputEmail = screen->getByLabelTextExn("Email")
  expect(inputEmail)->toBeVisible

  let inputPassword = screen->getByLabelTextExn("Password")
  expect(inputPassword)->toBeVisible

  await userEvent->TestingLibraryEvent.type_(inputEmail, "<EMAIL>")
  await userEvent->TestingLibraryEvent.type_(inputPassword, "mock-password")

  let button = screen->getAllByRoleWithOptionsExn(#button, {name: "Sign in"})->Array.getExn(0)
  expect(button)->toBeVisible

  await userEvent->TestingLibraryEvent.click(button)

  expect(screen->getByTextExn("Incorrect credentials."))->toBeVisible
})
