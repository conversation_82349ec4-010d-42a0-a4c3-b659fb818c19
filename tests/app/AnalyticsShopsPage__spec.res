open Vitest

let {mockShop} = module(Auth__Mock)

let mockRow = (~shopId="", ()) => {
  AnalyticsShopsPage.AnalyticsShopsTableRow.shopId,
  shopName: "",
  ticketsRevenueIncludingTaxes: 0.,
  ticketsRevenueExcludingTaxes: 0.,
  invoicesRevenueIncludingTaxes: 0.,
  invoicesRevenueExcludingTaxes: 0.,
  globalRevenueIncludingTaxes: 0.,
  globalRevenueExcludingTaxes: 0.,
  margin: 0.,
  marginRate: 0.,
  markupRate: 0.,
  salesCount: 0,
  salesIndex: 0.,
  shoppingCartAverage: 0.,
  productsSoldCount: 0,
  totalAmountOfTaxes: 0.,
  totalPurchaseCost: 0.,
  currency: Intl.currencyEur,
}

let mockTypename = () => ""

let mockQueryDataEdge = (
  ~shopId="",
  ~shopName="",
  ~ticketsRevenueIncludingTaxes=0.,
  ~ticketsRevenueExcludingTaxes=0.,
  ~invoicesRevenueIncludingTaxes=0.,
  ~invoicesRevenueExcludingTaxes=0.,
  ~globalRevenueIncludingTaxes=0.,
  ~globalRevenueExcludingTaxes=0.,
  ~salesCount=0,
  ~margin=0.,
  ~marginRate=0.,
  ~markupRate=0.,
  ~salesIndex=0.,
  ~shoppingCartAverage=0.,
  ~productsSoldCount=0,
  ~totalAmountOfTaxes=0.,
  ~totalPurchaseCost=0.,
  (),
) => {
  AnalyticsShopsPage.AnalyticsShopsQuery.node: {
    shop: {
      id: shopId,
      name: shopName,
      __typename: mockTypename(),
    },
    ticketsRevenue: {
      includingTaxes: ticketsRevenueIncludingTaxes,
      excludingTaxes: ticketsRevenueExcludingTaxes,
      __typename: mockTypename(),
    },
    invoicesRevenue: {
      includingTaxes: invoicesRevenueIncludingTaxes,
      excludingTaxes: invoicesRevenueExcludingTaxes,
      __typename: mockTypename(),
    },
    globalRevenue: {
      includingTaxes: globalRevenueIncludingTaxes,
      excludingTaxes: globalRevenueExcludingTaxes,
      __typename: mockTypename(),
    },
    salesCount,
    salesIndex,
    shoppingCartAverage,
    totalAmountOfTaxes,
    productsSoldCount,
    margin,
    marginRate,
    markupRate,
    totalPurchaseCost,
    __typename: mockTypename(),
  },
  __typename: mockTypename(),
}

let mockQueryDataPageInfo = (~startCursor=?, ~endCursor=?, ()) => {
  AnalyticsShopsPage.AnalyticsShopsQuery.startCursor,
  endCursor,
  __typename: mockTypename(),
}

let mockQueryData = (~edges=[], ~pageInfo=mockQueryDataPageInfo(), ~totalCount=0, ()) => {
  AnalyticsShopsPage.AnalyticsShopsQuery.shopsKeyPerformanceIndicators: {
    edges,
    pageInfo,
    totalCount,
    __typename: mockTypename(),
  },
}

let mockQueryVariablesFilterBy = () =>
  AnalyticsShopsPage.AnalyticsShopsQuery.makeInputObjectShopKeyPerformanceIndicatorsQueryFilter()

let mockQueryVariables = () => {
  AnalyticsShopsPage.AnalyticsShopsQuery.filterBy: None,
}

let mockAnalyticsShopsListFilters = (~shop=?, ~dateRange=?, ()) => {
  AnalyticsShopsPage.AnalyticsShopsFilters.shop,
  dateRange,
}

describe("AnalyticsShopsFilters", () => {
  todo("jsonCodec")
})

describe("AnalyticsShopsTableRow", () => {
  test("keyExtractor", () => {
    let {keyExtractor} = module(AnalyticsShopsPage.AnalyticsShopsTableRow)

    let row = mockRow()
    expect(keyExtractor(row))->toBe("")

    let row = mockRow(~shopId="mock-shopId", ())
    expect(keyExtractor(row))->toBe("mock-shopId")
  })
})

test("analyticsShopsQueryVariables", () => {
  let {analyticsShopsQueryVariables} = module(AnalyticsShopsPage)

  expect(
    analyticsShopsQueryVariables(
      LegacyResourceList.initialState(~filters=mockAnalyticsShopsListFilters()),
    ),
  )->toStrictEqual({
    AnalyticsShopsPage.AnalyticsShopsQuery.filterBy: Some({
      shopIds: None,
      startDate: None,
      endDate: None,
    }),
  })

  expect(
    analyticsShopsQueryVariables(
      LegacyResourceList.initialState(
        ~filters=mockAnalyticsShopsListFilters(~shop=mockShop(~id="mock-shop-id", ()), ()),
      ),
    ),
  )->toStrictEqual({
    AnalyticsShopsPage.AnalyticsShopsQuery.filterBy: Some({
      shopIds: Some({_in: ["mock-shop-id"]}),
      startDate: None,
      endDate: None,
    }),
  })
})

test("analyticsShopsQueryVariables", () => {
  let {analyticsShopsVariablesFilterBy} = module(AnalyticsShopsPage)

  let filters = {AnalyticsShopsPage.AnalyticsShopsFilters.shop: None, dateRange: None}
  expect(analyticsShopsVariablesFilterBy(filters))->toStrictEqual({
    AnalyticsShopsPage.AnalyticsShopsQuery.shopIds: None,
    startDate: None,
    endDate: None,
  })

  let filters = {
    AnalyticsShopsPage.AnalyticsShopsFilters.shop: Some(mockShop(~id="mock-shop-id", ())),
    dateRange: None,
  }
  expect(analyticsShopsVariablesFilterBy(filters))->toStrictEqual({
    AnalyticsShopsPage.AnalyticsShopsQuery.shopIds: Some({_in: ["mock-shop-id"]}),
    startDate: None,
    endDate: None,
  })

  let filters = {
    AnalyticsShopsPage.AnalyticsShopsFilters.shop: Some(mockShop(~id="mock-shop-id", ())),
    dateRange: Some((Js.Date.fromFloat(0.), Js.Date.fromFloat(1.))),
  }
  expect(analyticsShopsVariablesFilterBy(filters))->toStrictEqual({
    AnalyticsShopsPage.AnalyticsShopsQuery.shopIds: Some({_in: ["mock-shop-id"]}),
    startDate: Some(Js.Date.fromFloat(0.)->Scalar.Datetime.serialize),
    endDate: Some(Js.Date.fromFloat(1.)->Scalar.Datetime.serialize),
  })

  let filters = {
    AnalyticsShopsPage.AnalyticsShopsFilters.shop: None,
    dateRange: Some((Js.Date.fromFloat(0.), Js.Date.fromFloat(1.))),
  }
  expect(analyticsShopsVariablesFilterBy(filters))->toStrictEqual({
    AnalyticsShopsPage.AnalyticsShopsQuery.shopIds: None,
    startDate: Some(Js.Date.fromFloat(0.)->Scalar.Datetime.serialize),
    endDate: Some(Js.Date.fromFloat(1.)->Scalar.Datetime.serialize),
  })
})

test("analyticsShopsTableRowsFromQueryResult", () => {
  let {analyticsShopsTableRowsFromQueryResult} = module(AnalyticsShopsPage)

  let queryResult = mockQueryData()
  expect(analyticsShopsTableRowsFromQueryResult(queryResult))->toStrictEqual([])

  let queryResult = mockQueryData(~edges=[mockQueryDataEdge()], ())
  expect(analyticsShopsTableRowsFromQueryResult(queryResult))->toStrictEqual([
    {
      shopId: "",
      shopName: "",
      ticketsRevenueIncludingTaxes: 0.,
      ticketsRevenueExcludingTaxes: 0.,
      invoicesRevenueIncludingTaxes: 0.,
      invoicesRevenueExcludingTaxes: 0.,
      globalRevenueIncludingTaxes: 0.,
      globalRevenueExcludingTaxes: 0.,
      margin: 0.,
      marginRate: 0.,
      markupRate: 0.,
      salesCount: 0,
      salesIndex: 0.,
      shoppingCartAverage: 0.,
      productsSoldCount: 0,
      totalAmountOfTaxes: 0.,
      totalPurchaseCost: 0.,
      currency: Intl.currencyEur,
    },
  ])

  let queryResult = mockQueryData(
    ~edges=[
      mockQueryDataEdge(),
      mockQueryDataEdge(
        ~shopId="shop-id",
        ~shopName="shop-name",
        ~ticketsRevenueIncludingTaxes=-1.,
        ~ticketsRevenueExcludingTaxes=1.,
        ~invoicesRevenueIncludingTaxes=2.,
        ~invoicesRevenueExcludingTaxes=3.,
        ~globalRevenueIncludingTaxes=4.,
        ~globalRevenueExcludingTaxes=5.,
        ~salesCount=6,
        ~margin=7.,
        ~marginRate=8.,
        ~markupRate=9.,
        ~salesIndex=10.,
        ~shoppingCartAverage=11.,
        ~productsSoldCount=12,
        ~totalAmountOfTaxes=13.,
        ~totalPurchaseCost=12.3,
        (),
      ),
    ],
    (),
  )
  expect(analyticsShopsTableRowsFromQueryResult(queryResult))->toStrictEqual([
    {
      shopId: "",
      shopName: "",
      ticketsRevenueIncludingTaxes: 0.,
      ticketsRevenueExcludingTaxes: 0.,
      invoicesRevenueIncludingTaxes: 0.,
      invoicesRevenueExcludingTaxes: 0.,
      globalRevenueIncludingTaxes: 0.,
      globalRevenueExcludingTaxes: 0.,
      margin: 0.,
      marginRate: 0.,
      markupRate: 0.,
      salesCount: 0,
      salesIndex: 0.,
      shoppingCartAverage: 0.,
      productsSoldCount: 0,
      totalAmountOfTaxes: 0.,
      totalPurchaseCost: 0.,
      currency: Intl.currencyEur,
    },
    {
      shopId: "shop-id",
      shopName: "shop-name",
      ticketsRevenueIncludingTaxes: -1.,
      ticketsRevenueExcludingTaxes: 1.,
      invoicesRevenueIncludingTaxes: 2.,
      invoicesRevenueExcludingTaxes: 3.,
      globalRevenueIncludingTaxes: 4.,
      globalRevenueExcludingTaxes: 5.,
      salesCount: 6,
      margin: 7.,
      marginRate: 8.,
      markupRate: 9.,
      salesIndex: 10.,
      shoppingCartAverage: 11.,
      productsSoldCount: 12,
      totalAmountOfTaxes: 13.,
      totalPurchaseCost: 12.3,
      currency: Intl.currencyEur,
    },
  ])
})

// Some integration tests to ensure basic interactions
// with the other components and GraphQL might be valuable
// at some point.
todo("Integration test")
