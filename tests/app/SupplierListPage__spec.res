open Vitest

let {mockShop} = module(Auth__Mock)

let mockTypename = () => ""

let mockSuppliersQueryDataPageInfo = (~startCursor=?, ~endCursor=?, ()) => {
  SupplierListPage.SupplierListQuery.startCursor,
  endCursor,
  __typename: mockTypename(),
}

let mockSuppliersQueryData = (
  ~edges=[],
  ~pageInfo=mockSuppliersQueryDataPageInfo(),
  ~totalCount=0,
  (),
) => {
  SupplierListPage.SupplierListQuery.suppliers: {
    edges,
    pageInfo,
    totalCount,
    __typename: mockTypename(),
  },
}

let mockSupplierTableRow = (~id="", ()) => {
  SupplierListPage.SupplierTableRow.id,
  archivedAt: None,
  status: #UNARCHIVED,
  companyName: "",
  shopName: "",
  phoneNumber: None,
  email: None,
  firstName: None,
  lastName: "",
  civility: None,
  formattedOrdersTotalAmountIncludingTaxes: "",
}

let mockSuppliersQueryDataEdge = (
  ~id="",
  ~archivedAt=None,
  ~companyName="",
  ~email=None,
  ~phoneNumber=None,
  ~formattedOrdersTotalAmountIncludingTaxes="",
  ~contactsFirstName=None,
  ~contactsLastName="",
  ~contactsCivility=None,
  ~shopName="",
  (),
) => {
  SupplierListPage.SupplierListQuery.node: {
    id,
    archivedAt,
    companyName,
    email,
    phoneNumber,
    formattedOrdersTotalAmountIncludingTaxes,
    contacts: {
      edges: [
        {
          node: {
            firstName: contactsFirstName,
            lastName: contactsLastName,
            civility: contactsCivility,
            __typename: mockTypename(),
          },
          __typename: mockTypename(),
        },
      ],
      __typename: mockTypename(),
    },
    shop: {
      name: shopName,
      __typename: mockTypename(),
    },
    __typename: mockTypename(),
  },
  __typename: mockTypename(),
}

let mockSuppliersQueryVariables = () => {
  SupplierListPage.SupplierListQuery.first: None,
  last: None,
  before: None,
  after: None,
  filterBy: None,
  search: None,
}

let mockSuppliersListFilters = (~shop=?, ~status=#ARCHIVED, ()) => {
  SupplierListPage.SupplierListFilters.shop,
  status,
}

let mockState = (
  ~filters=mockSuppliersListFilters(),
  ~currentPage=1,
  ~previousPage=-1,
  ~searchQuery=?,
  (),
) => {
  Scaffold.filters,
  currentPage,
  previousPage,
  searchQuery,
  connectionArguments: {first: 10},
}

describe("SupplierListFilters", () => {
  todo("jsonCodec")
})

describe("SupplierTableRow", () => {
  open SupplierListPage

  test("keyExtractor", () => {
    let {keyExtractor} = module(SupplierTableRow)
    let row = mockSupplierTableRow()
    expect(keyExtractor(row))->toBe("")
    let row = mockSupplierTableRow(~id="mock-id", ())
    expect(keyExtractor(row))->toBe("mock-id")
  })
})

todo("SupplierActionsCell")

test("supplierTableRowsFromQueryResult", () => {
  let {supplierTableRowsFromQueryResult} = module(SupplierListPage)

  let queryData = mockSuppliersQueryData()
  expect(supplierTableRowsFromQueryResult(queryData))->toStrictEqual([])

  let queryData = mockSuppliersQueryData(~edges=[mockSuppliersQueryDataEdge()], ())
  expect(supplierTableRowsFromQueryResult(queryData))->toStrictEqual([
    {
      SupplierListPage.SupplierTableRow.id: "",
      archivedAt: None,
      status: #UNARCHIVED,
      companyName: "",
      shopName: "",
      phoneNumber: None,
      email: None,
      firstName: None,
      lastName: "",
      civility: None,
      formattedOrdersTotalAmountIncludingTaxes: "",
    },
  ])

  let queryData = mockSuppliersQueryData(
    ~edges=[
      mockSuppliersQueryDataEdge(
        ~id="mock-id",
        ~archivedAt=Some(Js.Date.fromFloat(1.)),
        ~companyName="mock-company-name",
        ~email=Some("mock-email"),
        ~phoneNumber=Some("0101010101"),
        ~formattedOrdersTotalAmountIncludingTaxes="mock-formatted-orders-total-amount-including-taxes",
        ~contactsFirstName=Some("mock-contact-first-name"),
        ~contactsLastName="mock-contact-last-name",
        ~contactsCivility=Some(#NEUTRAL),
        ~shopName="mock-shop-name",
        (),
      ),
    ],
    (),
  )
  expect(supplierTableRowsFromQueryResult(queryData))->toStrictEqual([
    {
      SupplierListPage.SupplierTableRow.id: "mock-id",
      archivedAt: Some(Js.Date.fromFloat(1.)),
      status: #ARCHIVED,
      companyName: "mock-company-name",
      shopName: "mock-shop-name",
      phoneNumber: Some("0101010101"),
      email: Some("mock-email"),
      firstName: Some("mock-contact-first-name"),
      lastName: "mock-contact-last-name",
      civility: Some(#NEUTRAL),
      formattedOrdersTotalAmountIncludingTaxes: "mock-formatted-orders-total-amount-including-taxes",
    },
  ])
})

test("suppliersQueryVariables", () => {
  let {suppliersQueryVariables} = module(SupplierListPage)

  expect(
    suppliersQueryVariables(LegacyResourceList.initialState(~filters=mockSuppliersListFilters())),
  )->toStrictEqual({
    SupplierListPage.SupplierListQuery.first: Some(10),
    last: None,
    before: None,
    after: None,
    filterBy: Some({archived: Some(#ONLY), shopIds: None}),
    search: None,
  })

  expect(
    suppliersQueryVariables({
      ...LegacyResourceList.initialState(
        ~filters={
          SupplierListPage.SupplierListFilters.shop: Some(mockShop(~id="mock-shop-id", ())),
          status: #ARCHIVED,
        },
      ),
      connectionArguments: {
        first: 1,
        last: 2,
        before: "before",
        after: "after",
      },
      searchQuery: "search",
    }),
  )->toStrictEqual({
    SupplierListPage.SupplierListQuery.first: Some(1),
    last: Some(2),
    before: Some("before"),
    after: Some("after"),
    search: Some("search"),
    filterBy: Some({archived: Some(#ONLY), shopIds: Some({_in: ["mock-shop-id"]})}),
  })
})

test("suppliersQueryVariablesFilterBy", () => {
  let {suppliersQueryVariablesFilterBy} = module(SupplierListPage)

  let filters = {
    SupplierListPage.SupplierListFilters.shop: None,
    status: #UNARCHIVED,
  }

  expect(suppliersQueryVariablesFilterBy(filters))->toStrictEqual({
    SupplierListPage.SupplierListQuery.shopIds: None,
    archived: Some(#EXCLUDED),
  })

  let filters = {
    SupplierListPage.SupplierListFilters.shop: Some(mockShop(~id="mock-shop-id", ())),
    status: #ARCHIVED,
  }

  expect(suppliersQueryVariablesFilterBy(filters))->toStrictEqual({
    SupplierListPage.SupplierListQuery.shopIds: Some({_in: ["mock-shop-id"]}),
    archived: Some(#ONLY),
  })
})

// Some integration tests to ensure basic interactions
// with the other components and GraphQL might be valuable
// at some point.
todo("Integration test")
