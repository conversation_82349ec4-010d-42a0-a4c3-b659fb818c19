open Vitest

let {mockShop} = module(Auth__Mock)

let mockQueryVariablesFilterBy = () =>
  StockTransferListPage.Query.makeInputObjectInputStockTransfersQueryFilter()

let mockTypename = () => ""

let mockQueryDataPageInfo = (~startCursor=?, ~endCursor=?, ()) => {
  StockTransferListPage.Query.startCursor,
  endCursor,
  __typename: mockTypename(),
}

let mockQueryData = (~edges=[], ~pageInfo=mockQueryDataPageInfo(), ~totalCount=0, ()) => {
  StockTransferListPage.Query.stockTransfers: {
    edges,
    pageInfo,
    totalCount,
    __typename: mockTypename(),
  },
}

let mockRow = (~id="", ()) => {
  StockTransferListPage.Row.id,
  code: "",
  date: Js.Date.fromFloat(0.),
  senderShop: "",
  recipientShop: "",
  productsCount: 0,
}

let mockQueryDataEdge = (
  ~id="",
  ~code="",
  ~createdAt=Js.Date.fromFloat(0.),
  ~senderShopName="",
  ~recipientShopName="",
  ~productsTotalCount=0,
  (),
) => {
  StockTransferListPage.Query.node: {
    id,
    code,
    createdAt,
    senderShop: {
      name: senderShopName,
      __typename: mockTypename(),
    },
    recipientShop: {
      name: recipientShopName,
      __typename: mockTypename(),
    },
    products: {
      totalCount: productsTotalCount,
      __typename: mockTypename(),
    },
    __typename: mockTypename(),
  },
  __typename: mockTypename(),
}

let mockQueryVariables = () => {
  StockTransferListPage.Query.first: None,
  last: None,
  before: None,
  after: None,
  filterBy: None,
  search: None,
}

let mockStateFilters = (~createdAtDateRange=?, ()) => {
  StockTransferListPage.Filters.createdAtDateRange: createdAtDateRange,
}

let mockState = (
  ~filters=mockStateFilters(),
  ~currentPage=1,
  ~previousPage=-1,
  ~searchQuery=?,
  (),
) => {
  Scaffold.filters,
  currentPage,
  previousPage,
  searchQuery,
  connectionArguments: {first: 10},
}

describe("Scaffolded", () => {
  todo("useFiltersJsonCodec")

  test("makeQueryVariables", () => {
    let {makeQueryVariables} = module(StockTransferListPage.Scaffolded)

    expect(makeQueryVariables(mockQueryVariables(), ~connectionArguments={}, ()))->toStrictEqual({
      StockTransferListPage.Query.first: None,
      last: None,
      before: None,
      after: None,
      filterBy: None,
      search: None,
    })

    expect(
      makeQueryVariables(
        mockQueryVariables(),
        ~connectionArguments={
          first: 1,
          last: 2,
          before: "before",
          after: "after",
        },
        ~search="search",
        ~filterBy=mockQueryVariablesFilterBy(),
        (),
      ),
    )->toStrictEqual({
      StockTransferListPage.Query.first: Some(1),
      last: Some(2),
      before: Some("before"),
      after: Some("after"),
      search: Some("search"),
      filterBy: Some(StockTransferListPage.Query.makeInputObjectInputStockTransfersQueryFilter()),
    })
  })

  test("makeQueryVariablesFilterBy", () => {
    let {makeQueryVariablesFilterBy} = module(StockTransferListPage.Scaffolded)

    let filters = {
      StockTransferListPage.Filters.createdAtDateRange: None,
    }

    expect(makeQueryVariablesFilterBy(filters))->toStrictEqual({
      StockTransferListPage.Query.createdAt: None,
    })

    let filters = {
      StockTransferListPage.Filters.createdAtDateRange: Some((
        Js.Date.fromFloat(0.),
        Js.Date.fromFloat(1.),
      )),
    }

    expect(makeQueryVariablesFilterBy(filters))->toStrictEqual({
      StockTransferListPage.Query.createdAt: Some({
        _before: None,
        _after: None,
        _between: Some([
          Js.Date.fromFloat(0.)->Scalar.Datetime.serialize,
          Js.Date.fromFloat(1.)->Scalar.Datetime.serialize,
        ]),
      }),
    })
  })

  test("totalCountFromQueryData", () => {
    let {totalCountFromQueryData} = module(StockTransferListPage.Scaffolded)

    let queryData = mockQueryData()
    expect(totalCountFromQueryData(queryData))->toBe(0)

    let queryData = mockQueryData(~totalCount=1, ())
    expect(totalCountFromQueryData(queryData))->toBe(1)

    let queryData = mockQueryData(~totalCount=100, ())
    expect(totalCountFromQueryData(queryData))->toBe(100)

    let queryData = mockQueryData(~totalCount=-1, ())
    expect(totalCountFromQueryData(queryData))->toBe(-1)
  })

  test("cursorsFromQueryData", () => {
    let {cursorsFromQueryData} = module(StockTransferListPage.Scaffolded)

    let queryData = mockQueryData()
    expect(cursorsFromQueryData(queryData))->toStrictEqual((None, None))

    let queryData = mockQueryData(~pageInfo=mockQueryDataPageInfo(~startCursor="start", ()), ())
    expect(cursorsFromQueryData(queryData))->toStrictEqual((Some("start"), None))

    let queryData = mockQueryData(
      ~pageInfo=mockQueryDataPageInfo(~startCursor="start", ~endCursor="end", ()),
      (),
    )
    expect(cursorsFromQueryData(queryData))->toStrictEqual((Some("start"), Some("end")))

    let queryData = mockQueryData(~pageInfo=mockQueryDataPageInfo(~endCursor="end", ()), ())
    expect(cursorsFromQueryData(queryData))->toStrictEqual((None, Some("end")))
  })

  test("rowsFromQueryDataAndState", () => {
    let {rowsFromQueryDataAndState} = module(StockTransferListPage.Scaffolded)

    let queryData = mockQueryData()
    expect(rowsFromQueryDataAndState(queryData, mockState()))->toStrictEqual([])

    let queryData = mockQueryData(~edges=[mockQueryDataEdge()], ())
    expect(rowsFromQueryDataAndState(queryData, mockState()))->toStrictEqual([
      {
        StockTransferListPage.Row.id: "",
        code: "",
        date: Js.Date.fromFloat(0.),
        senderShop: "",
        recipientShop: "",
        productsCount: 0,
      },
    ])

    let queryData = mockQueryData(
      ~edges=[
        mockQueryDataEdge(
          ~id="mock-id",
          ~code="mock-code",
          ~createdAt=Js.Date.fromFloat(1.),
          ~senderShopName="mock-sender-shop-name",
          ~recipientShopName="mock-recipient-shop-name",
          ~productsTotalCount=1,
          (),
        ),
      ],
      (),
    )
    expect(rowsFromQueryDataAndState(queryData, mockState()))->toStrictEqual([
      {
        StockTransferListPage.Row.id: "mock-id",
        code: "mock-code",
        date: Js.Date.fromFloat(1.),
        senderShop: "mock-sender-shop-name",
        recipientShop: "mock-recipient-shop-name",
        productsCount: 1,
      },
    ])
  })

  test("keyExtractor", () => {
    let {keyExtractor} = module(StockTransferListPage.Scaffolded)

    let row = mockRow()
    expect(keyExtractor(row))->toBe("")

    let row = mockRow(~id="mock-id", ())
    expect(keyExtractor(row))->toBe("mock-id")
  })
})

// Some integration tests to ensure basic interactions
// with the other components and GraphQL might be valuable
// at some point.
todo("Integration test")
