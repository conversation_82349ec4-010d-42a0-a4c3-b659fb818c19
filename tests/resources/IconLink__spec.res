open Vitest
open TestingLibraryReact

itPromise("should correctly redirect when pressing the button", async () => {
  let userEvent = TestingLibraryEvent.setup()
  let history = History.createMemoryHistory()
  let nextPathname = "/about"

  <Providers history>
    <IconLink to=Route(nextPathname) name=#queue_arrow_right_light />
  </Providers>
  ->render
  ->ignore

  let link = screen->getByRoleExn(#link)

  expect(link)->toBeVisible
  expect(history.length)->toBe(1)
  expect(history.location.pathname)->toBe("/")

  await userEvent->TestingLibraryEvent.click(link)

  expect(link)->toBeVisible
  expect(history.length)->toBe(2)
  expect(history.location.pathname)->toBe(nextPathname)
})

itPromise("should not render a link when the button is disabled", async () => {
  let userEvent = TestingLibraryEvent.setup()
  let history = History.createMemoryHistory()

  let {container} = <Providers history>
    <IconLink to=Route("/about") disabled=true name=#queue_arrow_right_light />
  </Providers>->render

  let link = screen->getByRoleExn(#link)

  expect(link)->toHaveAttributeValue("aria-disabled", "true")
  expect(container)->toBeVisible
  expect(history.length)->toBe(1)
  expect(history.location.pathname)->toBe("/")

  await userEvent->TestingLibraryEvent.click(container)

  expect(link)->toHaveAttributeValue("aria-disabled", "true")
  expect(container)->toBeVisible
  expect(history.length)->toBe(1)
  expect(history.location.pathname)->toBe("/")
})
