open Vitest
open TestingLibraryReact

it("should render an input element", () => {
  render(<SearchBar placeholder="" onChange={_ => ()} />)->ignore
  let textbox = screen->getByRoleExn(#textbox)
  expect(textbox)->toBeVisible
})

module TestableSearchBar = {
  @react.component
  let make = (~value=?, ~onChange) => {
    let (searchBarValue, setSearchBarValue) = React.useState(() => None)

    React.useEffect1(() => {
      switch value {
      | Some(value) => setSearchBarValue(_ => value)
      | None => setSearchBarValue(_ => Some(""))
      }
      None
    }, [value])

    <SearchBar
      placeholder=""
      value=?searchBarValue
      onChange={value => {
        setSearchBarValue(_ => Some(value))
        onChange(value)
      }}
    />
  }
}

itPromise("should tap and call (debounce) onChange prop", async () => {
  let userEvent = TestingLibraryEvent.setup()

  let onChange = fn1(ignore)
  render(<TestableSearchBar onChange={value => onChange->fn(value)} />)->ignore

  let textbox = screen->getByRoleExn(#textbox)

  expect(onChange)->toHaveBeenCalledTimes(0)
  expect(textbox)->toBeVisible
  expect(textbox)->toHaveValue("")

  await userEvent->TestingLibraryEvent.type_(textbox, "value")

  expect(onChange)->toHaveBeenCalledTimes(0)
  expect(textbox)->toBeVisible
  expect(textbox)->toHaveValue("value")

  await waitFor(() => expect(onChange)->toHaveBeenCalledTimes(1))

  expect(onChange)->toHaveBeenCalledWith1("value")
  expect(textbox)->toBeVisible
  expect(textbox)->toHaveValue("value")
})

it("should change value prop", () => {
  let onChange = fn1(ignore)
  let {rerender} = render(<TestableSearchBar onChange={value => onChange->fn(value)} />)

  let textbox = screen->getByRoleExn(#textbox)

  expect(textbox)->toBeVisible
  expect(textbox)->toHaveValue("")
  expect(onChange)->toHaveBeenCalledTimes(0)

  rerender(
    <TestableSearchBar value=Some("value") onChange={value => onChange->fn(value)} />,
  )->ignore

  expect(textbox)->toBeVisible
  expect(textbox)->toHaveValue("value")
  expect(onChange)->toHaveBeenCalledTimes(0)

  rerender(
    <TestableSearchBar value=Some("new-value") onChange={value => onChange->fn(value)} />,
  )->ignore

  expect(textbox)->toBeVisible
  expect(textbox)->toHaveValue("new-value")
  expect(onChange)->toHaveBeenCalledTimes(0)

  rerender(<TestableSearchBar value=None onChange={value => onChange->fn(value)} />)->ignore

  expect(textbox)->toBeVisible
  expect(textbox)->toHaveValue("")
  expect(onChange)->toHaveBeenCalledTimes(0)
})

todo("should clear value and focus input")
todo("should not clear value")
