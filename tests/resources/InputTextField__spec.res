open Vitest
open TestingLibraryReact

describe("component", () => {
  let userEvent = TestingLibraryEvent.setup()

  testPromise("default", async () => {
    let onChange = fn1(ignore)

    let {rerender} = render(<InputTextField label="My Input" onChange={onChange->fn} />)

    let inputElement = screen->getByLabelTextExn("My Input")

    expect(inputElement)->toBeVisible
    expect(onChange)->toHaveBeenCalledTimes(0)

    await userEvent->TestingLibraryEvent.type_(inputElement, "?")

    expect(onChange)->toHaveBeenCalled
    expect(onChange)->toHaveBeenCalledWith1("?")

    onChange->mockClear

    rerender(<InputTextField label="My Input" disabled=true onChange={onChange->fn} />)->ignore

    await userEvent->TestingLibraryEvent.type_(inputElement, "?")

    expect(onChange)->Vitest.not->toHaveBeenCalled

    rerender(
      <InputTextField
        label="My Input" errorMessage="Custom error message" onChange={onChange->fn}
      />,
    )->ignore

    expect(screen->queryByText("Custom error message"))->toBeDefined
  })

  testPromise("`suggestion` variation", async () => {
    let onRequestClear = fn0()
    let onPress = fn1(ignore)

    let suggestionVariationProps = {
      InputTextField.opened: false,
      toggleButtonDisabled: false,
      toggleButtonProps: {onPress: onPress->fn},
      onRequestClear: onRequestClear->fn,
    }
    let {rerender} = render(
      <InputTextField
        label="my label"
        value="my value"
        variation=#suggestion(suggestionVariationProps)
        onChange={_ => ()}
      />,
    )

    let inputElement = screen->getByLabelTextExn("my label")

    expect(inputElement)->toBeVisible
    expect(inputElement)->toHaveDisplayValue("my value")
    expect(onPress)->toHaveBeenCalledTimes(0)
    expect(onRequestClear)->toHaveBeenCalledTimes(0)

    let (clearButtonElement, toggleListElement) = screen->getAllByRoleExn2(#button)

    await userEvent->TestingLibraryEvent.click(clearButtonElement)

    expect(clearButtonElement)->toBeVisible
    expect(onRequestClear)->toHaveBeenCalledTimes(1)
    expect(onPress)->toHaveBeenCalledTimes(0)

    await userEvent->TestingLibraryEvent.click(toggleListElement)

    expect(onPress)->toHaveBeenCalledTimes(1)

    onPress->mockClear

    let suggestionVariationProps = {
      InputTextField.opened: false,
      toggleButtonDisabled: true,
      toggleButtonProps: {onPress: onPress->fn},
      onRequestClear: onRequestClear->fn,
    }
    rerender(
      <InputTextField
        variation=#suggestion(suggestionVariationProps) label="my label" onChange={_ => ()}
      />,
    )->ignore

    let (_clearButtonElement, toggleListElement) = screen->getAllByRoleExn2(#button)

    expect(onPress)->toHaveBeenCalledTimes(0)

    await userEvent->TestingLibraryEvent.click(toggleListElement)

    expect(onPress)->toHaveBeenCalledTimes(0)
  })
})
