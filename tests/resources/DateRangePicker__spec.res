open Vitest

test("getMaxDate", () => {
  let {getMaxDate} = module(DateRangePicker)
  let now = Js.Date.make()
  expect(getMaxDate())->toStrictEqual(now->DateHelpers.endOfDay)
})

test("isOutsideRange", () => {
  let {isOutsideRange} = module(DateRangePicker)
  let now = Js.Date.make()
  let theBeginningOfHackersLife = Js.Date.fromFloat(0.)
  let theEndOfTheWorld = Js.Date.fromFloat(100000000000000.)
  expect(now->isOutsideRange)->toBe(false)
  expect(theBeginningOfHackersLife->isOutsideRange)->toBe(false)
  expect(theEndOfTheWorld->isOutsideRange)->toBe(true)
  let now = Js.Date.make()
  let (todayStartDate, todayEndDate) = (now->DateHelpers.startOfDay, now->DateHelpers.endOfDay)
  expect(todayStartDate->isOutsideRange)->toBe(false)
  expect(todayEndDate->isOutsideRange)->toBe(false)
  let (lastYearStartDate, lastYearEndDate) = (
    now->DateHelpers.subYears(1.)->DateHelpers.startOfYear,
    now->DateHelpers.subYears(1.)->DateHelpers.endOfYear,
  )
  expect(lastYearStartDate->isOutsideRange)->toBe(false)
  expect(lastYearEndDate->isOutsideRange)->toBe(false)
  expect(Js.Date.fromFloat(todayEndDate->Js.Date.getTime +. 1.)->isOutsideRange)->toBe(true)
})

todo("Component")
