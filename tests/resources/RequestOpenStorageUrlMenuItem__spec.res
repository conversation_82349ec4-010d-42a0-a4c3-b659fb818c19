open Vitest
open TestingLibraryReact

external asExn: 'a => Js.Exn.t = "%identity"

let mockedFailedEndpoint = "https://some.error"
let mockedSuccessfulEndpoint = "https://some.success"
let mockedExpectedUrl = "https://some.result/"

let {setupNodeServer, listen, resetHandlers, ctxDelay, close} = module(MSW)
let {ctxUnsafeObject, postWithDelay} = module(MSW.Rest)

let server = setupNodeServer([
  postWithDelay(mockedSuccessfulEndpoint, (_req, res, ctx) => {
    let payload = {"data": {"url": mockedExpectedUrl}}
    res(. ctx->ctxDelay(), ctx->ctxUnsafeObject(payload))
  }),
  postWithDelay(mockedFailedEndpoint, (_req, res, ctx) => {
    let payload = {"error": "error"}
    res(. ctx->ctxDelay(), ctx->ctxUnsafeObject(payload))
  }),
])

beforeAll(() => server->listen({onUnhandledRequest: #warn}))
afterEach(() => server->resetHandlers)
afterAll(() => server->close)

let userEvent = TestingLibraryEvent.setup()

let render = element =>
  element->renderWithOptions(
    ~options={
      wrapper: props => <Providers> {props["children"]} </Providers>,
    },
  )

module TestableRequestOpenStorageUrlMenuItem = {
  let testBodyJson = Json.fromObjExn({
    "file": {
      "url": mockedExpectedUrl,
    },
  })

  @react.component
  let make = (
    ~text="Download",
    ~textError="Download failed",
    ~endpoint=mockedSuccessfulEndpoint,
    ~onSuccess=fn1(ignore)->fn,
    ~onFailure=fn1(ignore)->fn,
  ) => {
    let request = () => Request.make(endpoint, ~method=#POST, ~bodyJson=testBodyJson)

    let operableRequest = Ok(request)

    <RequestOpenStorageUrlMenuItem text textError operableRequest onSuccess onFailure />
  }
}

test("decodeAndMakeUrl", () => {
  let {decodeAndMakeUrl} = module(RequestOpenStorageUrlMenuItem)

  expect(
    decodeAndMakeUrl(
      Json.fromObjExn({
        "url": mockedExpectedUrl,
      }),
    ),
  )->toStrictEqual(Some(mockedExpectedUrl->Url.make))

  expect(
    decodeAndMakeUrl(
      Json.fromObjExn({
        "file": {
          "url": mockedExpectedUrl,
        },
      }),
    ),
  )->toStrictEqual(Some(mockedExpectedUrl->Url.make))

  expect(
    decodeAndMakeUrl(
      Json.fromObjExn({
        "timestamp": 1695723095,
      }),
    ),
  )->toStrictEqual(None)
})

itPromise(
  "should open the menu and render its item button with corresponding text content",
  async () => {
    <Menu>
      <TestableRequestOpenStorageUrlMenuItem text="My item" />
    </Menu>
    ->render
    ->ignore

    let button = screen->getByRoleWithOptionsExn(#button, {name: "Actions"})

    await userEvent->TestingLibraryEvent.click(button)

    let menuItem = screen->getByRoleWithOptionsExn(#button, {name: "My item"})

    expect(menuItem)->toBeVisible
    expect(menuItem)->toHaveTextContent("My item")
  },
)

itPromise("should fetch and render a temporary link with the result", async () => {
  let onSuccess = fn1(ignore)
  let onFailure = fn1(ignore)

  let {baseElement} = <Menu>
    <TestableRequestOpenStorageUrlMenuItem onSuccess={onSuccess->fn} onFailure={onFailure->fn} />
  </Menu>->render

  let button = screen->getByRoleWithOptionsExn(#button, {name: "Actions"})

  await userEvent->TestingLibraryEvent.click(button)

  let menuItem = screen->getByRoleWithOptionsExn(#button, {name: "Download"})

  expect(menuItem)->toBeVisible
  expect(menuItem)->toHaveTextContent("Download")
  expect(onSuccess)->toHaveBeenCalledTimes(0)
  expect(onFailure)->toHaveBeenCalledTimes(0)

  await userEvent->TestingLibraryEvent.click(menuItem)

  await waitFor(() => {
    let spinnerModal =
      within(baseElement->querySelectorUnsafe("#portals-modal"))->getByTextExn(
        "Please wait until the operation is complete",
      )

    expect(menuItem)->toBeVisible
    expect(spinnerModal)->toBeVisible
  })

  expect(onSuccess)->toHaveBeenCalledTimes(0)
  expect(onFailure)->toHaveBeenCalledTimes(0)
  expect(menuItem)->toHaveTextContent("Download")

  let tempLink = await waitForReturn(() => screen->getByRoleWithOptionsExn(#link, {hidden: true}))
  expect(tempLink)->toHaveProperty("download", mockedExpectedUrl)
  expect(tempLink)->toHaveAttributeValue("href", mockedExpectedUrl)
  expect(onFailure)->toHaveBeenCalledTimes(0)
  expect(onSuccess)->toHaveBeenCalledTimes(0)

  await waitFor(() => expect(onSuccess)->toHaveBeenCalledTimes(1))

  expect(menuItem)->toBeVisible
  expect(menuItem)->toHaveTextContent("Download")
  expect(tempLink)->toHaveProperty("download", mockedExpectedUrl)
  expect(tempLink)->toHaveAttributeValue("href", mockedExpectedUrl)
  expect(onFailure)->toHaveBeenCalledTimes(0)
  expect(onSuccess)->toHaveBeenCalledTimes(1)
  expect(onSuccess)->toHaveBeenCalledWith1(Some(mockedExpectedUrl->Url.make))
})

itPromise("should fail fetching and render an error message in the button", async () => {
  let onSuccess = fn1(ignore)
  let onFailure = fn1(ignore)

  <Menu>
    <TestableRequestOpenStorageUrlMenuItem
      textError="Bad Url Error"
      endpoint=mockedFailedEndpoint
      onSuccess={onSuccess->fn}
      onFailure={onFailure->fn}
    />
  </Menu>
  ->render
  ->ignore

  let button = screen->getByRoleWithOptionsExn(#button, {name: "Actions"})

  await userEvent->TestingLibraryEvent.click(button)

  let menuItem = screen->getByRoleWithOptionsExn(#button, {name: "Download"})

  expect(menuItem)->toBeVisible
  expect(menuItem)->toHaveTextContent("Download")
  expect(onSuccess)->toHaveBeenCalledTimes(0)
  expect(onFailure)->toHaveBeenCalledTimes(0)

  await userEvent->TestingLibraryEvent.click(menuItem)

  expect(menuItem)->toBeVisible

  await waitFor(() => expect(menuItem)->toHaveTextContent("Bad Url Error"))
  expect(onSuccess)->toHaveBeenCalledTimes(0)
  expect(onFailure)->toHaveBeenCalledTimes(1)
  expect(onFailure)->toHaveBeenCalledWith1(RequestError(MalformedResponse))
})

todo("test link download failure returning `LinkingOpenUrlError`")
