open Vitest

open Banner

describe("RichText", () => {
  it("should make the message with basic markdown", () => {
    let markdown = "this is some simple markdown without any spec"

    expect(RichText.make(~markdown))->toStrictEqual({message: markdown, link: None})
  })

  testEach2([
    (
      "this is a [href](https://wino.fr)",
      {
        RichText.message: "this is a ",
        link: Some({text: "href", to: Url("https://wino.fr"->Url.make)}),
      },
    ),
    (
      "this is a [composed href](http://www.notsecure.org)",
      {
        RichText.message: "this is a ",
        link: Some({text: "composed href", to: Url("http://www.notsecure.org"->Url.make)}),
      },
    ),
    (
      "this is not an href anymore [not an href]((https://parenthesis.org)",
      {
        RichText.message: "this is not an href anymore ",
        link: Some({text: "not an href", to: Route("(https://parenthesis.org")}),
      },
    ),
    (
      "this is clearly a relative route now [my route](/about)",
      {
        RichText.message: "this is clearly a relative route now ",
        link: Some({text: "my route", to: Route("/about")}),
      },
    ),
    (
      "this is still a route [my route](/about/82372-23925?query=string)",
      {
        RichText.message: "this is still a route ",
        link: Some({text: "my route", to: Route("/about/82372-23925?query=string")}),
      },
    ),
    (
      "[Only href](https://wino.fr)",
      {
        RichText.message: "",
        link: Some({text: "Only href", to: Url("https://wino.fr"->Url.make)}),
      },
    ),
  ])(."should make the message with some url markdown (#%#)", (input, output) => {
    expect(RichText.make(~markdown=input))->toStrictEqual(output)
  })

  it("it should only interpret the last link inserted in markdown", () => {
    let markdown = "this is [my href](https://wino.fr) and this is [my route](/about) here"

    expect(RichText.make(~markdown))->toStrictEqual({
      message: "this is [my href](https://wino.fr) and this is  here",
      link: Some({text: "my route", to: Route("/about")}),
    })
  })

  it("it should not interpret it as an url when there is some whitespace", () => {
    let markdown = "this is [not an url](/about /whitespace)"

    expect(RichText.make(~markdown))->toStrictEqual({
      message: "this is [not an url](/about /whitespace)",
      link: None,
    })
  })
})

todo("component")
