open Vitest
open TestingLibraryReact

module TestableCard = {
  @react.component
  let make = (~actionHandler) =>
    <Card
      title="Test"
      action={{
        icon: #queue_arrow_right_light,
        title: "Some action",
        handler: actionHandler,
      }}>
      React.null
    </Card>
}

itPromise("should redirect with a link when pressing the action button", async () => {
  let userEvent = TestingLibraryEvent.setup()
  let history = History.createMemoryHistory()
  let nextPathname = "/about"

  <Providers history>
    <TestableCard actionHandler=Card.Action.OpenLink(Route(nextPathname)) />
  </Providers>
  ->render
  ->ignore

  let link = screen->getByRoleExn(#link)

  expect(link)->toBeVisible
  expect(history.length)->toBe(1)
  expect(history.location.pathname)->toBe("/")

  await userEvent->TestingLibraryEvent.click(link)

  expect(history.length)->toBe(2)
  expect(history.location.pathname)->toBe(nextPathname)
})

itPromise("should request a callback when pressing the action button", async () => {
  let userEvent = TestingLibraryEvent.setup()
  let callback = fn0()

  let history = History.createMemoryHistory()

  <Providers history>
    <TestableCard actionHandler=Card.Action.Callback(callback->fn) />
  </Providers>
  ->render
  ->ignore

  let button = screen->getByRoleExn(#button)

  expect(button)->toBeVisible
  expect(callback)->toHaveBeenCalledTimes(0)
  expect(history.length)->toBe(1)
  expect(history.location.pathname)->toBe("/")

  await userEvent->TestingLibraryEvent.click(button)

  expect(callback)->toHaveBeenCalledTimes(1)
  expect(history.length)->toBe(1)
  expect(history.location.pathname)->toBe("/")
})
