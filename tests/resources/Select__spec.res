open Vitest
open TestingLibraryReact
open WebAPI

let {processItems} = module(Select)

test("processItems", () => {
  let sections = [
    {
      Select.title: "title-1",
      items: [
        {key: "key-1", label: "label-1", value: "value-1", sticky: false},
        {key: "key-2", label: "label-2", value: "value-2", sticky: true},
      ],
    },
    {
      items: [{key: "key-3", label: "label-3", value: "value-3", sticky: false}],
    },
  ]
  let searchValue = "label-3"
  let filterByLabel = label => Js.String.includes(searchValue, label)

  expect(processItems(~sections, ~filterByLabel))->toStrictEqual({
    sectionsItems: [
      {key: "key-1", label: "label-1", value: "value-1", sticky: false},
      {key: "key-2", label: "label-2", value: "value-2", sticky: true},
      {key: "key-3", label: "label-3", value: "value-3", sticky: false},
    ],
    searchableSectionsItems: [
      {key: "key-1", label: "label-1", value: "value-1", sticky: false},
      {key: "key-3", label: "label-3", value: "value-3", sticky: false},
    ],
    filteredSectionsItems: [
      {key: "key-2", label: "label-2", value: "value-2", sticky: true},
      {key: "key-3", label: "label-3", value: "value-3", sticky: false},
    ],
  })

  let searchValue = ""
  let filterByLabel = label => Js.String.includes(searchValue, label)

  expect(processItems(~sections, ~filterByLabel))->toStrictEqual({
    sectionsItems: [
      {key: "key-1", label: "label-1", value: "value-1", sticky: false},
      {key: "key-2", label: "label-2", value: "value-2", sticky: true},
      {key: "key-3", label: "label-3", value: "value-3", sticky: false},
    ],
    searchableSectionsItems: [
      {key: "key-1", label: "label-1", value: "value-1", sticky: false},
      {key: "key-3", label: "label-3", value: "value-3", sticky: false},
    ],
    filteredSectionsItems: [
      {key: "key-1", label: "label-1", value: "value-1", sticky: false},
      {key: "key-2", label: "label-2", value: "value-2", sticky: true},
      {key: "key-3", label: "label-3", value: "value-3", sticky: false},
    ],
  })

  let searchValue = "unknown"
  let filterByLabel = label => Js.String.includes(searchValue, label)

  expect(processItems(~sections, ~filterByLabel))->toStrictEqual({
    sectionsItems: [
      {key: "key-1", label: "label-1", value: "value-1", sticky: false},
      {key: "key-2", label: "label-2", value: "value-2", sticky: true},
      {key: "key-3", label: "label-3", value: "value-3", sticky: false},
    ],
    searchableSectionsItems: [
      {key: "key-1", label: "label-1", value: "value-1", sticky: false},
      {key: "key-3", label: "label-3", value: "value-3", sticky: false},
    ],
    filteredSectionsItems: [{key: "key-2", label: "label-2", value: "value-2", sticky: true}],
  })
})

describe("component", () => {
  let expectListBoxIsOpened = () => {
    let triggerElement = screen->getByRoleWithOptionsExn(#button, {expanded: true, hidden: true})
    let triggerElementControlsId = triggerElement->DomElement.getAttribute("aria-controls")

    expect(triggerElement)->toBeVisible

    let listboxElement = screen->getByRoleExn(#listbox)
    let listboxElementId = listboxElement->DomElement.getAttribute("id")

    expect(listboxElement)->toBeVisible
    expect(triggerElementControlsId)->toBe(listboxElementId)
  }

  let expectListBoxIsClosed = () => {
    let triggerElement = screen->getByRoleWithOptionsExn(#button, {expanded: false, hidden: false})

    expect(triggerElement)->toBeVisible
    expect(screen->queryByRole(#listbox))->toBeNone
  }

  let userEvent = TestingLibraryEvent.setup()

  module TestableSelect = {
    @react.component
    let make = (
      ~preset=#filter,
      ~label=?,
      ~placeholder=?,
      ~disabled=?,
      ~loading=?,
      ~searchable=?,
      ~defaultOpen=?,
      ~renderTriggerView=?,
      ~renderItemContent=?,
      ~overlayFooterLink=?,
      ~sections,
      ~defaultValue,
      ~onChange=?,
      ~onToggle=?,
    ) => {
      let (value, setValue) = React.useState(() => defaultValue)

      <Select
        preset
        ?label
        ?placeholder
        ?disabled
        ?loading
        ?searchable
        ?defaultOpen
        ?renderTriggerView
        ?renderItemContent
        ?overlayFooterLink
        sections
        value
        onChange={value => {
          setValue(_ => value)
          onChange->Option.forEach(onChange => onChange(value))
        }}
        ?onToggle
      />
    }
  }

  itPromise("should render with default value and be possible to interact with", async () => {
    let onChange = fn1(ignore)
    let sections = [
      {
        Select.items: [
          {
            key: "default",
            label: "All",
            value: None,
          },
          {
            key: "first",
            label: "First",
            value: Some("first"),
          },
        ],
      },
    ]

    let {baseElement} = render(
      <TestableSelect defaultValue=None sections onChange={onChange->fn} />,
    )

    let triggerElement = screen->getByRoleExn(#button)

    expect(triggerElement)->toBeVisible
    expect(triggerElement)->toHaveTextContent("All")
    expect(triggerElement)->Vitest.not->toBeDisabled
    expectListBoxIsClosed()

    await userEvent->TestingLibraryEvent.click(triggerElement)
    expectListBoxIsOpened()

    await userEvent->TestingLibraryEvent.click(baseElement)
    expectListBoxIsClosed()

    await userEvent->TestingLibraryEvent.click(triggerElement)
    expectListBoxIsOpened()

    await userEvent->TestingLibraryEvent.click(triggerElement)
    expectListBoxIsClosed()
    expect(onChange)->toHaveBeenCalledTimes(0)
  })

  itPromise("should render with a label & value and not be possible to interact with", async () => {
    let onChange = fn1(ignore)
    let sections = [
      {
        Select.items: [
          {
            key: "default",
            label: "All",
            value: None,
          },
          {
            key: "first",
            label: "First",
            value: Some("first"),
          },
        ],
      },
    ]

    render(
      <TestableSelect
        label="Order" disabled=true defaultValue=Some("first") sections onChange={onChange->fn}
      />,
    )->ignore

    let triggerElement = screen->getByRoleExn(#button)

    expect(triggerElement)->toBeVisible
    expect(triggerElement)->toHaveTextContent("Order: First")
    expect(triggerElement)->toBeDisabled
    expectListBoxIsClosed()

    await userEvent->TestingLibraryEvent.click(triggerElement)
    expectListBoxIsClosed()
    expect(onChange)->toHaveBeenCalledTimes(0)
  })

  itPromise(
    "should render with different titlted sections with a disabled option, and change value on selection",
    async () => {
      let onChange = fn1(ignore)
      let sections = [
        {
          Select.items: [
            {
              key: "default",
              label: "All",
              value: None,
            },
          ],
        },
        {
          title: "Wines",
          items: [
            {
              key: "cheverny-red",
              label: "Cheverny Rouge",
              value: Some("cheverny-red"),
              disabled: true,
            },
            {
              key: "colombard-sauvignon-white",
              label: "Colombard Sauvignon Blanc",
              value: Some("colombard-sauvignon-white"),
            },
          ],
        },
        {
          title: "Beers",
          items: [
            {
              key: "cibulle",
              label: "La Cibulle",
              value: Some("cibulle"),
            },
          ],
        },
      ]

      render(
        <TestableSelect label="Drink" defaultValue=None sections onChange={onChange->fn} />,
      )->ignore

      let triggerElement = screen->getByRoleExn(#button)

      expect(triggerElement)->toBeVisible
      expect(triggerElement)->toHaveTextContent("Drink: All")
      expectListBoxIsClosed()

      await userEvent->TestingLibraryEvent.click(triggerElement)

      expectListBoxIsOpened()
      expect(onChange)->toHaveBeenCalledTimes(0)

      let listboxElement = screen->getByRoleExn(#listbox)
      let (_section1, section2, _section3) = within(listboxElement)->getAllByRoleExn3(#presentation)

      let sectionTitleElement = within(section2)->getByTextExn("Wines")
      let groupElement = within(section2)->getByRoleExn(#group)

      expect(sectionTitleElement)->toBeVisible
      expect(groupElement)->toBeVisible

      let sectionTitleElementId = sectionTitleElement->DomElement.getAttribute("id")->Option.getExn
      let groupElementLabelledById =
        groupElement->DomElement.getAttribute("aria-labelledby")->Option.getExn

      expect(sectionTitleElementId)->toBe(groupElementLabelledById)

      let (option1, option2) = within(groupElement)->getAllByRoleExn2(#option)

      expect(option1)->toHaveAttributeValue("aria-disabled", "true")
      expect(option1)->toHaveAttributeValue("aria-selected", "false")
      expect(option1)->toHaveTextContent("Cheverny Rouge")
      expect(option1)->Vitest.not->toHaveFocus

      await userEvent->TestingLibraryEvent.click(option1)
      expectListBoxIsOpened()

      expect(option2)->toHaveAttributeValue("aria-selected", "false")
      expect(option2)->toHaveTextContent("Colombard Sauvignon Blanc")
      expect(option2)->Vitest.not->toHaveFocus
      expect(onChange)->toHaveBeenCalledTimes(0)

      await userEvent->TestingLibraryEvent.hover(option2)
      expect(option2)->toHaveFocus

      await userEvent->TestingLibraryEvent.click(option2)
      expectListBoxIsClosed()

      expect(triggerElement)->toHaveTextContent("Drink: Colombard Sauvignon Blanc")
      expect(onChange)->toHaveBeenCalledTimes(1)
      expect(onChange)->toHaveBeenCalledWith1(Some("colombard-sauvignon-white"))

      await userEvent->TestingLibraryEvent.click(triggerElement)
      expectListBoxIsOpened()

      let option2SelectedElement =
        screen->getByRoleWithOptionsExn(#option, {name: "Colombard Sauvignon Blanc"})

      expect(option2SelectedElement)->toHaveAttributeValue("aria-selected", "true")
      expect(option2SelectedElement)->toHaveTextContent("Colombard Sauvignon Blanc")
      expect(option2SelectedElement)->toHaveFocus
      expect(onChange)->toHaveBeenCalledTimes(1)
    },
  )

  itPromise("should render with a placeholder when there is no value selected", async () => {
    let onChange = fn1(ignore)
    let sections = [
      {
        Select.title: "Wines",
        items: [
          {
            key: "colombard-sauvignon-white",
            label: "Colombard Sauvignon Blanc",
            value: Some("colombard-sauvignon-white"),
          },
        ],
      },
      {
        title: "Beers",
        items: [
          {
            key: "cibulle",
            label: "La Cibulle",
            value: Some("cibulle"),
          },
        ],
      },
    ]

    let {rerender} = render(<TestableSelect defaultValue=None sections onChange={onChange->fn} />)

    let triggerElement = screen->getByRoleExn(#button)

    expect(triggerElement)->toBeVisible
    expect(triggerElement)->toHaveTextContent("Select")

    rerender(
      <TestableSelect
        label="Custom"
        placeholder="Your selection"
        defaultValue=None
        sections
        onChange={onChange->fn}
      />,
    )->ignore

    let triggerElement = screen->getByRoleExn(#button)

    expect(triggerElement)->toHaveTextContent("Custom: Your selection")
    expect(onChange)->toHaveBeenCalledTimes(0)
    expectListBoxIsClosed()
  })

  itPromise("should be possible to interact and navigate with keyboard when focused", async () => {
    let onChange = fn1(ignore)
    let sections = [
      {
        Select.title: "Wines",
        items: [
          {
            key: "cheverny-red",
            label: "Cheverny Rouge",
            value: Some("cheverny-red"),
            disabled: true,
          },
          {
            key: "colombard-sauvignon-white",
            label: "Colombard Sauvignon Blanc",
            value: Some("colombard-sauvignon-white"),
          },
        ],
      },
      {
        title: "Beers",
        items: [
          {
            key: "cibulle",
            label: "La Cibulle",
            value: Some("cibulle"),
          },
        ],
      },
    ]

    render(
      <TestableSelect
        label="Drink" defaultValue=Some("cibulle") sections onChange={onChange->fn}
      />,
    )->ignore

    let triggerElement = screen->getByRoleExn(#button)

    expect(triggerElement)->toBeVisible
    expect(triggerElement)->toHaveTextContent("La Cibulle")
    expect(onChange)->toHaveBeenCalledTimes(0)
    expectListBoxIsClosed()

    await userEvent->TestingLibraryEvent.keyboard("{arrowright}")
    expect(triggerElement)->toHaveTextContent("La Cibulle")
    expect(triggerElement)->Vitest.not->toHaveFocus
    expect(onChange)->toHaveBeenCalledTimes(0)

    await userEvent->TestingLibraryEvent.tab
    expect(triggerElement)->toHaveFocus
    expect(onChange)->toHaveBeenCalledTimes(0)

    await userEvent->TestingLibraryEvent.keyboard("{arrowright}")
    expect(onChange)->toHaveBeenCalledTimes(0)

    await userEvent->TestingLibraryEvent.keyboard("{arrowleft}")
    expect(onChange)->toHaveBeenCalledTimes(1)
    expect(triggerElement)->toHaveTextContent("Colombard Sauvignon Blanc")

    onChange->mockClear

    await userEvent->TestingLibraryEvent.keyboard("{enter}")
    await userEvent->TestingLibraryEvent.keyboard("{arrowup}")
    expect(onChange)->toHaveBeenCalledTimes(0)
    expectListBoxIsOpened()

    await userEvent->TestingLibraryEvent.keyboard("{enter}")
    expect(onChange)->toHaveBeenCalledTimes(1)
    expect(triggerElement)->toHaveTextContent("Colombard Sauvignon Blanc")
    await waitFor(() => expect(triggerElement)->toHaveFocus)

    expectListBoxIsClosed()

    await waitFor(() => expect(triggerElement)->toHaveFocus)
    await userEvent->TestingLibraryEvent.keyboard("{enter}")

    expectListBoxIsOpened()

    onChange->mockClear

    expect(triggerElement)->Vitest.not->toHaveFocus
    await userEvent->TestingLibraryEvent.keyboard("{escape}")
    expect(onChange)->toHaveBeenCalledTimes(0)

    expectListBoxIsClosed()

    await userEvent->TestingLibraryEvent.click(triggerElement)
    expect(onChange)->toHaveBeenCalledTimes(0)

    expectListBoxIsOpened()

    await userEvent->TestingLibraryEvent.tab
    expect(triggerElement)->Vitest.not->toHaveFocus
    expect(onChange)->toHaveBeenCalledTimes(0)

    expectListBoxIsClosed()
  })

  itPromise("should render with custom trigger view", async () => {
    let sections = [
      {
        Select.title: "Wines",
        items: [
          {
            key: "colombard-sauvignon-white",
            label: "Colombard Sauvignon Blanc",
            value: Some("colombard-sauvignon-white"),
          },
        ],
      },
      {
        title: "Beers",
        items: [
          {
            key: "cibulle",
            label: "La Cibulle",
            value: Some("cibulle"),
          },
        ],
      },
    ]

    let renderTriggerView = (~children, ~item as _, ~hovered, ~active, ~focused) =>
      <OverlayTriggerView
        label="Custom trigger" preset=#inputField({required: false}) hovered active focused>
        children
      </OverlayTriggerView>

    render(<TestableSelect renderTriggerView defaultValue=Some("cibulle") sections />)->ignore

    let triggerElement = screen->getByRoleExn(#button)
    let customTriggerElement = screen->getByTextExn("Custom trigger")

    expect(triggerElement)->toBeVisible
    expect(triggerElement)->toHaveTextContent("La Cibulle")
    expect(customTriggerElement)->toBeVisible
  })

  itPromise("should render with custom items content in list", async () => {
    let sections = [
      {
        Select.items: [
          {
            key: "colombard-sauvignon-white",
            label: "Colombard Sauvignon Blanc",
            value: "colombard-sauvignon-white",
          },
        ],
      },
    ]

    let renderItemContent = ({Select.label: label}: Select.item<string>) =>
      <TextStyle> {("Wine: " ++ label)->React.string} </TextStyle>

    render(
      <TestableSelect renderItemContent defaultValue="colombard-sauvignon-white" sections />,
    )->ignore

    let triggerElement = screen->getByRoleExn(#button)
    let customTriggerElement = screen->getByTextExn("Colombard Sauvignon Blanc")

    expect(triggerElement)->toBeVisible
    expect(triggerElement)->toHaveTextContent("Colombard Sauvignon Blanc")
    expect(customTriggerElement)->toBeVisible

    await userEvent->TestingLibraryEvent.click(triggerElement)

    let listboxElement = screen->getByRoleExn(#listbox)
    let optionElement = within(listboxElement)->getAllByRoleExn(#option)->Array.getExn(0)

    expect(optionElement)->toBeVisible
    expect(optionElement)->toHaveTextContent("Wine: Colombard Sauvignon Blanc")
  })

  itPromise("should render with a search input over 5 items", async () => {
    let onChange = fn1(ignore)
    let sections = [
      {
        Select.title: "Wines",
        items: [
          {
            key: "colombard-sauvignon-white",
            label: "Colombard Sauvignon Blanc",
            value: Some("colombard-sauvignon-white"),
          },
        ],
      },
      {
        title: "Beers",
        items: [
          {
            key: "cibulle",
            label: "La Cibulle",
            value: Some("cibulle"),
          },
        ],
      },
    ]

    let {unmount} = render(
      <TestableSelect defaultValue=Some("cibulle") sections onChange={onChange->fn} />,
    )

    let triggerElement = screen->getByRoleExn(#button)

    await userEvent->TestingLibraryEvent.click(triggerElement)

    let inputSearchElement = screen->queryByPlaceholderText("Search")
    expectListBoxIsOpened()

    expect(triggerElement)->toBeVisible
    expect(inputSearchElement)->toBeNone

    unmount()

    let {unmount} = render(
      <TestableSelect
        searchable=true defaultValue=Some("cibulle") sections onChange={onChange->fn}
      />,
    )

    let triggerElement = screen->getByRoleExn(#button)

    await userEvent->TestingLibraryEvent.click(triggerElement)
    expectListBoxIsOpened()

    let inputSearchElement = screen->queryByPlaceholderText("Search")->Option.getExn

    expect(triggerElement)->toBeVisible
    expect(inputSearchElement)->toBeVisible
    expect(inputSearchElement)->toHaveFocus

    unmount()

    let sections = [
      {
        Select.items: [
          {
            key: "default",
            label: "All",
            value: None,
          },
        ],
      },
      {
        title: "Wines",
        items: [
          {
            key: "cheverny-red",
            label: "Cheverny Rouge",
            value: Some("cheverny-red"),
            disabled: true,
          },
          {
            key: "colombard-sauvignon-white",
            label: "Colombard Sauvignon Blanc",
            value: Some("colombard-sauvignon-white"),
          },
        ],
      },
      {
        title: "Beers",
        items: [
          {
            key: "cibulle",
            label: "La Cibulle",
            value: Some("cibulle"),
          },
        ],
      },
      {
        title: "Juices",
        items: [
          {
            key: "apple-juice",
            label: "Apple Juice",
            value: Some("apple-juice"),
          },
          {
            key: "carrot-juice",
            label: "Carrot jus",
            value: Some("carrot-juice"),
          },
        ],
      },
    ]

    let {unmount} = render(
      <TestableSelect defaultValue=Some("cibulle") sections onChange={onChange->fn} />,
    )

    let triggerElement = screen->getByRoleExn(#button)

    await userEvent->TestingLibraryEvent.click(triggerElement)
    expectListBoxIsOpened()

    let inputSearchElement = screen->queryByPlaceholderText("Search")->Option.getExn

    expect(triggerElement)->toBeVisible
    expect(inputSearchElement)->toBeVisible
    expect(inputSearchElement)->toHaveFocus

    unmount()

    render(
      <TestableSelect
        searchable=false defaultValue=Some("cibulle") sections onChange={onChange->fn}
      />,
    )->ignore

    let triggerElement = screen->getByRoleExn(#button)

    await userEvent->TestingLibraryEvent.click(triggerElement)
    expectListBoxIsOpened()

    let inputSearchElement = screen->queryByPlaceholderText("Search")

    expect(triggerElement)->toBeVisible
    expect(inputSearchElement)->toBeNone
  })

  itPromise("should filter out upon search items list except `sticky` items", async () => {
    let onChange = fn1(ignore)
    let sections = [
      {
        Select.items: [
          {
            key: "default",
            label: "All",
            value: None,
            sticky: true,
          },
        ],
      },
      {
        title: "Wines",
        items: [
          {
            key: "cheverny-red",
            label: "Cheverny Rouge",
            value: Some("cheverny-red"),
            disabled: true,
          },
          {
            key: "colombard-sauvignon-white",
            label: "Colombard Sauvignon Blanc",
            value: Some("colombard-sauvignon-white"),
          },
        ],
      },
      {
        title: "Beers",
        items: [
          {
            key: "cibulle",
            label: "La Cibulle",
            value: Some("cibulle"),
          },
        ],
      },
      {
        title: "Juices",
        items: [
          {
            key: "apple-juice",
            label: "Apple juice",
            value: Some("apple-juice"),
          },
          {
            key: "carrot-juice",
            label: "Carrot juice",
            value: Some("carrot-juice"),
          },
        ],
      },
    ]

    render(<TestableSelect defaultValue=None sections onChange={onChange->fn} />)->ignore

    let triggerElement = screen->getByRoleExn(#button)

    await userEvent->TestingLibraryEvent.click(triggerElement)
    expectListBoxIsOpened()

    let inputSearchElement = screen->queryByPlaceholderText("Search")->Option.getExn

    expect(triggerElement)->toBeVisible
    expect(inputSearchElement)->toBeVisible
    expect(inputSearchElement)->toHaveFocus
    expect(inputSearchElement)->toHaveDisplayValue("")

    let listboxElement = screen->getByRoleExn(#listbox)
    let optionElements = within(listboxElement)->getAllByRoleExn(#option)

    expect(optionElements)->toHaveLength(6)

    await userEvent->TestingLibraryEvent.type_(inputSearchElement, "test")

    let optionElements = within(listboxElement)->getAllByRoleExn(#option)

    expect(optionElements)->toHaveLength(1)
    expect(optionElements->Array.getExn(0))->toHaveTextContent("All")

    await userEvent->TestingLibraryEvent.clear(inputSearchElement)

    let optionElements = within(listboxElement)->getAllByRoleExn(#option)

    expect(optionElements)->toHaveLength(6)

    await userEvent->TestingLibraryEvent.type_(inputSearchElement, "jui")

    let optionElements = within(listboxElement)->getAllByRoleExn(#option)

    expect(optionElements)->toHaveLength(3)
    expect(optionElements->Array.getExn(0))->toHaveTextContent("All")
    expect(optionElements->Array.getExn(1))->toHaveTextContent("Apple juice")
    expect(optionElements->Array.getExn(2))->toHaveTextContent("Carrot juice")

    expectListBoxIsOpened()
    expect(onChange)->toHaveBeenCalledTimes(0)

    // FIXME - keyboard navigation is not possible anymore with a search input (see Select.res)

    // await userEvent->TestingLibraryEvent.keyboard("{arrowdown}")
    // await userEvent->TestingLibraryEvent.keyboard("{enter}")

    // expectListBoxIsClosed()
    // expect(onChange)->toHaveBeenCalledTimes(1)
    // expect(triggerElement)->toHaveTextContent("Carrot juice")

    // onChange->mockClear

    // await userEvent->TestingLibraryEvent.click(triggerElement)
    // expectListBoxIsOpened()

    // let inputSearchElement = screen->queryByPlaceholderText("Search")->Option.getExn

    // await userEvent->TestingLibraryEvent.type_(inputSearchElement, "CHEVERNY")

    // let listboxElement = screen->getByRoleExn(#listbox)
    // let optionElements = within(listboxElement)->getAllByRoleExn(#option)

    // expect(optionElements)->toHaveLength(2)
    // expect(optionElements->Array.getExn(0))->toHaveTextContent("All")
    // expect(optionElements->Array.getExn(1))->toHaveTextContent("Cheverny Rouge")

    // let inputSearchElement = screen->queryByPlaceholderText("Search")->Option.getExn

    // expect(inputSearchElement)->toHaveDisplayValue("CHEVERNY")

    // let (_focusButtonElement, clearButtonElement) =
    //   within(inputSearchElement->DomElement.closest("div")->Option.getExn)->getAllByRoleExn2(
    //     #button,
    //   )

    // await userEvent->TestingLibraryEvent.click(clearButtonElement)
    // expectListBoxIsOpened()

    // let inputSearchElement = screen->queryByPlaceholderText("Search")->Option.getExn
    // let listboxElement = screen->getByRoleExn(#listbox)
    // let optionElements = within(listboxElement)->getAllByRoleExn(#option)

    // expect(inputSearchElement)->toHaveDisplayValue("")
    // expect(optionElements)->toHaveLength(6)

    // expect(onChange)->toHaveBeenCalledTimes(0)
  })

  // NOTE - ARIA `descrbibed-by` not supported yet in ListBox option
  itPromise("should display item description in list if given", async () => {
    let onChange = fn1(ignore)
    let sections = [
      {
        Select.title: "Comparer à",
        items: [
          {
            key: "previous-period",
            label: "Previous period",
            value: Some("previous-period"),
            description: "June 1st 2022 - June 8th 2022",
          },
          {
            key: "previous-year",
            label: "Previous year",
            value: Some("previous-year"),
            description: "June 9th 2021 - June 16th 2021",
          },
        ],
      },
    ]

    render(<TestableSelect defaultValue=None sections onChange={onChange->fn} />)->ignore

    let triggerElement = screen->getByRoleExn(#button)

    await userEvent->TestingLibraryEvent.click(triggerElement)
    expectListBoxIsOpened()

    let listboxElement = screen->getByRoleExn(#listbox)
    let optionElements = within(listboxElement)->getAllByRoleExn(#option)

    expect(optionElements)->toHaveLength(2)

    expect(optionElements->Array.getExn(0))->toHaveTextContent(
      "Previous period June 1st 2022 - June 8th 2022",
    )
    expect(optionElements->Array.getExn(1))->toHaveTextContent(
      "Previous year June 9th 2021 - June 16th 2021",
    )
  })

  itPromise("should have its list opened by default", async () => {
    let defaultValue = Some("cibulle")
    let sections = [
      {
        Select.title: "Wines",
        items: [
          {
            key: "colombard-sauvignon-white",
            label: "Colombard Sauvignon Blanc",
            value: Some("colombard-sauvignon-white"),
          },
        ],
      },
      {
        title: "Beers",
        items: [
          {
            key: "cibulle",
            label: "La Cibulle",
            value: Some("cibulle"),
          },
        ],
      },
    ]

    render(<TestableSelect defaultOpen=true defaultValue sections />)->ignore

    expectListBoxIsOpened()
  })

  itPromise("should display an error message in `field` preset", async () => {
    let defaultValue = Some("cibulle")
    let sections = [
      {
        Select.title: "Wines",
        items: [
          {
            key: "colombard-sauvignon-white",
            label: "Colombard Sauvignon Blanc",
            value: Some("colombard-sauvignon-white"),
          },
        ],
      },
      {
        title: "Beers",
        items: [
          {
            key: "cibulle",
            label: "La Cibulle",
            value: Some("cibulle"),
          },
        ],
      },
    ]

    render(
      <TestableSelect
        preset=#inputField({
          OverlayTriggerView.required: false,
          errorMessage: "Custom error message",
        })
        defaultValue
        sections
      />,
    )->ignore

    expect(screen->queryByText("Custom error message"))->toBeDefined
  })

  itPromise("should display a spinner svg in listbox instead of items", async () => {
    let defaultValue = Some("cibulle")
    let sections = [
      {
        Select.title: "Wines",
        items: [
          {
            key: "colombard-sauvignon-white",
            label: "Colombard Sauvignon Blanc",
            value: Some("colombard-sauvignon-white"),
          },
        ],
      },
      {
        title: "Beers",
        items: [
          {
            key: "cibulle",
            label: "La Cibulle",
            value: Some("cibulle"),
          },
        ],
      },
    ]

    let {rerender, container} = render(<TestableSelect loading=false defaultValue sections />)

    expect(within(container)->queryByRoleWithOptions(#status, {name: "Loading..."}))->toBeNone

    rerender(<TestableSelect loading=true defaultValue sections />)->ignore

    let triggerElement = screen->getByRoleExn(#button)

    await userEvent->TestingLibraryEvent.click(triggerElement)

    expectListBoxIsOpened()

    let listboxElement = screen->getByRoleExn(#listbox)

    expect(
      within(listboxElement)->getByRoleWithOptionsExn(#status, {name: "Loading..."}),
    )->toBeVisible
  })

  itPromise("should call render the passed footer in the overlay", async () => {
    let defaultValue = Some("cibulle")
    let sections = [
      {
        Select.title: "Wines",
        items: [
          {
            key: "colombard-sauvignon-white",
            label: "Colombard Sauvignon Blanc",
            value: Some("colombard-sauvignon-white"),
          },
        ],
      },
      {
        title: "Beers",
        items: [
          {
            key: "cibulle",
            label: "La Cibulle",
            value: Some("cibulle"),
          },
        ],
      },
    ]

    let overlayFooterLink = {Select.text: "footer link", to: Route("#")}

    render(
      <Providers>
        <TestableSelect overlayFooterLink defaultValue sections />
      </Providers>,
    )->ignore

    let triggerElement = screen->getByRoleExn(#button)

    await userEvent->TestingLibraryEvent.click(triggerElement)

    expectListBoxIsOpened()

    let overlayFooterLinkButton = screen->getByRoleWithOptionsExn(#link, {name: "footer link"})

    expect(overlayFooterLinkButton)->toBeVisible
    expectListBoxIsOpened()

    await userEvent->TestingLibraryEvent.click(overlayFooterLinkButton)

    expectListBoxIsClosed()
  })

  itPromise("should call `onToggle` when the select is opening or closing", async () => {
    let onToggle = fn1(ignore)
    let defaultValue = Some("cibulle")
    let sections = [
      {
        Select.title: "Wines",
        items: [
          {
            key: "colombard-sauvignon-white",
            label: "Colombard Sauvignon Blanc",
            value: Some("colombard-sauvignon-white"),
          },
        ],
      },
      {
        title: "Beers",
        items: [
          {
            key: "cibulle",
            label: "La Cibulle",
            value: Some("cibulle"),
          },
        ],
      },
    ]

    render(<TestableSelect onToggle={onToggle->fn} defaultValue sections />)->ignore

    let triggerElement = screen->getByRoleExn(#button)

    expect(onToggle)->toHaveBeenCalledTimes(0)
    expectListBoxIsClosed()

    await userEvent->TestingLibraryEvent.click(triggerElement)

    expectListBoxIsOpened()
    expect(onToggle)->toHaveBeenCalledTimes(1)
    expect(onToggle)->toHaveBeenCalledWith1(true)

    // await userEvent->TestingLibraryEvent.click(triggerElement)

    // expect(onToggle)->toHaveBeenCalledTimes(2)
    // expect(onToggle)->toHaveBeenCalledWith1(false)
  })
})
