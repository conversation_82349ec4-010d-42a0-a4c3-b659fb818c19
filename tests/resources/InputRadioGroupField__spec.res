open Vitest
open TestingLibraryReact

module TestableInputRadioGroupField = {
  @react.component
  let make = (~onChange) => {
    let (value, setValue) = React.useState(() => "option 1")

    <InputRadioGroupField
      label="My label"
      value
      required={false}
      onChange={value => {
        onChange->fn(value)
        setValue(_ => value)
      }}
      options={["option 1", "option 2", "option 3"]}
      optionToText={value => value}
    />
  }
}

itPromise("should trigger onChange callback on radio element click", async () => {
  let userEvent = TestingLibraryEvent.setup()
  let onChange = fn1(ignore)

  render(<TestableInputRadioGroupField onChange />)->ignore

  let elements = screen->getAllByRoleExn(#button)

  expect(elements)->toHaveLength(3)
  expect(onChange)->toHaveBeenCalledTimes(0)
  expect(elements->Array.getExn(0))->toBeVisible

  await userEvent->TestingLibraryEvent.click(elements->Array.getExn(0))

  expect(onChange)->toHaveBeenCalledTimes(1)
  expect(onChange)->toHaveBeenCalledWith1("option 1")
  expect(elements->Array.getExn(1))->toBeVisible

  await userEvent->TestingLibraryEvent.click(elements->Array.getExn(1))

  expect(onChange)->toHaveBeenCalledTimes(2)
  expect(onChange)->toHaveBeenCalledWith1("option 2")
  expect(elements->Array.getExn(2))->toBeVisible

  await userEvent->TestingLibraryEvent.click(elements->Array.getExn(2))

  expect(onChange)->toHaveBeenCalledTimes(3)
  expect(onChange)->toHaveBeenCalledWith1("option 3")
})

todo("should have a default value")
