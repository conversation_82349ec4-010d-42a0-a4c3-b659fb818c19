open Vitest
open TestingLibraryReact

let userEvent = TestingLibraryEvent.setup()

module TestableInputSearchComboBoxField = {
  @react.component
  let make = (
    ~label="My Label",
    ~disabled=?,
    ~loading=?,
    ~placeholder=?,
    ~errorMessage=?,
    ~noResultLabel=?,
    ~showResults=?,
    ~defaultValue="",
    ~items=[],
    ~onInputChange=fn1(ignore)->fn,
    ~onSelectionChange=fn1(ignore)->fn,
  ) => {
    let (value, setValue) = React.useState(() => defaultValue)

    <InputSearchComboBoxField
      label
      ?disabled
      ?loading
      ?placeholder
      ?errorMessage
      ?noResultLabel
      ?showResults
      value
      items
      onInputChange={value => {
        onInputChange(value)
        setValue(_ => value)
      }}
      onSelectionChange
    />
  }
}

let expectListBoxIsOpened = () => {
  let inputElement = screen->getByRoleWithOptionsExn(#combobox, {expanded: true, hidden: true})

  expect(inputElement)->toBeVisible
  expect(screen->getByRoleExn(#listbox))->toBeVisible
}

let expectListBoxIsClosed = () => {
  let inputElement = screen->getByRoleExn(#combobox)

  expect(inputElement)->toBeVisible
  expect(screen->queryByRole(#listbox))->toBeNone
}

type testDialogOption = {
  label: string,
  selected: bool,
}
let expectListBoxOptionsToStrictEqual = result => {
  let options = screen->getAllByRoleExn(#option)

  expect(options)->toHaveLength(2)
  let htmlOptionElements =
    options->Array.map(option =>
      option->WebAPI.DomElement.asHtmlOptionElement->WebAPI.HtmlOptionElement.textContent
    )
  expect(htmlOptionElements)->toStrictEqual([
    (result->Array.getExn(0)).label,
    (result->Array.getExn(1)).label,
  ])
  expect(options[0])->toHaveAttributeValue(
    "aria-selected",
    `${(result->Array.getExn(0)).selected->Obj.magic}`,
  )
  expect(options[1])->toHaveAttributeValue(
    "aria-selected",
    `${(result->Array.getExn(1)).selected->Obj.magic}`,
  )
}

let clickListboxOption = async index => {
  let options = screen->getAllByRoleExn(#option)

  await userEvent->TestingLibraryEvent.click(options->Array.getExn(index))
}

type testFieldChange<'item> = {
  item: 'item,
  label: string,
  calledTimes: int,
}
let expectSelectionChange = (onChange, result) => {
  let inputElement = screen->getByRoleExn(#combobox)

  expect(inputElement)->toHaveValue(result.label)
  expect(onChange)->toHaveBeenLastCalledWith1(result.item)
  expect(onChange)->toHaveBeenCalledTimes(result.calledTimes)
}

itPromise("should not be possible to interact with it when disabled", async () => {
  let onInputChange = fn1(ignore)
  let onSelectionChange = fn1(ignore)

  let items = [
    {InputSearchComboBoxField.key: "one", label: "My first key", value: Some("My first key")},
    {key: "two", label: "My second key", value: Some("My second key")},
  ]

  render(
    <TestableInputSearchComboBoxField
      disabled=true
      items
      onInputChange={onInputChange->fn}
      onSelectionChange={onSelectionChange->fn}
    />,
  )->ignore

  let inputElement = screen->getByRoleExn(#combobox)

  expect(inputElement)->toBeVisible
  expect(inputElement)->Vitest.not->toHaveFocus
  expectListBoxIsClosed()

  await userEvent->TestingLibraryEvent.click(inputElement)
  await userEvent->TestingLibraryEvent.type_(inputElement, "test")

  expectListBoxIsClosed()
  expect(inputElement)->toHaveValue("")
  expect(inputElement)->Vitest.not->toHaveFocus
  expect(onInputChange)->toHaveBeenCalledTimes(0)
  expect(onSelectionChange)->toHaveBeenCalledTimes(0)
})

itPromise("should interact with the options and unfocus the input upon selection", async () => {
  let onInputChange = fn1(ignore)
  let onSelectionChange = fn1(ignore)

  let items = [
    {InputSearchComboBoxField.key: "one", label: "My first key", value: Some("My first key")},
    {key: "two", label: "My second key", value: None},
  ]

  let {baseElement} = render(
    <TestableInputSearchComboBoxField
      items onInputChange={onInputChange->fn} onSelectionChange={onSelectionChange->fn}
    />,
  )

  let inputElement = screen->getByRoleExn(#combobox)

  expect(inputElement)->toBeVisible
  expect(inputElement)->Vitest.not->toHaveFocus
  expectListBoxIsClosed()

  await userEvent->TestingLibraryEvent.type_(inputElement, "searching")

  expectListBoxIsOpened()
  expect(inputElement)->toHaveFocus
  expect(inputElement)->toHaveValue("searching")
  expect(onInputChange)->toHaveBeenLastCalledWith1("searching")
  expect(onSelectionChange)->toHaveBeenCalledTimes(0)
  expect(onInputChange)->toHaveBeenLastCalledWith1("searching")
  expectListBoxOptionsToStrictEqual([
    {label: "My first key", selected: false},
    {label: "My second key", selected: false},
  ])

  mockClear(onInputChange)

  await clickListboxOption(0)

  expectListBoxIsClosed()
  expect(inputElement)->toHaveFocus
  expect(inputElement)->toHaveValue("My first key")
  expect(onInputChange)->toHaveBeenLastCalledWith1("My first key")
  expect(onInputChange)->toHaveBeenCalledTimes(1)
  expectSelectionChange(
    onSelectionChange,
    {
      item: items->Array.getExn(0),
      label: "My first key",
      calledTimes: 1,
    },
  )

  await userEvent->TestingLibraryEvent.type_(inputElement, "second")
  await clickListboxOption(1)

  expectListBoxIsClosed()
  expect(inputElement)->toHaveValue("My second key")
  expectSelectionChange(
    onSelectionChange,
    {
      item: items->Array.getExn(1),
      label: "My second key",
      calledTimes: 2,
    },
  )

  mockClear(onInputChange)
  mockClear(onSelectionChange)

  await userEvent->TestingLibraryEvent.click(baseElement)

  expect(inputElement)->Vitest.not->toHaveFocus
  expect(onInputChange)->toHaveBeenCalledTimes(0)
  expect(onSelectionChange)->toHaveBeenCalledTimes(0)
})

itPromise("should take a default input value and close the list when clearing value", async () => {
  let onInputChange = fn1(ignore)
  let onSelectionChange = fn1(ignore)

  let items = [
    {InputSearchComboBoxField.key: "one", label: "My first key", value: Some("My first key")},
    {key: "two", label: "My second key", value: None},
  ]

  render(
    <TestableInputSearchComboBoxField
      defaultValue="My first key"
      placeholder="Type here"
      items
      onInputChange={onInputChange->fn}
      onSelectionChange={onSelectionChange->fn}
    />,
  )->ignore

  let inputElement = screen->getByRoleExn(#combobox)
  let containerElement = inputElement->WebAPI.DomElement.closest("div")->Option.getExn
  let (focusButtonElement, clearButtonElement) = within(containerElement)->getAllByRoleExn2(#button)

  expect(inputElement)->toBeVisible
  expect(inputElement)->toHaveAttributeValue("placeholder", "Type here")
  expect(inputElement)->Vitest.not->toHaveFocus
  expect(inputElement)->toHaveValue("My first key")
  expectListBoxIsClosed()

  await userEvent->TestingLibraryEvent.click(focusButtonElement)

  expectListBoxIsClosed()
  expect(inputElement)->toHaveFocus
  expect(onInputChange)->toHaveBeenCalledTimes(0)
  expect(onSelectionChange)->toHaveBeenCalledTimes(0)

  await userEvent->TestingLibraryEvent.click(clearButtonElement)

  expectListBoxIsClosed()
  expect(inputElement)->toHaveFocus
  expect(inputElement)->toHaveValue("")
  expect(onInputChange)->toHaveBeenLastCalledWith1("")
  expect(onInputChange)->toHaveBeenCalledTimes(1)
})

itPromise("should display an error message", async () => {
  render(
    <TestableInputSearchComboBoxField label="My Label" errorMessage="Custom error message" />,
  )->ignore

  let inputElement = screen->getByRoleExn(#combobox)

  expect(inputElement)->toBeVisible
  expect(inputElement)->Vitest.not->toHaveFocus
  expect(screen->queryByText("Custom error message"))->toBeDefined
})

itPromise("should display a spinner in the container element when loading", async () => {
  render(<TestableInputSearchComboBoxField loading=true />)->ignore

  let inputElement = screen->getByRoleExn(#combobox)
  let containerElement = inputElement->WebAPI.DomElement.closest("div")->Option.getExn

  expect(inputElement)->toBeVisible
  expect(
    within(containerElement)->getByRoleWithOptionsExn(#status, {name: "Loading..."}),
  )->toBeVisible
})

itPromise("should not open the items list", async () => {
  let items = [
    {InputSearchComboBoxField.key: "one", label: "My first key", value: Some("My first key")},
    {key: "two", label: "My second key", value: Some("My second key")},
  ]

  let {rerender} = render(<TestableInputSearchComboBoxField items />)

  let inputElement = screen->getByRoleExn(#combobox)

  await userEvent->TestingLibraryEvent.type_(inputElement, "?")
  await userEvent->TestingLibraryEvent.clear(inputElement)

  expectListBoxIsClosed()
  expect(inputElement)->toHaveFocus
  expect(inputElement)->toHaveValue("")

  rerender(<TestableInputSearchComboBoxField items=[] />)->ignore

  await userEvent->TestingLibraryEvent.type_(inputElement, "?")

  expectListBoxIsOpened() // FIXME - since ReactAriaStately upgrade
  // expectListBoxIsClosed()
  expect(inputElement)->toHaveFocus
  expect(inputElement)->toHaveValue("?")

  rerender(<TestableInputSearchComboBoxField showResults=false />)->ignore

  await userEvent->TestingLibraryEvent.type_(inputElement, "?")

  expectListBoxIsClosed()
  expect(inputElement)->toHaveFocus
  expect(inputElement)->toHaveValue("??")

  rerender(<TestableInputSearchComboBoxField loading=true />)->ignore

  await userEvent->TestingLibraryEvent.type_(inputElement, "?")

  expectListBoxIsClosed()
  expect(inputElement)->toHaveFocus
  expect(inputElement)->toHaveValue("???")

  rerender(<TestableInputSearchComboBoxField items />)->ignore

  expectListBoxIsClosed()
})

itPromise("should display a no result item only", async () => {
  let {rerender} = render(
    <TestableInputSearchComboBoxField noResultLabel="Nothing here" items=[] />,
  )

  let inputElement = screen->getByRoleExn(#combobox)

  expect(inputElement)->toBeVisible
  expect(inputElement)->Vitest.not->toHaveFocus
  expectListBoxIsClosed()

  await userEvent->TestingLibraryEvent.type_(inputElement, "searching")

  expectListBoxIsOpened()
  expect(inputElement)->toHaveFocus

  let listbox = screen->getByRoleExn(#listbox)
  let noResultItem = within(listbox)->getByTextWithOptionsExn("Nothing here", {selector: `span`})

  expect(noResultItem)->toBeVisible
  expect(noResultItem)->toHaveTextContent("Nothing here")

  let items = [
    {InputSearchComboBoxField.key: "one", label: "My first key", value: Some("My first key")},
    {key: "two", label: "My second key", value: Some("My second key")},
  ]

  rerender(<TestableInputSearchComboBoxField noResultLabel="Nothing here" items />)->ignore

  expectListBoxIsOpened()
  expect(inputElement)->toHaveFocus
  expectListBoxOptionsToStrictEqual([
    {label: "My first key", selected: false},
    {label: "My second key", selected: false},
  ])
})
