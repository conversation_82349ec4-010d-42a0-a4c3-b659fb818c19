open Vitest
open TestingLibraryReact
open WebAPI

describe("computeColumnStickyPositionState", () => {
  let mockColumnLayout = (~x, ~width, ~index, ~sticky) => {
    Table.TableLayout.key: index->Int.toString,
    relativeRect: Some(DomRect.make(~x, ~y=0., ~width, ~height=0.)),
    index,
    sticky,
  }

  it("should compute position in a single column layout", () => {
    let layoutState = [mockColumnLayout(~index=0, ~sticky=false, ~x=0., ~width=100.)]
    let (left, sticking) = Table.TableLayout.computeColumnStickyPositionState(
      ~index=0,
      ~layoutState,
      ~scrolled=true,
    )

    expect(left)->toBe(0.)
    expect(sticking)->toBe(false)

    let layoutState = [mockColumnLayout(~index=0, ~sticky=true, ~x=0., ~width=100.)]
    let (left, sticking) = Table.TableLayout.computeColumnStickyPositionState(
      ~index=0,
      ~layoutState,
      ~scrolled=true,
    )

    expect(left)->toBe(0.)
    expect(sticking)->toBe(true)

    let layoutState = [mockColumnLayout(~index=0, ~sticky=true, ~x=0., ~width=100.)]
    let (left, sticking) = Table.TableLayout.computeColumnStickyPositionState(
      ~index=0,
      ~layoutState,
      ~scrolled=false,
    )

    expect(left)->toBe(0.)
    expect(sticking)->toBe(false)

    let layoutState = [mockColumnLayout(~index=0, ~sticky=true, ~x=0., ~width=100.)]
    let (left, sticking) = Table.TableLayout.computeColumnStickyPositionState(
      ~index=1,
      ~layoutState,
      ~scrolled=false,
    )

    expect(left)->toBe(0.)
    expect(sticking)->toBe(false)
  })

  it("should compute position in a multiple columns layout", () => {
    let layoutState = [
      mockColumnLayout(~index=0, ~sticky=true, ~x=0., ~width=100.),
      mockColumnLayout(~index=1, ~sticky=false, ~x=100., ~width=120.),
    ]
    let (left, sticking) = Table.TableLayout.computeColumnStickyPositionState(
      ~index=0,
      ~layoutState,
      ~scrolled=true,
    )

    expect(left)->toBe(0.)
    expect(sticking)->toBe(true)

    let layoutState = [
      mockColumnLayout(~index=0, ~sticky=true, ~x=0., ~width=100.),
      mockColumnLayout(~index=1, ~sticky=false, ~x=100., ~width=120.),
    ]
    let (left, sticking) = Table.TableLayout.computeColumnStickyPositionState(
      ~index=0,
      ~layoutState,
      ~scrolled=true,
    )

    expect(left)->toBe(0.)
    expect(sticking)->toBe(true)

    let layoutState = [
      mockColumnLayout(~index=0, ~sticky=true, ~x=0., ~width=100.),
      mockColumnLayout(~index=1, ~sticky=true, ~x=100., ~width=120.),
    ]
    let (left, sticking) = Table.TableLayout.computeColumnStickyPositionState(
      ~index=0,
      ~layoutState,
      ~scrolled=true,
    )

    expect(left)->toBe(0.)
    expect(sticking)->toBe(true)
  })

  it("should compute position when the column isn't the first in the layout", () => {
    let layoutState = [
      mockColumnLayout(~index=0, ~sticky=false, ~x=0., ~width=100.),
      mockColumnLayout(~index=1, ~sticky=true, ~x=0., ~width=120.),
    ]
    let (left, sticking) = Table.TableLayout.computeColumnStickyPositionState(
      ~index=1,
      ~layoutState,
      ~scrolled=true,
    )

    expect(left)->toBe(0.)
    expect(sticking)->toBe(true)

    let layoutState = [
      mockColumnLayout(~index=0, ~sticky=false, ~x=0., ~width=100.),
      mockColumnLayout(~index=1, ~sticky=true, ~x=1., ~width=120.),
    ]
    let (left, sticking) = Table.TableLayout.computeColumnStickyPositionState(
      ~index=1,
      ~layoutState,
      ~scrolled=true,
    )

    expect(left)->toBe(0.)
    expect(sticking)->toBe(false)
  })

  it("should compute position when there are multiple sticky columns in the layout", () => {
    let layoutState = [
      mockColumnLayout(~index=0, ~sticky=true, ~x=0., ~width=100.),
      mockColumnLayout(~index=1, ~sticky=true, ~x=100., ~width=120.),
    ]
    let (left, sticking) = Table.TableLayout.computeColumnStickyPositionState(
      ~index=0,
      ~layoutState,
      ~scrolled=true,
    )

    expect(left)->toBe(0.)
    expect(sticking)->toBe(true)

    let layoutState = [
      mockColumnLayout(~index=0, ~sticky=true, ~x=0., ~width=100.),
      mockColumnLayout(~index=1, ~sticky=true, ~x=130., ~width=120.),
    ]
    let (left, sticking) = Table.TableLayout.computeColumnStickyPositionState(
      ~index=1,
      ~layoutState,
      ~scrolled=true,
    )

    expect(left)->toBe(100.)
    expect(sticking)->toBe(false)

    let layoutState = [
      mockColumnLayout(~index=0, ~sticky=true, ~x=0., ~width=100.),
      mockColumnLayout(~index=1, ~sticky=true, ~x=99., ~width=120.),
    ]
    let (left, sticking) = Table.TableLayout.computeColumnStickyPositionState(
      ~index=1,
      ~layoutState,
      ~scrolled=true,
    )

    expect(left)->toBe(100.)
    expect(sticking)->toBe(true)

    let layoutState = [
      mockColumnLayout(~index=0, ~sticky=true, ~x=0., ~width=100.),
      mockColumnLayout(~index=1, ~sticky=false, ~x=0., ~width=100.),
      mockColumnLayout(~index=2, ~sticky=true, ~x=99., ~width=120.),
    ]
    let (left, sticking) = Table.TableLayout.computeColumnStickyPositionState(
      ~index=2,
      ~layoutState,
      ~scrolled=true,
    )

    expect(left)->toBe(100.)
    expect(sticking)->toBe(true)
  })
})

test("getRelativeBoundingRect", () => {
  let src = DomRect.make(~x=70., ~y=50., ~width=100., ~height=100.)
  let dest = DomRect.make(~x=100., ~y=100., ~width=99., ~height=99.)

  let resultRect = Table.TableLayout.getRelativeBoundingRect(~src, ~dest)

  expect(resultRect->DomRect.x)->toBe(30.)
  expect(resultRect->DomRect.y)->toBe(50.)
  expect(resultRect->DomRect.width)->toBe(99.)
  expect(resultRect->DomRect.height)->toBe(99.)
})

describe("component", () => {
  module TestableTableRow = {
    type t = {
      id: string,
      name: string,
      stock: int,
    }

    let keyExtractor = row => row.id
  }

  let userEvent = TestingLibraryEvent.setup()

  it("should render a table with multiple columns and rows", () => {
    let _ = render(
      <Table
        keyExtractor=TestableTableRow.keyExtractor
        columns=[
          {
            Table.key: "name",
            name: "Name",
            render: ({data}) => data.TestableTableRow.name->React.string,
          },
          {
            key: "stock",
            name: "Stock",
            render: ({data}) => data.stock->Int.toString->React.string,
          },
        ]
        rows=[
          {
            TestableTableRow.id: "id-0",
            name: "Product 0",
            stock: 5,
          },
          {
            id: "id-1",
            name: "Product 1",
            stock: 3,
          },
        ]
      />,
    )

    let table = screen->getByRoleWithOptionsExn(#grid, {name: "table"})

    expect(table)->toBeVisible
    expect(table)->toHaveAttributeValue("aria-label", "table")

    let (thead, tbody) = within(table)->getAllByRoleExn2(#rowgroup)

    let rowHeaders = within(thead)->getByRoleExn(#row)
    let headers = within(rowHeaders)->getAllByRoleExn(#columnheader)

    expect(headers)->toHaveLength(2)
    expect(headers->Array.getExn(0))->toHaveTextContent("Name")
    expect(headers->Array.getExn(1))->toHaveTextContent("Stock")

    let (rowA, rowB) = within(tbody)->getAllByRoleExn2(#row)

    let rowAcellA = within(rowA)->getByRoleExn(#rowheader)
    let rowAcellB = within(rowA)->getByRoleExn(#gridcell)

    expect(rowAcellA)->toHaveTextContent("Product 0")
    expect(rowAcellB)->toHaveTextContent("5")

    let rowBcellA = within(rowB)->getByRoleExn(#rowheader)
    let rowBcellB = within(rowB)->getByRoleExn(#gridcell)

    expect(rowBcellA)->toHaveTextContent("Product 1")
    expect(rowBcellB)->toHaveTextContent("3")
  })

  itPromise("should render a table with multi selection", async () => {
    let onSelectChange = fn1(ignore)

    let columns = [
      {
        Table.key: "name",
        name: "Name",
        render: ({data}) => data.TestableTableRow.name->React.string,
      },
      {
        key: "stock",
        name: "Stock",
        render: ({data}) => data.stock->Int.toString->React.string,
      },
    ]
    let rows = [
      {
        TestableTableRow.id: "id-0",
        name: "Product 0",
        stock: 0,
      },
      {
        id: "id-1",
        name: "Product 1",
        stock: 0,
      },
    ]

    let {rerender, unmount} = render(
      <Table
        keyExtractor=TestableTableRow.keyExtractor
        columns
        rows
        selectionEnabled=true
        onSelectChange={onSelectChange->fn}
      />,
    )
    onSelectChange->mockClear

    let table = screen->getByRoleWithOptionsExn(#grid, {name: "table"})

    expect(table)->toBeVisible
    expect(table)->toHaveAttributeValue("aria-label", "table")

    let (thead, tbody) = within(table)->getAllByRoleExn2(#rowgroup)

    let headersRow = within(thead)->getByRoleExn(#row)
    let headers = within(headersRow)->getAllByRoleExn(#columnheader)
    let selectAllCheckbox =
      within(headersRow)->getByRoleWithOptionsExn(#checkbox, {name: "select all"})

    expect(headers)->toHaveLength(3)
    expect(headers->Array.getExn(1))->toHaveTextContent("Name")
    expect(headers->Array.getExn(2))->toHaveTextContent("Stock")

    let (rowA, rowB) = within(tbody)->getAllByRoleExn2(#row)

    expect(rowA)->toHaveAttributeValue("aria-selected", "false")
    expect(rowB)->toHaveAttributeValue("aria-selected", "false")
    expect(onSelectChange)->toHaveBeenCalledTimes(0)

    await userEvent->TestingLibraryEvent.click(selectAllCheckbox)

    expect(rowA)->toHaveAttributeValue("aria-selected", "true")
    expect(rowB)->toHaveAttributeValue("aria-selected", "true")
    expect(onSelectChange)->toHaveBeenCalledTimes(1)
    expect(onSelectChange)->toHaveBeenLastCalledWith1(All)

    await userEvent->TestingLibraryEvent.click(selectAllCheckbox)

    expect(rowA)->toHaveAttributeValue("aria-selected", "false")
    expect(rowB)->toHaveAttributeValue("aria-selected", "false")
    expect(onSelectChange)->toHaveBeenCalledTimes(2)
    expect(onSelectChange)->toHaveBeenLastCalledWith1(Selected([]))

    let rowAselectCheckbox =
      within(rowA)->getByRoleWithOptionsExn(#checkbox, {name: "select Product 0"})

    await userEvent->TestingLibraryEvent.click(rowAselectCheckbox)

    expect(rowA)->toHaveAttributeValue("aria-selected", "true")
    expect(rowB)->toHaveAttributeValue("aria-selected", "false")
    expect(onSelectChange)->toHaveBeenCalledTimes(3)
    expect(onSelectChange)->toHaveBeenLastCalledWith1(Selected(["id-0"]))

    await userEvent->TestingLibraryEvent.click(rowB)

    expect(rowA)->toHaveAttributeValue("aria-selected", "true")
    expect(rowB)->toHaveAttributeValue("aria-selected", "true")
    expect(onSelectChange)->toHaveBeenCalledTimes(4)
    expect(onSelectChange)->toHaveBeenLastCalledWith1(Selected(["id-0", "id-1"]))

    let _ = rerender(
      <Table
        keyExtractor=TestableTableRow.keyExtractor
        columns
        rows
        selectAllEnabled=false
        selectionEnabled=true
        onSelectChange={onSelectChange->fn}
      />,
    )

    let selectAllCheckbox =
      within(headersRow)->queryByRoleWithOptions(#checkbox, {name: "select all"})

    expect(selectAllCheckbox)->Vitest.not->toBeDefined

    await userEvent->TestingLibraryEvent.click(rowA)

    expect(rowA)->toHaveAttributeValue("aria-selected", "false")
    expect(onSelectChange)->toHaveBeenLastCalledWith1(Selected(["id-1"]))

    await userEvent->TestingLibraryEvent.click(rowB)

    expect(rowB)->toHaveAttributeValue("aria-selected", "false")
    expect(onSelectChange)->toHaveBeenLastCalledWith1(Selected([]))

    unmount()

    let {rerender} = render(
      <Table
        keyExtractor=TestableTableRow.keyExtractor
        columns
        rows
        initialAllSelected=true
        selectionEnabled=true
        onSelectChange={onSelectChange->fn}
      />,
    )
    onSelectChange->mockClear

    let table = screen->getByRoleWithOptionsExn(#grid, {name: "table"})
    let (_, tbody) = within(table)->getAllByRoleExn2(#rowgroup)
    let (rowA, rowB) = within(tbody)->getAllByRoleExn2(#row)

    expect(rowA)->toHaveAttributeValue("aria-selected", "true")
    expect(rowB)->toHaveAttributeValue("aria-selected", "true")
    expect(onSelectChange)->toHaveBeenCalledTimes(0)

    let _ = rerender(
      <Table
        keyExtractor=TestableTableRow.keyExtractor
        columns
        rows
        initialAllSelected=true
        selectionEnabled=true
        onSelectChange={onSelectChange->fn}
      />,
    )

    expect(onSelectChange)->toHaveBeenCalledTimes(0)

    let _ = rerender(
      <Table
        keyExtractor=TestableTableRow.keyExtractor
        columns
        rows=[]
        initialAllSelected=true
        selectionEnabled=true
        onSelectChange={onSelectChange->fn}
      />,
    )

    expect(onSelectChange)->toHaveBeenCalledTimes(1)

    let _ = rerender(
      <Table
        keyExtractor=TestableTableRow.keyExtractor
        columns
        rows
        initialAllSelected=true
        selectionEnabled=true
        onSelectChange={onSelectChange->fn}
      />,
    )

    expect(onSelectChange)->toHaveBeenCalledTimes(2)
    expect(onSelectChange)->toHaveBeenLastCalledWith1(All)
  })

  itPromise("should handle extended selection", async () => {
    let onSelectChange = fn1(ignore)

    let _ = render(
      <Table
        keyExtractor=TestableTableRow.keyExtractor
        columns=[
          {
            Table.key: "id",
            name: "ID",
            render: _ => React.null,
          },
        ]
        rows={[
          {
            TestableTableRow.id: "id-0",
            name: "",
            stock: 0,
          },
          {
            id: "id-1",
            name: "",
            stock: 0,
          },
          {
            id: "id-2",
            name: "",
            stock: 0,
          },
        ]}
        selectionEnabled=true
        onSelectChange={onSelectChange->fn}
      />,
    )
    onSelectChange->mockClear

    let table = screen->getByRoleWithOptionsExn(#grid, {name: "table"})

    expect(table)->toBeVisible
    expect(table)->toHaveAttributeValue("aria-label", "table")

    let (_, tbody) = within(table)->getAllByRoleExn2(#rowgroup)
    let (rowA, rowB, rowC) = within(tbody)->getAllByRoleExn3(#row)

    expect(rowA)->toHaveAttributeValue("aria-selected", "false")
    expect(rowB)->toHaveAttributeValue("aria-selected", "false")
    expect(rowC)->toHaveAttributeValue("aria-selected", "false")
    expect(onSelectChange)->toHaveBeenCalledTimes(0)

    await userEvent->TestingLibraryEvent.click(rowA)

    expect(onSelectChange)->toHaveBeenLastCalledWith1(Selected(["id-0"]))

    await userEvent->TestingLibraryEvent.keyboard("{shift>}")
    await userEvent->TestingLibraryEvent.click(rowC)
    await userEvent->TestingLibraryEvent.keyboard("{/shift>}")

    expect(onSelectChange)->toHaveBeenLastCalledWith1(Selected(["id-0", "id-1", "id-2"]))

    await userEvent->TestingLibraryEvent.keyboard("{shift>}")
    await userEvent->TestingLibraryEvent.click(rowA)
    await userEvent->TestingLibraryEvent.keyboard("{/shift>}")

    expect(onSelectChange)->toHaveBeenLastCalledWith1(Selected(["id-0"]))
  })

  itPromise("should render a table with a specified key for each row", async () => {
    let onSelectChange = fn1(ignore)

    let _ = render(
      <Table
        columns=[
          {
            Table.key: "id",
            name: "ID",
            render: _ => React.null,
          },
        ]
        rows=[
          {
            TestableTableRow.id: "",
            name: "Product 0",
            stock: 0,
          },
        ]
        keyExtractor={row => row.TestableTableRow.name}
        selectionEnabled=true
        onSelectChange={onSelectChange->fn}
      />,
    )
    onSelectChange->mockClear

    let table = screen->getByRoleWithOptionsExn(#grid, {name: "table"})

    expect(table)->toBeVisible
    expect(table)->toHaveAttributeValue("aria-label", "table")

    let (_, tbody) = within(table)->getAllByRoleExn2(#rowgroup)
    let row = within(tbody)->getByRoleExn(#row)

    expect(onSelectChange)->toHaveBeenCalledTimes(0)

    await userEvent->TestingLibraryEvent.click(row)

    expect(onSelectChange)->toHaveBeenLastCalledWith1(Selected(["Product 0"]))
  })

  it("should render a table with the index rendered in first cell of each row", () => {
    let _ = render(
      <Table
        keyExtractor=TestableTableRow.keyExtractor
        columns=[
          {
            Table.key: "index",
            name: "Index",
            render: ({index}) => index->Int.toString->React.string,
          },
        ]
        rows=[
          {
            TestableTableRow.id: "id-0",
            name: "",
            stock: 0,
          },
          {
            id: "id-1",
            name: "",
            stock: 0,
          },
        ]
      />,
    )

    let table = screen->getByRoleWithOptionsExn(#grid, {name: "table"})

    expect(table)->toBeVisible
    expect(table)->toHaveAttributeValue("aria-label", "table")

    let (_, tbody) = within(table)->getAllByRoleExn2(#rowgroup)
    let (rowA, rowB) = within(tbody)->getAllByRoleExn2(#row)

    let rowAcellA = within(rowA)->getByRoleExn(#rowheader)
    let rowBcellA = within(rowB)->getByRoleExn(#rowheader)

    expect(rowAcellA)->toHaveTextContent("0")
    expect(rowBcellA)->toHaveTextContent("1")
  })

  itPromise("should have disabled rows with it indicated in first cell of each row", async () => {
    let onSelectChange = fn1(ignore)

    let columns = [
      {
        Table.key: "name",
        name: "Name",
        render: ({data, disabled}) =>
          (data.TestableTableRow.name ++ (disabled ? " (disabled)" : ""))->React.string,
      },
    ]
    let rows = [
      {
        TestableTableRow.id: "id-0",
        name: "Product 0",
        stock: 0,
      },
      {
        id: "id-1",
        name: "Product 1",
        stock: 0,
      },
    ]

    let {rerender} = render(
      <Table
        keyExtractor=TestableTableRow.keyExtractor
        columns
        rows
        disabledRowsKeys=["id-0"]
        selectionEnabled=true
        onSelectChange={onSelectChange->fn}
      />,
    )
    onSelectChange->mockClear

    let table = screen->getByRoleWithOptionsExn(#grid, {name: "table"})

    expect(table)->toBeVisible
    expect(table)->toHaveAttributeValue("aria-label", "table")

    let (thead, tbody) = within(table)->getAllByRoleExn2(#rowgroup)

    let headersRow = within(thead)->getByRoleExn(#row)
    let selectAllCheckbox =
      within(headersRow)->getByRoleWithOptionsExn(#checkbox, {name: "select all"})

    let (rowA, rowB) = within(tbody)->getAllByRoleExn2(#row)

    let rowAcellA = within(rowA)->getByRoleExn(#rowheader)
    let rowBcellA = within(rowB)->getByRoleExn(#rowheader)

    expect(rowAcellA)->toHaveTextContent("Product 0 (disabled)")
    expect(rowBcellA)->toHaveTextContent("Product 1")

    expect(rowA)->toHaveAttributeValue("aria-selected", "false")
    expect(rowB)->toHaveAttributeValue("aria-selected", "false")
    expect(rowA)->toHaveAttributeValue("aria-disabled", "true")
    expect(rowB)->Vitest.not->toHaveAttribute("aria-disabled")
    expect(onSelectChange)->toHaveBeenCalledTimes(0)

    await userEvent->TestingLibraryEvent.click(selectAllCheckbox)

    expect(rowA)->toHaveAttributeValue("aria-selected", "false")
    expect(rowB)->toHaveAttributeValue("aria-selected", "true")
    expect(onSelectChange)->toHaveBeenLastCalledWith1(All)
    expect(onSelectChange)->toHaveBeenCalledTimes(1)

    await userEvent->TestingLibraryEvent.click(rowA)

    expect(onSelectChange)->toHaveBeenCalledTimes(1)
    expect(rowA)->toHaveAttributeValue("aria-selected", "false")

    let _ = rerender(
      <Table
        keyExtractor=TestableTableRow.keyExtractor
        columns
        rows
        disabledRowsKeys=["id-0", "id-1"]
        selectionEnabled=true
        onSelectChange={onSelectChange->fn}
      />,
    )

    expect(rowA)->toHaveAttributeValue("aria-selected", "false")
    expect(rowB)->toHaveAttributeValue("aria-selected", "false")
    expect(rowA)->toHaveAttributeValue("aria-disabled", "true")
    expect(rowB)->toHaveAttributeValue("aria-disabled", "true")

    await userEvent->TestingLibraryEvent.click(selectAllCheckbox)

    expect(rowA)->toHaveAttributeValue("aria-selected", "false")
    expect(rowB)->toHaveAttributeValue("aria-selected", "false")
    expect(onSelectChange)->toHaveBeenLastCalledWith1(Selected([]))

    await userEvent->TestingLibraryEvent.click(selectAllCheckbox)

    expect(rowA)->toHaveAttributeValue("aria-selected", "false")
    expect(rowB)->toHaveAttributeValue("aria-selected", "false")
    expect(onSelectChange)->toHaveBeenLastCalledWith1(All)
  })

  it("should render a table with error message rendered in first cell of each row", () => {
    let _ = render(
      <Table
        keyExtractor=TestableTableRow.keyExtractor
        columns=[
          {
            Table.key: "name",
            name: "Name",
            render: ({data, errorMessage}) =>
              errorMessage->Option.getWithDefault(data.TestableTableRow.name)->React.string,
          },
        ]
        rows=[
          {
            TestableTableRow.id: "id-0",
            name: "Product 0",
            stock: 0,
          },
          {
            id: "id-1",
            name: "Product 1",
            stock: 0,
          },
        ]
        erroredRowsMap=[{Table.key: "id-0", message: "Product 0 errored"}]
      />,
    )

    let table = screen->getByRoleWithOptionsExn(#grid, {name: "table"})

    expect(table)->toBeVisible
    expect(table)->toHaveAttributeValue("aria-label", "table")

    let (_, tbody) = within(table)->getAllByRoleExn2(#rowgroup)
    let (rowA, rowB) = within(tbody)->getAllByRoleExn2(#row)

    let rowAcellA = within(rowA)->getByRoleExn(#rowheader)
    let rowBcellA = within(rowB)->getByRoleExn(#rowheader)

    expect(rowAcellA)->toHaveTextContent("Product 0 errored")
    expect(rowBcellA)->toHaveTextContent("Product 1")
  })

  itPromise("should handle keyboard navigation and selection", async () => {
    let onSelectChange = fn1(ignore)

    let _ = render(
      <Table
        keyExtractor=TestableTableRow.keyExtractor
        columns=[
          {
            Table.key: "id",
            name: "",
            render: _ => React.null,
          },
        ]
        rows=[
          {
            TestableTableRow.id: "id-0",
            name: "",
            stock: 0,
          },
          {
            id: "id-1",
            name: "",
            stock: 0,
          },
          {
            id: "id-2",
            name: "",
            stock: 0,
          },
        ]
        selectionEnabled=true
        onSelectChange={onSelectChange->fn}
      />,
    )
    onSelectChange->mockClear

    let table = screen->getByRoleWithOptionsExn(#grid, {name: "table"})

    expect(table)->toBeVisible
    expect(table)->toHaveAttributeValue("aria-label", "table")

    let (_, tbody) = within(table)->getAllByRoleExn2(#rowgroup)
    let (rowA, rowB, rowC) = within(tbody)->getAllByRoleExn3(#row)

    expect(rowA)->toHaveAttributeValue("aria-selected", "false")
    expect(rowB)->toHaveAttributeValue("aria-selected", "false")
    expect(rowC)->toHaveAttributeValue("aria-selected", "false")
    expect(onSelectChange)->toHaveBeenCalledTimes(0)

    await userEvent->TestingLibraryEvent.keyboard("{tab}{enter}")

    expect(onSelectChange)->toHaveBeenLastCalledWith1(Selected(["id-0"]))

    await userEvent->TestingLibraryEvent.keyboard("{arrowdown}{arrowdown}")

    await userEvent->TestingLibraryEvent.keyboard("{shift>}{enter}{/shift>}")

    expect(onSelectChange)->toHaveBeenLastCalledWith1(Selected(["id-0", "id-1", "id-2"]))

    await userEvent->TestingLibraryEvent.keyboard("{arrowup}")

    await userEvent->TestingLibraryEvent.keyboard(" ")

    expect(onSelectChange)->toHaveBeenLastCalledWith1(Selected(["id-0", "id-2"]))

    await userEvent->TestingLibraryEvent.keyboard("{escape}")

    expect(onSelectChange)->toHaveBeenLastCalledWith1(Selected([]))

    await userEvent->TestingLibraryEvent.keyboard("{Control>}a{/Control}")

    expect(onSelectChange)->toHaveBeenLastCalledWith1(All)
  })

  itPromise("should fire sorting action when clicking on sortable headers", async () => {
    let onSortChange = fn1(ignore)

    let _ = render(
      <Table
        keyExtractor=TestableTableRow.keyExtractor
        columns=[
          {
            Table.key: "id",
            name: "Id",
            allowsSorting: false,
            render: _ => React.null,
          },
          {
            Table.key: "name",
            name: "Name",
            allowsSorting: true,
            render: _ => React.null,
          },
          {
            Table.key: "stock",
            name: "Stock",
            allowsSorting: true,
            render: _ => React.null,
          },
        ]
        rows=[]
        sortDescriptor={column: "name", direction: #ascending}
        onSortChange={onSortChange->fn}
      />,
    )
    onSortChange->mockClear

    let table = screen->getByRoleWithOptionsExn(#grid, {name: "table"})

    expect(table)->toBeVisible
    expect(table)->toHaveAttributeValue("aria-label", "table")

    let (thead, _tbody) = within(table)->getAllByRoleExn2(#rowgroup)

    let rowHeaders = within(thead)->getByRoleExn(#row)
    let (idHeader, nameHeader, stockHeader) = within(rowHeaders)->getAllByRoleExn3(#columnheader)

    expect(idHeader)->toHaveTextContent("Id")
    expect(idHeader)->Vitest.not->toHaveAttribute("aria-sort")

    expect(nameHeader)->toHaveTextContent("Name")
    expect(nameHeader)->toHaveAttributeValue("aria-sort", "ascending")

    expect(stockHeader)->toHaveTextContent("Stock")
    expect(stockHeader)->toHaveAttributeValue("aria-sort", "none")

    expect(onSortChange)->toHaveBeenCalledTimes(0)

    await userEvent->TestingLibraryEvent.click(idHeader)

    expect(onSortChange)->toHaveBeenCalledTimes(0)

    await userEvent->TestingLibraryEvent.click(nameHeader)

    expect(onSortChange)->toHaveBeenCalledTimes(1)
    expect(onSortChange)->toHaveBeenCalledWith1({column: "name", direction: #descending})

    await userEvent->TestingLibraryEvent.click(stockHeader)

    expect(onSortChange)->toHaveBeenCalledTimes(2)
    expect(onSortChange)->toHaveBeenCalledWith1({column: "stock", direction: #ascending})
  })

  todo("test that onLoadMore is triggered everytime table is vertically scrolled under 100px left")
  todo("test that a column is hidden when its breakpoint matched")
})
