open Vitest
open TestingLibraryReact

let userEvent = TestingLibraryEvent.setup()

module TestableToggleButtonGroup = {
  @react.component
  let make = (~selectionMode, ~items, ~defaultValue, ~onChange) => {
    let (value, setValue) = React.useState(() => defaultValue)

    <ToggleButtonGroup
      items
      value
      selectionMode
      onChange={value => {
        setValue(_ => value)
        onChange(value)
      }}
    />
  }
}

itPromise(
  "should render without any default value and be possible to interact with in `single` selection mode",
  async () => {
    let onChange = fn1(ignore)
    let items = [
      {
        ToggleButtonGroup.value: "cat",
        label: "Cat",
        tone: #success,
      },
      {
        value: "dog",
        label: "Dog",
        tone: #success,
      },
      {
        value: "dragon",
        label: "Dragon",
        tone: #danger,
      },
    ]
    render(
      <TestableToggleButtonGroup
        selectionMode=#single defaultValue=[] items onChange={onChange->fn}
      />,
    )->ignore

    let radioGroup = screen->getByRoleExn(#radiogroup)
    let radiosLength = screen->getAllByRoleExn(#radio)->Array.length
    let (radio1, radio2, radio3) = screen->getAllByRoleExn3(#radio)

    expect(radiosLength)->toBe(3)
    expect(radioGroup)->toBeVisible
    expect(radio1)->toBeVisible
    expect(radio2)->toBeVisible
    expect(radio3)->toBeVisible

    expect(radio1)->toHaveTextContent("Cat")
    expect(radio2)->toHaveTextContent("Dog")
    expect(radio3)->toHaveTextContent("Dragon")

    expect(radio1)->toHaveAttributeValue("aria-checked", "false")
    expect(radio2)->toHaveAttributeValue("aria-checked", "false")
    expect(radio3)->toHaveAttributeValue("aria-checked", "false")

    expect(onChange)->toHaveBeenCalledTimes(0)

    await userEvent->TestingLibraryEvent.click(radio3)

    expect(radio1)->toHaveAttributeValue("aria-checked", "false")
    expect(radio2)->toHaveAttributeValue("aria-checked", "false")
    expect(radio3)->toHaveAttributeValue("aria-checked", "true")

    expect(onChange)->toHaveBeenCalledTimes(1)
    expect(onChange)->toHaveBeenCalledWith1(["dragon"])

    await userEvent->TestingLibraryEvent.click(radio2)

    expect(radio1)->toHaveAttributeValue("aria-checked", "false")
    expect(radio2)->toHaveAttributeValue("aria-checked", "true")
    expect(radio3)->toHaveAttributeValue("aria-checked", "false")

    expect(onChange)->toHaveBeenCalledTimes(2)
    expect(onChange)->toHaveBeenCalledWith1(["dog"])

    await userEvent->TestingLibraryEvent.click(radio2)

    expect(radio1)->toHaveAttributeValue("aria-checked", "false")
    expect(radio2)->toHaveAttributeValue("aria-checked", "true")
    expect(radio3)->toHaveAttributeValue("aria-checked", "false")

    expect(onChange)->toHaveBeenCalledTimes(3)
    expect(onChange)->toHaveBeenCalledWith1(["dog"])
  },
)

itPromise(
  "should render with default values and be possible to interact with in `multiple` selection mode",
  async () => {
    let onChange = fn1(ignore)
    let items = [
      {
        ToggleButtonGroup.value: "cat",
        label: "Cat",
        tone: #success,
      },
      {
        value: "dog",
        label: "Dog",
        tone: #success,
      },
      {
        value: "dragon",
        label: "Dragon",
        tone: #danger,
      },
    ]
    render(
      <TestableToggleButtonGroup
        selectionMode=#multiple defaultValue=["dragon", "dog"] items onChange={onChange->fn}
      />,
    )->ignore

    let toolbar = screen->getByRoleExn(#toolbar)
    let toolbarButtonsLength = screen->getAllByRoleExn(#button)->Array.length
    let (button1, button2, button3) = screen->getAllByRoleExn3(#button)

    expect(toolbarButtonsLength)->toBe(3)
    expect(toolbar)->toBeVisible
    expect(button1)->toBeVisible
    expect(button2)->toBeVisible
    expect(button3)->toBeVisible

    expect(button1)->toHaveTextContent("Cat")
    expect(button2)->toHaveTextContent("Dog")
    expect(button3)->toHaveTextContent("Dragon")

    expect(button1)->toHaveAttributeValue("aria-pressed", "false")
    expect(button2)->toHaveAttributeValue("aria-pressed", "true")
    expect(button3)->toHaveAttributeValue("aria-pressed", "true")

    expect(onChange)->toHaveBeenCalledTimes(0)

    await userEvent->TestingLibraryEvent.click(button1)

    expect(button1)->toHaveAttributeValue("aria-pressed", "true")
    expect(button2)->toHaveAttributeValue("aria-pressed", "true")
    expect(button3)->toHaveAttributeValue("aria-pressed", "true")

    expect(onChange)->toHaveBeenCalledTimes(1)
    expect(onChange)->toHaveBeenCalledWith1(["dragon", "dog", "cat"])

    await userEvent->TestingLibraryEvent.click(button2)
    await userEvent->TestingLibraryEvent.click(button3)

    expect(button1)->toHaveAttributeValue("aria-pressed", "true")
    expect(button2)->toHaveAttributeValue("aria-pressed", "false")
    expect(button3)->toHaveAttributeValue("aria-pressed", "false")

    expect(onChange)->toHaveBeenCalledTimes(3)
    expect(onChange)->toHaveBeenCalledWith1(["cat"])

    await userEvent->TestingLibraryEvent.click(button1)

    expect(button1)->toHaveAttributeValue("aria-pressed", "true")
    expect(button2)->toHaveAttributeValue("aria-pressed", "false")
    expect(button3)->toHaveAttributeValue("aria-pressed", "false")

    expect(onChange)->toHaveBeenCalledTimes(4)
    expect(onChange)->toHaveBeenCalledWith1(["cat"])
  },
)
