open Vitest
open TestingLibraryReact

module TestableRoundButton = {
  @react.component
  let make = (~disabled=?, ~onPress=fn1(ignore)->fn) =>
    <RoundButton icon=#search ?disabled onPress />
}

itPromise("should trigger onPress when the button is not disabled", async () => {
  let userEvent = TestingLibraryEvent.setup()
  let onPress = fn1(ignore)
  let {rerender} = <TestableRoundButton onPress={onPress->fn} />->render
  let button = screen->getByRoleExn(#button)

  expect(button)->toBeVisible
  expect(onPress)->toHaveBeenCalledTimes(0)

  await userEvent->TestingLibraryEvent.click(button)

  expect(button)->toBeVisible
  expect(onPress)->toHaveBeenCalledTimes(1)

  <TestableRoundButton disabled=true onPress={onPress->fn} />->rerender->ignore
  let button = screen->getByRoleExn(#button)

  expect(button)->toBeVisible
  expect(onPress)->toHaveBeenCalledTimes(1)

  await userEvent->TestingLibraryEvent.click(button)

  expect(button)->toBeVisible
  expect(onPress)->toHaveBeenCalledTimes(1)
})
