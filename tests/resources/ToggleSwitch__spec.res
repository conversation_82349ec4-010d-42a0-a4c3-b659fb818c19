open Vitest
open TestingLibraryReact

module TestableToggleSwitch = {
  @react.component
  let make = (~onChange, ~disabled=?) => {
    let (value, setValue) = React.useState(() => false)

    <ToggleSwitch
      ?disabled
      value={value}
      onChange={newValue => {
        onChange(newValue)
        setValue(_ => newValue)
      }}
    />
  }
}

itPromise("should trigger onChange prop on checkbox element click", async () => {
  let userEvent = TestingLibraryEvent.setup()
  let onChange = fn1(ignore)

  render(<TestableToggleSwitch onChange={onChange->fn} />)->ignore

  let button = screen->getByRoleExn(#button)

  expect(button)->toBeVisible
  expect(onChange)->toHaveBeenCalledTimes(0)

  await userEvent->TestingLibraryEvent.click(button)

  expect(button)->toBeVisible
  expect(onChange)->toHaveBeenCalledTimes(1)
  expect(onChange)->toHaveBeenCalledWith1(true)

  await userEvent->TestingLibraryEvent.click(button)

  expect(button)->toBeVisible
  expect(onChange)->toHaveBeenCalledTimes(2)
  expect(onChange)->toHaveBeenCalledWith1(false)
})

itPromise("should not be possible to interact with", async () => {
  let userEvent = TestingLibraryEvent.setup()
  let onChange = fn1(ignore)

  render(<TestableToggleSwitch disabled=true onChange={onChange->fn} />)->ignore

  let button = screen->getByRoleExn(#button)

  expect(button)->toBeVisible
  expect(onChange)->toHaveBeenCalledTimes(0)

  await userEvent->TestingLibraryEvent.click(button)

  expect(button)->toBeVisible
  expect(onChange)->toHaveBeenCalledTimes(0)
})
