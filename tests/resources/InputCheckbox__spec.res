open Vitest
open TestingLibraryReact

module TestableInputCheckboxField = {
  @react.component
  let make = (~onChange, ~disabled=?) => {
    let (value, setValue) = React.useState(() => false)

    <InputCheckboxField
      label="My label"
      text="Text"
      value
      required=false
      onChange={newValue => {
        onChange(newValue)
        setValue(_ => newValue)
      }}
      ?disabled
    />
  }
}

itPromise("should trigger onChange callback on checkbox element click", async () => {
  let userEvent = TestingLibraryEvent.setup()
  let onChange = fn1(ignore)

  render(<TestableInputCheckboxField onChange={onChange->fn} />)->ignore
  let element = screen->getAllByRoleExn(#button)->Array.getExn(0)

  expect(element)->toBeVisible
  expect(onChange)->toHaveBeenCalledTimes(0)

  await userEvent->TestingLibraryEvent.click(element)

  expect(onChange)->toHaveBeenCalledTimes(1)
  expect(onChange)->toHaveBeenCalledWith1(true)

  await userEvent->TestingLibraryEvent.click(element)

  expect(onChange)->toHaveBeenCalledTimes(2)
  expect(onChange)->toHaveBeenCalledWith1(false)
})

itPromise("should trigger onChange callback on label element click", async () => {
  let userEvent = TestingLibraryEvent.setup()
  let onChange = fn1(ignore)

  render(<TestableInputCheckboxField onChange={onChange->fn} />)->ignore
  let element = screen->getAllByRoleExn(#button)->Array.getExn(0)

  expect(element)->toBeVisible
  expect(onChange)->toHaveBeenCalledTimes(0)

  await userEvent->TestingLibraryEvent.click(element)

  expect(onChange)->toHaveBeenCalledTimes(1)
  expect(onChange)->toHaveBeenCalledWith1(true)

  await userEvent->TestingLibraryEvent.click(element)

  expect(onChange)->toHaveBeenCalledTimes(2)
  expect(onChange)->toHaveBeenCalledWith1(false)
})

itPromise("should not be possible to interact with", async () => {
  let userEvent = TestingLibraryEvent.setup()
  let onChange = fn1(ignore)
  let disabled = true

  render(<TestableInputCheckboxField onChange={onChange->fn} disabled />)->ignore
  let element = screen->getAllByRoleExn(#button)->Array.getExn(0)

  expect(element)->toBeVisible
  expect(onChange)->toHaveBeenCalledTimes(0)

  await userEvent->TestingLibraryEvent.click(element)

  expect(element)->toBeVisible
  expect(onChange)->toHaveBeenCalledTimes(0)

  await userEvent->TestingLibraryEvent.click(element)

  expect(element)->toBeVisible
  expect(onChange)->toHaveBeenCalledTimes(0)
})
