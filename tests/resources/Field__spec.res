open Vitest
open TestingLibraryReact

describe("makeLabel", () => {
  it("should return label with an asterique when required is true", () => {
    expect("My label"->Field.makeLabel(~required=false))->toStrictEqual("My label")
  })

  it("should only return label when required is false", () => {
    expect("My label"->Field.makeLabel(~required=true))->toStrictEqual("My label *")
  })

  it("should return empty string when required is false and there is empty label", () => {
    expect(""->Field.makeLabel(~required=false))->toStrictEqual("")
  })

  it("should return empty string when required is true but there is empty label", () => {
    expect(""->Field.makeLabel(~required=true))->toStrictEqual("Required field *")
  })
})

describe("component", () => {
  it("should render", () => {
    <Field>
      <InputTextField label="my children label" value="" />
    </Field>
    ->render
    ->ignore

    expect(screen->getByLabelTextExn("my children label"))->toBeVisible
  })

  it("should render a field with a label", () => {
    <Field label="my label"> React.null </Field>->render->ignore

    expect(screen->getByTextExn("my label"))->toBeVisible
  })

  it("should render a required field", () => {
    let {rerender} = <Field label="my label" required=true> React.null </Field>->render

    expect(screen->getByTextExn("my label *"))->toBeVisible

    <Field label="" required=true> React.null </Field>->rerender->ignore

    expect(screen->getByTextExn("Required field *"))->toBeVisible

    <Field required=true> React.null </Field>->rerender->ignore

    expect(screen->queryByText("Required field *"))->toBeNone
  })

  itPromise("should render and trigger an action callback on click", async () => {
    let userEvent = TestingLibraryEvent.setup()
    let callback = fn0()
    let action = {
      Field.Action.text: "my action",
      handler: Callback(_ => callback->fn()),
    }
    let {rerender} = <Field label="my label" action> React.null </Field>->render
    let button = screen->getByTextExn("my action")

    expect(button)->toBeVisible
    expect(callback)->toHaveBeenCalledTimes(0)

    await userEvent->TestingLibraryEvent.click(button)

    expect(button)->toBeVisible
    expect(callback)->toHaveBeenCalledTimes(1)

    <Field action> React.null </Field>->rerender->ignore

    expect(button)->Vitest.not->toBeVisible
    expect(callback)->toHaveBeenCalledTimes(1)
  })

  it("should render an appended link", () => {
    let href = "https://www.wino.fr"->Url.make
    let action = {
      Field.Action.text: "my link",
      handler: OpenLink(Url(href)),
    }

    let {rerender} = <Field label="my label" action> React.null </Field>->render

    let link = screen->getByRoleExn(#link)

    expect(link)->toBeVisible
    expect(link)->toHaveTextContent("my link")
    expect(link)->toHaveAttributeValue("href", href->Url.href)

    <Field action> React.null </Field>->rerender->ignore

    expect(link)->Vitest.not->toBeVisible
  })
})
