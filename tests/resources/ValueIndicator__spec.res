open Vitest
open TestingLibraryReact

testEach2([
  (0., "€0.00"),
  (0.01, "€0.01"),
  (0.001, "€0.00"),
  (0.005, "€0.01"),
  (1., "€1.00"),
  (1.01, "€1.01"),
  (1.1, "€1.10"),
  (1.001, "€1.00"),
  (12.001, "€12.00"),
  (12.01, "€12.01"),
  (12.1, "€12.10"),
  (1200000.1, "€1,200,000.10"),
  (1200000.01, "€1,200,000.01"),
  (1200000.008, "€1,200,000.01"),
])(."should render euro value '%s'", (input, output) => {
  render(<ValueIndicator value=#currency(input, Intl.currencyEur) />)->ignore
  let element = screen->getByTextExn(output)
  expect(element)->toBeVisible
})

testEach2([
  (0., "$0.00"),
  (0.01, "$0.01"),
  (0.001, "$0.00"),
  (0.005, "$0.01"),
  (1., "$1.00"),
  (1.01, "$1.01"),
  (1.1, "$1.10"),
  (1.001, "$1.00"),
  (12.001, "$12.00"),
  (12.01, "$12.01"),
  (12.1, "$12.10"),
  (1200000.1, "$1,200,000.10"),
  (1200000.01, "$1,200,000.01"),
  (1200000.008, "$1,200,000.01"),
])(."should render usd value '%s'", (input, output) => {
  render(<ValueIndicator value=#currency(input, Intl.currencyUsd) />)->ignore
  let element = screen->getByTextExn(output)
  expect(element)->toBeVisible
})

testEach2([
  (0., "0.00"),
  (0.01, "0.01"),
  (0.001, "0.00"),
  (0.005, "0.01"),
  (1., "1.00"),
  (1.01, "1.01"),
  (1.1, "1.10"),
  (1.001, "1.00"),
  (12.001, "12.00"),
  (12.01, "12.01"),
  (12.1, "12.10"),
  (1200000.1, "1,200,000.10"),
  (1200000.01, "1,200,000.01"),
  (1200000.008, "1,200,000.01"),
])(."should render decimal value '%s'", (input, output) => {
  render(<ValueIndicator value=#decimal(input) />)->ignore
  let element = screen->getByTextExn(output)
  expect(element)->toBeVisible
})

testEach2([
  (0., "0.00%"),
  (0.01, "1.00%"),
  (0.001, "0.10%"),
  (0.005, "0.50%"),
  (0.0001, "0.01%"),
  (0.0005, "0.05%"),
  (0.00001, "0.00%"),
  (0.00005, "0.01%"),
  (0.000005, "0.00%"),
  (0.501, "50.10%"),
  (0.505, "50.50%"),
  (0.5001, "50.01%"),
  (0.5005, "50.05%"),
  (0.50001, "50.00%"),
  (0.50005, "50.01%"),
  (0.500005, "50.00%"),
  (1., "100.00%"),
  (1.01, "101.00%"),
  (1.001, "100.10%"),
  (1.005, "100.50%"),
  (1.0001, "100.01%"),
  (1.0005, "100.05%"),
  (1.00001, "100.00%"),
  (1.00005, "100.01%"),
  (1.000005, "100.00%"),
  (1.500005, "150.00%"),
  (18., "1,800.00%"),
  (18.01, "1,801.00%"),
  (18.001, "1,800.10%"),
  (18.005, "1,800.50%"),
  (18.0001, "1,800.01%"),
  (18.0005, "1,800.05%"),
  (18.5005, "1,850.05%"),
  (18.00001, "1,800.00%"),
  (18.00005, "1,800.01%"),
  (18.000005, "1,800.00%"),
  (1200000.1, "120,000,010.00%"),
  (1200000.01, "120,000,001.00%"),
  (1200000.008, "120,000,000.80%"),
])(."should render percent value '%s'", (input, output) => {
  render(<ValueIndicator value=#percent(input) />)->ignore
  let element = screen->getByTextExn(output)
  expect(element)->toBeVisible
})

testEach2([
  (0., "0"),
  (0.1, "0"),
  (0.5, "1"),
  (0.01, "0"),
  (0.05, "0"),
  (1., "1"),
  (2., "2"),
  (10., "10"),
  (12., "12"),
  (100., "100"),
  (120., "120"),
  (1050., "1,050"),
  (1550., "1,550"),
  (1550.1, "1,550"),
  (1550.5, "1,551"),
  (1550.01, "1,550"),
  (1550.05, "1,550"),
])(."should render integer value '%s'", (input, output) => {
  // We voluntarily test here float values to be sure of
  // the correct working in a use of the component outside ReScript
  render(<ValueIndicator value=#integer(Obj.magic(input)) />)->ignore
  let element = screen->getByTextExn(output)
  expect(element)->toBeVisible
})
