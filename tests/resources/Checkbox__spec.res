open Vitest
open TestingLibraryReact

module TestableCheckbox = {
  @react.component
  let make = (~onChange, ~disabled=?) => {
    let (value, setValue) = React.useState(() => false)

    <Checkbox
      value={value}
      onChange={newValue => {
        onChange(newValue)
        setValue(_ => newValue)
      }}
      ?disabled
    />
  }
}

itPromise("should trigger onChange callback on click", async () => {
  let userEvent = TestingLibraryEvent.setup()
  let onChange = fn1(ignore)

  render(<TestableCheckbox onChange={onChange->fn} />)->ignore

  let checkbox = screen->getByRoleExn(#checkbox)
  let label = checkbox->WebAPI.DomElement.parentElement->Option.getExn

  expect(checkbox)->Vitest.not->toBeVisible
  expect(label)->toBeVisible
  expect(onChange)->toHaveBeenCalledTimes(0)

  await userEvent->TestingLibraryEvent.click(checkbox)

  expect(onChange)->toHaveBeenCalledTimes(1)
  expect(onChange)->toHaveBeenCalledWith1(true)

  await userEvent->TestingLibraryEvent.click(label)

  expect(onChange)->toHaveBeenCalledTimes(2)
  expect(onChange)->toHaveBeenCalledWith1(false)
})

itPromise("should not be possible to interact with", async () => {
  let userEvent = TestingLibraryEvent.setup()
  let onChange = fn1(ignore)
  let disabled = true

  render(<TestableCheckbox onChange={onChange->fn} disabled />)->ignore

  let checkbox = screen->getByRoleExn(#checkbox)
  let label = checkbox->WebAPI.DomElement.parentElement->Option.getExn

  expect(checkbox)->Vitest.not->toBeVisible
  expect(label)->toBeVisible
  expect(onChange)->toHaveBeenCalledTimes(0)

  await userEvent->TestingLibraryEvent.click(checkbox)

  expect(onChange)->toHaveBeenCalledTimes(0)
})
