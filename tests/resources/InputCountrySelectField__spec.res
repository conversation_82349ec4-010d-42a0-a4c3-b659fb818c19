open Vitest

describe("fr-FR locale", () => {
  let {countries, subdivisions, countriesAndSubdivisions} = module(InputCountrySelectField)
  let locale = #"fr-FR"

  test("countries", () => {
    expect(countries(~locale))->toContain("Royaume-Uni")
    expect(countries(~locale))->toContain("France")
    expect(countries(~locale))->Vitest.not->toContain("Écosse")
  })

  test("subdivisions", () => {
    expect(subdivisions(~locale))->toContain("Écosse")
    expect(subdivisions(~locale))->Vitest.not->toContain("Royaume-Uni")
    expect(subdivisions(~locale))->Vitest.not->toContain("France")
  })

  test("countriesAndSubdivisions", () => {
    expect(countriesAndSubdivisions(~locale))->toContain("Royaume-Uni")
    expect(countriesAndSubdivisions(~locale))->Vitest.not->toContain("United Kingdom")

    expect(countriesAndSubdivisions(~locale))->toContain("Angleterre")
    expect(countriesAndSubdivisions(~locale))->Vitest.not->toContain("England")
  })
})

describe("en-EN locale", () => {
  let {countries, subdivisions, countriesAndSubdivisions} = module(InputCountrySelectField)
  let locale = #"en-EN"

  test("countries", () => {
    expect(countries(~locale))->toStrictEqual([])
  })

  test("subdivisions", () => {
    expect(subdivisions(~locale))->toStrictEqual([])
  })

  test("countriesAndSubdivisions", () => {
    expect(countriesAndSubdivisions(~locale))->toStrictEqual([])
  })
})
