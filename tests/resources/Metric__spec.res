open Vitest
open TestingLibraryReact

let userEvent = TestingLibraryEvent.setup()
let children = <span> {"Hello"->React.string} </span>

test("getVariation", () => {
  expect(Metric.getVariation(~value=0.))->toBe(#neutral)
  expect(Metric.getVariation(~value=90.))->toBe(#success)
  expect(Metric.getVariation(~value=1.))->toBe(#success)
  expect(Metric.getVariation(~value=-1.))->toBe(#danger)
})

describe("component", () => {
  it("should display progression increase if any ", () => {
    render(<Metric title={"Title"} progression=0.22> {children} </Metric>)->ignore

    expect(screen->getByTextExn("+ 22%"))->toBeVisible
  })

  it("should display progression decrease if any ", () => {
    render(<Metric title={"Title"} progression={-0.22}> {children} </Metric>)->ignore

    expect(screen->getByTextExn("- 22%"))->toBeVisible
  })

  it("should display 0% if progression constant ", () => {
    render(<Metric title={"Title"} progression={0.}> {children} </Metric>)->ignore

    expect(screen->getByTextExn("0%"))->toBeVisible
  })

  it("should display ∞ if progression infinite ", () => {
    render(<Metric title={"Title"} progression={1. /. 0.}> {children} </Metric>)->ignore

    expect(screen->getByTextExn("∞"))->toBeVisible
  })

  todo("should display display progression tooltip if any given ")
})
