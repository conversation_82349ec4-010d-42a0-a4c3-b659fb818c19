open Vitest
open TestingLibraryReact

let mockedSuccessfulEndpoint = "https://some.success"
let mockedFailedEndpoint = "https://some.error"
let mockedExpectedResultUrl = "https://some.result/"->Url.make

let {setupNodeServer, listen, resetHandlers, close, ctxDelayDuration} = module(MSW)
let {ctxUnsafeObject, postWithDelay} = module(MSW.Rest)

let server = setupNodeServer([
  postWithDelay(mockedSuccessfulEndpoint, (_req, res, ctx) => {
    let payload = {"data": {"url": mockedExpectedResultUrl}}
    res(. ctx->ctxDelayDuration(100), ctx->ctxUnsafeObject(payload))
  }),
  postWithDelay(mockedFailedEndpoint, (_req, res, ctx) => {
    let payload = {"error": "error"}
    res(. ctx->ctxDelayDuration(100), ctx->ctxUnsafeObject(payload))
  }),
])

beforeAll(() => server->listen({onUnhandledRequest: #warn}))
afterEach(() => server->resetHandlers)
afterAll(() => server->close)

module ProviderContext = Popover.Context.Provider

module TestableRequestMenuItem = {
  @react.component
  let make = (
    ~text="Download",
    ~textError="Download failed",
    ~endpoint=mockedSuccessfulEndpoint,
    ~onChange,
  ) => {
    let {state} = Popover.useTrigger()

    let request = () =>
      Request.make(
        endpoint,
        ~method=#POST,
        ~bodyJson=Js.Dict.empty()->Json.encodeDict,
      )->Future.mapError(_error => ())

    let operableRequest = Ok(request)

    <Providers>
      <ProviderContext value=Some(state)>
        <RequestMenuItem text textError operableRequest onChange />
      </ProviderContext>
    </Providers>
  }
}

it("should render", () => {
  let onChange = fn1(ignore)
  let _ = <TestableRequestMenuItem text="My button" onChange={onChange->fn} />->render

  let button = screen->getByRoleExn(#button)

  expect(button)->toBeVisible
  expect(button)->toHaveTextContent("My button")
  expect(button)->Vitest.not->toHaveAttribute("aria-disabled")
  expect(onChange)->toHaveBeenCalledTimes(0)
})

itPromise("should display loading state on click", async () => {
  let userEvent = TestingLibraryEvent.setup()
  let onChange = fn1(ignore)
  let _ = <TestableRequestMenuItem onChange={onChange->fn} />->render

  let button = screen->getByRoleExn(#button)

  expect(button)->toBeVisible
  expect(button)->toHaveTextContent("Download")
  expect(button)->Vitest.not->toHaveAttribute("aria-disabled")
  expect(onChange)->toHaveBeenCalledTimes(0)

  await userEvent->TestingLibraryEvent.click(button)

  expect(button)->toBeVisible
  expect(button)->toHaveAttributeValue("aria-disabled", "true")
  expect(onChange)->toHaveBeenCalledWith1(Loading)
})

itPromise("should successfully run the request on click", async () => {
  let userEvent = TestingLibraryEvent.setup()
  let onChange = fn1(ignore)
  let _ = <TestableRequestMenuItem onChange={onChange->fn} />->render

  let button = screen->getByRoleExn(#button)

  expect(onChange)->toHaveBeenCalledTimes(0)
  expect(button)->toBeVisible
  expect(button)->toHaveTextContent("Download")
  expect(button)->Vitest.not->toHaveAttribute("aria-disabled")

  await userEvent->TestingLibraryEvent.click(button)

  expect(button)->toBeVisible
  expect(button)->toHaveAttributeValue("aria-disabled", "true")
  expect(onChange)->toHaveBeenCalledWith1(Loading)

  await waitFor(() =>
    expect(onChange)->toHaveBeenCalledWith1(
      Done(
        Ok(
          Json.fromObjExn({
            "url": mockedExpectedResultUrl->Url.href,
          }),
        ),
      ),
    )
  )

  expect(button)->toBeVisible
  expect(button)->toHaveTextContent("Download")
  expect(button)->Vitest.not->toHaveAttribute("aria-disabled")
})

itPromise("should execute the request on the click with failure", async () => {
  let userEvent = TestingLibraryEvent.setup()
  let onChange = fn1(ignore)
  let _ = <TestableRequestMenuItem endpoint=mockedFailedEndpoint onChange={onChange->fn} />->render

  let button = screen->getByRoleExn(#button)

  expect(button)->toBeVisible
  expect(button)->toHaveTextContent("Download")
  expect(button)->Vitest.not->toHaveAttribute("aria-disabled")
  expect(onChange)->toHaveBeenCalledTimes(0)

  await userEvent->TestingLibraryEvent.click(button)

  expect(button)->toBeVisible
  expect(button)->toHaveAttributeValue("aria-disabled", "true")
  expect(onChange)->toHaveBeenCalledWith1(Loading)

  await waitFor(() => expect(onChange)->toHaveBeenCalledWith1(Done(Error())))

  expect(button)->toBeVisible
  expect(button)->toHaveTextContent("Download failed")
  expect(button)->Vitest.not->toHaveAttribute("aria-disabled")
})

itPromise(
  "should execute, with a custom text error, the request on the click with failure",
  async () => {
    let userEvent = TestingLibraryEvent.setup()
    let onChange = fn1(ignore)
    let textError = "Bad url error"
    let _ =
      <TestableRequestMenuItem
        endpoint=mockedFailedEndpoint textError onChange={onChange->fn}
      />->render

    let button = screen->getByRoleExn(#button)

    expect(button)->toBeVisible
    expect(button)->toHaveTextContent("Download")
    expect(button)->Vitest.not->toHaveAttribute("aria-disabled")
    expect(onChange)->toHaveBeenCalledTimes(0)

    await userEvent->TestingLibraryEvent.click(button)

    expect(button)->toBeVisible
    expect(button)->toHaveAttributeValue("aria-disabled", "true")
    expect(onChange)->toHaveBeenCalledWith1(Loading)

    await waitFor(() => expect(onChange)->toHaveBeenCalledWith1(Done(Error())))

    expect(button)->toBeVisible
    expect(button)->toHaveTextContent(textError)
    expect(button)->Vitest.not->toHaveAttribute("aria-disabled")
  },
)

todo("should re run the request")
todo("should run the request after a failure")
todo("should not execute the request and display a tooltip error msg")
