open Vitest
open TestingLibraryReact

describe("component", () => {
  let userEvent = TestingLibraryEvent.setup()

  module TestableRadioCardGroup = {
    @react.component
    let make = (~items, ~defaultValue, ~onChange) => {
      let (value, setValue) = React.useState(() => defaultValue)

      <RadioCardGroup
        items
        value
        onChange={value => {
          setValue(_ => value)
          onChange(value)
        }}
      />
    }
  }

  itPromise(
    "should render without any default value and be possible to interact with",
    async () => {
      let onChange = fn1(ignore)
      let items = [
        {
          RadioCardGroup.value: "cat",
          title: "Cat",
          description: "feline",
        },
        {
          value: "dog",
          title: "Dog",
          description: "canine",
        },
        {
          value: "dragon",
          title: "Dragon",
        },
      ]
      render(<TestableRadioCardGroup defaultValue="cat" items onChange={onChange->fn} />)->ignore

      let radioGroup = screen->getByRoleExn(#radiogroup)
      let radiosLength = screen->getAllByRoleExn(#radio)->Array.length
      let (radio1, radio2, radio3) = screen->getAllByRoleExn3(#radio)

      expect(radiosLength)->toBe(3)
      expect(radioGroup)->toBeVisible
      expect(radio1)->toBeVisible
      expect(radio2)->toBeVisible
      expect(radio3)->toBeVisible

      let _radioLabel1 = screen->getByLabelTextExn("Catfeline")
      let radioLabel2 = screen->getByLabelTextExn("Dogcanine")
      let radioLabel3 = screen->getByLabelTextExn("Dragon")

      expect(radio1)->toHaveAttributeValue("value", "cat")
      expect(radio2)->toHaveAttributeValue("value", "dog")
      expect(radio3)->toHaveAttributeValue("value", "dragon")

      expect(radio1->WebAPI.DomElement.checked)->toBe(true)
      expect(radio2->WebAPI.DomElement.checked)->toBe(false)
      expect(radio3->WebAPI.DomElement.checked)->toBe(false)

      expect(onChange)->toHaveBeenCalledTimes(0)

      await userEvent->TestingLibraryEvent.click(radioLabel3)

      expect(radio1->WebAPI.DomElement.checked)->toBe(false)
      expect(radio2->WebAPI.DomElement.checked)->toBe(false)
      expect(radio3->WebAPI.DomElement.checked)->toBe(true)

      expect(onChange)->toHaveBeenCalledTimes(1)
      expect(onChange)->toHaveBeenCalledWith1("dragon")

      await userEvent->TestingLibraryEvent.click(radioLabel2)

      expect(radio1->WebAPI.DomElement.checked)->toBe(false)
      expect(radio2->WebAPI.DomElement.checked)->toBe(true)
      expect(radio3->WebAPI.DomElement.checked)->toBe(false)

      expect(onChange)->toHaveBeenCalledTimes(2)
      expect(onChange)->toHaveBeenCalledWith1("dog")

      await userEvent->TestingLibraryEvent.click(radioLabel2)

      expect(radio1->WebAPI.DomElement.checked)->toBe(false)
      expect(radio2->WebAPI.DomElement.checked)->toBe(true)
      expect(radio3->WebAPI.DomElement.checked)->toBe(false)

      expect(onChange)->toHaveBeenCalledTimes(2)
      expect(onChange)->toHaveBeenCalledWith1("dog")
    },
  )

  itPromise("should not be possible to interact with disabled value", async () => {
    let onChange = fn1(ignore)
    let items = [
      {
        RadioCardGroup.value: "cat",
        title: "Cat",
      },
      {
        value: "dog",
        title: "Dog",
        disabled: true,
      },
    ]
    render(<TestableRadioCardGroup defaultValue="cat" items onChange={onChange->fn} />)->ignore

    let radioGroup = screen->getByRoleExn(#radiogroup)
    let radiosLength = screen->getAllByRoleExn(#radio)->Array.length
    let (radio1, radio2) = screen->getAllByRoleExn2(#radio)

    expect(radiosLength)->toBe(2)
    expect(radioGroup)->toBeVisible
    expect(radio1)->toBeVisible
    expect(radio2)->toBeVisible

    let _radioLabel1 = screen->getByLabelTextExn("Cat")
    let radioLabel2 = screen->getByLabelTextExn("Dog")

    expect(radio1)->toHaveAttributeValue("value", "cat")
    expect(radio2)->toHaveAttributeValue("value", "dog")

    expect(radio1->WebAPI.DomElement.checked)->toBe(true)
    expect(radio2->WebAPI.DomElement.checked)->toBe(false)

    expect(onChange)->toHaveBeenCalledTimes(0)

    await userEvent->TestingLibraryEvent.click(radioLabel2)

    expect(radio1->WebAPI.DomElement.checked)->toBe(true)
    expect(radio2->WebAPI.DomElement.checked)->toBe(false)

    expect(onChange)->toHaveBeenCalledTimes(0)
  })

  itPromise(
    "should select by default the first radio when the passed value is not found",
    async () => {
      let onChange = fn1(ignore)
      let items = [
        {
          RadioCardGroup.value: "cat",
          title: "Cat",
        },
        {
          value: "dog",
          title: "Dog",
          disabled: true,
        },
      ]
      render(<TestableRadioCardGroup defaultValue="sponge" items onChange={onChange->fn} />)->ignore

      let radioGroup = screen->getByRoleExn(#radiogroup)
      let radiosLength = screen->getAllByRoleExn(#radio)->Array.length
      let (radio1, radio2) = screen->getAllByRoleExn2(#radio)

      expect(radiosLength)->toBe(2)
      expect(radioGroup)->toBeVisible
      expect(radio1)->toBeVisible
      expect(radio2)->toBeVisible

      expect(radio1)->toHaveAttributeValue("value", "cat")
      expect(radio2)->toHaveAttributeValue("value", "dog")

      expect(radio1->WebAPI.DomElement.checked)->toBe(true)
      expect(radio2->WebAPI.DomElement.checked)->toBe(false)
    },
  )
})
