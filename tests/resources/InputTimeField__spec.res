open Vitest
open TestingLibraryReact

let originalDate = %raw(`global.Date`)
let originalTimestamp = Js.Date.now()

let makeDate = Js.Date.makeWithYMD(~year=2000., ~month=1., ~date=15.)
let makeDatetime = Js.Date.makeWithYMDHM(~year=2000., ~month=1., ~date=15.)

let mockDate: Js.Date.t => unit = %raw(`date => {
  global.Date = vi.fn().mockImplementation(() => date)
  global.Date.now = vi.fn().mockReturnValue(date.valueOf())
}`)
let resetDate: unit => unit = %raw(`() => {
  global.Date = originalDate
  global.Date.now = () => originalTimestamp
}`)

afterEach(() => {
  restoreAllMocks()
  resetDate()
})

describe("conversion", () => {
  let {internationalizationDatetimeFromJsDateObj, internationalizationDatetimeToJsDateObj} = module(
    InputTimeField
  )
  let {make, getHours, getMinutes} = module(Internationalization.Date.Time)

  test("internationalizationDatetimeFromJsDateObj", () => {
    let date = makeDate()
    let result = internationalizationDatetimeFromJsDateObj(date)

    expect(result->getHours)->toBe(0.)
    expect(result->getMinutes)->toBe(0.)

    let datetime = makeDatetime(~hours=23., ~minutes=59., ())
    let result = internationalizationDatetimeFromJsDateObj(datetime)

    expect(result->getHours)->toBe(23.)
    expect(result->getMinutes)->toBe(59.)
  })

  test("internationalizationDatetimeToJsDateObj", () => {
    let time = make(~hours=12., ~minutes=58.)
    let result = internationalizationDatetimeToJsDateObj(time)

    expect(result->Js.Date.getHours)->toBe(12.)
    expect(result->Js.Date.getMinutes)->toBe(58.)
  })
})

module TestableInputTimeField = {
  @react.component
  let make = (~initialValue, ~onChange=?, ~onFocus=?, ~onBlur=?) => {
    let (value, setValue) = React.useState(() => initialValue)

    <InputTimeField
      testLocalization=#"fr-FR"
      value
      onChange={value => {
        setValue(_ => Some(value))
        onChange->Option.forEach(onChange => onChange(value))
      }}
      ?onFocus
      ?onBlur
    />
  }
}

describe("component", () => {
  let userEvent = TestingLibraryEvent.setup()

  itPromise("should render and be possible to interact with", async () => {
    let onChange = fn1(ignore)
    let onFocus = fn1(ignore)
    let onBlur = fn1(ignore)

    let {unmount, baseElement} =
      <TestableInputTimeField
        initialValue=None onChange={onChange->fn} onFocus={onFocus->fn} onBlur={onBlur->fn}
      />->render

    let input = screen->getByRoleExn(#group)
    let (hours, minutes) = within(input)->getAllByRoleExn2(#spinbutton)

    expect(input)->toBeVisible
    expect(input)->toHaveTextContent("––:––")

    expect(hours)->toBeVisible
    expect(hours)->toHaveTextContent("––")
    expect(hours)->toHaveAttributeValue("aria-valuenow", "0")
    expect(hours)->toHaveAttributeValue("aria-valuetext", "Empty")
    expect(hours)->toHaveAttributeValue("aria-valuemin", "0")
    expect(hours)->toHaveAttributeValue("aria-valuemax", "23")

    expect(minutes)->toBeVisible
    expect(minutes)->toHaveTextContent("––")
    expect(minutes)->toHaveAttributeValue("aria-valuenow", "0")
    expect(minutes)->toHaveAttributeValue("aria-valuetext", "Empty")
    expect(minutes)->toHaveAttributeValue("aria-valuemin", "0")
    expect(minutes)->toHaveAttributeValue("aria-valuemax", "59")

    expect(input)->Vitest.not->toHaveFocus
    expect(hours)->Vitest.not->toHaveFocus
    expect(minutes)->Vitest.not->toHaveFocus
    expect(onFocus)->toHaveBeenCalledTimes(0)
    expect(onBlur)->toHaveBeenCalledTimes(0)

    await userEvent->TestingLibraryEvent.click(input)

    expect(hours)->toHaveFocus
    expect(minutes)->Vitest.not->toHaveFocus
    expect(onFocus)->toHaveBeenCalledTimes(1)
    expect(onBlur)->toHaveBeenCalledTimes(0)

    await userEvent->TestingLibraryEvent.type_(hours, "12") // FIXME - only chromium supports inputing directly on the whole input focused

    expect(hours)->Vitest.not->toHaveFocus
    expect(minutes)->toHaveFocus
    expect(onFocus)->toHaveBeenCalledTimes(1)
    expect(onBlur)->toHaveBeenCalledTimes(0)
    expect(onChange)->toHaveBeenCalledTimes(0)
    expect(input)->toHaveTextContent("12:––")

    await userEvent->TestingLibraryEvent.type_(minutes, "3")

    expect(onChange)->toHaveBeenCalledTimes(1)
    expect(input)->toHaveTextContent("12:03")

    await userEvent->TestingLibraryEvent.type_(minutes, "0")

    expect(onChange)->toHaveBeenCalledTimes(2)
    expect(input)->toHaveTextContent("12:30")

    expect(hours)->toHaveAttributeValue("aria-valuenow", "12")
    expect(hours)->toHaveAttributeValue("aria-valuetext", "12")
    expect(minutes)->toHaveAttributeValue("aria-valuenow", "30")
    expect(minutes)->toHaveAttributeValue("aria-valuetext", "30")
    expect(onFocus)->toHaveBeenCalledTimes(1)
    expect(onBlur)->toHaveBeenCalledTimes(0)

    await userEvent->TestingLibraryEvent.click(baseElement)

    expect(onFocus)->toHaveBeenCalledTimes(1)
    expect(onBlur)->toHaveBeenCalledTimes(1)

    mockClear(onChange)
    unmount()

    let datetime = makeDatetime(~hours=23., ~minutes=59., ())
    let _ = <TestableInputTimeField initialValue=Some(datetime) onChange={onChange->fn} />->render

    let input = screen->getByRoleExn(#group)
    let (hours, minutes) = within(input)->getAllByRoleExn2(#spinbutton)

    expect(input)->toBeVisible
    expect(input)->toHaveTextContent("23:59")

    expect(hours)->toBeVisible
    expect(hours)->toHaveTextContent("23")
    expect(hours)->toHaveAttributeValue("aria-valuenow", "23")
    expect(hours)->toHaveAttributeValue("aria-valuetext", "23")

    expect(minutes)->toBeVisible
    expect(minutes)->toHaveTextContent("59")
    expect(minutes)->toHaveAttributeValue("aria-valuenow", "59")
    expect(minutes)->toHaveAttributeValue("aria-valuetext", "59")
  })

  itPromise("should trigger onChange when the datetime is completed", async () => {
    let onChange = fn1(ignore)

    mockDate(makeDate())

    let {baseElement} = <TestableInputTimeField initialValue=None onChange={onChange->fn} />->render

    let input = screen->getByRoleExn(#group)
    let (hours, minutes) = within(input)->getAllByRoleExn2(#spinbutton)

    expect(input)->toBeVisible
    expect(input)->toHaveTextContent("––:––")

    await userEvent->TestingLibraryEvent.click(input)

    let expectedDatetime = makeDatetime(~hours=15., ~minutes=14., ())

    await userEvent->TestingLibraryEvent.type_(hours, "15:14") // FIXME - only chromium supports inputing directly on the whole input focused

    expect(input)->toHaveTextContent("15:14")
    expect(onChange)->toHaveBeenCalledTimes(2)
    expect(onChange)->toHaveBeenLastCalledWith1(expectedDatetime)
    expect(minutes)->toHaveFocus

    let expectedDatetime = makeDatetime(~hours=15., ~minutes=1., ())

    await userEvent->TestingLibraryEvent.type_(minutes, "{backspace}")

    expect(input)->toHaveTextContent("15:01")
    expect(onChange)->toHaveBeenLastCalledWith1(expectedDatetime)
    expect(onChange)->toHaveBeenCalledTimes(3)

    await userEvent->TestingLibraryEvent.type_(input, "{backspace}{backspace}")
    await userEvent->TestingLibraryEvent.click(hours)
    await userEvent->TestingLibraryEvent.type_(hours, "{backspace}{backspace}")

    expect(hours)->toHaveFocus
    expect(input)->toHaveTextContent("––:––")
    expect(onChange)->toHaveBeenCalledTimes(3)

    await userEvent->TestingLibraryEvent.type_(hours, "6")

    expect(minutes)->toHaveFocus
    expect(input)->toHaveTextContent("06:––")
    expect(onChange)->toHaveBeenCalledTimes(3)

    await userEvent->TestingLibraryEvent.click(baseElement)

    expect(input)->Vitest.not->toHaveFocus
    expect(hours)->Vitest.not->toHaveFocus
    expect(minutes)->Vitest.not->toHaveFocus
  })

  itPromise(
    "should be possible to edit the minutes directly by clicking on their segment",
    async () => {
      let onChange = fn1(ignore)

      // FIXME - when mocking: only in component lifetime, the datetime is 12:00 instead
      let datetime = makeDatetime(~hours=11., ~minutes=26., ())
      // Js.log2("sent:", datetime)
      let _ = <TestableInputTimeField initialValue=Some(datetime) onChange={onChange->fn} />->render

      let input = screen->getByRoleExn(#group)
      let (_, minutes) = within(input)->getAllByRoleExn2(#spinbutton)

      // screen->debugElement(input)

      expect(input)->toBeVisible
      expect(input)->toHaveTextContent("11:26")

      // let expectedDatetime = makeDatetime(~hours=11., ~minutes=45., ())

      await userEvent->TestingLibraryEvent.click(minutes)
      await userEvent->TestingLibraryEvent.type_(minutes, "45")

      expect(minutes)->toHaveFocus
      expect(input)->toHaveTextContent("11:45")
      // expect(onChange)->toHaveBeenLastCalledWith1(expectedDatetime)
      expect(onChange)->toHaveBeenCalledTimes(2)
    },
  )

  itPromise(
    "should be possible to interact with the component and change the value with keyboard arrows",
    async () => {
      let _ = <TestableInputTimeField initialValue=None />->render

      let input = screen->getByRoleExn(#group)
      let (hours, minutes) = within(input)->getAllByRoleExn2(#spinbutton)

      expect(input)->toBeVisible
      expect(input)->toHaveTextContent("––:––")

      await userEvent->TestingLibraryEvent.click(hours)
      await userEvent->TestingLibraryEvent.type_(hours, "{arrowdown}")

      expect(hours)->toHaveFocus
      expect(input)->toHaveTextContent("00:––")

      await userEvent->TestingLibraryEvent.type_(hours, "{arrowdown}")

      expect(input)->toHaveTextContent("23:––")

      await userEvent->TestingLibraryEvent.type_(hours, "{arrowup}{arrowup}{arrowup}")
      await userEvent->TestingLibraryEvent.type_(minutes, "59")

      expect(input)->toHaveTextContent("02:59")

      await userEvent->TestingLibraryEvent.type_(minutes, "{arrowup}{arrowup}")

      expect(input)->toHaveTextContent("02:01")

      await userEvent->TestingLibraryEvent.type_(minutes, "{arrowdown}{arrowdown}{arrowdown}")

      expect(input)->toHaveTextContent("02:58")

      expect(hours)->toHaveAttributeValue("aria-valuenow", "2")
      expect(hours)->toHaveAttributeValue("aria-valuetext", "02")
      expect(minutes)->toHaveAttributeValue("aria-valuenow", "58")
      expect(minutes)->toHaveAttributeValue("aria-valuetext", "58")
    },
  )

  itPromise("should switch the focus to the next segment in different scenarios", async () => {
    let datetime = makeDatetime(~hours=12., ~minutes=30., ())
    let _ = <TestableInputTimeField initialValue=Some(datetime) />->render

    let input = screen->getByRoleExn(#group)
    let (hours, minutes) = within(input)->getAllByRoleExn2(#spinbutton)

    expect(input)->toBeVisible
    expect(input)->toHaveTextContent("12:30")

    await userEvent->TestingLibraryEvent.click(hours)

    expect(hours)->toHaveFocus

    await userEvent->TestingLibraryEvent.type_(hours, "12:30")

    expect(minutes)->toHaveFocus
    expect(input)->toHaveTextContent("12:30")

    await userEvent->TestingLibraryEvent.click(hours)
    await userEvent->TestingLibraryEvent.type_(hours, "3")

    expect(minutes)->toHaveFocus
    expect(input)->toHaveTextContent("03:30")

    await userEvent->TestingLibraryEvent.click(hours)
    await userEvent->TestingLibraryEvent.type_(hours, "13")

    expect(minutes)->toHaveFocus
    expect(input)->toHaveTextContent("13:30")

    await userEvent->TestingLibraryEvent.type_(minutes, "27")

    expect(minutes)->toHaveFocus
    expect(input)->toHaveTextContent("13:27")

    await userEvent->TestingLibraryEvent.click(hours)
    await userEvent->TestingLibraryEvent.type_(hours, "24")

    expect(minutes)->toHaveFocus
    expect(input)->toHaveTextContent("04:27")

    expect(hours)->toHaveAttributeValue("aria-valuenow", "4")
    expect(hours)->toHaveAttributeValue("aria-valuetext", "04")
    expect(minutes)->toHaveAttributeValue("aria-valuenow", "27")
    expect(minutes)->toHaveAttributeValue("aria-valuetext", "27")
  })

  itPromise("should reset the segment placeholders on blur if the value incomplete", async () => {
    let {baseElement} = <TestableInputTimeField initialValue=None />->render

    let input = screen->getByRoleExn(#group)
    let (hours, minutes) = within(input)->getAllByRoleExn2(#spinbutton)

    expect(input)->toBeVisible
    expect(input)->toHaveTextContent("––:––")

    await userEvent->TestingLibraryEvent.click(input)
    await userEvent->TestingLibraryEvent.type_(hours, "13h")

    expect(minutes)->toHaveFocus
    expect(input)->toHaveTextContent("13:––")

    await userEvent->TestingLibraryEvent.click(baseElement)

    expect(input)->Vitest.not->toHaveFocus
    expect(hours)->Vitest.not->toHaveFocus
    expect(minutes)->Vitest.not->toHaveFocus

    expect(input)->toHaveTextContent("––:––")

    expect(hours)->toHaveAttributeValue("aria-valuenow", "13")
    expect(hours)->toHaveAttributeValue("aria-valuetext", "Empty")

    await userEvent->TestingLibraryEvent.click(minutes)
    await userEvent->TestingLibraryEvent.type_(minutes, "42")

    expect(input)->toHaveTextContent("––:42")

    await userEvent->TestingLibraryEvent.click(baseElement)

    expect(input)->toHaveTextContent("––:––")

    expect(minutes)->toHaveAttributeValue("aria-valuenow", "0")
    expect(minutes)->toHaveAttributeValue("aria-valuetext", "Empty")
  })
})
