open Vitest
open TestingLibraryReact

it("should render a visible element", () => {
  <Providers>
    <ButtonLink to=Route("/")> {"my button link"->React.string} </ButtonLink>
  </Providers>
  ->render
  ->ignore

  expect(screen->getByTextExn("my button link"))->toBeVisible
})

itPromise("should render a button route link", async () => {
  let userEvent = TestingLibraryEvent.setup()
  let history = History.createMemoryHistory()
  let nextPathname = "/route"

  <Providers history>
    <ButtonLink to=Route(nextPathname)> {"my button route link"->React.string} </ButtonLink>
  </Providers>
  ->render
  ->ignore

  expect(history.length)->toBe(1)
  expect(history.location.pathname)->toBe("/")

  let link = screen->getByRoleExn(#link)

  expect(link)->toBeVisible
  expect(link)->toHaveAttributeValue("href", nextPathname)

  await userEvent->TestingLibraryEvent.click(link)

  expect(history.length)->toBe(2)
  expect(history.location.pathname)->toBe(nextPathname)
})

itPromise("should render a disabled button", async () => {
  let userEvent = TestingLibraryEvent.setup()
  let history = History.createMemoryHistory()
  let nextPathname = "/forbidden-route"

  let {container} = <Providers history>
    <ButtonLink to=Route(nextPathname) disabled=true>
      {"my disabled button"->React.string}
    </ButtonLink>
  </Providers>->render

  let link = screen->getByRoleExn(#link)

  expect(link)->toHaveAttributeValue("aria-disabled", "true")
  expect(container)->toBeVisible
  expect(history.length)->toBe(1)
  expect(history.location.pathname)->toBe("/")

  await userEvent->TestingLibraryEvent.click(container)

  expect(history.length)->toBe(1)
  expect(history.location.pathname)->toBe("/")
})

todo("should render a button simulated href link (window.open link in new tab)")
