open Vitest
open TestingLibraryReact
open WebAPI

let userEvent = TestingLibraryEvent.setup()

itPromise("should open a dialog when clicking on Menu button", async () => {
  render(
    <Menu buttonText="My menu">
      <MenuItem content=Text("Test item") action=Callback(() => ()) />
    </Menu>,
  )->ignore

  let menuButton =
    screen->getByRoleWithOptionsExn(#button, {name: "My menu", expanded: false, hidden: false})

  expect(menuButton)->toBeVisible
  expect(menuButton->DomElement.getAttribute("aria-controls"))->toBeNone
  expect(screen->queryByRole(#dialog))->toBeNone

  await userEvent->TestingLibraryEvent.click(menuButton)

  let menuButton =
    screen->getByRoleWithOptionsExn(#button, {name: "My menu", expanded: true, hidden: true})

  expect(menuButton)->toBeVisible

  let dialog = screen->getByRoleExn(#dialog)
  let menuControlId = menuButton->DomElement.getAttribute("aria-controls")->Option.getExn

  expect(dialog)->toBeVisible
  expect(dialog)->toHaveAttributeValue("id", menuControlId)

  await userEvent->TestingLibraryEvent.click(menuButton)

  let menuButton =
    screen->getByRoleWithOptionsExn(#button, {name: "My menu", expanded: false, hidden: false})

  expect(menuButton)->toBeVisible
  expect(menuButton->DomElement.getAttribute("aria-controls"))->toBeNone
  expect(screen->queryByRole(#dialog))->toBeNone
})

itPromise("should close the opened dialog when clicking on its children item", async () => {
  let spyMenuItemAction = fn1(ignore)

  render(
    <Menu buttonText="My menu">
      <MenuItem content=Text("My item") action=Callback(() => spyMenuItemAction->fn()) />
    </Menu>,
  )->ignore

  let menuButton = screen->getByRoleWithOptionsExn(#button, {name: "My menu"})

  await userEvent->TestingLibraryEvent.click(menuButton)

  let menuItem = screen->getByRoleWithOptionsExn(#button, {name: "My item"})

  expect(spyMenuItemAction)->toHaveBeenCalledTimes(0)
  expect(menuItem)->toBeVisible

  await userEvent->TestingLibraryEvent.click(menuItem)

  expect(spyMenuItemAction)->toHaveBeenCalledTimes(1)
  expect(menuItem)->Vitest.not->toBeVisible
})

itPromise("should not open the dialog when Menu is disabled", async () => {
  render(
    <Menu buttonText="My menu" disabled=true>
      <MenuItem content=Text("My item") action=Callback(() => ()) />
    </Menu>,
  )->ignore

  let menuButton =
    screen->getByRoleWithOptionsExn(#button, {name: "My menu", expanded: false, hidden: false})

  expect(menuButton)->toBeVisible
  expect(screen->queryByRole(#dialog))->toBeNone

  await userEvent->TestingLibraryEvent.click(menuButton)

  let menuButton =
    screen->getByRoleWithOptionsExn(#button, {name: "My menu", expanded: false, hidden: false})

  expect(menuButton)->toBeVisible
  expect(screen->queryByRole(#dialog))->toBeNone
})

todo("component: test `overlayPriority` props")
