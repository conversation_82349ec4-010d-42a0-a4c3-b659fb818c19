open Vitest
open TestingLibraryReact

module TestableInputToggleSwitchField = {
  @react.component
  let make = (~onChange, ~disabled=?) => {
    let (value, setValue) = React.useState(() => false)

    <InputToggleSwitchField
      label="My label"
      value={value}
      required={false}
      onChange={newValue => {
        onChange(newValue)
        setValue(_ => newValue)
      }}
      ?disabled
    />
  }
}

itPromise("should trigger onChange callback on checkbox button click", async () => {
  let userEvent = TestingLibraryEvent.setup()
  let onChange = fn1(ignore)

  render(<TestableInputToggleSwitchField onChange={onChange->fn} />)->ignore

  let button = screen->getAllByRoleExn(#button)->Array.getExn(0)

  expect(button)->toBeVisible
  expect(onChange)->toHaveBeenCalledTimes(0)

  await userEvent->TestingLibraryEvent.click(button)

  expect(button)->toBeVisible
  expect(onChange)->toHaveBeenCalledTimes(1)
  expect(onChange)->toHaveBeenCalledWith1(true)

  await userEvent->TestingLibraryEvent.click(button)

  expect(button)->toBeVisible
  expect(onChange)->toHaveBeenCalledTimes(2)
  expect(onChange)->toHaveBeenCalledWith1(false)
})

itPromise("should trigger onChange callback on label button click", async () => {
  let userEvent = TestingLibraryEvent.setup()
  let onChange = fn1(ignore)

  render(<TestableInputToggleSwitchField onChange={onChange->fn} />)->ignore

  let button = screen->getAllByRoleExn(#button)->Array.getExn(0)

  expect(button)->toBeVisible
  expect(onChange)->toHaveBeenCalledTimes(0)

  await userEvent->TestingLibraryEvent.click(button)

  expect(button)->toBeVisible
  expect(onChange)->toHaveBeenCalledTimes(1)
  expect(onChange)->toHaveBeenCalledWith1(true)

  await userEvent->TestingLibraryEvent.click(button)

  expect(button)->toBeVisible
  expect(onChange)->toHaveBeenCalledTimes(2)
  expect(onChange)->toHaveBeenCalledWith1(false)
})

itPromise("should not be possible to interact with", async () => {
  let userEvent = TestingLibraryEvent.setup()
  let onChange = fn1(ignore)
  let disabled = true

  render(<TestableInputToggleSwitchField onChange={onChange->fn} disabled />)->ignore

  let button = screen->getAllByRoleExn(#button)->Array.getExn(0)

  expect(button)->toBeVisible
  expect(onChange)->toHaveBeenCalledTimes(0)

  await userEvent->TestingLibraryEvent.click(button)

  expect(button)->toBeVisible
  expect(onChange)->toHaveBeenCalledTimes(0)

  await userEvent->TestingLibraryEvent.click(button)

  expect(button)->toBeVisible
  expect(onChange)->toHaveBeenCalledTimes(0)
})
