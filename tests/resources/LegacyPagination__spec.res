open Vitest
open TestingLibraryReact

test("isButtonDisabled", () => {
  let {isButtonDisabled} = module(LegacyPagination)

  let currentPage = 1
  let totalPages = 1
  expect(isButtonDisabled(~action=LegacyPagination.First, ~currentPage, ~totalPages))->toBe(true)
  expect(isButtonDisabled(~action=Prev, ~currentPage, ~totalPages))->toBe(true)
  expect(isButtonDisabled(~action=Last, ~currentPage, ~totalPages))->toBe(true)
  expect(isButtonDisabled(~action=Next, ~currentPage, ~totalPages))->toBe(true)

  let currentPage = 1
  let totalPages = 2
  expect(isButtonDisabled(~action=First, ~currentPage, ~totalPages))->toBe(true)
  expect(isButtonDisabled(~action=Prev, ~currentPage, ~totalPages))->toBe(true)
  expect(isButtonDisabled(~action=Last, ~currentPage, ~totalPages))->toBe(false)
  expect(isButtonDisabled(~action=Next, ~currentPage, ~totalPages))->toBe(false)

  let currentPage = 2
  let totalPages = 2
  expect(isButtonDisabled(~action=First, ~currentPage, ~totalPages))->toBe(false)
  expect(isButtonDisabled(~action=Prev, ~currentPage, ~totalPages))->toBe(false)
  expect(isButtonDisabled(~action=Last, ~currentPage, ~totalPages))->toBe(true)
  expect(isButtonDisabled(~action=Next, ~currentPage, ~totalPages))->toBe(true)

  let currentPage = 1
  let totalPages = 3
  expect(isButtonDisabled(~action=First, ~currentPage, ~totalPages))->toBe(true)
  expect(isButtonDisabled(~action=Prev, ~currentPage, ~totalPages))->toBe(true)
  expect(isButtonDisabled(~action=Last, ~currentPage, ~totalPages))->toBe(false)
  expect(isButtonDisabled(~action=Next, ~currentPage, ~totalPages))->toBe(false)

  let currentPage = 2
  let totalPages = 3
  expect(isButtonDisabled(~action=First, ~currentPage, ~totalPages))->toBe(false)
  expect(isButtonDisabled(~action=Prev, ~currentPage, ~totalPages))->toBe(false)
  expect(isButtonDisabled(~action=Last, ~currentPage, ~totalPages))->toBe(false)
  expect(isButtonDisabled(~action=Next, ~currentPage, ~totalPages))->toBe(false)

  let currentPage = 3
  let totalPages = 3
  expect(isButtonDisabled(~action=First, ~currentPage, ~totalPages))->toBe(false)
  expect(isButtonDisabled(~action=Prev, ~currentPage, ~totalPages))->toBe(false)
  expect(isButtonDisabled(~action=Last, ~currentPage, ~totalPages))->toBe(true)
  expect(isButtonDisabled(~action=Next, ~currentPage, ~totalPages))->toBe(true)

  let currentPage = 1
  let totalPages = 5
  expect(isButtonDisabled(~action=First, ~currentPage, ~totalPages))->toBe(true)
  expect(isButtonDisabled(~action=Prev, ~currentPage, ~totalPages))->toBe(true)
  expect(isButtonDisabled(~action=Last, ~currentPage, ~totalPages))->toBe(false)
  expect(isButtonDisabled(~action=Next, ~currentPage, ~totalPages))->toBe(false)

  let currentPage = 2
  let totalPages = 5
  expect(isButtonDisabled(~action=First, ~currentPage, ~totalPages))->toBe(false)
  expect(isButtonDisabled(~action=Prev, ~currentPage, ~totalPages))->toBe(false)
  expect(isButtonDisabled(~action=Last, ~currentPage, ~totalPages))->toBe(false)
  expect(isButtonDisabled(~action=Next, ~currentPage, ~totalPages))->toBe(false)

  let currentPage = 3
  let totalPages = 5
  expect(isButtonDisabled(~action=First, ~currentPage, ~totalPages))->toBe(false)
  expect(isButtonDisabled(~action=Prev, ~currentPage, ~totalPages))->toBe(false)
  expect(isButtonDisabled(~action=Last, ~currentPage, ~totalPages))->toBe(false)
  expect(isButtonDisabled(~action=Next, ~currentPage, ~totalPages))->toBe(false)

  let currentPage = 4
  let totalPages = 5
  expect(isButtonDisabled(~action=First, ~currentPage, ~totalPages))->toBe(false)
  expect(isButtonDisabled(~action=Prev, ~currentPage, ~totalPages))->toBe(false)
  expect(isButtonDisabled(~action=Last, ~currentPage, ~totalPages))->toBe(false)
  expect(isButtonDisabled(~action=Next, ~currentPage, ~totalPages))->toBe(false)

  let currentPage = 5
  let totalPages = 5
  expect(isButtonDisabled(~action=First, ~currentPage, ~totalPages))->toBe(false)
  expect(isButtonDisabled(~action=Prev, ~currentPage, ~totalPages))->toBe(false)
  expect(isButtonDisabled(~action=Last, ~currentPage, ~totalPages))->toBe(true)
  expect(isButtonDisabled(~action=Next, ~currentPage, ~totalPages))->toBe(true)
})

let toBeAriaDisabled = expect => expect->toHaveAttributeValue("aria-disabled", "true")

let toBeVisibleAndDisabled = expectButton => {
  expectButton->toBeVisible
  expectButton->toBeAriaDisabled
}

let toBeVisibleAndActive = expectButton => {
  expectButton->toBeVisible
  expectButton->Vitest.not->toBeAriaDisabled
}

let toBeVisibleAndDisabledWithText = (expectButton, text) => {
  expectButton->toBeVisible
  expectButton->toBeAriaDisabled
  expectButton->toHaveTextContent(text)
}

let toBeVisibleAndActiveWithText = (expectButton, text) => {
  expectButton->toBeVisible
  expectButton->toHaveTextContent(text)
  expectButton->Vitest.not->toBeAriaDisabled
}

let assert5Buttons = buttons =>
  switch buttons {
  | [a, b, c, d, e] => (a, b, c, d, e)
  | _ => failwith("Expect to have 6 pagintion buttons")
  }

let assert6Buttons = buttons =>
  switch buttons {
  | [a, b, c, d, e, f] => (a, b, c, d, e, f)
  | _ => failwith("Expect to have 6 pagintion buttons")
  }

let assert7Buttons = buttons =>
  switch buttons {
  | [a, b, c, d, e, f, g] => (a, b, c, d, e, f, g)
  | _ => failwith("Expect to have 7 pagintion buttons")
  }

testPromise("component", async () => {
  let userEvent = TestingLibraryEvent.setup()
  let onRequestPaginate = fn1(ignore)

  let {rerender} =
    <LegacyPagination
      currentPage=1 totalPages=3 onRequestPaginate={onRequestPaginate->fn}
    />->render
  let buttons = screen->getAllByRoleExn(#button)
  let (buttonFirst, buttonPrev, button1, button2, buttonNext, buttonLast) = assert6Buttons(buttons)

  let expectPage1Of3 = () => {
    expect(screen->getByTextExn("Page 1 of 3"))->toBeVisible
    expect(buttonFirst)->toBeVisibleAndDisabled
    expect(buttonPrev)->toBeVisibleAndDisabled
    expect(button1)->toBeVisibleAndDisabledWithText("1")
    expect(button2)->toBeVisibleAndActiveWithText("2")
    expect(buttonNext)->toBeVisibleAndActive
    expect(buttonLast)->toBeVisibleAndActive
  }

  expectPage1Of3()
  expect(onRequestPaginate)->toHaveBeenCalledTimes(0)

  await userEvent->TestingLibraryEvent.click(buttonFirst)

  expectPage1Of3()
  expect(onRequestPaginate)->toHaveBeenCalledTimes(0)

  await userEvent->TestingLibraryEvent.click(buttonPrev)

  expectPage1Of3()
  expect(onRequestPaginate)->toHaveBeenCalledTimes(0)

  await userEvent->TestingLibraryEvent.click(button1)

  expectPage1Of3()
  expect(onRequestPaginate)->toHaveBeenCalledTimes(0)

  await userEvent->TestingLibraryEvent.click(button2)

  expectPage1Of3()
  expect(onRequestPaginate)->toHaveBeenCalledTimes(1)
  expect(onRequestPaginate)->toHaveBeenCalledWith1(Next)
  onRequestPaginate->mockClear

  await userEvent->TestingLibraryEvent.click(buttonNext)

  expectPage1Of3()
  expect(onRequestPaginate)->toHaveBeenCalledTimes(1)
  expect(onRequestPaginate)->toHaveBeenCalledWith1(Next)
  onRequestPaginate->mockClear

  await userEvent->TestingLibraryEvent.click(buttonLast)

  expectPage1Of3()
  expect(onRequestPaginate)->toHaveBeenCalledTimes(1)
  expect(onRequestPaginate)->toHaveBeenCalledWith1(Last)
  onRequestPaginate->mockClear

  rerender(
    <LegacyPagination currentPage=2 totalPages=3 onRequestPaginate={onRequestPaginate->fn} />,
  )->ignore

  let buttons = screen->getAllByRoleExn(#button)
  let (buttonFirst, buttonPrev, button1, button2, button3, buttonNext, buttonLast) = assert7Buttons(
    buttons,
  )

  let expectPage2Of3 = () => {
    expect(screen->getByTextExn("Page 2 of 3"))->toBeVisible
    expect(buttonFirst)->toBeVisibleAndActive
    expect(buttonPrev)->toBeVisibleAndActive
    expect(button1)->toBeVisibleAndActiveWithText("1")
    expect(button2)->toBeVisibleAndDisabledWithText("2")
    expect(button3)->toBeVisibleAndActiveWithText("3")
    expect(buttonNext)->toBeVisibleAndActive
    expect(buttonLast)->toBeVisibleAndActive
  }

  expectPage2Of3()
  expect(onRequestPaginate)->toHaveBeenCalledTimes(0)

  await userEvent->TestingLibraryEvent.click(buttonFirst)

  expectPage2Of3()
  expect(onRequestPaginate)->toHaveBeenCalledTimes(1)
  expect(onRequestPaginate)->toHaveBeenCalledWith1(First)
  onRequestPaginate->mockClear

  await userEvent->TestingLibraryEvent.click(buttonPrev)

  expectPage2Of3()
  expect(onRequestPaginate)->toHaveBeenCalledTimes(1)
  expect(onRequestPaginate)->toHaveBeenCalledWith1(Prev)
  onRequestPaginate->mockClear

  await userEvent->TestingLibraryEvent.click(button1)

  expectPage2Of3()
  expect(onRequestPaginate)->toHaveBeenCalledTimes(1)
  expect(onRequestPaginate)->toHaveBeenCalledWith1(Prev)
  onRequestPaginate->mockClear

  await userEvent->TestingLibraryEvent.click(button2)

  expectPage2Of3()
  expect(onRequestPaginate)->toHaveBeenCalledTimes(0)

  await userEvent->TestingLibraryEvent.click(button3)

  expectPage2Of3()
  expect(onRequestPaginate)->toHaveBeenCalledTimes(1)
  expect(onRequestPaginate)->toHaveBeenCalledWith1(Next)
  onRequestPaginate->mockClear

  await userEvent->TestingLibraryEvent.click(buttonNext)

  expectPage2Of3()
  expect(onRequestPaginate)->toHaveBeenCalledTimes(1)
  expect(onRequestPaginate)->toHaveBeenCalledWith1(Next)
  onRequestPaginate->mockClear

  await userEvent->TestingLibraryEvent.click(buttonLast)

  expectPage2Of3()
  expect(onRequestPaginate)->toHaveBeenCalledTimes(1)
  expect(onRequestPaginate)->toHaveBeenCalledWith1(Last)
  onRequestPaginate->mockClear

  rerender(
    <LegacyPagination currentPage=3 totalPages=3 onRequestPaginate={onRequestPaginate->fn} />,
  )->ignore

  let buttons = screen->getAllByRoleExn(#button)
  let (buttonFirst, buttonPrev, button2, button3, buttonNext, buttonLast) = assert6Buttons(buttons)

  let expectPage3Of3 = () => {
    expect(screen->getByTextExn("Page 3 of 3"))->toBeVisible
    expect(buttonFirst)->toBeVisibleAndActive
    expect(buttonPrev)->toBeVisibleAndActive
    expect(button2)->toBeVisibleAndActiveWithText("2")
    expect(button3)->toBeVisibleAndDisabledWithText("3")
    expect(buttonNext)->toBeVisibleAndDisabled
    expect(buttonLast)->toBeVisibleAndDisabled
  }

  expectPage3Of3()
  expect(onRequestPaginate)->toHaveBeenCalledTimes(0)

  await userEvent->TestingLibraryEvent.click(buttonFirst)

  expectPage3Of3()
  expect(onRequestPaginate)->toHaveBeenCalledTimes(1)
  expect(onRequestPaginate)->toHaveBeenCalledWith1(First)
  onRequestPaginate->mockClear

  await userEvent->TestingLibraryEvent.click(buttonPrev)

  expectPage3Of3()
  expect(onRequestPaginate)->toHaveBeenCalledTimes(1)
  expect(onRequestPaginate)->toHaveBeenCalledWith1(Prev)
  onRequestPaginate->mockClear

  await userEvent->TestingLibraryEvent.click(button2)

  expectPage3Of3()
  expect(onRequestPaginate)->toHaveBeenCalledTimes(1)
  expect(onRequestPaginate)->toHaveBeenCalledWith1(Prev)
  onRequestPaginate->mockClear

  await userEvent->TestingLibraryEvent.click(button3)

  expectPage3Of3()
  expect(onRequestPaginate)->toHaveBeenCalledTimes(0)

  await userEvent->TestingLibraryEvent.click(buttonNext)

  expectPage3Of3()
  expect(onRequestPaginate)->toHaveBeenCalledTimes(0)

  await userEvent->TestingLibraryEvent.click(buttonLast)

  expectPage3Of3()
  expect(onRequestPaginate)->toHaveBeenCalledTimes(0)

  rerender(
    <LegacyPagination currentPage=1 totalPages=1 onRequestPaginate={onRequestPaginate->fn} />,
  )->ignore

  let buttons = screen->getAllByRoleExn(#button)
  let (buttonFirst, buttonPrev, button1, buttonNext, buttonLast) = assert5Buttons(buttons)

  expect(screen->getByTextExn("Page 1 of 1"))->toBeVisible
  expect(buttonFirst)->toBeVisibleAndDisabled
  expect(buttonPrev)->toBeVisibleAndDisabled
  expect(button1)->toBeVisibleAndDisabledWithText("1")
  expect(buttonNext)->toBeVisibleAndDisabled
  expect(buttonLast)->toBeVisibleAndDisabled

  rerender(
    <LegacyPagination currentPage=1 totalPages=2 onRequestPaginate={onRequestPaginate->fn} />,
  )->ignore

  let buttons = screen->getAllByRoleExn(#button)
  let (buttonFirst, buttonPrev, button1, button2, buttonNext, buttonLast) = assert6Buttons(buttons)

  expect(screen->getByTextExn("Page 1 of 2"))->toBeVisible
  expect(buttonFirst)->toBeVisibleAndDisabled
  expect(buttonPrev)->toBeVisibleAndDisabled
  expect(button1)->toBeVisibleAndDisabledWithText("1")
  expect(button2)->toBeVisibleAndActiveWithText("2")
  expect(buttonNext)->toBeVisibleAndActive
  expect(buttonLast)->toBeVisibleAndActive

  rerender(
    <LegacyPagination currentPage=2 totalPages=2 onRequestPaginate={onRequestPaginate->fn} />,
  )->ignore

  let buttons = screen->getAllByRoleExn(#button)
  let (buttonFirst, buttonPrev, button1, button2, buttonNext, buttonLast) = assert6Buttons(buttons)

  expect(screen->getByTextExn("Page 2 of 2"))->toBeVisible
  expect(buttonFirst)->toBeVisibleAndActive
  expect(buttonPrev)->toBeVisibleAndActive
  expect(button1)->toBeVisibleAndActiveWithText("1")
  expect(button2)->toBeVisibleAndDisabledWithText("2")
  expect(buttonNext)->toBeVisibleAndDisabled
  expect(buttonLast)->toBeVisibleAndDisabled

  rerender(
    <LegacyPagination currentPage=1 totalPages=5 onRequestPaginate={onRequestPaginate->fn} />,
  )->ignore

  let buttons = screen->getAllByRoleExn(#button)
  let (buttonFirst, buttonPrev, button1, button2, buttonNext, buttonLast) = assert6Buttons(buttons)

  expect(screen->getByTextExn("Page 1 of 5"))->toBeVisible
  expect(buttonFirst)->toBeVisibleAndDisabled
  expect(buttonPrev)->toBeVisibleAndDisabled
  expect(button1)->toBeVisibleAndDisabledWithText("1")
  expect(button2)->toBeVisibleAndActiveWithText("2")
  expect(buttonNext)->toBeVisibleAndActive
  expect(buttonLast)->toBeVisibleAndActive

  rerender(
    <LegacyPagination currentPage=2 totalPages=5 onRequestPaginate={onRequestPaginate->fn} />,
  )->ignore

  let buttons = screen->getAllByRoleExn(#button)
  let (buttonFirst, buttonPrev, button1, button2, button3, buttonNext, buttonLast) = assert7Buttons(
    buttons,
  )

  expect(screen->getByTextExn("Page 2 of 5"))->toBeVisible
  expect(buttonFirst)->toBeVisibleAndActive
  expect(buttonPrev)->toBeVisibleAndActive
  expect(button1)->toBeVisibleAndActiveWithText("1")
  expect(button2)->toBeVisibleAndDisabledWithText("2")
  expect(button3)->toBeVisibleAndActiveWithText("3")
  expect(buttonNext)->toBeVisibleAndActive
  expect(buttonLast)->toBeVisibleAndActive

  rerender(
    <LegacyPagination currentPage=3 totalPages=5 onRequestPaginate={onRequestPaginate->fn} />,
  )->ignore

  let buttons = screen->getAllByRoleExn(#button)
  let (buttonFirst, buttonPrev, button2, button3, button4, buttonNext, buttonLast) = assert7Buttons(
    buttons,
  )

  expect(screen->getByTextExn("Page 3 of 5"))->toBeVisible
  expect(buttonFirst)->toBeVisibleAndActive
  expect(buttonPrev)->toBeVisibleAndActive
  expect(button2)->toBeVisibleAndActiveWithText("2")
  expect(button3)->toBeVisibleAndDisabledWithText("3")
  expect(button4)->toBeVisibleAndActiveWithText("4")
  expect(buttonNext)->toBeVisibleAndActive
  expect(buttonLast)->toBeVisibleAndActive

  rerender(
    <LegacyPagination currentPage=4 totalPages=5 onRequestPaginate={onRequestPaginate->fn} />,
  )->ignore

  let buttons = screen->getAllByRoleExn(#button)
  let (buttonFirst, buttonPrev, button3, button4, button5, buttonNext, buttonLast) = assert7Buttons(
    buttons,
  )

  expect(screen->getByTextExn("Page 4 of 5"))->toBeVisible
  expect(buttonFirst)->toBeVisibleAndActive
  expect(buttonPrev)->toBeVisibleAndActive
  expect(button3)->toBeVisibleAndActiveWithText("3")
  expect(button4)->toBeVisibleAndDisabledWithText("4")
  expect(button5)->toBeVisibleAndActiveWithText("5")
  expect(buttonNext)->toBeVisibleAndActive
  expect(buttonLast)->toBeVisibleAndActive

  rerender(
    <LegacyPagination currentPage=5 totalPages=5 onRequestPaginate={onRequestPaginate->fn} />,
  )->ignore

  let buttons = screen->getAllByRoleExn(#button)
  let (buttonFirst, buttonPrev, button4, button5, buttonNext, buttonLast) = assert6Buttons(buttons)

  expect(screen->getByTextExn("Page 5 of 5"))->toBeVisible
  expect(buttonFirst)->toBeVisibleAndActive
  expect(buttonPrev)->toBeVisibleAndActive
  expect(button4)->toBeVisibleAndActiveWithText("4")
  expect(button5)->toBeVisibleAndDisabledWithText("5")
  expect(buttonNext)->toBeVisibleAndDisabled
  expect(buttonLast)->toBeVisibleAndDisabled
})
