open Vitest
open TestingLibraryReact

let userEvent = TestingLibraryEvent.setup()

let sleep = delay =>
  Js.Promise.make((~resolve, ~reject as _) =>
    Js.Global.setTimeout(() => resolve(. ()), delay)->ignore
  )

itPromise("should trigger copy to clipboard", async () => {
  let spyCopyClipboard = spyOn1(%raw(`navigator.clipboard`), "writeText")

  let {rerender} = <KbdClipboard value="my to be copied value" />->render

  let button = screen->getByRoleExn(#button)

  expect(button)->toBeVisible
  expect(spyCopyClipboard)->toHaveBeenCalledTimes(0)

  await userEvent->TestingLibraryEvent.click(button)

  expect(button)->toBeVisible
  expect(spyCopyClipboard)->toHaveBeenCalledTimes(1)
  expect(spyCopyClipboard)->toHaveBeenCalledWith1("my to be copied value")

  await userEvent->TestingLibraryEvent.click(button)

  expect(button)->toBeVisible
  expect(spyCopyClipboard)->toHaveBeenCalledTimes(1)

  await actPromise(async () => await sleep(1500))

  expect(button)->toBeVisible
  expect(spyCopyClipboard)->toHaveBeenCalledTimes(1)

  await userEvent->TestingLibraryEvent.click(button)

  expect(button)->toBeVisible
  expect(spyCopyClipboard)->toHaveBeenCalledTimes(2)
  expect(spyCopyClipboard)->toHaveBeenCalledWith1("my to be copied value")

  <KbdClipboard value="my new to be copied value" />->rerender->ignore

  let button = screen->getByRoleExn(#button)

  await userEvent->TestingLibraryEvent.click(button)

  expect(button)->toBeVisible
  expect(spyCopyClipboard)->toHaveBeenCalledTimes(3)
  expect(spyCopyClipboard)->toHaveBeenCalledWith1("my new to be copied value")
})
