open Vitest

describe("applyRule", () => {
  it("should apply MinStr rule", () => {
    expect(InputPasswordField.applyRule("123456789", InputPasswordField.minStr))->toBe(false)
    expect(InputPasswordField.applyRule("123456789A", InputPasswordField.minStr))->toBe(true)
  })

  it("should apply MinLower rule", () => {
    expect(InputPasswordField.applyRule("AAA", InputPasswordField.minLower))->toBe(false)
    expect(InputPasswordField.applyRule("AaA", InputPasswordField.minLower))->toBe(true)
  })

  it("should apply MinUpper rule", () => {
    expect(InputPasswordField.applyRule("aaa", InputPasswordField.minUpper))->toBe(false)
    expect(InputPasswordField.applyRule("aAa", InputPasswordField.minUpper))->toBe(true)
  })

  it("should apply MinDigit rule", () => {
    expect(InputPasswordField.applyRule("aaa", InputPasswordField.minDigit))->toBe(false)
    expect(InputPasswordField.applyRule("a1a", InputPasswordField.minDigit))->toBe(true)
  })
})

todo("component")
