open Vitest
open TestingLibraryReact

let isAriaDisabled = expect => expect->toHaveAttributeValue("aria-disabled", "true")

itPromise("should render a visible button executing a callback on click", async () => {
  let userEvent = TestingLibraryEvent.setup()
  let onPress = fn1(ignore)
  let {rerender} = <Button onPress={onPress->fn}> {"my button"->React.string} </Button>->render
  let button = screen->getByRoleExn(#button)

  expect(button)->toBeVisible
  expect(button)->Vitest.not->isAriaDisabled
  expect(button)->toHaveTextContent("my button")
  expect(onPress)->toHaveBeenCalledTimes(0)

  await userEvent->TestingLibraryEvent.click(button)

  expect(button)->toBeVisible
  expect(button)->Vitest.not->isAriaDisabled
  expect(button)->toHaveTextContent("my button")
  expect(onPress)->toHaveBeenCalledTimes(1)

  <Button disabled=true onPress={onPress->fn}> {"my disabled button"->React.string} </Button>
  ->rerender
  ->ignore
  let button = screen->getByRoleExn(#button)

  await userEvent->TestingLibraryEvent.click(button)

  expect(button)->toBeVisible
  expect(button)->isAriaDisabled
  expect(button)->toHaveTextContent("my disabled button")
  expect(onPress)->toHaveBeenCalledTimes(1)
})

it(
  "should not render button but only its children when not callback is passed or it's disabled",
  () => {
    let {rerender} = <Button> {"my fake button"->React.string} </Button>->render
    let button = screen->getByTextExn("my fake button")

    expect(screen->queryByRole(#button))->toBeNone
    expect(button)->toBeVisible
    expect(button)->Vitest.not->isAriaDisabled

    <Button disabled=true> {"my fake disabled button"->React.string} </Button>->rerender->ignore

    let button = screen->getByTextExn("my fake disabled button")
    expect(button)->toBeVisible
    expect(button)->Vitest.not->isAriaDisabled
  },
)
