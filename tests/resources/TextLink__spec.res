open Vitest
open TestingLibraryReact

module TestableTextLink = {
  @react.component
  let make = (~text, ~to, ~openNewTab=?, ~history=History.createMemoryHistory()) =>
    <Providers history>
      <TextLink text to ?openNewTab />
    </Providers>
}

testEach1([
  Navigation.Route("/"),
  RouteWithQueryString("/", ""->QueryString.fromString),
  Url("https://wino.fr"->Url.make),
])(."should render a link", to => {
  render(<TestableTextLink text="my link" to />)->ignore

  let link = screen->getByRoleExn(#link)
  let href = switch to {
  | Route(pathname) | RouteWithQueryString(pathname, _) => pathname
  | Url(url) => url->Url.href
  }

  expect(link)->toBeVisible
  expect(link)->toHaveAttributeValue("href", href)
  expect(link)->toHaveAttributeValue("target", #_self)
  expect(link)->toHaveTextContent("my link")
})

testEach1([
  Navigation.Route("/"),
  RouteWithQueryString("/", ""->QueryString.fromString),
  Url("https://wino.fr"->Url.make),
])(."should render a link that open a new tab", to => {
  render(<TestableTextLink text="my link" to openNewTab=true />)->ignore

  let link = screen->getByRoleExn(#link)
  let href = switch to {
  | Route(pathname) | RouteWithQueryString(pathname, _) => pathname
  | Url(url) => url->Url.href
  }

  expect(link)->toBeVisible
  expect(link)->toHaveAttributeValue("href", href)
  expect(link)->toHaveAttributeValue("target", #_blank)
  expect(link)->toHaveTextContent("my link")
})

itPromise("should render a route link and navigate on click", async () => {
  let userEvent = TestingLibraryEvent.setup()
  let history = History.createMemoryHistory()

  <TestableTextLink text="my link" history to=Navigation.Route("/route") />->render->ignore

  let link = screen->getByRoleExn(#link)

  expect(link)->toHaveAttributeValue("href", "/route")
  expect(link)->toHaveTextContent("my link")
  expect(link)->toBeVisible
  expect(history.index)->toBe(Some(0))
  expect(history.length)->toBe(1)
  expect(history.location.pathname)->toBe("/")
  expect(history.location.state)->toBe(None)
  expect(history.location.search)->toBe("")

  await userEvent->TestingLibraryEvent.click(link)

  expect(link)->toHaveAttributeValue("href", "/route")
  expect(link)->toHaveTextContent("my link")
  expect(link)->toBeVisible
  expect(history.index)->toBe(Some(1))
  expect(history.length)->toBe(2)
  expect(history.location.pathname)->toBe("/route")
  expect(history.location.state)->toBe(None)
  expect(history.location.search)->toBe("")
})

itPromise(
  "should render a route link with query string and navigate + trigger callback on click",
  async () => {
    let userEvent = TestingLibraryEvent.setup()
    let history = History.createMemoryHistory()
    let queryString = "?some=test"->QueryString.fromString

    <TestableTextLink
      text="my link" history to=Navigation.RouteWithQueryString("/route", queryString)
    />
    ->render
    ->ignore

    let link = screen->getByRoleExn(#link)

    expect(link)->toHaveAttributeValue("href", "/route?some=test")
    expect(link)->toHaveTextContent("my link")
    expect(link)->toBeVisible
    expect(history.index)->toBe(Some(0))
    expect(history.length)->toBe(1)
    expect(history.location.pathname)->toBe("/")
    expect(history.location.state)->toBe(None)
    expect(history.location.search)->toBe("")

    await userEvent->TestingLibraryEvent.click(link)

    expect(link)->toHaveAttributeValue("href", "/route?some=test")
    expect(link)->toHaveTextContent("my link")
    expect(link)->toBeVisible
    expect(history.index)->toBe(Some(1))
    expect(history.length)->toBe(2)
    expect(history.location.pathname)->toBe("/route")
    expect(history.location.state)->toBe(None)
    expect(history.location.search)->toBe("?some=test")
  },
)

todo("should render a hypertext link and navigate on click and not trigger callback")
todo("should render and navigate on a new tab on click")
