open Vitest
open TestingLibraryReact

let userEvent = TestingLibraryEvent.setup()

module TestableInputNumberField = {
  module OptionalValue = {
    @react.component
    let make = (~defaultValue, ~onChange=fn1(ignore)->fn, ~onFocus=?, ~onBlur=?) => {
      let (value, setValue) = React.useState(() => defaultValue)

      <InputNumberField.OptionalValue
        testLocalization=#"fr-FR"
        label="Input"
        value
        onChange={value => {
          setValue(_ => value)
          onChange(value)
        }}
        ?onFocus
        ?onBlur
      />
    }
  }

  @react.component
  let make = (
    ~defaultValue,
    ~minValue=?,
    ~maxValue=?,
    ~step=?,
    ~placeholder=?,
    ~minPrecision=?,
    ~precision=?,
    ~disabled=?,
    ~useGrouping=?,
    ~shrinkInput=?,
    ~onChange=fn1(ignore)->fn,
    ~onFocus=?,
    ~onBlur=?,
  ) => {
    let (value, setValue) = React.useState(() => defaultValue)

    <InputNumberField
      testLocalization=#"fr-FR"
      label="Input"
      ?minValue
      ?maxValue
      ?step
      ?placeholder
      ?disabled
      ?minPrecision
      ?precision
      ?shrinkInput
      ?useGrouping
      value
      onChange={value => {
        setValue(_ => value)
        onChange(value)
      }}
      ?onFocus
      ?onBlur
    />
  }
}

itPromise(
  "should render the component with optional value, focus, change the value and blur",
  async () => {
    let onChange = fn1(ignore)
    let onFocus = fn0()
    let onBlur = fn0()

    <TestableInputNumberField.OptionalValue
      defaultValue=None onChange={onChange->fn} onFocus={onFocus->fn} onBlur={onBlur->fn}
    />
    ->render
    ->ignore

    let inputElement = screen->getByLabelTextExn("Input")

    expect(inputElement)->toBeVisible
    expect(inputElement)->toHaveValue("")
    expect(onChange)->toHaveBeenCalledTimes(0)
    expect(onFocus)->toHaveBeenCalledTimes(0)
    expect(onBlur)->toHaveBeenCalledTimes(0)

    await userEvent->TestingLibraryEvent.click(inputElement)

    expect(inputElement)->toHaveValue("")
    expect(onChange)->toHaveBeenCalledTimes(0)
    expect(onFocus)->toHaveBeenCalledTimes(1)
    expect(onBlur)->toHaveBeenCalledTimes(0)

    await userEvent->TestingLibraryEvent.type_(inputElement, "9")

    expect(inputElement)->toHaveValue("9")
    expect(onChange)->toHaveBeenCalledWith1(Some(9.))
    expect(onChange)->toHaveBeenCalledTimes(1)
    expect(onFocus)->toHaveBeenCalledTimes(1)
    expect(onBlur)->toHaveBeenCalledTimes(0)

    await userEvent->TestingLibraryEvent.tab

    expect(inputElement)->toHaveValue("9,00")
    expect(onChange)->toHaveBeenCalledTimes(1)
    expect(onFocus)->toHaveBeenCalledTimes(1)
    expect(onBlur)->toHaveBeenCalledTimes(1)

    await userEvent->TestingLibraryEvent.type_(inputElement, "{backspace}")

    expect(inputElement)->toHaveValue("")
    expect(onChange)->toHaveBeenCalledWith1(None)
    expect(onChange)->toHaveBeenCalledTimes(2)
    expect(onFocus)->toHaveBeenCalledTimes(2)
    expect(onBlur)->toHaveBeenCalledTimes(1)

    await userEvent->TestingLibraryEvent.tab

    expect(inputElement)->toHaveValue("")
    expect(onFocus)->toHaveBeenCalledTimes(2)
    expect(onBlur)->toHaveBeenCalledTimes(2)
  },
)

itPromise("should render the default component, focus, change the value and blur", async () => {
  let onChange = fn1(ignore)
  let onFocus = fn0()
  let onBlur = fn0()

  <TestableInputNumberField
    defaultValue=0. onChange={onChange->fn} onFocus={onFocus->fn} onBlur={onBlur->fn}
  />
  ->render
  ->ignore

  let inputElement = screen->getByLabelTextExn("Input")

  expect(inputElement)->toBeVisible
  expect(inputElement)->toHaveValue("0,00")
  expect(onChange)->toHaveBeenCalledTimes(0)
  expect(onFocus)->toHaveBeenCalledTimes(0)
  expect(onBlur)->toHaveBeenCalledTimes(0)

  await userEvent->TestingLibraryEvent.click(inputElement)

  expect(inputElement)->toHaveValue("0")
  expect(onChange)->toHaveBeenCalledTimes(1)
  expect(onFocus)->toHaveBeenCalledTimes(1)
  expect(onBlur)->toHaveBeenCalledTimes(0)

  await userEvent->TestingLibraryEvent.type_(inputElement, "9")

  expect(inputElement)->toHaveValue("09")
  expect(onChange)->toHaveBeenCalledWith1(9.)
  expect(onChange)->toHaveBeenCalledTimes(2)
  expect(onFocus)->toHaveBeenCalledTimes(1)
  expect(onBlur)->toHaveBeenCalledTimes(0)

  await userEvent->TestingLibraryEvent.tab

  expect(inputElement)->toHaveValue("9,00")
  expect(onChange)->toHaveBeenCalledTimes(2)
  expect(onFocus)->toHaveBeenCalledTimes(1)
  expect(onBlur)->toHaveBeenCalledTimes(1)
})

itPromise("should format the value with the minimum fraction digits", async () => {
  <TestableInputNumberField defaultValue=0. minPrecision=0 />->render->ignore

  let inputElement = screen->getByLabelTextExn("Input")

  expect(inputElement)->toHaveValue("0")

  await userEvent->TestingLibraryEvent.type_(inputElement, ",01")

  expect(inputElement)->toHaveValue("0,01")
})

it("should format the value with the maximum fraction digits", () => {
  <TestableInputNumberField defaultValue=0. precision=3 />->render->ignore

  expect(screen->getByLabelTextExn("Input"))->toHaveValue("0,000")
})

itPromise("should remove decimal 0 digits while allowing a precision", async () => {
  <TestableInputNumberField defaultValue=1. minPrecision=0 />->render->ignore

  let inputElement = screen->getByLabelTextExn("Input")

  expect(inputElement)->toHaveValue("1")

  await userEvent->TestingLibraryEvent.type_(inputElement, ",33")

  expect(inputElement)->toHaveValue("1,33")
})

itPromise("should format incorrect number values upon user input", async () => {
  let userEvent = TestingLibraryEvent.setupWithOptions({delay: 100, skipClick: true})

  <TestableInputNumberField defaultValue=0. />->render->ignore

  let inputElement = screen->getByLabelTextExn("Input")

  expect(inputElement)->toHaveValue("0,00")

  await userEvent->TestingLibraryEvent.click(inputElement)

  await userEvent->TestingLibraryEvent.type_(inputElement, "3a")

  screen->debugElement(inputElement)

  expect(inputElement)->toHaveValue("03")

  await userEvent->TestingLibraryEvent.tab

  expect(inputElement)->toHaveValue("3,00")
})

itPromise(
  "should keep the floating point in the input value when erasing the decimal digits while focusing the input",
  async () => {
    let onChange = fn1(ignore)

    <TestableInputNumberField.OptionalValue defaultValue=None onChange={onChange->fn} />
    ->render
    ->ignore

    let inputElement = screen->getByLabelTextExn("Input")

    expect(onChange)->toHaveBeenCalledTimes(0)
    expect(inputElement)->toHaveValue("")

    await userEvent->TestingLibraryEvent.type_(inputElement, ",2")

    expect(inputElement)->toHaveValue(",2")

    await userEvent->TestingLibraryEvent.tab

    expect(inputElement)->toHaveValue("0,20")
    expect(onChange)->toHaveBeenLastCalledWith1(Some(0.2))
    expect(onChange)->toHaveBeenCalledTimes(1)

    await userEvent->TestingLibraryEvent.type_(inputElement, "{backspace}")

    expect(inputElement)->toHaveValue("0,")

    await userEvent->TestingLibraryEvent.tab

    expect(inputElement)->toHaveValue("0,00")
    expect(onChange)->toHaveBeenLastCalledWith1(Some(0.))
    expect(onChange)->toHaveBeenCalledTimes(2)

    await userEvent->TestingLibraryEvent.type_(inputElement, "{backspace}")

    expect(inputElement)->toHaveValue("")

    await userEvent->TestingLibraryEvent.type_(inputElement, "25,55")

    expect(inputElement)->toHaveValue("25,55")

    await userEvent->TestingLibraryEvent.type_(inputElement, "{backspace},2")

    expect(inputElement)->toHaveValue("25,52")

    await userEvent->TestingLibraryEvent.type_(inputElement, "{backspace}{backspace}9")

    expect(inputElement)->toHaveValue("25,9")

    await userEvent->TestingLibraryEvent.tab

    expect(inputElement)->toHaveValue("25,90")
    expect(onChange)->toHaveBeenLastCalledWith1(Some(25.9))
    expect(onChange)->toHaveBeenCalledTimes(12)

    await userEvent->TestingLibraryEvent.type_(inputElement, "999")

    expect(inputElement)->toHaveValue("25,9999")

    await userEvent->TestingLibraryEvent.tab

    expect(inputElement)->toHaveValue("26,00")
    expect(onChange)->toHaveBeenLastCalledWith1(Some(26.))
    expect(onChange)->toHaveBeenCalledTimes(16)
  },
)

itPromise(
  "should increment/decrement the value then focus the input when the user clicks on the stepper buttons",
  async () => {
    <TestableInputNumberField defaultValue=0.99 />->render->ignore

    let inputElement = screen->getByLabelTextExn("Input")
    let incrementStepperElement = screen->getByLabelTextExn("Increase Input")
    let decrementStepperElement = screen->getByLabelTextExn("Decrease Input")

    expect(inputElement)->toHaveValue("0,99")

    await userEvent->TestingLibraryEvent.click(inputElement)
    await userEvent->TestingLibraryEvent.click(incrementStepperElement)
    await userEvent->TestingLibraryEvent.click(incrementStepperElement)

    expect(inputElement)->toHaveValue("2")

    await userEvent->TestingLibraryEvent.type_(inputElement, ",5")

    expect(inputElement)->toHaveValue("2,5")

    await userEvent->TestingLibraryEvent.click(decrementStepperElement)

    expect(inputElement)->toHaveValue("2")

    await userEvent->TestingLibraryEvent.click(decrementStepperElement)

    expect(inputElement)->toHaveValue("1")

    await userEvent->TestingLibraryEvent.tab

    expect(inputElement)->toHaveValue("1,00")
  },
)

itPromise("should increment/decrement the value when the user press arrow keys", async () => {
  <TestableInputNumberField defaultValue=0. />->render->ignore

  let inputElement = screen->getByLabelTextExn("Input")

  expect(inputElement)->toHaveValue("0,00")

  await userEvent->TestingLibraryEvent.click(inputElement)
  await userEvent->TestingLibraryEvent.keyboard("{arrowup}")
  await userEvent->TestingLibraryEvent.keyboard("{arrowup}")

  expect(inputElement)->toHaveValue("2")

  await userEvent->TestingLibraryEvent.keyboard("{arrowdown}")

  await userEvent->TestingLibraryEvent.tab

  expect(inputElement)->toHaveValue("1,00")
})

itPromise(
  "should not be possible for the user to input value or to have focus if disabled is set to true",
  async () => {
    let onChange = fn1(ignore)
    let onFocus = fn0()
    let onBlur = fn0()

    <TestableInputNumberField
      defaultValue=0.
      disabled=true
      onChange={onChange->fn}
      onFocus={onFocus->fn}
      onBlur={onBlur->fn}
    />
    ->render
    ->ignore

    let inputElement = screen->getByLabelTextExn("Input")
    let incrementStepperElement = screen->getByLabelTextExn("Increase Input")

    expect(inputElement)->toBeVisible
    expect(inputElement)->toHaveValue("0,00")
    expect(onChange)->toHaveBeenCalledTimes(0)
    expect(onFocus)->toHaveBeenCalledTimes(0)
    expect(onBlur)->toHaveBeenCalledTimes(0)

    await userEvent->TestingLibraryEvent.click(inputElement)

    expect(onChange)->toHaveBeenCalledTimes(0)
    expect(onFocus)->toHaveBeenCalledTimes(0)
    expect(onBlur)->toHaveBeenCalledTimes(0)

    await userEvent->TestingLibraryEvent.type_(inputElement, "2")

    expect(inputElement)->toHaveValue("0,00")
    expect(onChange)->toHaveBeenCalledTimes(0)
    expect(onFocus)->toHaveBeenCalledTimes(0)
    expect(onBlur)->toHaveBeenCalledTimes(0)

    await userEvent->TestingLibraryEvent.click(inputElement)
    await userEvent->TestingLibraryEvent.click(incrementStepperElement)
    await userEvent->TestingLibraryEvent.keyboard("{arrowup}")

    expect(inputElement)->toHaveValue("0,00")
    expect(onChange)->toHaveBeenCalledTimes(0)
    expect(onFocus)->toHaveBeenCalledTimes(0)
    expect(onBlur)->toHaveBeenCalledTimes(0)
  },
)

it("should show the placeholder when the value is 0 or NaN (blank input)", () => {
  <TestableInputNumberField defaultValue=0. placeholder="Price" />->render->ignore

  expect(screen->getByLabelTextExn("Input"))->toHaveAttributeValue("placeholder", "Price")
})

itPromise(
  "should trigger on blur then format the value when exceeding limit and its stepper should be disabled",
  async () => {
    <TestableInputNumberField defaultValue=0. minValue=0. maxValue=1. />->render->ignore

    let inputElement = screen->getByLabelTextExn("Input")
    let incrementStepperElement = screen->getByLabelTextExn("Increase Input")
    let decrementStepperElement = screen->getByLabelTextExn("Decrease Input")

    expect(inputElement)->toHaveValue("0,00")

    await userEvent->TestingLibraryEvent.type_(inputElement, "2")
    await userEvent->TestingLibraryEvent.tab // FIXME - shouldn't be necessary

    expect(inputElement)->toHaveValue("1,00")

    await userEvent->TestingLibraryEvent.click(inputElement) // NOTE - should be necessary
    await userEvent->TestingLibraryEvent.keyboard("{arrowup}")

    expect(inputElement)->toHaveValue("1")

    await userEvent->TestingLibraryEvent.keyboard("{arrowdown}")
    await userEvent->TestingLibraryEvent.keyboard("{arrowdown}")

    expect(inputElement)->toHaveValue("0")

    await userEvent->TestingLibraryEvent.click(decrementStepperElement)

    expect(inputElement)->toHaveValue("0")

    await userEvent->TestingLibraryEvent.click(incrementStepperElement)
    await userEvent->TestingLibraryEvent.click(incrementStepperElement)
    await userEvent->TestingLibraryEvent.tab

    expect(inputElement)->toHaveValue("1,00")

    await userEvent->TestingLibraryEvent.type_(inputElement, "-2")
    await userEvent->TestingLibraryEvent.tab

    expect(inputElement)->toHaveValue("1,00")
  },
)

itPromise("should format the value settled by the step value", async () => {
  <TestableInputNumberField defaultValue=0. step=6. maxValue=10. />->render->ignore

  let inputElement = screen->getByLabelTextExn("Input")
  let incrementStepperElement = screen->getByLabelTextExn("Increase Input")
  let decrementStepperElement = screen->getByLabelTextExn("Decrease Input")

  expect(inputElement)->toHaveValue("0,00")

  await userEvent->TestingLibraryEvent.click(incrementStepperElement)
  await userEvent->TestingLibraryEvent.tab

  expect(inputElement)->toHaveValue("6,00")

  await userEvent->TestingLibraryEvent.type_(inputElement, "12")
  await userEvent->TestingLibraryEvent.tab

  expect(inputElement)->toHaveValue("6,00")

  await userEvent->TestingLibraryEvent.click(decrementStepperElement)
  await userEvent->TestingLibraryEvent.click(decrementStepperElement)
  await userEvent->TestingLibraryEvent.tab

  expect(inputElement)->toHaveValue("-6,00")

  await userEvent->TestingLibraryEvent.click(inputElement)
  await userEvent->TestingLibraryEvent.keyboard("{arrowdown}")

  expect(inputElement)->toHaveValue("-12")

  await userEvent->TestingLibraryEvent.keyboard("{arrowup}")

  expect(inputElement)->toHaveValue("-6")
})

itPromise("should have an adaptative input width fitting the value length", async () => {
  let getNodeMinWidth = %raw(`node => getComputedStyle(node).minWidth`)

  <TestableInputNumberField defaultValue=0. shrinkInput=true />->render->ignore

  let inputElement = screen->getByLabelTextExn("Input")

  expect(inputElement)->toHaveDisplayValue("0,00")

  let beforeWidth = getNodeMinWidth(inputElement)

  await userEvent->TestingLibraryEvent.type_(inputElement, "0")
  await userEvent->TestingLibraryEvent.tab

  expect(inputElement)->toHaveDisplayValue("0,00")

  let afterWidth = getNodeMinWidth(inputElement)

  expect(beforeWidth)->toBe(afterWidth)

  await userEvent->TestingLibraryEvent.type_(inputElement, "100")
  await userEvent->TestingLibraryEvent.tab

  expect(inputElement)->toHaveDisplayValue("100,00")

  let afterWidth = getNodeMinWidth(inputElement)

  expect(beforeWidth)->Vitest.not->toBe(afterWidth)
})

itPromise("should blur the input when focusing and pressing {enter}", async () => {
  let onFocus = fn0()
  let onBlur = fn0()

  <TestableInputNumberField defaultValue=0. onFocus={onFocus->fn} onBlur={onBlur->fn} />
  ->render
  ->ignore

  let inputElement = screen->getByLabelTextExn("Input")

  expect(inputElement)->toBeVisible
  expect(inputElement)->toHaveValue("0,00")
  expect(onFocus)->toHaveBeenCalledTimes(0)
  expect(onBlur)->toHaveBeenCalledTimes(0)

  await userEvent->TestingLibraryEvent.type_(inputElement, "1{enter}")

  expect(inputElement)->toHaveValue("1,00")
  expect(onFocus)->toHaveBeenCalledTimes(1)
  expect(onBlur)->toHaveBeenCalledTimes(1)
})

itPromise("should use number grouping when sets to true", async () => {
  <TestableInputNumberField defaultValue=3200100. useGrouping=true />->render->ignore

  let inputElement = screen->getByLabelTextExn("Input")

  expect(inputElement)->toHaveValue("3 200 100,00")

  await userEvent->TestingLibraryEvent.click(inputElement)

  expect(inputElement)->toHaveValue("3 200 100")

  await userEvent->TestingLibraryEvent.tab

  expect(inputElement)->toHaveValue("3 200 100,00")
})

itPromise("should not use number grouping when sets to false", async () => {
  <TestableInputNumberField defaultValue=3200100. useGrouping=false />->render->ignore

  expect(screen->getByLabelTextExn("Input"))->toHaveValue("3200100,00")
})
